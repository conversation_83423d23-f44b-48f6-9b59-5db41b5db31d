import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowRight, Check } from 'lucide-react';
import Loading from '../../../components/common/Loading';
import type { Template } from '../../../types';

const templates: Template[] = [
  {
    id: 'colorful',
    name: 'Colorful',
    style: 'colorful',
    preview: 'https://images.unsplash.com/photo-1557683316-973673baf926?w=400'
  },
  {
    id: 'modern',
    name: 'Modern',
    style: 'modern',
    preview: 'https://images.unsplash.com/photo-1542831371-29b0f74f9713?w=400'
  },
  {
    id: 'heritage',
    name: 'Heritage',
    style: 'heritage',
    preview: 'https://images.unsplash.com/photo-1577495508048-b635879837f1?w=400'
  }
];

export default function TemplateSelector() {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const handleSelectTemplate = async (template: Template) => {
    setSelectedId(template.id);
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate loading
    
    navigate('/preview', {
      state: {
        ...location.state,
        selectedTemplate: template,
      },
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-8">
      {loading && <Loading />}
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-indigo-900 mb-4">
            Choose Your Template Style
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Select a template that best fits your document's purpose and brand identity
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl 
                ${selectedId === template.id ? 'ring-2 ring-indigo-600' : ''}`}
            >
              <div className="relative">
                <img
                  src={template.preview}
                  alt={template.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2 flex items-center justify-between">
                  {template.name}
                  {selectedId === template.id && (
                    <Check className="w-5 h-5 text-indigo-600" />
                  )}
                </h3>
                <button
                  onClick={() => handleSelectTemplate(template)}
                  disabled={loading}
                  className={`w-full px-4 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors
                    ${selectedId === template.id
                      ? 'bg-indigo-700 text-white'
                      : 'bg-indigo-600 text-white hover:bg-indigo-700'
                    } disabled:bg-indigo-300`}
                >
                  {selectedId === template.id ? (
                    <>Selected</>
                  ) : (
                    <>
                      Use this template
                      <ArrowRight className="w-5 h-5" />
                    </>
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center text-gray-600">
          <p className="text-sm">
            All templates are fully customizable after selection
          </p>
        </div>
      </div>
    </div>
  );
}