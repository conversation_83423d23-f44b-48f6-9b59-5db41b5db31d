import { useState, useCallback } from 'react';
import { chatAPI } from '../services/api';

export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export function useChat() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(async (documentIds?: string[]) => {
    if (!inputValue.trim()) return;

    try {
      setIsLoading(true);
      setError(null);

      // Add user message to chat
      const userMessage: Message = {
        role: 'user',
        content: inputValue
      };
      setMessages(prev => [...prev, userMessage]);
      setInputValue('');

      // Get streaming response from API
      const response = await chatAPI.streamChat(userMessage.content, messages, documentIds);
      const reader = response.getReader();
      let assistantMessage = '';

      // Add assistant message placeholder
      setMessages(prev => [...prev, { role: 'assistant', content: '' }]);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode and accumulate the chunk
        const chunk = new TextDecoder().decode(value);
        assistantMessage += chunk;

        // Update the last message with accumulated content
        setMessages(prev => {
          const newMessages = [...prev];
          newMessages[newMessages.length - 1] = {
            role: 'assistant',
            content: assistantMessage
          };
          return newMessages;
        });
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while sending message');
      console.error('Error in chat:', err);
    } finally {
      setIsLoading(false);
    }
  }, [inputValue, messages]);

  const resetChat = useCallback(() => {
    setMessages([]);
    setInputValue('');
    setError(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    inputValue,
    setInputValue,
    sendMessage,
    resetChat,
    setMessages
  };
} 