@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Add new animations utility classes */
  .transition-standard {
    transition-property: color, background-color, border-color,
      text-decoration-color, fill, stroke, opacity, box-shadow, transform,
      filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .gradient-primary {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--accent-primary)) 100%
    );
  }

  .gradient-secondary {
    background: linear-gradient(
      135deg,
      hsl(var(--secondary)) 0%,
      hsl(var(--accent-secondary)) 100%
    );
  }

  .glass-effect {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: hsla(var(--background), 0.8);
    border: 1px solid hsla(var(--border), 0.2);
  }

  .chat-bubble-user {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--accent-primary)) 100%
    );
    color: hsl(var(--primary-foreground));
  }

  .chat-bubble-ai {
    background: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    border: 1px solid hsl(var(--border));
  }

  .knowledge-card {
    background: linear-gradient(
      135deg,
      hsla(var(--card), 0.9) 0%,
      hsla(var(--muted), 0.5) 100%
    );
    backdrop-filter: blur(8px);
    border: 1px solid hsla(var(--border), 0.3);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 262 83% 58%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 262 83% 58%;

    /* Advanced color palette */
    --accent-primary: 280 100% 70%;
    --accent-secondary: 200 100% 70%;
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 221 83% 53%;
    --info-foreground: 210 40% 98%;

    /* Knowledge base specific colors */
    --knowledge-primary: 262 83% 58%;
    --knowledge-secondary: 280 100% 70%;
    --chat-user: 262 83% 58%;
    --chat-ai: 240 4.8% 95.9%;
    --upload-zone: 142 76% 36%;
    --upload-zone-hover: 142 76% 30%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 262 83% 58%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 262 83% 58%;

    /* Dark mode advanced colors */
    --accent-primary: 280 100% 70%;
    --accent-secondary: 200 100% 70%;
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 221 83% 53%;
    --info-foreground: 210 40% 98%;

    /* Dark mode knowledge base colors */
    --knowledge-primary: 262 83% 58%;
    --knowledge-secondary: 280 100% 70%;
    --chat-user: 262 83% 58%;
    --chat-ai: 240 3.7% 15.9%;
    --upload-zone: 142 76% 36%;
    --upload-zone-hover: 142 76% 40%;
  }
}

@layer base {
  * {
    @apply border-[hsl(var(--border))];
  }
  html,
  body {
    @apply w-full h-full m-0 p-0;
  }
  body {
    @apply bg-background text-foreground;
  }
  #root {
    @apply w-full h-full;
  }
}

/* Make sure the component styles are properly loaded */
@layer components {
  .dashboard-card {
    @apply bg-card border-border border rounded-lg p-6 shadow-sm hover:shadow-lg transition-all duration-300 text-card-foreground backdrop-blur-sm;
  }

  .main-layout {
    @apply min-h-screen flex flex-col bg-gradient-to-br from-background via-background to-muted/20 w-full;
  }

  .app-header {
    @apply glass-effect text-foreground shadow-lg border-b border-border/50;
  }

  .header-link-active {
    @apply gradient-primary text-primary-foreground rounded-lg px-4 py-2 font-medium shadow-md;
  }

  .header-link {
    @apply text-foreground hover:bg-muted/50 rounded-lg px-4 py-2 transition-standard font-medium;
  }

  .knowledge-upload-zone {
    @apply border-2 border-dashed border-muted-foreground/25 rounded-xl p-8 text-center hover:border-[hsl(var(--upload-zone))] hover:bg-[hsl(var(--upload-zone))]/5 transition-all duration-300 cursor-pointer;
  }

  .knowledge-upload-zone.dragover {
    @apply border-[hsl(var(--upload-zone))] bg-[hsl(var(--upload-zone))]/10 scale-105;
  }

  .chat-container {
    @apply flex flex-col h-full bg-gradient-to-b from-background to-muted/10 rounded-xl border border-border/50 shadow-lg;
  }

  .chat-message {
    @apply max-w-[80%] p-4 rounded-2xl shadow-sm backdrop-blur-sm;
  }

  .chat-input-container {
    @apply glass-effect rounded-xl border border-border/50 p-4 shadow-lg;
  }

  .document-card {
    @apply knowledge-card rounded-xl p-4 hover:shadow-lg transition-all duration-300 cursor-pointer border hover:border-primary/30;
  }

  .document-card.selected {
    @apply border-primary bg-primary/5 shadow-md;
  }

  .floating-action-button {
    @apply fixed bottom-6 right-6 w-14 h-14 gradient-primary rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-primary-foreground hover:scale-110;
  }

  .nav-pill {
    @apply px-6 py-3 rounded-full font-medium transition-all duration-300 border border-transparent;
  }

  .nav-pill.active {
    @apply gradient-primary text-primary-foreground shadow-md border-primary/20;
  }

  .nav-pill.inactive {
    @apply bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground border-border/50;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  .toast-success {
    @apply bg-[hsl(var(--success))] text-[hsl(var(--success-foreground))] border border-[hsl(var(--success))]/20;
  }

  .toast-error {
    @apply bg-destructive text-destructive-foreground border border-destructive/20;
  }

  .toast-info {
    @apply bg-[hsl(var(--info))] text-[hsl(var(--info-foreground))] border border-[hsl(var(--info))]/20;
  }
}
