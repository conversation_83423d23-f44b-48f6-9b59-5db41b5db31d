import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import {
  Building,
  Users,
  Mail,
  Check,
  X,
  Crown,
  User,
  AlertCircle,
} from "lucide-react";
import { toast } from "../../hooks/use-toast";
import {
  getUserPendingOrganizationInvitations,
  acceptOrganizationInvitation,
  rejectOrganizationInvitation,
} from "../../services/organizationService";
import {
  getUserPendingTeamInvitations,
  acceptTeamInvitation,
  rejectTeamInvitation,
} from "../../services/teamService";
import { OrganizationInvitation } from "../../types/organization";
import { TeamInvitation } from "../../services/teamService";

interface InvitationNotificationsProps {
  onInvitationHandled?: () => void;
}

export function InvitationNotifications({
  onInvitationHandled,
}: InvitationNotificationsProps) {
  const [orgInvitations, setOrgInvitations] = useState<
    OrganizationInvitation[]
  >([]);
  const [teamInvitations, setTeamInvitations] = useState<TeamInvitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [selectedInvitation, setSelectedInvitation] = useState<{
    type: "organization" | "team";
    invitation: OrganizationInvitation | TeamInvitation;
    action: "accept" | "reject";
  } | null>(null);

  const loadInvitations = async () => {
    try {
      setLoading(true);
      const [orgData, teamData] = await Promise.all([
        getUserPendingOrganizationInvitations(),
        getUserPendingTeamInvitations(),
      ]);

      setOrgInvitations(orgData);
      setTeamInvitations(teamData);
    } catch (error) {
      console.error("Error loading invitations:", error);
      toast({
        title: "Error",
        description: "Failed to load invitations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, []);

  const handleAction = async () => {
    if (!selectedInvitation) return;

    try {
      setActionLoading(selectedInvitation.invitation.id);

      if (selectedInvitation.type === "organization") {
        if (selectedInvitation.action === "accept") {
          await acceptOrganizationInvitation(selectedInvitation.invitation.id);
        } else {
          await rejectOrganizationInvitation(selectedInvitation.invitation.id);
        }
      } else {
        if (selectedInvitation.action === "accept") {
          await acceptTeamInvitation(selectedInvitation.invitation.id);
        } else {
          await rejectTeamInvitation(selectedInvitation.invitation.id);
        }
      }

      toast({
        title: "Success",
        description: `Invitation ${selectedInvitation.action}ed successfully`,
      });

      // Reload invitations
      loadInvitations();
      onInvitationHandled?.();
    } catch (error) {
      console.error(`Error ${selectedInvitation.action}ing invitation:`, error);
      toast({
        title: "Error",
        description: `Failed to ${selectedInvitation.action} invitation`,
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
      setSelectedInvitation(null);
    }
  };

  const totalInvitations = orgInvitations.length + teamInvitations.length;

  if (totalInvitations === 0) {
    return null;
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        <Card className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-amber-800 dark:text-amber-200">
              <Mail className="w-5 h-5" />
              <span>Pending Invitations</span>
              <Badge variant="secondary" className="ml-2">
                {totalInvitations}
              </Badge>
            </CardTitle>
            <CardDescription className="text-amber-700 dark:text-amber-300">
              You have {totalInvitations} pending invitation
              {totalInvitations !== 1 ? "s" : ""} to review.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Organization Invitations */}
            <AnimatePresence>
              {orgInvitations.map((invitation) => (
                <motion.div
                  key={invitation.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                      <Building className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="font-medium">
                        Organization: {invitation.organization_name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Invited as{" "}
                        <Badge
                          variant={
                            invitation.role === "admin"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {invitation.role === "admin" ? (
                            <>
                              <Crown className="w-3 h-3 mr-1" />
                              Admin
                            </>
                          ) : (
                            <>
                              <User className="w-3 h-3 mr-1" />
                              Member
                            </>
                          )}
                        </Badge>
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setSelectedInvitation({
                          type: "organization",
                          invitation,
                          action: "accept",
                        })
                      }
                      disabled={actionLoading === invitation.id}
                    >
                      <Check className="w-4 h-4 mr-1" />
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setSelectedInvitation({
                          type: "organization",
                          invitation,
                          action: "reject",
                        })
                      }
                      disabled={actionLoading === invitation.id}
                    >
                      <X className="w-4 h-4 mr-1" />
                      Reject
                    </Button>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Team Invitations */}
            <AnimatePresence>
              {teamInvitations.map((invitation) => (
                <motion.div
                  key={invitation.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                      <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="font-medium">
                        Team: {invitation.team_name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {invitation.organization_name
                          ? `Organization: ${invitation.organization_name}`
                          : "Independent Team"}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setSelectedInvitation({
                          type: "team",
                          invitation,
                          action: "accept",
                        })
                      }
                      disabled={actionLoading === invitation.id}
                    >
                      <Check className="w-4 h-4 mr-1" />
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setSelectedInvitation({
                          type: "team",
                          invitation,
                          action: "reject",
                        })
                      }
                      disabled={actionLoading === invitation.id}
                    >
                      <X className="w-4 h-4 mr-1" />
                      Reject
                    </Button>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>

      {/* Confirmation Dialog */}
      <AlertDialog
        open={!!selectedInvitation}
        onOpenChange={() => setSelectedInvitation(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              {selectedInvitation?.action === "accept" ? (
                <Check className="w-5 h-5 text-green-600" />
              ) : (
                <X className="w-5 h-5 text-red-600" />
              )}
              <span>
                {selectedInvitation?.action === "accept" ? "Accept" : "Reject"}{" "}
                Invitation
              </span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedInvitation?.action === "accept" ? (
                <>
                  Are you sure you want to accept this invitation to join{" "}
                  <strong>
                    {selectedInvitation.type === "organization"
                      ? (
                          selectedInvitation.invitation as OrganizationInvitation
                        ).organization_name
                      : (selectedInvitation.invitation as TeamInvitation)
                          .team_name}
                  </strong>
                  ?
                </>
              ) : (
                <>
                  Are you sure you want to reject this invitation to join{" "}
                  <strong>
                    {selectedInvitation?.type === "organization"
                      ? (
                          selectedInvitation.invitation as OrganizationInvitation
                        ).organization_name
                      : (selectedInvitation.invitation as TeamInvitation)
                          .team_name}
                  </strong>
                  ?
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleAction}
              className={
                selectedInvitation?.action === "accept"
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {selectedInvitation?.action === "accept" ? "Accept" : "Reject"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
