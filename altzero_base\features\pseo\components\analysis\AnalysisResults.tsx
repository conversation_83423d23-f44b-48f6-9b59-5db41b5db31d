import React, { useState } from 'react';
import type { PSEOWebsite } from '../../types';

interface AnalysisResultsProps {
  website: PSEOWebsite;
  results: Record<string, any>;
  analysisTypes: string[];
}

export const AnalysisResults: React.FC<AnalysisResultsProps> = ({
  website,
  results,
  analysisTypes
}) => {
  const [activeTab, setActiveTab] = useState<string>(analysisTypes[0] || 'overview');

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    ...(analysisTypes.includes('page_discovery') ? [{ id: 'pages', name: 'Page Discovery', icon: '🔍' }] : []),
    ...(analysisTypes.includes('keyword_research') ? [{ id: 'keywords', name: 'Keywords', icon: '🎯' }] : []),
    ...(analysisTypes.includes('content_generation') ? [{ id: 'content', name: 'Content', icon: '📝' }] : []),
  ];

  const renderOverview = () => {
    const stats = [
      {
        label: 'Pages Discovered',
        value: results.PageDiscoveryAgent?.pages_discovered?.length || 0,
        icon: '📄',
        color: 'bg-blue-500'
      },
      {
        label: 'Keywords Found',
        value: results.KeywordResearchAgent?.keywords_found?.length || 0,
        icon: '🎯',
        color: 'bg-green-500'
      },
      {
        label: 'Content Opportunities',
        value: results.ContentGenerationAgent?.content_opportunities?.length || 0,
        icon: '💡',
        color: 'bg-purple-500'
      },
      {
        label: 'Analysis Agents',
        value: Object.keys(results).filter(k => k.endsWith('Agent')).length,
        icon: '🤖',
        color: 'bg-orange-500'
      }
    ];

    return (
      <div className="space-y-6">
        {/* Website Summary */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Website Analysis Summary</h3>
          <div className="flex items-center gap-4 mb-4">
            <div className="text-3xl">🌐</div>
            <div>
              <h4 className="font-medium text-foreground">{website.name}</h4>
              <p className="text-muted-foreground">{website.domain}</p>
              <p className="text-xs text-muted-foreground">
                Analysis completed {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat) => (
            <div key={stat.label} className="bg-card border rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 ${stat.color} rounded-lg flex items-center justify-center text-white text-lg`}>
                  {stat.icon}
                </div>
                <div>
                  <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  <p className="text-sm text-muted-foreground">{stat.label}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Insights */}
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Key Insights</h3>
          <div className="space-y-3">
            {results.PageDiscoveryAgent && (
              <div className="flex items-start gap-3">
                <span className="text-blue-500">🔍</span>
                <p className="text-sm text-foreground">
                  Discovered <strong>{results.PageDiscoveryAgent.pages_discovered?.length || 0}</strong> pages 
                  across your website with detailed metadata and SEO analysis.
                </p>
              </div>
            )}
            
            {results.KeywordResearchAgent && (
              <div className="flex items-start gap-3">
                <span className="text-green-500">🎯</span>
                <p className="text-sm text-foreground">
                  Identified <strong>{results.KeywordResearchAgent.keywords_found?.length || 0}</strong> target keywords 
                  with search volume and competition data from multiple sources.
                </p>
              </div>
            )}
            
            {results.ContentGenerationAgent && (
              <div className="flex items-start gap-3">
                <span className="text-purple-500">📝</span>
                <p className="text-sm text-foreground">
                  Found <strong>{results.ContentGenerationAgent.content_opportunities?.length || 0}</strong> content opportunities 
                  to improve your SEO performance and fill content gaps.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderPageDiscovery = () => {
    const pageData = results.PageDiscoveryAgent?.pages_discovered || [];
    
    return (
      <div className="space-y-6">
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Page Discovery Results ({pageData.length} pages)
          </h3>
          
          {pageData.length > 0 ? (
            <div className="space-y-4">
              {/* Page Type Distribution */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {['home', 'about', 'product', 'blog'].map((type) => {
                  const count = pageData.filter((p: any) => p.page_type === type).length;
                  return (
                    <div key={type} className="text-center p-3 bg-muted rounded-lg">
                      <p className="text-xl font-bold text-foreground">{count}</p>
                      <p className="text-sm text-muted-foreground capitalize">{type} Pages</p>
                    </div>
                  );
                })}
              </div>

              {/* Pages Table */}
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left p-2 text-foreground">URL</th>
                      <th className="text-left p-2 text-foreground">Title</th>
                      <th className="text-left p-2 text-foreground">Type</th>
                      <th className="text-left p-2 text-foreground">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pageData.slice(0, 10).map((page: any, index: number) => (
                      <tr key={index} className="border-b border-border/50">
                        <td className="p-2">
                          <a 
                            href={page.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-primary hover:underline text-xs"
                          >
                            {page.url.replace(website.url, '')}
                          </a>
                        </td>
                        <td className="p-2 text-foreground">{page.title || 'No title'}</td>
                        <td className="p-2">
                          <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                            {page.page_type || 'page'}
                          </span>
                        </td>
                        <td className="p-2">
                          <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                            {page.status || 'discovered'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {pageData.length > 10 && (
                <p className="text-sm text-muted-foreground text-center">
                  Showing 10 of {pageData.length} pages. Full results available in detailed report.
                </p>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground">No pages discovered in this analysis.</p>
          )}
        </div>
      </div>
    );
  };

  const renderKeywords = () => {
    const keywordData = results.KeywordResearchAgent?.keywords_found || [];
    const topKeywords = keywordData.slice(0, 20);
    
    return (
      <div className="space-y-6">
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Keyword Research Results ({keywordData.length} keywords)
          </h3>
          
          {topKeywords.length > 0 ? (
            <div className="space-y-4">
              {/* Keyword Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <p className="text-xl font-bold text-foreground">
                    {topKeywords.filter((k: any) => k.search_volume > 1000).length}
                  </p>
                  <p className="text-sm text-muted-foreground">High Volume</p>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <p className="text-xl font-bold text-foreground">
                    {topKeywords.filter((k: any) => k.keyword_difficulty < 30).length}
                  </p>
                  <p className="text-sm text-muted-foreground">Low Difficulty</p>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <p className="text-xl font-bold text-foreground">
                    {topKeywords.filter((k: any) => k.intent === 'commercial').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Commercial</p>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <p className="text-xl font-bold text-foreground">
                    {topKeywords.filter((k: any) => k.intent === 'informational').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Informational</p>
                </div>
              </div>

              {/* Top Keywords Table */}
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left p-2 text-foreground">Keyword</th>
                      <th className="text-left p-2 text-foreground">Volume</th>
                      <th className="text-left p-2 text-foreground">Difficulty</th>
                      <th className="text-left p-2 text-foreground">Intent</th>
                      <th className="text-left p-2 text-foreground">CPC</th>
                    </tr>
                  </thead>
                  <tbody>
                    {topKeywords.map((keyword: any, index: number) => (
                      <tr key={index} className="border-b border-border/50">
                        <td className="p-2 text-foreground font-medium">{keyword.keyword}</td>
                        <td className="p-2 text-muted-foreground">
                          {keyword.search_volume?.toLocaleString() || 'N/A'}
                        </td>
                        <td className="p-2">
                          <span className={`px-2 py-1 rounded text-xs ${
                            keyword.keyword_difficulty < 30 ? 'bg-green-100 text-green-700' :
                            keyword.keyword_difficulty < 70 ? 'bg-yellow-100 text-yellow-700' :
                            'bg-red-100 text-red-700'
                          }`}>
                            {keyword.keyword_difficulty || 'N/A'}
                          </span>
                        </td>
                        <td className="p-2">
                          <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                            {keyword.intent || 'informational'}
                          </span>
                        </td>
                        <td className="p-2 text-muted-foreground">
                          ${keyword.cpc?.toFixed(2) || '0.00'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">No keywords found in this analysis.</p>
          )}
        </div>
      </div>
    );
  };

  const renderContent = () => {
    const contentData = results.ContentGenerationAgent?.content_opportunities || [];
    
    return (
      <div className="space-y-6">
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            Content Opportunities ({contentData.length} identified)
          </h3>
          
          {contentData.length > 0 ? (
            <div className="space-y-4">
              {/* Content Type Distribution */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {['blog', 'guide', 'faq', 'landing'].map((type) => {
                  const count = contentData.filter((c: any) => c.content_type === type).length;
                  return (
                    <div key={type} className="text-center p-3 bg-muted rounded-lg">
                      <p className="text-xl font-bold text-foreground">{count}</p>
                      <p className="text-sm text-muted-foreground capitalize">{type} Content</p>
                    </div>
                  );
                })}
              </div>

              {/* Content Opportunities */}
              <div className="space-y-3">
                {contentData.slice(0, 10).map((opportunity: any, index: number) => (
                  <div key={index} className="border border-border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-foreground">{opportunity.title}</h4>
                      <span className={`px-2 py-1 rounded text-xs ${
                        opportunity.priority === 'high' ? 'bg-red-100 text-red-700' :
                        opportunity.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-green-100 text-green-700'
                      }`}>
                        {opportunity.priority || 'medium'} priority
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                      <span>🎯 {opportunity.target_keyword}</span>
                      <span>📝 {opportunity.content_type}</span>
                      <span>📊 {opportunity.estimated_traffic || 0} est. traffic</span>
                    </div>
                    
                    {opportunity.content_brief && (
                      <p className="text-sm text-muted-foreground">
                        {opportunity.content_brief}
                      </p>
                    )}
                  </div>
                ))}
              </div>
              
              {contentData.length > 10 && (
                <p className="text-sm text-muted-foreground text-center">
                  Showing 10 of {contentData.length} opportunities. 
                </p>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground">No content opportunities identified in this analysis.</p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card border rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-foreground mb-2">Analysis Results</h2>
        <p className="text-muted-foreground">
          Complete analysis results for {website.name}
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-border mb-6">
        <nav className="flex space-x-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              <span>{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'pages' && renderPageDiscovery()}
        {activeTab === 'keywords' && renderKeywords()}
        {activeTab === 'content' && renderContent()}
      </div>

      {/* Export Actions */}
      <div className="mt-8 pt-6 border-t border-border">
        <h3 className="text-lg font-semibold text-foreground mb-4">Export Results</h3>
        <div className="flex gap-3">
          <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
            📊 Download Report (PDF)
          </button>
          <button className="px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors">
            📋 Export Data (CSV)
          </button>
          <button className="px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors">
            📧 Email Report
          </button>
        </div>
      </div>
    </div>
  );
}; 