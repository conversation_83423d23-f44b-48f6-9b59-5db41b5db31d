import React, { useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  LayoutDashboard,
  FileText,
  BookOpen,
  Users,
  Settings,
  Brain,
  FolderOpen,
  MessageSquare,
  Plus,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { cn } from "../lib/utils";
import { useUser } from "../../../../base/contextapi/UserContext";
import { Button } from "../../../../base/components/ui/button";

interface LayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

const navigationItems = [
  {
    name: "Dashboard",
    href: "/scopingai/dashboard",
    icon: LayoutDashboard,
  },

  {
    name: "Proposals",
    href: "/scopingai/proposals",
    icon: FileText,
  },
  {
    name: "Knowledge Base",
    href: "/scopingai/knowledge-base",
    icon: BookOpen,
    children: [
      {
        name: "Documents",
        href: "/scopingai/knowledge-base/documents",
        icon: FileText,
      },
      {
        name: "Clients",
        href: "/scopingai/knowledge-base/clients",
        icon: Users,
      },
      {
        name: "Prompts",
        href: "/scopingai/knowledge-base/prompts",
        icon: MessageSquare,
      },
    ],
  },
];

export default function Layout({ children, requireAuth = true }: LayoutProps) {
  const { user, isLoading, hasInitiallyLoaded, error } = useUser();
  const navigate = useNavigate();
  const location = useLocation();

  // Simple redirect logic - only redirect after initial load is complete
  useEffect(() => {
    if (hasInitiallyLoaded && requireAuth && !user && !error) {
      navigate("/login");
    }
  }, [user, hasInitiallyLoaded, requireAuth, navigate, error]);

  const isActive = (href: string) => {
    if (href === "/scopingai/dashboard") {
      return (
        location.pathname === "/scopingai" ||
        location.pathname === "/scopingai/dashboard"
      );
    }
    return (
      location.pathname === href || location.pathname.startsWith(href + "/")
    );
  };

  const isChildActive = (parentHref: string, childHref: string) => {
    return location.pathname === childHref;
  };

  // Show loading only during initial authentication check
  if (!hasInitiallyLoaded && isLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-muted-foreground mt-2">
            Checking authentication...
          </p>
        </div>
      </div>
    );
  }

  // Show error state with retry option
  if (error && !hasInitiallyLoaded) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Authentication Error</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Don't render children if auth is required but user is not authenticated
  // Only check this after initial load is complete
  if (hasInitiallyLoaded && requireAuth && !user) {
    return null; // Will redirect via useEffect
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <Link
            to="/scopingai/dashboard"
            className="flex items-center space-x-3"
          >
            <div className="p-2 bg-blue-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">ScopingAI</h1>
              <p className="text-sm text-gray-600">AI-Powered Scoping</p>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navigationItems.map((item) => (
            <div key={item.name}>
              <Link
                to={item.href}
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  isActive(item.href)
                    ? "bg-blue-50 text-blue-700 border border-blue-200"
                    : "text-gray-700 hover:bg-gray-100"
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.name}</span>
              </Link>

              {/* Sub-navigation */}
              {item.children && isActive(item.href) && (
                <div className="ml-8 mt-2 space-y-1">
                  {item.children.map((child) => (
                    <Link
                      key={child.name}
                      to={child.href}
                      className={cn(
                        "flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm transition-colors",
                        isChildActive(item.href, child.href)
                          ? "bg-blue-100 text-blue-700"
                          : "text-gray-600 hover:bg-gray-50"
                      )}
                    >
                      <child.icon className="h-4 w-4" />
                      <span>{child.name}</span>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <Link
            to="/settings"
            className="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <Settings className="h-5 w-5" />
            <span>Settings</span>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
    </div>
  );
}
