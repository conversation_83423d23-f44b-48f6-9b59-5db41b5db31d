
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

type MenuItemProps = {
  title: string;
  icon: string;
  href: string;
  stats?: string;
  isNew?: boolean;
};

const MenuItem: React.FC<MenuItemProps> = ({
  title,
  icon,
  href,
  stats,
  isNew
}) => {
  const location = useLocation();
  const isActive = location.pathname === href;

  return (
    <Link
      to={href}
      className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors group ${
        isActive
          ? 'bg-primary/10 text-primary border border-primary/20'
          : 'text-foreground hover:bg-muted hover:text-foreground'
      }`}
    >
      <span className="text-lg">{icon}</span>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="truncate">{title}</span>
          {isNew && (
            <span className="bg-primary/10 text-primary text-xs px-1.5 py-0.5 rounded-full">
              NEW
            </span>
          )}
        </div>
        {stats && (
          <div className="text-xs text-muted-foreground truncate">
            {stats}
          </div>
        )}
      </div>
    </Link>
  );
};

const PSEONavigation: React.FC<{
  stats: {
    totalClients: number;
    totalWebsites: number;
    totalAudits: number;
    totalAnalysisJobs: number;
    loading: boolean;
  };
  fullSiteAnalysisResults: any[];
  contentOpportunities: any[];
  keywordResearch: any[];
}> = ({
  stats,
  fullSiteAnalysisResults,
  contentOpportunities,
  keywordResearch
}) => {
  // Define navigation items - simple menu style
  const navigationItems = [
    {
      title: 'Dashboard',
      icon: '📊',
      href: '/pseo',
      stats: 'Overview & Analytics'
    },
    {
      title: 'Client & Website Management',
      icon: '👥',
      href: '/website-management',
      stats: `${stats.totalClients} clients • ${stats.totalWebsites} websites`
    },
    {
      title: 'Create AI Blog Post',
      icon: '🤖',
      href: '/create-blog-post',
      stats: 'AI content generation',
      isNew: true
    },
    {
      title: 'Keyword Research',
      icon: '🔍',
      href: '/keyword-research',
      stats: 'Discover opportunities',
      isNew: true
    },
    {
      title: 'Full Site Analysis',
      icon: '🚀',
      href: '/full-site-analysis',
      stats: `${stats.totalAnalysisJobs} analysis jobs`
    },
    {
      title: 'Full Site Analysis Results',
      icon: '📊',
      href: '/full-site-analysis-results',
      stats: `${fullSiteAnalysisResults.length} completed analyses`
    },
    {
      title: 'Content History',
      icon: '📝',
      href: '/pseo/content-history',
      stats: `${contentOpportunities.length} content items`
    },
    {
      title: 'Keyword History',
      icon: '🎯',
      href: '/pseo/keyword-history',
      stats: `${keywordResearch.length} keywords`
    }
  ];

  return (
    <>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-8 h-8 text-green-600 mr-3 text-2xl">🚀</div>
            <h1 className="text-xl font-bold text-gray-900">pSEO</h1>
          </div>
        </div>

        {/* Back to Main Dashboard */}
        <Link
          to="/"
          className="flex items-center text-sm text-gray-600 hover:text-green-600 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Dashboard
        </Link>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-1">
          {navigationItems.map((item, index) => (
            <MenuItem
              key={index}
              title={item.title}
              icon={item.icon}
              href={item.href}
              stats={item.stats}
              isNew={item.isNew}
            />
          ))}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          pSEO Platform v1.0
        </div>
      </div>
    </>
  );
};

export default PSEONavigation;

