import React, { useState } from "react";
import { <PERSON><PERSON> } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import { Label } from "../../../../base/components/ui/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "../../../../base/components/ui/radio-group";
import { Textarea } from "../../../../base/components/ui/textarea";
import { Loader2, UserPlus, ChevronDown, User } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../../base/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "../../../../base/components/ui/command";
import { useDocumentForm } from "../contexts/DocumentFormContext";
import { EnhancedClientSelector } from "./EnhancedClientSelector";
import { EnhancedClient } from "../services/enhancedClientService";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../../../base/components/ui/tabs";

// Define StepProps interface locally instead of importing from @/types
interface StepProps {
  onNext: () => void;
  onBack: () => void;
}

const ClientInfoStep: React.FC<StepProps> = ({ onNext, onBack }) => {
  const {
    clients,
    clientData,
    selectedClient,
    isNewClient,
    clientSearchOpen,
    isSavingClient,
    isLoadingClients,
    clientsError,
    projectInfo,
    handleClientSelect,
    handleClientInputChange,
    handleProjectInfoChange,
    handleBudgetChange,
    setClientSearchOpen,
    saveClient,
    loadClients,
    isCurrentStepValid,
  } = useDocumentForm();

  // State for enhanced client selector
  const [useEnhancedSelector, setUseEnhancedSelector] = useState(true);
  const [selectedEnhancedClient, setSelectedEnhancedClient] =
    useState<EnhancedClient | null>(null);

  // Debug: Log client loading state
  console.log("ClientInfoStep - Debug info:", {
    clientsCount: clients.length,
    isLoadingClients,
    clientsError,
    clientSearchOpen,
    clientData: clientData.name,
    useEnhancedSelector,
    selectedEnhancedClient: selectedEnhancedClient?.name,
  });

  // Force load clients when component mounts if no clients are loaded
  React.useEffect(() => {
    if (
      !useEnhancedSelector &&
      !isLoadingClients &&
      clients.length === 0 &&
      !clientsError
    ) {
      console.log("ClientInfoStep: No clients found, attempting to load...");
      loadClients();
    }
  }, [useEnhancedSelector]);

  const handleEnhancedClientSelect = (client: EnhancedClient) => {
    console.log("Enhanced client selected:", client);
    setSelectedEnhancedClient(client);

    // Update the form context with the selected client data
    handleClientInputChange({
      target: { name: "name", value: client.name },
    } as any);
    handleClientInputChange({
      target: { name: "contactPerson", value: client.contactPerson },
    } as any);
    handleClientInputChange({
      target: { name: "email", value: client.email },
    } as any);
    handleClientInputChange({
      target: { name: "phone", value: client.phone },
    } as any);
    handleClientInputChange({
      target: { name: "industry", value: client.industry },
    } as any);
    handleClientInputChange({
      target: { name: "company", value: client.company },
    } as any);
  };

  const handleNext = () => {
    // If using enhanced selector and a client is selected, proceed
    if (useEnhancedSelector && selectedEnhancedClient) {
      onNext();
      return;
    }

    // If it's a new client, save it first
    if (isNewClient && clientData.name) {
      saveClient();
    } else {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold leading-tight">
        Client Information
      </h2>
      <p className="!mt-2 !text-muted-foreground">
        Enter details about the client and project
      </p>

      {/* Client Selection Tabs */}
      <Tabs
        value={useEnhancedSelector ? "enhanced" : "legacy"}
        onValueChange={(value) => setUseEnhancedSelector(value === "enhanced")}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="enhanced">All Organization Contacts</TabsTrigger>
          <TabsTrigger value="legacy">Manual Entry</TabsTrigger>
        </TabsList>

        <TabsContent value="enhanced" className="space-y-4">
          <EnhancedClientSelector
            onClientSelect={handleEnhancedClientSelect}
            selectedClientId={selectedEnhancedClient?.id}
            className="w-full"
          />

          {/* Show selected client details */}
          {selectedEnhancedClient && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  Selected Client Details
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      selectedEnhancedClient.source === "crm"
                        ? "bg-blue-100 text-blue-700"
                        : "bg-green-100 text-green-700"
                    }`}
                  >
                    {selectedEnhancedClient.source === "crm"
                      ? "CRM Contact"
                      : "ScopingAI Client"}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Name:</span>{" "}
                    {selectedEnhancedClient.name}
                  </div>
                  <div>
                    <span className="font-medium">Contact:</span>{" "}
                    {selectedEnhancedClient.contactPerson}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span>{" "}
                    {selectedEnhancedClient.email || "Not provided"}
                  </div>
                  <div>
                    <span className="font-medium">Phone:</span>{" "}
                    {selectedEnhancedClient.phone || "Not provided"}
                  </div>
                  <div>
                    <span className="font-medium">Company:</span>{" "}
                    {selectedEnhancedClient.company || "Not specified"}
                  </div>
                  <div>
                    <span className="font-medium">Industry:</span>{" "}
                    {selectedEnhancedClient.industry || "Not specified"}
                  </div>
                </div>

                {/* Organization info */}
                {(selectedEnhancedClient.organisationName ||
                  selectedEnhancedClient.organisationId) && (
                  <div className="pt-2 border-t">
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">Organization:</span>{" "}
                      {selectedEnhancedClient.organisationName ||
                        selectedEnhancedClient.organisationId}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      This contact is available through your organization
                      membership
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="legacy" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="client-name" className="flex items-center gap-1">
                Client Name <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Popover
                  open={clientSearchOpen}
                  onOpenChange={setClientSearchOpen}
                >
                  <div className="flex">
                    <Input
                      id="client-name"
                      name="name"
                      value={clientData.name}
                      onChange={handleClientInputChange}
                      placeholder={`Search existing or type new client name (required) ${
                        clients.length > 0
                          ? `(${clients.length} available)`
                          : ""
                      }`}
                      className={`w-full pr-10 ${
                        !clientData.name?.trim()
                          ? "border-red-300 focus:border-red-500"
                          : ""
                      }`}
                      onFocus={() => setClientSearchOpen(true)}
                    />
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full"
                        onClick={() => setClientSearchOpen(!clientSearchOpen)}
                      >
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </PopoverTrigger>
                  </div>
                  <PopoverContent className="p-0 w-[400px]" align="start">
                    <Command>
                      <CommandInput
                        placeholder="Search for existing client..."
                        value={clientData.name}
                        onValueChange={(value) => {
                          handleClientInputChange({
                            target: { name: "name", value },
                          } as any);
                        }}
                      />
                      <CommandEmpty>
                        <div className="px-2 py-3 text-sm text-center">
                          {isLoadingClients ? (
                            <div className="flex items-center justify-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Loading clients...</span>
                            </div>
                          ) : clientsError ? (
                            <div className="space-y-2">
                              <p className="text-destructive text-xs">
                                {clientsError}
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={loadClients}
                                className="text-xs"
                              >
                                Retry Loading
                              </Button>
                            </div>
                          ) : clientData.name ? (
                            <div className="space-y-2">
                              <p className="text-green-600">
                                ✨ Ready to create new client: "
                                {clientData.name}"
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Continue to next step to save this new client
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <p>
                                No clients found. Enter details for a new
                                client.
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  console.log("Manual refresh clicked");
                                  loadClients();
                                }}
                                className="text-xs"
                              >
                                Refresh Clients
                              </Button>
                            </div>
                          )}
                        </div>
                      </CommandEmpty>
                      <CommandGroup>
                        {!isLoadingClients &&
                          !clientsError &&
                          clients
                            .filter((client) =>
                              client.name
                                .toLowerCase()
                                .includes(clientData.name.toLowerCase())
                            )
                            .map((client) => (
                              <CommandItem
                                key={client.id}
                                value={client.id}
                                onSelect={() => {
                                  handleClientSelect(client.id);
                                  setClientSearchOpen(false);
                                }}
                              >
                                <User className="mr-2 h-4 w-4" />
                                <div className="flex flex-col">
                                  <span>{client.name}</span>
                                  {client.email && (
                                    <span className="text-xs text-muted-foreground">
                                      {client.email}
                                    </span>
                                  )}
                                </div>
                              </CommandItem>
                            ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              {!clientData.name?.trim() && (
                <p className="text-sm text-red-500 mt-1">
                  Client name is required
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contact-person">Contact Person</Label>
              <Input
                id="contact-person"
                name="contactPerson"
                value={clientData.contactPerson}
                onChange={handleClientInputChange}
                placeholder="Contact person name"
              />
            </div>
          </div>

          {clientData.name && !selectedClient && (
            <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
              💡 New client "{clientData.name}" will be created
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-1">
                Email <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={clientData.email}
                onChange={handleClientInputChange}
                placeholder="Contact email (required)"
                className={
                  !clientData.email?.trim()
                    ? "border-red-300 focus:border-red-500"
                    : ""
                }
              />
              {!clientData.email?.trim() && (
                <p className="text-sm text-red-500 mt-1">
                  Client email is required
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                value={clientData.phone}
                onChange={handleClientInputChange}
                placeholder="Contact phone"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="industry">Industry</Label>
            <Input
              id="industry"
              name="industry"
              value={clientData.industry}
              onChange={handleClientInputChange}
              placeholder="Client industry"
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* Project Information Section - Always visible */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold !mt-8">Project Information</h2>

        <div className="space-y-2">
          <Label htmlFor="project-title" className="flex items-center gap-1">
            Project Title <span className="text-red-500">*</span>
          </Label>
          <Input
            id="project-title"
            placeholder="Enter the title of your project (required)"
            value={projectInfo.title}
            onChange={handleProjectInfoChange}
            className={
              !projectInfo.title?.trim()
                ? "border-red-300 focus:border-red-500"
                : ""
            }
          />
          {!projectInfo.title?.trim() && (
            <p className="text-sm text-red-500 mt-1">
              Project title is required
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label
            htmlFor="project-description"
            className="flex items-center gap-1"
          >
            Project Description <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="project-description"
            placeholder="Brief description of the project and its goals (required)"
            rows={4}
            value={projectInfo.description}
            onChange={handleProjectInfoChange}
            className={
              !projectInfo.description?.trim()
                ? "border-red-300 focus:border-red-500"
                : ""
            }
          />
          {!projectInfo.description?.trim() && (
            <p className="text-sm text-red-500 mt-1">
              Project description is required
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="start-date">Expected Start Date</Label>
            <Input
              id="start-date"
              type="date"
              value={projectInfo.startDate}
              onChange={handleProjectInfoChange}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="end-date">Expected End Date</Label>
            <Input
              id="end-date"
              type="date"
              value={projectInfo.endDate}
              onChange={handleProjectInfoChange}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="budget">Budget Range</Label>
          <RadioGroup
            defaultValue="medium"
            className="flex flex-col space-y-1"
            value={projectInfo.budget}
            onValueChange={handleBudgetChange}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="small" id="budget-small" />
              <Label htmlFor="budget-small">$1,000 - $5,000</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="medium" id="budget-medium" />
              <Label htmlFor="budget-medium">$5,000 - $15,000</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="large" id="budget-large" />
              <Label htmlFor="budget-large">$15,000 - $50,000</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="enterprise" id="budget-enterprise" />
              <Label htmlFor="budget-enterprise">$50,000+</Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between mt-8">
        <Button variant="outline" onClick={onBack} disabled>
          Back
        </Button>

        <div className="flex gap-2">
          {isNewClient && clientData.name && (
            <Button
              onClick={saveClient}
              disabled={isSavingClient}
              variant="outline"
              className="gap-2"
            >
              {isSavingClient ? (
                <Loader2 size={16} className="mr-2 animate-spin" />
              ) : (
                <UserPlus size={16} className="mr-2" />
              )}
              Save Client & Next
            </Button>
          )}

          <Button onClick={handleNext} disabled={!isCurrentStepValid()}>
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ClientInfoStep;
