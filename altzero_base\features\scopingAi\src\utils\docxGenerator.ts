import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  AlignmentType,
  PageBreak,
  <PERSON>er,
  <PERSON>er,
  ImageRun,
  SectionType,
  BorderStyle,
  WidthType,
} from "docx";
import { saveAs } from "file-saver";

export interface DocumentData {
  title?: string;
  client?: string;
  type?: string;
  content?: string;
  sections?: Array<{
    title: string;
    content: string;
    description?: string;
    imageData?: Array<{
      data: string;
      width?: number;
      height?: number;
      position?: {
        x: number;
        y: number;
        page?: number;
        width?: number;
        height?: number;
      };
    }>;
  }>;
  editableSections?: Array<{
    id: string;
    title: string;
    content: string;
    blocks?: Array<{
      type: string;
      content: any;
      items?: string[];
    }>;
  }>;
}

export const generateDOCX = async (
  documentData: DocumentData,
  documentTheme: any,
  filename: string
) => {
  try {
    // Create cover page elements
    const coverPageChildren: any[] = [];

    // Logo placeholder (if available)
    if (documentTheme?.logo) {
      // Add space for logo
      coverPageChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "",
            }),
          ],
          spacing: {
            after: 800,
          },
        })
      );
    }

    // Title on cover page
    coverPageChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: documentData.title || "Document Title",
            bold: true,
            size: 48,
            color: documentTheme?.headingColor?.replace("#", "") || "1a365d",
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          before: documentTheme?.logo ? 400 : 1200,
          after: 600,
        },
      })
    );

    // Client information on cover page
    if (documentData.client) {
      coverPageChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `Prepared for: ${documentData.client}`,
              size: 28,
              color: documentTheme?.textColor?.replace("#", "") || "2d3748",
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 400,
          },
        })
      );
    }

    // Document type on cover page
    if (documentData.type) {
      coverPageChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: documentData.type,
              size: 24,
              italics: true,
              color: documentTheme?.textColor?.replace("#", "") || "2d3748",
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 800,
          },
        })
      );
    }

    // Date on cover page
    const currentDate = new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    coverPageChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: currentDate,
            size: 20,
            color: documentTheme?.textColor?.replace("#", "") || "2d3748",
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          before: 1200,
        },
      })
    );

    // Page break after cover page
    coverPageChildren.push(
      new Paragraph({
        children: [new PageBreak()],
      })
    );

    // Content pages
    const contentChildren: any[] = [];

    // Process sections for content
    const sections =
      documentData.editableSections || documentData.sections || [];

    if (sections.length > 0) {
      sections.forEach((section: any, index: number) => {
        // Section title with appropriate spacing
        contentChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text: section.title || section.name || `Section ${index + 1}`,
                bold: true,
                size: 28,
                color:
                  documentTheme?.headingColor?.replace("#", "") || "1a365d",
              }),
            ],
            heading: HeadingLevel.HEADING_1,
            spacing: {
              before: index === 0 ? 200 : 600, // Less space for first section, more for subsequent
              after: 300,
            },
            border: {
              bottom: {
                color: documentTheme?.accentColor?.replace("#", "") || "3182ce",
                space: 1,
                style: BorderStyle.SINGLE,
                size: 6,
              },
            },
          })
        );

        // Section content
        const content = section.content || section.description || "";
        if (content) {
          // Process content with better formatting
          const paragraphs = content.split("\n\n");

          paragraphs.forEach((paragraph: string) => {
            if (paragraph.trim()) {
              // Check if it's a heading (starts with #)
              if (paragraph.trim().startsWith("#")) {
                const headingText = paragraph.replace(/^#+\s*/, "").trim();
                contentChildren.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: headingText,
                        bold: true,
                        size: 24,
                        color:
                          documentTheme?.subheadingColor?.replace("#", "") ||
                          "2d3748",
                      }),
                    ],
                    heading: HeadingLevel.HEADING_2,
                    spacing: {
                      before: 300,
                      after: 200,
                    },
                  })
                );
              } else {
                // Regular paragraph
                contentChildren.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: paragraph.trim(),
                        size: 22,
                        color:
                          documentTheme?.textColor?.replace("#", "") ||
                          "2d3748",
                      }),
                    ],
                    spacing: {
                      after: 240,
                    },
                    alignment: AlignmentType.JUSTIFIED,
                  })
                );
              }
            }
          });
        }

        // Add minimal space between sections (sections will flow continuously)
        if (index < sections.length - 1) {
          contentChildren.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: "",
                }),
              ],
              spacing: {
                after: 200, // Reduced spacing between sections
              },
            })
          );
        }
      });
    } else {
      // No content available
      contentChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "No content available to display.",
              size: 22,
              color: "666666",
              italics: true,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            before: 600,
          },
        })
      );
    }

    // Create the document with separate sections for cover and content
    const doc = new Document({
      creator: "ScopingAI",
      title: documentData.title || "Document",
      description: `Generated document for ${documentData.client || "client"}`,
      sections: [
        // Cover page section
        {
          properties: {
            type: SectionType.NEXT_PAGE,
            page: {
              margin: {
                top: 1440, // 1 inch
                right: 1440,
                bottom: 1440,
                left: 1440,
              },
            },
          },
          children: coverPageChildren,
        },
        // Content section
        {
          properties: {
            type: SectionType.NEXT_PAGE,
            page: {
              margin: {
                top: 1440, // 1 inch
                right: 1440,
                bottom: 1440,
                left: 1440,
              },
              pageNumbers: {
                start: 1,
                formatType: "decimal",
              },
            },
          },
          headers: {
            default: new Header({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: documentData.title || "Document",
                      size: 18,
                      color: "666666",
                    }),
                  ],
                  alignment: AlignmentType.LEFT,
                }),
              ],
            }),
          },
          footers: {
            default: new Footer({
              children: [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: "Page ",
                      size: 18,
                      color: "666666",
                    }),
                  ],
                  alignment: AlignmentType.CENTER,
                }),
              ],
            }),
          },
          children: contentChildren,
        },
      ],
    });

    // Generate and save the document
    const blob = await Packer.toBlob(doc);
    saveAs(blob, filename);

    return true;
  } catch (error) {
    console.error("Error generating DOCX:", error);
    throw error;
  }
};
