import type { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';
import type { saveAs } from 'file-saver';

export interface DocumentData {
  title?: string;
  client?: string;
  type?: string;
  content?: string;
  sections?: Array<{
    title: string;
    content: string;
    description?: string;
    imageData?: Array<{
      data: string;
      width?: number;
      height?: number;
      position?: {
        x: number;
        y: number;
        page?: number;
        width?: number;
        height?: number;
      };
    }>;
  }>;
}

export const generateDOCX = async (
  documentData: DocumentData,
  documentTheme: any, // TODO: Future enhancement - apply theme colors and fonts
  filename: string
) => {
  try {
    const children: any[] = [];

    // Add title
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: documentData.title || 'Untitled Document',
            bold: true,
            size: 32,
          }),
        ],
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 400,
        },
      })
    );

    // Add client info if available
    if (documentData.client) {
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `Client: ${documentData.client}`,
              bold: true,
              size: 24,
            }),
          ],
          spacing: {
            after: 200,
          },
        })
      );
    }

    // Add document type if available
    if (documentData.type) {
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `Document Type: ${documentData.type}`,
              size: 22,
            }),
          ],
          spacing: {
            after: 300,
          },
        })
      );
    }

    // Add sections
    if (documentData.sections && Array.isArray(documentData.sections)) {
      documentData.sections.forEach((section: any) => {
        // Section title
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: section.title || section.name || 'Untitled Section',
                bold: true,
                size: 24,
              }),
            ],
            heading: HeadingLevel.HEADING_1,
            spacing: {
              before: 300,
              after: 200,
            },
          })
        );

        // Section content
        const content = section.content || section.description || '';
        if (content) {
          // Split content by paragraphs (double line breaks)
          const paragraphs = content.split('\n\n');
          
          paragraphs.forEach((paragraph: string) => {
            if (paragraph.trim()) {
              children.push(
                new Paragraph({
                  children: [
                    new TextRun({
                      text: paragraph.trim(),
                      size: 22,
                    }),
                  ],
                  spacing: {
                    after: 200,
                  },
                })
              );
            }
          });
        }
      });
    }

    // Add blocks if available (from editableSections)
    if (documentData.editableSections && Array.isArray(documentData.editableSections)) {
      documentData.editableSections.forEach((section: any) => {
        // Section title
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: section.title || 'Untitled Section',
                bold: true,
                size: 24,
              }),
            ],
            heading: HeadingLevel.HEADING_1,
            spacing: {
              before: 300,
              after: 200,
            },
          })
        );

        // Process blocks
        if (section.blocks && Array.isArray(section.blocks)) {
          section.blocks.forEach((block: any) => {
            if (block.type === 'text' && block.content) {
              children.push(
                new Paragraph({
                  children: [
                    new TextRun({
                      text: block.content,
                      size: 22,
                    }),
                  ],
                  spacing: {
                    after: 200,
                  },
                })
              );
            } else if (block.type === 'heading' && block.content) {
              children.push(
                new Paragraph({
                  children: [
                    new TextRun({
                      text: block.content,
                      bold: true,
                      size: 26,
                    }),
                  ],
                  heading: HeadingLevel.HEADING_2,
                  spacing: {
                    before: 200,
                    after: 150,
                  },
                })
              );
            } else if (block.type === 'list' && block.items) {
              block.items.forEach((item: string) => {
                children.push(
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: `• ${item}`,
                        size: 22,
                      }),
                    ],
                    spacing: {
                      after: 100,
                    },
                  })
                );
              });
            }
          });
        }
      });
    }

    // If no content, add a placeholder
    if (children.length === 1) { // Only title
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: 'No content available to display.',
              size: 22,
            }),
          ],
          spacing: {
            before: 300,
          },
        })
      );
    }

    // Create the document
    const doc = new Document({
      sections: [{
        properties: {},
        children: children,
      }],
    });

    // Generate and save the document
    const blob = await Packer.toBlob(doc);
    saveAs(blob, filename);
    
    return true;
  } catch (error) {
    console.error('Error generating DOCX:', error);
    throw error;
  }
}; 