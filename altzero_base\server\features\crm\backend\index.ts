import express from 'express';
import { BackendPlugin } from '../../../plugins/loader';
import crmRoutes from '../routes/crm';

// Create the backend plugin for CRM
const crmBackendPlugin: BackendPlugin = {
  router: crmRoutes,
  config: {
    name: 'CRM API',
    version: '1.0.0',
    apiPrefix: '/api/crm'
  },
  initialize: async () => {
    console.log('🏢 CRM backend plugin initialized');
  },
  cleanup: async () => {
    console.log('🏢 CRM backend plugin cleaned up');
  },
  healthCheck: async () => {
    try {
      // Basic health check - could be expanded to check database connectivity
      return true;
    } catch (error) {
      console.error('CRM backend health check failed:', error);
      return false;
    }
  }
};

export default crmBackendPlugin;
