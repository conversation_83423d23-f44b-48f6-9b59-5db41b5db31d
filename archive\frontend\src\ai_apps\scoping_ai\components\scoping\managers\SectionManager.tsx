import React, { useState, useEffect } from "react";
import Layout from "../../../../../components/Layout";
import { supabase } from "../../../../../utils/supabaseClient";

// Section item type from database
interface SectionItem {
  id: string;
  sections: {
    title: string;
    description?: string;
    content?: string;
    order?: number;
  }[];
  created_at?: string;
  user_id?: string;
}

// API functions for database interaction
const fetchSections = async (): Promise<SectionItem[]> => {
  try {
    // Fetch sections without user filter
    const { data, error } = await supabase
      .from("sections")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching sections:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching sections:", error);
    return [];
  }
};

const saveSection = async (section: SectionItem): Promise<SectionItem> => {
  try {
    // Get the current user's ID
    const { data: user } = await supabase.auth.getUser();

    if (!user?.user?.id) {
      throw new Error("User is not authenticated");
    }

    // Prepare the data for insert/update
    const sectionData = {
      user_id: user.user.id,
      sections: section.sections || [],
    };

    let result;

    // If section has an ID, update it, otherwise insert a new one
    if (section.id && !section.id.startsWith("new-")) {
      const { data, error } = await supabase
        .from("sections")
        .update(sectionData)
        .eq("id", section.id)
        .select()
        .single();

      if (error) throw error;
      result = data;
    } else {
      const { data, error } = await supabase
        .from("sections")
        .insert(sectionData)
        .select()
        .single();

      if (error) throw error;
      result = data;
    }

    return result;
  } catch (error) {
    console.error("Error saving section:", error);
    throw error;
  }
};

const deleteSection = async (sectionId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from("sections")
      .delete()
      .eq("id", sectionId);

    if (error) throw error;
  } catch (error) {
    console.error("Error deleting section:", error);
    throw error;
  }
};

const SectionManager: React.FC = () => {
  // Section management state
  const [sections, setSections] = useState<SectionItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSection, setSelectedSection] = useState<SectionItem | null>(
    null
  );
  const [isEditing, setIsEditing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sectionTitle, setSectionTitle] = useState("");
  const [sectionDescription, setSectionDescription] = useState("");
  const [sectionContent, setSectionContent] = useState("");

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const sectionData = await fetchSections();
      setSections(sectionData);
      setLoading(false);
    } catch (error) {
      console.error("Error loading sections:", error);
      setLoading(false);
    }
  };

  // Section Tab Handlers
  const handleCreateSection = () => {
    const newSection: SectionItem = {
      id: `new-${Date.now()}`,
      sections: [{ title: "", description: "", content: "" }],
    };
    setSelectedSection(newSection);
    setSectionTitle("");
    setSectionDescription("");
    setSectionContent("");
    setIsEditing(true);
  };

  const handleEditSection = (section: SectionItem) => {
    setSelectedSection(section);
    // Set the first section's title and description for editing
    if (section.sections && section.sections.length > 0) {
      setSectionTitle(section.sections[0].title || "");
      setSectionDescription(section.sections[0].description || "");
      setSectionContent(section.sections[0].content || "");
    } else {
      setSectionTitle("");
      setSectionDescription("");
      setSectionContent("");
    }
    setIsEditing(true);
  };

  const handleDeleteSection = async (section: SectionItem) => {
    const firstSectionTitle =
      section.sections && section.sections.length > 0
        ? section.sections[0].title
        : "this section";
    if (
      window.confirm(`Are you sure you want to delete "${firstSectionTitle}"?`)
    ) {
      try {
        await deleteSection(section.id);
        setSections((prevSections) =>
          prevSections.filter((s) => s.id !== section.id)
        );
      } catch (error) {
        console.error("Error deleting section:", error);
      }
    }
  };

  const handleSaveSection = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedSection) return;

    try {
      // Update the selected section with the current title and description
      const updatedSection = {
        ...selectedSection,
        sections: [
          {
            title: sectionTitle,
            description: sectionDescription,
            content: sectionContent,
          },
        ],
      };

      const savedSection = await saveSection(updatedSection);

      setSections((prevSections) => {
        const existingIndex = prevSections.findIndex(
          (s) => s.id === savedSection.id
        );
        if (existingIndex >= 0) {
          // Update existing section
          const newSections = [...prevSections];
          newSections[existingIndex] = savedSection;
          return newSections;
        } else {
          // Add new section
          return [...prevSections, savedSection];
        }
      });

      setIsEditing(false);
      setSelectedSection(null);
      setSectionTitle("");
      setSectionDescription("");
      setSectionContent("");
    } catch (error) {
      console.error("Error saving section:", error);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSelectedSection(null);
    setSectionTitle("");
    setSectionDescription("");
    setSectionContent("");
  };

  const filteredSections = sections.filter((section) =>
    section.sections && section.sections.length > 0
      ? section.sections[0].title
          .toLowerCase()
          .includes(searchTerm.toLowerCase())
      : false
  );

  return (
    <Layout>
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Section Manager</h1>
          <button
            onClick={handleCreateSection}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Create New Section
          </button>
        </div>

        {/* Search */}
        <div className="mb-6">
          <input
            type="text"
            placeholder="Search sections..."
            className="w-full p-2 border border-gray-300 rounded"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {isEditing ? (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-4">
                  {selectedSection && selectedSection.id.startsWith("new-")
                    ? "Create New Section"
                    : "Edit Section"}
                </h2>
                <form onSubmit={handleSaveSection}>
                  <div className="grid grid-cols-1 gap-4 mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Section Title
                      </label>
                      <input
                        type="text"
                        value={sectionTitle}
                        onChange={(e) => setSectionTitle(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        value={sectionDescription}
                        onChange={(e) => setSectionDescription(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded"
                        rows={4}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Content
                      </label>
                      <textarea
                        value={sectionContent}
                        onChange={(e) => setSectionContent(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded"
                        rows={10}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2 mt-4">
                    <button
                      type="button"
                      onClick={handleCancelEdit}
                      className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-100"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Save Section
                    </button>
                  </div>
                </form>
              </div>
            ) : (
              <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                {filteredSections.length > 0 ? (
                  filteredSections.map((section) => (
                    <div
                      key={section.id}
                      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                    >
                      <div className="p-4">
                        <h3 className="font-medium text-lg mb-1">
                          {section.sections && section.sections.length > 0
                            ? section.sections[0].title
                            : "Untitled"}
                        </h3>
                        {section.sections &&
                          section.sections.length > 0 &&
                          section.sections[0].description && (
                            <p className="text-gray-600 text-sm mb-2">
                              {section.sections[0].description}
                            </p>
                          )}
                        {section.sections &&
                          section.sections.length > 0 &&
                          section.sections[0].content && (
                            <div className="mt-2 border-t border-gray-100 pt-2">
                              <div className="text-sm text-gray-700 line-clamp-3">
                                {section.sections[0].content}
                              </div>
                            </div>
                          )}
                        <div className="flex justify-end space-x-2 mt-3">
                          <button
                            onClick={() => handleEditSection(section)}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteSection(section)}
                            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center py-8 text-gray-500">
                    No sections found. Create your first section to get started.
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default SectionManager;
