# Progressive Modular Scoping System Design

## Architecture Overview

The Progressive Modular Scoping System is a comprehensive solution for creating, managing, and enhancing scoping documents with AI assistance. The system employs a modular approach where document components can be stored, reused, and progressively enhanced.

## 1. Current Implementation

### 1.1 Screens/Pages

- **Document Creator**: Central hub for creating scoping documents with template selection
- **Document Editor**: Progressive editing environment with section enhancement and real-time updates
- **Section Manager**: Interface for creating and managing reusable section templates

### 1.2 Data Layer

- **State Management**: React hooks for document and section state management
- **API Services**: Backend services for AI generation with Server-Sent Events (SSE)
- **Local Fallbacks**: Graceful degradation when backend services are unavailable
- **Database Storage**: Supabase/PostgreSQL for storing document templates and user data

### 1.3 Core Features

- Progressive document creation with template support
- Real-time section generation with SSE updates
- Markdown-based section editing
- PDF/HTML document export
- Debug capabilities for troubleshooting
- User-specific template management with sharing capabilities

## 2. Current Features & Workflows

### 2.1 Content Management

- Users can create and manage:
  - Section templates defining document structure
  - Document metadata including project and client information
  - Template information for AI generation

### 2.2 Section Template Management

1. Users can create personalized section templates through the Section Manager
2. Templates are stored in Supabase with user-level permissions
3. Templates can be shared across users by setting as public
4. Section templates contain structured sections with title, description, content, and ordering
5. Templates can be edited, deleted, and previewed before use
6. When creating a document, users can select from their own templates or public templates

### 2.3 Progressive Document Creation

1. User creates a new document with project and client information
2. User selects from available section templates (including their own created templates)
3. System generates base document structure from selected template
4. User accesses Document Editor with section side panel
5. User can generate individual sections or trigger "Generate All"
6. Each section is generated via SSE API calls, maintaining context
7. Document updates in real-time as sections are completed

### 2.4 User-specific Template Management

1. All templates (prompt, scope, section) are stored with user ID references
2. Users can only access their own templates or public templates
3. Data is fetched directly from Supabase using the authenticated user's ID
4. Templates include metadata like creation and update timestamps
5. Row-level security policies enforce data isolation and access control
6. Frontend components dynamically load the user's templates from the database

### 2.5 Section-Based Editing

- Each section contains formatted content with markdown support
- Rich text preview with headers, lists, code blocks, etc.
- In-place section editing with save/cancel functionality
- Real-time status updates during generation
- Debug capabilities for troubleshooting

### 2.6 Document Export

- Export documents as HTML with styling
- PDF export capability via browser rendering
- Maintains document structure and formatting
- Includes metadata and client information

## 3. Technical Implementation

### 3.1 Component Structure

```typescript
// Core document types
interface Section {
  id: string;
  title: string;
  content: string;
  status: SectionStatus;
  order: number;
  updatedAt: Date;
  description?: string;
}

interface ExtendedScopingDocument {
  id: string;
  userId: string;
  name: string;
  projectName: string;
  clientName: string;
  documentId?: string;
  baseContent: {
    description: string;
    blocks?: Block[];
  };
  sections: Section[];
  clientInfo?: any;
  scopingInfo?: any;
  templateInfo?: {
    promptTemplate?: {
      id: string;
      name: string;
      content: string;
    };
    scopeTemplate?: {
      id: string;
      name: string;
      description?: string;
    };
    sectionTemplate?: {
      id: string;
      name: string;
      description?: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

type SectionStatus = "pending" | "generating" | "completed" | "failed";
```

### 3.2 Component Hierarchy

```
App
├── DocumentCreator
│   └── InitialPromptForm
├── SectionManager
│   ├── TemplateEditor
│   └── TemplateList
└── DocumentEditor
    ├── DocumentHeader
    ├── SectionSidePanel
    │   └── SectionStatusIndicator
    └── SectionEditor
        └── ContentRenderer
```

### 3.3 State Management

- `useProgressiveScoping` hook for document state management
- Real-time section status tracking
- SSE-based content generation with progress updates
- Error handling and recovery mechanisms
- Timeout protection to prevent UI freezes

### 3.4 Content Generation Flow

```
┌─ Document Creator ─┐     ┌─ SSE API ─────────┐     ┌─ Section Editor ─┐
│ 1. Create document │     │ 3. Stream content │     │ 5. Display and   │
│ 2. Select template│ ──▶ │    generation     │ ──▶ │    edit content  │
└──────────────────┘     └──────────────────┘     └─────────────────┘
                                  │
                                  ▼
┌─ Status Updates ──┐     ┌─ Event Processing ─┐
│ 4. Track progress │ ◀── │    - section      │
│    and errors    │     │    - research      │
└──────────────────┘     │    - completed     │
                         └──────────────────┘
```

### 3.5 API Endpoints

The system uses the following key API endpoints:

```
POST /scoping/stream         - Stream section generation events
POST /documents/:id/export   - Export document as PDF/HTML
```

## 4. Database Implementation

### 4.1 Database Schema

#### 4.1.1 Primary Tables

- **users**: Auth and user information
- **scoping_documents**:
  ```sql
  CREATE TABLE scoping_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    scope_type TEXT NOT NULL, -- client, internal, etc.
    client_id UUID REFERENCES clients,
    scope_template_id UUID REFERENCES scope_templates,
    prompt_template_id UUID REFERENCES prompt_templates,
    section_template_id UUID REFERENCES section_templates,
    base_content JSONB,
    sections JSONB, -- contains section data with generation status
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
  ```

#### 4.1.2 Supporting Tables

- **clients**:

  ```sql
  CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    contact_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
  ```

- **scope_templates**:

  ```sql
  CREATE TABLE scope_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    content JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
  ```

- **prompt_templates**:

  ```sql
  CREATE TABLE prompt_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    system_prompt TEXT,
    user_prompt TEXT,
    variables JSONB, -- defines customizable variables
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
  ```

- **section_templates**:
  ```sql
  CREATE TABLE section_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    sections JSONB NOT NULL DEFAULT '[]', -- array of section definitions with id, title, description, content, order
    is_public BOOLEAN DEFAULT FALSE, -- Whether template is publicly available to all users
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
  ```

### 4.2 Row Level Security Policies

To ensure proper data isolation in a multi-user environment, Row Level Security (RLS) policies are implemented:

#### 4.2.1 Section Templates RLS Policies

```sql
-- Enable Row Level Security
ALTER TABLE section_templates ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select their own templates or public ones
CREATE POLICY "Users can view their own templates or public ones"
  ON section_templates
  FOR SELECT USING (
    auth.uid() = user_id OR
    is_public = TRUE
  );

-- Create policy to allow users to insert their own templates
CREATE POLICY "Users can insert their own templates"
  ON section_templates
  FOR INSERT WITH CHECK (
    auth.uid() = user_id
  );

-- Create policy to allow users to update their own templates
CREATE POLICY "Users can update their own templates"
  ON section_templates
  FOR UPDATE USING (
    auth.uid() = user_id
  );

-- Create policy to allow users to delete their own templates
CREATE POLICY "Users can delete their own templates"
  ON section_templates
  FOR DELETE USING (
    auth.uid() = user_id
  );
```

## 5. Current Implementation Details

### 5.1 Section Template Management

- Section Manager UI for creating, editing, and deleting templates
- Templates stored in Supabase with user-specific access controls
- Public/private template visibility options
- Structured section format with title, description, content, and ordering
- Drag-and-drop section reordering
- Ability to customize section content before generation

### 5.2 Section Generation Process

- SSE-based streaming for real-time updates
- Event types: section, research, summary, completed
- Progress tracking with status indicators
- Error handling with recovery options
- Timeout mechanism to prevent UI freezes (60 second default)
- Graceful fallback to local generation when API fails

### 5.3 Content Processing

- Markdown parsing and rendering
- Block-based content structure
- Real-time preview during editing
- Content validation and cleanup

### 5.4 Error Handling

- Generation retry capabilities
- Detailed error logging
- User-friendly error messages
- Debug mode for detailed inspection
- Fallbacks when backend services are unavailable
- Progressive degradation instead of complete failure

### 5.5 Document Export

- HTML/CSS based document export
- Proper formatting of markdown content
- Document metadata and section organization
- Client and project information included
- Local export capability when API is unavailable
- Download mechanism for exported documents

## 6. Future Enhancements

### 6.1 Additional Template Types

- Enhanced template management for all template types
- Template version history
- Template import/export
- Template tagging and categorization
- AI-assisted template generation

### 6.2 Additional Features

- Collaborative editing capabilities
- Additional export formats (DOCX, PDF)
- Integration with external systems
- Advanced permission management
- Template marketplace or gallery

### 6.3 Technical Improvements

- Enhanced error recovery
- Performance optimizations
- Extended debugging capabilities
- Additional content formats support

### 6.4 Planned API Endpoints

- Document Management:

  - `POST /api/documents`: Create new document
  - `GET /api/documents`: List documents
  - `GET /api/documents/:id`: Get document by ID
  - `PUT /api/documents/:id`: Update document
  - `DELETE /api/documents/:id`: Delete document

- Progressive Generation:

  - `POST /api/documents/:id/generate-base`: Generate base document
  - `POST /api/documents/:id/sections/:sectionId/generate`: Generate specific section
  - `POST /api/documents/:id/generate-all`: Generate all pending sections

- Template Management:
  - `POST /api/section-templates`: Create template
  - `GET /api/section-templates`: List templates
  - `GET /api/section-templates/:id`: Get template by ID
  - `PUT /api/section-templates/:id`: Update template
  - `DELETE /api/section-templates/:id`: Delete template
