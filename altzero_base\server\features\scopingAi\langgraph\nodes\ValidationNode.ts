// =====================================================
// VALIDATION NODE - SCOPINGAI LANGGRAPH
// =====================================================

import { BaseNode, ValidationNodeResult } from '../types/NodeTypes';
import { ScopingAiWorkflowState, WorkflowContext } from '../types/WorkflowState';

export class ValidationNode implements BaseNode {
  name = 'validation';
  description = 'Validates input data and ensures all required fields are present';

  async execute(context: WorkflowContext): Promise<Partial<ScopingAiWorkflowState>> {
    const { state, logger } = context;
    
    logger.info('Starting input validation', {
      workflow_id: state.workflow_id,
      client_name: state.client?.name,
      project_title: state.project?.title
    });

    try {
      const validationResult = await this.validateInput(state, logger);
      
      if (!validationResult.validation_passed) {
        logger.error('Validation failed', {
          errors: validationResult.validation_errors,
          warnings: validationResult.validation_warnings
        });

        return {
          status: 'failed',
          current_step: 'validation_failed',
          progress: 5,
          errors: [
            {
              node_name: this.name,
              error_message: `Validation failed: ${validationResult.validation_errors.join(', ')}`,
              error_code: 'VALIDATION_FAILED',
              timestamp: new Date().toISOString(),
              recoverable: false
            }
          ],
          last_updated: new Date().toISOString()
        };
      }

      logger.info('Validation completed successfully', {
        warnings: validationResult.validation_warnings.length,
        input_summary: validationResult.input_summary
      });

      return {
        current_step: 'validation_completed',
        progress: 10,
        warnings: validationResult.validation_warnings,
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          validation: validationResult
        }
      };

    } catch (error) {
      logger.error('Validation node execution failed', error);
      throw error;
    }
  }

  private async validateInput(state: ScopingAiWorkflowState, logger: any): Promise<ValidationNodeResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate client information
    if (!state.client) {
      errors.push('Client information is required');
    } else {
      if (!state.client.name || state.client.name.trim().length === 0) {
        errors.push('Client name is required');
      }
      if (!state.client.industry || state.client.industry.trim().length === 0) {
        warnings.push('Client industry not specified - may affect proposal quality');
      }
      if (!state.client.email && !state.client.phone) {
        warnings.push('No client contact information provided');
      }
    }

    // Validate project information
    if (!state.project) {
      errors.push('Project information is required');
    } else {
      if (!state.project.title || state.project.title.trim().length === 0) {
        errors.push('Project title is required');
      }
      if (!state.project.description || state.project.description.trim().length === 0) {
        errors.push('Project description is required');
      }
      if (state.project.description && state.project.description.length < 50) {
        warnings.push('Project description is very short - consider adding more details');
      }
      if (!state.project.timeline) {
        warnings.push('Project timeline not specified');
      }
      if (!state.project.budget_range) {
        warnings.push('Budget range not specified');
      }
    }

    // Validate template information
    if (!state.template) {
      errors.push('Template information is required');
    } else {
      if (!state.template.name || state.template.name.trim().length === 0) {
        errors.push('Template name is required');
      }
      if (!state.template.sections || state.template.sections.length === 0) {
        errors.push('Template must have at least one section');
      }
      if (state.template.sections && state.template.sections.length > 15) {
        warnings.push('Template has many sections - proposal may be very long');
      }
    }

    // Validate knowledge base documents
    if (state.selected_knowledge_documents && state.selected_knowledge_documents.length > 0) {
      if (!state.document_requirements || Object.keys(state.document_requirements).length === 0) {
        warnings.push('Knowledge base documents selected but no specific requirements provided');
      }
      
      // Check if requirements match selected documents
      const missingRequirements = state.selected_knowledge_documents.filter(
        docId => !state.document_requirements[docId]
      );
      
      if (missingRequirements.length > 0) {
        warnings.push(`${missingRequirements.length} selected documents have no specific requirements`);
      }
    }

    // Validate user ID
    if (!state.user_id || state.user_id.trim().length === 0) {
      errors.push('User ID is required');
    }

    // Validate workflow configuration
    if (state.config) {
      if (state.config.timeout_seconds < 60) {
        warnings.push('Workflow timeout is very short - may cause failures');
      }
      if (state.config.quality_threshold < 0.5) {
        warnings.push('Quality threshold is very low - may produce poor results');
      }
      if (state.config.max_sections > 20) {
        warnings.push('Maximum sections is very high - proposal may be extremely long');
      }
    }

    // Create input summary
    const input_summary = {
      client_name: state.client?.name || 'Unknown',
      project_title: state.project?.title || 'Unknown',
      sections_count: state.template?.sections?.length || 0,
      knowledge_docs_count: state.selected_knowledge_documents?.length || 0
    };

    logger.debug('Validation summary', {
      errors_count: errors.length,
      warnings_count: warnings.length,
      input_summary
    });

    return {
      validation_passed: errors.length === 0,
      validation_errors: errors,
      validation_warnings: warnings,
      input_summary
    };
  }

  // Additional validation methods
  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private validatePhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  private validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Validate business requirements
  private validateBusinessRequirements(requirements: Record<string, any>): string[] {
    const warnings: string[] = [];

    if (!requirements || Object.keys(requirements).length === 0) {
      warnings.push('No specific business requirements provided');
      return warnings;
    }

    // Check for common business requirement fields
    const commonFields = ['budget', 'timeline', 'scope', 'deliverables', 'success_criteria'];
    const missingFields = commonFields.filter(field => !requirements[field]);

    if (missingFields.length > 0) {
      warnings.push(`Missing common business requirements: ${missingFields.join(', ')}`);
    }

    return warnings;
  }

  // Validate AI prompts
  private validateAIPrompts(ai_prompts: any): string[] {
    const warnings: string[] = [];

    if (!ai_prompts) {
      warnings.push('No AI prompt customization provided - using defaults');
      return warnings;
    }

    if (ai_prompts.style_guidance && ai_prompts.style_guidance.length > 500) {
      warnings.push('Style guidance is very long - may affect AI performance');
    }

    if (ai_prompts.additional_instructions && ai_prompts.additional_instructions.length > 1000) {
      warnings.push('Additional instructions are very long - may affect AI performance');
    }

    return warnings;
  }
}
