import React from "react";
import { RouteObject } from "react-router-dom";
import { PluginModule, NavigationItem } from "../../plugins/types";

// Import the layout with sidebar toggle
import Layout from "./src/components/layout";

// Import pages from the src/pages directory
import Dashboard from "./src/pages/Dashboard";
import Documents from "./src/pages/Documents";
import DocumentsGenerated from "./src/pages/DocumentsGenerated";
import DocumentsLibrary from "./src/pages/DocumentsLibrary";
import DocumentsNew from "./src/pages/DocumentsNew";
import KnowledgeBase from "./src/pages/KnowledgeBase";
import KnowledgeBaseClients from "./src/pages/KnowledgeBaseClients";
import KnowledgeBaseDocuments from "./src/pages/KnowledgeBaseDocuments";
import KnowledgeBasePrompts from "./src/pages/KnowledgeBasePrompts";
import Scoping from "./src/pages/Scoping";
import ScopingProposals from "./src/pages/ScopingProposals";

// Helper function to wrap pages with the layout
const withLayout = (Component: React.ComponentType) => {
  return React.createElement(Layout, {}, React.createElement(Component));
};

// Define routes for the ScopingAI plugin with layout
const routes: RouteObject[] = [
  {
    path: "/scopingai",
    element: withLayout(Dashboard),
  },
  {
    path: "/scopingai/dashboard",
    element: withLayout(Dashboard),
  },
  {
    path: "/scopingai/documents",
    element: withLayout(Documents),
  },
  {
    path: "/scopingai/documents/new",
    element: withLayout(DocumentsNew),
  },
  {
    path: "/scopingai/documents/generated",
    element: withLayout(DocumentsGenerated),
  },
  {
    path: "/scopingai/documents/library",
    element: withLayout(DocumentsLibrary),
  },
  {
    path: "/scopingai/knowledge-base",
    element: withLayout(KnowledgeBase),
  },
  {
    path: "/scopingai/knowledge-base/clients",
    element: withLayout(KnowledgeBaseClients),
  },
  {
    path: "/scopingai/knowledge-base/documents",
    element: withLayout(DocumentsLibrary),
  },
  {
    path: "/scopingai/knowledge-base/prompts",
    element: withLayout(KnowledgeBasePrompts),
  },
  {
    path: "/scopingai/scoping",
    element: withLayout(Scoping),
  },
  {
    path: "/scopingai/proposals",
    element: withLayout(ScopingProposals),
  },
];

// Define navigation items - simple flat structure like pseo and crm
const navigation: NavigationItem[] = [
  {
    name: "ScopingAI",
    route: "/scopingai",
    icon: "FileText",
    order: 7,
    permissions: ["scopingai:read"],
  },
];

// ScopingAI plugin module
const scopingAiPlugin: PluginModule = {
  routes,
  navigation,
  providers: [], // No specific providers for ScopingAI yet
  config: {
    name: "ScopingAI",
    version: "1.0.0",
    description:
      "AI-powered project scoping and proposal generation system with document management and knowledge base",
  },
  initialize: async () => {
    console.log("🎯 ScopingAI plugin initialized");
  },
  cleanup: async () => {
    console.log("🎯 ScopingAI plugin cleaned up");
  },
};

export default scopingAiPlugin;
