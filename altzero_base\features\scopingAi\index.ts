import React from "react";
import { RouteObject } from "react-router-dom";
import { PluginModule, NavigationItem } from "../../plugins/types";

// Import pages from the src/pages directory
import Dashboard from "./src/pages/Dashboard";
import Documents from "./src/pages/Documents";
import DocumentsGenerated from "./src/pages/DocumentsGenerated";
import DocumentsLibrary from "./src/pages/DocumentsLibrary";
import DocumentsNew from "./src/pages/DocumentsNew";
import KnowledgeBase from "./src/pages/KnowledgeBase";
import KnowledgeBaseClients from "./src/pages/KnowledgeBaseClients";
import KnowledgeBaseDocuments from "./src/pages/KnowledgeBaseDocuments";
import KnowledgeBasePrompts from "./src/pages/KnowledgeBasePrompts";
import Scoping from "./src/pages/Scoping";
import ScopingProposals from "./src/pages/ScopingProposals";

// Define routes for the ScopingAI plugin
const routes: RouteObject[] = [
  {
    path: "/scopingai",
    element: React.createElement(Dashboard),
  },
  {
    path: "/scopingai/dashboard",
    element: React.createElement(Dashboard),
  },
  {
    path: "/scopingai/documents",
    element: React.createElement(Documents),
  },
  {
    path: "/scopingai/documents/new",
    element: React.createElement(DocumentsNew),
  },
  {
    path: "/scopingai/documents/generated",
    element: React.createElement(DocumentsGenerated),
  },
  {
    path: "/scopingai/documents/library",
    element: React.createElement(DocumentsLibrary),
  },
  {
    path: "/scopingai/knowledge-base",
    element: React.createElement(KnowledgeBase),
  },
  {
    path: "/scopingai/knowledge-base/clients",
    element: React.createElement(KnowledgeBaseClients),
  },
  {
    path: "/scopingai/knowledge-base/documents",
    element: React.createElement(DocumentsLibrary),
  },
  {
    path: "/scopingai/knowledge-base/prompts",
    element: React.createElement(KnowledgeBasePrompts),
  },
  {
    path: "/scopingai/scoping",
    element: React.createElement(Scoping),
  },
  {
    path: "/scopingai/proposals",
    element: React.createElement(ScopingProposals),
  },
];

// Define navigation items - simple flat structure like pseo and crm
const navigation: NavigationItem[] = [
  {
    name: "ScopingAI",
    route: "/scopingai",
    icon: "FileText",
    order: 7,
    permissions: ["scopingai:read"],
  },
];

// ScopingAI plugin module
const scopingAiPlugin: PluginModule = {
  routes,
  navigation,
  providers: [], // No specific providers for ScopingAI yet
  config: {
    name: "ScopingAI",
    version: "1.0.0",
    description:
      "AI-powered project scoping and proposal generation system with document management and knowledge base",
  },
  initialize: async () => {
    console.log("🎯 ScopingAI plugin initialized");
  },
  cleanup: async () => {
    console.log("🎯 ScopingAI plugin cleaned up");
  },
};

export default scopingAiPlugin;
