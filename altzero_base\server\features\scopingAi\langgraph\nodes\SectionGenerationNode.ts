// =====================================================
// SECTION GENERATION NODE - SCOPINGAI LANGGRAPH
// =====================================================

import { BaseNode, SectionGenerationNodeResult } from '../types/NodeTypes';
import { ScopingAiWorkflowState, WorkflowContext } from '../types/WorkflowState';

export class SectionGenerationNode implements BaseNode {
  name = 'section_generation';
  description = 'Generates all proposal sections based on template and research analysis';

  async execute(context: WorkflowContext): Promise<Partial<ScopingAiWorkflowState>> {
    const { state, tools, logger } = context;
    
    logger.info('Starting section generation', {
      workflow_id: state.workflow_id,
      sections_to_generate: state.template?.sections?.length || 0,
      has_research: !!(state.research_analysis?.content)
    });

    try {
      const startTime = Date.now();
      
      // Generate all proposal sections
      const sectionResult = await this.generateAllSections(state, tools, logger);
      
      const processingTime = Date.now() - startTime;

      logger.info('Section generation completed', {
        sections_generated: sectionResult.sections.length,
        total_words: sectionResult.total_word_count,
        avg_quality: sectionResult.average_quality_score
      });

      return {
        proposal_sections: sectionResult.sections,
        current_step: 'section_generation_completed',
        progress: 85,
        processing_time: (state.processing_time || 0) + processingTime,
        api_calls_made: [
          ...(state.api_calls_made || []),
          {
            provider: 'ai_generation',
            endpoint: 'section_generation',
            calls_made: sectionResult.sections.length,
            success_rate: 1.0,
            average_response_time: processingTime / sectionResult.sections.length,
            cost_estimate: sectionResult.generation_metrics.total_cost,
            timestamp: new Date().toISOString()
          }
        ],
        total_cost: (state.total_cost || 0) + sectionResult.generation_metrics.total_cost,
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          section_generation: sectionResult
        }
      };

    } catch (error) {
      logger.error('Section generation failed', error);
      throw error;
    }
  }

  private async generateAllSections(
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<SectionGenerationNodeResult> {
    
    const sections = state.template?.sections || ['Introduction', 'Scope', 'Timeline', 'Budget'];
    const generatedSections: any[] = [];
    let totalTokensUsed = 0;
    let totalCost = 0;
    const generationTimes: number[] = [];

    // Generate each section
    for (let i = 0; i < sections.length; i++) {
      const sectionTitle = sections[i];
      
      try {
        logger.info(`Generating section: ${sectionTitle} (${i + 1}/${sections.length})`);
        
        const sectionStartTime = Date.now();
        const sectionData = await this.generateSection(sectionTitle, state, tools, logger);
        const sectionTime = Date.now() - sectionStartTime;
        
        generatedSections.push({
          title: sectionTitle,
          content: sectionData.content,
          word_count: sectionData.word_count,
          quality_score: sectionData.quality_score,
          generation_time_ms: sectionTime
        });

        totalTokensUsed += sectionData.tokens_used || 0;
        totalCost += sectionData.cost_estimate || 0;
        generationTimes.push(sectionTime);

        logger.debug(`Section completed: ${sectionTitle}`, {
          word_count: sectionData.word_count,
          quality_score: sectionData.quality_score,
          generation_time: sectionTime
        });

      } catch (error) {
        logger.error(`Failed to generate section: ${sectionTitle}`, error);
        
        // Add fallback section
        generatedSections.push({
          title: sectionTitle,
          content: this.getFallbackSectionContent(sectionTitle, state),
          word_count: 200,
          quality_score: 60,
          generation_time_ms: 0
        });
      }
    }

    const totalWordCount = generatedSections.reduce((sum, section) => sum + section.word_count, 0);
    const averageQualityScore = generatedSections.reduce((sum, section) => sum + section.quality_score, 0) / generatedSections.length;

    return {
      sections: generatedSections,
      total_word_count: totalWordCount,
      average_quality_score: Math.round(averageQualityScore),
      generation_metrics: {
        total_tokens_used: totalTokensUsed,
        total_cost: totalCost,
        average_generation_time: generationTimes.reduce((sum, time) => sum + time, 0) / generationTimes.length
      }
    };
  }

  private async generateSection(
    sectionTitle: string,
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<any> {
    
    // Create section-specific prompt
    const sectionPrompt = this.createSectionPrompt(sectionTitle, state);
    
    // Generate section content
    const content = await tools.ai.generateText(sectionPrompt, {
      model: 'gpt-4o-mini',
      temperature: 0.7,
      max_tokens: this.getSectionMaxTokens(sectionTitle)
    });

    // Calculate metrics
    const wordCount = content.split(/\s+/).length;
    const qualityScore = await this.assessSectionQuality(sectionTitle, content, state, tools);

    return {
      content: content.trim(),
      word_count: wordCount,
      quality_score: qualityScore,
      tokens_used: Math.ceil(wordCount * 1.3), // Rough estimate
      cost_estimate: Math.ceil(wordCount * 1.3) * 0.00002 // Rough cost estimate
    };
  }

  private createSectionPrompt(sectionTitle: string, state: ScopingAiWorkflowState): string {
    const baseContext = this.getBaseContext(state);
    const sectionRequirements = this.getSectionRequirements(sectionTitle);
    const researchContext = this.getResearchContext(state);

    return `${baseContext}

${researchContext}

SECTION TO GENERATE: ${sectionTitle}

${sectionRequirements}

WRITING GUIDELINES:
- Professional business tone appropriate for client proposals
- Clear, concise, and actionable content
- Use specific details and avoid generic statements
- Include relevant data and insights from research analysis
- Structure content with clear headings and bullet points where appropriate
- Target length: ${this.getSectionTargetLength(sectionTitle)} words
- Focus on client value and business outcomes

STYLE GUIDANCE: ${state.ai_prompts?.style_guidance || 'Professional, confident, and results-focused'}

CONTENT FOCUS: ${state.ai_prompts?.content_focus || 'Business value and practical implementation'}

Generate the ${sectionTitle} section content now:`;
  }

  private getBaseContext(state: ScopingAiWorkflowState): string {
    return `CLIENT & PROJECT CONTEXT:
- Client: ${state.client?.name || 'Unknown'}
- Industry: ${state.client?.industry || 'Unknown'}
- Project: ${state.project?.title || 'Unknown'}
- Description: ${state.project?.description || 'No description provided'}
- Timeline: ${state.project?.timeline || 'To be determined'}
- Budget: ${state.project?.budget_range || 'To be discussed'}

EXECUTIVE SUMMARY INSIGHTS:
${state.executive_summary?.content || 'Executive summary not available'}

VALUE PROPOSITION:
${state.executive_summary?.value_proposition || 'Value proposition not defined'}`;
  }

  private getResearchContext(state: ScopingAiWorkflowState): string {
    if (!state.research_analysis) {
      return 'RESEARCH CONTEXT: Limited research data available';
    }

    return `RESEARCH ANALYSIS:
${state.research_analysis.content}

KEY FINDINGS:
${state.research_analysis.key_findings?.map(f => `- ${f}`).join('\n') || 'None available'}

STRATEGIC RECOMMENDATIONS:
${state.research_analysis.recommendations?.map(r => `- ${r}`).join('\n') || 'None available'}

KNOWLEDGE BASE INSIGHTS:
${state.knowledge_base_content?.content_summary || 'No knowledge base content available'}`;
  }

  private getSectionRequirements(sectionTitle: string): string {
    const requirements: Record<string, string> = {
      'Introduction': `INTRODUCTION SECTION REQUIREMENTS:
- Establish context and project background
- Clearly state the business challenge or opportunity
- Introduce our organization and relevant experience
- Set expectations for the proposal content
- Create engagement and interest in our solution`,

      'Scope': `SCOPE SECTION REQUIREMENTS:
- Define project boundaries and deliverables
- Specify what is included and excluded
- Detail work breakdown structure
- Identify key milestones and phases
- Address assumptions and dependencies`,

      'Timeline': `TIMELINE SECTION REQUIREMENTS:
- Provide realistic project schedule
- Break down phases and key milestones
- Identify critical path activities
- Address resource allocation over time
- Include buffer time for risk mitigation`,

      'Budget': `BUDGET SECTION REQUIREMENTS:
- Present cost structure and pricing model
- Break down costs by category or phase
- Justify pricing with value proposition
- Address payment terms and schedule
- Include assumptions and potential variables`,

      'Methodology': `METHODOLOGY SECTION REQUIREMENTS:
- Describe our proven approach and framework
- Explain methodologies and best practices
- Detail quality assurance processes
- Address risk management strategies
- Highlight differentiating factors`,

      'Team': `TEAM SECTION REQUIREMENTS:
- Introduce key team members and roles
- Highlight relevant experience and expertise
- Demonstrate team's fit for this project
- Address team availability and commitment
- Include organizational support structure`,

      'Benefits': `BENEFITS SECTION REQUIREMENTS:
- Quantify expected business outcomes
- Detail tangible and intangible benefits
- Provide ROI analysis where possible
- Address risk mitigation benefits
- Connect benefits to client's strategic goals`
    };

    return requirements[sectionTitle] || `${sectionTitle.toUpperCase()} SECTION REQUIREMENTS:
- Provide comprehensive coverage of ${sectionTitle.toLowerCase()}
- Include specific details relevant to the client's needs
- Support content with research insights and best practices
- Focus on client value and business outcomes
- Use professional business language and structure`;
  }

  private getSectionTargetLength(sectionTitle: string): number {
    const lengths: Record<string, number> = {
      'Introduction': 400,
      'Executive Summary': 500,
      'Scope': 600,
      'Timeline': 400,
      'Budget': 500,
      'Methodology': 700,
      'Team': 500,
      'Benefits': 600,
      'Conclusion': 300
    };

    return lengths[sectionTitle] || 500;
  }

  private getSectionMaxTokens(sectionTitle: string): number {
    return Math.ceil(this.getSectionTargetLength(sectionTitle) * 1.5);
  }

  private async assessSectionQuality(
    sectionTitle: string,
    content: string,
    state: ScopingAiWorkflowState,
    tools: any
  ): Promise<number> {
    try {
      const prompt = `Assess the quality of this ${sectionTitle} section for a business proposal on a scale of 0-100.

Section Content:
${content}

Evaluation Criteria:
- Relevance to section purpose
- Clarity and readability
- Professional tone and language
- Specific details vs generic content
- Business value focus
- Logical structure and flow

Provide only a numeric score (0-100).`;

      const response = await tools.ai.generateText(prompt, {
        temperature: 0.1,
        max_tokens: 50
      });

      const score = parseInt(response.match(/\d+/)?.[0] || '75');
      return Math.max(0, Math.min(100, score));
    } catch (error) {
      // Fallback quality assessment
      return this.calculateFallbackQuality(content);
    }
  }

  private calculateFallbackQuality(content: string): number {
    let score = 70; // Base score

    const wordCount = content.split(/\s+/).length;
    if (wordCount < 200) score -= 20;
    if (wordCount > 800) score -= 10;

    // Check for specific business terms
    const businessTerms = ['benefit', 'value', 'solution', 'approach', 'deliver', 'outcome'];
    const foundTerms = businessTerms.filter(term => 
      content.toLowerCase().includes(term)
    ).length;
    score += foundTerms * 3;

    // Check for structure
    if (content.includes('•') || content.includes('-')) score += 5;
    if (content.match(/\d+\./)) score += 5;

    return Math.max(50, Math.min(95, score));
  }

  private getFallbackSectionContent(sectionTitle: string, state: ScopingAiWorkflowState): string {
    const client = state.client?.name || 'your organization';
    const project = state.project?.title || 'this project';

    const fallbackContent: Record<string, string> = {
      'Introduction': `We are pleased to present this proposal for ${project} with ${client}. Our team brings extensive experience in delivering successful solutions that drive measurable business results. This proposal outlines our understanding of your requirements and presents a comprehensive approach to achieving your objectives.`,

      'Scope': `The scope of ${project} includes comprehensive analysis, solution design, implementation, and ongoing support. We will work closely with your team to ensure all deliverables meet your specific requirements and business objectives. Our approach includes detailed planning, risk mitigation, and quality assurance throughout the project lifecycle.`,

      'Timeline': `We propose a phased approach to ${project} with clear milestones and deliverables. The project timeline will be developed in collaboration with your team to ensure alignment with your business priorities and resource availability. We will provide regular progress updates and maintain flexibility to accommodate changing requirements.`,

      'Budget': `Our pricing for ${project} is structured to provide maximum value while maintaining cost effectiveness. We offer flexible payment terms and transparent pricing with no hidden costs. The investment in this project will deliver significant returns through improved efficiency, reduced costs, and enhanced business capabilities.`
    };

    return fallbackContent[sectionTitle] || `This section will provide comprehensive information about ${sectionTitle.toLowerCase()} for ${project}. Our team will work with ${client} to ensure all aspects of ${sectionTitle.toLowerCase()} are thoroughly addressed and aligned with your business objectives.`;
  }
}
