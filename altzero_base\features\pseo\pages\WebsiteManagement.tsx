import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/pseo/databaseService';
import { useUser } from '../../../base/contextapi/UserContext';
import type { PSEOClient, PSEOWebsite } from '../types';
import PSEOLayout from '../components/PSEOLayout';

const WebsiteManagement: React.FC = () => {
  const { user } = useUser();
  const [clients, setClients] = useState<PSEOClient[]>([]);
  const [selectedClientId, setSelectedClientId] = useState('');
  const [websites, setWebsites] = useState<PSEOWebsite[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<PSEOWebsite | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    url: '',
    name: ''
  });

  // Client management state
  const [showClientForm, setShowClientForm] = useState(false);
  const [editingClient, setEditingClient] = useState<PSEOClient | null>(null);
  const [clientFormData, setClientFormData] = useState({
    name: '',
    email: '',
    company: ''
  });

  useEffect(() => {
    if (user?.id) {
      loadClients();
    }
  }, [user?.id]);

  useEffect(() => {
    if (selectedClientId) {
      loadWebsites();
    } else {
      setWebsites([]);
    }
  }, [selectedClientId]);

  const loadClients = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const clientsData = await databaseService.getClientsByUserId(user.id);
      setClients(clientsData);
      
      // Auto-select first client if available
      if (clientsData.length > 0 && !selectedClientId) {
        setSelectedClientId(clientsData[0].id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const loadWebsites = async () => {
    if (!selectedClientId) return;

    setLoading(true);
    try {
      const websitesData = await databaseService.getWebsitesByClientId(selectedClientId);
      setWebsites(websitesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load websites');
    } finally {
      setLoading(false);
    }
  };

  // Client management functions
  const handleClientSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.id) return;

    if (!clientFormData.name || !clientFormData.email) {
      setError('Name and email are required');
      return;
    }

    setLoading(true);
    try {
      if (editingClient) {
        // Update existing client
        await databaseService.updateClient(editingClient.id, {
          name: clientFormData.name,
          email: clientFormData.email,
          company: clientFormData.company || undefined,
        });
      } else {
        // Create new client
        const newClient = await databaseService.createClient({
          user_id: user.id,
          name: clientFormData.name,
          email: clientFormData.email,
          company: clientFormData.company || undefined,
        });
        // Auto-select the newly created client
        setSelectedClientId(newClient.id);
      }

      await loadClients();
      resetClientForm();
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save client');
    } finally {
      setLoading(false);
    }
  };

  const handleClientEdit = (client: PSEOClient) => {
    setEditingClient(client);
    setClientFormData({
      name: client.name,
      email: client.email || '',
      company: client.company || ''
    });
    setShowClientForm(true);
  };

  const handleClientDelete = async (clientId: string) => {
    if (!confirm('Are you sure you want to delete this client? This will also delete all associated websites and audits.')) {
      return;
    }

    setLoading(true);
    try {
      await databaseService.deleteClient(clientId);
      if (selectedClientId === clientId) {
        setSelectedClientId('');
      }
      await loadClients();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete client');
    } finally {
      setLoading(false);
    }
  };

  const resetClientForm = () => {
    setClientFormData({ name: '', email: '', company: '' });
    setEditingClient(null);
    setShowClientForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedClientId) {
      setError('Please select a client first');
      return;
    }

    if (!formData.url || !formData.name) {
      setError('URL and name are required');
      return;
    }

    setLoading(true);
    try {
      if (editingWebsite) {
        // Update existing website
        await databaseService.updateWebsite(editingWebsite.id, {
          url: formData.url,
          name: formData.name,
        });
      } else {
        // Create new website
        await databaseService.createWebsite({
          client_id: selectedClientId,
          url: formData.url,
          name: formData.name,
        });
      }

      await loadWebsites();
      resetForm();
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save website');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (website: PSEOWebsite) => {
    setEditingWebsite(website);
    setFormData({
      url: website.url,
      name: website.name
    });
    setShowCreateForm(true);
  };

  const handleDelete = async (websiteId: string) => {
    if (!confirm('Are you sure you want to delete this website? This will also delete all associated audits.')) {
      return;
    }

    setLoading(true);
    try {
      await databaseService.deleteWebsite(websiteId);
      await loadWebsites();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete website');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ url: '', name: '' });
    setEditingWebsite(null);
    setShowCreateForm(false);
  };

  const selectedClient = clients.find(c => c.id === selectedClientId);

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Client & Website Management
          </h1>
          <p className="text-muted-foreground">
            Manage your clients and their websites in one place
          </p>
        </div>

        {/* Client Management Section */}
        <div className="mb-8 bg-card rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Your Clients</h2>
            <button
              onClick={() => setShowClientForm(true)}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
            >
              Add New Client
            </button>
          </div>

          {clients.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No clients found. Create your first client to get started.
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {clients.map((client) => (
                <div key={client.id} className="border border-border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-foreground">{client.name}</h3>
                    <div className="flex gap-1">
                      <button
                        onClick={() => handleClientEdit(client)}
                        className="text-xs px-2 py-1 border border-border rounded hover:bg-accent"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleClientDelete(client.id)}
                        className="text-xs px-2 py-1 border border-destructive text-destructive rounded hover:bg-destructive/10"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">{client.email}</p>
                  {client.company && (
                    <p className="text-sm text-muted-foreground">{client.company}</p>
                  )}
                  <button
                    onClick={() => setSelectedClientId(client.id)}
                    className={`mt-2 text-xs px-3 py-1 rounded ${
                      selectedClientId === client.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-secondary text-secondary-foreground hover:bg-secondary/90'
                    }`}
                  >
                    {selectedClientId === client.id ? 'Selected' : 'Select'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Client Selection */}
        {/* <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            Select Client
          </label>
          <select
            value={selectedClientId}
            onChange={(e) => setSelectedClientId(e.target.value)}
            className="w-full max-w-md px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
          >
            <option value="">Choose a client...</option>
            {clients.map((client) => (
              <option key={client.id} value={client.id}>
                {client.name} {client.company ? `(${client.company})` : ''}
              </option>
            ))}
          </select>
        </div> */}

        {selectedClient && (
          <>
            {/* Selected Client Info */}
            <div className="mb-6 p-4 bg-accent rounded-lg">
              <h3 className="font-medium">Selected Client: {selectedClient.name}</h3>
              <p className="text-sm text-muted-foreground">{selectedClient.email}</p>
              {selectedClient.company && (
                <p className="text-sm text-muted-foreground">{selectedClient.company}</p>
              )}
            </div>

            {/* Actions */}
            <div className="mb-6">
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
              >
                Add New Website
              </button>
            </div>
          </>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        {/* Client Form Modal */}
        {showClientForm && (
          <div className="mb-6">
            <div className="bg-card rounded-lg border p-6 max-w-md">
              <h2 className="text-xl font-semibold mb-4">
                {editingClient ? 'Edit Client' : 'Create New Client'}
              </h2>
              
              <form onSubmit={handleClientSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Client Name *
                  </label>
                  <input
                    type="text"
                    value={clientFormData.name}
                    onChange={(e) => setClientFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter client name"
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    value={clientFormData.email}
                    onChange={(e) => setClientFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Enter email address"
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Company
                  </label>
                  <input
                    type="text"
                    value={clientFormData.company}
                    onChange={(e) => setClientFormData(prev => ({ ...prev, company: e.target.value }))}
                    placeholder="Enter company name"
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>

                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={resetClientForm}
                    className="flex-1 px-4 py-2 border border-border rounded-md hover:bg-accent"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : editingClient ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {selectedClientId && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Website Form */}
            {showCreateForm && (
              <div className="lg:col-span-1">
                <div className="bg-card rounded-lg border p-6">
                  <h2 className="text-xl font-semibold mb-4">
                    {editingWebsite ? 'Edit Website' : 'Add New Website'}
                  </h2>
                  
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Website URL *
                      </label>
                      <input
                        type="url"
                        value={formData.url}
                        onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                        placeholder="https://example.com"
                        className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Website Name *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter website name"
                        className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                        required
                      />
                    </div>

                    <div className="flex gap-3">
                      <button
                        type="button"
                        onClick={resetForm}
                        className="flex-1 px-4 py-2 border border-border rounded-md hover:bg-accent"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={loading}
                        className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50"
                      >
                        {loading ? 'Saving...' : editingWebsite ? 'Update' : 'Add'}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Websites List */}
            <div className={showCreateForm ? 'lg:col-span-2' : 'lg:col-span-3'}>
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold mb-4">
                  Websites for {selectedClient?.name}
                </h2>
                
                {loading && !showCreateForm ? (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground">Loading websites...</div>
                  </div>
                ) : websites.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground">
                      No websites found for this client. Add the first website to get started.
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {websites.map((website) => (
                      <div key={website.id} className="border border-border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-medium text-foreground">{website.name}</h3>
                            <p className="text-sm text-muted-foreground">{website.url}</p>
                            <p className="text-sm text-muted-foreground">Domain: {website.domain}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <span className={`text-xs px-2 py-1 rounded ${
                                website.status === 'active' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {website.status}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                Created: {new Date(website.created_at).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => handleEdit(website)}
                              className="text-sm px-3 py-1 border border-border rounded hover:bg-accent"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDelete(website.id)}
                              className="text-sm px-3 py-1 border border-destructive text-destructive rounded hover:bg-destructive/10"
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {!selectedClientId && clients.length === 0 && (
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              No clients found. Please create a client first before adding websites.
            </div>
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default WebsiteManagement; 