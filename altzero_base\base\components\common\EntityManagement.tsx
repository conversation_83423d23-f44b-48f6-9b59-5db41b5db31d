import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import { Card } from "../ui/card";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { InvitationsList } from "../invitation/InvitationsList";
import { CreateOrganizationDialog } from "../organization/CreateOrganizationDialog";
import { CreateTeamDialog } from "../team/CreateTeamDialog";
import { OrganizationCard } from "../organization/OrganizationCard";
import { TeamCard } from "../team/TeamCard";
import {
  getUserOrganizations,
  getUserPendingOrganizationInvitations,
} from "../../services/organizationService";
import {
  getUserGroups,
  getUserPendingGroupInvitations,
} from "../../services/teamService";
import { Organization, OrganizationInvitation } from "../../types/organization";
import { Group, GroupInvitation } from "../../types/team";
import { MemberRole, EntityType, ENTITY_LABELS } from "../../utils/constants";
import { toast } from "../../hooks/use-toast";

interface EntityManagementProps {
  activeEntity: EntityType;
  onChangeEntity: (entity: EntityType) => void;
}

export const EntityManagement: React.FC<EntityManagementProps> = ({
  activeEntity,
  onChangeEntity,
}) => {
  const navigate = useNavigate();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [teams, setTeams] = useState<Group[]>([]);
  const [orgMemberRoles, setOrgMemberRoles] = useState<
    Record<string, MemberRole>
  >({});
  const [teamMemberRoles, setTeamMemberRoles] = useState<
    Record<string, MemberRole>
  >({});
  const [orgInvitations, setOrgInvitations] = useState<
    OrganizationInvitation[]
  >([]);
  const [teamInvitations, setTeamInvitations] = useState<GroupInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("entities");

  // Settings state
  const [notificationEnabled, setNotificationEnabled] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [defaultView, setDefaultView] = useState<EntityType>(
    EntityType.ORGANIZATION
  );
  const [autoAcceptInvites, setAutoAcceptInvites] = useState(false);

  useEffect(() => {
    fetchEntitiesAndInvitations();
  }, [activeEntity]);

  const fetchEntitiesAndInvitations = async () => {
    setIsLoading(true);
    try {
      // Fetch organizations
      const { organizations, membership: orgMembership } =
        await getUserOrganizations();
      setOrganizations(organizations);

      // Map roles to organizations for easy lookup
      const orgRoleMap: Record<string, MemberRole> = {};
      orgMembership.forEach((member) => {
        orgRoleMap[member.organisation_id] = member.role as MemberRole;
      });
      setOrgMemberRoles(orgRoleMap);

      // Fetch teams
      const { groups, membership: teamMembership } = await getUserGroups();
      setTeams(groups);

      // Map roles to teams for easy lookup
      const teamRoleMap: Record<string, MemberRole> = {};
      teamMembership.forEach((member) => {
        teamRoleMap[member.group_id] = member.role as MemberRole;
      });
      setTeamMemberRoles(teamRoleMap);

      // Fetch pending invitations
      const orgInvites = await getUserPendingOrganizationInvitations();
      setOrgInvitations(orgInvites);

      const teamInvites = await getUserPendingGroupInvitations();
      setTeamInvitations(teamInvites);
    } catch (error) {
      console.error("Error fetching entities and invitations:", error);
      toast({
        title: "Error",
        description: "Failed to load data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEntityCreated = () => {
    fetchEntitiesAndInvitations();
  };

  const handleInvitationAccepted = () => {
    fetchEntitiesAndInvitations();
  };

  const handleEntityClick = (id: string) => {
    if (activeEntity === EntityType.ORGANIZATION) {
      navigate(`/organizations/${id}`);
    } else {
      navigate(`/teams/${id}`);
    }
  };

  const getEntityItems = () => {
    return activeEntity === EntityType.ORGANIZATION ? organizations : teams;
  };

  const getEntityRoles = () => {
    return activeEntity === EntityType.ORGANIZATION
      ? orgMemberRoles
      : teamMemberRoles;
  };

  const getTotalInvitationsCount = () => {
    return orgInvitations.length + teamInvitations.length;
  };

  const renderCreateButton = () => {
    if (activeEntity === EntityType.ORGANIZATION) {
      return <CreateOrganizationDialog onSuccess={handleEntityCreated} />;
    }
    return <CreateTeamDialog onSuccess={handleEntityCreated} />;
  };

  const renderEmptyState = () => {
    const entityLabel = ENTITY_LABELS[activeEntity].plural;
    const entityLabelSingular = ENTITY_LABELS[activeEntity].singular;

    return (
      <div className="text-center p-10 bg-muted/40 rounded-lg">
        <h3 className="text-lg font-medium mb-2">
          No {entityLabel.toLowerCase()} yet
        </h3>
        <p className="text-muted-foreground mb-4">
          Create a {entityLabelSingular.toLowerCase()} to collaborate with
          others
        </p>
        {activeEntity === EntityType.ORGANIZATION ? (
          <CreateOrganizationDialog
            onSuccess={handleEntityCreated}
            trigger={<Button>Create Your First {entityLabelSingular}</Button>}
          />
        ) : (
          <CreateTeamDialog
            onSuccess={handleEntityCreated}
            trigger={<Button>Create Your First {entityLabelSingular}</Button>}
          />
        )}
      </div>
    );
  };

  const renderEntityCards = () => {
    const items = getEntityItems();
    const roles = getEntityRoles();

    if (activeEntity === EntityType.ORGANIZATION) {
      return (organizations as Organization[]).map((org) => (
        <OrganizationCard
          key={org.id}
          organization={org}
          role={orgMemberRoles[org.id] || "member"}
        />
      ));
    }

    return (teams as Group[]).map((team) => (
      <TeamCard
        key={team.id}
        team={team}
        role={teamMemberRoles[team.id] || "member"}
      />
    ));
  };

  const handleSaveSettings = () => {
    toast({
      title: "Settings Saved",
      description: "Your preferences have been updated successfully.",
    });
  };

  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-6">
          <h1 className="text-3xl font-bold">
            {ENTITY_LABELS[activeEntity].plural}
          </h1>
          <div className="flex rounded-lg overflow-hidden border border-border">
            <button
              className={`px-4 py-2 ${
                activeEntity === EntityType.ORGANIZATION
                  ? "bg-primary text-primary-foreground"
                  : "bg-card hover:bg-muted/50"
              }`}
              onClick={() => onChangeEntity(EntityType.ORGANIZATION)}
            >
              Organizations
            </button>
            <button
              className={`px-4 py-2 ${
                activeEntity === EntityType.TEAM
                  ? "bg-primary text-primary-foreground"
                  : "bg-card hover:bg-muted/50"
              }`}
              onClick={() => onChangeEntity(EntityType.TEAM)}
            >
              Teams
            </button>
          </div>
        </div>
        {renderCreateButton()}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="entities">
            My {ENTITY_LABELS[activeEntity].plural}
          </TabsTrigger>
          <TabsTrigger value="invitations">
            Invitations
            {getTotalInvitationsCount() > 0 && (
              <span className="ml-2 px-2 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
                {getTotalInvitationsCount()}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="entities">
          {isLoading ? (
            <div className="flex justify-center p-10">
              <div className="animate-spin w-8 h-8 border-2 border-primary rounded-full border-t-transparent"></div>
            </div>
          ) : getEntityItems().length === 0 ? (
            renderEmptyState()
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderEntityCards()}
            </div>
          )}
        </TabsContent>

        <TabsContent value="invitations">
          <InvitationsList
            organizationInvitations={orgInvitations}
            groupInvitations={teamInvitations}
            onAcceptOrganization={handleInvitationAccepted}
            onAcceptGroup={handleInvitationAccepted}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="settings">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                Notification Preferences
              </h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="notifications" className="font-medium">
                      Enable Notifications
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications about updates and changes
                    </p>
                  </div>
                  <Switch
                    id="notifications"
                    checked={notificationEnabled}
                    onCheckedChange={setNotificationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label
                      htmlFor="email-notifications"
                      className="font-medium"
                    >
                      Email Notifications
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via email
                    </p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                    disabled={!notificationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-accept" className="font-medium">
                      Auto-accept Invitations
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically accept invitations from trusted members
                    </p>
                  </div>
                  <Switch
                    id="auto-accept"
                    checked={autoAcceptInvites}
                    onCheckedChange={setAutoAcceptInvites}
                  />
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                Display Preferences
              </h2>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="default-view" className="font-medium">
                    Default View
                  </Label>
                  <Select
                    value={defaultView}
                    onValueChange={(value) =>
                      setDefaultView(value as EntityType)
                    }
                  >
                    <SelectTrigger id="default-view">
                      <SelectValue placeholder="Select default view" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={EntityType.ORGANIZATION}>
                        Organizations
                      </SelectItem>
                      <SelectItem value={EntityType.TEAM}>Teams</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Choose which view to show by default
                  </p>
                </div>
              </div>
            </Card>

            <div className="lg:col-span-2 flex justify-end">
              <Button onClick={handleSaveSettings}>Save Settings</Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
