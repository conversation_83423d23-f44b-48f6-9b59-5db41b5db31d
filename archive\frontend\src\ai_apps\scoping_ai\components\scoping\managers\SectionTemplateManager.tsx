import React, { useState, useEffect, useRef } from "react";
import Layout from "../../../../../components/Layout";
import { SectionTemplate } from "../../../../../types/scoping";
import { toast } from "react-hot-toast";
import { supabase } from "../../../../../utils/supabaseClient";

// Types
interface Section {
  id?: string;
  title: string;
  content?: string;
  description?: string;
  order: number;
  elements?: Array<{
    type: string;
    content: string;
    items?: string[];
  }>;
  imageData?: Array<{
    data: string;
    width?: number;
    height?: number;
    position?: {
      x: number;
      y: number;
      page?: number;
    };
  }>;
  data?: unknown[];
}

interface ElementStyle {
  fontSize?: number;
  fontFamily?: string;
  isBold?: boolean;
  isItalic?: boolean;
  alignment?: "left" | "center" | "right";
  color?: string;
  lineHeight?: number;
  marginTop?: number;
  marginBottom?: number;
  marginLeft?: number;
  marginRight?: number;
}

interface DocumentElement {
  type: string;
  content: string;
  level?: number;
  items?: string[];
  isNumbered?: boolean;
  order?: number;
  style?: ElementStyle;
  position?: {
    x: number;
    y: number;
    page: number;
    width?: number;
    height?: number;
  };
  imageData?: {
    data: string;
    width: number;
    height: number;
    position: {
      x: number;
      y: number;
      page: number;
    };
    alt?: string;
  };
}

interface ImagePosition {
  x: number;
  y: number;
  width: number;
  height: number;
  page: number;
}

interface ProcessedSection {
  title: string;
  content: string;
  description?: string;
  images?: string[];
  imageData?: Array<{
    data: string;
    width?: number;
    height?: number;
    position?: ImagePosition;
    alt?: string;
  }>;
  data?: Array<Array<string | number | boolean | null>>;
  order?: number;
  elements?: DocumentElement[];
  pageStart?: number;
  pageEnd?: number;
}

interface ProcessedDocument {
  title: string;
  sections: ProcessedSection[];
  metadata?: {
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    pageCount?: number;
    fileType?: string;
    sheetCount?: number;
  };
  fullContent?: string;
  structured_content?: {
    pages: PageContent[];
  };
  pages?: {
    pageNumber: number;
    elements: DocumentElement[];
  }[];
}

interface ExtendedSectionTemplate extends Omit<SectionTemplate, "sections"> {
  metadata?: {
    pageCount?: number;
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    fileType?: string;
  };
  sections?: Section[];
  createdAt: Date;
  updatedAt: Date;
}

interface DatabaseTemplate {
  id?: string;
  name: string;
  description?: string;
  sections?: Section[];
  metadata?: {
    pageCount?: number;
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    fileType?: string;
  };
  created_at: string;
  updated_at: string;
  user_id: string;
}

// Add new interface for API response
interface DocumentProcessingResponse {
  success: boolean;
  data: {
    file_size: number;
    file_type: string;
    image_count: number;
    images: Array<{
      data: string;
      width?: number;
      height?: number;
      position: {
        x: number;
        y: number;
        width: number;
        height: number;
        page: number;
      };
    }>;
    page_count: number;
    text: string;
    structured_content: {
      pages: PageContent[];
    };
    title: string;
  };
}

// Add new interfaces for structured content
interface StructuredContent {
  type: string;
  content: string;
  position: {
    x: number;
    y: number;
    page: number;
  };
  font?: string;
  fontSize?: number;
  color?: string;
}

interface PageContent {
  page_number: number;
  content: StructuredContent[];
}

// Update the image interface to include format
interface ImageData {
  data: string;
  width?: number;
  height?: number;
  position: ImagePosition;
  format?: string;
  alt?: string;
}

// Helper function to convert database template to ExtendedSectionTemplate format
const convertDatabaseTemplate = (
  dbTemplate: DatabaseTemplate
): ExtendedSectionTemplate => {
  console.log("Converting template:", dbTemplate);
  return {
    id: dbTemplate.id || `temp-${Date.now()}`,
    name: dbTemplate.name,
    description: dbTemplate.description,
    sections: dbTemplate.sections || [],
    metadata: dbTemplate.metadata,
    createdAt: new Date(dbTemplate.created_at),
    updatedAt: new Date(dbTemplate.updated_at),
  };
};

interface TemplateCardProps {
  template: ExtendedSectionTemplate;
  onEdit: (template: ExtendedSectionTemplate) => void;
  onDelete: (template: ExtendedSectionTemplate) => void;
}

// Template Card Component
const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onEdit,
  onDelete,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200">
      <div className="p-6">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
              {template.name}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2 mb-4">
              {template.description || "No description provided"}
            </p>
          </div>
          <div className="flex space-x-2 ml-4">
            <button
              onClick={() => onEdit(template)}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              title="Edit template"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            </button>
            <button
              onClick={() => onDelete(template)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete template"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Template Metadata */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center text-sm text-gray-500">
              <svg
                className="w-4 h-4 mr-1.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              {template.sections?.length || 0} sections
            </div>
            {template.metadata?.pageCount && (
              <div className="flex items-center text-sm text-gray-500">
                <svg
                  className="w-4 h-4 mr-1.5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
                {template.metadata.pageCount} pages
              </div>
            )}
          </div>
        </div>

        {/* Sections Preview */}
        {template.sections && template.sections.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium text-gray-700">
                Content Preview
              </h4>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                {isExpanded ? "Show less" : "Show more"}
              </button>
            </div>
            <div
              className={`space-y-4 ${
                isExpanded ? "" : "max-h-32 overflow-hidden"
              }`}
            >
              {template.sections.map((section, index) => (
                <div key={section.id || index} className="space-y-2">
                  <h5 className="text-sm font-medium text-gray-600">
                    {section.title}
                  </h5>
                  {section.content && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {section.content}
                    </p>
                  )}
                  {section.imageData && section.imageData.length > 0 && (
                    <div className="flex gap-2 overflow-x-auto py-2">
                      {section.imageData.map((img, imgIndex) => (
                        <div key={imgIndex} className="relative flex-shrink-0">
                          <img
                            src={`data:image/jpeg;base64,${img.data}`}
                            alt={`Image ${imgIndex + 1}`}
                            className="h-20 w-auto rounded border border-gray-200"
                          />
                          {img.position?.page && (
                            <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                              P{img.position.page}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Document Info */}
        <div className="mt-4 pt-4 border-t border-gray-100 grid grid-cols-2 gap-2 text-xs text-gray-500">
          <div>
            Created: {new Date(template.createdAt).toLocaleDateString()}
          </div>
          {template.metadata?.author && (
            <div>Author: {template.metadata.author}</div>
          )}
        </div>
      </div>
    </div>
  );
};

interface FileUploadAreaProps {
  onFileSelect: (file: File) => Promise<void>;
  isUploading: boolean;
  uploadProgress: number;
  onClose: () => void;
}

// File Upload Component
const FileUploadArea: React.FC<FileUploadAreaProps> = ({
  onFileSelect,
  isUploading,
  uploadProgress,
  onClose,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) onFileSelect(file);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Import Document</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <svg
            className="w-6 h-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      {isUploading ? (
        <div className="space-y-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <p className="text-center text-gray-600">
            Processing document... {uploadProgress}%
          </p>
        </div>
      ) : (
        <div
          className="border-2 border-dashed border-gray-300 rounded-xl p-10 text-center cursor-pointer hover:border-blue-500 transition-colors"
          onDragOver={(e) => e.preventDefault()}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            onChange={(e) =>
              e.target.files?.[0] && onFileSelect(e.target.files[0])
            }
            accept=".pdf,.doc,.docx,.txt,.rtf,.odt,.xlsx,.xls,.csv,.ods"
          />
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-blue-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            <div>
              <p className="text-base text-gray-600">
                Drag and drop your document here, or{" "}
                <span className="text-blue-500 font-medium">browse</span>
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Supported formats: PDF, Word, Excel, and text files
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Update the DocumentPage component
const DocumentPage: React.FC<{
  pageNumber: number;
  content: StructuredContent[];
  images: ImageData[];
}> = ({ pageNumber, content, images }) => {
  // Standard A4 page dimensions (in pixels at 96 DPI)
  const PAGE_WIDTH = 794; // ~8.27 inches
  const PAGE_HEIGHT = 1123; // ~11.69 inches

  // Debug log for images on this page
  const pageImages = images.filter((img) => img.position.page === pageNumber);
  console.log(
    `Page ${pageNumber} has ${pageImages.length} images:`,
    pageImages
  );

  return (
    <div className="relative bg-white shadow-lg rounded-lg mb-6 overflow-hidden">
      {/* Page header */}
      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
        <span className="text-sm font-medium text-gray-600">
          Page {pageNumber}
        </span>
      </div>

      {/* Page content container - maintain A4 aspect ratio */}
      <div
        className="relative bg-white"
        style={{
          width: "100%",
          maxWidth: PAGE_WIDTH,
          height: 0,
          paddingBottom: `${(PAGE_HEIGHT / PAGE_WIDTH) * 100}%`,
          margin: "0 auto",
        }}
      >
        <div className="absolute inset-0">
          {/* Images - Render images first so text appears on top */}
          {pageImages.map((img, index) => {
            // Calculate image dimensions while maintaining aspect ratio
            const aspectRatio =
              img.width && img.height ? img.width / img.height : 1;
            const containerWidth = img.position.width * 100;
            const containerHeight = img.position.height * 100;

            return (
              <div
                key={`img-${index}`}
                className="absolute"
                style={{
                  left: `${img.position.x * 100}%`,
                  top: `${img.position.y * 100}%`,
                  width: `${containerWidth}%`,
                  height: `${containerHeight}%`,
                  zIndex: 1,
                }}
              >
                <div className="relative w-full h-full">
                  <img
                    src={`data:image/${img.format || "jpeg"};base64,${
                      img.data
                    }`}
                    alt={img.alt || `Image ${index + 1} on page ${pageNumber}`}
                    className="absolute inset-0 w-full h-full object-contain"
                    style={{
                      maxWidth: "100%",
                      maxHeight: "100%",
                      objectFit: "contain",
                      aspectRatio: aspectRatio,
                    }}
                    onLoad={() =>
                      console.log(`Image ${index} loaded on page ${pageNumber}`)
                    }
                    onError={(e) => {
                      console.error(
                        `Failed to load image ${index} on page ${pageNumber}`
                      );
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      // Add a placeholder for failed images
                      target.parentElement?.classList.add(
                        "bg-gray-100",
                        "border",
                        "border-gray-200",
                        "rounded"
                      );
                    }}
                  />
                </div>
              </div>
            );
          })}

          {/* Text content */}
          {content.map((item, index) => (
            <div
              key={`text-${index}`}
              className="absolute"
              style={{
                left: `${item.position.x * 100}%`,
                top: `${item.position.y * 100}%`,
                fontSize: item.fontSize ? `${item.fontSize}px` : "inherit",
                fontFamily: item.font || "inherit",
                color: item.color || "inherit",
                transform: "translateY(-50%)", // Center text vertically
                maxWidth: "90%", // Prevent text from overflowing
                overflowWrap: "break-word",
                zIndex: 2, // Ensure text appears above images
              }}
            >
              {item.content}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Update the document preview component
const DocumentPreview: React.FC<{ processedDocument: ProcessedDocument }> = ({
  processedDocument,
}) => {
  const [activeTab, setActiveTab] = useState<"formatted" | "raw">("formatted");

  if (!processedDocument) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="text-center text-gray-500">
          <svg
            className="w-12 h-12 mx-auto text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p>No document data available</p>
        </div>
      </div>
    );
  }

  // Get structured content from the response
  const structuredContent = processedDocument.structured_content?.pages || [];

  // Transform image data to match the expected format
  const images = (processedDocument.sections?.[0]?.imageData || []).map(
    (img) => ({
      data: img.data,
      position: {
        x: img.position?.x || 0,
        y: img.position?.y || 0,
        width: img.position?.width || 1,
        height: img.position?.height || 1,
        page: img.position?.page || 1,
      } as ImagePosition,
    })
  );

  return (
    <div className="bg-white rounded-xl shadow-lg">
      {/* Document Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {processedDocument.title || "Untitled Document"}
        </h1>

        {/* Document Stats */}
        <div className="flex flex-wrap gap-4 text-sm text-gray-600 mt-4">
          {processedDocument.metadata?.pageCount && (
            <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
              <svg
                className="w-4 h-4 mr-2 text-blue-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              {processedDocument.metadata.pageCount} pages
            </div>
          )}
          {processedDocument.metadata?.fileType && (
            <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
              <svg
                className="w-4 h-4 mr-2 text-blue-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                />
              </svg>
              {processedDocument.metadata.fileType}
            </div>
          )}
        </div>

        {/* View Toggle */}
        <div className="flex justify-end mt-4">
          <div className="bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab("formatted")}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === "formatted"
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Formatted View
            </button>
            <button
              onClick={() => setActiveTab("raw")}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === "raw"
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Raw Text
            </button>
          </div>
        </div>
      </div>

      {/* Document Content */}
      <div className="p-6">
        {activeTab === "formatted" ? (
          <div className="space-y-6">
            {structuredContent.map((page: PageContent) => (
              <DocumentPage
                key={page.page_number}
                pageNumber={page.page_number}
                content={page.content}
                images={images}
              />
            ))}
          </div>
        ) : (
          <pre className="whitespace-pre-wrap text-sm font-mono bg-gray-50 p-4 rounded-lg overflow-x-auto">
            {JSON.stringify(processedDocument, null, 2)}
          </pre>
        )}
      </div>
    </div>
  );
};

// Update the DocumentPreviewModal to use the new DocumentPreview component
const DocumentPreviewModal: React.FC<{
  processedDocument: ProcessedDocument;
  onSave: () => void;
  onCancel: () => void;
  isLoading: boolean;
}> = ({ processedDocument, onSave, onCancel, isLoading }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-6xl max-h-[90vh] flex flex-col">
        <div className="flex-1 overflow-auto">
          <DocumentPreview processedDocument={processedDocument} />
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end space-x-4">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onSave}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                    />
                  </svg>
                  Save Template
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Component
const SectionTemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<ExtendedSectionTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showUploadArea, setShowUploadArea] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processedDocument, setProcessedDocument] =
    useState<ProcessedDocument | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      console.log("Loading templates...");

      const { data, error } = await supabase
        .from("section_templates")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Load error:", error);
        throw error;
      }

      console.log("Raw templates from DB:", data);

      // Convert database templates to ExtendedSectionTemplate format
      const convertedTemplates = (data || []).map((template) => {
        const converted = convertDatabaseTemplate(template as DatabaseTemplate);
        console.log("Converted template:", converted);
        return converted;
      });

      console.log("Final templates:", convertedTemplates);
      setTemplates(convertedTemplates);
    } catch (error) {
      toast.error("Failed to load templates");
      console.error("Error loading templates:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileSelect = async (file: File) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      // Get current user session
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError) {
        throw new Error("Authentication required");
      }

      if (!session?.user?.id) {
        throw new Error("User not authenticated");
      }

      const formData = new FormData();
      formData.append("file", file);

      const apiUrl = import.meta.env.VITE_API_URL || "";
      console.log("Processing document at:", `${apiUrl}/api/documents/process`);

      const response = await fetch(`${apiUrl}/api/documents/process`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Failed to process document: ${response.statusText}`
        );
      }

      const result = (await response.json()) as DocumentProcessingResponse;
      console.log("Document processing result:", result);

      if (!result.success || !result.data) {
        throw new Error("Failed to process document: Invalid response format");
      }

      // Transform the API response into ProcessedDocument format
      const processedDocument: ProcessedDocument = {
        title: file.name || result.data.title,
        metadata: {
          author: undefined,
          createdDate: new Date().toISOString(),
          modifiedDate: new Date().toISOString(),
          pageCount: result.data.page_count,
          fileType: result.data.file_type,
        },
        sections: [
          {
            title: "Document Content",
            content: result.data.text,
            description: `Processed from ${file.name}`,
            imageData: result.data.images.map((img, index) => ({
              data: img.data,
              width: img.width,
              height: img.height,
              position: {
                x: img.position.x,
                y: img.position.y,
                width: img.position.width,
                height: img.position.height,
                page: img.position.page,
              } as ImagePosition,
              alt: `Image ${index + 1}`,
            })),
            order: 0,
            pageStart: 1,
            pageEnd: result.data.page_count,
          },
        ],
        fullContent: result.data.text,
        structured_content: result.data.structured_content,
      };

      // Show preview instead of saving immediately
      setProcessedDocument(processedDocument);
      setShowPreview(true);
      setShowUploadArea(false);
      setUploadProgress(100);
    } catch (error) {
      console.error("Error processing document:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to process document"
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleSaveTemplate = async () => {
    if (!processedDocument || !showPreview) return;

    try {
      setIsUploading(true);

      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        throw new Error("User not authenticated");
      }

      // Create a new template from the processed document
      const newTemplate: DatabaseTemplate = {
        name: processedDocument.title || "Untitled Template",
        description: "Generated from uploaded document",
        sections:
          processedDocument.sections?.map((section, index) => ({
            id: `section-${Date.now()}-${index}`,
            title: section.title || `Section ${index + 1}`,
            content: section.content || "",
            description: section.description || "",
            elements: section.elements || [],
            imageData: section.imageData || [],
            data: section.data || [],
            order: index,
          })) || [],
        metadata: processedDocument.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: session.user.id,
      };

      const { data: savedTemplate, error: saveError } = await supabase
        .from("section_templates")
        .insert([newTemplate])
        .select("*")
        .single();

      if (saveError) throw saveError;

      const convertedTemplate = convertDatabaseTemplate(
        savedTemplate as DatabaseTemplate
      );
      setTemplates((prev) => [convertedTemplate, ...prev]);

      toast.success("Template saved successfully");
      setShowPreview(false);
      setProcessedDocument(null);
    } catch (error) {
      console.error("Error saving template:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to save template"
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteTemplate = async (template: ExtendedSectionTemplate) => {
    if (!window.confirm("Are you sure you want to delete this template?"))
      return;

    try {
      const { error } = await supabase
        .from("section_templates")
        .delete()
        .eq("id", template.id);

      if (error) throw error;

      toast.success("Template deleted successfully");
      setTemplates(templates.filter((t) => t.id !== template.id));
    } catch (error) {
      toast.error("Failed to delete template");
      console.error("Error deleting template:", error);
    }
  };

  const handleEditTemplate = (template: ExtendedSectionTemplate) => {
    // Implement edit functionality
    console.log("Edit template:", template);
  };

  const filteredTemplates = templates.filter((template) =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Document Templates
                </h1>
                <p className="mt-2 text-sm text-gray-600">
                  Manage and create document templates for your proposals
                </p>
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={() => setShowUploadArea(!showUploadArea)}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                    />
                  </svg>
                  Import Document
                </button>
                <button
                  onClick={() => setShowUploadArea(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create Template
                </button>
              </div>
            </div>

            {/* Search Bar */}
            <div className="mt-6">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md h-11"
                />
              </div>
            </div>
          </div>

          {/* File Upload Area */}
          {showUploadArea && (
            <FileUploadArea
              onFileSelect={handleFileSelect}
              isUploading={isUploading}
              uploadProgress={uploadProgress}
              onClose={() => setShowUploadArea(false)}
            />
          )}

          {/* Main Content */}
          {isLoading ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
                <p className="text-gray-600">Loading templates...</p>
              </div>
            </div>
          ) : (
            <>
              {filteredTemplates.length > 0 ? (
                filteredTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onEdit={handleEditTemplate}
                    onDelete={handleDeleteTemplate}
                  />
                ))
              ) : (
                <div className="col-span-full flex flex-col items-center justify-center py-12 px-4 text-center">
                  <svg
                    className="w-12 h-12 text-gray-400 mb-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
                    />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No templates found
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Get started by creating your first template or importing a
                    document.
                  </p>
                  <div className="flex space-x-4">
                    <button
                      onClick={() => setShowUploadArea(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      Import Document
                    </button>
                    <button
                      onClick={() => setShowUploadArea(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                    >
                      Create Template
                    </button>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Preview Modal */}
          {showPreview && processedDocument && (
            <DocumentPreviewModal
              processedDocument={processedDocument}
              onSave={handleSaveTemplate}
              onCancel={() => {
                setShowPreview(false);
                setProcessedDocument(null);
              }}
              isLoading={isUploading}
            />
          )}
        </div>
      </div>
    </Layout>
  );
};

export default SectionTemplateManager;
