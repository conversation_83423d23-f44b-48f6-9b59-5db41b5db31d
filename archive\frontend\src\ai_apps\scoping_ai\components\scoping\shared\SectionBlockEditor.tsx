import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  useDroppable,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

// Define the types of content blocks available
export type BlockType =
  | "header"
  | "text"
  | "image"
  | "quote"
  | "list"
  | "bullet-list"
  | "numbered-list";

// Define the structure of a content block
export interface Block {
  id: string;
  type: BlockType;
  content: string;
  level?: number; // For headers (h1, h2, h3)
  url?: string; // For images
  alt?: string; // For images
  caption?: string; // For images
  items?: string[]; // For lists
  imagePosition?: {
    // Position info for images from PDF
    x: number;
    y: number;
    page?: number;
  };
}

interface SectionBlockEditorProps {
  initialBlocks: Block[];
  onSave: (blocks: Block[]) => void;
  readOnly?: boolean;
}

// Toolbar button component
const ToolbarButton = ({
  icon,
  label,
  onClick,
}: {
  icon: string;
  label: string;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className="flex items-center p-2 hover:bg-gray-100 rounded text-gray-700 text-sm"
    title={label}
  >
    <svg
      className="w-5 h-5 mr-1"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d={icon}
      />
    </svg>
    <span>{label}</span>
  </button>
);

// Content block component
const BlockComponent = ({
  block,
  isSelected,
  onClick,
  onChange,
  onDelete,
  readOnly,
}: {
  block: Block;
  isSelected: boolean;
  onClick: () => void;
  onChange: (updates: Partial<Block>) => void;
  onDelete: () => void;
  readOnly?: boolean;
}) => {
  const renderContent = () => {
    switch (block.type) {
      case "header":
        const HeaderTag = `h${block.level || 2}` as keyof JSX.IntrinsicElements;
        return (
          <HeaderTag
            className={`font-bold ${
              block.level === 1
                ? "text-2xl"
                : block.level === 2
                ? "text-xl"
                : "text-lg"
            }`}
          >
            {readOnly ? (
              block.content
            ) : (
              <input
                type="text"
                value={block.content}
                onChange={(e) => onChange({ content: e.target.value })}
                className="w-full bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-500 rounded px-1"
              />
            )}
          </HeaderTag>
        );

      case "text":
        return readOnly ? (
          <p className="text-gray-700">{block.content}</p>
        ) : (
          <textarea
            value={block.content}
            onChange={(e) => onChange({ content: e.target.value })}
            className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
        );

      case "image":
        return (
          <div className="image-container">
            {block.url ? (
              <>
                <img
                  src={block.url}
                  alt={block.alt || ""}
                  className="max-w-full h-auto rounded-lg border border-gray-200"
                />
                {block.caption && (
                  <div className="text-center text-sm text-gray-500 mt-2 italic">
                    {block.caption}
                  </div>
                )}
                {block.imagePosition && (
                  <div className="text-xs text-gray-400 mt-1">
                    Position: Page {block.imagePosition.page}, X:{" "}
                    {block.imagePosition.x.toFixed(0)}, Y:{" "}
                    {block.imagePosition.y.toFixed(0)}
                  </div>
                )}
              </>
            ) : (
              <div className="bg-gray-200 p-8 text-center text-gray-500 rounded-lg">
                Image Placeholder
              </div>
            )}
            {!readOnly && (
              <div className="mt-2">
                <input
                  type="text"
                  value={block.url || ""}
                  onChange={(e) => onChange({ url: e.target.value })}
                  placeholder="Image URL"
                  className="w-full p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="text"
                  value={block.alt || ""}
                  onChange={(e) => onChange({ alt: e.target.value })}
                  placeholder="Alt text"
                  className="w-full mt-2 p-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}
          </div>
        );

      case "quote":
        return (
          <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600">
            {readOnly ? (
              block.content
            ) : (
              <textarea
                value={block.content}
                onChange={(e) => onChange({ content: e.target.value })}
                className="w-full p-2 bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={2}
              />
            )}
          </blockquote>
        );

      case "bullet-list":
      case "list":
        if (readOnly) {
          return (
            <ul className="list-disc pl-5">
              {block.items?.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          );
        }

        return (
          <div className="space-y-2">
            {block.items?.map((item, index) => (
              <div key={index} className="flex items-center">
                <span className="mr-2">•</span>
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newItems = [...(block.items || [])];
                    newItems[index] = e.target.value;
                    onChange({ items: newItems });
                  }}
                  className="flex-grow p-1 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded"
                />
                <button
                  onClick={() => {
                    const newItems = [...(block.items || [])];
                    newItems.splice(index, 1);
                    onChange({ items: newItems });
                  }}
                  className="ml-2 p-1 text-red-500 hover:bg-red-50 rounded"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ))}
            <button
              onClick={() => {
                onChange({ items: [...(block.items || []), ""] });
              }}
              className="text-blue-500 text-sm flex items-center"
            >
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Add item
            </button>
          </div>
        );

      case "numbered-list":
        if (readOnly) {
          return (
            <ol className="list-decimal pl-5">
              {block.items?.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ol>
          );
        }

        return (
          <div className="space-y-2">
            {block.items?.map((item, index) => (
              <div key={index} className="flex items-center">
                <span className="mr-2">{index + 1}.</span>
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newItems = [...(block.items || [])];
                    newItems[index] = e.target.value;
                    onChange({ items: newItems });
                  }}
                  className="flex-grow p-1 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded"
                />
                <button
                  onClick={() => {
                    const newItems = [...(block.items || [])];
                    newItems.splice(index, 1);
                    onChange({ items: newItems });
                  }}
                  className="ml-2 p-1 text-red-500 hover:bg-red-50 rounded"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ))}
            <button
              onClick={() => {
                onChange({ items: [...(block.items || []), ""] });
              }}
              className="text-blue-500 text-sm flex items-center"
            >
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Add item
            </button>
          </div>
        );

      default:
        return <p>{block.content || "Content block"}</p>;
    }
  };

  return (
    <div
      className={`p-4 rounded-lg border my-2 ${
        isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200"
      } relative group transition-all`}
      onClick={onClick}
    >
      <div className="flex items-center mb-2 text-xs text-gray-500 uppercase tracking-wide">
        <span className="mr-2">
          {block.type === "header" ? `H${block.level}` : block.type}
        </span>
      </div>

      {renderContent()}

      {!readOnly && (
        <div
          className={`absolute top-2 right-2 ${
            isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"
          } transition-opacity`}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="p-1 text-red-500 hover:bg-red-50 rounded-full"
            title="Delete block"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

const SectionBlockEditor: React.FC<SectionBlockEditorProps> = ({
  initialBlocks = [],
  onSave,
  readOnly = false,
}) => {
  const [blocks, setBlocks] = useState<Block[]>(initialBlocks);
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Update blocks whenever initialBlocks changes
  useEffect(() => {
    setBlocks(initialBlocks);
    setIsDirty(false);
  }, [initialBlocks]);

  // Handler for adding a new block
  const handleAddBlock = (type: BlockType) => {
    const id = `block-${Date.now()}`;
    let newBlock: Block = {
      id,
      type,
      content: "",
    };

    // Set defaults based on block type
    switch (type) {
      case "header":
        newBlock.level = 2;
        newBlock.content = "New Heading";
        break;
      case "bullet-list":
      case "numbered-list":
      case "list":
        newBlock.items = ["New list item"];
        break;
      case "image":
        newBlock.alt = "Image description";
        break;
      case "quote":
        newBlock.content = "New quote";
        break;
      default:
        newBlock.content = "New content";
    }

    const newBlocks = [...blocks, newBlock];
    setBlocks(newBlocks);
    setSelectedBlockId(id);
    setIsDirty(true);
  };

  // Handler for changing a block's properties
  const handleBlockChange = (blockId: string, updates: Partial<Block>) => {
    const newBlocks = blocks.map((block) =>
      block.id === blockId ? { ...block, ...updates } : block
    );
    setBlocks(newBlocks);
    setIsDirty(true);
  };

  // Handler for deleting a block
  const handleDeleteBlock = (blockId: string) => {
    const newBlocks = blocks.filter((block) => block.id !== blockId);
    setBlocks(newBlocks);
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null);
    }
    setIsDirty(true);
  };

  // Handler for drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setBlocks((blocks) => {
        const oldIndex = blocks.findIndex((block) => block.id === active.id);
        const newIndex = blocks.findIndex((block) => block.id === over.id);

        return arrayMove(blocks, oldIndex, newIndex);
      });
      setIsDirty(true);
    }
  };

  // Handle save
  const handleSave = () => {
    onSave(blocks);
    setIsDirty(false);
  };

  // Toolbar with block types
  const toolbar = !readOnly && (
    <div className="flex flex-wrap border-b border-gray-200 p-2 bg-gray-50 rounded-t">
      <ToolbarButton
        icon="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        label="Heading"
        onClick={() => handleAddBlock("header")}
      />
      <ToolbarButton
        icon="M4 6h16M4 12h16M4 18h7"
        label="Text"
        onClick={() => handleAddBlock("text")}
      />
      <ToolbarButton
        icon="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
        label="Image"
        onClick={() => handleAddBlock("image")}
      />
      <ToolbarButton
        icon="M8 9l3 3-3 3M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        label="Quote"
        onClick={() => handleAddBlock("quote")}
      />
      <ToolbarButton
        icon="M4 6h16M4 10h16M4 14h16M4 18h16"
        label="Bullet List"
        onClick={() => handleAddBlock("bullet-list")}
      />
      <ToolbarButton
        icon="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
        label="Numbered List"
        onClick={() => handleAddBlock("numbered-list")}
      />
    </div>
  );

  // Render blocks
  const content = (
    <div className="space-y-2 p-4 bg-white rounded-b border-l border-r border-b">
      {blocks.length === 0 ? (
        <div className="text-center text-gray-500 py-6">
          {readOnly
            ? "No content blocks"
            : "Add blocks using the toolbar above"}
        </div>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={blocks.map((block) => block.id)}
            strategy={verticalListSortingStrategy}
          >
            {blocks.map((block) => (
              <BlockComponent
                key={block.id}
                block={block}
                isSelected={selectedBlockId === block.id}
                onClick={() => setSelectedBlockId(block.id)}
                onChange={(updates) => handleBlockChange(block.id, updates)}
                onDelete={() => handleDeleteBlock(block.id)}
                readOnly={readOnly}
              />
            ))}
          </SortableContext>
        </DndContext>
      )}
    </div>
  );

  // Save button
  const saveButton = !readOnly && isDirty && (
    <div className="flex justify-end mt-4">
      <button
        onClick={handleSave}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Save Changes
      </button>
    </div>
  );

  return (
    <div className="section-block-editor">
      {toolbar}
      {content}
      {saveButton}
    </div>
  );
};

export default SectionBlockEditor;
