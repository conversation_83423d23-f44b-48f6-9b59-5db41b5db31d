import React from "react";
import { Loader2 } from "lucide-react";
import { But<PERSON> } from "@base/components/ui/button";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "@base/components/ui/tabs";
import { DocumentCard } from "./DocumentCard";
import { TemplateCard } from "./TemplateCard";
import {
  LibraryDocument,
  TemplateDocument,
} from "../../../types/document-library";

interface DocumentLibraryTabsProps {
  isLoading: boolean;
  existingDocuments: LibraryDocument[];
  templateDocuments: TemplateDocument[];
  onDocumentPreview: (id: number | string) => void;
  onDocumentEdit: (id: number | string) => void;
  onTemplatePreview: (id: string) => void;
  onMakeDocumentPublic: (id: string) => void;
  onUploadClick: () => void;
}

export function DocumentLibraryTabs({
  isLoading,
  existingDocuments,
  templateDocuments,
  onDocumentPreview,
  onDocumentEdit,
  onTemplatePreview,
  onMakeDocumentPublic,
  onUploadClick,
}: DocumentLibraryTabsProps) {
  return (
    <Tabs defaultValue="documents" className="w-full">
      <TabsList className="grid w-full grid-cols-2 mb-6">
        <TabsTrigger value="documents">Existing Documents</TabsTrigger>
        <TabsTrigger value="templates">Templates</TabsTrigger>
      </TabsList>

      <TabsContent value="documents">
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <p>Loading documents...</p>
          </div>
        ) : existingDocuments.length === 0 ? (
          <div className="text-center py-12 border rounded-lg bg-muted/30">
            <p className="text-muted-foreground mb-4">No documents found</p>
            <Button onClick={onUploadClick}>Upload a document</Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {existingDocuments.map((doc) => (
              <DocumentCard
                key={doc.id}
                document={doc}
                onPreview={onDocumentPreview}
                onEdit={onDocumentEdit}
                onMakePublic={onMakeDocumentPublic}
              />
            ))}
          </div>
        )}
      </TabsContent>

      <TabsContent value="templates">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templateDocuments.map((template) => (
            <TemplateCard
              key={template.id}
              template={template}
              onPreview={onTemplatePreview}
            />
          ))}
        </div>
      </TabsContent>
    </Tabs>
  );
}
