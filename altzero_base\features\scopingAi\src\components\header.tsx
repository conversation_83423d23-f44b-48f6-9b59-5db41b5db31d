import React from "react";
import { Link } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { cn } from "../lib/utils";
import { UserButton } from "../../../base/components/user-button";
import { useUser } from "../../../base/contextapi/UserContext";
import { ThemeToggle } from "../../../base/components/theme-toggle";

export function Header() {
  const pathname = useLocation().pathname;
  const { user } = useUser();

  const navigation = [
    { name: "Dashboard", href: "/scoping-dashboard" },
    { name: "Scoping Proposals", href: "/scoping-proposals" },
    { name: "Knowledge Base", href: "/knowledge-base" },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white dark:bg-gray-900 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link
            to="/scoping-dashboard"
            className="flex items-center space-x-2 flex-shrink-0"
          >
            <svg
              className="h-8 w-8 text-primary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              />
            </svg>
            <span className="hidden sm:block font-bold text-xl text-gray-900 dark:text-white">
              ScopingAI
            </span>
          </Link>

          {/* Navigation - Always visible */}
          <nav className="flex items-center space-x-2 sm:space-x-4 lg:space-x-8 flex-1 justify-center">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors duration-200 whitespace-nowrap",
                  pathname === item.href || pathname.startsWith(item.href + "/")
                    ? "bg-primary text-primary-foreground"
                    : "text-gray-700 dark:text-gray-300 hover:text-primary hover:bg-gray-100 dark:hover:bg-gray-800"
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right side controls */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            <ThemeToggle />
            <UserButton />
          </div>
        </div>
      </div>
    </header>
  );
}
