import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Form, Spinner, Alert } from 'react-bootstrap';
import ReactMarkdown from 'react-markdown';
import { SectionStatus } from '@/types/document';
import { Section } from '../../../types/scoping';
import { Box } from '@mui/material';

interface SectionEditorProps {
  section: Section;
  onUpdateContent: (sectionId: string, content: string) => Promise<void>;
  isLoading: boolean;
  onGenerateSection?: (sectionId: string) => Promise<void>;
}

const ErrorDisplay = ({ error }: { error: any }) => {
  if (!error) return null;

  return (
    <Alert variant="danger" className="mb-3">
      <Alert.Heading>Generation Error</Alert.Heading>
      <div>
        <strong>Message:</strong> {error.message}
        {error.timestamp && (
          <div>
            <strong>Time:</strong> {new Date(error.timestamp).toLocaleString()}
          </div>
        )}
        {error.details && (
          <div>
            <strong>Details:</strong>
            <pre className="mt-2 p-2 bg-light rounded">
              {JSON.stringify(error.details, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </Alert>
  );
};

const SectionEditor: React.FC<SectionEditorProps> = ({
  section,
  onUpdateContent,
  isLoading,
  onGenerateSection
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editableContent, setEditableContent] = useState(section.content);
  const [isSaving, setIsSaving] = useState(false);
  const [showDebug, setShowDebug] = useState(false);

  // Handle content change
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditableContent(e.target.value);
  };

  // Save changes
  const handleSave = async () => {
    if (isLoading || isSaving) return;
    
    setIsSaving(true);
    try {
      await onUpdateContent(section.id, editableContent);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving section content:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Cancel editing
  const handleCancel = () => {
    setEditableContent(section.content);
    setIsEditing(false);
  };

  // Render section content with simple markdown-like formatting
  const renderContent = (content: string) => {
    if (!content) return <p className="text-gray-500 italic">No content available</p>;

    // Replace headers (# Header)
    let formattedContent = content
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-bold mb-3">$1</h2>')
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-bold mb-2">$1</h3>')
      // Replace bold (**text**)
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Replace italic (*text*)
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Replace links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:underline">$1</a>')
      // Replace lists
      .replace(/^\s*-\s*(.*$)/gm, '<li class="ml-6 list-disc">$1</li>')
      .replace(/^\s*\d+\.\s*(.*$)/gm, '<li class="ml-6 list-decimal">$1</li>')
      // Replace code blocks
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-3 rounded my-2 overflow-x-auto"><code>$1</code></pre>')
      // Replace inline code
      .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
      // Replace paragraphs
      .replace(/\n\n/g, '</p><p class="mb-4">');

    // Wrap with paragraph tags if not already there
    if (!formattedContent.startsWith('<')) {
      formattedContent = `<p class="mb-4">${formattedContent}</p>`;
    }

    return <div dangerouslySetInnerHTML={{ __html: formattedContent }} />;
  };
  
  // Debug button to view section data
  const renderDebugButton = () => {
    return (
      <button 
        onClick={() => {
          setShowDebug(!showDebug);
          console.log('Section data:', section);
        }}
        className="px-3 py-1 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-50 ml-2"
      >
        Debug
      </button>
    );
  };
  
  // Debug view to show section data
  const renderDebugView = () => {
    if (!showDebug) return null;
    
    return (
      <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md overflow-auto">
        <h3 className="text-sm font-semibold mb-2">Debug Information</h3>
        <pre className="text-xs overflow-auto max-h-48">
          {JSON.stringify({
            id: section.id,
            title: section.title,
            status: section.status,
            contentLength: section.content?.length || 0,
            content: section.content?.substring(0, 200) + (section.content?.length > 200 ? '...' : '')
          }, null, 2)}
        </pre>
        
        <button 
          onClick={() => console.log('Full section data:', section)}
          className="mt-2 px-2 py-1 text-xs bg-gray-200 rounded"
        >
          Log Full Data
        </button>
      </div>
    );
  };

  if (section.status === 'pending') {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
          {renderDebugButton()}
        </div>
        <div className="bg-gray-50 border border-gray-200 rounded-md p-8 text-center">
          <p className="text-gray-500 mb-4">This section hasn't been generated yet.</p>
          
          <Button 
            variant="primary" 
            size="lg" 
            className="mt-3 px-5 py-3 font-bold" 
            onClick={() => onGenerateSection && onGenerateSection(section.id)}
            disabled={isLoading}
          >
            <div className="flex items-center justify-center">
              {isLoading ? (
                <Spinner animation="border" size="sm" className="mr-2" />
              ) : (
                <svg className="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              )}
              Generate This Section
            </div>
          </Button>
        </div>
        {renderDebugView()}
      </div>
    );
  }

  if (section.status === 'generating' || isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
          {renderDebugButton()}
        </div>
        <div className="bg-blue-50 border border-blue-100 rounded-md p-8 text-center">
          <div className="flex justify-center mb-4">
            <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <p className="text-blue-700 font-medium">Generating content...</p>
          <p className="text-blue-500 mt-2 text-sm">AI is creating content for this section. This may take a moment.</p>
        </div>
        {renderDebugView()}
      </div>
    );
  }

  if (section.status === 'failed') {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
          {renderDebugButton()}
        </div>
        <div className="bg-red-50 border border-red-100 rounded-md p-8 text-center">
          <svg className="h-12 w-12 text-red-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <p className="text-red-700 font-medium mb-2">Failed to generate content</p>
          <p className="text-red-600 text-sm mb-4">There was an error generating content for this section. Please try again.</p>
          
          <Button 
            variant="danger" 
            size="lg" 
            className="mt-3" 
            onClick={() => onGenerateSection && onGenerateSection(section.id)}
            disabled={isLoading}
          >
            <div className="flex items-center justify-center">
              {isLoading ? (
                <Spinner animation="border" size="sm" className="mr-2" />
              ) : (
                <svg className="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              )}
              Retry Generation
            </div>
          </Button>
        </div>
        {renderDebugView()}
        {section.error && (
          <ErrorDisplay error={section.error} />
        )}
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
        <div className="flex items-center">
          {renderDebugButton()}
          {isEditing ? (
            <div className="flex space-x-2">
              <Button
                variant="outline-secondary"
                onClick={handleCancel}
                disabled={isSaving}
                size="sm"
              >
                Cancel
              </Button>
              <Button
                variant="success"
                onClick={handleSave}
                disabled={isSaving}
                size="sm"
              >
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            </div>
          ) : (
            <Button
              variant="outline-primary"
              onClick={() => setIsEditing(true)}
              disabled={isLoading}
              size="sm"
            >
              Edit
            </Button>
          )}
        </div>
      </div>

      {section.error && <ErrorDisplay error={section.error} />}

      {isEditing ? (
        <Form.Group className="mb-3">
          <Form.Control
            as="textarea"
            value={editableContent}
            onChange={(e) => setEditableContent(e.target.value)}
            rows={10}
            disabled={isSaving}
            className="font-mono"
          />
          <Form.Text className="text-muted">
            Supports basic Markdown: # Headers, **bold**, *italic*, - lists, ```code blocks```
          </Form.Text>
        </Form.Group>
      ) : (
        <div className="prose prose-blue max-w-none">
          {renderContent(section.content)}
        </div>
      )}
      
      {renderDebugView()}
    </div>
  );
};

export default SectionEditor; 