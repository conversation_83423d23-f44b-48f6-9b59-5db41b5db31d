{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Paths */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@base/*": ["./base/*"],
      "@layout/*": ["./layout/*"],
      "@header/*": ["./header/*"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["server/**/*", "vite.config.ts", "node_modules", "dist"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
