import React from "react";
import { <PERSON><PERSON> } from "../../../../base/components/ui/button";
import { Label } from "../../../../base/components/ui/label";
import { Input } from "../../../../base/components/ui/input";
import { Textarea } from "../../../../base/components/ui/textarea";
import {
  RadioGroup,
  RadioGroupItem,
} from "../../../base/components/ui/radio-group";
import {
  Sparkles,
  Loader2,
  ArrowRight,
  ChevronDown,
  FileText,
  Eye,
  Plus,
  Wand2,
  X,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../../base/components/ui/accordion";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../../base/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "../../../../base/components/ui/command";
import { ScrollArea } from "../../../../base/components/ui/scroll-area";
import { Badge } from "../../../../base/components/ui/badge";
import { Progress } from "../../../../base/components/ui/progress";
import { Separator } from "../../../../base/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../../../base/components/ui/dialog";
import {
  FileText as LucideFileText,
  User,
  Calendar,
  DollarSign,
  Trash2,
  Check,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../base/components/ui/select";
import { useDocumentForm } from "../contexts/DocumentFormContext";
import { StepProps } from "../types/document-form";
import { useState, useEffect } from "react";
import { useUser } from "../../../../base/contextapi/UserContext";
import { useToast } from "../../../../base/hooks/use-toast";
import { fetchPrompts, PromptTemplate } from "../services/promptService";

// Component for inline document preview
const InlineDocumentPreview: React.FC<{
  referenceDocument?: any;
  selectedTemplate: string;
}> = ({ referenceDocument, selectedTemplate }) => {
  // Show reference document preview if available
  if (referenceDocument) {
    // Get content with proper string conversion
    const getDocumentContent = () => {
      const rawContent =
        referenceDocument.rawData?.fullContent ||
        referenceDocument.rawData?.content ||
        referenceDocument.content;

      if (typeof rawContent === "string") {
        return rawContent;
      } else if (rawContent && typeof rawContent === "object") {
        return JSON.stringify(rawContent, null, 2);
      } else {
        return rawContent ? String(rawContent) : "No content available";
      }
    };

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center gap-2 mb-4">
          <Eye size={20} className="text-primary" />
          <h3 className="text-lg font-semibold">Reference Document Preview</h3>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 flex-1 flex flex-col">
          {/* Document Header */}
          <div className="border-b pb-3 mb-4">
            <h4 className="font-medium text-lg">{referenceDocument.title}</h4>
            <p className="text-sm text-muted-foreground">
              {referenceDocument.type} • {referenceDocument.client} •{" "}
              {referenceDocument.date}
            </p>
            {referenceDocument.rawData?.metadata && (
              <div className="text-xs text-muted-foreground mt-1">
                {referenceDocument.rawData.metadata.author &&
                  `Author: ${referenceDocument.rawData.metadata.author} • `}
                {referenceDocument.rawData.metadata.pageCount &&
                  `${referenceDocument.rawData.metadata.pageCount} pages`}
              </div>
            )}
          </div>

          {/* Scrollable Content Area */}
          <ScrollArea className="flex-1 bg-white rounded-lg border">
            <div className="p-4">
              {/* Document Sections */}
              {referenceDocument.rawData?.sections &&
              referenceDocument.rawData.sections.length > 0 ? (
                <div className="space-y-6">
                  <div className="mb-4">
                    <h5 className="font-medium text-sm mb-2">
                      Document Sections (
                      {referenceDocument.rawData.sections.length})
                    </h5>
                  </div>
                  {referenceDocument.rawData.sections.map(
                    (section: any, index: number) => (
                      <div
                        key={index}
                        className="border-l-4 border-blue-500 pl-4 pb-4"
                      >
                        <h6 className="font-medium text-sm mb-2">
                          {section.title}
                        </h6>
                        {section.description && (
                          <p className="text-xs text-muted-foreground mb-2">
                            {section.description}
                          </p>
                        )}
                        <div className="text-xs prose prose-sm max-w-none">
                          {typeof section.content === "string" ? (
                            <div>{section.content}</div>
                          ) : (
                            <div className="bg-gray-100 p-2 rounded text-gray-600">
                              {section.content
                                ? JSON.stringify(section.content, null, 2)
                                : "No content available"}
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </div>
              ) : (
                /* Fallback to full content */
                <div className="space-y-4">
                  <h5 className="font-medium text-sm">Document Content</h5>
                  <div className="text-sm prose prose-sm max-w-none">
                    <div>{getDocumentContent()}</div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-xs text-blue-800">
              💡 This document will be used as a reference template for
              generating your new document.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show message when no template is selected
  if (!selectedTemplate) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center gap-2 mb-4">
          <FileText size={20} className="text-muted-foreground" />
          <h3 className="text-lg font-semibold text-muted-foreground">
            Template Preview
          </h3>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 flex-1 flex flex-col items-center justify-center">
          <FileText size={48} className="text-muted-foreground mb-4" />
          <h4 className="font-medium text-muted-foreground mb-2">
            No Template Selected
          </h4>
          <p className="text-sm text-muted-foreground text-center">
            Select a template from the previous step to see its structure
            preview here.
          </p>
        </div>
      </div>
    );
  }

  // Show predefined template structure when template is selected but no reference document
  const getTemplateStructure = (template: string) => {
    const commonSections = [
      {
        name: "Executive Summary",
        color: "blue",
        description: "Project overview and key points",
      },
      {
        name: "Project Scope",
        color: "green",
        description: "Detailed requirements and deliverables",
      },
      {
        name: "Timeline",
        color: "yellow",
        description: "Project milestones and deadlines",
      },
      {
        name: "Budget",
        color: "purple",
        description: "Cost breakdown and pricing",
      },
    ];

    switch (template) {
      case "Proposal":
        return [
          ...commonSections,
          {
            name: "Technical Approach",
            color: "indigo",
            description: "Implementation methodology",
          },
          {
            name: "Team & Qualifications",
            color: "pink",
            description: "Team members and expertise",
          },
        ];
      case "SOW":
        return [
          ...commonSections,
          {
            name: "Statement of Work",
            color: "indigo",
            description: "Detailed work breakdown",
          },
          {
            name: "Terms & Conditions",
            color: "pink",
            description: "Legal and contractual terms",
          },
        ];
      case "Report":
        return [
          {
            name: "Introduction",
            color: "blue",
            description: "Background and objectives",
          },
          {
            name: "Methodology",
            color: "green",
            description: "Research and analysis approach",
          },
          {
            name: "Findings",
            color: "yellow",
            description: "Key results and insights",
          },
          {
            name: "Recommendations",
            color: "purple",
            description: "Proposed actions and next steps",
          },
        ];
      default:
        return commonSections;
    }
  };

  const sections = getTemplateStructure(selectedTemplate);

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center gap-2 mb-4">
        <FileText size={20} className="text-primary" />
        <h3 className="text-lg font-semibold">
          Predefined {selectedTemplate} Template
        </h3>
      </div>

      <div className="bg-gray-50 rounded-lg p-4 flex-1 flex flex-col">
        <div className="border-b pb-3 mb-4">
          <h4 className="font-medium">Standard {selectedTemplate} Structure</h4>
          <p className="text-sm text-muted-foreground">
            This template will be customized based on your input
          </p>
        </div>

        <ScrollArea className="flex-1">
          <div className="space-y-3 text-sm">
            {sections.map((section, index) => (
              <div
                key={index}
                className={`bg-white p-3 rounded border-l-4 border-${section.color}-500`}
              >
                <h5 className="font-medium mb-1">{section.name}</h5>
                <p className="text-gray-600 text-xs">{section.description}</p>
              </div>
            ))}
          </div>
        </ScrollArea>

        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-800">
            💡 This structure will be automatically populated with your project
            information and requirements.
          </p>
        </div>
      </div>
    </div>
  );
};

const DocumentOverviewStep: React.FC<StepProps> = ({ onNext, onBack }) => {
  const {
    selectedTemplate,
    clientData,
    selectedClient,
    referenceDocument,
    promptData,
    promptTemplates,
    promptSearchOpen,
    selectedPrompt,
    isNewPrompt,
    isGenerating,
    isSavingPrompt,
    requirementsOption,
    requirementsText,
    projectInfo,
    generationStatus,
    generationProgress,
    setPromptSearchOpen,
    handlePromptSelect,
    handlePromptInputChange,
    savePromptTemplate,
    handleGenerate,
    selectedKnowledgeDocuments,
  } = useDocumentForm();

  // Add prompt selection state
  const { user } = useUser();
  const { toast } = useToast();
  const [availablePrompts, setAvailablePrompts] = useState<PromptTemplate[]>(
    []
  );
  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false);
  const [selectedAiPrompt, setSelectedAiPrompt] =
    useState<PromptTemplate | null>(null);
  const [localPromptSearchOpen, setLocalPromptSearchOpen] = useState(false);

  // Load prompts when component mounts
  useEffect(() => {
    if (user) {
      loadAvailablePrompts();
    }
  }, [user]);

  const loadAvailablePrompts = async () => {
    try {
      setIsLoadingPrompts(true);
      const prompts = await fetchPrompts();
      setAvailablePrompts(prompts);
    } catch (error) {
      console.error("Error loading prompts:", error);
      toast({
        title: "Error",
        description: "Failed to load AI prompt templates",
        variant: "destructive",
      });
    } finally {
      setIsLoadingPrompts(false);
    }
  };

  const handlePromptSelection = (prompt: PromptTemplate) => {
    setSelectedAiPrompt(prompt);
    setLocalPromptSearchOpen(false);
    toast({
      title: "Prompt Selected",
      description: `Selected "${prompt.name}" for document generation`,
    });
  };

  const clearSelectedPrompt = () => {
    setSelectedAiPrompt(null);
    toast({
      title: "Prompt Cleared",
      description: "No specific prompt will be used for generation",
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "scoping":
      case "scope":
        return "bg-blue-100 text-blue-800";
      case "requirements":
        return "bg-green-100 text-green-800";
      case "proposals":
      case "proposal":
        return "bg-purple-100 text-purple-800";
      case "technical":
        return "bg-orange-100 text-orange-800";
      case "creative":
        return "bg-pink-100 text-pink-800";
      case "general":
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get template information
  const getTemplateInfo = () => {
    const templates = [
      { id: "standard", name: "Standard Scope" },
      { id: "agile", name: "Agile Project" },
      { id: "minimal", name: "Minimal Scope" },
      { id: "custom", name: "Custom Template" },
    ];
    return (
      templates.find((t) => t.id === selectedTemplate) || {
        id: "",
        name: "No template selected",
      }
    );
  };

  const templateInfo = getTemplateInfo();

  // Create a wrapper function for handleGenerate that includes the selected AI prompt
  const handleGenerateWithPrompt = async (preserveOriginalContent: boolean) => {
    try {
      // If we have a selected AI prompt, we need to pass it to the generation process
      if (selectedAiPrompt) {
        console.log("Using selected AI prompt for generation:", {
          id: selectedAiPrompt.id,
          name: selectedAiPrompt.name,
          category: selectedAiPrompt.category,
          content: selectedAiPrompt.content,
          variables: selectedAiPrompt.variables,
        });

        // Create an enhanced aiPrompts object that includes our selected prompt
        const enhancedAiPrompts = {
          ...promptData, // Include existing prompt data from context
          customPrompt: {
            id: selectedAiPrompt.id,
            name: selectedAiPrompt.name,
            content: selectedAiPrompt.content,
            category: selectedAiPrompt.category,
            variables: selectedAiPrompt.variables || [],
            isSelected: true,
          },
          selectedPromptId: selectedAiPrompt.id,
          useCustomPrompt: true,
        };

        // Store the enhanced prompts for the generation process
        (window as any).enhancedAiPrompts = enhancedAiPrompts;

        toast({
          title: "Using Custom AI Prompt",
          description: `Generating document with "${selectedAiPrompt.name}" prompt`,
        });
      } else {
        // Clear any previously stored prompts
        delete (window as any).enhancedAiPrompts;
      }

      await handleGenerate(preserveOriginalContent);
    } catch (error) {
      console.error("Generation failed:", error);
      throw error;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold">Document Overview</h2>
          <p className="text-muted-foreground">
            Review all information and generate your scoping document
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Information Summary */}
        <div className="space-y-4">
          {/* Client Information */}
          <div className="border rounded-md p-4">
            <h4 className="font-medium text-base mb-2">Client Information</h4>
            {selectedClient?.name || clientData.name ? (
              <div className="grid grid-cols-1 gap-2 text-sm">
                {(selectedClient?.name || clientData.name) && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Client Name:</span>
                    <span>{selectedClient?.name || clientData.name}</span>
                  </div>
                )}
                {(selectedClient?.contactPerson ||
                  clientData.contactPerson) && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Contact Person:
                    </span>
                    <span>
                      {selectedClient?.contactPerson ||
                        clientData.contactPerson}
                    </span>
                  </div>
                )}
                {(selectedClient?.email || clientData.email) && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email:</span>
                    <span>{selectedClient?.email || clientData.email}</span>
                  </div>
                )}
                {(selectedClient?.phone || clientData.phone) && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone:</span>
                    <span>{selectedClient?.phone || clientData.phone}</span>
                  </div>
                )}
                {(selectedClient?.industry || clientData.industry) && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Industry:</span>
                    <span>
                      {selectedClient?.industry || clientData.industry}
                    </span>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No client information provided
              </p>
            )}
          </div>

          {/* Document Type */}
          <div className="border rounded-md p-4">
            <h4 className="font-medium text-base mb-2">Document Type</h4>
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{templateInfo.name}</span>
            </div>
          </div>

          {/* Project Information */}
          <div className="border rounded-md p-4">
            <h4 className="font-medium text-base mb-2">Project Information</h4>
            {projectInfo.title ||
            projectInfo.description ||
            projectInfo.startDate ||
            projectInfo.endDate ||
            projectInfo.budget ? (
              <div className="grid grid-cols-1 gap-2 text-sm">
                {projectInfo.title && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Title:</span>
                    <span>{projectInfo.title}</span>
                  </div>
                )}
                {projectInfo.description && (
                  <div>
                    <span className="text-muted-foreground">Description:</span>
                    <p className="text-xs mt-1 bg-gray-50 p-2 rounded max-h-20 overflow-y-auto">
                      {projectInfo.description}
                    </p>
                  </div>
                )}
                {projectInfo.startDate && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Start Date:</span>
                    <span>{projectInfo.startDate}</span>
                  </div>
                )}
                {projectInfo.endDate && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">End Date:</span>
                    <span>{projectInfo.endDate}</span>
                  </div>
                )}
                {projectInfo.budget && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Budget:</span>
                    <span className="capitalize">{projectInfo.budget}</span>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No project information provided
              </p>
            )}
          </div>

          {/* Requirements */}
          <div className="border rounded-md p-4">
            <h4 className="font-medium text-base mb-2">Requirements</h4>
            {requirementsText ? (
              <div className="text-sm">
                <p className="text-muted-foreground mb-1">
                  Method: Manual Entry
                </p>
                <div className="bg-gray-50 p-2 rounded max-h-32 overflow-y-auto">
                  <p className="text-xs">{requirementsText}</p>
                </div>
              </div>
            ) : selectedKnowledgeDocuments &&
              selectedKnowledgeDocuments.length > 0 ? (
              <div className="text-sm">
                <p className="text-muted-foreground mb-1">
                  Method: Knowledge Base Documents
                </p>
                <div className="bg-gray-50 p-2 rounded max-h-32 overflow-y-auto">
                  <p className="text-xs font-medium text-primary">
                    {selectedKnowledgeDocuments.length} document(s) selected
                    from knowledge base
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    These documents will be retrieved from Pinecone and used as
                    context for generating requirements and content. The AI will
                    analyze the document content to provide specific, relevant
                    details for each section.
                  </p>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                {requirementsOption === "upload"
                  ? "No files uploaded"
                  : "No requirements provided"}
              </p>
            )}
          </div>

          {/* AI Prompt Selection */}
          <div className="border rounded-md p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-base flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-primary" />
                AI Prompt Template
              </h4>
              <Popover
                open={localPromptSearchOpen}
                onOpenChange={setLocalPromptSearchOpen}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={isLoadingPrompts}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    {isLoadingPrompts ? "Loading..." : "Select Prompt"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96 p-0" align="end">
                  <Command>
                    <CommandInput placeholder="Search AI prompts..." />
                    <CommandEmpty>No prompts found.</CommandEmpty>
                    <CommandGroup>
                      <ScrollArea className="max-h-64">
                        {availablePrompts.map((prompt) => (
                          <CommandItem
                            key={prompt.id}
                            onSelect={() => handlePromptSelection(prompt)}
                            className="flex flex-col items-start p-3 cursor-pointer"
                          >
                            <div className="flex items-center justify-between w-full mb-1">
                              <span className="font-medium text-sm">
                                {prompt.name}
                              </span>
                              <Badge
                                className={getCategoryColor(
                                  prompt.category || "general"
                                )}
                              >
                                {prompt.category || "general"}
                              </Badge>
                            </div>
                            {prompt.description && (
                              <p className="text-xs text-muted-foreground mb-2">
                                {prompt.description}
                              </p>
                            )}
                            <div className="text-xs text-muted-foreground">
                              Used {prompt.usage_count || 0} times
                            </div>
                          </CommandItem>
                        ))}
                      </ScrollArea>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {selectedAiPrompt ? (
              <div className="text-sm">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge
                      className={getCategoryColor(
                        selectedAiPrompt.category || "general"
                      )}
                    >
                      {selectedAiPrompt.category || "general"}
                    </Badge>
                    <span className="font-medium">{selectedAiPrompt.name}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearSelectedPrompt}
                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                {selectedAiPrompt.description && (
                  <p className="text-xs text-muted-foreground mb-2">
                    {selectedAiPrompt.description}
                  </p>
                )}
                <div className="bg-gray-50 p-2 rounded max-h-24 overflow-y-auto">
                  <p className="text-xs whitespace-pre-wrap">
                    {selectedAiPrompt.content.length > 200
                      ? `${selectedAiPrompt.content.substring(0, 200)}...`
                      : selectedAiPrompt.content}
                  </p>
                </div>
                {selectedAiPrompt.variables &&
                  selectedAiPrompt.variables.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedAiPrompt.variables.map((variable, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs"
                        >
                          {variable}
                        </Badge>
                      ))}
                    </div>
                  )}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                <p className="mb-2">No specific AI prompt selected</p>
                <p className="text-xs">
                  The system will use default prompts for document generation.
                  Select a custom prompt template to improve generation quality
                  for specific use cases.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Template Preview */}
        <div className="space-y-4">
          <div className="border rounded-md">
            <div className="p-4 border-b">
              <h4 className="font-medium text-base">Template Preview</h4>
            </div>
            <div className="p-4">
              <InlineDocumentPreview
                referenceDocument={referenceDocument}
                selectedTemplate={selectedTemplate}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Generation Modal */}
      {/* <Dialog open={isGenerating} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              Generating Document
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              {generationStatus}
            </div>
            <Progress value={generationProgress} className="w-full" />
            <div className="text-right text-sm text-muted-foreground">
              {generationProgress}%
            </div>
          </div>
        </DialogContent>
      </Dialog> */}

      <div className="flex justify-between mt-8">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <div className="flex gap-2">
          {selectedTemplate === "custom" && (
            <Button
              onClick={() => handleGenerateWithPrompt(true)}
              disabled={isGenerating || (isNewPrompt && !!promptData.name)}
              variant="outline"
              className="gap-2"
            >
              <FileText className="h-4 w-4" />
              Generate with Original Content
            </Button>
          )}
          <Button
            onClick={async () => {
              try {
                await handleGenerateWithPrompt(false);
              } catch (error) {
                console.error("Generation failed:", error);
              }
            }}
            disabled={isGenerating}
            className="gap-2"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4" />
                Generate Document
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DocumentOverviewStep;
