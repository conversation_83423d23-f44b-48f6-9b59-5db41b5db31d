export interface Block {
  id: string;
  type: 'text' | 'header' | 'list' | 'image';
  content: string;
  level?: number;
  items?: string[];
  caption?: string;
}

export interface Section {
  id: string;
  title: string;
  content: string;
  blocks: Block[];
  status: 'draft' | 'generating' | 'completed' | 'error';
  order?: number;
}

export interface GeneratedDocumentData {
  title: string;
  client: string;
  type: string;
  sections: Section[];
  theme?: DocumentTheme;
}

export interface AIPromptData {
  styleGuidance: string;
  contentFocus: string;
  additionalInstructions: string;
  templateId?: string;
}

export interface AIRegenerationOption {
  id: string;
  label: string;
  description: string;
  icon: JSX.Element;
}

export interface ScopingDocument {
  id: string;
  title: string;
  createdAt: string;
  status: string;
  clientName?: string;
}

export interface PageData {
  id: string;
  content: Section[];
}

export interface DocumentHeader {
  enabled: boolean;
  type: 'simple' | 'logo' | 'custom';
  text?: string;
  includePageNumbers: boolean;
}

export interface DocumentFooter {
  enabled: boolean;
  type: 'simple' | 'contact' | 'custom';
  text?: string;
  includePageNumbers: boolean;
}

export interface DocumentTheme {
  headingColor: string;
  subheadingColor: string;
  textColor: string;
  backgroundColor: string;
  accentColor: string;
  fontFamily: string;
  headingSize: 'small' | 'medium' | 'large';
  textSize: 'small' | 'medium' | 'large';
  header?: DocumentHeader;
  footer?: DocumentFooter;
  logo?: string;
  showCoverPage?: boolean;
} 