import { User } from "./user";

export enum SubscriptionStatus {
  ACTIVE = "active",
  TRIALING = "trialing",
  PAST_DUE = "past_due",
  CANCELED = "canceled",
  INCOMPLETE = "incomplete",
  INCOMPLETE_EXPIRED = "incomplete_expired",
  UNPAID = "unpaid",
}

export enum BillingCycle {
  MONTHLY = "monthly",
  YEARLY = "yearly",
}

export enum OwnerType {
  USER = "user",
  ORGANIZATION = "organisation",
}

export interface PlanTier {
  id: string;
  name: string;
  description?: string;
  tier_level: number;
  is_active: boolean;
  is_public: boolean;
  monthly_price_id?: string;
  yearly_price_id?: string;
  features: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface ResourceType {
  id: string;
  code: string;
  name: string;
  description?: string;
  unit_label: string;
  created_at: string;
}

export interface PlanResourceLimit {
  id: string;
  plan_id: string;
  resource_type_id: string;
  base_limit: number;
  created_at: string;
  updated_at: string;

  // Joined data
  resource_type?: ResourceType;
}

export interface SubscriptionRecord {
  id: string;
  owner_id: string;
  owner_type: OwnerType;
  plan_id: string;
  status: SubscriptionStatus;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  current_period_start?: string;
  current_period_end?: string;
  cancel_at_period_end: boolean;
  trial_start?: string;
  trial_end?: string;
  billing_cycle: BillingCycle;
  created_at: string;
  updated_at: string;

  // Joined data
  plan?: PlanTier;
}

export interface ResourceAddition {
  id: string;
  subscription_id: string;
  resource_type_id: string;
  additional_amount: number;
  created_at: string;
  updated_at: string;

  // Joined data
  resource_type?: ResourceType;
}

export interface ResourceUsage {
  id: string;
  subscription_id: string;
  resource_type_id: string;
  current_usage: number;
  last_updated: string;

  // Joined data
  resource_type?: ResourceType;
  limit?: number;
  percentage_used?: number;
}

export interface ResourceAddonPack {
  id: string;
  resource_type_id: string;
  name: string;
  description?: string;
  quantity: number;
  monthly_price_id?: string;
  yearly_price_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;

  // Joined data
  resource_type?: ResourceType;
}

export interface SubscriptionAddon {
  id: string;
  subscription_id: string;
  addon_pack_id: string;
  stripe_item_id?: string;
  quantity: number;
  is_active: boolean;
  start_date: string;
  created_at: string;

  // Joined data
  addon_pack?: ResourceAddonPack;
}

export interface BillingContact {
  id: string;
  subscription_id: string;
  email: string;
  full_name?: string;
  phone?: string;
  billing_address?: Record<string, any>;
  tax_id?: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentMethod {
  id: string;
  subscription_id: string;
  stripe_payment_method_id: string;
  card_brand?: string;
  card_last4?: string;
  card_exp_month?: number;
  card_exp_year?: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface Invoice {
  id: string;
  subscription_id: string;
  stripe_invoice_id: string;
  amount: number;
  currency: string;
  status: string;
  invoice_date: string;
  due_date?: string;
  paid_date?: string;
  invoice_pdf_url?: string;
  created_at: string;
}

export interface SubscriptionUser {
  id: string;
  subscription_id: string;
  user_id: string;
  added_at: string;
  added_by: string;
  status: "active" | "suspended" | "pending";

  // Joined data
  user?: User;
}

export interface SubscriptionSummary {
  subscription_id: string;
  plan_id: string;
  resources: ResourceSummary[];
}

export interface ResourceSummary {
  resource_type_id: string;
  code: string;
  name: string;
  unit_label: string;
  base_limit: number;
  additional: number;
  total_limit: number;
  current_usage: number;
  percentage_used: number;
  available: number;
}
