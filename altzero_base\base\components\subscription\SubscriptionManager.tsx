import React, { useState } from "react";
import { useSubscription } from "../../contextapi/SubscriptionContext";
import { SubscriptionInfo } from "./SubscriptionInfo";
import { ResourceUsage } from "./ResourceUsage";
import { PlanSelector } from "./PlanSelector";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../components/ui/tabs";
import { BillingCycle, SubscriptionStatus } from "../../types/subscription";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { AlertTriangle } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Label } from "../ui/label";
import { toast } from "../../hooks/use-toast";

export interface SubscriptionManagerProps {
  className?: string;
}

export function SubscriptionManager({ className }: SubscriptionManagerProps) {
  const {
    userSubscription,
    isLoading,
    isChangingBillingCycle,
    isCanceling,
    changeCycle,
    cancelSub,
  } = useSubscription();

  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [changeCycleDialogOpen, setChangeCycleDialogOpen] = useState(false);
  const [selectedBillingCycle, setSelectedBillingCycle] =
    useState<BillingCycle>(
      userSubscription?.billing_cycle || BillingCycle.MONTHLY
    );

  const handleBillingCycleChange = async () => {
    if (!userSubscription) return;

    try {
      await changeCycle(userSubscription.id, selectedBillingCycle);
      setChangeCycleDialogOpen(false);
      toast({
        title: "Billing cycle changed",
        description: "Your subscription billing cycle has been updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to change billing cycle. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCancelSubscription = async (atPeriodEnd: boolean = true) => {
    if (!userSubscription) return;

    try {
      await cancelSub(userSubscription.id, atPeriodEnd);
      setCancelDialogOpen(false);
      toast({
        title: "Subscription canceled",
        description: atPeriodEnd
          ? "Your subscription will end at the end of the current billing period."
          : "Your subscription has been canceled immediately.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel subscription. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">
          Subscription Management
        </h1>
        <p className="text-muted-foreground">
          Manage your subscription and resource usage
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <SubscriptionInfo subscription={userSubscription} />
            <ResourceUsage />
          </div>
        </TabsContent>

        <TabsContent value="plans">
          <PlanSelector subscription={userSubscription} />
        </TabsContent>

        <TabsContent value="resources">
          <ResourceUsage className="mb-6" />

          <Card>
            <CardHeader>
              <CardTitle>Resource Add-ons</CardTitle>
              <CardDescription>
                Purchase additional resource capacity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Need more resources? You can purchase add-ons to increase your
                limits.
              </p>
              {/* Add-on content would go here */}
            </CardContent>
            <CardFooter>
              <Button variant="outline">View Available Add-ons</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Billing Cycle</CardTitle>
                <CardDescription>Manage your billing frequency</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  You are currently on a{" "}
                  {userSubscription?.billing_cycle === BillingCycle.MONTHLY
                    ? "monthly"
                    : "yearly"}{" "}
                  billing cycle.
                </p>
                <p className="text-sm text-muted-foreground mb-4">
                  {userSubscription?.billing_cycle === BillingCycle.MONTHLY
                    ? "Switch to yearly billing to save 15% on your subscription."
                    : "You are already on yearly billing, getting the best available rate."}
                </p>
              </CardContent>
              <CardFooter>
                {userSubscription?.billing_cycle === BillingCycle.MONTHLY && (
                  <Dialog
                    open={changeCycleDialogOpen}
                    onOpenChange={setChangeCycleDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline">
                        Switch to Yearly Billing
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Change Billing Cycle</DialogTitle>
                        <DialogDescription>
                          You're about to change your billing cycle. This will
                          take effect at your next renewal.
                        </DialogDescription>
                      </DialogHeader>
                      <RadioGroup
                        defaultValue={selectedBillingCycle}
                        onValueChange={(value) =>
                          setSelectedBillingCycle(value as BillingCycle)
                        }
                        className="space-y-3 my-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={BillingCycle.MONTHLY}
                            id="monthly"
                          />
                          <Label htmlFor="monthly">Monthly Billing</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={BillingCycle.YEARLY}
                            id="yearly"
                          />
                          <Label htmlFor="yearly">
                            Yearly Billing (Save 15%)
                          </Label>
                        </div>
                      </RadioGroup>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setChangeCycleDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleBillingCycleChange}
                          disabled={isChangingBillingCycle}
                        >
                          {isChangingBillingCycle ? (
                            <>
                              <span className="animate-spin mr-2">⟳</span>
                              Updating...
                            </>
                          ) : (
                            "Confirm Change"
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cancel Subscription</CardTitle>
                <CardDescription>
                  Cancel your current subscription
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  {userSubscription?.cancel_at_period_end
                    ? "Your subscription is already scheduled to be canceled at the end of the current billing period."
                    : "You can cancel your subscription at any time. You will still have access to your subscription until the end of your current billing period."}
                </p>
                {userSubscription?.cancel_at_period_end && (
                  <p className="text-sm font-medium">
                    Your subscription will end on:{" "}
                    {new Date(
                      userSubscription.current_period_end || ""
                    ).toLocaleDateString()}
                  </p>
                )}
              </CardContent>
              <CardFooter>
                {!userSubscription?.cancel_at_period_end &&
                  userSubscription?.status !== SubscriptionStatus.CANCELED && (
                    <Dialog
                      open={cancelDialogOpen}
                      onOpenChange={setCancelDialogOpen}
                    >
                      <DialogTrigger asChild>
                        <Button variant="destructive">
                          Cancel Subscription
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-destructive" />
                            Cancel Subscription
                          </DialogTitle>
                          <DialogDescription>
                            Are you sure you want to cancel your subscription?
                          </DialogDescription>
                        </DialogHeader>
                        <div className="py-4">
                          <p className="mb-4">
                            If you cancel, you will still have access to your
                            subscription until the end of your current billing
                            period.
                          </p>
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => setCancelDialogOpen(false)}
                          >
                            No, Keep My Subscription
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={() => handleCancelSubscription(true)}
                            disabled={isCanceling}
                          >
                            {isCanceling ? (
                              <>
                                <span className="animate-spin mr-2">⟳</span>
                                Canceling...
                              </>
                            ) : (
                              "Yes, Cancel My Subscription"
                            )}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  )}
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
