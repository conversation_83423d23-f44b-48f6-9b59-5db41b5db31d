import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Section as BaseSection, ScopingDocument, SectionStatus, Block, BlockType } from '../types/scoping';

// Extended Section interface with additional properties
interface Section extends BaseSection {
  description?: string; // Add description property
}

// Extend the ScopingDocument type with additional properties needed for API calls
interface ExtendedScopingDocument extends ScopingDocument {
  documentId?: string;  // For uploaded document reference
  clientInfo?: any;     // Additional client information
  scopingInfo?: any;    // Additional scoping information
  templateInfo?: {      // Template information
    promptTemplate?: {
      id: string;
      name: string;
      content: string;
    };
    scopeTemplate?: {
      id: string;
      name: string;
      description?: string;
    };
    sectionTemplate?: {
      id: string;
      name: string;
      description?: string;
    };
  };
}

// Mock API service - replace with actual API calls
const apiService = {
  createDocument: async (data: Partial<ScopingDocument>): Promise<ScopingDocument> => {
    // This would be an API call to create document in Supabase
    const id = uuidv4();
    const now = new Date();
    
    return {
      id,
      userId: 'current-user-id', // This would come from auth
      name: data.name || 'Untitled Document',
      projectName: data.projectName || 'Untitled Project',
      clientName: data.clientName || 'Unknown Client',
      baseContent: {
        description: data.baseContent?.description || '',
        blocks: data.baseContent?.blocks || []
      },
      sections: data.sections || [],
      createdAt: now,
      updatedAt: now
    };
  },
  
  generateBaseDocument: async (projectInfo: any): Promise<ScopingDocument> => {
    // This would call the backend to generate the base document
    const id = uuidv4();
    const now = new Date();
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Sample sections based on project type
    const sectionTemplates = [
      { title: 'Project Overview', order: 0 },
      { title: 'Goals and Objectives', order: 1 },
      { title: 'Requirements', order: 2 },
      { title: 'Technical Approach', order: 3 },
      { title: 'Timeline and Milestones', order: 4 },
      { title: 'Budget Estimation', order: 5 },
      { title: 'Risk Assessment', order: 6 },
      { title: 'Next Steps', order: 7 }
    ];
    
    // Create initial sections with pending status
    const sections: Section[] = sectionTemplates.map((template, index) => ({
      id: uuidv4(),
      title: template.title,
      content: '',
      status: 'pending' as SectionStatus,
      order: template.order,
      updatedAt: now
    }));
    
    // Generate introduction - this would be done by AI in the real implementation
    const introduction = `# Introduction\n\nThis document outlines the scope of work for the ${projectInfo.projectName} project for ${projectInfo.clientName}. The purpose of this document is to define the project's goals, requirements, approach, timeline, and budget considerations.`;
    
    return {
      id,
      userId: 'current-user-id',
      name: `${projectInfo.projectName} - Scope`,
      projectName: projectInfo.projectName,
      clientName: projectInfo.clientName,
      baseContent: {
        description: introduction,
        blocks: []
      },
      sections,
      createdAt: now,
      updatedAt: now
    };
  },
  
  generateSection: async (documentId: string, sectionId: string, context: any): Promise<Section> => {
    // This would call the backend to generate content for a specific section
    
    // Simulate API delay (2-4 seconds)
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 2000));
    
    // Find the section to generate
    const section = context.sections.find((s: Section) => s.id === sectionId);
    if (!section) throw new Error('Section not found');
    
    // Generate content based on section title
    let content = '';
    
    switch (section.title) {
      case 'Project Overview':
        content = `## Project Overview\n\nThe ${context.projectName} project aims to [provide brief description]. This initiative is being undertaken for ${context.clientName} to address [specific needs or challenges].\n\nThe project will involve [key components or activities] and will be delivered using [methodology or approach].`;
        break;
      case 'Goals and Objectives':
        content = `## Goals and Objectives\n\n### Primary Goals\n- Develop a [specific solution/product/service]\n- Enable [specific capability or outcome]\n- Improve [specific metric or aspect]\n\n### Success Criteria\n- [Measurable outcome 1]\n- [Measurable outcome 2]\n- [Measurable outcome 3]`;
        break;
      case 'Requirements':
        content = `## Requirements\n\n### Functional Requirements\n- [Requirement 1]\n- [Requirement 2]\n- [Requirement 3]\n\n### Technical Requirements\n- [Technical spec 1]\n- [Technical spec 2]\n- [Technical spec 3]\n\n### Constraints\n- [Constraint 1]\n- [Constraint 2]`;
        break;
      default:
        content = `## ${section.title}\n\nThis section covers the ${section.title.toLowerCase()} aspects of the ${context.projectName} project.\n\n[Content to be generated based on project specifics]`;
    }
    
    return {
      ...section,
      content,
      status: 'completed' as SectionStatus,
      updatedAt: new Date()
    };
  },
  
  updateDocument: async (documentId: string, updates: Partial<ScopingDocument>): Promise<ScopingDocument> => {
    // This would update the document in Supabase
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return a mock response - in reality, this would return the updated document from the API
    return {
      id: documentId,
      ...updates,
      updatedAt: new Date()
    } as ScopingDocument;
  },
  
  updateSection: async (documentId: string, sectionId: string, updates: Partial<Section>): Promise<Section> => {
    // This would update a specific section in Supabase
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return a mock response
    return {
      id: sectionId,
      ...updates,
      updatedAt: new Date()
    } as Section;
  },
  
  exportDocument: async (documentId: string): Promise<string> => {
    // This would generate a PDF or other export format via the API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return a mock download URL
    return `https://example.com/documents/${documentId}/download`;
  }
};

// API configuration (would come from environment variables in production)
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';
const API_KEY = import.meta.env.VITE_API_KEY || '';

// Log API configuration on initialization
console.log('Progressive scoping hook initialized with API config:', { 
  API_URL, 
  hasApiKey: !!API_KEY,
  useEnvVars: !!import.meta.env.VITE_API_URL
});

// Add callback type
type GenerationCallback = (event: any) => void;

// Add error interface
interface GenerationError {
  message: string;
  details?: any;
  timestamp: string;
}

export interface UseProgressiveScopingReturn {
  document: ExtendedScopingDocument | null;
  sections: Section[];
  activeSectionId: string | null;
  setActiveSectionId: (sectionId: string | null) => void;
  loading: boolean;
  error: string | null;
  messages: string[];
  createDocument: (projectInfo: any) => Promise<void>;
  generateSection: (sectionId: string, callback?: GenerationCallback) => Promise<void>;
  generateAllSections: () => Promise<void>;
  updateDocument: (updates: Partial<ExtendedScopingDocument>) => Promise<void>;
  updateSection: (sectionId: string, updates: Partial<Section>) => Promise<void>;
  updateSectionContent: (sectionId: string, content: string) => Promise<void>;
  reorderSections: (sections: Section[]) => Promise<void>;
  exportDocument: () => Promise<string>;
  resetDocument: () => void;
}

export const useProgressiveScoping = (documentId?: string): UseProgressiveScopingReturn => {
  const [document, setDocument] = useState<ExtendedScopingDocument | null>(null);
  const [loading, setLoading] = useState<boolean>(true); // Start with loading state
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<string[]>([]);
  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  
  // Load document by ID if provided
  useEffect(() => {
    if (documentId) {
      // Would normally load from API here
      // For now, create a mock document with the ID
      const mockDocument: ExtendedScopingDocument = {
        id: documentId,
        userId: 'current-user-id',
        name: 'Loaded Document',
        projectName: 'Loaded Project',
        clientName: 'Client Name',
        documentId: documentId, // Store the documentId
        baseContent: {
          description: 'This is a loaded document',
          blocks: []
        },
        sections: [
          {
            id: `section-${Date.now()}-1`,
            title: 'Introduction',
            content: '# Introduction\n\nThis is the introduction section.',
            status: 'completed' as SectionStatus,
            order: 0,
            updatedAt: new Date()
          },
          {
            id: `section-${Date.now()}-2`,
            title: 'Requirements',
            content: '',
            status: 'pending' as SectionStatus,
            order: 1,
            updatedAt: new Date()
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      setDocument(mockDocument);
      setSections(mockDocument.sections);
      setLoading(false);
    } else {
      // No document ID, so we're not loading anything
      setLoading(false);
    }
  }, [documentId]);
  
  // Clear error when component unmounts or on certain actions
  useEffect(() => {
    return () => {
      setError(null);
    };
  }, []);
  
  // Create document with sections from chosen template
  const createDocument = async (data: any) => {
    setLoading(true);
    setError(null);
    setMessages(['Creating document...']);
    
    try {
      // Create a document with the provided sections from template
      const newDocument: ExtendedScopingDocument = {
        id: 'doc_' + Math.random().toString(36).substring(2, 15),
        userId: 'current-user-id',
        name: data.name || 'New Document',
        projectName: data.projectName || 'Untitled Project',
        clientName: data.clientName || 'Unknown Client',
        baseContent: {
          description: data.baseContent?.description || 'No description provided.',
          blocks: convertContentToBlocks(data.baseContent?.description || '')
        },
        sections: data.sections.map((s: any, index: number) => ({
          ...s,
          id: s.id || `section-${index}`,
          status: 'pending' as SectionStatus,
          order: index,
          updatedAt: new Date(),
          blocks: convertContentToBlocks(s.content || '')
        })),
        clientInfo: data.clientInfo || null,
        scopingInfo: data.scopingInfo || null,
        createdAt: new Date(),
        updatedAt: new Date(),
        // Store the uploaded document ID for API calls
        documentId: data.documentId || null
      };
      
      setDocument(newDocument);
      setSections(newDocument.sections);
      setMessages(prev => [...prev, 'Document created successfully']);
      setLoading(false);
    } catch (err: any) {
      console.error('Error creating document:', err);
      setError(err.message || 'Failed to create document');
      setLoading(false);
      throw err;
    }
  };
  
  // Update generateSection to accept a callback
  const generateSection = async (sectionId: string, callback?: GenerationCallback) => {
    if (!document) return;
    
    console.log(`Starting section generation for: ${sectionId}`);
    
    try {
      // Find the section
      const section = document.sections.find(s => s.id === sectionId);
      if (!section) {
        console.error(`Section ${sectionId} not found`);
        throw new Error(`Section ${sectionId} not found`);
      }
      
      console.log(`Generating section: ${section.title}`);
      
      // Update section status and clear any previous errors
      updateSection(sectionId, { 
        status: 'generating',
        error: undefined 
      });
      
      // Prepare the API payload
      const payload = {
        clientInfo: document.clientInfo || {
          name: document.clientName || 'Unknown Client',
          industry: 'Technology',
          company: document.clientName || 'Unknown Client',
          contactPerson: 'Project Manager',
          email: '<EMAIL>'
        },
        scopingInfo: {
          projectName: document.projectName || 'Untitled Project',
          projectDescription: document.baseContent?.description || 'No description provided',
          timeline: '2-3 months',
          budget: '$10,000 - $20,000',
          goals: ['Complete project successfully', 'Meet all requirements']
        },
        promptTemplate: document.templateInfo?.promptTemplate || {
          id: 'default',
          name: 'Default Template',
          content: 'You are an expert AI consultant tasked with creating professional scoping documents.'
        },
        sections: [{
          id: sectionId,
          title: section.title,
          description: section.description || `Generate content for ${section.title}`
        }]
      };
      
      console.log(`API endpoint: ${API_URL}/scoping/stream`);
      console.log(`Sending payload for section ${section.title}:`, JSON.stringify(payload, null, 2));
      
      try {
        // Try making the API call first
        const response = await fetch(`${API_URL}/scoping/stream`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': API_KEY
          },
          body: JSON.stringify(payload)
        });
        
        console.log(`API response status: ${response.status}`);
        
        if (!response.ok) {
          throw new Error(`API call failed with status: ${response.status} ${response.statusText}`);
        }
        
        // Process the stream
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Stream reader not available');
        }
        
        const decoder = new TextDecoder();
        let buffer = '';
        let contentBuffer = '';
        let done = false;
        
        // Add timeout for stream
        const startTime = Date.now();
        const timeoutDuration = 60000; // 60 seconds timeout
                
        try {
          while (!done) {
            // Check if we've exceeded the timeout
            if (Date.now() - startTime > timeoutDuration) {
              console.log(`Generation timed out after ${timeoutDuration / 1000} seconds`);
              done = true;
              
              // If we have content, mark as completed despite timeout
              if (contentBuffer) {
                updateSection(sectionId, {
                  status: 'completed',
                  content: contentBuffer,
                  updatedAt: new Date()
                });
              } else {
                throw new Error(`Section generation timed out after ${timeoutDuration / 1000} seconds`);
              }
              
              break;
            }
            
            const { done: streamDone, value } = await reader.read();
            
            if (streamDone) {
              done = true;
              console.log("Stream completed naturally");
              
              // If we have content but no completion event was received, 
              // mark as completed anyway
              if (contentBuffer) {
                updateSection(sectionId, {
                  status: 'completed',
                  content: contentBuffer,
                  updatedAt: new Date()
                });
              }
              break;
            }
            
            buffer += decoder.decode(value, { stream: true });
            
            // Process events in buffer
            const lines = buffer.split('\n\n');
            buffer = lines.pop() || '';
            
            let eventsProcessed = false;
            
            for (const line of lines) {
              if (!line.trim()) continue;
              
              eventsProcessed = true;
              const eventMatch = line.match(/^event: (.+)$/m);
              const dataMatch = line.match(/^data: (.+)$/m);
              
              if (eventMatch && dataMatch) {
                const eventType = eventMatch[1];
                let eventData;
                
                try {
                  eventData = JSON.parse(dataMatch[1]);
                  
                  // Invoke callback if provided
                  if (callback) {
                    callback({
                      type: eventType,
                      data: eventData
                    });
                  }
                  
                  // Process event based on type
                  switch (eventType) {
                    case 'section':
                      // Update contentBuffer with the latest chunk
                      if (eventData.section && eventData.section.content) {
                        contentBuffer = eventData.section.content; // Replace with complete content
                      } else if (eventData.content) {
                        contentBuffer += eventData.content; // Append content chunk
                      }
                      
                      // Update section with in-progress content
                      if (contentBuffer) {
                        updateSection(sectionId, {
                          content: contentBuffer,
                          status: 'generating' // Keep status as generating
                        });
                      }
                      break;
                      
                    case 'completed':
                      // Handle completion
                      done = true;
                      
                      // If there's content in the completed event, use it
                      if (eventData.result && eventData.result.sections) {
                        const sectionData = eventData.result.sections.find((s: any) => s.title === section.title);
                        if (sectionData && sectionData.content) {
                          contentBuffer = sectionData.content;
                        }
                      } else if (eventData.scoping && eventData.scoping.sections) {
                        const sectionData = eventData.scoping.sections.find((s: any) => s.title === section.title);
                        if (sectionData && sectionData.content) {
                          contentBuffer = sectionData.content;
                        }
                      }
                      
                      // Update section with final content
                      updateSection(sectionId, {
                        status: 'completed',
                        content: contentBuffer,
                        updatedAt: new Date()
                      });
                      break;
                      
                    case 'error':
                      // Handle error in the stream
                      console.error('Backend returned an error event:', eventData);
                      throw new Error(eventData.message || eventData.error || 'Error in stream processing');
                  }
                } catch (e) {
                  console.error(`Error processing event data:`, e, 'Raw data:', dataMatch[1]);
                }
              }
            }
            
            // If no events were processed in this chunk, but we have content,
            // update the section to show progress
            if (!eventsProcessed && contentBuffer) {
              updateSection(sectionId, {
                content: contentBuffer,
                status: 'generating'
              });
            }
          }
        } catch (streamError) {
          console.error(`Stream processing error:`, streamError);
          reader.cancel();
          
          // If we have content despite the error, mark as completed
          if (contentBuffer) {
            console.log(`Stream ended with error but content was generated, updating section ${sectionId}`);
            updateSection(sectionId, {
              status: 'completed',
              content: contentBuffer,
              updatedAt: new Date()
            });
          } else {
            // Otherwise, propagate the error
            throw streamError;
          }
        } finally {
          reader.cancel();
        }
      } catch (apiError) {
        console.warn('API call failed, falling back to local generation:', apiError);
        
        // If API call fails, fall back to local mock content generation
        // Generate mock content based on section title
        const mockContent = generateMockContent(section.title);
        
        // Simulate API delay (1-2 seconds)
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
        
        // Update section with mock content
        updateSection(sectionId, {
          status: 'completed',
          content: mockContent,
          updatedAt: new Date()
        });
        
        // Call the callback with mock events if provided
        if (callback) {
          callback({
            type: 'section',
            data: { content: mockContent }
          });
          
          callback({
            type: 'completed',
            data: { content: mockContent }
          });
        }
      }
    } catch (error) {
      // Enhanced error handling for overall generation errors
      const generationError: GenerationError = {
        message: error instanceof Error ? error.message : 'Failed to generate section content',
        details: { error },
        timestamp: new Date().toISOString()
      };
      
      console.error('Error generating section:', {
        sectionId,
        sectionTitle: document.sections.find(s => s.id === sectionId)?.title,
        error: generationError
      });
      
      updateSection(sectionId, {
        status: 'failed',
        error: generationError,
        updatedAt: new Date()
      });
      
      throw error;
    }
  };

  // Generate all sections sequentially
  const generateAllSections = async () => {
    if (!document || sections.length === 0) {
      throw new Error('No document or sections to generate');
    }
    
    // Filter for only pending sections
    const pendingSections = sections.filter(
      section => section.status === 'pending'
    );
    
    if (pendingSections.length === 0) {
      return; // Nothing to generate
    }
    
    setLoading(true);
    setError(null);
    
    try {
      setMessages([`Starting generation of ${pendingSections.length} sections...`]);
      
      // Process sections one by one to avoid context length issues
      for (let i = 0; i < pendingSections.length; i++) {
        const section = pendingSections[i];
        setMessages(prev => [...prev, `Generating section ${i+1}/${pendingSections.length}: ${section.title}`]);
        
        console.log(`Starting to generate section ${i+1}/${pendingSections.length}: ${section.title}`);
        
        // Update UI status before starting generation
        updateSection(section.id, { status: 'generating' });
        
        try {
          // Generate sections one by one
          await generateSection(section.id);
          console.log(`Successfully generated section: ${section.title}`);
        } catch (sectionError) {
          console.error(`Error generating section ${section.title}:`, sectionError);
          // Continue to next section even if one fails
        }
        
        // Small delay to allow UI to update between sections
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // Refresh section status from the actual sections array
      setSections(prevSections => {
        const updatedSections = [...prevSections];
        return updatedSections;
      });
      
      setMessages(prev => [...prev, 'All sections generated successfully!']);
      
    } catch (err: unknown) {
      console.error('Error generating all sections:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate sections');
      
    } finally {
      setLoading(false);
    }
  };
  
  // Update document properties
  const updateDocument = async (updates: Partial<ExtendedScopingDocument>) => {
    if (!document) {
      setError('No document loaded');
      return;
    }
    
    try {
      const updatedDocument = {
        ...document,
        ...updates,
        updatedAt: new Date()
      };
      
      setDocument(updatedDocument);
      
      // If sections were updated, sync them
      if (updates.sections) {
        setSections(updates.sections);
      }
    } catch (err: any) {
      console.error('Error updating document:', err);
      setError(err.message || 'Failed to update document');
    }
  };
  
  // Update a specific section
  const updateSection = async (sectionId: string, updates: Partial<Section>) => {
    if (!document) {
      setError('No document loaded');
      return;
    }
    
    try {
      const sectionIndex = sections.findIndex(s => s.id === sectionId);
      if (sectionIndex === -1) {
        throw new Error(`Section with ID ${sectionId} not found`);
      }
      
      const updatedSections = [...sections];
      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        ...updates,
        updatedAt: new Date()
      };
      
      setSections(updatedSections);
      
      setDocument({
        ...document,
        sections: updatedSections,
        updatedAt: new Date()
      });
    } catch (err: any) {
      console.error('Error updating section:', err);
      setError(err.message || 'Failed to update section');
    }
  };

  // Update section content specifically (convenience method)
  const updateSectionContent = async (sectionId: string, content: string) => {
    await updateSection(sectionId, { 
      content,
      blocks: convertContentToBlocks(content)
    });
  };
  
  // Reorder sections
  const reorderSections = async (updatedSections: Section[]) => {
    if (!document) {
      setError('No document loaded');
      return;
    }
    
    try {
      setSections(updatedSections);
      
      setDocument({
        ...document,
        sections: updatedSections,
        updatedAt: new Date()
      });
    } catch (err: any) {
      console.error('Error reordering sections:', err);
      setError(err.message || 'Failed to reorder sections');
    }
  };
  
  // Export document as PDF/Word
  const exportDocument = async (): Promise<string> => {
    if (!document) {
      throw new Error('No document loaded');
    }
    
    try {
      // Try to use the real export API
      try {
        console.log(`Exporting document ID: ${document.id} via API`);
        const response = await fetch(`${API_URL}/documents/${document.id}/export`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': API_KEY
          },
          body: JSON.stringify({
            format: 'pdf',
            documentId: document.documentId
          })
        });
        
        if (!response.ok) {
          throw new Error(`Export API call failed with status: ${response.status}`);
        }
        
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      } catch (apiError) {
        console.warn('Export API call failed, generating PDF locally:', apiError);
        
        // Generate a simple PDF from the document content
        return generateLocalPDF();
      }
    } catch (err: any) {
      console.error('Error exporting document:', err);
      setError(err.message || 'Failed to export document');
      throw err;
    }
  };
  
  // Helper function to generate a simple PDF locally
  const generateLocalPDF = (): string => {
    // Create HTML content from the document
    let htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${document?.projectName || 'Untitled Project'} - Scope Document</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; }
          h2 { color: #444; margin-top: 30px; }
          h3 { color: #555; }
          .section { margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
          .header { text-align: center; margin-bottom: 40px; }
          .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #777; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${document?.projectName || 'Untitled Project'}</h1>
          <p>Scope Document for ${document?.clientName || 'Client'}</p>
          <p>Generated on ${new Date().toLocaleDateString()}</p>
        </div>
    `;
    
    // Add base content/introduction
    if (document?.baseContent?.description) {
      htmlContent += `<div class="section">${document.baseContent.description}</div>`;
    }
    
    // Add each section
    if (sections && sections.length > 0) {
      // Sort sections by order
      const sortedSections = [...sections].sort((a, b) => a.order - b.order);
      
      for (const section of sortedSections) {
        if (section.content) {
          const formattedContent = section.content
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n\n/g, '</p><p>');
          
          htmlContent += `
            <div class="section">
              <h2>${section.title}</h2>
              ${formattedContent}
            </div>
          `;
        }
      }
    }
    
    // Close HTML
    htmlContent += `
        <div class="footer">
          <p>Generated by Scoping AI</p>
        </div>
      </body>
      </html>
    `;
    
    // Convert HTML to a blob
    const blob = new Blob([htmlContent], { type: 'text/html' });
    
    // Return object URL
    return URL.createObjectURL(blob);
  };
  
  // Reset document state
  const resetDocument = () => {
    setDocument(null);
    setSections([]);
    setActiveSectionId(null);
    setError(null);
    setLoading(false);
    setMessages([]);
  };

  // Helper function to generate mock content for sections
  const generateMockContent = (sectionTitle: string): string => {
    // Generate different content based on section title
    switch(sectionTitle.toLowerCase()) {
      case 'introduction':
        return `# Introduction\n\nThis document outlines the scope of work for the ${document?.projectName || 'project'}. It provides a comprehensive overview of the project requirements, deliverables, timeline, and budget considerations.\n\n## Project Overview\n\nThe ${document?.projectName || 'project'} aims to deliver a robust solution for ${document?.clientName || 'the client'} that meets their specific business needs while maintaining high standards of quality and performance.`;
      
      case 'requirements':
        return `# Requirements\n\n## Functional Requirements\n\n- User authentication and authorization\n- Data management and storage\n- Reporting and analytics\n- Integration with existing systems\n\n## Non-Functional Requirements\n\n- Performance: The system must respond to user interactions within 2 seconds\n- Scalability: Support for up to 1000 concurrent users\n- Security: Data encryption at rest and in transit\n- Reliability: 99.9% uptime during business hours`;
      
      case 'design & architecture':
        return `# Design & Architecture\n\n## System Architecture\n\nThe solution will follow a microservices architecture with the following components:\n\n1. Frontend layer built with React.js\n2. API Gateway for request routing and authentication\n3. Microservices for different business domains\n4. Data persistence layer with both SQL and NoSQL databases\n\n## User Interface Design\n\nThe UI design will follow material design principles with a clean, intuitive interface that emphasizes usability and accessibility.`;
      
      case 'development process':
        return `# Development Process\n\n## Methodology\n\nThe project will follow an Agile development methodology with two-week sprints. Each sprint will include planning, implementation, testing, and review phases.\n\n## Team Structure\n\n- Project Manager: Overall coordination and client communication\n- Tech Lead: Technical architecture and code quality\n- Frontend Developers: UI/UX implementation\n- Backend Developers: API and database implementation\n- QA Engineers: Testing and quality assurance`;
      
      case 'testing strategy':
        return `# Testing Strategy\n\n## Testing Levels\n\n- Unit Testing: Individual components and functions\n- Integration Testing: Interactions between components\n- End-to-End Testing: Complete user workflows\n- Performance Testing: System behavior under load\n\n## Test Automation\n\nAutomation will be implemented for unit tests, integration tests, and critical user workflows to ensure consistent quality throughout the development process.`;
      
      case 'delivery & deployment':
        return `# Delivery & Deployment\n\n## Deployment Strategy\n\nThe application will be deployed using a CI/CD pipeline with the following stages:\n\n1. Build and unit test\n2. Integration testing\n3. Staging deployment\n4. User acceptance testing\n5. Production deployment\n\n## Infrastructure\n\nThe application will be hosted in a cloud environment with automatic scaling capabilities to handle varying loads.`;
      
      default:
        return `# ${sectionTitle}\n\nThis section provides details about ${sectionTitle.toLowerCase()} for the ${document?.projectName || 'project'}.\n\n## Key Points\n\n- First important aspect of ${sectionTitle.toLowerCase()}\n- Second important consideration\n- Third critical element\n\n## Implementation Approach\n\nThe implementation will follow industry best practices and standards, ensuring high quality and maintainability.`;
    }
  };

  // Helper function to convert content to blocks
  const convertContentToBlocks = (content: string): Block[] => {
    // Simple conversion of markdown-like content to blocks
    const paragraphs = content.split(/\n\n+/);
    return paragraphs.map((para, index) => {
      // Detect block type based on content
      if (para.startsWith('# ')) {
        return {
          id: `block-${index}`,
          type: 'header' as BlockType,
          content: para.substring(2),
          level: 1
        };
      } else if (para.startsWith('## ')) {
        return {
          id: `block-${index}`,
          type: 'header' as BlockType,
          content: para.substring(3),
          level: 2
        };
      } else if (para.startsWith('### ')) {
        return {
          id: `block-${index}`,
          type: 'header' as BlockType,
          content: para.substring(4),
          level: 3
        };
      } else if (para.startsWith('> ')) {
        return {
          id: `block-${index}`,
          type: 'quote' as BlockType,
          content: para.substring(2)
        };
      } else if (para.startsWith('- ')) {
        // Convert bullet points to list
        const items = para.split('\n').map(item => item.substring(2));
        return {
          id: `block-${index}`,
          type: 'list' as BlockType,
          content: para,
          items
        };
      }
      
      // Default to text block
      return {
        id: `block-${index}`,
        type: 'text' as BlockType,
        content: para
      };
    });
  };
  
  return {
    document,
    sections,
    activeSectionId,
    setActiveSectionId,
    loading,
    error,
    messages,
    createDocument,
    generateSection,
    generateAllSections,
    updateDocument,
    updateSection,
    updateSectionContent,
    reorderSections,
    exportDocument,
    resetDocument
  };
};

export default useProgressiveScoping; 