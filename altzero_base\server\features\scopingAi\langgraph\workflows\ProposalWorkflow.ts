// =====================================================
// MAIN PROPOSAL WORKFLOW - SCOPINGAI LANGGRAPH
// =====================================================

import { BaseWorkflow } from "../core/BaseWorkflow";
import {
  ScopingAiWorkflowState,
  WorkflowConfig,
  ProposalGenerationInput,
} from "../types/WorkflowState";
import { ValidationNode } from "../nodes/ValidationNode";
import { KnowledgeRetrievalNode } from "../nodes/KnowledgeRetrievalNode";
import { ClientAnalysisNode } from "../nodes/ClientAnalysisNode";
import { ResearchAnalysisNode } from "../nodes/ResearchAnalysisNode";
import { ExecutiveSummaryNode } from "../nodes/ExecutiveSummaryNode";
import { SectionGenerationNode } from "../nodes/SectionGenerationNode";
import { QualityReviewNode } from "../nodes/QualityReviewNode";
import { FinalizationNode } from "../nodes/FinalizationNode";

export class ProposalWorkflow extends BaseWorkflow {
  private validationNode: ValidationNode;
  private knowledgeRetrievalNode: KnowledgeRetrievalNode;
  private clientAnalysisNode: ClientAnalysisNode;
  private researchAnalysisNode: ResearchAnalysisNode;
  private executiveSummaryNode: ExecutiveSummaryNode;
  private sectionGenerationNode: SectionGenerationNode;
  private qualityReviewNode: QualityReviewNode;
  private finalizationNode: FinalizationNode;
  private currentWorkflowId: string | null = null;

  constructor(config: WorkflowConfig) {
    super(config);

    // Initialize nodes
    this.validationNode = new ValidationNode();
    this.knowledgeRetrievalNode = new KnowledgeRetrievalNode();
    this.clientAnalysisNode = new ClientAnalysisNode();
    this.researchAnalysisNode = new ResearchAnalysisNode();
    this.executiveSummaryNode = new ExecutiveSummaryNode();
    this.sectionGenerationNode = new SectionGenerationNode();
    this.qualityReviewNode = new QualityReviewNode();
    this.finalizationNode = new FinalizationNode();

    // Initialize the workflow after nodes are created
    this.initializeWorkflow();
  }

  getWorkflowName(): string {
    return "ProposalWorkflow";
  }

  // Define all nodes in the workflow
  defineNodes(): void {
    // Register validation node
    this.registerNode(this.validationNode, {
      node_name: "validation",
      execution_order: 1,
      dependencies: [],
      optional: false,
      timeout_seconds: 30,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 64,
        cpu_cores: 1,
      },
    });

    // Register knowledge retrieval node
    this.registerNode(this.knowledgeRetrievalNode, {
      node_name: "knowledge_retrieval",
      execution_order: 2,
      dependencies: ["validation"],
      optional: false,
      timeout_seconds: 120,
      retry_attempts: 3,
      resource_requirements: {
        memory_mb: 128,
        cpu_cores: 1,
      },
    });

    // Register client analysis node
    this.registerNode(this.clientAnalysisNode, {
      node_name: "client_analysis",
      execution_order: 3,
      dependencies: ["knowledge_retrieval"],
      optional: true, // Can continue without detailed analysis
      timeout_seconds: 180,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 256,
        cpu_cores: 1,
      },
    });

    // Register research analysis node
    this.registerNode(this.researchAnalysisNode, {
      node_name: "research_analysis",
      execution_order: 4,
      dependencies: ["client_analysis"],
      optional: false,
      timeout_seconds: 240,
      retry_attempts: 3,
      resource_requirements: {
        memory_mb: 512,
        cpu_cores: 1,
      },
    });

    // Register executive summary node
    this.registerNode(this.executiveSummaryNode, {
      node_name: "executive_summary",
      execution_order: 5,
      dependencies: ["research_analysis"],
      optional: false,
      timeout_seconds: 120,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 256,
        cpu_cores: 1,
      },
    });

    // Register section generation node
    this.registerNode(this.sectionGenerationNode, {
      node_name: "section_generation",
      execution_order: 6,
      dependencies: ["executive_summary"],
      optional: false,
      timeout_seconds: 600, // Longer timeout for multiple sections
      retry_attempts: 3,
      resource_requirements: {
        memory_mb: 1024,
        cpu_cores: 2,
      },
    });

    // Register quality review node
    this.registerNode(this.qualityReviewNode, {
      node_name: "quality_review",
      execution_order: 7,
      dependencies: ["section_generation"],
      optional: true, // Can skip if quality threshold is disabled
      timeout_seconds: 180,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 512,
        cpu_cores: 1,
      },
    });

    // Register finalization node
    this.registerNode(this.finalizationNode, {
      node_name: "finalization",
      execution_order: 8,
      dependencies: ["quality_review"],
      optional: false,
      timeout_seconds: 120,
      retry_attempts: 2,
      resource_requirements: {
        memory_mb: 256,
        cpu_cores: 1,
      },
    });
  }

  // Define edges (workflow flow)
  defineEdges(): void {
    // Set entry point
    this.setEntryPoint("validation");

    // Define the workflow flow with conditional logic
    this.addConditionalEdge(
      "validation",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        return "knowledge_retrieval";
      },
      {
        knowledge_retrieval: "knowledge_retrieval",
        end: "__end__",
      }
    );

    this.addConditionalEdge(
      "knowledge_retrieval",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        // Always proceed to client analysis (it's optional and has fallbacks)
        return "client_analysis";
      },
      {
        client_analysis: "client_analysis",
        end: "__end__",
      }
    );

    this.addConditionalEdge(
      "client_analysis",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        return "research_analysis";
      },
      {
        research_analysis: "research_analysis",
        end: "__end__",
      }
    );

    this.addConditionalEdge(
      "research_analysis",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        return "executive_summary";
      },
      {
        executive_summary: "executive_summary",
        end: "__end__",
      }
    );

    this.addConditionalEdge(
      "executive_summary",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        return "section_generation";
      },
      {
        section_generation: "section_generation",
        end: "__end__",
      }
    );

    this.addConditionalEdge(
      "section_generation",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        // Check if quality review is enabled
        if (state.config.quality_threshold > 0) {
          return "quality_review";
        }
        return "finalization";
      },
      {
        quality_review: "quality_review",
        finalization: "finalization",
        end: "__end__",
      }
    );

    this.addConditionalEdge(
      "quality_review",
      (state: ScopingAiWorkflowState) => {
        if (state.status === "failed") {
          return "end";
        }
        // Check if quality review passed or if we should retry section generation
        const qualityData = state.node_data?.quality_review;
        if (
          qualityData &&
          !qualityData.passed_quality_threshold &&
          qualityData.retry_count < 2
        ) {
          return "section_generation"; // Retry section generation
        }
        return "finalization";
      },
      {
        section_generation: "section_generation",
        finalization: "finalization",
        end: "__end__",
      }
    );

    // Set exit point
    this.setExitPoint("finalization");
  }

  // Get workflow configuration with defaults
  public static getDefaultConfig(): WorkflowConfig {
    return {
      timeout_seconds: 600,
      retry_attempts: 3,
      enable_caching: true,
      quality_threshold: 0.7,
      max_sections: 10,
      enable_market_research: true,
      enable_competitive_analysis: true,
      openai_api_key: process.env.OPENAI_API_KEY,
      pinecone_api_key: process.env.PINECONE_API_KEY,
      serp_api_key: process.env.SERP_API_KEY,
    };
  }

  // Validate workflow configuration
  public static validateConfig(config: WorkflowConfig): {
    valid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    if (!config.openai_api_key) {
      issues.push(
        "OpenAI API key is required for AI-powered proposal generation"
      );
    }

    if (config.timeout_seconds < 60) {
      issues.push("Workflow timeout should be at least 60 seconds");
    }

    if (config.quality_threshold < 0 || config.quality_threshold > 1) {
      issues.push("Quality threshold should be between 0 and 1");
    }

    if (config.max_sections < 1 || config.max_sections > 50) {
      issues.push("Max sections should be between 1 and 50");
    }

    return {
      valid: issues.length === 0,
      issues,
    };
  }

  // Create workflow instance with validation
  public static async create(
    config?: Partial<WorkflowConfig>
  ): Promise<ProposalWorkflow> {
    const fullConfig = { ...ProposalWorkflow.getDefaultConfig(), ...config };
    const validation = ProposalWorkflow.validateConfig(fullConfig);

    if (!validation.valid) {
      throw new Error(
        `Invalid workflow configuration: ${validation.issues.join(", ")}`
      );
    }

    return new ProposalWorkflow(fullConfig);
  }

  // Execute workflow with typed input
  public async executeProposal(
    input: ProposalGenerationInput,
    customWorkflowId?: string
  ): Promise<ScopingAiWorkflowState> {
    const result = await this.execute(input, customWorkflowId);
    this.currentWorkflowId = result.workflow_id; // Store the workflow ID
    return result;
  }

  // TODO: Add real-time progress monitoring methods here later

  // Get workflow state by ID
  public async getWorkflowState(
    workflowId: string
  ): Promise<ScopingAiWorkflowState | null> {
    try {
      return await this.stateManager.getWorkflowState(workflowId);
    } catch (error) {
      console.error("Error getting workflow state:", error);
      return null;
    }
  }
}
