// =====================================================
// API CONFIGURATION PANEL FOR PSEO LANGGRAPH
// =====================================================

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Settings, Eye, EyeOff } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface APIConfig {
  openai_enabled: boolean;
  openai_api_key: string;
  semrush_enabled: boolean;
  semrush_api_key: string;
  ubersuggest_enabled: boolean;
  ubersuggest_api_key: string;
  ahrefs_enabled: boolean;
  ahrefs_api_key: string;
}

interface APIStatus {
  openai: boolean;
  semrush: boolean;
  ubersuggest: boolean;
  ahrefs: boolean;
}

export const APIConfigurationPanel: React.FC = () => {
  const [config, setConfig] = useState<APIConfig>({
    openai_enabled: true,
    openai_api_key: '',
    semrush_enabled: false,
    semrush_api_key: '',
    ubersuggest_enabled: false,
    ubersuggest_api_key: '',
    ahrefs_enabled: false,
    ahrefs_api_key: ''
  });

  const [status, setStatus] = useState<APIStatus>({
    openai: false,
    semrush: false,
    ubersuggest: false,
    ahrefs: false
  });

  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({
    openai: false,
    semrush: false,
    ubersuggest: false,
    ahrefs: false
  });

  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Load configuration on mount
  useEffect(() => {
    loadConfiguration();
    checkAPIStatus();
  }, []);

  const loadConfiguration = async () => {
    try {
      const response = await fetch('/api/pseo/langgraph/health');
      if (response.ok) {
        const data = await response.json();
        if (data.configuration?.available_tools) {
          setStatus(data.configuration.available_tools);
        }
      }
    } catch (error) {
      console.error('Failed to load API configuration:', error);
    }
  };

  const checkAPIStatus = async () => {
    try {
      const response = await fetch('/api/pseo/langgraph/health');
      if (response.ok) {
        const data = await response.json();
        if (data.configuration?.available_tools) {
          setStatus(data.configuration.available_tools);
        }
      }
    } catch (error) {
      console.error('Failed to check API status:', error);
    }
  };

  const saveConfiguration = async () => {
    setSaving(true);
    setMessage(null);

    try {
      const response = await fetch('/api/pseo/configuration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Configuration saved successfully!' });
        await checkAPIStatus();
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: error.message || 'Failed to save configuration' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save configuration' });
    } finally {
      setSaving(false);
    }
  };

  const testAPIConnection = async (provider: string) => {
    setTesting(provider);
    setMessage(null);

    try {
      const response = await fetch(`/api/pseo/test-api/${provider}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          api_key: config[`${provider}_api_key` as keyof APIConfig]
        })
      });

      if (response.ok) {
        setMessage({ type: 'success', text: `${provider} API connection successful!` });
        setStatus(prev => ({ ...prev, [provider]: true }));
      } else {
        const error = await response.json();
        setMessage({ type: 'error', text: `${provider} API test failed: ${error.message}` });
        setStatus(prev => ({ ...prev, [provider]: false }));
      }
    } catch (error) {
      setMessage({ type: 'error', text: `Failed to test ${provider} API connection` });
      setStatus(prev => ({ ...prev, [provider]: false }));
    } finally {
      setTesting(null);
    }
  };

  const toggleKeyVisibility = (provider: string) => {
    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }));
  };

  const updateConfig = (key: keyof APIConfig, value: string | boolean) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const getStatusBadge = (provider: string) => {
    const isEnabled = config[`${provider}_enabled` as keyof APIConfig];
    const hasKey = config[`${provider}_api_key` as keyof APIConfig];
    const isWorking = status[provider as keyof APIStatus];

    if (!isEnabled) {
      return <Badge variant="secondary">Disabled</Badge>;
    }

    if (!hasKey) {
      return <Badge variant="destructive">No API Key</Badge>;
    }

    if (isWorking) {
      return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
    }

    return <Badge variant="outline"><AlertCircle className="w-3 h-3 mr-1" />Unknown</Badge>;
  };

  const apiProviders = [
    {
      name: 'OpenAI',
      key: 'openai',
      description: 'Required for AI-powered keyword research and analysis',
      required: true
    },
    {
      name: 'Semrush',
      key: 'semrush',
      description: 'Professional SEO data for accurate search volumes and competition',
      required: false
    },
    {
      name: 'Ubersuggest',
      key: 'ubersuggest',
      description: 'Keyword suggestions and search volume data',
      required: false
    },
    {
      name: 'Ahrefs',
      key: 'ahrefs',
      description: 'Comprehensive SEO data and backlink analysis',
      required: false
    }
  ];

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          API Configuration
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Configure external API integrations for enhanced keyword research capabilities.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {message && (
          <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6">
          {apiProviders.map((provider) => (
            <div key={provider.key} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <h3 className="font-medium">{provider.name}</h3>
                  {provider.required && <Badge variant="destructive" className="text-xs">Required</Badge>}
                  {getStatusBadge(provider.key)}
                </div>
                <Switch
                  checked={config[`${provider.key}_enabled` as keyof APIConfig] as boolean}
                  onCheckedChange={(checked) => updateConfig(`${provider.key}_enabled` as keyof APIConfig, checked)}
                />
              </div>

              <p className="text-sm text-muted-foreground">{provider.description}</p>

              {config[`${provider.key}_enabled` as keyof APIConfig] && (
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <Label htmlFor={`${provider.key}_key`}>API Key</Label>
                      <div className="relative">
                        <Input
                          id={`${provider.key}_key`}
                          type={showKeys[provider.key] ? 'text' : 'password'}
                          value={config[`${provider.key}_api_key` as keyof APIConfig] as string}
                          onChange={(e) => updateConfig(`${provider.key}_api_key` as keyof APIConfig, e.target.value)}
                          placeholder={`Enter ${provider.name} API key`}
                          className="pr-10"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => toggleKeyVisibility(provider.key)}
                        >
                          {showKeys[provider.key] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testAPIConnection(provider.key)}
                        disabled={!config[`${provider.key}_api_key` as keyof APIConfig] || testing === provider.key}
                        className="whitespace-nowrap"
                      >
                        {testing === provider.key ? 'Testing...' : 'Test'}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            Changes will be applied to new keyword research workflows.
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={checkAPIStatus}>
              Refresh Status
            </Button>
            <Button onClick={saveConfiguration} disabled={saving}>
              {saving ? 'Saving...' : 'Save Configuration'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
