# LangGraph Studio Export for ScopingAI Proposal Workflow

This directory contains the LangGraph Studio export for the ScopingAI Proposal Workflow, allowing you to visualize and debug the workflow in LangGraph Studio.

## 🚀 Quick Start

### 1. Start LangGraph Studio

From the project root directory (`altzero_base/`), run:

```bash
npx @langchain/langgraph-cli dev --port 8123 --no-pull
```

### 2. Access LangGraph Studio

Open your browser and navigate to:
```
http://localhost:8123
```

### 3. Select Workflow

In LangGraph Studio, you should see the **"proposal-workflow"** available for visualization and testing.

## 📊 Workflow Overview

The ScopingAI Proposal Workflow consists of 8 main nodes:

### 🔍 **1. Validation Node**
- **Purpose**: Validates input data and requirements
- **Inputs**: Client data, template ID, requirements
- **Outputs**: Validation status and error messages
- **Dependencies**: None (entry point)

### 📚 **2. Knowledge Retrieval Node**
- **Purpose**: Retrieves relevant knowledge documents and context
- **Inputs**: Validated requirements and knowledge document IDs
- **Outputs**: Knowledge context and relevance scores
- **Dependencies**: Validation

### 🏢 **3. Client Analysis Node**
- **Purpose**: Analyzes client industry, size, challenges, and requirements
- **Inputs**: Client data and knowledge context
- **Outputs**: Client analysis insights and recommendations
- **Dependencies**: Knowledge Retrieval

### 🔬 **4. Research Analysis Node**
- **Purpose**: Conducts market research and competitive analysis
- **Inputs**: Client analysis and research requirements
- **Outputs**: Market trends, competitive landscape, industry insights
- **Dependencies**: Client Analysis

### 📋 **5. Executive Summary Node**
- **Purpose**: Generates executive summary based on analysis
- **Inputs**: Client analysis and research data
- **Outputs**: Executive summary with key benefits and ROI
- **Dependencies**: Research Analysis

### 📝 **6. Section Generation Node**
- **Purpose**: Generates detailed proposal sections
- **Inputs**: Executive summary and section templates
- **Outputs**: Structured proposal sections with content
- **Dependencies**: Executive Summary

### ✅ **7. Quality Review Node** (Optional)
- **Purpose**: Reviews proposal quality against thresholds
- **Inputs**: Generated sections and quality criteria
- **Outputs**: Quality score and improvement recommendations
- **Dependencies**: Section Generation
- **Note**: Only runs if `quality_threshold > 0` in config

### 🎯 **8. Finalization Node**
- **Purpose**: Assembles final proposal document
- **Inputs**: All generated content and metadata
- **Outputs**: Complete proposal with formatting and structure
- **Dependencies**: Quality Review (or Section Generation if quality review disabled)

## 🔄 Workflow Flow

```mermaid
graph TD
    A[START] --> B[Validation]
    B --> C{Validation Passed?}
    C -->|Yes| D[Knowledge Retrieval]
    C -->|No| Z[END]
    D --> E[Client Analysis]
    E --> F[Research Analysis]
    F --> G[Executive Summary]
    G --> H[Section Generation]
    H --> I{Quality Review Enabled?}
    I -->|Yes| J[Quality Review]
    I -->|No| L[Finalization]
    J --> K{Quality Passed?}
    K -->|Yes| L[Finalization]
    K -->|No, Retries < 2| H
    K -->|No, Max Retries| L
    L --> Z[END]
```

## 🧪 Testing in LangGraph Studio

### Sample Input Data

You can test the workflow with this sample input:

```json
{
  "workflow_id": "test_workflow_123",
  "user_id": "user_123",
  "client_id": "client_456",
  "client_data": {
    "company_name": "TechCorp Inc",
    "industry": "Technology",
    "company_size": "Medium",
    "budget_range": "500000-750000",
    "timeline": "6 months",
    "decision_makers": ["CTO", "CEO"]
  },
  "template_id": "digital_transformation",
  "requirements": [
    "Cloud migration strategy",
    "Process automation",
    "Team training program"
  ],
  "knowledge_documents": [
    {
      "document_id": "doc_1",
      "title": "Cloud Migration Best Practices",
      "custom_prompt": "Focus on security and compliance aspects"
    }
  ],
  "config": {
    "quality_threshold": 0.7,
    "max_sections": 10,
    "enable_market_research": true,
    "enable_competitive_analysis": true
  }
}
```

### Expected Output Structure

The workflow will produce:

```json
{
  "status": "completed",
  "final_proposal": {
    "proposal_id": "test_workflow_123",
    "title": "Digital Transformation Proposal",
    "executive_summary": { ... },
    "sections": [ ... ],
    "metadata": {
      "generated_at": "2024-01-15T10:30:00Z",
      "version": "1.0",
      "total_pages": 25,
      "word_count": 5000
    }
  },
  "processing_time": 45000,
  "node_data": { ... }
}
```

## 🔧 Configuration Options

### Quality Review Settings
- `quality_threshold`: 0.0-1.0 (0 disables quality review)
- `max_sections`: Maximum number of proposal sections
- `enable_market_research`: Enable/disable market research
- `enable_competitive_analysis`: Enable/disable competitive analysis

### Timeout Settings
- `timeout_seconds`: Overall workflow timeout (default: 600)
- `retry_attempts`: Number of retry attempts per node (default: 3)

## 🐛 Debugging Tips

### Common Issues

1. **Validation Failures**
   - Check that all required fields are provided
   - Ensure client_data contains necessary information
   - Verify template_id exists

2. **Quality Review Loops**
   - Monitor retry_count in quality_review node_data
   - Adjust quality_threshold if too strict
   - Check section generation quality

3. **Timeout Issues**
   - Increase timeout_seconds for complex proposals
   - Monitor processing_time in node_data
   - Check API rate limits

### Monitoring Progress

Each node updates the state with:
- `current_step`: Current node being executed
- `progress`: Percentage completion (0-100)
- `status`: Current workflow status
- `node_data`: Detailed execution information per node

## 📁 File Structure

```
scopingAi/langgraph/
├── langGraphStudioExport.js     # LangGraph Studio export
├── LANGGRAPH_STUDIO.md          # This documentation
├── workflows/
│   └── ProposalWorkflow.ts       # Main workflow implementation
├── nodes/                        # Individual node implementations
├── core/                         # Core workflow infrastructure
└── types/                        # TypeScript type definitions
```

## 🔗 Related Files

- **Main Workflow**: `./workflows/ProposalWorkflow.ts`
- **Configuration**: `../../../../langgraph.json`
- **Environment**: `../../../../.env`

## 📚 Additional Resources

- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [LangGraph Studio Guide](https://langchain-ai.github.io/langgraph/tutorials/langgraph-platform/local-server/)
- [ScopingAI Workflow Documentation](./README.md)
