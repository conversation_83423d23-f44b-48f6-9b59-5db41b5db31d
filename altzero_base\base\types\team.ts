/**
 * Team/Group related type definitions
 */

import { MemberRole } from "./organization";

export type EntityType = "address" | "group";

export interface Group {
  id: string;
  name: string;
  description: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
  organisation_id: string | null;
  entities_ids: string[] | null;
}

export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: MemberRole;
  joined_at: string;
  // Include user info when joined with profiles
  full_name?: string;
  email?: string;
  avatar_url?: string;
}

export interface GroupInvitation {
  id: string;
  group_id: string;
  email: string;
  status: "pending" | "accepted" | "rejected";
  created_at: string;
  updated_at: string;
  // Include group info when joined
  group_name?: string;
}
