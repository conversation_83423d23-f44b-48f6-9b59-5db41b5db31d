// =====================================================
// BASE WORKFLOW CLASS FOR LANGGRAPH SCOPINGAI WORKFLOWS
// =====================================================

import {
  ScopingAiWorkflowState,
  WorkflowContext,
  WorkflowTools,
  WorkflowLogger,
  WorkflowConfig,
} from "../types/WorkflowState";
import {
  BaseNode,
  NodeMetadata,
  DetailedNodeResult,
  NodeStatus,
} from "../types/NodeTypes";
import { ToolRegistry } from "./ToolRegistry";
import { StateManager } from "./StateManager";

export abstract class BaseWorkflow {
  protected tools: ToolRegistry;
  protected stateManager: StateManager;
  protected nodes: Map<string, BaseNode>;
  protected nodeMetadata: Map<string, NodeMetadata>;
  protected logger: WorkflowLogger;
  protected config: WorkflowConfig;
  protected nodeExecutionOrder: string[];

  constructor(config: WorkflowConfig) {
    this.config = config;
    this.tools = new ToolRegistry(config);
    this.stateManager = new StateManager();
    this.nodes = new Map();
    this.nodeMetadata = new Map();
    this.nodeExecutionOrder = [];
    this.logger = this.createLogger();
  }

  // Abstract methods that must be implemented by subclasses
  abstract defineNodes(): void;
  abstract defineEdges(): void;
  abstract getWorkflowName(): string;

  // Initialize the workflow
  protected initializeWorkflow(): void {
    this.defineNodes();
    this.defineEdges();
    this.setupErrorHandling();
  }

  // Register a node in the workflow
  protected registerNode(node: BaseNode, metadata: NodeMetadata): void {
    if (!node) {
      throw new Error("Node is null or undefined");
    }

    if (!node.name) {
      throw new Error(
        `Node name is undefined. Node type: ${node.constructor?.name}`
      );
    }

    this.nodes.set(node.name, node);
    this.nodeMetadata.set(node.name, metadata);

    // Add to execution order based on metadata
    this.addToExecutionOrder(node.name, metadata.execution_order);

    this.logger.debug(`Registered node: ${node.name}`, { metadata });
  }

  // Add node to execution order
  private addToExecutionOrder(nodeName: string, order: number): void {
    // Insert node in correct position based on execution order
    let inserted = false;
    for (let i = 0; i < this.nodeExecutionOrder.length; i++) {
      const existingNode = this.nodeExecutionOrder[i];
      const existingOrder =
        this.nodeMetadata.get(existingNode)?.execution_order || 0;
      if (order < existingOrder) {
        this.nodeExecutionOrder.splice(i, 0, nodeName);
        inserted = true;
        break;
      }
    }
    if (!inserted) {
      this.nodeExecutionOrder.push(nodeName);
    }
  }

  // Execute a specific node
  private async executeNode(
    nodeName: string,
    state: ScopingAiWorkflowState
  ): Promise<Partial<ScopingAiWorkflowState>> {
    const node = this.nodes.get(nodeName);
    const metadata = this.nodeMetadata.get(nodeName);

    if (!node || !metadata) {
      throw new Error(`Node not found: ${nodeName}`);
    }

    const startTime = Date.now();
    let result: DetailedNodeResult;

    try {
      this.logger.info(`Executing node: ${nodeName}`, {
        workflow_id: state.workflow_id,
        current_step: state.current_step,
      });

      // Update state to show current node execution
      const updatedState: Partial<ScopingAiWorkflowState> = {
        current_step: nodeName,
        last_updated: new Date().toISOString(),
      };

      // Create execution context
      const context: WorkflowContext = {
        state: { ...state, ...updatedState },
        tools: await this.tools.getTools(),
        logger: this.logger,
        config: this.config,
      };

      // Execute the node with retry logic
      const nodeResult = await this.executeWithRetry(
        node,
        context,
        metadata.retry_attempts
      );

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      result = {
        ...nodeResult,
        node_name: nodeName,
        status: nodeResult.success ? "completed" : "failed",
        started_at: new Date(startTime).toISOString(),
        completed_at: new Date(endTime).toISOString(),
        execution_time_ms: executionTime,
        retry_count: 0,
        warnings: [],
      };

      // Log execution result
      this.logger.info(`Node execution completed: ${nodeName}`, {
        success: result.success,
        execution_time: executionTime,
        data_points: result.metrics?.data_points_processed || 0,
      });

      // Update processing time in state
      const totalProcessingTime = (state.processing_time || 0) + executionTime;

      return {
        ...nodeResult.data,
        processing_time: totalProcessingTime,
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          [nodeName]: result,
        },
      };
    } catch (error) {
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      this.logger.error(`Node execution failed: ${nodeName}`, error);

      // Add error to state
      const workflowError = {
        node_name: nodeName,
        error_message: error instanceof Error ? error.message : "Unknown error",
        error_code: "NODE_EXECUTION_FAILED",
        timestamp: new Date().toISOString(),
        recoverable: false,
      };

      return {
        status: "failed",
        errors: [...(state.errors || []), workflowError],
        processing_time: (state.processing_time || 0) + executionTime,
        last_updated: new Date().toISOString(),
      };
    }
  }

  // Execute node with retry logic
  private async executeWithRetry(
    node: BaseNode,
    context: WorkflowContext,
    maxRetries: number
  ): Promise<any> {
    let lastError: Error = new Error("Unknown error");

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          this.logger.warn(`Retrying node execution: ${node.name}`, {
            attempt,
            maxRetries,
          });

          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        const result = await node.execute(context);
        return { success: true, data: result };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("Unknown error");

        if (attempt === maxRetries) {
          break;
        }
      }
    }

    return {
      success: false,
      error: lastError.message,
      data: {},
    };
  }

  // Simplified edge methods (for sequential execution)
  protected addEdge(fromNode: string, toNode: string): void {
    this.logger.debug(`Edge added: ${fromNode} -> ${toNode}`);
  }

  // Add conditional edge (simplified)
  protected addConditionalEdge(
    fromNode: string,
    condition: (state: ScopingAiWorkflowState) => string,
    pathMap: Record<string, string>
  ): void {
    this.logger.debug(`Conditional edge added: ${fromNode}`, { pathMap });
  }

  // Set entry and exit points (simplified)
  protected setEntryPoint(nodeName: string): void {
    this.logger.debug(`Entry point set: ${nodeName}`);
  }

  protected setExitPoint(nodeName: string): void {
    this.logger.debug(`Exit point set: ${nodeName}`);
  }

  // Execute workflow sequentially
  private async executeSequentially(
    state: ScopingAiWorkflowState
  ): Promise<ScopingAiWorkflowState> {
    let currentState = { ...state };

    for (const nodeName of this.nodeExecutionOrder) {
      try {
        this.logger.info(`Executing node: ${nodeName}`);

        // Update progress
        const progress = Math.round(
          (this.nodeExecutionOrder.indexOf(nodeName) /
            this.nodeExecutionOrder.length) *
            100
        );
        currentState.progress = progress;
        currentState.current_step = nodeName;

        // Save state
        await this.stateManager.saveWorkflowState(
          currentState.workflow_id,
          currentState
        );

        // Execute node
        const nodeResult = await this.executeNode(nodeName, currentState);

        // Merge results
        currentState = { ...currentState, ...nodeResult };

        // Check for errors
        if (currentState.status === "failed") {
          this.logger.error(`Node ${nodeName} failed, stopping workflow`);
          break;
        }
      } catch (error) {
        this.logger.error(`Error executing node ${nodeName}:`, error);
        currentState.status = "failed";
        currentState.errors = [
          ...(currentState.errors || []),
          {
            node_name: nodeName,
            error_message:
              error instanceof Error ? error.message : "Unknown error",
            error_code: "NODE_EXECUTION_ERROR",
            timestamp: new Date().toISOString(),
            recoverable: false,
          },
        ];
        break;
      }
    }

    return currentState;
  }

  // Create logger instance
  private createLogger(): WorkflowLogger {
    return {
      info: (message: string, data?: any) => {
        console.log(`[INFO] ${this.getWorkflowName()}: ${message}`, data || "");
      },
      warn: (message: string, data?: any) => {
        console.warn(
          `[WARN] ${this.getWorkflowName()}: ${message}`,
          data || ""
        );
      },
      error: (message: string, error?: any) => {
        console.error(
          `[ERROR] ${this.getWorkflowName()}: ${message}`,
          error || ""
        );
      },
      debug: (message: string, data?: any) => {
        if (process.env.NODE_ENV === "development") {
          console.debug(
            `[DEBUG] ${this.getWorkflowName()}: ${message}`,
            data || ""
          );
        }
      },
    };
  }

  // Setup error handling
  private setupErrorHandling(): void {
    this.logger.debug("Error handling setup completed");
  }

  // Generate unique workflow ID
  private generateWorkflowId(): string {
    return `${this.getWorkflowName()}_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }

  // Get workflow status
  public async getStatus(workflowId: string): Promise<any> {
    return this.stateManager.getWorkflowStatus(workflowId);
  }

  // Execute the entire workflow
  public async execute(input: any): Promise<ScopingAiWorkflowState> {
    const workflowId = this.generateWorkflowId();
    const startTime = Date.now();

    try {
      this.logger.info(
        `Starting workflow execution: ${this.getWorkflowName()}`,
        {
          workflow_id: workflowId,
          input,
        }
      );

      // Initialize state
      const initialState: ScopingAiWorkflowState = {
        workflow_id: workflowId,
        user_id: input.user_id,
        status: "running",
        current_step: "initializing",
        progress: 0,
        started_at: new Date().toISOString(),
        last_updated: new Date().toISOString(),
        processing_time: 0,
        client: input.client,
        project: input.project,
        template: input.template,
        requirements: input.requirements || {},
        selected_knowledge_documents: input.selected_knowledge_documents || [],
        document_requirements: input.document_requirements || {},
        ai_prompts: input.ai_prompts,
        errors: [],
        warnings: [],
        api_calls_made: [],
        total_cost: 0,
        data_sources_used: [],
        node_data: {},
        config: {
          timeout_seconds: 600,
          retry_attempts: 3,
          enable_caching: true,
          quality_threshold: 0.7,
          max_sections: 10,
          enable_market_research: true,
          enable_competitive_analysis: true,
          ...input.config,
        },
      };

      // Save initial state
      await this.stateManager.saveWorkflowState(workflowId, initialState);

      // Execute the workflow sequentially
      const result = await this.executeSequentially(initialState);

      // Update final state
      const endTime = Date.now();
      const finalState: ScopingAiWorkflowState = {
        ...result,
        status:
          result.errors && result.errors.length > 0 ? "failed" : "completed",
        completed_at: new Date().toISOString(),
        processing_time: endTime - startTime,
        progress: 100,
      };

      // Save final state
      await this.stateManager.saveWorkflowState(workflowId, finalState);

      this.logger.info(
        `Workflow execution completed: ${this.getWorkflowName()}`,
        {
          workflow_id: workflowId,
          status: finalState.status,
          processing_time: finalState.processing_time,
        }
      );

      return finalState;
    } catch (error) {
      this.logger.error(
        `Workflow execution failed: ${this.getWorkflowName()}`,
        error
      );

      const endTime = Date.now();
      return {
        workflow_id: workflowId,
        user_id: input.user_id,
        status: "failed",
        current_step: "failed",
        progress: 0,
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        last_updated: new Date().toISOString(),
        processing_time: endTime - startTime,
        client: input.client,
        project: input.project,
        template: input.template,
        requirements: input.requirements || {},
        selected_knowledge_documents: input.selected_knowledge_documents || [],
        document_requirements: input.document_requirements || {},
        ai_prompts: input.ai_prompts,
        errors: [
          {
            node_name: "workflow",
            error_message:
              error instanceof Error ? error.message : "Unknown error",
            error_code: "WORKFLOW_EXECUTION_FAILED",
            timestamp: new Date().toISOString(),
            recoverable: false,
          },
        ],
        warnings: [],
        api_calls_made: [],
        total_cost: 0,
        data_sources_used: [],
        node_data: {},
        config: {
          timeout_seconds: 600,
          retry_attempts: 3,
          enable_caching: true,
          quality_threshold: 0.7,
          max_sections: 10,
          enable_market_research: true,
          enable_competitive_analysis: true,
        },
      };
    }
  }

  // Cancel workflow execution
  public async cancel(workflowId: string): Promise<void> {
    this.logger.info(`Cancelling workflow: ${workflowId}`);
  }
}
