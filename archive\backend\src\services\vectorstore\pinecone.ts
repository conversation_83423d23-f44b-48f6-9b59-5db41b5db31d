import { environment } from "../../config/environment";
import { Pinecone } from "@pinecone-database/pinecone";
import { VectorStoreQuery, VectorStoreQueryResult } from "llamaindex";

/**
 * Provides support for writing and querying vector data in Pinecone.
 */
export class PineconeVectorStore {
  public index: any;

  constructor() {
    const client = new Pinecone({
      apiKey: environment.pineconeApiKey as string,
    });

    this.index = client.Index(environment.pineconeIndexName as string);
  }

  async query(query: VectorStoreQuery): Promise<VectorStoreQueryResult> {
    try {
      const queryResponse = await this.index.query({
        vector: query.queryEmbedding,
        topK: query.similarityTopK || 5,
        includeMetadata: true,
        includeValues: true,
      });

      const matches = queryResponse.matches || [];
      return {
        nodes: matches.map((match) => ({
          id_: match.id,
          text: match.metadata?.text || "",
          metadata: match.metadata || {},
          embedding: match.values || [],
          getContent: () => match.metadata?.text || "",
          getEmbedding: () => match.values || [],
        })),
        similarities: matches.map((match) => Number(match.score || 0)),
        ids: matches.map((match) => match.id),
      };
    } catch (error) {
      console.error("Error querying Pinecone:", error);
      return {
        nodes: [],
        similarities: [],
        ids: [],
      };
    }
  }
}

async function testPineconeIndex() {
  console.log("🔍 Testing Pinecone index...");

  try {
    const pc = new Pinecone({
      apiKey: environment.pineconeApiKey as string,
    });

    // Get the index
    const index = pc.Index(environment.pineconeIndexName as string);

    // Correctly get namespace using the method approach
    const namespaceIndex = environment.pineconeNamespace
      ? index.namespace(environment.pineconeNamespace)
      : index;

    // Get index details
    console.log("Getting index info...");
    const indexStats = await index.describeIndexStats();

    console.log(
      `\nTotal vectors in index: ${indexStats.totalRecordCount || 0}`
    );
    console.log(`Namespaces: ${Object.keys(indexStats.namespaces || {})}`);

    // Test a simple query
    console.log("\nTesting a simple query...");
    const randomVector = Array.from(
      { length: 1024 },
      () => Math.random() * 0.01
    );

    const results = await namespaceIndex.query({
      vector: randomVector,
      topK: 5,
      includeMetadata: true,
    });

    console.log(`Query returned ${results.matches?.length || 0} matches`);

    return true;
  } catch (error) {
    console.error("❌ Pinecone test failed:", error);
    return false;
  }
}
