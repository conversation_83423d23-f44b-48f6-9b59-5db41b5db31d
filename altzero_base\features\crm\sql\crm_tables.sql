-- Enable UUID extension if not already
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Example: assuming you have this already
-- CREATE TABLE organisations (
--   id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
--   name text NOT NULL,
--   ...
-- );

-- 1. <PERSON><PERSON> Contacts
CREATE TABLE public.crm_contacts (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  full_name text NOT NULL,
  email text,
  phone text,
  phone2 text,
  mobile text,
  fax text,
  address jsonb,
  job_title text,
  company_id uuid REFERENCES public.crm_companies(id),
  external_id text,
  owner_id uuid REFERENCES auth.users(id),
  salutation text,
  skypename text,
  webpage text,
  note text,
  tags text[],
  custom_fields jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 2. CRM Companies
CREATE TABLE public.crm_companies (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  name text NOT NULL,
  website text,
  email text,
  phone text,
  fax text,
  address jsonb,
  employees integer,
  revenues text,
  tax_number text,
  region_id uuid REFERENCES public.crm_regions(id),
  owner_id uuid REFERENCES auth.users(id),
  external_id text,
  custom_fields jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 3. CRM Events
CREATE TABLE public.crm_events (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  title text NOT NULL,
  start_time timestamptz,
  end_time timestamptz,
  location text,
  geo jsonb,
  note text,
  contact_id uuid REFERENCES public.crm_contacts(id),
  company_id uuid REFERENCES public.crm_companies(id),
  owner_id uuid REFERENCES auth.users(id),
  related_project_id uuid,
  related_opportunity_id uuid,
  custom_fields jsonb DEFAULT '{}'::jsonb,
  recurrence_rule text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Note: Using existing organisation_groups table instead of creating crm_groups
-- The organisation_groups table already handles group management within organizations

-- 4. Contact Groups (many-to-many) - Links contacts to existing organisation_groups
CREATE TABLE public.crm_contact_groups (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  contact_id uuid REFERENCES public.crm_contacts(id) ON DELETE CASCADE,
  group_id uuid REFERENCES public.organisation_groups(id) ON DELETE CASCADE
);

-- 6. CRM Regions
CREATE TABLE public.crm_regions (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  name text NOT NULL,
  is_favorite boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- 7. CRM Pipelines
CREATE TABLE public.crm_pipelines (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  name text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- 8. CRM Pipeline Stages
CREATE TABLE public.crm_pipeline_stages (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  pipeline_id uuid REFERENCES public.crm_pipelines(id),
  name text NOT NULL,
  position integer NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- 9. CRM Opportunities
CREATE TABLE public.crm_opportunities (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  contact_id uuid REFERENCES public.crm_contacts(id),
  title text NOT NULL,
  value numeric(12,2),
  currency text DEFAULT 'USD',
  stage text,
  close_date date,
  assigned_to uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 10. CRM Activities
CREATE TABLE public.crm_activities (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  contact_id uuid REFERENCES public.crm_contacts(id),
  opportunity_id uuid REFERENCES public.crm_opportunities(id),
  type text CHECK (type IN ('call', 'email', 'meeting', 'note')),
  content text,
  scheduled_at timestamptz,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now()
);

-- 11. CRM Jobs
CREATE TABLE public.crm_jobs (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  title text NOT NULL,
  description text,
  location text,
  employment_type text CHECK (employment_type IN ('full_time', 'part_time', 'contract', 'internship')),
  salary_range text,
  posted_at timestamptz DEFAULT now(),
  closing_date timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 12. CRM Job Applications
CREATE TABLE public.crm_job_applications (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  organisation_id uuid NOT NULL REFERENCES public.organisations(id),
  job_id uuid REFERENCES public.crm_jobs(id),
  contact_id uuid REFERENCES public.crm_contacts(id),
  status text CHECK (status IN ('applied', 'interview', 'offered', 'rejected', 'hired')) DEFAULT 'applied',
  resume_url text,
  cover_letter text,
  applied_at timestamptz DEFAULT now()
);

-- Note: CRM contacts are directly usable in ScopingAI without additional linking
-- All CRM contacts within an organization are automatically available for ScopingAI proposals

-- Note: Using existing organisation_members table instead of creating crm_organisation_users
-- The organisation_members table already handles user-organization relationships

-- Enable Row-Level Security (RLS) on all tables
ALTER TABLE public.crm_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_contact_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_regions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_pipelines ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_pipeline_stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_opportunities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_job_applications ENABLE ROW LEVEL SECURITY;


-- RLS Policies
-- Only allow access to rows for organizations where the user is a member
CREATE POLICY "Organisation members can manage contacts" ON public.crm_contacts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_contacts.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage companies" ON public.crm_companies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_companies.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage events" ON public.crm_events
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_events.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage contact groups" ON public.crm_contact_groups
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_contact_groups.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage regions" ON public.crm_regions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_regions.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage pipelines" ON public.crm_pipelines
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_pipelines.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage pipeline stages" ON public.crm_pipeline_stages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_pipeline_stages.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage opportunities" ON public.crm_opportunities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_opportunities.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage activities" ON public.crm_activities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_activities.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage jobs" ON public.crm_jobs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_jobs.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Organisation members can manage job applications" ON public.crm_job_applications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.organisation_members
      WHERE organisation_members.organisation_id = crm_job_applications.organisation_id
      AND organisation_members.user_id = auth.uid()
    )
  );



-- Indexes for performance optimization
-- CRM contacts are directly usable in ScopingAI without additional linking tables
