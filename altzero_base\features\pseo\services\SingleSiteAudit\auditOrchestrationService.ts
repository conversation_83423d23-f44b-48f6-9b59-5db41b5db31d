import { databaseService } from '../pseo/databaseService';
import { scraperService } from './scraperService';
import { aiAnalysisService } from './aiAnalysisService';
import { contentOptimizationService } from './contentOptimizationService';
import { auditSetupService } from './auditSetupService';
import { auditProgressService } from './auditProgressService';
import { PSEO_CONSTANTS } from '../../utilities/pseo/constants';
import type { 
  PSEOAudit, 
  AuditProgress,
  PSEOError,
  PSEOAnalysisResponse,
  PSEOReportGenerationResponse,
  ScrapedData
} from '../../types';

export interface AuditOrchestrationOptions {
  aiModel?: string;
  userId?: string;
  skipSteps?: string[];
  customPrompts?: {
    technical?: string;
    content?: string;
  };
  websiteId?: string;
}

export interface AuditResult {
  audit: PSEOAudit;
  scrapedData: ScrapedData;
  analysis: PSEOAnalysisResponse;
  report: PSEOReportGenerationResponse;
  processingTime: number;
}

class AuditOrchestrationService {
  private progressCallbacks: Map<string, (progress: AuditProgress) => void> = new Map();
  private errorCallbacks: Map<string, (error: PSEOError) => void> = new Map();

  /**
   * Register progress callback for an audit
   */
  onProgress(auditId: string, callback: (progress: AuditProgress) => void): void {
    this.progressCallbacks.set(auditId, callback);
  }

  /**
   * Register error callback for an audit
   */
  onError(auditId: string, callback: (error: PSEOError) => void): void {
    this.errorCallbacks.set(auditId, callback);
  }

  /**
   * Remove callbacks for an audit
   */
  removeCallbacks(auditId: string): void {
    this.progressCallbacks.delete(auditId);
    this.errorCallbacks.delete(auditId);
  }

  /**
   * Emit progress update
   */
  private emitProgress(auditId: string, progress: AuditProgress): void {
    const callback = this.progressCallbacks.get(auditId);
    if (callback) {
      callback(progress);
    }
  }

  /**
   * Emit error
   */
  private emitError(auditId: string, error: PSEOError): void {
    const callback = this.errorCallbacks.get(auditId);
    if (callback) {
      callback(error);
    }
  }

  /**
   * Update progress in database and emit to callbacks
   */
  private async updateProgress(auditId: string, currentStep: string, completedSteps: string[]): Promise<void> {
    try {
      // Update audit status in database
      await databaseService.updateAudit(auditId, {
        status: this.getAuditStatusFromStep(currentStep) as 'pending' | 'scraping' | 'analyzing' | 'completed' | 'failed',
      });

      // Create progress object
      const totalSteps = 5; // setup, scraping, analysis, report, complete
      const progressPercentage = Math.round((completedSteps.length / totalSteps) * 100);
      
      const progress: AuditProgress = {
        auditId,
        currentStep,
        completedSteps,
        totalSteps,
        isComplete: completedSteps.length === totalSteps,
        hasError: false,
        progressPercentage,
      };

      // Emit progress
      this.emitProgress(auditId, progress);
      console.log(`📊 Progress Update: ${currentStep} (${progressPercentage}%)`);
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  }

  /**
   * Get audit status from current step
   */
  private getAuditStatusFromStep(step: string): string {
    switch (step) {
      case 'Setup': return PSEO_CONSTANTS.AUDIT_STATUS.PENDING;
      case 'Scraping': return PSEO_CONSTANTS.AUDIT_STATUS.SCRAPING;
      case 'Analysis': return PSEO_CONSTANTS.AUDIT_STATUS.ANALYZING;
      case 'Report Generation': return PSEO_CONSTANTS.AUDIT_STATUS.ANALYZING;
      case 'Completed': return PSEO_CONSTANTS.AUDIT_STATUS.COMPLETED;
      default: return PSEO_CONSTANTS.AUDIT_STATUS.PENDING;
    }
  }

  /**
   * Start a complete pSEO audit workflow with proper progress tracking
   */
  async startAudit(
    websiteUrl: string,
    websiteName: string,
    options: AuditOrchestrationOptions = {}
  ): Promise<AuditResult> {
    const startTime = Date.now();
    let audit: PSEOAudit | null = null;

    try {
      console.log(`🚀 Starting simplified pSEO audit for: ${websiteUrl}`);

      // Validate user ID
      if (!options.userId) {
        throw new Error('User ID is required for pSEO audit. Please ensure you are logged in.');
      }

      if (!options.websiteId) {
        throw new Error('Website ID is required. Please select an existing website from your client list.');
      }

      // Get existing website
      const website = await databaseService.getWebsiteById(options.websiteId);
      if (!website) {
        throw new Error(`Website with ID ${options.websiteId} not found`);
      }

      // Create audit record
      audit = await databaseService.createAudit({
        website_id: options.websiteId,
        ai_model_used: options.aiModel || PSEO_CONSTANTS.AI_MODELS.DEFAULT,
        status: PSEO_CONSTANTS.AUDIT_STATUS.PENDING,
        scrape_metadata: {},
        technical_analysis: {},
        content_analysis: {},
      });

      console.log(`✅ Audit created: ${audit.id}`);

      // Step 1: Setup Complete
      await this.updateProgress(audit.id, 'Setup', []);

      // Step 2: Website Scraping
      await this.updateProgress(audit.id, 'Scraping', ['Setup']);
      
      const scrapedData = await scraperService.scrapeWebsite(websiteUrl);
      console.log(`✅ Website scraped: ${scrapedData.html.length} chars`);

      // Step 2.5: Content Optimization (NEW - reduce content size before analysis)
      const optimizedContent = await contentOptimizationService.optimizeForAnalysis(scrapedData.html);
      const optimizationStats = contentOptimizationService.getOptimizationStats(
        scrapedData.html.length, 
        optimizedContent.length
      );
      console.log(`✅ Content optimized: ${optimizationStats.reductionPercentage}% reduction (${optimizationStats.originalSize} → ${optimizationStats.optimizedSize} chars)`);

      // Update audit with scraped content (store both original and optimized)
      await databaseService.updateAudit(audit.id, {
        scraped_content: optimizedContent, // Use optimized content for analysis
        scrape_metadata: {
          statusCode: scrapedData.statusCode,
          headers: scrapedData.headers,
          loadTime: scrapedData.loadTime,
          timestamp: scrapedData.timestamp,
          originalSize: scrapedData.html.length,
          optimizedSize: optimizedContent.length,
          reductionPercentage: optimizationStats.reductionPercentage,
        },
      });

      // Step 3: AI Analysis  
      await this.updateProgress(audit.id, 'Analysis', ['Setup', 'Scraping']);

      const analysis = await aiAnalysisService.performAnalysis({
        scrapedContent: optimizedContent, // Use optimized content for analysis
        url: websiteUrl,
        userId: options.userId,
        auditId: audit.id,
      });
      console.log(`✅ AI analysis completed`);

      // Step 3.5: Server-side SEO Scoring (NEW - secure API analysis)
      let htmlAnalysis = null;
      let seoScoring = null;
      
      try {
        console.log('🔒 Starting server-side SEO analysis...');
        const response = await fetch('http://localhost:3001/api/pseo/seo-scoring', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': 'altzero_base',
            'x-user-id': options.userId || '',
          },
          body: JSON.stringify({
            htmlContent: optimizedContent,
            url: websiteUrl,
          }),
        });

        if (response.ok) {
          const result = await response.json();
          htmlAnalysis = result.htmlAnalysis;
          seoScoring = result.seoScoring;
          console.log(`✅ Server-side SEO analysis: ${htmlAnalysis?.totalIssues || 0} issues, score: ${seoScoring?.overallScore || 0}/100`);
        } else {
          console.warn('⚠️ Server-side SEO analysis failed, continuing with AI analysis only');
        }
      } catch (error) {
        console.warn('⚠️ Server-side SEO analysis error:', error);
      }

      // Update audit with analysis results (including enhanced data if available)
      await databaseService.updateAudit(audit.id, {
        technical_audit_raw: JSON.stringify(analysis.technical),
        content_audit_raw: JSON.stringify(analysis.content),
        technical_analysis: analysis.technical as unknown as Record<string, unknown>,
        content_analysis: analysis.content as unknown as Record<string, unknown>,
        // Store enhanced analysis if available
        ...(htmlAnalysis && { html_analysis: JSON.stringify(htmlAnalysis) }),
        ...(seoScoring && { seo_metrics: JSON.stringify(seoScoring) }),
      });

      // Step 4: Report Generation
      await this.updateProgress(audit.id, 'Report Generation', ['Setup', 'Scraping', 'Analysis']);

      const domain = this.extractDomain(websiteUrl);
      const report = await aiAnalysisService.generateReport({
        technicalAnalysis: analysis.technical,
        contentAnalysis: analysis.content,
        url: websiteUrl,
        domain,
        userId: options.userId,
      });
      console.log(`✅ Report generated`);

      // Convert markdown to HTML
      const { html } = await aiAnalysisService.convertMarkdownToHTML(report.markdownReport);

      // Final audit update
      await databaseService.updateAudit(audit.id, {
        combined_report: report.markdownReport,
        report_html: html,
        completed_at: new Date().toISOString(),
        processing_time_seconds: Math.round((Date.now() - startTime) / 1000),
      });

      // Step 5: Completed
      await this.updateProgress(audit.id, 'Completed', ['Setup', 'Scraping', 'Analysis', 'Report Generation']);

      const processingTime = Date.now() - startTime;
      console.log(`🎉 pSEO audit ${audit.id} completed successfully in ${processingTime}ms`);

      return {
        audit,
        scrapedData,
        analysis,
        report,
        processingTime,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error(`❌ pSEO audit failed:`, error);

      // Update audit status to failed (only if audit was created)
      if (audit?.id) {
        try {
          await databaseService.updateAudit(audit.id, {
            status: PSEO_CONSTANTS.AUDIT_STATUS.FAILED,
            error_message: errorMessage,
            processing_time_seconds: Math.round((Date.now() - startTime) / 1000),
          });

          const pseoError: PSEOError = {
            code: 'AUDIT_FAILED',
            message: errorMessage,
            details: { auditId: audit.id, websiteUrl },
            timestamp: new Date().toISOString(),
          };

          this.emitError(audit.id, pseoError);
        } catch (updateError) {
          console.error('Failed to update audit status:', updateError);
        }
      }

      throw error;
    } finally {
      // Clean up callbacks (only if audit was created)
      if (audit?.id) {
        this.removeCallbacks(audit.id);
      }
    }
  }

  /**
   * Get audit progress from database
   */
  async getAuditProgress(auditId: string): Promise<AuditProgress> {
    try {
      const audit = await databaseService.getAuditById(auditId);
      if (!audit) {
        throw new Error('Audit not found');
      }

      // Map audit status to progress
      let currentStep = 'Pending';
      let completedSteps: string[] = [];
      let progressPercentage = 0;

      switch (audit.status) {
        case PSEO_CONSTANTS.AUDIT_STATUS.PENDING:
          currentStep = 'Setup';
          completedSteps = [];
          progressPercentage = 0;
          break;
        case PSEO_CONSTANTS.AUDIT_STATUS.SCRAPING:
          currentStep = 'Scraping';
          completedSteps = ['Setup'];
          progressPercentage = 20;
          break;
        case PSEO_CONSTANTS.AUDIT_STATUS.ANALYZING:
          if (audit.scraped_content && !audit.technical_analysis) {
            currentStep = 'Analysis';
            completedSteps = ['Setup', 'Scraping'];
            progressPercentage = 40;
          } else if (audit.technical_analysis && !audit.combined_report) {
            currentStep = 'Report Generation';
            completedSteps = ['Setup', 'Scraping', 'Analysis'];
            progressPercentage = 80;
          }
          break;
        case PSEO_CONSTANTS.AUDIT_STATUS.COMPLETED:
          currentStep = 'Completed';
          completedSteps = ['Setup', 'Scraping', 'Analysis', 'Report Generation'];
          progressPercentage = 100;
          break;
        case PSEO_CONSTANTS.AUDIT_STATUS.FAILED:
          currentStep = 'Failed';
          progressPercentage = 0;
          break;
      }

      return {
        auditId,
        currentStep,
        completedSteps,
        totalSteps: 5,
        isComplete: audit.status === PSEO_CONSTANTS.AUDIT_STATUS.COMPLETED,
        hasError: audit.status === PSEO_CONSTANTS.AUDIT_STATUS.FAILED,
        errorMessage: audit.error_message || undefined,
        progressPercentage,
      };
    } catch (error) {
      console.error(`Failed to get audit progress for ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel a running audit
   */
  async cancelAudit(auditId: string): Promise<void> {
    try {
      await databaseService.updateAudit(auditId, {
        status: PSEO_CONSTANTS.AUDIT_STATUS.FAILED,
        error_message: 'Audit cancelled by user',
      });

      this.removeCallbacks(auditId);
      console.log(`Audit ${auditId} cancelled successfully`);
    } catch (error) {
      console.error(`Failed to cancel audit ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch (error) {
      throw new Error(`Invalid URL: ${url}`);
    }
  }

  /**
   * Validate orchestration configuration
   */
  async validateConfiguration(): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Check all sub-services
      const services = [
        { name: 'Database', service: databaseService },
        { name: 'Scraper', service: scraperService },
        { name: 'AI Analysis', service: aiAnalysisService },
      ];

      for (const { name, service } of services) {
        try {
          if ('healthCheck' in service) {
            const health = await (service as any).healthCheck();
            if (!health) {
              issues.push(`${name} service is not healthy`);
              recommendations.push(`Check ${name.toLowerCase()} service configuration`);
            }
          }
        } catch (error) {
          issues.push(`${name} service validation failed`);
          recommendations.push(`Check ${name.toLowerCase()} service connectivity`);
        }
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      issues.push(`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        isValid: false,
        issues,
        recommendations,
      };
    }
  }
}

export const auditOrchestrationService = new AuditOrchestrationService(); 