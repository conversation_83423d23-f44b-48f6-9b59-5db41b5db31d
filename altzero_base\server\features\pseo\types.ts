// =====================================================
// PSEO TYPES AND INTERFACES
// =====================================================

export interface KeywordResearchInput {
  website_id: string;
  domain?: string;
  seed_keywords?: string[];
  research_method: 'website' | 'topic';
  topic_input?: string;
  competitor_domains?: string[];
  max_keywords?: number;
  data_sources: string[];
}

export interface KeywordData {
  keyword: string;
  search_volume: number;
  keyword_difficulty: number;
  cpc: number;
  competition: 'low' | 'medium' | 'high';
  intent: 'informational' | 'navigational' | 'commercial' | 'transactional';
  trend: 'rising' | 'stable' | 'declining';
  data_source: string;
  ranking_position?: number;
  ranking_url?: string;
}

export interface CompetitorData {
  domain: string;
  keywords: KeywordData[];
  ranking_overlap: number;
  authority_score: number;
}

export interface ContentSuggestion {
  title: string;
  content_type: 'blog' | 'landing' | 'product' | 'guide' | 'faq';
  target_keywords: string[];
  estimated_traffic: number;
  difficulty_score: number;
}

export interface KeywordCluster {
  cluster_name: string;
  primary_keyword: string;
  related_keywords: string[];
  search_volume: number;
  difficulty_score: number;
  intent_category: string;
}

export interface WorkflowError {
  node_name: string;
  error_message: string;
  error_code: string;
  timestamp: string;
  recoverable: boolean;
}

export interface APICallMetrics {
  provider: string;
  endpoint: string;
  calls_made: number;
  success_rate: number;
  average_response_time: number;
  cost_estimate: number;
}

export interface PSEOWorkflowResult {
  workflow_id: string;
  user_id: string;
  website_id: string;
  domain: string;
  seed_keywords: string[];
  research_method: 'website' | 'topic';
  topic_input?: string;
  competitor_domains?: string[];
  max_keywords?: number;
  data_sources: string[];
  
  // Processing state
  current_step: string;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  errors: WorkflowError[];
  
  // Results data
  keywords: KeywordData[];
  keyword_clusters: KeywordCluster[];
  competitor_data: CompetitorData[];
  content_suggestions: ContentSuggestion[];
  
  // Metadata and metrics
  api_calls_made: APICallMetrics[];
  processing_time: number;
  data_sources_used: string[];
  total_cost: number;
  
  // Timestamps
  started_at: string;
  completed_at?: string;
  last_updated: string;
}

export interface WorkflowStatus {
  workflow_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'not_found';
  progress: number;
  current_step: string;
  started_at: string;
  completed_at?: string;
  processing_time: number;
  errors: WorkflowError[];
  keywords_found: number;
  last_updated: string;
}

export interface APIConfiguration {
  openai_enabled: boolean;
  openai_api_key: string;
  semrush_enabled: boolean;
  semrush_api_key: string;
  ubersuggest_enabled: boolean;
  ubersuggest_api_key: string;
  ahrefs_enabled: boolean;
  ahrefs_api_key: string;
}

export interface APIHealthStatus {
  openai: boolean;
  semrush: boolean;
  ubersuggest: boolean;
  ahrefs: boolean;
}

export interface WorkflowStats {
  total: number;
  active: number;
  completed: number;
  failed: number;
  average_processing_time: number;
}
