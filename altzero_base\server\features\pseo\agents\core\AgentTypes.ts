// =====================================================
// AGENTIC pSEO TYPES - CORE AGENT SYSTEM
// =====================================================

// Basic types (replicated to avoid import issues)
interface PSEOWebsite {
  id: string;
  client_id: string;
  url: string;
  domain: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface PSEOWebsitePage {
  id: string;
  website_id: string;
  url: string;
  title?: string;
  meta_description?: string;
  content_hash?: string;
  page_type: 'page' | 'blog' | 'product' | 'category' | 'homepage';
  status: 'discovered' | 'crawled' | 'analyzed' | 'error';
  discovered_via: 'sitemap' | 'crawl' | 'manual';
  last_crawled?: string;
  crawl_error?: string;
  word_count?: number;
  response_code?: number;
  load_time_ms?: number;
  created_at: string;
  updated_at: string;
}

interface PSEOKeyword {
  id: string;
  website_id: string;
  keyword: string;
  search_volume?: number;
  keyword_difficulty?: number;
  cpc?: number;
  competition?: 'low' | 'medium' | 'high';
  ranking_position?: number;
  ranking_url?: string;
  intent?: 'informational' | 'navigational' | 'commercial' | 'transactional';
  data_source: 'google_planner' | 'ubersuggest' | 'dataforseo' | 'manual';
  created_at: string;
  updated_at: string;
}

interface PSEOBacklink {
  id: string;
  website_id: string;
  source_domain: string;
  source_url: string;
  target_url: string;
  anchor_text?: string;
  link_type: 'dofollow' | 'nofollow' | 'unknown';
  domain_authority?: number;
  page_authority?: number;
  spam_score?: number;
  first_seen?: string;
  last_seen?: string;
  status: 'active' | 'lost' | 'unknown';
  data_source: 'moz' | 'majestic' | 'ahrefs' | 'manual';
  created_at: string;
}

interface PSEOContentOpportunity {
  id: string;
  website_id: string;
  target_keyword: string;
  content_type: 'blog' | 'landing' | 'product' | 'guide' | 'faq';
  title: string;
  content_brief?: string;
  target_word_count?: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  difficulty_score?: number;
  estimated_traffic?: number;
  status: 'identified' | 'in_progress' | 'completed' | 'abandoned';
  assigned_to?: string;
  due_date?: string;
  generated_content?: string;
  ai_model_used?: string;
  created_at: string;
  updated_at: string;
}

interface PSEOAgentJob {
  id: string;
  website_id: string;
  job_type: 'page_discovery' | 'keyword_research' | 'backlink_analysis' | 'content_generation' | 'full_site_audit';
  agent_name: string;
  parent_job_id?: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  started_at?: string;
  completed_at?: string;
  processing_time_seconds?: number;
  result_data: Record<string, unknown>;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  created_at: string;
  updated_at: string;
}

// Base agent interface
export interface IAgent {
  name: string;
  description: string;
  capabilities: AgentCapability[];
  execute(context: AgentContext): Promise<AgentResult>;
  validateInput(input: AgentInput): boolean;
  getRequiredTools(): string[];
}

// Agent capabilities
export type AgentCapability = 
  | 'page_discovery' 
  | 'sitemap_parsing' 
  | 'web_crawling'
  | 'keyword_research'
  | 'search_volume_analysis'
  | 'competition_analysis'
  | 'backlink_analysis'
  | 'domain_authority_check'
  | 'content_generation'
  | 'seo_optimization'
  | 'technical_audit'
  | 'report_generation';

// Agent execution context
export interface AgentContext {
  website: PSEOWebsite;
  job: PSEOAgentJob;
  tools: AgentToolbox;
  config: AgentConfig;
  logger: AgentLogger;
  userData?: Record<string, unknown>;
}

// Agent input/output
export interface AgentInput {
  website_id: string;
  parameters: Record<string, unknown>;
  options?: AgentOptions;
}

export interface AgentResult {
  success: boolean;
  data?: Record<string, unknown>;
  error?: string;
  metrics: AgentMetrics;
  next_actions?: NextAction[];
}

// Agent configuration
export interface AgentConfig {
  max_execution_time_seconds: number;
  retry_attempts: number;
  rate_limits?: {
    requests_per_minute?: number;
    requests_per_hour?: number;
  };
  api_keys?: Record<string, string>;
  feature_flags?: Record<string, boolean>;
}

// Agent options
export interface AgentOptions {
  priority?: 'low' | 'medium' | 'high' | 'critical';
  async_execution?: boolean;
  dependencies?: string[]; // job IDs this depends on
  timeout_override?: number;
  custom_params?: Record<string, unknown>;
}

// Agent metrics
export interface AgentMetrics {
  execution_time_ms: number;
  api_calls_made: number;
  data_points_processed: number;
  errors_encountered: number;
  cache_hits: number;
  memory_usage_mb?: number;
}

// Next actions for orchestration
export interface NextAction {
  agent_name: string;
  action_type: 'parallel' | 'sequential' | 'conditional';
  condition?: string;
  input_data?: Record<string, unknown>;
  priority?: number;
}

// Agent toolbox - available tools for agents
export interface AgentToolbox {
  http: HttpTool;
  database: DatabaseTool;
  ai: AITool;
  crawler: CrawlerTool;
  parser: ParserTool;
  validator: ValidatorTool;
  cache: CacheTool;
}

// Tool interfaces
export interface HttpTool {
  get(url: string, options?: HttpOptions): Promise<HttpResponse>;
  post(url: string, data: unknown, options?: HttpOptions): Promise<HttpResponse>;
  put(url: string, data: unknown, options?: HttpOptions): Promise<HttpResponse>;
  delete(url: string, options?: HttpOptions): Promise<HttpResponse>;
}

export interface DatabaseTool {
  query<T>(sql: string, params?: unknown[]): Promise<T[]>;
  insert<T>(table: string, data: Record<string, unknown>): Promise<T>;
  update<T>(table: string, id: string, data: Record<string, unknown>): Promise<T>;
  delete(table: string, id: string): Promise<boolean>;
  transaction<T>(operations: (() => Promise<T>)[]): Promise<T[]>;
}

export interface AITool {
  generateText(prompt: string, options?: AIOptions): Promise<string>;
  analyzeContent(content: string, analysis_type: string): Promise<unknown>;
  generateEmbeddings(text: string): Promise<number[]>;
  classifyContent(content: string, categories: string[]): Promise<string>;
}

export interface CrawlerTool {
  crawlPage(url: string, options?: CrawlOptions): Promise<CrawlResult>;
  parseSitemap(url: string): Promise<string[]>;
  checkRobotsTxt(domain: string): Promise<RobotsInfo>;
  batchCrawl(urls: string[], options?: CrawlOptions): Promise<CrawlResult[]>;
}

export interface ParserTool {
  parseHTML(html: string): ParsedHTML;
  extractMetadata(html: string): PageMetadata;
  extractLinks(html: string): Link[];
  extractText(html: string): string;
  extractStructuredData(html: string): StructuredData[];
}

export interface ValidatorTool {
  validateURL(url: string): boolean;
  validateEmail(email: string): boolean;
  validateDomain(domain: string): boolean;
  validateSEOData(data: unknown): ValidationResult;
}

export interface CacheTool {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(pattern?: string): Promise<void>;
}

// HTTP related types
export interface HttpOptions {
  headers?: Record<string, string>;
  timeout?: number;
  retry_attempts?: number;
  follow_redirects?: boolean;
  user_agent?: string;
}

export interface HttpResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: unknown;
  url: string;
  response_time_ms: number;
}

// AI tool options
export interface AIOptions {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  system_prompt?: string;
  response_format?: 'text' | 'json' | 'markdown';
}

// Crawling types
export interface CrawlOptions {
  follow_redirects?: boolean;
  max_depth?: number;
  respect_robots_txt?: boolean;
  delay_between_requests?: number;
  user_agent?: string;
  include_assets?: boolean;
}

export interface CrawlResult {
  url: string;
  status_code: number;
  content: string;
  headers: Record<string, string>;
  load_time_ms: number;
  metadata: PageMetadata;
  links: Link[];
  images: Image[];
  errors?: string[];
}

export interface RobotsInfo {
  allowed_paths: string[];
  disallowed_paths: string[];
  crawl_delay?: number;
  sitemap_urls: string[];
}

// Parsing types
export interface ParsedHTML {
  title?: string;
  meta_description?: string;
  meta_keywords?: string;
  headings: Heading[];
  content: string;
  word_count: number;
  images: Image[];
  links: Link[];
}

export interface PageMetadata {
  title?: string;
  description?: string;
  keywords?: string;
  author?: string;
  published_date?: string;
  modified_date?: string;
  canonical_url?: string;
  og_data?: Record<string, string>;
  schema_org?: StructuredData[];
}

export interface Link {
  url: string;
  text: string;
  type: 'internal' | 'external';
  rel?: string;
  target?: string;
}

export interface Image {
  src: string;
  alt?: string;
  title?: string;
  width?: number;
  height?: number;
}

export interface Heading {
  level: number;
  text: string;
  id?: string;
}

export interface StructuredData {
  type: string;
  data: Record<string, unknown>;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Agent logger interface
export interface AgentLogger {
  debug(message: string, metadata?: Record<string, unknown>): void;
  info(message: string, metadata?: Record<string, unknown>): void;
  warn(message: string, metadata?: Record<string, unknown>): void;
  error(message: string, error?: Error, metadata?: Record<string, unknown>): void;
  metric(name: string, value: number, metadata?: Record<string, unknown>): void;
}

// =====================================================
// ORCHESTRATOR TYPES
// =====================================================

// Orchestrator configuration
export interface OrchestratorConfig {
  max_concurrent_agents: number;
  default_timeout_seconds: number;
  retry_failed_jobs: boolean;
  max_retry_attempts: number;
  job_queue_size: number;
  enable_job_persistence: boolean;
}

// Job execution plan
export interface ExecutionPlan {
  job_id: string;
  website_id: string;
  agents: AgentExecution[];
  dependencies: JobDependency[];
  estimated_duration_minutes: number;
  priority: number;
}

export interface AgentExecution {
  agent_name: string;
  execution_order: number;
  depends_on: string[]; // agent names
  input_mapping: Record<string, string>;
  output_mapping: Record<string, string>;
  timeout_seconds?: number;
}

export interface JobDependency {
  job_id: string;
  dependency_type: 'data' | 'completion' | 'success';
  required_output?: string[];
}

// Orchestrator state
export interface OrchestratorState {
  active_jobs: Map<string, ExecutionPlan>;
  agent_registry: Map<string, IAgent>;
  job_queue: PSEOAgentJob[];
  system_metrics: SystemMetrics;
}

export interface SystemMetrics {
  total_jobs_processed: number;
  average_job_duration_minutes: number;
  success_rate: number;
  active_agents: number;
  queue_length: number;
  system_load: number;
}

// =====================================================
// SPECIALIZED AGENT TYPES
// =====================================================

// Page Discovery Agent types
export interface PageDiscoveryInput extends AgentInput {
  parameters: {
    discovery_methods: ('sitemap' | 'crawl')[];
    max_pages?: number;
    crawl_depth?: number;
    include_subdomains?: boolean;
  };
}

export interface PageDiscoveryResult extends AgentResult {
  data: {
    pages_discovered: PSEOWebsitePage[];
    sitemap_urls: string[];
    total_pages_found: number;
    crawl_errors: string[];
  };
}

// Keyword Research Agent types
export interface KeywordResearchInput extends AgentInput {
  parameters: {
    seed_keywords?: string[];
    competitor_domains?: string[];
    max_keywords?: number;
    data_sources: ('google_planner' | 'ubersuggest' | 'dataforseo')[];
    include_long_tail?: boolean;
  };
}

export interface KeywordResearchResult extends AgentResult {
  data: {
    keywords_found: PSEOKeyword[];
    competitor_keywords: PSEOKeyword[];
    keyword_clusters: KeywordCluster[];
    search_trends: SearchTrend[];
  };
}

export interface KeywordCluster {
  cluster_name: string;
  primary_keyword: string;
  related_keywords: string[];
  search_volume: number;
  difficulty_score: number;
}

export interface SearchTrend {
  keyword: string;
  trend_direction: 'up' | 'down' | 'stable';
  percentage_change: number;
  time_period: string;
}

// Backlink Analysis Agent types
export interface BacklinkAnalysisInput extends AgentInput {
  parameters: {
    data_sources: ('moz' | 'majestic' | 'ahrefs')[];
    include_competitor_analysis?: boolean;
    competitor_domains?: string[];
    max_backlinks?: number;
  };
}

export interface BacklinkAnalysisResult extends AgentResult {
  data: {
    backlinks: PSEOBacklink[];
    domain_metrics: DomainMetrics;
    competitor_backlinks?: PSEOBacklink[];
    link_opportunities: LinkOpportunity[];
  };
}

export interface DomainMetrics {
  domain_authority: number;
  page_authority: number;
  spam_score: number;
  total_backlinks: number;
  referring_domains: number;
  trust_flow?: number;
  citation_flow?: number;
}

export interface LinkOpportunity {
  source_domain: string;
  opportunity_type: 'broken_link' | 'unlinked_mention' | 'competitor_link' | 'guest_post';
  target_page: string;
  anchor_text_suggestion: string;
  difficulty_score: number;
  estimated_value: number;
}

// Content Generation Agent types
export interface ContentGenerationInput extends AgentInput {
  parameters: {
    target_keywords: string[];
    content_types: ('blog' | 'landing' | 'product' | 'guide' | 'faq')[];
    target_word_count?: number;
    tone_of_voice?: string;
    include_images?: boolean;
    ai_model?: string;
  };
}

export interface ContentGenerationResult extends AgentResult {
  data: {
    content_opportunities: PSEOContentOpportunity[];
    generated_content: GeneratedContent[];
    content_calendar: ContentCalendarItem[];
  };
}

export interface GeneratedContent {
  title: string;
  content: string;
  word_count: number;
  target_keywords: string[];
  meta_description: string;
  content_type: string;
  readability_score: number;
  seo_score: number;
}

export interface ContentCalendarItem {
  title: string;
  target_keyword: string;
  content_type: string;
  priority: number;
  estimated_traffic: number;
  suggested_publish_date: string;
  status: 'planned' | 'in_progress' | 'ready' | 'published';
}

// =====================================================
// ERROR HANDLING TYPES
// =====================================================

export class AgentError extends Error {
  public agent_name: string;
  public error_code: string;
  public retry_possible: boolean;
  public metadata?: Record<string, unknown>;

  constructor(
    message: string, 
    agent_name: string, 
    error_code: string, 
    retry_possible: boolean = true,
    metadata?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AgentError';
    this.agent_name = agent_name;
    this.error_code = error_code;
    this.retry_possible = retry_possible;
    this.metadata = metadata;
  }
}

export interface ErrorRecoveryStrategy {
  error_type: string;
  retry_attempts: number;
  backoff_strategy: 'linear' | 'exponential' | 'fixed';
  fallback_action?: string;
  notify_admin?: boolean;
} 