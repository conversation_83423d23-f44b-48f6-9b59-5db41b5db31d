# 🏗️ **LangGraph ScopingAI Agent System - Complete Design**

## 🎯 **System Overview**

### **Current vs. Proposed Architecture**

**Current (Sequential):**
```
Research → Summary → Section Loop → Complete Document
```

**Proposed (Multi-Agent LangGraph):**
```
Coordinator Agent → Specialized Section Agents → Quality Assurance → Document Assembly
```

## 🤖 **Agent Architecture Design**

### **1. Core Agent Types**

#### **🎯 Coordinator Agent**
- **Role**: Orchestrates the entire workflow
- **Responsibilities**:
  - Parse client requirements and template
  - Analyze reference documents
  - Route sections to specialized agents
  - Coordinate dependencies between sections
  - Manage document assembly

#### **📋 Template Analysis Agent**
- **Role**: Understands template structure and requirements
- **Responsibilities**:
  - Parse template sections and their relationships
  - Generate section-specific prompts
  - Determine section dependencies
  - Validate template completeness

#### **📚 Document Intelligence Agent**
- **Role**: Analyzes reference documents
- **Responsibilities**:
  - Extract relevant information per section
  - Create document summaries
  - Identify reusable content patterns
  - Generate retrieval queries

#### **✍️ Section Specialist Agents**
- **Role**: Generate specific document sections
- **Types**:
  - **Executive Summary Agent**
  - **Technical Requirements Agent**
  - **Timeline & Budget Agent**
  - **Methodology Agent**
  - **Risk Assessment Agent**
  - **Generic Section Agent** (fallback)

#### **🔍 Quality Assurance Agent**
- **Role**: Review and improve generated content
- **Responsibilities**:
  - Check section consistency
  - Validate against requirements
  - Ensure professional tone
  - Cross-reference accuracy

#### **📖 Document Assembly Agent**
- **Role**: Combine sections into final document
- **Responsibilities**:
  - Maintain document flow
  - Add transitions between sections
  - Format final output
  - Generate table of contents

## 🔄 **Migration Strategy: Parallel Routes**

### **Route Selection in Frontend**
```typescript
// Add route selection in DocumentOverviewStep.tsx
interface GenerationOptions {
  useNewSystem: boolean;  // Toggle between old/new
  enableAdvancedFeatures: boolean;  // Gradual feature rollout
}

// In DocumentFormContext.tsx
const [generationRoute, setGenerationRoute] = useState<'current' | 'langgraph'>('current');
const [advancedFeaturesEnabled, setAdvancedFeaturesEnabled] = useState(false);
```

### **API Endpoints**
```typescript
// Keep existing endpoint
POST /api/scopingai/proposals/stream  // Current system

// Add new endpoint
POST /api/scopingai/proposals/langgraph  // New LangGraph system

// Feature flag endpoint
GET /api/scopingai/features  // Check which features are enabled
```

## 📱 **Frontend Changes: Minimal & Gradual**

### **Step 1: Client Information (NO CHANGES)**
```typescript
// Keep exactly the same
clientData: {
  name: string,
  industry: string,
  contactPerson: string,
  email: string,
  phone: string
}

projectData: {
  title: string,
  description: string
}
```

### **Step 2: Template Selection (BACKWARD COMPATIBLE)**
```typescript
// Current structure (keep working)
selectedTemplate: {
  id: string,
  name: string,
  sections: string[]  // Simple array - still works
}

// Enhanced structure (optional, for new system)
selectedTemplate: {
  id: string,
  name: string,
  sections: string[] | TemplateSection[],  // Support both formats
  metadata?: {  // Optional - only for new system
    estimatedPages: number,
    complexity: string,
    timeEstimate: string
  }
}
```

### **Step 3: Requirements (BACKWARD COMPATIBLE)**
```typescript
// Current structure (keep working)
requirements: {
  option: "manual" | "upload" | "knowledge",
  description: string,
  selectedKnowledgeDocuments: string[]
}

// Enhanced structure (optional fields for new system)
requirements: {
  option: "manual" | "upload" | "knowledge",
  description: string,
  selectedKnowledgeDocuments: string[],

  // NEW: Optional enhanced fields (only used if generationRoute === 'langgraph')
  projectType?: string,
  complexity?: string,
  timeline?: string,
  budget?: string,
  focusAreas?: string[],
  constraints?: string[],
  mustHave?: string[],
  niceToHave?: string[]
}
```

### **Step 4: AI Prompts (BACKWARD COMPATIBLE)**
```typescript
// Current structure (keep working)
aiPrompts: {
  styleGuidance: string,
  contentFocus: string,
  additionalInstructions: string
}

// Enhanced structure (optional for new system)
aiPrompts: {
  // Keep existing fields
  styleGuidance: string,
  contentFocus: string,
  additionalInstructions: string,

  // NEW: Optional enhanced fields
  style?: {
    tone: string,
    formality: string,
    perspective: string,
    audience: string
  },
  focus?: {
    primaryFocus: string,
    secondaryFocus: string[],
    industryContext: string,
    businessPriorities: string[],
    technicalEmphasis: string[]
  },
  global?: {
    mustInclude: string[],
    mustAvoid: string[],
    keyTerminology: string[],
    complianceRequirements: string[]
  }
}
```

### **Step 5: Document Prompts (OPTIONAL NEW FEATURE)**
```typescript
// Only appears if generationRoute === 'langgraph' AND advancedFeaturesEnabled === true
documentPrompts?: {
  [documentId: string]: {
    extractionPrompt: string,
    queries: DocumentQuery[],
    targetSections: string[],
    priority: "high" | "medium" | "low"
  }
}
```

## 🎛️ **Frontend UI Changes**

### **DocumentOverviewStep.tsx - Add Route Selection**
```typescript
// Add toggle in DocumentOverviewStep
<Card className="mb-6">
  <CardHeader>
    <CardTitle>Generation Method</CardTitle>
    <CardDescription>Choose how to generate your document</CardDescription>
  </CardHeader>
  <CardContent>
    <RadioGroup value={generationRoute} onValueChange={setGenerationRoute}>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="current" id="current" />
        <Label htmlFor="current">Current System (Stable)</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="langgraph" id="langgraph" />
        <Label htmlFor="langgraph">New AI Agents (Beta) 🚀</Label>
      </div>
    </RadioGroup>

    {generationRoute === 'langgraph' && (
      <div className="mt-4">
        <Checkbox
          checked={advancedFeaturesEnabled}
          onCheckedChange={setAdvancedFeaturesEnabled}
        >
          Enable advanced document analysis features
        </Checkbox>
      </div>
    )}
  </CardContent>
</Card>
```

### **RequirementsStep.tsx - Conditional Enhanced Fields**
```typescript
// Only show enhanced fields if using new system
{generationRoute === 'langgraph' && (
  <Card className="mt-6">
    <CardHeader>
      <CardTitle>Enhanced Requirements (Optional)</CardTitle>
      <CardDescription>Provide additional context for better AI generation</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Project Type</Label>
          <Select value={projectType} onValueChange={setProjectType}>
            <option value="web_app">Web Application</option>
            <option value="mobile_app">Mobile Application</option>
            <option value="integration">System Integration</option>
          </Select>
        </div>
        <div>
          <Label>Complexity</Label>
          <Select value={complexity} onValueChange={setComplexity}>
            <option value="simple">Simple</option>
            <option value="medium">Medium</option>
            <option value="complex">Complex</option>
          </Select>
        </div>
      </div>

      <div className="mt-4">
        <Label>Focus Areas</Label>
        <MultiSelect
          options={["security", "performance", "scalability", "usability"]}
          value={focusAreas}
          onChange={setFocusAreas}
        />
      </div>
    </CardContent>
  </Card>
)}
```

### **Knowledge Base Document Selection - Conditional Prompts**
```typescript
// Only show document prompts if advanced features enabled
{generationRoute === 'langgraph' && advancedFeaturesEnabled && (
  <DocumentPromptSection>
    {selectedKnowledgeDocuments.map(docId => (
      <DocumentPromptCard key={docId}>
        <h4>{getDocumentName(docId)}</h4>
        <Textarea
          placeholder="What should we extract from this document?"
          value={documentPrompts[docId]?.extractionPrompt || ''}
          onChange={(e) => updateDocumentPrompt(docId, 'extractionPrompt', e.target.value)}
        />

        <Label>Target Sections</Label>
        <MultiSelect
          options={template.sections}
          value={documentPrompts[docId]?.targetSections || []}
          onChange={(sections) => updateDocumentPrompt(docId, 'targetSections', sections)}
        />
      </DocumentPromptCard>
    ))}
  </DocumentPromptSection>
)}
```
```typescript
aiPrompts: {
  // Document-wide style and tone
  style: {
    tone: "professional" | "conversational" | "technical" | "executive",
    formality: "formal" | "semi-formal" | "casual",
    perspective: "first-person" | "third-person" | "consultative",
    audience: "executives" | "technical-team" | "mixed" | "stakeholders"
  },
  
  // Content focus and emphasis
  focus: {
    primaryFocus: string,           // "Security and compliance"
    secondaryFocus: string[],       // ["Performance", "Scalability"]
    industryContext: string,        // "Healthcare regulations"
    businessPriorities: string[],   // ["Cost reduction", "Risk mitigation"]
    technicalEmphasis: string[]     // ["Cloud-native", "API-first"]
  },
  
  // Document structure preferences
  structure: {
    executiveSummaryStyle: "brief" | "detailed" | "strategic",
    sectionDepth: "high-level" | "detailed" | "comprehensive",
    includeExamples: boolean,
    includeMetrics: boolean,
    includeDiagrams: boolean
  },
  
  // Cross-cutting instructions
  global: {
    mustInclude: string[],          // ["GDPR compliance", "Mobile-first"]
    mustAvoid: string[],            // ["Vendor lock-in", "Legacy tech"]
    keyTerminology: string[],       // ["Patient portal", "EHR integration"]
    complianceRequirements: string[], // ["HIPAA", "SOC 2", "ISO 27001"]
    additionalInstructions: string
  }
}
```

## 🗄️ **Database Schema Design**

### **Templates Table**
```typescript
interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  sections: TemplateSection[];
  metadata: {
    estimatedPages: number;
    complexity: 'simple' | 'medium' | 'complex';
    industry?: string;
  };
}
```

### **Prompts Table**
```typescript
interface PromptTemplate {
  id: string;
  name: string;
  type: 'section' | 'analysis' | 'extraction' | 'quality';
  agentType: string;
  template: string;
  variables: PromptVariable[];
  metadata: {
    version: string;
    lastUpdated: string;
    performance: {
      avgQuality: number;
      avgLength: number;
      successRate: number;
    };
  };
}
```

### **Document Analysis Prompts**
```typescript
interface DocumentAnalysisPrompt {
  id: string;
  sectionType: string;
  extractionQueries: ExtractionQuery[];
  analysisPrompt: string;
}

interface ExtractionQuery {
  question: string;
  targetInfo: string;
  priority: 'high' | 'medium' | 'low';
  extractionPrompt: string;
}
```

## 🔄 **LangGraph Workflow Design**

### **State Management**
```typescript
interface ScopingState {
  // Input data
  client: ClientData;
  template: Template;
  requirements: Requirements;
  referenceDocuments: Document[];
  documentPrompts: DocumentPrompts;
  aiPrompts: AIPrompts;
  
  // Processing state
  documentAnalysis: DocumentAnalysis[];
  sectionPlan: SectionPlan[];
  generatedSections: GeneratedSection[];
  qualityChecks: QualityCheck[];
  
  // Output
  finalDocument: FinalDocument;
  metadata: GenerationMetadata;
}
```

### **Workflow Nodes**

#### **1. Input Validation Node**
```typescript
async function validateInputs(state: ScopingState): Promise<ScopingState> {
  // Validate client data completeness
  // Check template structure
  // Verify reference documents accessibility
  // Return updated state with validation results
}
```

#### **2. Document Analysis Node**
```typescript
async function analyzeDocuments(state: ScopingState): Promise<ScopingState> {
  // For each reference document:
  //   - Use user's extraction prompt
  //   - Answer specific queries
  //   - Extract section-specific info
  //   - Create retrieval indices
}
```

#### **3. Section Planning Node**
```typescript
async function planSections(state: ScopingState): Promise<ScopingState> {
  // Analyze template sections
  // Determine generation order based on dependencies
  // Estimate token requirements
  // Assign specialist agents
}
```

#### **4. Parallel Section Generation**
```typescript
async function generateSections(state: ScopingState): Promise<ScopingState> {
  // Execute sections in parallel where possible
  // Respect dependencies (e.g., Executive Summary after Technical sections)
  // Apply AI prompts globally
  // Use document analysis per section
}
```

#### **5. Quality Assurance Node**
```typescript
async function qualityAssurance(state: ScopingState): Promise<ScopingState> {
  // Cross-section consistency checks
  // Requirement compliance validation
  // Professional tone verification
  // Length and structure validation
}
```

#### **6. Document Assembly Node**
```typescript
async function assembleDocument(state: ScopingState): Promise<ScopingState> {
  // Combine sections in template order
  // Add transitions and flow improvements
  // Generate table of contents
  // Apply final formatting
}
```

## 📄 **100-Page Document Strategy**

### **Section Chunking Approach**
```typescript
interface LargeDocumentStrategy {
  sectionBreakdown: {
    maxSectionLength: 2000; // words
    chunkOverlap: 200; // words
    transitionGeneration: boolean;
  };
  
  parallelGeneration: {
    maxConcurrentSections: 5;
    dependencyManagement: boolean;
    resourceAllocation: TokenAllocation;
  };
  
  qualityMaintenance: {
    crossSectionValidation: boolean;
    consistencyChecks: boolean;
    narrativeFlow: boolean;
  };
}
```

### **Progressive Generation**
```typescript
const generationStrategy = {
  phase1: {
    name: "Foundation",
    sections: ["executive_summary", "project_overview"],
    purpose: "Establish core narrative and context"
  },
  
  phase2: {
    name: "Technical Core", 
    sections: ["requirements", "architecture", "methodology"],
    purpose: "Build technical foundation"
  },
  
  phase3: {
    name: "Implementation",
    sections: ["timeline", "resources", "budget"],
    purpose: "Practical execution details"
  },
  
  phase4: {
    name: "Risk & Quality",
    sections: ["risk_assessment", "quality_assurance", "testing"],
    purpose: "Risk mitigation and quality"
  },
  
  phase5: {
    name: "Appendices",
    sections: ["technical_specs", "references", "glossary"],
    purpose: "Supporting documentation"
  }
};
```

## 📝 **Prompt Engineering Strategy**

### **Template vs. Prompt Separation**

#### **What Goes in Template:**
```typescript
interface TemplateDefinition {
  structure: {
    sections: SectionDefinition[];
    dependencies: SectionDependency[];
    estimatedLength: PageEstimate;
  };

  metadata: {
    industry: string;
    complexity: string;
    targetAudience: string;
    documentType: string;
  };

  formatting: {
    pageLayout: string;
    sectionNumbering: boolean;
    tableOfContents: boolean;
    appendices: string[];
  };
}
```

#### **What Goes in Prompts:**
```typescript
interface PromptDefinition {
  content: {
    systemPrompt: string;
    userPromptTemplate: string;
    exampleOutputs: string[];
  };

  parameters: {
    temperature: number;
    maxTokens: number;
    model: string;
  };

  variables: {
    required: string[];
    optional: string[];
    contextSources: string[];
  };

  qualityChecks: {
    lengthRange: [number, number];
    requiredElements: string[];
    toneValidation: string;
  };
}
```

### **AI Prompts Examples**

#### **Example 1: Healthcare Project**
```typescript
aiPrompts: {
  style: {
    tone: "professional",
    formality: "formal",
    perspective: "consultative",
    audience: "executives"
  },

  focus: {
    primaryFocus: "HIPAA compliance and patient data security",
    secondaryFocus: ["Scalability for 10,000+ patients", "Integration with existing EHR"],
    industryContext: "Healthcare regulations require strict data protection and audit trails",
    businessPriorities: ["Regulatory compliance", "Patient safety", "Operational efficiency"],
    technicalEmphasis: ["End-to-end encryption", "Role-based access control", "Audit logging"]
  },

  global: {
    mustInclude: [
      "HIPAA compliance checklist",
      "Data breach prevention measures",
      "Patient consent management",
      "Integration with Epic/Cerner systems"
    ],
    mustAvoid: [
      "Storing PHI in unsecured locations",
      "Third-party analytics without BAA",
      "Hardcoded credentials"
    ],
    keyTerminology: [
      "Protected Health Information (PHI)",
      "Business Associate Agreement (BAA)",
      "Electronic Health Record (EHR)",
      "Patient portal"
    ],
    complianceRequirements: ["HIPAA", "HITECH", "State privacy laws"],
    additionalInstructions: "Emphasize how each technical decision supports patient care quality and regulatory compliance."
  }
}
```

#### **Example 2: E-commerce Platform**
```typescript
aiPrompts: {
  style: {
    tone: "professional",
    formality: "semi-formal",
    perspective: "consultative",
    audience: "mixed"
  },

  focus: {
    primaryFocus: "High-performance e-commerce with seamless user experience",
    secondaryFocus: ["Mobile optimization", "Payment security", "Inventory management"],
    industryContext: "Competitive e-commerce market requiring fast, secure, and scalable solutions",
    businessPriorities: ["Revenue growth", "Customer retention", "Operational efficiency"],
    technicalEmphasis: ["Sub-second page loads", "99.9% uptime", "PCI DSS compliance"]
  },

  global: {
    mustInclude: [
      "Mobile-first responsive design",
      "Payment gateway integration (Stripe/PayPal)",
      "Real-time inventory tracking",
      "SEO optimization strategy",
      "Analytics and conversion tracking"
    ],
    mustAvoid: [
      "Single points of failure",
      "Slow database queries",
      "Insecure payment processing",
      "Poor mobile experience"
    ],
    keyTerminology: [
      "Conversion rate optimization",
      "Shopping cart abandonment",
      "Payment Card Industry (PCI)",
      "Search engine optimization (SEO)"
    ],
    complianceRequirements: ["PCI DSS", "GDPR", "CCPA"],
    additionalInstructions: "Focus on ROI and business impact. Include performance benchmarks and competitive analysis."
  }
}
```

### **Document-Specific Prompt Examples**

#### **Healthcare Case Study Document**
```typescript
documentPrompt: {
  documentId: "healthcare_case_study_001",
  extractionPrompt: "Extract project timeline, budget estimates, and technical challenges from this healthcare project case study",
  queries: [
    {
      question: "What was the project timeline and key milestones?",
      purpose: "For timeline section planning",
      targetSection: "timeline",
      required: true
    },
    {
      question: "What technical challenges were encountered and how were they solved?",
      purpose: "For risk assessment and technical approach",
      targetSection: "technical_requirements",
      required: true
    },
    {
      question: "What was the final budget and resource allocation?",
      purpose: "For budget estimation",
      targetSection: "budget",
      required: false
    }
  ],
  targetSections: ["timeline", "budget", "technical_requirements", "risk_assessment"],
  priority: "high"
}
```

#### **HIPAA Compliance Guide**
```typescript
documentPrompt: {
  documentId: "hipaa_compliance_guide",
  extractionPrompt: "Extract HIPAA compliance requirements, security measures, and regulatory constraints",
  queries: [
    {
      question: "What are the mandatory HIPAA compliance requirements for patient data systems?",
      purpose: "For compliance section and technical requirements",
      targetSection: "compliance",
      required: true
    },
    {
      question: "What security measures must be implemented?",
      purpose: "For security architecture",
      targetSection: "security",
      required: true
    },
    {
      question: "What are the audit and reporting requirements?",
      purpose: "For ongoing maintenance section",
      targetSection: "maintenance",
      required: false
    }
  ],
  targetSections: ["compliance", "security", "technical_requirements"],
  priority: "high"
}
```

## 🔧 **How Agents Use Prompts**

### **Document Intelligence Agent**
```typescript
async function analyzeDocument(doc: Document, prompt: DocumentPrompt) {
  const extractionResult = await llm.invoke(`
    ${prompt.extractionPrompt}

    DOCUMENT CONTENT:
    ${doc.content}

    SPECIFIC QUESTIONS:
    ${prompt.queries.map(q => `- ${q.question} (for ${q.targetSection})`).join('\n')}

    TARGET SECTIONS: ${prompt.targetSections.join(', ')}

    Extract relevant information and structure it for the target sections.
  `);

  return {
    documentId: doc.id,
    extractedInfo: extractionResult,
    targetSections: prompt.targetSections,
    priority: prompt.priority,
    queries: prompt.queries.map(q => ({
      question: q.question,
      answer: extractAnswerFromResult(extractionResult, q.question),
      targetSection: q.targetSection
    }))
  };
}
```

### **Section Writer Agents**
```typescript
async function generateSection(sectionName: string, context: SectionContext) {
  // Get document analysis relevant to this section
  const relevantDocs = context.documentAnalysis.filter(
    analysis => analysis.targetSections.includes(sectionName)
  );

  // Apply global AI prompts
  const globalStyle = context.aiPrompts.style;
  const globalFocus = context.aiPrompts.focus;
  const globalRequirements = context.aiPrompts.global;

  // Build section prompt with all context
  const sectionPrompt = `
    Generate ${sectionName} section for ${context.client.name}

    STYLE REQUIREMENTS:
    - Tone: ${globalStyle.tone}
    - Formality: ${globalStyle.formality}
    - Audience: ${globalStyle.audience}

    CONTENT FOCUS:
    - Primary: ${globalFocus.primaryFocus}
    - Secondary: ${globalFocus.secondaryFocus.join(', ')}
    - Industry Context: ${globalFocus.industryContext}

    MUST INCLUDE: ${globalRequirements.mustInclude.join(', ')}
    MUST AVOID: ${globalRequirements.mustAvoid.join(', ')}
    KEY TERMS: ${globalRequirements.keyTerminology.join(', ')}

    CLIENT: ${context.client.name} (${context.client.industry})
    PROJECT: ${context.project.description}
    REQUIREMENTS: ${context.requirements.description}

    DOCUMENT INSIGHTS:
    ${relevantDocs.map(doc => `
      From ${doc.documentName}:
      ${doc.extractedInfo}

      Specific Q&A:
      ${doc.queries.filter(q => q.targetSection === sectionName)
        .map(q => `Q: ${q.question}\nA: ${q.answer}`).join('\n')}
    `).join('\n\n')}

    ${globalRequirements.additionalInstructions}

    Generate comprehensive ${sectionName} section using all context above.
  `;

  return await llm.invoke(sectionPrompt);
}
```

## 🔧 **Backend Implementation: Parallel Routes**

### **Current Route (Keep Unchanged)**
```typescript
// Keep existing endpoint exactly as is
POST /api/scopingai/proposals/stream
// File: server/features/scopingAi/routes/scopingAi.ts (no changes)
```

### **New LangGraph Route**
```typescript
// Add new endpoint
POST /api/scopingai/proposals/langgraph

// Implementation in same file or new file
async function handleLangGraphGeneration(req: Request, res: Response) {
  const { generationRoute, advancedFeaturesEnabled, ...existingData } = req.body;

  if (generationRoute === 'current') {
    // Route to existing implementation
    return handleCurrentGeneration(req, res);
  }

  // New LangGraph implementation
  const langGraphWorkflow = createScopingWorkflow();
  const result = await langGraphWorkflow.invoke({
    client: existingData.client,
    template: existingData.template,
    requirements: existingData.requirements,
    aiPrompts: existingData.aiPrompts,
    documentPrompts: advancedFeaturesEnabled ? existingData.documentPrompts : {},
    useAdvancedFeatures: advancedFeaturesEnabled
  });

  return streamLangGraphResult(result, res);
}
```

### **Feature Flag System**
```typescript
// Add feature flags
interface FeatureFlags {
  langGraphEnabled: boolean;
  advancedDocumentAnalysis: boolean;
  enhancedPrompts: boolean;
  parallelGeneration: boolean;
}

// Environment-based feature flags
const features: FeatureFlags = {
  langGraphEnabled: process.env.LANGGRAPH_ENABLED === 'true',
  advancedDocumentAnalysis: process.env.ADVANCED_DOCS_ENABLED === 'true',
  enhancedPrompts: process.env.ENHANCED_PROMPTS_ENABLED === 'true',
  parallelGeneration: process.env.PARALLEL_GEN_ENABLED === 'true'
};

// Feature flag endpoint
GET /api/scopingai/features
```

### **Data Compatibility Layer**
```typescript
// Transform current data format to LangGraph format
function transformToLangGraphInput(currentInput: any): LangGraphInput {
  return {
    client: currentInput.client,  // No change
    project: currentInput.project,  // No change
    template: {
      ...currentInput.template,
      sections: Array.isArray(currentInput.template.sections[0])
        ? currentInput.template.sections  // Already enhanced
        : currentInput.template.sections.map(name => ({ // Convert simple to enhanced
            id: generateId(),
            name,
            order: index,
            required: true,
            estimatedPages: 2,
            agentType: 'generic',
            dependencies: []
          }))
    },
    requirements: {
      ...currentInput.requirements,
      // Add defaults for optional fields
      projectType: currentInput.requirements.projectType || 'web_app',
      complexity: currentInput.requirements.complexity || 'medium',
      timeline: currentInput.requirements.timeline || 'standard'
    },
    aiPrompts: transformAiPrompts(currentInput.aiPrompts),
    documentPrompts: currentInput.documentPrompts || {}
  };
}

function transformAiPrompts(currentPrompts: any) {
  return {
    // Keep existing format for backward compatibility
    styleGuidance: currentPrompts.styleGuidance,
    contentFocus: currentPrompts.contentFocus,
    additionalInstructions: currentPrompts.additionalInstructions,

    // Add enhanced format if available
    style: currentPrompts.style || {
      tone: inferToneFromStyleGuidance(currentPrompts.styleGuidance),
      formality: 'professional',
      perspective: 'consultative',
      audience: 'mixed'
    },
    focus: currentPrompts.focus || {
      primaryFocus: currentPrompts.contentFocus,
      secondaryFocus: [],
      industryContext: '',
      businessPriorities: [],
      technicalEmphasis: []
    }
  };
}
```

## 🚀 **Implementation Roadmap: Gradual Migration**

### **Phase 1: Parallel Infrastructure (Weeks 1-2)**
1. ✅ Keep existing system 100% unchanged
2. 🆕 Add new LangGraph endpoint alongside existing
3. 🆕 Implement feature flag system
4. 🆕 Create data compatibility layer
5. 🆕 Add route selection UI toggle

### **Phase 2: Basic LangGraph (Weeks 3-4)**
1. 🆕 Implement basic LangGraph workflow
2. 🆕 Create simple agent system (1-2 agents)
3. 🆕 Test with existing data format
4. ✅ Ensure backward compatibility
5. 🆕 A/B testing framework

### **Phase 3: Enhanced Features (Weeks 5-6)**
1. 🆕 Add document-specific prompts (optional)
2. 🆕 Implement enhanced AI prompts (optional)
3. 🆕 Add advanced requirements fields (optional)
4. ✅ All features behind feature flags
5. 🆕 User feedback collection

### **Phase 4: Full Agent System (Weeks 7-8)**
1. 🆕 Complete multi-agent implementation
2. 🆕 Parallel section generation
3. 🆕 Quality assurance agents
4. 🆕 Performance optimization
5. ✅ Side-by-side comparison tools

### **Phase 5: Production Migration (Weeks 9-10)**
1. 📊 Performance comparison analysis
2. 📊 Quality comparison analysis
3. 🔄 Gradual user migration (opt-in)
4. 📊 Monitor and optimize
5. 🔄 Eventually deprecate old system (if successful)

## 🎯 **Migration Benefits**

### **Zero Risk Migration**
- ✅ Existing system continues working unchanged
- ✅ Users can choose which system to use
- ✅ Easy rollback if issues occur
- ✅ Gradual feature adoption

### **User Experience**
- ✅ Familiar interface with optional enhancements
- ✅ Progressive disclosure of advanced features
- ✅ Clear benefits communication
- ✅ Smooth transition path

### **Development Benefits**
- ✅ Test new system with real users
- ✅ Compare performance side-by-side
- ✅ Iterate based on feedback
- ✅ Maintain system stability

## 🎯 **Key Benefits**

1. **Faster Generation**: Multiple agents work in parallel
2. **Better Quality**: Each agent is specialized for specific tasks
3. **Scalable**: Can handle 100+ page documents efficiently
4. **Flexible**: Easy to add new section types and agents
5. **User Control**: Document-specific prompts give precise control
6. **Consistent**: Global AI prompts ensure document-wide consistency
7. **Maintainable**: Clear separation of concerns between agents

This design provides a comprehensive, scalable system that can generate high-quality scoping documents while giving users complete control over the process.
