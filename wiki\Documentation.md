# 🔍 Comprehensive Codebase Documentation Prompt

You are an expert technical documentation specialist. I need you to analyze this codebase file-by-file and create comprehensive documentation covering:

## 📋 DOCUMENTATION SCOPE

### 1. Architecture Overview
- System architecture patterns (MVC, microservices, monolith, etc.)
- High-level component relationships and data flow
- Technology stack and framework choices
- Deployment architecture and infrastructure
- Security architecture and authentication flows

### 2. Code Structure & Organization
- Directory structure and file organization patterns
- Module/package organization and dependencies
- Naming conventions and coding standards
- Design patterns used throughout the codebase
- Code layering (presentation, business logic, data access)

### 3. Database Design
- Database schema and table relationships
- Entity-relationship diagrams
- Data models and their relationships
- Migration strategies and versioning
- Indexing strategies and performance considerations
- Data validation and constraints

### 4. API & Interface Documentation
- REST/GraphQL endpoint documentation
- Request/response schemas and examples
- Authentication and authorization mechanisms
- Rate limiting and error handling
- API versioning strategy
- WebSocket connections (if applicable)

### 5. Integration Points
- External service integrations and dependencies
- Third-party APIs and SDKs used
- Message queues and event systems
- File storage and CDN integrations
- Payment gateways and external services
- Webhook implementations

### 6. Data Flow & Business Logic
- User journey and workflow documentation
- Business rule implementations
- Data transformation processes
- Background job processing
- Caching strategies and implementations
- State management patterns

### 7. Configuration & Environment
- Environment variable documentation
- Configuration file structures
- Feature flags and toggles
- Deployment configurations
- Monitoring and logging setup
- Performance optimization settings

### 8. Testing Strategy
- Test coverage and testing patterns
- Unit, integration, and e2e test structures
- Mock and fixture strategies
- Testing utilities and helpers
- CI/CD pipeline documentation

### 9. Development Workflow
- Local development setup instructions
- Build and deployment processes
- Code review guidelines
- Git workflow and branching strategy
- Development tools and utilities

### 10. Performance & Monitoring
- Performance bottlenecks and optimizations
- Monitoring and alerting setup
- Logging strategies and log levels
- Error tracking and debugging approaches
- Scalability considerations

## 📝 OUTPUT FORMAT

For each file/component, provide:

1. **Purpose & Responsibility**: What this file/component does
2. **Dependencies**: What it depends on and what depends on it
3. **Key Functions/Methods**: Main functionality with brief descriptions
4. **Data Structures**: Important interfaces, types, or models
5. **Configuration**: Any configuration options or environment variables
6. **Integration Points**: How it connects to other parts of the system
7. **Error Handling**: How errors are managed and propagated
8. **Performance Notes**: Any performance considerations or optimizations
9. **Security Considerations**: Authentication, authorization, data validation
10. **Testing**: How this component is tested

## 🎯 ANALYSIS INSTRUCTIONS

1. **Start with the entry points** (main.js, app.js, index.html, etc.)
2. **Map the request/response flow** from user interaction to data persistence
3. **Identify core business logic** and how it's implemented
4. **Document data models** and their relationships
5. **Trace integration points** and external dependencies
6. **Note configuration patterns** and environment-specific settings
7. **Highlight security implementations** and potential vulnerabilities
8. **Document error handling** and logging strategies
9. **Identify performance optimizations** and potential bottlenecks
10. **Note testing strategies** and coverage gaps

## 📊 DELIVERABLES

Create a structured wiki with:
- **Architecture Overview** (high-level system design)
- **Getting Started Guide** (setup and development)
- **API Documentation** (endpoints and schemas)
- **Database Documentation** (schema and relationships)
- **Component Documentation** (detailed file-by-file analysis)
- **Integration Guide** (external services and APIs)
- **Deployment Guide** (build and deployment processes)
- **Troubleshooting Guide** (common issues and solutions)

## 🔍 SPECIAL ATTENTION TO

- **Security vulnerabilities** and authentication flows
- **Performance bottlenecks** and optimization opportunities
- **Scalability limitations** and architectural constraints
- **Technical debt** and areas needing refactoring
- **Missing documentation** and unclear code sections
- **Configuration complexity** and environment dependencies
- **Testing gaps** and quality assurance issues
- **Integration fragility** and external dependencies

Please analyze each file systematically and build comprehensive documentation that would help a new developer understand and contribute to this codebase effectively.

---

## 🎯 Additional Specific Prompts for Different Aspects

### For Database Analysis
```
Focus on the database layer: analyze all migration files, model definitions, relationships, indexes, and data access patterns. Document the data flow from API endpoints to database operations.
```

### For API Documentation
```
Document all API endpoints, their request/response schemas, authentication requirements, error responses, and provide example requests. Include rate limiting and versioning information.
```

### For Frontend Architecture
```
Analyze the frontend architecture: component hierarchy, state management, routing, API integration, styling approach, and build process. Document the user interaction flow.
```

### For Integration Points
```
Identify and document all external integrations: third-party APIs, webhooks, message queues, file storage, payment systems, and authentication providers. Include configuration and error handling.
```

---

## 📋 Usage Instructions

1. **Start with high-level analysis** using the main prompt
2. **Use specific prompts** for detailed analysis of particular areas
3. **Go file-by-file** through the codebase systematically
4. **Build documentation incrementally** as you analyze each component
5. **Cross-reference** between different sections to ensure consistency
6. **Update and refine** documentation as you discover new patterns

This comprehensive approach will give you thorough documentation covering all aspects of your codebase!
