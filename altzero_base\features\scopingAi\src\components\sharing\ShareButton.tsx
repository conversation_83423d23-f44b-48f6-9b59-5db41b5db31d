import React, { useState } from "react";
import { Button } from "../../../../base/components/ui/button";
import { Share2, Users, Link2, Settings } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../../base/components/ui/dropdown-menu";
import { DocumentSharingModal } from "./DocumentSharingModal";

interface ShareButtonProps {
  documentId: string;
  documentTitle: string;
  canManage?: boolean;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
}

export const ShareButton: React.FC<ShareButtonProps> = ({
  documentId,
  documentTitle,
  canManage = true,
  variant = "outline",
  size = "sm",
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"share" | "link" | "manage">(
    "share"
  );

  const handleOpenModal = (mode: "share" | "link" | "manage") => {
    setModalMode(mode);
    setModalOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant={variant} size={size}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => handleOpenModal("share")}>
            <Users className="h-4 w-4 mr-2" />
            Share with people
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleOpenModal("link")}>
            <Link2 className="h-4 w-4 mr-2" />
            Create link
          </DropdownMenuItem>
          {canManage && (
            <DropdownMenuItem onClick={() => handleOpenModal("manage")}>
              <Settings className="h-4 w-4 mr-2" />
              Manage access
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <DocumentSharingModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        documentId={documentId}
        documentTitle={documentTitle}
        canManage={canManage}
        initialTab={modalMode}
      />
    </>
  );
};
