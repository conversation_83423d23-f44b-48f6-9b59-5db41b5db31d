import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { But<PERSON> } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { createTeam } from "../../services/teamService";
import { getUserOrganizations } from "../../services/organizationService";
import { toast } from "../../hooks/use-toast";
import { Users } from "lucide-react";

const formSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Team name must be at least 2 characters." }),
  description: z.string().optional(),
  organizationId: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface CreateTeamDialogProps {
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

export function CreateTeamDialog({
  onSuccess,
  trigger,
}: CreateTeamDialogProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [loadingOrgs, setLoadingOrgs] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      organizationId: "independent",
    },
  });

  useEffect(() => {
    if (open) {
      loadOrganizations();
    }
  }, [open]);

  const loadOrganizations = async () => {
    try {
      setLoadingOrgs(true);
      const orgData = await getUserOrganizations();
      setOrganizations(orgData.organizations || []);
    } catch (error) {
      console.error("Error loading organizations:", error);
    } finally {
      setLoadingOrgs(false);
    }
  };

  async function onSubmit(values: FormValues) {
    setIsLoading(true);
    try {
      const teamId = await createTeam(
        values.name,
        values.description,
        values.organizationId === "independent"
          ? undefined
          : values.organizationId
      );

      if (teamId) {
        toast({
          title: "Team created",
          description: `${values.name} has been created successfully.`,
        });

        if (onSuccess) {
          onSuccess();
        }

        setOpen(false);
        form.reset({
          name: "",
          description: "",
          organizationId: "independent",
        });
      }
    } catch (error) {
      console.error("Error creating team:", error);
      toast({
        title: "Error",
        description: "Failed to create team. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Users className="w-4 h-4 mr-2" />
            Create Team
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Create Team</span>
          </DialogTitle>
          <DialogDescription>
            Create a new team to collaborate with specific members.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Team Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter team name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter team description (optional)"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="organizationId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={loadingOrgs}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select organization (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="independent">
                        Independent Team
                      </SelectItem>
                      {organizations.map((org) => (
                        <SelectItem key={org.id} value={org.id}>
                          {org.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Creating..." : "Create Team"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
