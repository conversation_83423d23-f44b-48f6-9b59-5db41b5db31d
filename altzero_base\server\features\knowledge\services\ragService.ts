import { Chat<PERSON><PERSON>AI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { langGraphService } from "./langGraphService";
import { pineconeService } from "./pineconeService";

export interface ChatRequest {
  message: string;
  sessionId?: string;
  selectedDocuments?: string[];
  conversationHistory?: Array<{ role: string; content: string }>;
  userId?: string;
  systemMessage?: string;
}

export interface ChatResponse {
  message: string;
  sources: Array<{
    id: string;
    content: string;
    score: number;
    metadata: any;
  }>;
  metadata: {
    sessionId?: string;
    processingTime: number;
    model: string;
    userId?: string;
  };
}

class RAGService {
  private llm: ChatOpenAI | null = null;
  private isEnabled: boolean = false;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn("OPENAI_API_KEY not found. RAG service will be disabled.");
      this.isEnabled = false;
    } else {
      try {
        this.llm = new ChatOpenAI({
          openAIApiKey: process.env.OPENAI_API_KEY,
          modelName: process.env.OPENAI_MODEL || "gpt-4",
          temperature: 0.7,
          maxTokens: 2048,
        });
        this.isEnabled = true;
      } catch (error) {
        console.error("Failed to initialize RAG service:", error);
        this.isEnabled = false;
      }
    }
  }

  async generateResponse(request: ChatRequest): Promise<ChatResponse> {
    if (!this.isEnabled) {
      throw new Error(
        "RAG service not available - OpenAI API key not configured"
      );
    }

    const startTime = Date.now();

    try {
      console.log(
        `Generating RAG response for user: ${request.userId || "anonymous"}`
      );

      // Use LangGraph for RAG processing with user filter
      const ragResponse = await langGraphService.performRAG({
        query: request.message,
        selectedDocuments: request.selectedDocuments,
        systemMessage:
          request.systemMessage ||
          "You are a helpful AI assistant with access to a comprehensive knowledge base.",
        temperature: 0.7,
        maxTokens: 2048,
        userFilter: request.userId ? { userId: request.userId } : undefined,
      });

      const processingTime = Date.now() - startTime;

      return {
        message: ragResponse.answer,
        sources: ragResponse.sources,
        metadata: {
          sessionId: request.sessionId,
          processingTime,
          model: this.llm?.modelName || "gpt-4",
          userId: request.userId,
        },
      };
    } catch (error) {
      console.error("Error generating RAG response:", error);
      throw error;
    }
  }

  async searchDocuments(
    query: string,
    userId?: string,
    maxResults: number = 10
  ): Promise<
    Array<{
      id: string;
      text: string;
      score: number;
      metadata: any;
    }>
  > {
    try {
      console.log(`Searching documents for user: ${userId || "anonymous"}`);

      // Use LangGraph search with user filter
      const results = await langGraphService.searchDocuments(query, {
        topK: maxResults,
        minScore: 0.5,
        filter: userId ? { userId } : undefined,
      });

      return results.map((result) => ({
        id: result.id,
        text: result.text,
        score: result.score,
        metadata: result.metadata,
      }));
    } catch (error) {
      console.error("Error searching documents:", error);
      return [];
    }
  }

  async healthCheck(): Promise<boolean> {
    if (!this.isEnabled) {
      return false;
    }

    try {
      // Test LangGraph and Pinecone connectivity
      const langGraphHealthy = await langGraphService.healthCheck();
      const pineconeHealthy = await pineconeService.healthCheck();

      return langGraphHealthy && pineconeHealthy;
    } catch (error) {
      console.error("RAG service health check failed:", error);
      return false;
    }
  }

  // Getter to check if RAG service is enabled
  get enabled(): boolean {
    return this.isEnabled;
  }
}

export const ragService = new RAGService();
