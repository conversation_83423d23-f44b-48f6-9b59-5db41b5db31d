// API base URL and key for authentication
export const API_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";
export const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "scopingai";

// File upload configuration
export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
export const ALLOWED_FILE_TYPES = [
  "application/pdf",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/msword", // .doc
  "text/plain",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
  "application/vnd.ms-excel", // .xls
  "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
  "application/vnd.ms-powerpoint", // .ppt
  "text/markdown",
];

// Document status types
export const STATUS_TYPES = {
  DRAFT: "draft",
  IN_PROGRESS: "in_progress",
  UNDER_REVIEW: "under_review",
  APPROVED: "approved",
  COMPLETED: "completed",
  ARCHIVED: "archived",
};

// Chat configuration
export const MAX_CHAT_HISTORY = 10;
export const MAX_CHAT_LENGTH = 4000;

// API endpoints
export const ENDPOINTS = {
  // Knowledge base
  UPLOAD_FILE: "/api/scopingai/documents",
  GET_DOCUMENTS: "/api/scopingai/knowledge/documents",
  GET_DOCUMENT: "/api/scopingai/knowledge/documents/:id",
  DELETE_DOCUMENT: "/api/scopingai/knowledge/documents/:id",
  SEARCH_DOCUMENTS: "/api/scopingai/knowledge/search",

  // Chat
  CHAT_COMPLETION: "/api/scopingai/chat/completion",
  CHAT_HISTORY: "/api/scopingai/chat/history",
  SAVE_CHAT: "/api/scopingai/chat/save",

  // Progressive scoping
  PROGRESSIVE_SCOPING: "/api/scopingai/scoping/progressive",
  GENERATE_SECTION: "/api/scopingai/scoping/section",
  SAVE_SECTION: "/api/scopingai/scoping/section/:id",
  GET_SECTIONS: "/api/scopingai/scoping/sections",

  // Templates
  GET_TEMPLATES: "/api/templates",
  GET_TEMPLATE: "/api/templates/:id",
  SAVE_TEMPLATE: "/api/templates/:id",
  CREATE_TEMPLATE: "/api/templates",
  DELETE_TEMPLATE: "/api/templates/:id",

  // Clients
  GET_CLIENTS: "/api/clients",
  GET_CLIENT: "/api/clients/:id",
  SAVE_CLIENT: "/api/clients/:id",
  CREATE_CLIENT: "/api/clients",
  DELETE_CLIENT: "/api/clients/:id",
};
