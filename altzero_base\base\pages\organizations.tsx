import React, { useEffect, useState } from "react";
import { But<PERSON> } from "../components/ui/button";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "../components/ui/tabs";
import { CreateOrganizationDialog } from "../components/organization/CreateOrganizationDialog";
import { OrganizationCard } from "../components/organization/OrganizationCard";
import { InvitationsList } from "../components/invitation/InvitationsList";
import {
  getUserOrganizations,
  getUserPendingOrganizationInvitations,
} from "../services/organizationService";
import { getUserPendingGroupInvitations } from "../services/teamService";
import {
  Organization,
  OrganizationInvitation,
  MemberRole,
} from "../types/organization";
import { GroupInvitation } from "../types/team";
import { toast } from "../hooks/use-toast";

export default function OrganizationsPage() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [memberRoles, setMemberRoles] = useState<Record<string, MemberRole>>(
    {}
  );
  const [orgInvitations, setOrgInvitations] = useState<
    OrganizationInvitation[]
  >([]);
  const [groupInvitations, setGroupInvitations] = useState<GroupInvitation[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("organizations");

  useEffect(() => {
    fetchOrganizationsAndInvitations();
  }, []);

  const fetchOrganizationsAndInvitations = async () => {
    setIsLoading(true);
    try {
      // Fetch organizations
      const { organizations, membership } = await getUserOrganizations();
      setOrganizations(organizations);

      // Map roles to organizations for easy lookup
      const roleMap: Record<string, MemberRole> = {};
      membership.forEach((member) => {
        roleMap[member.organisation_id] = member.role as MemberRole;
      });
      setMemberRoles(roleMap);

      // Fetch pending invitations
      const orgInvites = await getUserPendingOrganizationInvitations();
      setOrgInvitations(orgInvites);

      const groupInvites = await getUserPendingGroupInvitations();
      setGroupInvitations(groupInvites);
    } catch (error) {
      console.error("Error fetching organizations and invitations:", error);
      toast({
        title: "Error",
        description: "Failed to load organizations. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOrganizationCreated = () => {
    fetchOrganizationsAndInvitations();
  };

  const handleInvitationAccepted = () => {
    fetchOrganizationsAndInvitations();
  };

  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Organizations</h1>
        <CreateOrganizationDialog onSuccess={handleOrganizationCreated} />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="organizations">My Organizations</TabsTrigger>
          <TabsTrigger value="invitations">
            Invitations
            {(orgInvitations.length > 0 || groupInvitations.length > 0) && (
              <span className="ml-2 px-2 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
                {orgInvitations.length + groupInvitations.length}
              </span>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="organizations">
          {isLoading ? (
            <div className="flex justify-center p-10">
              <div className="animate-spin w-8 h-8 border-2 border-primary rounded-full border-t-transparent"></div>
            </div>
          ) : organizations.length === 0 ? (
            <div className="text-center p-10 bg-muted/40 rounded-lg">
              <h3 className="text-lg font-medium mb-2">No organizations yet</h3>
              <p className="text-muted-foreground mb-4">
                Create an organization to collaborate with your team
              </p>
              <CreateOrganizationDialog
                onSuccess={handleOrganizationCreated}
                trigger={<Button>Create Your First Organization</Button>}
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {organizations.map((org) => (
                <OrganizationCard
                  key={org.id}
                  organization={org}
                  role={memberRoles[org.id] || "member"}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="invitations">
          <InvitationsList
            organizationInvitations={orgInvitations}
            groupInvitations={groupInvitations}
            onAcceptOrganization={handleInvitationAccepted}
            onAcceptGroup={handleInvitationAccepted}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
