import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
});

export async function generateContent(
  company: string,
  documentType: string,
  client: string,
  objective: string,
  sectionTitle: string
): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a professional business document writer. Write content that is clear, professional, and engaging."
        },
        {
          role: "user",
          content: `Write content for a ${documentType} section titled "${sectionTitle}".
            Context:
            - Company: ${company}
            - Client: ${client}
            - Objective: ${objective}
            
            The content should be specific to the section title and context provided.
            Keep it concise but informative, around 2-3 paragraphs.`
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    });

    return response.choices[0]?.message?.content || "Content generation failed. Please try again.";
  } catch (error) {
    console.error('Error generating content:', error);
    return "Failed to generate content. Please check your API key and try again.";
  }
}