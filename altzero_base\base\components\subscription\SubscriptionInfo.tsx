import React from "react";
import { useSubscription } from "../../contextapi/SubscriptionContext";
import { Badge } from "../ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Progress } from "../ui/progress";
import {
  SubscriptionStatus,
  BillingCycle,
  SubscriptionRecord,
} from "../../types/subscription";

const statusColors = {
  [SubscriptionStatus.ACTIVE]: "bg-green-500",
  [SubscriptionStatus.TRIALING]: "bg-blue-500",
  [SubscriptionStatus.PAST_DUE]: "bg-yellow-500",
  [SubscriptionStatus.CANCELED]: "bg-red-500",
  [SubscriptionStatus.INCOMPLETE]: "bg-orange-500",
  [SubscriptionStatus.INCOMPLETE_EXPIRED]: "bg-red-500",
  [SubscriptionStatus.UNPAID]: "bg-red-500",
};

const billingCycleLabels = {
  [BillingCycle.MONTHLY]: "Monthly",
  [BillingCycle.YEARLY]: "Yearly",
};

export interface SubscriptionInfoProps {
  subscription?: SubscriptionRecord | null;
  className?: string;
}

export function SubscriptionInfo({
  subscription,
  className,
}: SubscriptionInfoProps) {
  const {
    subscriptionSummary,
    isLoading,
    hasActiveSubscription,
    getFormattedNextBillingDate,
    getFormattedSubscriptionStatus,
  } = useSubscription();

  const activeSubscription = subscription || hasActiveSubscription();
  const statusText = getFormattedSubscriptionStatus();
  const nextBillingDate = getFormattedNextBillingDate();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Subscription</CardTitle>
          <CardDescription>Loading subscription information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!activeSubscription) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Subscription</CardTitle>
          <CardDescription>
            You don't have an active subscription
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Subscribe to a plan to get access to all features.
          </p>
        </CardContent>
      </Card>
    );
  }

  const sub = subscription;
  if (!sub || !sub.plan) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{sub.plan.name}</CardTitle>
            <CardDescription>
              {billingCycleLabels[sub.billing_cycle]} Plan
            </CardDescription>
          </div>
          <Badge className={statusColors[sub.status]}>{statusText}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        {subscriptionSummary && (
          <div className="space-y-4 mt-2">
            <h4 className="text-sm font-medium">Resource Usage</h4>
            {subscriptionSummary.resources.map((resource) => (
              <div key={resource.resource_type_id} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{resource.name}</span>
                  <span>
                    {resource.current_usage} / {resource.total_limit}{" "}
                    {resource.unit_label}
                  </span>
                </div>
                <Progress value={resource.percentage_used} className="h-2" />
              </div>
            ))}
          </div>
        )}

        <div className="border-t border-border mt-4 pt-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Billing Cycle</p>
              <p className="font-medium">
                {billingCycleLabels[sub.billing_cycle]}
              </p>
            </div>
            {nextBillingDate && (
              <div>
                <p className="text-muted-foreground">Next Billing Date</p>
                <p className="font-medium">{nextBillingDate}</p>
              </div>
            )}
            {sub.cancel_at_period_end && (
              <div className="col-span-2">
                <p className="text-destructive font-medium">
                  Your subscription will end after the current billing period.
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
