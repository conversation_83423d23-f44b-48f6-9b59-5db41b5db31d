import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Settings as SettingsIcon,
  Users,
  Building,
  CreditCard,
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  Database,
  Key,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Crown,
  Zap,
  Check,
  Star,
  Plus,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { useUser } from "../contextapi/UserContext";
import {
  getUserOrganizations,
  createOrganization,
} from "../services/organizationService";
import { getUserTeams, TeamWithDetails } from "../services/teamService";
import {
  getAvailablePlans,
  getUserSubscription,
  getInvoices,
} from "../services/subscriptionService";
import { useToast } from "../hooks/use-toast";
import { CreateOrganizationDialog } from "../components/organization/CreateOrganizationDialog";
import { CreateTeamDialog } from "../components/team/CreateTeamDialog";
import { OrganizationManageDialog } from "../components/organization/OrganizationManageDialog";
import { TeamManageDialog } from "../components/team/TeamManageDialog";
import { InvitationNotifications } from "../components/invitation/InvitationNotifications";

type SettingsTab =
  | "profile"
  | "organizations"
  | "subscription"
  | "preferences"
  | "security";

interface Organization {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  created_by: string;
  updated_at: string;
  role: string;
}

interface Plan {
  id: string;
  name: string;
  description?: string;
  tier_level: number;
  monthly_price_id?: string;
  yearly_price_id?: string;
  features: any;
}

interface Subscription {
  id: string;
  owner_id: string;
  owner_type: string;
  plan_id: string;
  status: string;
  billing_cycle: string;
  current_period_start?: string;
  current_period_end?: string;
  plan?: Plan;
}

interface Invoice {
  id: string;
  amount: number;
  currency: string;
  status: string;
  invoice_date: string;
  due_date?: string;
  paid_date?: string;
}

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SettingsTab>("profile");

  const tabs = [
    {
      id: "profile" as SettingsTab,
      label: "Profile",
      icon: User,
      description: "Manage your personal information",
    },
    {
      id: "organizations" as SettingsTab,
      label: "Organizations & Teams",
      icon: Building,
      description: "Manage your organizations and teams",
    },
    {
      id: "subscription" as SettingsTab,
      label: "Subscription",
      icon: CreditCard,
      description: "Billing and subscription management",
    },
    {
      id: "preferences" as SettingsTab,
      label: "Preferences",
      icon: Palette,
      description: "Customize your experience",
    },
    {
      id: "security" as SettingsTab,
      label: "Security",
      icon: Shield,
      description: "Privacy and security settings",
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return <ProfileSettings />;
      case "organizations":
        return <OrganizationsSettings />;
      case "subscription":
        return <SubscriptionSettings />;
      case "preferences":
        return <PreferencesSettings />;
      case "security":
        return <SecuritySettings />;
      default:
        return <ProfileSettings />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4 mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent-primary bg-clip-text text-transparent">
            Settings
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Manage your account, preferences, and integrations
          </p>
        </motion.div>

        {/* Invitation Notifications */}
        <InvitationNotifications />

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mt-6">
          {/* Sidebar Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-1"
          >
            <div className="dashboard-card p-4 sticky top-6">
              <h3 className="font-semibold mb-4 flex items-center space-x-2">
                <SettingsIcon className="w-5 h-5" />
                <span>Settings</span>
              </h3>
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                        activeTab === tab.id
                          ? "bg-primary text-primary-foreground shadow-md"
                          : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="w-4 h-4 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium">{tab.label}</p>
                          <p className="text-xs opacity-75 truncate">
                            {tab.description}
                          </p>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-3"
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderTabContent()}
              </motion.div>
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// Profile Settings Component
const ProfileSettings: React.FC = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    full_name: user?.user_metadata?.full_name || "",
    phone: user?.phone || "",
    location: user?.user_metadata?.location || "",
    bio: user?.user_metadata?.bio || "",
  });

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.user_metadata?.full_name || "",
        phone: user.phone || "",
        location: user.user_metadata?.location || "",
        bio: user.user_metadata?.bio || "",
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveProfile = async () => {
    try {
      setLoading(true);

      // Import supabase client
      const { supabase } = await import("../utils/supabaseClient");

      // Update user metadata and phone
      const { error: authError } = await supabase.auth.updateUser({
        phone: formData.phone,
        data: {
          full_name: formData.full_name,
          location: formData.location,
          bio: formData.bio,
        },
      });

      if (authError) throw authError;

      // Also update the profiles table if it exists
      const { error: profileError } = await supabase.from("profiles").upsert({
        id: user?.id,
        full_name: formData.full_name,
        phone: formData.phone,
        location: formData.location,
        bio: formData.bio,
        updated_at: new Date().toISOString(),
      });

      // Don't throw error if profiles table doesn't exist or update fails
      if (profileError) {
        console.warn("Profile table update failed:", profileError);
      }

      toast({
        title: "Profile updated",
        description: "Your profile information has been saved successfully.",
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Update failed",
        description: "Failed to update profile information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="dashboard-card p-6">
        <h3 className="text-xl font-semibold mb-4">Profile Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">Full Name</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Enter your full name"
              value={formData.full_name}
              onChange={(e) => handleInputChange("full_name", e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Email</label>
            <input
              type="email"
              className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 bg-muted/50"
              placeholder="Enter your email"
              value={user?.email || ""}
              disabled
            />
            <p className="text-xs text-muted-foreground mt-1">
              Email cannot be changed from here. Contact support if needed.
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Phone</label>
            <input
              type="tel"
              className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Enter your phone number"
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Location</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Enter your location"
              value={formData.location}
              onChange={(e) => handleInputChange("location", e.target.value)}
            />
          </div>
        </div>
        <div className="mt-6">
          <label className="block text-sm font-medium mb-2">Bio</label>
          <textarea
            rows={4}
            className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
            placeholder="Tell us about yourself"
            value={formData.bio}
            onChange={(e) => handleInputChange("bio", e.target.value)}
          />
        </div>
        <div className="mt-6 flex justify-end">
          <button
            onClick={handleSaveProfile}
            disabled={loading}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading && <Loader2 className="w-4 h-4 animate-spin" />}
            <span>{loading ? "Saving..." : "Save Changes"}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// Organizations Settings Component
const OrganizationsSettings: React.FC = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [teams, setTeams] = useState<TeamWithDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load organizations
      const orgData = await getUserOrganizations();
      setOrganizations(
        orgData.organizations.map((org) => {
          const membership = orgData.membership.find(
            (m) => m.organisation_id === org.id
          );
          return {
            id: org.id,
            name: org.name,
            description: org.description,
            created_at: org.created_at,
            created_by: org.created_by,
            updated_at: org.updated_at,
            role: membership?.role || "member",
          };
        })
      );

      // Load teams
      const teamData = await getUserTeams();
      setTeams(teamData);
    } catch (error) {
      console.error("Error loading organizations and teams:", error);
      toast({
        title: "Error",
        description: "Failed to load organizations and teams",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="dashboard-card p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span className="ml-2">Loading organizations and teams...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="dashboard-card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold">Organizations</h3>
          <CreateOrganizationDialog
            onSuccess={loadData}
            trigger={
              <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Create Organization</span>
              </button>
            }
          />
        </div>

        {organizations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Building className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>You haven't joined any organizations yet.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {organizations.map((org) => (
              <div key={org.id} className="border border-border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Building className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold">{org.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {org.role} • Created{" "}
                        {new Date(org.created_at).toLocaleDateString()}
                      </p>
                      {org.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {org.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <OrganizationManageDialog
                      organization={org}
                      onSuccess={loadData}
                      onDelete={loadData}
                    />
                    {org.role !== "admin" && (
                      <button className="px-3 py-1 text-sm text-destructive hover:bg-destructive/10 rounded transition-colors">
                        Leave
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Teams Section */}
      <div className="dashboard-card p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold">Your Teams</h3>
          <CreateTeamDialog
            onSuccess={loadData}
            trigger={
              <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Create Team</span>
              </button>
            }
          />
        </div>

        {teams.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>You haven't joined any teams yet.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {teams.map((team) => (
              <div
                key={team.id}
                className="border border-border rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                      <Users className="w-5 h-5 text-blue-500" />
                    </div>
                    <div>
                      <h4 className="font-medium">{team.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {team.organization_name
                          ? `${team.organization_name} • `
                          : ""}
                        {team.role} • {team.member_count} members
                      </p>
                      {team.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {team.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <TeamManageDialog
                    team={team}
                    onSuccess={loadData}
                    onDelete={loadData}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Subscription Settings Component
const SubscriptionSettings: React.FC = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadSubscriptionData();
    }
  }, [user]);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);

      // Load user subscription
      const subData = await getUserSubscription(user!.id);
      setSubscription(subData);

      // Load available plans
      const planData = await getAvailablePlans();
      setPlans(planData);

      // Load invoices if subscription exists
      if (subData) {
        const invoiceData = await getInvoices(subData.id);
        setInvoices(invoiceData);
      }
    } catch (error) {
      console.error("Error loading subscription data:", error);
      toast({
        title: "Error",
        description: "Failed to load subscription data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (tierLevel: number) => {
    // This would typically come from your pricing configuration
    const prices: Record<number, string> = {
      0: "$0",
      1: "$29",
      2: "$99",
    };
    return prices[tierLevel] || "$0";
  };

  const formatPeriod = (tierLevel: number) => {
    return tierLevel === 0 ? "forever" : "month";
  };

  const getFeatures = (plan: Plan) => {
    // Extract features from the plan's features JSON
    if (plan.features && typeof plan.features === "object") {
      return Object.values(plan.features)
        .filter(Boolean)
        .slice(0, 4) as string[];
    }

    // Fallback based on tier level
    const defaultFeatures: Record<number, string[]> = {
      0: ["5 documents", "Basic AI chat", "Email support"],
      1: [
        "Unlimited documents",
        "Advanced AI",
        "Priority support",
        "Team collaboration",
      ],
      2: [
        "Everything in Pro",
        "Custom integrations",
        "Dedicated support",
        "Advanced analytics",
      ],
    };

    return defaultFeatures[plan.tier_level] || ["Basic features"];
  };

  if (loading) {
    return (
      <div className="dashboard-card p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span className="ml-2">Loading subscription data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      {subscription && (
        <div className="dashboard-card p-6">
          <h3 className="text-xl font-semibold mb-4">Current Plan</h3>
          <div className="flex items-center justify-between p-4 bg-primary/5 border border-primary/20 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-primary/10 rounded-lg">
                <Crown className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h4 className="font-semibold">
                  {subscription.plan?.name || "Unknown Plan"}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {formatPrice(subscription.plan?.tier_level || 0)}/
                  {formatPeriod(subscription.plan?.tier_level || 0)} • Billed{" "}
                  {subscription.billing_cycle}ly
                </p>
              </div>
            </div>
            {subscription.current_period_end && (
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Next billing</p>
                <p className="font-medium">
                  {new Date(
                    subscription.current_period_end
                  ).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Available Plans */}
      <div className="dashboard-card p-6">
        <h3 className="text-xl font-semibold mb-6">Available Plans</h3>
        {plans.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <AlertCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No plans available at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan) => {
              const isCurrentPlan = subscription?.plan_id === plan.id;
              return (
                <div
                  key={plan.id}
                  className={`border rounded-lg p-6 ${
                    isCurrentPlan
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  } transition-colors`}
                >
                  <div className="text-center">
                    <h4 className="font-semibold text-lg">{plan.name}</h4>
                    <div className="mt-2">
                      <span className="text-3xl font-bold">
                        {formatPrice(plan.tier_level)}
                      </span>
                      <span className="text-muted-foreground">
                        /{formatPeriod(plan.tier_level)}
                      </span>
                    </div>
                    {plan.description && (
                      <p className="text-sm text-muted-foreground mt-2">
                        {plan.description}
                      </p>
                    )}
                  </div>
                  <ul className="mt-6 space-y-3">
                    {getFeatures(plan).map((feature, idx) => (
                      <li key={idx} className="flex items-center space-x-2">
                        <Check className="w-4 h-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    className={`w-full mt-6 py-2 rounded-lg transition-colors ${
                      isCurrentPlan
                        ? "bg-muted text-muted-foreground cursor-not-allowed"
                        : "bg-primary text-primary-foreground hover:bg-primary/90"
                    }`}
                    disabled={isCurrentPlan}
                  >
                    {isCurrentPlan ? "Current Plan" : "Upgrade"}
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Billing History */}
      {invoices.length > 0 && (
        <div className="dashboard-card p-6">
          <h3 className="text-xl font-semibold mb-4">Billing History</h3>
          <div className="space-y-3">
            {invoices.map((invoice) => (
              <div
                key={invoice.id}
                className="flex items-center justify-between py-3 border-b border-border last:border-b-0"
              >
                <div>
                  <p className="font-medium">
                    {new Date(invoice.invoice_date).toLocaleDateString()}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {subscription?.plan?.name || "Subscription"}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-medium">
                    {(invoice.amount / 100).toLocaleString("en-US", {
                      style: "currency",
                      currency: invoice.currency.toUpperCase(),
                    })}
                  </p>
                  <p
                    className={`text-sm ${
                      invoice.status === "paid"
                        ? "text-green-600"
                        : "text-orange-600"
                    }`}
                  >
                    {invoice.status.charAt(0).toUpperCase() +
                      invoice.status.slice(1)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Preferences Settings Component
const PreferencesSettings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="dashboard-card p-6">
        <h3 className="text-xl font-semibold mb-4">Appearance</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Theme</label>
            <select className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50">
              <option>System</option>
              <option>Light</option>
              <option>Dark</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Language</label>
            <select className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50">
              <option>English</option>
              <option>Spanish</option>
              <option>French</option>
              <option>German</option>
            </select>
          </div>
        </div>
      </div>

      <div className="dashboard-card p-6">
        <h3 className="text-xl font-semibold mb-4">Notifications</h3>
        <div className="space-y-4">
          {[
            {
              label: "Email notifications",
              description: "Receive updates via email",
            },
            {
              label: "Push notifications",
              description: "Browser push notifications",
            },
            {
              label: "Weekly digest",
              description: "Weekly summary of activity",
            },
            {
              label: "Marketing emails",
              description: "Product updates and tips",
            },
          ].map((setting, index) => (
            <div key={index} className="flex items-center justify-between">
              <div>
                <p className="font-medium">{setting.label}</p>
                <p className="text-sm text-muted-foreground">
                  {setting.description}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  defaultChecked={index < 2}
                />
                <div className="w-11 h-6 bg-muted peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Security Settings Component
const SecuritySettings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="dashboard-card p-6">
        <h3 className="text-xl font-semibold mb-4">
          Password & Authentication
        </h3>
        <div className="space-y-4">
          <button className="w-full text-left p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Change Password</p>
                <p className="text-sm text-muted-foreground">
                  Update your account password
                </p>
              </div>
              <Key className="w-5 h-5 text-muted-foreground" />
            </div>
          </button>
          <button className="w-full text-left p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Two-Factor Authentication</p>
                <p className="text-sm text-muted-foreground">
                  Add an extra layer of security
                </p>
              </div>
              <Shield className="w-5 h-5 text-muted-foreground" />
            </div>
          </button>
        </div>
      </div>

      <div className="dashboard-card p-6">
        <h3 className="text-xl font-semibold mb-4">Privacy</h3>
        <div className="space-y-4">
          {[
            {
              label: "Profile visibility",
              description: "Who can see your profile",
            },
            {
              label: "Activity status",
              description: "Show when you're online",
            },
            {
              label: "Data sharing",
              description: "Share usage data for improvements",
            },
          ].map((setting, index) => (
            <div key={index} className="flex items-center justify-between">
              <div>
                <p className="font-medium">{setting.label}</p>
                <p className="text-sm text-muted-foreground">
                  {setting.description}
                </p>
              </div>
              <select className="px-3 py-1 border border-border rounded text-sm">
                <option>Public</option>
                <option>Private</option>
                <option>Friends</option>
              </select>
            </div>
          ))}
        </div>
      </div>

      <div className="dashboard-card p-6 border-destructive/20">
        <h3 className="text-xl font-semibold mb-4 text-destructive">
          Danger Zone
        </h3>
        <div className="space-y-4">
          <button className="w-full text-left p-4 border border-destructive/20 rounded-lg hover:bg-destructive/5 transition-colors">
            <div>
              <p className="font-medium text-destructive">Delete Account</p>
              <p className="text-sm text-muted-foreground">
                Permanently delete your account and all data
              </p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Settings;
