import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import { useUser } from '../../../../base/contextapi/UserContext';
import type { PSEOWebsite } from '../../types';
import PSEOLayout from '../../components/PSEOLayout';

interface ContentGenerationResultsProps {}

const ContentGenerationResults: React.FC<ContentGenerationResultsProps> = () => {
  const { websiteId } = useParams<{ websiteId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();
  
  const [website, setWebsite] = useState<PSEOWebsite | null>(null);
  const [allWebsites, setAllWebsites] = useState<PSEOWebsite[]>([]);
  const [contentOpportunities, setContentOpportunities] = useState<any[]>([]);
  const [contentGaps, setContentGaps] = useState<any[]>([]);
  const [generatedContent, setGeneratedContent] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'opportunities' | 'gaps' | 'generated'>('overview');
  const [expandedContent, setExpandedContent] = useState<Record<string, boolean>>({});

  useEffect(() => {
    if (websiteId) {
      loadSingleWebsiteResults();
    } else if (user?.id) {
      loadAllWebsitesResults();
    }
  }, [websiteId, user?.id]);

  const loadSingleWebsiteResults = async () => {
    try {
      setLoading(true);
      
      // Get website info
      const websiteData = await databaseService.getWebsiteById(websiteId!);
      if (!websiteData) {
        throw new Error('Website not found');
      }
      setWebsite(websiteData);
      
      // Get content opportunities
      const opportunitiesData = await databaseService.getContentOpportunities(websiteId!, {
        limit: 200
      });
      setContentOpportunities(opportunitiesData);
      
      // Get content gaps
      const gapsData = await databaseService.getContentGaps(websiteId!, {
        limit: 100
      });
      setContentGaps(gapsData);
      
      // Get generated content items
      const generatedData = await databaseService.getGeneratedContentItems(websiteId!, {
        limit: 100
      });
      setGeneratedContent(generatedData);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load content generation results');
    } finally {
      setLoading(false);
    }
  };

  const loadAllWebsitesResults = async () => {
    try {
      setLoading(true);
      
      // Get all clients for this user
      const clients = await databaseService.getClientsByUserId(user!.id);
      
      // Get all websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsitesData = websitesArrays.flat();
      setAllWebsites(allWebsitesData);
      
      if (allWebsitesData.length === 0) {
        setError('No websites found. Please add a website first.');
        return;
      }
      
      // Get content data for all websites
      const allOpportunities: any[] = [];
      const allGaps: any[] = [];
      const allGenerated: any[] = [];
      
      for (const website of allWebsitesData) {
        try {
          const [opportunities, gaps, generated] = await Promise.all([
            databaseService.getContentOpportunities(website.id, { limit: 50 }),
            databaseService.getContentGaps(website.id, { limit: 50 }),
            databaseService.getGeneratedContentItems(website.id, { limit: 50 })
          ]);
          
          // Add website info to each item
          allOpportunities.push(...opportunities.map(item => ({ ...item, website_name: website.name, website_url: website.url })));
          allGaps.push(...gaps.map(item => ({ ...item, website_name: website.name, website_url: website.url })));
          allGenerated.push(...generated.map(item => ({ ...item, website_name: website.name, website_url: website.url })));
        } catch (err) {
          console.warn(`Failed to load content data for website ${website.name}:`, err);
        }
      }
      
      setContentOpportunities(allOpportunities);
      setContentGaps(allGaps);
      setGeneratedContent(allGenerated);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load content generation results');
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated': return 'bg-green-100 text-green-800';
      case 'identified': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate stats
  const stats = {
    totalOpportunities: contentOpportunities.length,
    highPriority: contentOpportunities.filter(opp => opp.priority === 'high').length,
    generatedContent: generatedContent.length,
    totalGaps: contentGaps.length,
    avgDifficulty: contentOpportunities.length > 0 ? 
      Math.round(contentOpportunities.reduce((sum, opp) => sum + (opp.difficulty_score || 0), 0) / contentOpportunities.length) : 0,
    estimatedTraffic: contentOpportunities.reduce((sum, opp) => sum + (opp.estimated_traffic || 0), 0),
    contentTypes: contentOpportunities.reduce((acc, opp) => {
      acc[opp.content_type] = (acc[opp.content_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  };

  const toggleContentExpansion = (contentId: string) => {
    setExpandedContent(prev => ({
      ...prev,
      [contentId]: !prev[contentId]
    }));
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (error) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="bg-destructive/10 border border-destructive/20 rounded-md p-6">
              <h2 className="text-lg font-semibold text-destructive mb-2">Error Loading Results</h2>
              <p className="text-destructive">{error}</p>
              <button
                onClick={() => navigate('/full-site-analysis')}
                className="mt-4 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
              >
                Back to Full Site Analysis
              </button>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              📝 Content Generation Results
            </h1>
            {website ? (
              <>
                <p className="text-muted-foreground">
                  {website.name} - {website.domain}
                </p>
                <p className="text-sm text-muted-foreground">
                  {stats.totalOpportunities} content opportunities identified with {stats.estimatedTraffic.toLocaleString()} estimated monthly traffic
                </p>
              </>
            ) : (
              <>
                <p className="text-muted-foreground">
                  All Websites ({allWebsites.length} total)
                </p>
                <p className="text-sm text-muted-foreground">
                  {stats.totalOpportunities} content opportunities across all websites with {stats.estimatedTraffic.toLocaleString()} estimated monthly traffic
                </p>
              </>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => navigate('/full-site-analysis')}
              className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90"
            >
              ← Back to Analysis
            </button>
            <button
              onClick={() => navigate('/create-blog-post')}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
            >
              🤖 Create AI Blog Post
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-border mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'opportunities', label: 'Content Opportunities' },
              { id: 'gaps', label: 'Content Gaps' },
              { id: 'generated', label: 'Generated Content' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-blue-600">{stats.totalOpportunities}</div>
                <p className="text-sm text-muted-foreground">Content Opportunities</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-red-600">{stats.highPriority}</div>
                <p className="text-sm text-muted-foreground">High Priority</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-green-600">{stats.generatedContent}</div>
                <p className="text-sm text-muted-foreground">Generated Content</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-purple-600">{stats.estimatedTraffic.toLocaleString()}</div>
                <p className="text-sm text-muted-foreground">Est. Monthly Traffic</p>
              </div>
            </div>

            {/* Content Type Distribution */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">📊 Content Type Distribution</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(stats.contentTypes).map(([type, count]) => (
                  <div key={type} className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-xl font-bold">{count as number}</div>
                    <div className="text-sm text-muted-foreground capitalize">{type}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Opportunities */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">🚀 Top Content Opportunities</h3>
              <div className="space-y-3">
                {contentOpportunities
                  .sort((a, b) => (b.estimated_traffic || 0) - (a.estimated_traffic || 0))
                  .slice(0, 10)
                  .map((opportunity) => (
                    <div key={opportunity.id} className="flex items-center justify-between p-4 bg-muted rounded-lg">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-foreground">{opportunity.title}</h4>
                        <p className="text-sm text-muted-foreground mt-1">{opportunity.content_brief}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(opportunity.priority)}`}>
                            {opportunity.priority} priority
                          </span>
                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            {opportunity.content_type}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            Target: {opportunity.target_keyword}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium text-blue-600">{(opportunity.estimated_traffic || 0).toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">traffic</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-orange-600">{opportunity.difficulty_score || 0}</div>
                          <div className="text-xs text-muted-foreground">difficulty</div>
                        </div>
                        <span className={`px-2 py-1 rounded text-xs ${getStatusColor(opportunity.status)}`}>
                          {opportunity.status}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'opportunities' && (
          <div className="space-y-6">
            {/* Filter and Search */}
            <div className="bg-card rounded-lg border p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search content opportunities..."
                    className="w-full p-2 border rounded-md"
                  />
                </div>
                <select className="p-2 border rounded-md">
                  <option value="">All Content Types</option>
                  <option value="blog">Blog Post</option>
                  <option value="guide">Guide</option>
                  <option value="faq">FAQ</option>
                  <option value="landing">Landing Page</option>
                </select>
                <select className="p-2 border rounded-md">
                  <option value="">All Priorities</option>
                  <option value="high">High Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="low">Low Priority</option>
                </select>
                <select className="p-2 border rounded-md">
                  <option value="">All Status</option>
                  <option value="identified">Identified</option>
                  <option value="generated">Generated</option>
                  <option value="in_progress">In Progress</option>
                </select>
              </div>
            </div>

            {/* Opportunities Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {contentOpportunities.map((opportunity) => (
                <div key={opportunity.id} className="bg-card rounded-lg border p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-medium text-foreground line-clamp-2">{opportunity.title}</h3>
                    <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(opportunity.priority)}`}>
                      {opportunity.priority}
                    </span>
                  </div>
                  
                  {!website && opportunity.website_name && (
                    <p className="text-xs text-blue-600 mb-2">
                      📝 {opportunity.website_name}
                    </p>
                  )}
                  
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                    {opportunity.content_brief}
                  </p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span>Target Keyword:</span>
                      <span className="font-medium">{opportunity.target_keyword}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Est. Traffic:</span>
                      <span className="font-medium text-blue-600">{(opportunity.estimated_traffic || 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Difficulty:</span>
                      <span className="font-medium text-orange-600">{opportunity.difficulty_score || 0}/100</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Word Count:</span>
                      <span className="font-medium">{opportunity.target_word_count || 800} words</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        {opportunity.content_type}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs ${getStatusColor(opportunity.status)}`}>
                        {opportunity.status}
                      </span>
                    </div>
                    
                    {opportunity.status === 'identified' && (
                      <button
                        onClick={() => navigate(`/create-blog-post?keyword=${encodeURIComponent(opportunity.target_keyword)}`)}
                        className="text-xs bg-primary text-primary-foreground px-3 py-1 rounded hover:bg-primary/90"
                      >
                        Generate Content
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'gaps' && (
          <div className="space-y-6">
            {/* Content Gaps */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">🔍 Content Gap Analysis</h3>
              <p className="text-muted-foreground mb-6">
                Areas where competitors have content but you don't, representing opportunities to capture market share.
              </p>
              
              {contentGaps.length > 0 ? (
                <div className="space-y-4">
                  {contentGaps.map((gap) => (
                    <div key={gap.id} className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-orange-800">{gap.gap_topic || gap.target_keyword}</h4>
                        <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded">
                          Gap Identified
                        </span>
                      </div>
                      <p className="text-sm text-orange-700 mb-3">{gap.description}</p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Competitor Coverage:</span>
                          <div className="text-orange-600">{gap.competitor_count || 3} competitors found</div>
                        </div>
                        <div>
                          <span className="font-medium">Est. Traffic:</span>
                          <div className="text-orange-600">{(gap.estimated_traffic || 0).toLocaleString()}/month</div>
                        </div>
                        <div>
                          <span className="font-medium">Opportunity Score:</span>
                          <div className="text-orange-600">{gap.opportunity_score || 75}/100</div>
                        </div>
                      </div>
                      <div className="mt-3 pt-3 border-t border-orange-200">
                        <button
                          onClick={() => navigate(`/create-blog-post?keyword=${encodeURIComponent(gap.target_keyword || gap.gap_topic)}`)}
                          className="text-sm bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700"
                        >
                          Create Content for This Gap
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">🎯</div>
                  <h4 className="font-medium mb-2">No Content Gaps Found</h4>
                  <p className="text-muted-foreground">
                    Your content strategy appears comprehensive, or gap analysis data is not yet available.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'generated' && (
          <div className="space-y-6">
            {/* Generated Content */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">🤖 AI Generated Content</h3>
              
              {stats.generatedContent > 0 ? (
                <div className="space-y-4">
                  {generatedContent.map((content) => (
                    <div key={content.id} className="p-4 border border-green-200 bg-green-50 rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <h4 className="font-medium text-green-800">{content.title}</h4>
                        <div className="flex items-center gap-2">
                          {content.ai_model_used && (
                            <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">
                              {content.ai_model_used}
                            </span>
                          )}
                          <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">
                            Generated
                          </span>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm mb-4">
                        <div>
                          <span className="font-medium">Target Keyword:</span>
                          <div className="text-green-700">{content.target_keyword}</div>
                        </div>
                        <div>
                          <span className="font-medium">Content Type:</span>
                          <div className="text-green-700">{content.content_type}</div>
                        </div>
                        <div>
                          <span className="font-medium">Word Count:</span>
                          <div className="text-green-700">{content.generated_content?.split(' ').length || 0} words</div>
                        </div>
                        <div>
                          <span className="font-medium">Priority:</span>
                          <div className="text-green-700">{content.priority}</div>
                        </div>
                      </div>
                      
                      <div className="bg-white border border-green-200 rounded p-4 mb-4">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-medium">Generated Markdown Content:</h5>
                          <button
                            onClick={() => toggleContentExpansion(content.id)}
                            className="text-sm text-green-600 hover:text-green-800 flex items-center gap-1"
                          >
                            {expandedContent[content.id] ? (
                              <>Collapse ↑</>
                            ) : (
                              <>Expand Full Content ↓</>
                            )}
                          </button>
                        </div>
                        <div className={`text-sm text-gray-700 font-mono leading-relaxed ${
                          expandedContent[content.id] 
                            ? 'max-h-96 overflow-y-auto' 
                            : 'max-h-32 overflow-hidden'
                        }`}>
                          <pre className="whitespace-pre-wrap break-words">
                            {expandedContent[content.id] 
                              ? content.generated_content 
                              : `${content.generated_content?.substring(0, 300)}${content.generated_content?.length > 300 ? '...' : ''}`
                            }
                          </pre>
                        </div>
                        {!expandedContent[content.id] && content.generated_content?.length > 300 && (
                          <div className="mt-2 text-xs text-gray-500">
                            Showing first 300 characters. Click "Expand Full Content" to see all.
                          </div>
                        )}
                      </div>
                      
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(content.generated_content);
                          }}
                          className="text-sm bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                        >
                          📋 Copy Content
                        </button>
                        <button
                          onClick={() => {
                            const blob = new Blob([content.generated_content], { type: 'text/markdown' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${content.title.replace(/[^a-z0-9]/gi, '-')}.md`;
                            a.click();
                            URL.revokeObjectURL(url);
                          }}
                          className="text-sm bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                        >
                          📄 Download Markdown
                        </button>
                        <button
                          onClick={() => toggleContentExpansion(content.id)}
                          className="text-sm bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
                        >
                          👁️ {expandedContent[content.id] ? 'Collapse' : 'View Full'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">🤖</div>
                  <h4 className="font-medium mb-2">No Generated Content Yet</h4>
                  <p className="text-muted-foreground mb-4">
                    Start generating AI content from your identified opportunities.
                  </p>
                  <button
                    onClick={() => navigate('/create-blog-post')}
                    className="bg-primary text-primary-foreground px-6 py-3 rounded-md hover:bg-primary/90"
                  >
                    🤖 Generate Your First AI Content
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default ContentGenerationResults; 