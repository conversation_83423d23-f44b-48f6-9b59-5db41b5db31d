import React, { useState } from 'react';
import { UseScopingReturn } from '../../../hooks/useScoping';

interface SingleScopingFormProps extends UseScopingReturn {
  onBack: () => void;
}

const SingleScopingForm: React.FC<SingleScopingFormProps> = ({
  onBack,
  state,
  generateScoping,
  isLoading,
  messages,
  error,
  setDocumentFile
}) => {
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setDocumentFile(selectedFile);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generateScoping();
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Create Scoping Document</h2>
      <p className="text-gray-600 mb-6">
        Review your inputs and generate your AI-powered scoping document
      </p>
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Summary View */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {/* Client Information */}
            <div className="p-5 border-b border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Client Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Client Name</h4>
                  <p className="mt-1 text-sm text-gray-900">{state.clientInfo?.name || 'Not provided'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Industry</h4>
                  <p className="mt-1 text-sm text-gray-900">{state.clientInfo?.industry || 'Not provided'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Company</h4>
                  <p className="mt-1 text-sm text-gray-900">{state.clientInfo?.company || 'Not provided'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Contact Person</h4>
                  <p className="mt-1 text-sm text-gray-900">{state.clientInfo?.contactPerson || 'Not provided'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Email</h4>
                  <p className="mt-1 text-sm text-gray-900">{state.clientInfo?.email || 'Not provided'}</p>
                </div>
              </div>
            </div>

            {/* Project Information */}
            <div className="p-5 border-b border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Project Information</h3>
              </div>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Project Name</h4>
                  <p className="mt-1 text-sm text-gray-900">{state.scopingInfo?.projectName || 'Not provided'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Project Description</h4>
                  <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{state.scopingInfo?.projectDescription || 'Not provided'}</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Timeline</h4>
                    <p className="mt-1 text-sm text-gray-900">{state.scopingInfo?.timeline || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Budget</h4>
                    <p className="mt-1 text-sm text-gray-900">{state.scopingInfo?.budget || 'Not provided'}</p>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Project Goals</h4>
                  {state.scopingInfo?.goals && state.scopingInfo.goals.length > 0 ? (
                    <ul className="mt-1 pl-5 text-sm text-gray-900 list-disc">
                      {state.scopingInfo.goals.map((goal, index) => (
                        <li key={index}>{goal}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="mt-1 text-sm text-gray-500 italic">No goals provided</p>
                  )}
                </div>
              </div>
            </div>

            {/* Template */}
            <div className="p-5 border-b border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Prompt Template</h3>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Template Name</h4>
                <p className="mt-1 text-sm text-gray-900">{state.promptTemplate?.name || 'Not selected'}</p>
              </div>
            </div>

            {/* Document Sections */}
            <div className="p-5 border-b border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Document Sections</h3>
              </div>
              {state.sections.length > 0 ? (
                <ul className="space-y-2">
                  {state.sections.map((section, index) => (
                    <li key={section.id} className="text-sm">
                      <span className="font-medium text-gray-900">{index + 1}. {section.title}</span>
                      <p className="ml-5 text-gray-600">{section.description}</p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500 italic">No sections defined</p>
              )}
            </div>

            {/* Reference Document */}
            <div className="p-5">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Reference Document (Optional)</h3>
              </div>
              <div className="mt-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload PDF Reference
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div className="flex text-sm text-gray-600 justify-center">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none"
                      >
                        <span>Upload a file</span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          accept=".pdf"
                          onChange={handleFileChange}
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">PDF up to 10MB</p>
                  </div>
                </div>
                {file && (
                  <p className="mt-2 text-sm text-gray-500 flex items-center">
                    <svg className="h-4 w-4 text-green-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {file.name}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Progress Messages */}
          {isLoading && messages.length > 0 && (
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Generation Progress</h3>
              <ul className="space-y-2">
                {messages.map((message, index) => (
                  <li key={index} className="flex items-start">
                    {index === messages.length - 1 ? (
                      <>
                        <div className="mr-2 h-5 w-5 text-blue-500 animate-spin">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </div>
                        <span className="text-sm text-gray-700">{message}</span>
                      </>
                    ) : (
                      <>
                        <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-sm text-gray-700">{message}</span>
                      </>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <button
              type="button"
              onClick={onBack}
              className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              disabled={isLoading}
            >
              Back
            </button>
            <button
              type="submit"
              className={`inline-flex items-center justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                isLoading
                  ? 'bg-indigo-400 cursor-not-allowed'
                  : 'bg-indigo-600 hover:bg-indigo-700'
              }`}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </>
              ) : (
                'Generate Document'
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SingleScopingForm; 