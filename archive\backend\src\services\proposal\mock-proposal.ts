import fs from 'fs/promises';
import path from 'path';

interface MockedProposal {
  research: {
    content: string;
  };
  summary: {
    content: string;
  };
  sections: Array<{
    title: string;
    content: string;
  }>;
}

export class MockProposalService {
  private readonly mockPath: string;

  constructor() {
    // Store mocks in the proposal service directory
    this.mockPath = path.join(__dirname, 'mocks');
    this.initializeMockDirectory();
  }

  private async initializeMockDirectory() {
    try {
      await fs.mkdir(this.mockPath, { recursive: true });
    } catch (error) {
      console.error('Error creating mocks directory:', error);
    }
  }

  async getMockedResponse(clientName: string): Promise<MockedProposal | null> {
    try {
      const filePath = this.getFilePath(clientName);
      console.log('Looking for mock at:', filePath);
      const fileContent = await fs.readFile(filePath, 'utf-8');
      console.log('Found mock data for:', clientName);
      return JSON.parse(fileContent) as MockedProposal;
    } catch (error) {
      console.log('No mock found for:', clientName);
      return null;
    }
  }

  private getFilePath(clientName: string): string {
    const safeName = clientName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    return path.join(this.mockPath, `${safeName}.json`);
  }

  async saveMockedResponse(clientName: string, proposal: MockedProposal): Promise<void> {
    try {
      // Ensure mocks directory exists
      await fs.mkdir(this.mockPath, { recursive: true });
      
      const filePath = this.getFilePath(clientName);
      await fs.writeFile(filePath, JSON.stringify(proposal, null, 2));
    } catch (error) {
      console.error('Error saving mock proposal:', error);
      throw error;
    }
  }
}

// Example mock data structure
export const exampleMockProposal: MockedProposal = {
  research: {
    content: "Research findings for the client...",
  },
  summary: {
    content: "Executive summary of the proposal...",
  },
  sections: [
    {
      title: "Project Overview",
      content: "Detailed project overview content..."
    },
    {
      title: "Our Approach",
      content: "Our methodology and approach..."
    },
    {
      title: "Deliverables",
      content: "List of project deliverables..."
    },
    {
      title: "Timeline",
      content: "Project timeline and milestones..."
    },
    {
      title: "Investment",
      content: "Project costs and investment details..."
    }
  ]
}; 