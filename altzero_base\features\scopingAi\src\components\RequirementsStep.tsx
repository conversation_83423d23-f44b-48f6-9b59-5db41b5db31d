import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../../../../base/components/ui/button";
import { Textarea } from "../../../../base/components/ui/textarea";
import {
  RadioGroup,
  RadioGroupItem,
} from "../../../../base/components/ui/radio-group";
import { Label } from "../../../../base/components/ui/label";
import { Input } from "../../../../base/components/ui/input";
import {
  Upload,
  AlertTriangle,
  Database,
  FileText,
  Search,
  CheckCircle,
  Loader2,
  Eye,
  Trash2,
  RefreshCw,
  User,
  Calendar,
  Clock,
  Sparkles,
  ArrowRight,
  FilePlus,
  Zap,
  BookOpen,
  Filter,
  SortAsc,
  SortDesc,
  Grid,
  List,
  X,
  ChevronDown,
  ChevronUp,
  Download,
  Star,
  Tag,
  Folder,
  MoreHorizontal,
} from "lucide-react";
import { Alert, AlertDescription } from "../../../../base/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { ScrollArea } from "../../../../base/components/ui/scroll-area";
import { Badge } from "../../../../base/components/ui/badge";
import { Checkbox } from "../../../../base/components/ui/checkbox";
import { useDocumentForm } from "../contexts/DocumentFormContext";
import { knowledgeService } from "../../../knowledge/services/knowledgeService";
import { useToast } from "../../../../base/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";

// Define StepProps interface locally
interface StepProps {
  onNext: () => void;
  onBack: () => void;
}

interface KnowledgeDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  status: string;
  uploadedAt: string;
  description?: string;
}

const RequirementsStep: React.FC<StepProps> = ({ onNext, onBack }) => {
  const [uploadedFile, setUploadedFile] = React.useState<File | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [knowledgeDocs, setKnowledgeDocs] = useState<KnowledgeDocument[]>([]);
  const [isLoadingKnowledge, setIsLoadingKnowledge] = useState(false);

  // Advanced UI state for large datasets
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const [sortBy, setSortBy] = useState<"name" | "date" | "size" | "type">(
    "name"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50); // Optimized for performance
  const [showSelectedOnly, setShowSelectedOnly] = useState(false);
  const [isFiltering, setIsFiltering] = useState(false);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

  const {
    requirementsOption,
    requirementsText,
    setRequirementsOption,
    setRequirementsText,
    selectedKnowledgeDocuments,
    setSelectedKnowledgeDocuments,
    documentRequirements,
    setDocumentRequirements,
    isCurrentStepValid,
  } = useDocumentForm();

  const { toast } = useToast();

  // Debounced search for performance
  useEffect(() => {
    setIsFiltering(true);
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setIsFiltering(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Load knowledge base documents
  useEffect(() => {
    if (requirementsOption === "knowledge") {
      loadKnowledgeDocuments();
    }
  }, [requirementsOption]);

  const loadKnowledgeDocuments = async () => {
    setIsLoadingKnowledge(true);
    try {
      const docs = await knowledgeService.getDocuments();
      const transformedDocs = docs.map((doc: any) => ({
        id: doc.id,
        name: doc.name || "Untitled Document",
        type: doc.type || "Unknown",
        size: doc.size || 0,
        status: doc.status || "unknown",
        uploadedAt: doc.uploadedAt || new Date().toISOString(),
        description: doc.description,
      }));
      setKnowledgeDocs(transformedDocs);
    } catch (error) {
      console.error("Failed to load knowledge documents:", error);
      // Removed toast error to prevent UI spam
      console.error("Failed to load knowledge base documents:", error);
    } finally {
      setIsLoadingKnowledge(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleKnowledgeDocSelection = (docId: string, checked: boolean) => {
    if (checked) {
      setSelectedKnowledgeDocuments([...selectedKnowledgeDocuments, docId]);
      // Initialize empty requirement for new document
      setDocumentRequirements({ ...documentRequirements, [docId]: "" });
    } else {
      setSelectedKnowledgeDocuments(
        selectedKnowledgeDocuments.filter((id) => id !== docId)
      );
      // Remove requirement when document is deselected
      const newReqs = { ...documentRequirements };
      delete newReqs[docId];
      setDocumentRequirements(newReqs);
    }
  };

  const handleDocumentRequirementChange = (
    docId: string,
    requirement: string
  ) => {
    setDocumentRequirements({ ...documentRequirements, [docId]: requirement });
  };

  const handleNext = () => {
    // The context state is already updated, just proceed
    if (requirementsOption === "knowledge") {
      console.log("Selected knowledge documents:", selectedKnowledgeDocuments);
      console.log("Document requirements:", documentRequirements);
    }
    onNext();
  };

  // Advanced filtering and sorting logic
  const filteredAndSortedDocs = React.useMemo(() => {
    let filtered = knowledgeDocs.filter((doc) => {
      // Text search using debounced query for performance
      const matchesSearch =
        debouncedSearchQuery === "" ||
        doc.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        doc.type.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        (doc.description &&
          doc.description
            .toLowerCase()
            .includes(debouncedSearchQuery.toLowerCase()));

      // Type filter
      const matchesType = filterType === "all" || doc.type === filterType;

      // Status filter
      const matchesStatus =
        filterStatus === "all" || doc.status === filterStatus;

      // Selected only filter
      const matchesSelected =
        !showSelectedOnly || selectedKnowledgeDocuments.includes(doc.id);

      return matchesSearch && matchesType && matchesStatus && matchesSelected;
    });

    // Sorting
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "date":
          comparison =
            new Date(a.uploadedAt).getTime() - new Date(b.uploadedAt).getTime();
          break;
        case "size":
          comparison = a.size - b.size;
          break;
        case "type":
          comparison = a.type.localeCompare(b.type);
          break;
        default:
          comparison = 0;
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });

    return filtered;
  }, [
    knowledgeDocs,
    debouncedSearchQuery,
    filterType,
    filterStatus,
    showSelectedOnly,
    selectedKnowledgeDocuments,
    sortBy,
    sortOrder,
  ]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedDocs.length / itemsPerPage);
  const paginatedDocs = filteredAndSortedDocs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Get unique types and statuses for filters
  const uniqueTypes = React.useMemo(
    () => [...new Set(knowledgeDocs.map((doc) => doc.type))].sort(),
    [knowledgeDocs]
  );

  const uniqueStatuses = React.useMemo(
    () => [...new Set(knowledgeDocs.map((doc) => doc.status))].sort(),
    [knowledgeDocs]
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "processing":
        return <Clock className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case "error":
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const requirementOptions = [
    {
      id: "requirements",
      title: "Manual Entry",
      description: "Type your requirements directly",
      icon: <FileText className="h-6 w-6" />,
      color: "blue",
      bgGradient:
        "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50",
      borderColor: "border-blue-200 dark:border-blue-800",
      iconColor: "text-blue-600",
    },
    {
      id: "upload",
      title: "Upload Documents",
      description: "Upload requirement files",
      icon: <Upload className="h-6 w-6" />,
      color: "purple",
      bgGradient:
        "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50",
      borderColor: "border-purple-200 dark:border-purple-800",
      iconColor: "text-purple-600",
    },
    {
      id: "knowledge",
      title: "Knowledge Base",
      description: "Select from vector documents",
      icon: <Database className="h-6 w-6" />,
      color: "emerald",
      bgGradient:
        "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/50 dark:to-emerald-900/50",
      borderColor: "border-emerald-200 dark:border-emerald-800",
      iconColor: "text-emerald-600",
    },
  ];

  return (
    <div className="space-y-8 max-w-4xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
          <Sparkles className="h-4 w-4" />
          Step 2 of 4
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-3 flex items-center justify-center gap-2">
          Project Requirements <span className="text-red-500">*</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Choose how you'd like to provide your project requirements to generate
          the most accurate scope document (required)
        </p>
      </motion.div>

      {/* Requirement Options */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <RadioGroup
          value={requirementsOption}
          onValueChange={setRequirementsOption}
          className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          {requirementOptions.map((option) => (
            <motion.div
              key={option.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card
                className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  requirementsOption === option.id
                    ? `ring-2 ring-primary shadow-lg ${option.bgGradient}`
                    : "hover:shadow-md"
                }`}
                onClick={() => setRequirementsOption(option.id)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div
                      className={`p-3 rounded-xl ${option.bgGradient} ${option.borderColor} border`}
                    >
                      <div className={option.iconColor}>{option.icon}</div>
                    </div>
                    <RadioGroupItem
                      value={option.id}
                      className="data-[state=checked]:border-primary"
                    />
                  </div>
                  <CardTitle className="text-lg font-semibold">
                    {option.title}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {option.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            </motion.div>
          ))}
        </RadioGroup>
      </motion.div>

      {/* Content Based on Selection */}
      <AnimatePresence mode="wait">
        {/* Manual Requirements */}
        {requirementsOption === "requirements" && (
          <motion.div
            key="manual"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">
                      Manual Requirements Entry
                    </CardTitle>
                    <CardDescription>
                      Describe your project requirements in detail
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Label
                    htmlFor="requirements-text"
                    className="text-base font-medium flex items-center gap-1"
                  >
                    Project Requirements and Expectations{" "}
                    <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="requirements-text"
                    placeholder="Describe what you need (required):
• Key features and functionality
• Technical constraints and requirements
• Timeline expectations
• Budget considerations
• Target audience
• Integration requirements
• Performance expectations
• Design preferences

Be as detailed as possible to get the most accurate scope document..."
                    rows={12}
                    value={requirementsText}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                      setRequirementsText(e.target.value)
                    }
                    className={`min-h-[300px] text-base leading-relaxed resize-none border-2 transition-colors ${
                      !requirementsText?.trim()
                        ? "border-red-300 focus:border-red-500"
                        : "focus:border-blue-500"
                    }`}
                  />
                  {!requirementsText?.trim() && (
                    <p className="text-sm text-red-500 mt-1">
                      Project requirements are required
                    </p>
                  )}
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>
                      The more details you provide, the better our AI can scope
                      your project
                    </span>
                    <span>{requirementsText.length} characters</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Upload Documents */}
        {requirementsOption === "upload" && (
          <motion.div
            key="upload"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {requirementsText && (
              <Alert className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <AlertDescription className="text-amber-800 dark:text-amber-200">
                  Your previously entered requirements will be cleared when you
                  switch to document upload mode.
                </AlertDescription>
              </Alert>
            )}

            <Card className="shadow-lg border-0 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
                    <Upload className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">
                      Upload Requirements
                    </CardTitle>
                    <CardDescription>
                      Upload documents containing your project requirements
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div
                  className="relative border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-xl p-12 text-center hover:border-purple-400 dark:hover:border-purple-600 transition-all duration-300 bg-white/50 dark:bg-purple-950/10 hover:bg-purple-50/50 dark:hover:bg-purple-950/20"
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                >
                  <div className="space-y-6">
                    <div className="mx-auto w-16 h-16 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                      <Upload className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="space-y-3">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        Upload your requirement documents
                      </h3>
                      <p className="text-muted-foreground max-w-sm mx-auto">
                        Drag and drop your files here, or click the button below
                        to browse
                      </p>
                    </div>
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                      accept=".pdf,.doc,.docx,.txt"
                      multiple
                    />
                    <Button
                      asChild
                      className="gap-2 px-6 py-3 shadow-lg hover:shadow-xl"
                    >
                      <Label htmlFor="file-upload" className="cursor-pointer">
                        <FilePlus className="h-4 w-4" />
                        Choose Files
                      </Label>
                    </Button>
                    <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
                      <Badge variant="secondary">PDF</Badge>
                      <Badge variant="secondary">DOC</Badge>
                      <Badge variant="secondary">DOCX</Badge>
                      <Badge variant="secondary">TXT</Badge>
                    </div>
                  </div>
                </div>

                {uploadedFile && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6"
                  >
                    <Card className="bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            </div>
                            <div>
                              <p className="font-medium text-green-900 dark:text-green-100">
                                {uploadedFile.name}
                              </p>
                              <p className="text-sm text-green-600 dark:text-green-400">
                                {(uploadedFile.size / 1024 / 1024).toFixed(2)}{" "}
                                MB
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setUploadedFile(null)}
                            className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Remove
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Knowledge Base Selection */}
        {requirementsOption === "knowledge" && (
          <motion.div
            key="knowledge"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950/20 dark:to-teal-950/20">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-emerald-100 dark:bg-emerald-900/50 rounded-lg">
                      <Database className="h-5 w-5 text-emerald-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl flex items-center gap-2">
                        Knowledge Base Documents{" "}
                        <span className="text-red-500">*</span>
                      </CardTitle>
                      <CardDescription>
                        Select documents from your AI-powered knowledge base (at
                        least one required)
                      </CardDescription>
                      {selectedKnowledgeDocuments.length === 0 && (
                        <p className="text-sm text-red-500 mt-1">
                          Please select at least one knowledge base document
                        </p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadKnowledgeDocuments}
                    disabled={isLoadingKnowledge}
                    className="gap-2"
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${
                        isLoadingKnowledge ? "animate-spin" : ""
                      }`}
                    />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Advanced Search and Filters Toolbar */}
                <div className="space-y-4">
                  {/* Search Bar */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                    <Input
                      placeholder="Search documents by name, type, or description..."
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                        setCurrentPage(1); // Reset to first page on search
                      }}
                      className="pl-12 h-12 text-base border-2 focus:border-emerald-500"
                    />
                    {searchQuery && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                        onClick={() => {
                          setSearchQuery("");
                          setCurrentPage(1);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {/* Toolbar */}
                  <div className="flex flex-wrap items-center justify-between gap-4 p-4 bg-emerald-50 dark:bg-emerald-950/20 rounded-lg border border-emerald-200 dark:border-emerald-800">
                    <div className="flex items-center gap-3">
                      {/* Results Count */}
                      <div className="text-sm font-medium text-emerald-900 dark:text-emerald-100 flex items-center gap-2">
                        {isFiltering && (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        )}
                        {filteredAndSortedDocs.length} of {knowledgeDocs.length}{" "}
                        documents
                        {debouncedSearchQuery && (
                          <Badge variant="outline" className="text-xs">
                            filtered
                          </Badge>
                        )}
                      </div>

                      {/* Selected Count */}
                      {selectedKnowledgeDocuments.length > 0 && (
                        <Badge className="bg-emerald-600 text-white">
                          {selectedKnowledgeDocuments.length} selected
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {/* Bulk Actions */}
                      {filteredAndSortedDocs.length > 0 && (
                        <div className="flex items-center gap-2 border-r pr-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentPageIds = paginatedDocs.map(
                                (doc) => doc.id
                              );
                              const newSelected = [
                                ...new Set([
                                  ...selectedKnowledgeDocuments,
                                  ...currentPageIds,
                                ]),
                              ];
                              setSelectedKnowledgeDocuments(newSelected);
                              // Initialize requirements for new selections
                              const newReqs = { ...documentRequirements };
                              currentPageIds.forEach((id) => {
                                if (!newReqs[id]) newReqs[id] = "";
                              });
                              setDocumentRequirements(newReqs);
                            }}
                            className="gap-2 text-xs"
                          >
                            <CheckCircle className="h-3 w-3" />
                            Select Page
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const allIds = filteredAndSortedDocs.map(
                                (doc) => doc.id
                              );
                              setSelectedKnowledgeDocuments(allIds);
                              // Initialize requirements for all
                              const newReqs = { ...documentRequirements };
                              allIds.forEach((id) => {
                                if (!newReqs[id]) newReqs[id] = "";
                              });
                              setDocumentRequirements(newReqs);
                            }}
                            className="gap-2 text-xs"
                          >
                            <CheckCircle className="h-3 w-3" />
                            Select All
                          </Button>
                          {selectedKnowledgeDocs.length > 0 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedKnowledgeDocuments([]);
                                setDocumentRequirements({});
                              }}
                              className="gap-2 text-xs"
                            >
                              <X className="h-3 w-3" />
                              Clear
                            </Button>
                          )}
                        </div>
                      )}

                      {/* Show Selected Only Toggle */}
                      <Button
                        variant={showSelectedOnly ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          setShowSelectedOnly(!showSelectedOnly);
                          setCurrentPage(1);
                        }}
                        className="gap-2"
                      >
                        <Star className="h-4 w-4" />
                        Selected Only
                      </Button>

                      {/* View Mode Toggle */}
                      <div className="flex border rounded-lg overflow-hidden">
                        <Button
                          variant={viewMode === "list" ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setViewMode("list")}
                          className="rounded-none border-0"
                        >
                          <List className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={viewMode === "grid" ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setViewMode("grid")}
                          className="rounded-none border-0"
                        >
                          <Grid className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Filters Toggle */}
                      <Button
                        variant={showFilters ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowFilters(!showFilters)}
                        className="gap-2"
                      >
                        <Filter className="h-4 w-4" />
                        Filters
                        {showFilters ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Advanced Filters Panel */}
                  {showFilters && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-white dark:bg-emerald-950/10 rounded-lg border border-emerald-200 dark:border-emerald-800"
                    >
                      {/* Sort By */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Sort By</Label>
                        <select
                          value={sortBy}
                          onChange={(e) => setSortBy(e.target.value as any)}
                          className="w-full px-3 py-2 border border-emerald-200 dark:border-emerald-700 rounded-lg text-sm focus:border-emerald-500 bg-background"
                        >
                          <option value="name">Name</option>
                          <option value="date">Upload Date</option>
                          <option value="size">File Size</option>
                          <option value="type">File Type</option>
                        </select>
                      </div>

                      {/* Sort Order */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Order</Label>
                        <div className="flex border rounded-lg overflow-hidden">
                          <Button
                            variant={sortOrder === "asc" ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setSortOrder("asc")}
                            className="flex-1 rounded-none border-0 gap-2"
                          >
                            <SortAsc className="h-4 w-4" />
                            Asc
                          </Button>
                          <Button
                            variant={sortOrder === "desc" ? "default" : "ghost"}
                            size="sm"
                            onClick={() => setSortOrder("desc")}
                            className="flex-1 rounded-none border-0 gap-2"
                          >
                            <SortDesc className="h-4 w-4" />
                            Desc
                          </Button>
                        </div>
                      </div>

                      {/* Filter by Type */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">File Type</Label>
                        <select
                          value={filterType}
                          onChange={(e) => {
                            setFilterType(e.target.value);
                            setCurrentPage(1);
                          }}
                          className="w-full px-3 py-2 border border-emerald-200 dark:border-emerald-700 rounded-lg text-sm focus:border-emerald-500 bg-background"
                        >
                          <option value="all">All Types</option>
                          {uniqueTypes.map((type) => (
                            <option key={type} value={type}>
                              {type}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Filter by Status */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Status</Label>
                        <select
                          value={filterStatus}
                          onChange={(e) => {
                            setFilterStatus(e.target.value);
                            setCurrentPage(1);
                          }}
                          className="w-full px-3 py-2 border border-emerald-200 dark:border-emerald-700 rounded-lg text-sm focus:border-emerald-500 bg-background"
                        >
                          <option value="all">All Status</option>
                          {uniqueStatuses.map((status) => (
                            <option key={status} value={status}>
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                            </option>
                          ))}
                        </select>
                      </div>
                    </motion.div>
                  )}
                </div>

                {/* Selected Documents Count */}
                {selectedKnowledgeDocs.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="bg-emerald-100 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-lg p-4"
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-emerald-600 rounded-full">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-emerald-900 dark:text-emerald-100">
                          {selectedKnowledgeDocs.length} document
                          {selectedKnowledgeDocs.length !== 1 ? "s" : ""}{" "}
                          selected
                        </p>
                        <p className="text-sm text-emerald-700 dark:text-emerald-300">
                          {
                            Object.values(documentRequirements).filter((req) =>
                              req.trim()
                            ).length
                          }{" "}
                          with specific requirements • Ready for AI analysis
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Documents List */}
                <Card className="border-2 border-emerald-200 dark:border-emerald-800">
                  <ScrollArea className="h-[400px]">
                    <div className="p-4 space-y-3">
                      {isLoadingKnowledge ? (
                        <div className="flex flex-col items-center justify-center py-12 space-y-3">
                          <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
                          <span className="font-medium">
                            Loading documents...
                          </span>
                          <span className="text-sm text-muted-foreground">
                            Fetching from knowledge base
                          </span>
                        </div>
                      ) : filteredAndSortedDocs.length === 0 ? (
                        <div className="text-center py-12 space-y-4">
                          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto">
                            <BookOpen className="h-8 w-8 text-muted-foreground" />
                          </div>
                          <div>
                            <h3 className="font-medium text-lg mb-2">
                              {searchQuery
                                ? "No matching documents"
                                : "No documents found"}
                            </h3>
                            <p className="text-muted-foreground mb-4">
                              {searchQuery
                                ? "Try adjusting your search terms"
                                : "Upload documents to your knowledge base to get started"}
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            onClick={() =>
                              window.open("/knowledge-base", "_blank")
                            }
                            className="gap-2"
                          >
                            <ArrowRight className="h-4 w-4" />
                            Go to Knowledge Base
                          </Button>
                        </div>
                      ) : (
                        paginatedDocs.map((doc, index) => (
                          <motion.div
                            key={doc.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className={`group flex items-start space-x-4 p-4 border-2 rounded-xl transition-all duration-200 hover:shadow-md ${
                              selectedKnowledgeDocuments.includes(doc.id)
                                ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-950/20 shadow-sm"
                                : "border-gray-200 dark:border-gray-700 hover:border-emerald-300 dark:hover:border-emerald-700"
                            }`}
                          >
                            <Checkbox
                              id={doc.id}
                              checked={selectedKnowledgeDocuments.includes(
                                doc.id
                              )}
                              onCheckedChange={(checked) =>
                                handleKnowledgeDocSelection(
                                  doc.id,
                                  checked as boolean
                                )
                              }
                              className="mt-1 data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
                            />
                            <div className="flex-1 min-w-0 space-y-3">
                              <div className="flex items-center gap-3">
                                {getStatusIcon(doc.status)}
                                <Label
                                  htmlFor={doc.id}
                                  className="text-base font-medium cursor-pointer hover:text-emerald-600 transition-colors"
                                >
                                  {doc.name}
                                </Label>
                                <Badge
                                  variant="secondary"
                                  className="text-xs bg-gray-100 dark:bg-gray-800"
                                >
                                  {doc.type}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <FileText className="h-3 w-3" />
                                  {formatFileSize(doc.size)}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {formatDate(doc.uploadedAt)}
                                </span>
                                <span
                                  className={`flex items-center gap-1 font-medium ${
                                    doc.status === "success"
                                      ? "text-green-600"
                                      : doc.status === "processing"
                                      ? "text-yellow-600"
                                      : doc.status === "error"
                                      ? "text-red-600"
                                      : ""
                                  }`}
                                >
                                  <span className="capitalize">
                                    {doc.status}
                                  </span>
                                </span>
                              </div>
                              {doc.description && (
                                <p className="text-sm text-muted-foreground italic">
                                  {doc.description}
                                </p>
                              )}

                              {/* Document-specific requirement input */}
                              {selectedKnowledgeDocuments.includes(doc.id) && (
                                <motion.div
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: "auto" }}
                                  exit={{ opacity: 0, height: 0 }}
                                  transition={{ duration: 0.3 }}
                                  className="mt-4 pt-4 border-t border-emerald-200 dark:border-emerald-800"
                                >
                                  <Label
                                    htmlFor={`requirement-${doc.id}`}
                                    className="text-sm font-medium text-emerald-900 dark:text-emerald-100 mb-2 block flex items-center gap-2"
                                  >
                                    <Sparkles className="h-4 w-4" />
                                    What do you want from this document?
                                  </Label>
                                  <Textarea
                                    id={`requirement-${doc.id}`}
                                    placeholder="Describe what specific information, sections, or insights you need from this document for your project scope...

Examples:
• Extract technical requirements and specifications
• Identify key features and functionality
• Focus on timeline and budget constraints
• Analyze user requirements and acceptance criteria
• Review compliance and security requirements"
                                    value={documentRequirements[doc.id] || ""}
                                    onChange={(e) =>
                                      handleDocumentRequirementChange(
                                        doc.id,
                                        e.target.value
                                      )
                                    }
                                    rows={4}
                                    className="w-full text-sm border-2 border-emerald-200 dark:border-emerald-700 focus:border-emerald-500 dark:focus:border-emerald-400 rounded-lg bg-white dark:bg-emerald-950/10 resize-none transition-colors"
                                  />
                                  <div className="flex items-center justify-between mt-2 text-xs text-emerald-600 dark:text-emerald-400">
                                    <span className="flex items-center gap-1">
                                      <Sparkles className="h-3 w-3" />
                                      Be specific to get better AI analysis
                                    </span>
                                    <span>
                                      {
                                        (documentRequirements[doc.id] || "")
                                          .length
                                      }{" "}
                                      characters
                                    </span>
                                  </div>
                                </motion.div>
                              )}
                            </div>
                          </motion.div>
                        ))
                      )}
                    </div>
                  </ScrollArea>

                  {/* Pagination Controls */}
                  {totalPages > 1 && (
                    <div className="px-4 py-3 border-t bg-emerald-50 dark:bg-emerald-950/20">
                      <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                        <div className="text-sm text-emerald-700 dark:text-emerald-300">
                          Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                          {Math.min(
                            currentPage * itemsPerPage,
                            filteredAndSortedDocs.length
                          )}{" "}
                          of {filteredAndSortedDocs.length} documents
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(1)}
                            disabled={currentPage === 1}
                            className="px-2"
                          >
                            First
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setCurrentPage(Math.max(1, currentPage - 1))
                            }
                            disabled={currentPage === 1}
                          >
                            Previous
                          </Button>
                          <div className="flex items-center gap-1">
                            {Array.from(
                              { length: Math.min(5, totalPages) },
                              (_, i) => {
                                const pageNum =
                                  Math.max(
                                    1,
                                    Math.min(totalPages - 4, currentPage - 2)
                                  ) + i;
                                if (pageNum > totalPages) return null;
                                return (
                                  <Button
                                    key={pageNum}
                                    variant={
                                      currentPage === pageNum
                                        ? "default"
                                        : "outline"
                                    }
                                    size="sm"
                                    onClick={() => setCurrentPage(pageNum)}
                                    className="w-8 h-8 p-0"
                                  >
                                    {pageNum}
                                  </Button>
                                );
                              }
                            )}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setCurrentPage(
                                Math.min(totalPages, currentPage + 1)
                              )
                            }
                            disabled={currentPage === totalPages}
                          >
                            Next
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(totalPages)}
                            disabled={currentPage === totalPages}
                            className="px-2"
                          >
                            Last
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </Card>

                {/* Selected Documents Summary */}
                {selectedKnowledgeDocuments.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6"
                  >
                    <Card className="border-2 border-emerald-300 dark:border-emerald-700 bg-emerald-50 dark:bg-emerald-950/30">
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-emerald-600 rounded-full">
                            <CheckCircle className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-lg text-emerald-900 dark:text-emerald-100">
                              Selected Documents Summary
                            </CardTitle>
                            <CardDescription className="text-emerald-700 dark:text-emerald-300">
                              {selectedKnowledgeDocuments.length} document
                              {selectedKnowledgeDocuments.length !== 1
                                ? "s"
                                : ""}{" "}
                              ready for AI analysis
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {selectedKnowledgeDocs.map((docId) => {
                            const doc = knowledgeDocs.find(
                              (d) => d.id === docId
                            );
                            const requirement = documentRequirements[docId];
                            if (!doc) return null;

                            return (
                              <div
                                key={docId}
                                className="bg-white dark:bg-emerald-950/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800"
                              >
                                <div className="flex items-start gap-3">
                                  <div className="p-2 bg-emerald-100 dark:bg-emerald-900/50 rounded-lg">
                                    <FileText className="h-4 w-4 text-emerald-600" />
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="font-medium text-emerald-900 dark:text-emerald-100 mb-1">
                                      {doc.name}
                                    </h4>
                                    <div className="flex items-center gap-2 mb-3">
                                      <Badge
                                        variant="secondary"
                                        className="text-xs"
                                      >
                                        {doc.type}
                                      </Badge>
                                      <span className="text-xs text-emerald-600 dark:text-emerald-400">
                                        {formatFileSize(doc.size)}
                                      </span>
                                    </div>
                                    {requirement ? (
                                      <div className="bg-emerald-50 dark:bg-emerald-950/30 rounded-md p-3 border border-emerald-200 dark:border-emerald-800">
                                        <p className="text-sm text-emerald-800 dark:text-emerald-200 font-medium mb-1">
                                          Requirements:
                                        </p>
                                        <p className="text-sm text-emerald-700 dark:text-emerald-300 leading-relaxed">
                                          {requirement}
                                        </p>
                                      </div>
                                    ) : (
                                      <div className="bg-amber-50 dark:bg-amber-950/30 rounded-md p-3 border border-amber-200 dark:border-amber-800">
                                        <p className="text-sm text-amber-800 dark:text-amber-200 flex items-center gap-2">
                                          <AlertTriangle className="h-4 w-4" />
                                          No specific requirements provided - AI
                                          will analyze the entire document
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="flex justify-between items-center pt-8 border-t"
      >
        <Button variant="outline" onClick={onBack} className="gap-2 h-12 px-6">
          <ArrowRight className="h-4 w-4 rotate-180" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!isCurrentStepValid()}
          className="gap-2 h-12 px-8 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>Continue</span>
          <Zap className="h-4 w-4" />
        </Button>
      </motion.div>
    </div>
  );
};

export default RequirementsStep;
