import React from 'react';
import { ScopingInfo, UseScopingReturn } from '../../../hooks/useScoping';

interface ScopingInfoFormProps {
  onNext: () => void;
  onBack: () => void;
  state: UseScopingReturn['state'];
  setScopingInfo: (info: ScopingInfo) => void;
}

const ScopingInfoForm: React.FC<ScopingInfoFormProps> = ({
  onNext,
  onBack,
  state,
  setScopingInfo
}) => {
  const [goals, setGoals] = React.useState<string[]>(
    state.scopingInfo?.goals || ['']
  );

  const handleGoalChange = (index: number, value: string) => {
    const updatedGoals = [...goals];
    updatedGoals[index] = value;
    setGoals(updatedGoals);
  };

  const addGoal = () => {
    setGoals([...goals, '']);
  };

  const removeGoal = (index: number) => {
    if (goals.length > 1) {
      const updatedGoals = goals.filter((_, i) => i !== index);
      setGoals(updatedGoals);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Filter out empty goals
    const filteredGoals = goals.filter(goal => goal.trim() !== '');
    
    const scopingInfo: ScopingInfo = {
      projectName: state.scopingInfo?.projectName || '',
      projectDescription: state.scopingInfo?.projectDescription || '',
      timeline: state.scopingInfo?.timeline || '',
      budget: state.scopingInfo?.budget || '',
      goals: filteredGoals
    };
    
    setScopingInfo(scopingInfo);
    onNext();
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Project Information</h2>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <label htmlFor="projectName" className="block text-sm font-medium text-gray-700">
              Project Name
            </label>
            <input
              type="text"
              id="projectName"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="e.g. Website Redesign"
              value={state.scopingInfo?.projectName || ''}
              onChange={(e) => setScopingInfo({
                ...state.scopingInfo as ScopingInfo,
                projectName: e.target.value
              })}
              required
            />
          </div>

          <div>
            <label htmlFor="projectDescription" className="block text-sm font-medium text-gray-700">
              Project Description
            </label>
            <textarea
              id="projectDescription"
              rows={4}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Detailed description of the project"
              value={state.scopingInfo?.projectDescription || ''}
              onChange={(e) => setScopingInfo({
                ...state.scopingInfo as ScopingInfo,
                projectDescription: e.target.value
              })}
              required
            />
          </div>

          <div>
            <label htmlFor="timeline" className="block text-sm font-medium text-gray-700">
              Timeline
            </label>
            <input
              type="text"
              id="timeline"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="e.g. 3 months"
              value={state.scopingInfo?.timeline || ''}
              onChange={(e) => setScopingInfo({
                ...state.scopingInfo as ScopingInfo,
                timeline: e.target.value
              })}
              required
            />
          </div>

          <div>
            <label htmlFor="budget" className="block text-sm font-medium text-gray-700">
              Budget
            </label>
            <input
              type="text"
              id="budget"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="e.g. $50,000 - $75,000"
              value={state.scopingInfo?.budget || ''}
              onChange={(e) => setScopingInfo({
                ...state.scopingInfo as ScopingInfo,
                budget: e.target.value
              })}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project Goals
            </label>
            
            {goals.map((goal, index) => (
              <div key={index} className="flex items-center mb-2">
                <input
                  type="text"
                  className="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder={`Goal ${index + 1}`}
                  value={goal}
                  onChange={(e) => handleGoalChange(index, e.target.value)}
                  required={index === 0}
                />
                {goals.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeGoal(index)}
                    className="ml-2 p-2 text-red-600 hover:text-red-800"
                  >
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            ))}
            
            <button
              type="button"
              onClick={addGoal}
              className="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Goal
            </button>
          </div>

          <div className="flex justify-between">
            <button
              type="button"
              onClick={onBack}
              className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Back
            </button>
            <button
              type="submit"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Next
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ScopingInfoForm; 