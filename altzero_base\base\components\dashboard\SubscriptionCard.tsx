import React from "react";
import { <PERSON> } from "react-router-dom";
import { useSubscription } from "../../contextapi/SubscriptionContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Progress } from "../ui/progress";
import { ArrowRight } from "lucide-react";

export function SubscriptionCard() {
  const {
    userSubscription,
    subscriptionSummary,
    isLoading,
    getFormattedSubscriptionStatus,
    getFormattedNextBillingDate,
  } = useSubscription();

  const statusText = getFormattedSubscriptionStatus();
  const nextBillingDate = getFormattedNextBillingDate();

  // Get the top resource usage item
  const topResource = subscriptionSummary?.resources?.sort(
    (a, b) => b.percentage_used - a.percentage_used
  )[0];

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription</CardTitle>
          <CardDescription>Loading...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!userSubscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription</CardTitle>
          <CardDescription>No active subscription</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-6">
            Subscribe to a plan to get access to all features.
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild>
            <Link to="/subscription">View Plans</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Subscription</CardTitle>
            <CardDescription>Current plan details</CardDescription>
          </div>
          <Badge
            variant={
              userSubscription.status === "active" ||
              userSubscription.status === "trialing"
                ? "default"
                : "destructive"
            }
          >
            {statusText}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-xl font-bold">{userSubscription.plan?.name}</h3>
            <p className="text-sm text-muted-foreground">
              {userSubscription.billing_cycle === "monthly"
                ? "Monthly"
                : "Yearly"}{" "}
              billing
            </p>
          </div>

          {nextBillingDate && (
            <div>
              <p className="text-sm text-muted-foreground">Next billing date</p>
              <p className="font-medium">{nextBillingDate}</p>
            </div>
          )}

          {topResource && (
            <div className="space-y-1 pt-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  {topResource.name} Usage
                </span>
                <span>
                  {topResource.current_usage} / {topResource.total_limit}{" "}
                  {topResource.unit_label}
                </span>
              </div>
              <Progress
                value={topResource.percentage_used}
                className="h-2"
                indicatorClassName={
                  topResource.percentage_used > 90
                    ? "bg-destructive"
                    : topResource.percentage_used > 75
                    ? "bg-yellow-500"
                    : undefined
                }
              />
            </div>
          )}

          {userSubscription.cancel_at_period_end && (
            <div className="mt-2 text-sm text-destructive font-medium">
              Your subscription will end after the current billing period.
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" asChild className="w-full">
          <Link to="/subscription" className="flex items-center justify-center">
            Manage Subscription
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
