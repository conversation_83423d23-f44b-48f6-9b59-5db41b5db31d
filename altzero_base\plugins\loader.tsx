import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { getEnabledPlugins, getPluginConfig } from './registry';
import type { 
  PluginModule, 
  PluginLoadingState, 
  PluginContextType,
  NavigationItem,
  UsePluginNavigationResult,
  UsePluginRoutesResult
} from './types';

// Plugin Context
const PluginContext = createContext<PluginContextType | undefined>(undefined);

// Plugin Provider Component
export const PluginProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [loadedPlugins, setLoadedPlugins] = useState<Record<string, PluginModule>>({});
  const [loadingStates, setLoadingStates] = useState<Record<string, PluginLoadingState>>({});
  const [enabledPlugins] = useState<string[]>(getEnabledPlugins());

  const loadPlugin = useCallback(async (pluginName: string) => {
    console.log(`🔄 Loading plugin: ${pluginName}`);
    
    setLoadingStates(prev => ({
      ...prev,
      [pluginName]: { pluginName, status: 'loading' }
    }));

    try {
      // Dynamic import from features folder
      const pluginModule = await import(`../features/${pluginName}/index.ts`);
      
      if (!pluginModule.default) {
        throw new Error(`Plugin ${pluginName} does not have a default export`);
      }

      const plugin: PluginModule = pluginModule.default;
      
      // Run plugin initialization if available
      if (plugin.initialize) {
        await plugin.initialize();
      }

      setLoadedPlugins(prev => ({
        ...prev,
        [pluginName]: plugin
      }));

      setLoadingStates(prev => ({
        ...prev,
        [pluginName]: { 
          pluginName, 
          status: 'loaded', 
          module: plugin 
        }
      }));

      console.log(`✅ Plugin loaded successfully: ${pluginName}`);
    } catch (error) {
      console.error(`❌ Failed to load plugin: ${pluginName}`, error);
      
      setLoadingStates(prev => ({
        ...prev,
        [pluginName]: { 
          pluginName, 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    }
  }, []);

  useEffect(() => {
    const loadAllPlugins = async () => {
      console.log(`🚀 Loading ${enabledPlugins.length} enabled plugins:`, enabledPlugins);
      
      // Load all enabled plugins
      await Promise.allSettled(
        enabledPlugins.map(pluginName => loadPlugin(pluginName))
      );
    };

    loadAllPlugins();
  }, [enabledPlugins, loadPlugin]);

  const isPluginLoaded = useCallback((pluginName: string): boolean => {
    return loadingStates[pluginName]?.status === 'loaded';
  }, [loadingStates]);

  const getPluginModule = useCallback((pluginName: string): PluginModule | undefined => {
    return loadedPlugins[pluginName];
  }, [loadedPlugins]);

  const contextValue: PluginContextType = {
    loadedPlugins,
    loadingStates,
    enabledPlugins,
    isPluginLoaded,
    getPluginModule
  };

  return (
    <PluginContext.Provider value={contextValue}>
      {children}
    </PluginContext.Provider>
  );
};

// Plugin Context Hook
export const usePluginContext = (): PluginContextType => {
  const context = useContext(PluginContext);
  if (!context) {
    throw new Error('usePluginContext must be used within a PluginProvider');
  }
  return context;
};

// Hook for Plugin Navigation
export const usePluginNavigation = (): UsePluginNavigationResult => {
  const { loadedPlugins, loadingStates } = usePluginContext();
  const [navigation, setNavigation] = useState<NavigationItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>();

  useEffect(() => {
    try {
      const navItems: NavigationItem[] = [];
      
      Object.entries(loadedPlugins).forEach(([pluginName, plugin]) => {
        if (plugin.navigation && Array.isArray(plugin.navigation)) {
          navItems.push(...plugin.navigation);
        }
      });

      // Sort by order if specified
      navItems.sort((a, b) => (a.order || 999) - (b.order || 999));
      
      setNavigation(navItems);
      setError(undefined);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load navigation');
    } finally {
      setIsLoading(false);
    }
  }, [loadedPlugins]);

  return { navigation, isLoading, error };
};

// Hook for Plugin Routes
export const usePluginRoutes = (): UsePluginRoutesResult => {
  const { loadedPlugins } = usePluginContext();
  const [routes, setRoutes] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>();

  useEffect(() => {
    try {
      const allRoutes: any[] = [];
      
      Object.entries(loadedPlugins).forEach(([pluginName, plugin]) => {
        if (plugin.routes && Array.isArray(plugin.routes)) {
          allRoutes.push(...plugin.routes);
        }
      });
      
      setRoutes(allRoutes);
      setError(undefined);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load routes');
    } finally {
      setIsLoading(false);
    }
  }, [loadedPlugins]);

  return { routes, isLoading, error };
};

// Plugin Routes Component
export const PluginRoutes: React.FC = () => {
  const { routes, isLoading, error } = usePluginRoutes();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-3 text-muted-foreground">Loading plugins...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive mb-2">Plugin Loading Error</h2>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  // 404 Not Found component
  const NotFound = () => (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 p-6">
      <h1 className="text-4xl font-bold text-gray-900 mb-2">404</h1>
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">
        Page Not Found
      </h2>
      <p className="text-gray-600 mb-8">
        The page you are looking for doesn't exist or has been moved.
      </p>
      <a
        href="/"
        className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
      >
        Go Back Home
      </a>
    </div>
  );

  return (
    <Routes>
      {routes.map((route, index) => {
        const key = route.path || `plugin-route-${index}`;
        
        // Handle index routes differently
        if (route.index) {
          return (
            <Route 
              key={key}
              index={true}
              element={route.element}
            />
          );
        }
        
        // Handle regular routes
        return (
          <Route 
            key={key} 
            path={route.path} 
            element={route.element}
          />
        );
      })}
      {/* 404 fallback for unknown routes */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

// Plugin Providers Wrapper
export const PluginProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { loadedPlugins } = usePluginContext();

  // Collect all plugin providers
  const providers = Object.values(loadedPlugins)
    .flatMap(plugin => plugin.providers || [])
    .reverse(); // Reverse to apply in correct order

  // Wrap children in all plugin providers
  return providers.reduce(
    (wrapped, Provider) => <Provider>{wrapped}</Provider>,
    children
  );
}; 