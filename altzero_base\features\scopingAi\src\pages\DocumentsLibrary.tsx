import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import { ArrowLeft, Search, Upload } from "lucide-react";
import { Button } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import { useUser } from "../../../../base/contextapi/UserContext";
import { useToast } from "../../../../base/hooks/use-toast";
import ScopingAILayout from "../components/ScopingAILayout";
import { DocumentDialog } from "../components/DocumentDialog";
import {
  DocumentLibraryTabs,
  DocumentPreview,
  UploadDialog,
} from "../components/documents/library";
import {
  fetchProposalTemplates,
  makeDocumentPublic,
  saveDocumentToStorage,
  fetchMarkdownContent,
} from "../services/documentLibraryService";
import {
  ProcessedDocument,
  DocumentData,
  TemplateDocument,
  LibraryDocument,
} from "../types/document-library";

export default function DocumentsLibrary() {
  const { user } = useUser();
  const { toast } = useToast();

  // State
  const [isUploading, setIsUploading] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [processedDocument, setProcessedDocument] =
    useState<ProcessedDocument | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<DocumentData | null>(
    null
  );
  const [isEditMode, setIsEditMode] = useState(false);
  const [existingDocuments, setExistingDocuments] = useState<LibraryDocument[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Static hardcoded template documents
  const templateDocuments: TemplateDocument[] = [
    {
      id: "template-1",
      title: "Standard Project Proposal",
      description: "A comprehensive template for software project proposals",
      date: "Jan 15, 2023",
      sections: ["Overview", "Scope", "Timeline", "Budget", "Team"],
      rawData: {
        content:
          "# Standard Project Proposal\n\n## Overview\n\nThis is a standard project proposal template.\n\n## Scope\n\nDefine the project scope here.\n\n## Timeline\n\nOutline project milestones and deadlines.\n\n## Budget\n\nDetail the project costs and payment schedule.\n\n## Team\n\nIntroduce the team members and their roles.",
        sections: [
          {
            title: "Overview",
            content: "This is a standard project proposal template.",
            description: "Project introduction",
            order: 0,
          },
          {
            title: "Scope",
            content: "Define the project scope here.",
            description: "Scope definition",
            order: 1,
          },
          {
            title: "Timeline",
            content: "Outline project milestones and deadlines.",
            description: "Project schedule",
            order: 2,
          },
          {
            title: "Budget",
            content: "Detail the project costs and payment schedule.",
            description: "Financial details",
            order: 3,
          },
          {
            title: "Team",
            content: "Introduce the team members and their roles.",
            description: "Team composition",
            order: 4,
          },
        ],
        structured_content: {
          pages: [
            {
              page_number: 1,
              content: [
                {
                  type: "text",
                  content: "Standard Project Proposal",
                  position: { x: 0.1, y: 0.1, page: 1 },
                },
              ],
            },
          ],
        },
        metadata: {
          author: "ScopingAI Team",
          createdDate: "2023-01-15",
          modifiedDate: "2023-01-15",
          pageCount: 5,
          fileType: "Template",
        },
      },
    },
    {
      id: "template-2",
      title: "Web Application SOW",
      description: "Statement of Work template for web applications",
      date: "Mar 22, 2023",
      sections: [
        "Introduction",
        "Requirements",
        "Deliverables",
        "Acceptance Criteria",
        "Timeline",
      ],
      rawData: {
        content:
          "# Web Application SOW\n\n## Introduction\n\nThis Statement of Work outlines the development of a web application.\n\n## Requirements\n\nList of functional and non-functional requirements.\n\n## Deliverables\n\nSpecific components to be delivered.\n\n## Acceptance Criteria\n\nCriteria for project acceptance.\n\n## Timeline\n\nDetailed project schedule.",
        sections: [
          {
            title: "Introduction",
            content:
              "This Statement of Work outlines the development of a web application.",
            description: "Project context",
            order: 0,
          },
          {
            title: "Requirements",
            content: "List of functional and non-functional requirements.",
            description: "Project specifications",
            order: 1,
          },
          {
            title: "Deliverables",
            content: "Specific components to be delivered.",
            description: "Project outputs",
            order: 2,
          },
          {
            title: "Acceptance Criteria",
            content: "Criteria for project acceptance.",
            description: "Validation metrics",
            order: 3,
          },
          {
            title: "Timeline",
            content: "Detailed project schedule.",
            description: "Project timeline",
            order: 4,
          },
        ],
        structured_content: {
          pages: [
            {
              page_number: 1,
              content: [
                {
                  type: "text",
                  content: "Web Application SOW",
                  position: { x: 0.1, y: 0.1, page: 1 },
                },
              ],
            },
          ],
        },
        metadata: {
          author: "ScopingAI Team",
          createdDate: "2023-03-22",
          modifiedDate: "2023-03-22",
          pageCount: 5,
          fileType: "Template",
        },
      },
    },
    {
      id: "template-3",
      title: "Agile Development Contract",
      description: "Legal contract template for agile software development",
      date: "Jun 5, 2023",
      sections: [
        "Parties",
        "Services",
        "Sprints",
        "Payments",
        "Intellectual Property",
        "Termination",
      ],
      rawData: {
        content:
          "# Agile Development Contract\n\n## Parties\n\nDetails of the parties involved.\n\n## Services\n\nDescription of services to be provided.\n\n## Sprints\n\nSprint planning and execution details.\n\n## Payments\n\nPayment terms and schedule.\n\n## Intellectual Property\n\nOwnership and rights to project outputs.\n\n## Termination\n\nTerms for contract termination.",
        sections: [
          {
            title: "Parties",
            content: "Details of the parties involved.",
            description: "Contract stakeholders",
            order: 0,
          },
          {
            title: "Services",
            content: "Description of services to be provided.",
            description: "Service details",
            order: 1,
          },
          {
            title: "Sprints",
            content: "Sprint planning and execution details.",
            description: "Sprint methodology",
            order: 2,
          },
          {
            title: "Payments",
            content: "Payment terms and schedule.",
            description: "Financial terms",
            order: 3,
          },
          {
            title: "Intellectual Property",
            content: "Ownership and rights to project outputs.",
            description: "IP rights",
            order: 4,
          },
          {
            title: "Termination",
            content: "Terms for contract termination.",
            description: "Exit conditions",
            order: 5,
          },
        ],
        structured_content: {
          pages: [
            {
              page_number: 1,
              content: [
                {
                  type: "text",
                  content: "Agile Development Contract",
                  position: { x: 0.1, y: 0.1, page: 1 },
                },
              ],
            },
          ],
        },
        metadata: {
          author: "ScopingAI Team",
          createdDate: "2023-06-05",
          modifiedDate: "2023-06-05",
          pageCount: 6,
          fileType: "Template",
        },
      },
    },
  ];

  // Fetch existing templates from Supabase on component mount
  useEffect(() => {
    if (user) {
      fetchExistingTemplates();
    }
  }, [user]);

  // Filter documents based on search query
  const filteredDocuments = existingDocuments.filter(
    (doc) =>
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.client.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Fetch templates from proposal_templates table
  const fetchExistingTemplates = async () => {
    try {
      setIsLoading(true);
      const templates = await fetchProposalTemplates();
      setExistingDocuments(templates);
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast({
        title: "Error",
        description: "Failed to load existing templates",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Event Handlers

  // Handle document preview
  const handleDocumentPreview = async (documentId: number | string) => {
    try {
      setIsLoading(true);

      // Find the document in our existing list
      const doc = existingDocuments.find((d) => d.id === documentId);
      if (!doc || !doc.markdown_path) {
        throw new Error("Document not found or missing markdown path");
      }

      // Fetch the markdown content
      const markdownData = await fetchMarkdownContent(doc.markdown_path);

      // Set the selected document
      setSelectedDocument({
        id: doc.id,
        title: doc.title,
        content: markdownData.content,
        metadata: doc.rawData?.metadata || {
          pageCount: markdownData.sections.length,
          fileType: doc.type,
        },
        sections: markdownData.sections,
      });

      setIsEditMode(false);
    } catch (error) {
      console.error("Error loading document:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to load document",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle document edit
  const handleDocumentEdit = async (documentId: number | string) => {
    await handleDocumentPreview(documentId);
    setIsEditMode(true);
  };

  // Handle template preview
  const handleTemplatePreview = (templateId: string) => {
    const template = templateDocuments.find((t) => t.id === templateId);
    if (template) {
      setSelectedDocument({
        id: parseInt(template.id.replace("template-", "")),
        title: template.title,
        content: template.rawData.content || "",
        metadata: template.rawData.metadata || {
          fileType: "Template",
        },
        sections:
          template.rawData.sections ||
          template.sections.map((section: string) => ({
            title: section,
            content: section,
            description: `Section: ${section}`,
          })),
      });
      setIsEditMode(false);
    }
  };

  // Handle making document public (convert to template)
  const handleMakeDocumentPublic = async (docId: string) => {
    try {
      setIsLoading(true);
      await makeDocumentPublic(docId);

      toast({
        title: "Success",
        description: "Document marked as public template",
      });

      // Refresh templates
      await fetchExistingTemplates();
    } catch (error) {
      console.error("Error making document public:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to update document",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle when processed document is ready
  const handleProcessedDocument = (document: ProcessedDocument) => {
    setProcessedDocument(document);
    setShowPreview(true);
  };

  // Handle document save
  const handleDocumentSave = async (document: DocumentData) => {
    try {
      setIsLoading(true);
      await saveDocumentToStorage(document);

      toast({
        title: "Success",
        description: "Document saved successfully",
      });

      // Refresh the template list
      await fetchExistingTemplates();

      // Close the document dialog
      setSelectedDocument(null);
    } catch (error) {
      console.error("Error saving document:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save document",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle template saving for uploaded documents
  const handleSaveTemplate = async () => {
    if (!processedDocument || !showPreview) return;

    try {
      setIsUploading(true);
      await saveDocumentToStorage({
        title: processedDocument.title,
        content: processedDocument.fullContent,
        metadata: processedDocument.metadata,
        sections: processedDocument.sections,
      });

      toast({
        title: "Success",
        description: "Document uploaded and saved successfully",
      });

      setShowPreview(false);
      setProcessedDocument(null);

      // Refresh the template list
      await fetchExistingTemplates();

      // Close the upload dialog
      setIsUploadDialogOpen(false);
    } catch (error) {
      console.error("Error saving template:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save template",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <ScopingAILayout>
      <div className="container mx-auto px-6 py-8">
        <div className="space-y-6">
          <div>
            <Link
              to="/scopingai/knowledge-base"
              className="flex items-center text-muted-foreground hover:text-foreground mb-4"
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Knowledge Base
            </Link>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">Document Library</h1>
                <p className="text-muted-foreground mt-1">
                  Manage and access your reference documents
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => setIsUploadDialogOpen(true)}
                  className="gap-2"
                >
                  <Upload size={16} />
                  Upload Document
                </Button>
              </div>
            </div>
          </div>

          <div className="flex items-center mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search documents..."
                className="w-full bg-background pl-8 focus-visible:ring-primary"
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setSearchQuery(e.target.value)
                }
              />
            </div>
          </div>

          {/* Main content - Tabs */}
          <DocumentLibraryTabs
            isLoading={isLoading}
            existingDocuments={filteredDocuments}
            templateDocuments={templateDocuments}
            onDocumentPreview={handleDocumentPreview}
            onDocumentEdit={handleDocumentEdit}
            onTemplatePreview={handleTemplatePreview}
            onMakeDocumentPublic={handleMakeDocumentPublic}
            onUploadClick={() => setIsUploadDialogOpen(true)}
          />

          {/* Upload Dialog */}
          <UploadDialog
            isOpen={isUploadDialogOpen}
            onClose={() => setIsUploadDialogOpen(false)}
            onDocumentProcessed={handleProcessedDocument}
          />

          {/* Document Preview Dialog */}
          {showPreview && processedDocument && (
            <DocumentPreview
              processedDocument={processedDocument}
              onSave={handleSaveTemplate}
              onCancel={() => {
                setShowPreview(false);
                setProcessedDocument(null);
              }}
              isLoading={isUploading}
            />
          )}

          {/* Document View/Edit Dialog */}
          {selectedDocument && (
            <DocumentDialog
              isOpen={true}
              onClose={() => {
                console.log("Closing document dialog");
                setSelectedDocument(null);
              }}
              onSave={(document: { title: string; content: string }) => {
                // Convert the simple document format to DocumentData format
                const documentData: DocumentData = {
                  ...selectedDocument,
                  title: document.title,
                  content: document.content,
                };
                handleDocumentSave(documentData);
              }}
              document={{
                title: selectedDocument.title,
                content: selectedDocument.content,
                metadata: selectedDocument.metadata,
                sections: selectedDocument.sections,
              }}
              readOnly={!isEditMode}
            />
          )}
        </div>
      </div>
    </ScopingAILayout>
  );
}
