import React, { useState } from 'react';
import { Source } from '../../hooks/useChat';

interface SourceViewerProps {
  sources: Source[];
}

const SourceViewer: React.FC<SourceViewerProps> = ({ sources }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!sources || sources.length === 0) {
    return null;
  }

  return (
    <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center text-xs font-medium text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300"
      >
        <svg 
          className={`w-3 h-3 mr-1 transition-transform ${isExpanded ? 'transform rotate-90' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
        </svg>
        Sources ({sources.length})
      </button>
      
      {isExpanded && (
        <div className="mt-2 space-y-2">
          {sources.map((source, index) => (
            <div 
              key={index}
              className="text-xs bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded p-2 overflow-hidden"
            >
              <div className="flex justify-between items-start mb-1">
                <div className="font-medium text-gray-700 dark:text-gray-300 truncate mr-2">
                  {source.document?.title || source.source || 'Unknown source'}
                </div>
                <div className="flex-shrink-0 text-gray-500 dark:text-gray-400">
                  {(source.score * 100).toFixed(0)}% match
                </div>
              </div>
              
              <div className="text-gray-600 dark:text-gray-400 line-clamp-3">
                {source.content}
              </div>
              
              {source.document && (
                <div className="mt-1 flex items-center text-gray-500 dark:text-gray-400">
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  <span className="truncate">{source.document.filename}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SourceViewer; 