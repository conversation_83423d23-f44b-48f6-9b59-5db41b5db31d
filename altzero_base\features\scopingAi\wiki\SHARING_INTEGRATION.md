# Document Sharing Integration Guide

This guide explains how to integrate the document sharing system with your existing `scoping_documents` table using direct TypeScript queries (no Supabase functions).

## 🚀 Quick Start

### 1. Database Setup

First, run the SQL migration:

```sql
-- Run the documentSharing.sql file
\i altzero_base/supabase/documentSharing.sql
```

**Note:** This only creates tables, types, and policies - no Supabase functions are used. All logic is implemented in TypeScript for better control and debugging.

### 2. Add Share Button to Document Pages

Add the share button to any page that displays a document:

```tsx
import { ShareButton } from "../path/to/scopingAi/src/components/sharing";

// In your document component
<ShareButton
  documentId={document.id}
  documentTitle={document.title}
  canManage={document.user_id === currentUserId}
/>;
```

### 3. Create a Shared Documents Page

Create a new page to show documents shared with the user:

```tsx
import { SharedDocumentsView } from "../path/to/scopingAi/src/components/sharing";

export const SharedDocumentsPage = () => {
  return (
    <div className="container mx-auto py-6">
      <SharedDocumentsView />
    </div>
  );
};
```

## 📋 Features Available

### ✅ Share with Users

- Share documents with individual users
- Set permission levels (view, comment, edit, manage, admin)
- Add custom messages
- Set expiration dates

### ✅ Share with Teams

- Share with entire teams/groups
- Uses your existing `groups` and `group_members` tables

### ✅ Share with Organizations

- Share with entire organizations
- Uses your existing `organisations` and `organisation_members` tables

### ✅ Create Share Links

- Generate secure shareable links
- Password protection (hashed)
- Access limits
- Expiration dates

### ✅ Comments & Collaboration

- Add comments to shared documents
- Track document activities
- Real-time collaboration sessions

## 🔧 API Functions Available

### Sharing Functions (Direct TypeScript Queries)

```typescript
// Share with a user
await shareDocumentWithUser(documentId, userId, "edit", "Check this out!");

// Share with a team
await shareDocumentWithTeam(documentId, groupId, "view");

// Create a share link
const token = await createDocumentShareLink(documentId, "view", "password123");

// Get all shares for a document
const { shares, links } = await getDocumentShares(documentId);
```

### Collaboration Functions

```typescript
// Add a comment
await addDocumentComment(documentId, "Great work!", "section-1");

// Get shared documents for current user
const sharedDocs = await getSharedDocuments();
```

## 🔒 Permission Levels

- **view**: Can only view the document
- **comment**: Can view and add comments
- **suggest**: Can view, comment, and suggest edits
- **edit**: Can view, comment, suggest, and edit content
- **manage**: Can do everything including sharing permissions
- **admin**: Full control including deletion

## 🎨 Components Available

### ShareButton

Simple dropdown button for sharing actions:

```tsx
<ShareButton
  documentId="uuid"
  documentTitle="My Document"
  canManage={true}
  variant="outline"
  size="sm"
/>
```

### DocumentSharingModal

Full-featured sharing modal:

```tsx
<DocumentSharingModal
  isOpen={true}
  onClose={() => setOpen(false)}
  documentId="uuid"
  documentTitle="My Document"
  canManage={true}
  initialTab="share"
/>
```

### SharedDocumentsView

Complete view of shared documents:

```tsx
<SharedDocumentsView />
```

## 🔗 Integration Points

### With Your Existing Tables

- ✅ **scoping_documents**: Main document table (already integrated)
- ✅ **profiles**: User profiles for sharing
- ✅ **groups**: Team sharing
- ✅ **group_members**: Team membership
- ✅ **organisations**: Organization sharing
- ✅ **organisation_members**: Organization membership
- ✅ **auth.users**: Authentication

### New Tables Created

- **document_shares**: Unified sharing table
- **document_interactions**: Comments, activities, sessions
- **document_favorites**: User bookmarks

## 🚨 Security Features

- Row Level Security (RLS) policies
- Permission-based access control
- Secure token generation (using crypto)
- Password hashing for protected links
- Document ownership validation
- Access level validation

## 📱 Usage Examples

### Basic Document Page Integration

```tsx
import React from "react";
import { ShareButton } from "./components/sharing";

const DocumentPage = ({ document, currentUser }) => {
  return (
    <div className="document-page">
      <div className="document-header">
        <h1>{document.title}</h1>
        <div className="actions">
          <ShareButton
            documentId={document.id}
            documentTitle={document.title}
            canManage={document.user_id === currentUser.id}
          />
        </div>
      </div>
      <div className="document-content">
        {/* Your existing document content */}
      </div>
    </div>
  );
};
```

### Navigation Integration

```tsx
// Add to your navigation menu
<NavItem href="/shared-documents">
  <FileText className="h-4 w-4 mr-2" />
  Shared with Me
</NavItem>
```

## 🔧 Customization

### Styling

All components use your existing design system via Tailwind CSS and your base components.

### URLs

Update the document opening URL in `SharedDocumentsView.tsx`:

```typescript
const openDocument = (documentId: string) => {
  // Update this to match your routing
  window.open(`/your-document-route/${documentId}`, "_blank");
};
```

### Notifications

Implement the notification system in `documentSharingService.ts`:

```typescript
const sendShareNotification = async (target, documentId, message) => {
  // Add your notification logic here
  // Email, in-app notifications, etc.
};
```

## 🏗️ Architecture Benefits

### No Supabase Functions

- **Better Debugging**: All logic in TypeScript, easier to debug
- **Type Safety**: Full TypeScript support throughout
- **Version Control**: All logic in your codebase
- **Testing**: Easier to unit test TypeScript functions
- **Flexibility**: Easier to modify and extend

### Direct Query Benefits

- **Performance**: Optimized queries for your specific use case
- **Maintainability**: Clear, readable TypeScript code
- **Error Handling**: Better error messages and handling
- **Security**: Permission checks in TypeScript with full context

## 🐛 Troubleshooting

### Common Issues

1. **RLS Policy Errors**: If you get infinite recursion errors, temporarily disable RLS:

   ```sql
   ALTER TABLE public.document_shares DISABLE ROW LEVEL SECURITY;
   ALTER TABLE public.document_interactions DISABLE ROW LEVEL SECURITY;
   ALTER TABLE public.document_favorites DISABLE ROW LEVEL SECURITY;
   ```

2. **Missing Dependencies**: Install required packages:

   ```bash
   npm install date-fns
   ```

3. **Permission Errors**: Check that:

   - User is document owner (stored in `scoping_documents.user_id`)
   - Target users/teams/orgs exist in your tables
   - User has proper permissions for the action

4. **Type Errors**: Ensure all types are properly imported from the sharing module.

## 🚀 Performance Tips

1. **Indexes**: The SQL file creates proper indexes for fast queries
2. **Pagination**: For large lists, consider adding pagination to the queries
3. **Caching**: Consider caching user permissions for frequently accessed documents
4. **Batch Operations**: For bulk sharing, consider batch operations

That's it! Your document sharing system is now ready to use with your existing `scoping_documents` table using direct TypeScript queries. 🎉
