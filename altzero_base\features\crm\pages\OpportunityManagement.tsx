import React, { useState, useEffect } from "react";
import {
  Plus,
  Target,
  DollarSign,
  TrendingUp,
  Calendar,
  RefreshCw,
  MoreHorizontal,
  Download,
  Upload,
  Filter,
} from "lucide-react";
import { crmService } from "../services/crmService";
import {
  Opportunity,
  OpportunityFilters,
  PaginatedResponse,
  OPPORTUNITY_STAGES,
} from "../types";
import OpportunityForm from "../components/OpportunityForm";
import OpportunityTable from "../components/OpportunityTable";
import Modal from "../components/Modal";
import CRMLayout from "../components/CRMLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../base/components/ui/card";
import { Button } from "../../../base/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../base/components/ui/dropdown-menu";

const OpportunityManagement: React.FC = () => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedStage, setSelectedStage] = useState("");

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] =
    useState<Opportunity | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const pageSize = 50;

  useEffect(() => {
    loadOpportunities();
  }, [currentPage, selectedStage]);

  const loadOpportunities = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: OpportunityFilters = {
        page: currentPage,
        limit: pageSize,
        stage: selectedStage || undefined,
      };

      const response: PaginatedResponse<Opportunity> =
        await crmService.getOpportunities(filters);
      setOpportunities(response.data);
      setTotal(response.total);
    } catch (err) {
      console.error("Error loading opportunities:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load opportunities"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOpportunity = async (opportunityData: any) => {
    try {
      await crmService.createOpportunity(opportunityData);
      setShowCreateModal(false);
      loadOpportunities();
    } catch (err) {
      console.error("Error creating opportunity:", err);
      throw err;
    }
  };

  const handleEditOpportunity = async (opportunityData: any) => {
    if (!selectedOpportunity?.id) return;

    try {
      await crmService.updateOpportunity(
        selectedOpportunity.id,
        opportunityData
      );
      setShowEditModal(false);
      setSelectedOpportunity(null);
      loadOpportunities();
    } catch (err) {
      console.error("Error updating opportunity:", err);
      throw err;
    }
  };

  const handleDeleteOpportunity = async () => {
    if (!selectedOpportunity?.id) return;

    try {
      await crmService.deleteOpportunity(selectedOpportunity.id);
      setShowDeleteModal(false);
      setSelectedOpportunity(null);
      loadOpportunities();
    } catch (err) {
      console.error("Error deleting opportunity:", err);
      setError(
        err instanceof Error ? err.message : "Failed to delete opportunity"
      );
    }
  };

  const openEditModal = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    setShowEditModal(true);
  };

  const openDeleteModal = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    setShowDeleteModal(true);
  };

  const totalPages = Math.ceil(total / pageSize);
  const totalValue = opportunities.reduce(
    (sum, opp) => sum + (opp.value || 0),
    0
  );
  const avgValue =
    opportunities.length > 0 ? totalValue / opportunities.length : 0;

  // Group opportunities by stage for stats
  const opportunitiesByStage = opportunities.reduce((acc, opp) => {
    const stage = opp.stage || "unassigned";
    if (!acc[stage]) {
      acc[stage] = { count: 0, value: 0 };
    }
    acc[stage].count++;
    acc[stage].value += opp.value || 0;
    return acc;
  }, {} as Record<string, { count: number; value: number }>);

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Sales Pipeline
              </h1>
              <p className="text-muted-foreground mt-1">
                Track and manage your sales opportunities
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={loadOpportunities}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <MoreHorizontal className="h-4 w-4" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem className="gap-2">
                    <Upload className="h-4 w-4" />
                    Import Opportunities
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Download className="h-4 w-4" />
                    Export Pipeline
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="gap-2">
                    <Filter className="h-4 w-4" />
                    Advanced Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Opportunity
              </Button>
            </div>
          </div>

          {/* Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-foreground mb-1">
                    Filter by Stage
                  </label>
                  <select
                    value={selectedStage}
                    onChange={(e) => {
                      setSelectedStage(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="w-full px-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-transparent bg-background"
                  >
                    <option value="">All Stages</option>
                    {OPPORTUNITY_STAGES.map((stage) => (
                      <option key={stage.value} value={stage.value}>
                        {stage.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Opportunities
                </CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Value
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${totalValue.toLocaleString()}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Average Value
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${Math.round(avgValue).toLocaleString()}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Closing This Month
                </CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {
                    opportunities.filter((opp) => {
                      if (!opp.close_date) return false;
                      const closeDate = new Date(opp.close_date);
                      const now = new Date();
                      return (
                        closeDate.getMonth() === now.getMonth() &&
                        closeDate.getFullYear() === now.getFullYear()
                      );
                    }).length
                  }
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </CRMLayout>
  );
};

export default OpportunityManagement;
