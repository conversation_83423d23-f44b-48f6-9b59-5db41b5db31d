import { supabase } from "../../../../base/common/apps/supabase";

// Interface for unified contact representation
export interface CrmContactForScoping {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  company: string;
  industry: string;
  source: "crm" | "scopingai";
  organisationId: string;
  organisationName?: string; // Add organization name
  isLinked?: boolean;
  createdAt: string;
  updatedAt: string;
}

// Interface for ScopingAI client from database
interface DbScopingAiClient {
  id: string;
  name: string;
  contact_person: string | null;
  email: string | null;
  phone: string | null;
  industry: string | null;
  company: string | null;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export class CrmContactService {
  /**
   * Get all available contacts for ALL user's organizations (CRM + ScopingAI)
   */
  async getOrganizationContacts(
    organisationId: string,
    userId: string
  ): Promise<CrmContactForScoping[]> {
    try {
      console.log(
        `🔍 Fetching contacts for user's organizations - User: ${userId}`
      );

      // Get ALL organizations user is member of
      const userOrganizationIds = await this.getUserOrganizationIds(userId);

      if (userOrganizationIds.length === 0) {
        throw new Error("User is not a member of any organization");
      }

      console.log(
        `👥 User is member of ${userOrganizationIds.length} organizations:`,
        userOrganizationIds
      );

      // Fetch organization names for display
      const { data: organizations, error: orgError } = await supabase
        .from("organisations")
        .select("id, name")
        .in("id", userOrganizationIds);

      if (orgError) {
        console.error("Error fetching organization names:", orgError);
      }

      // Create organization name lookup
      const orgNameMap = new Map<string, string>();
      organizations?.forEach((org: any) => {
        orgNameMap.set(org.id, org.name);
      });

      console.log(`📋 Organization name mapping:`, {
        organizationsFound: organizations?.length || 0,
        organizations: organizations,
        orgNameMap: Object.fromEntries(orgNameMap),
      });

      // Fetch CRM contacts for ALL user's organizations
      const { data: crmContacts, error: crmError } = await supabase
        .from("crm_contacts")
        .select(
          `
          id,
          full_name,
          email,
          phone,
          job_title,
          organisation_id,
          created_at,
          updated_at,
          crm_companies (
            name,
            employees
          )
        `
        )
        .in("organisation_id", userOrganizationIds); // Use ALL user's organizations

      if (crmError) {
        console.error("Error fetching CRM contacts:", crmError);
        throw crmError;
      }

      // Fetch ScopingAI clients for ALL organization members across all user's organizations
      const { data: orgMembers, error: membersError } = await supabase
        .from("organisation_members")
        .select("user_id")
        .in("organisation_id", userOrganizationIds); // Use ALL user's organizations

      if (membersError) {
        console.error("Error fetching organization members:", membersError);
        throw membersError;
      }

      const memberUserIds = orgMembers.map((m: any) => m.user_id);

      const { data: scopingAiClients, error: scopingError } = await supabase
        .from("scopingai_clients")
        .select("*")
        .in("user_id", memberUserIds);

      if (scopingError) {
        console.error("Error fetching ScopingAI clients:", scopingError);
        throw scopingError;
      }

      // All CRM contacts are now directly available (no linking required)

      // Transform CRM contacts (all are now directly available)
      const transformedCrmContacts: CrmContactForScoping[] = (
        crmContacts || []
      ).map((contact: any) => ({
        id: contact.id,
        name: contact.full_name,
        contactPerson: contact.full_name,
        email: contact.email || "",
        phone: contact.phone || "",
        company:
          Array.isArray(contact.crm_companies) &&
          contact.crm_companies.length > 0
            ? contact.crm_companies[0].name || ""
            : contact.crm_companies?.name || "",
        industry: contact.job_title || "",
        source: "crm" as const,
        organisationId: contact.organisation_id,
        organisationName:
          orgNameMap.get(contact.organisation_id) || "Unknown Organization",
        isLinked: true, // All CRM contacts are now directly available
        createdAt: contact.created_at,
        updatedAt: contact.updated_at,
      }));

      // Transform ScopingAI clients
      const transformedScopingAiClients: CrmContactForScoping[] = (
        scopingAiClients || []
      ).map((client: DbScopingAiClient) => ({
        id: client.id,
        name: client.name,
        contactPerson: client.contact_person || client.name,
        email: client.email || "",
        phone: client.phone || "",
        company: client.company || "",
        industry: client.industry || "",
        source: "scopingai" as const,
        organisationId: userOrganizationIds[0] || "", // Use first organization for consistency
        organisationName: orgNameMap.get(userOrganizationIds[0]) || "Personal",
        isLinked: true, // ScopingAI clients are always "linked"
        createdAt: client.created_at,
        updatedAt: client.updated_at,
      }));

      const allContacts = [
        ...transformedCrmContacts,
        ...transformedScopingAiClients,
      ];

      console.log(
        `✅ Found contacts across ${userOrganizationIds.length} organizations:`,
        {
          crmContacts: transformedCrmContacts.length,
          scopingAiClients: transformedScopingAiClients.length,
          totalContacts: allContacts.length,
          organizations: userOrganizationIds,
        }
      );

      return allContacts.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error("Error in getOrganizationContacts:", error);
      throw error;
    }
  }

  // Note: CRM contacts are now directly available in ScopingAI without linking

  /**
   * Convert CRM contact to dedicated ScopingAI client
   */
  async convertCrmContactToScopingClient(
    crmContactId: string,
    userId: string
  ): Promise<string> {
    try {
      console.log(
        `🔄 Converting CRM contact ${crmContactId} to ScopingAI client`
      );

      // Fetch CRM contact with company info
      const { data: crmContact, error: contactError } = await supabase
        .from("crm_contacts")
        .select(
          `
          id,
          full_name,
          email,
          phone,
          job_title,
          organisation_id,
          crm_companies (
            name,
            employees
          )
        `
        )
        .eq("id", crmContactId)
        .single();

      if (contactError || !crmContact) {
        throw new Error("CRM contact not found");
      }

      // Create ScopingAI client
      const { data: newClient, error: clientError } = await supabase
        .from("scopingai_clients")
        .insert({
          user_id: userId,
          name: crmContact.full_name,
          contact_person: crmContact.full_name,
          email: crmContact.email,
          phone: crmContact.phone,
          industry: crmContact.job_title,
          company: "", // Company info will be handled separately
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select("id")
        .single();

      if (clientError || !newClient) {
        console.error("Error creating ScopingAI client:", clientError);
        throw clientError;
      }

      console.log(
        `✅ Successfully converted CRM contact to ScopingAI client: ${newClient.id}`
      );
      return newClient.id;
    } catch (error) {
      console.error("Error in convertCrmContactToScopingClient:", error);
      throw error;
    }
  }

  /**
   * Get user's organization ID
   */
  async getUserOrganization(userId: string): Promise<string | null> {
    try {
      console.log(
        `🔍 CrmContactService.getUserOrganization - Looking up user: ${userId}`
      );

      // Get ALL organizations user is member of (no .single() or .limit())
      const { data: memberships, error } = await supabase
        .from("organisation_members")
        .select("organisation_id, role, created_at")
        .eq("user_id", userId);

      console.log(`📊 Database query result:`, {
        userId,
        memberships,
        count: memberships?.length || 0,
        error: error?.message,
      });

      if (error) {
        console.log(`❌ Database error:`, error);
        return null;
      }

      if (!memberships || memberships.length === 0) {
        console.log(`❌ No memberships found for user: ${userId}`);
        return null;
      }

      // Return first organization for backward compatibility
      const firstOrgId = memberships[0].organisation_id;

      console.log(`✅ Found organization memberships:`, {
        userId,
        firstOrgId,
        totalMemberships: memberships.length,
        allOrgs: memberships.map((m: any) => ({
          id: m.organisation_id,
          role: m.role,
        })),
      });

      return firstOrgId;
    } catch (error) {
      console.error("❌ Error getting user organization:", error);
      return null;
    }
  }

  /**
   * Get ALL organization IDs that user is a member of
   */
  async getUserOrganizationIds(userId: string): Promise<string[]> {
    try {
      console.log(
        `🔍 CrmContactService.getUserOrganizationIds - Looking up user: ${userId}`
      );

      const { data: memberships, error } = await supabase
        .from("organisation_members")
        .select("organisation_id, role")
        .eq("user_id", userId);

      if (error) {
        console.log(`❌ Database error:`, error);
        return [];
      }

      if (!memberships || memberships.length === 0) {
        console.log(`❌ No memberships found for user: ${userId}`);
        return [];
      }

      const organizationIds = memberships.map((m: any) => m.organisation_id);

      console.log(
        `✅ Found ${organizationIds.length} organization memberships:`,
        {
          userId,
          organizationIds,
          roles: memberships.map((m: any) => ({
            orgId: m.organisation_id,
            role: m.role,
          })),
        }
      );

      return organizationIds;
    } catch (error) {
      console.error("❌ Error getting user organization IDs:", error);
      return [];
    }
  }
}

export const crmContactService = new CrmContactService();
