import { BaseExternalService, ExternalServiceResult, ExternalServiceConfig, SEOMetrics, SEOIssue } from './BaseExternalService';

export class GoogleLighthouseService extends BaseExternalService {
  private readonly API_URL = 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed';

  constructor(config: ExternalServiceConfig) {
    super('Google Lighthouse', 'free', config);
  }

  isConfigured(): boolean {
    return !!this.config.apiKey;
  }

  async testConnection(): Promise<boolean> {
    try {
      const testUrl = 'https://example.com';
      const params = new URLSearchParams({
        url: testUrl,
        key: this.config.apiKey!,
        category: 'seo'
      });

      const response = await fetch(`${this.API_URL}?${params}`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async analyze(url: string): Promise<ExternalServiceResult> {
    if (!this.isEnabled()) {
      return this.createErrorResult('Google Lighthouse service is not enabled or configured');
    }

    try {
      await this.handleRateLimit();

      const result = await this.withRetry(async () => {
        const params = new URLSearchParams({
          url: url,
          key: this.config.apiKey!,
          strategy: 'desktop'
        });

        // Add categories
        params.append('category', 'performance');
        params.append('category', 'accessibility');
        params.append('category', 'best-practices');
        params.append('category', 'seo');
        params.append('category', 'pwa');

        const response = await fetch(`${this.API_URL}?${params}`, {
          signal: AbortSignal.timeout(this.config.timeout || 30000)
        });

        if (!response.ok) {
          throw new Error(`Lighthouse API failed: ${response.statusText}`);
        }

        return await response.json();
      });

      return this.parseResponse(result);

    } catch (error) {
      return this.createErrorResult(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  protected getFreeTierLimit(): number {
    return 25000; // 25,000 requests per day
  }

  protected getPremiumCost(): number {
    return 0; // Free service
  }

  private parseResponse(data: any): ExternalServiceResult {
    const categories = data.lighthouseResult?.categories || {};
    const audits = data.lighthouseResult?.audits || {};

    const metrics: SEOMetrics = {
      overall: Math.round((categories.seo?.score || 0) * 100),
      technical: Math.round((categories['best-practices']?.score || 0) * 100),
      content: Math.round((categories.seo?.score || 0) * 100),
      performance: Math.round((categories.performance?.score || 0) * 100),
      accessibility: Math.round((categories.accessibility?.score || 0) * 100),
      seo: Math.round((categories.seo?.score || 0) * 100),
      details: data.lighthouseResult
    };

    const issues: SEOIssue[] = this.extractIssues(audits);

    return {
      provider: this.name,
      metrics,
      issues,
      rawData: data,
      timestamp: new Date().toISOString(),
      success: true
    };
  }

  private extractIssues(audits: any): SEOIssue[] {
    const issues: SEOIssue[] = [];

    const seoAudits = [
      { id: 'document-title', category: 'Meta Tags' },
      { id: 'meta-description', category: 'Meta Tags' },
      { id: 'viewport', category: 'Mobile' },
      { id: 'image-alt', category: 'Images' },
      { id: 'link-text', category: 'Links' },
      { id: 'hreflang', category: 'Internationalization' },
      { id: 'canonical', category: 'Technical SEO' },
      { id: 'robots-txt', category: 'Technical SEO' },
      { id: 'structured-data', category: 'Technical SEO' }
    ];

    seoAudits.forEach(({ id, category }) => {
      const audit = audits[id];
      if (audit && audit.score !== null && audit.score < 1) {
        issues.push({
          category,
          severity: audit.score === 0 ? 'critical' : 'warning',
          title: audit.title || id,
          description: audit.description || 'No description available',
          recommendation: this.getRecommendation(id, audit),
          impact: audit.score === 0 ? 'high' : 'medium'
        });
      }
    });

    return issues;
  }

  private getRecommendation(auditId: string, audit: any): string {
    const recommendations: { [key: string]: string } = {
      'document-title': 'Add a descriptive title tag (50-60 characters)',
      'meta-description': 'Add a meta description (150-160 characters)',
      'viewport': 'Add <meta name="viewport" content="width=device-width, initial-scale=1">',
      'image-alt': 'Add descriptive alt text to all images',
      'link-text': 'Use descriptive link text instead of "click here" or "read more"',
      'hreflang': 'Add hreflang tags for international content',
      'canonical': 'Add canonical URLs to prevent duplicate content',
      'robots-txt': 'Create or fix robots.txt file',
      'structured-data': 'Add structured data markup for better search results'
    };

    return recommendations[auditId] || audit.description || 'Fix this SEO issue';
  }
} 