import { DocumentTheme } from "../types/documentTypes";

// Define theme presets with proper TypeScript types
export const themePresets: Record<string, DocumentTheme> = {
  professional: {
    headingColor: "#2563EB",
    subheadingColor: "#3B82F6",
    textColor: "#334155",
    backgroundColor: "#F8FAFC",
    accentColor: "#BFDBFE",
    fontFamily: "Inter (Sans-serif)",
    headingSize: "medium",
    textSize: "medium",
    header: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    footer: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    logo: undefined,
    brandName: "Altzero base",
    brandTagline: "Digital Transformation Solutions",
    showCoverPage: true,
  },
  modern: {
    headingColor: "#0D9488",
    subheadingColor: "#14B8A6",
    textColor: "#334155",
    backgroundColor: "#F8FAFC",
    accentColor: "#CCFBF1",
    fontFamily: "Inter (Sans-serif)",
    headingSize: "medium",
    textSize: "medium",
    header: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    footer: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    logo: undefined,
    brandName: "Altzero base",
    brandTagline: "Innovation Through Technology",
    showCoverPage: true,
  },
  creative: {
    headingColor: "#4F46E5",
    subheadingColor: "#6366F1",
    textColor: "#334155",
    backgroundColor: "#F8FAFC",
    accentColor: "#C7D2FE",
    fontFamily: "Playfair Display (Display)",
    headingSize: "large",
    textSize: "medium",
    header: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    footer: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    logo: undefined,
    brandName: "Altzero base",
    brandTagline: "Where Vision Meets Reality",
    showCoverPage: true,
  },
  formal: {
    headingColor: "#1F2937",
    subheadingColor: "#374151",
    textColor: "#4B5563",
    backgroundColor: "#FFFFFF",
    accentColor: "#E5E7EB",
    fontFamily: "Merriweather (Serif)",
    headingSize: "medium",
    textSize: "medium",
    header: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    footer: {
      enabled: true,
      type: "simple",
      includePageNumbers: true,
    },
    logo: undefined,
    brandName: "Altzero base",
    brandTagline: "Excellence in Professional Services",
    showCoverPage: true,
  },
};
