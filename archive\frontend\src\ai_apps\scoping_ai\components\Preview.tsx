import React, { useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { usePDF, Options } from 'react-to-pdf';
import {
  Download,
  Loader2,
  Type,
  Image as ImageIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  Grip,
} from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import type { ProposalData } from '../../../types';
import { generateContent } from '../../../lib/openai';

interface SectionContent {
  [key: string]: {
    content: string;
    style: {
      textAlign: 'left' | 'center' | 'right';
      padding: string;
      fontSize: string;
    };
    images: Array<{ url: string; alt: string }>;
  };
}

interface SortableSectionProps {
  id: string;
  title: string;
  content: SectionContent[string] | undefined;
  isLoading: boolean;
  onGenerateContent: () => void;
  onContentChange: (content: string) => void;
  onStyleChange: (style: Partial<SectionContent[string]['style']> | { images: Array<{ url: string; alt: string }> }) => void;
  onAddImage: (imageUrl: string) => void;
  onRemove: () => void;
  selectedText: SelectedTextState | null;
  onTextSelect: (text: SelectedTextState | null) => void;
}

interface ComponentItem {
  id: string;
  type: 'text' | 'image' | 'quote' | 'section';
  label: string;
  icon: React.ReactNode;
}

interface SelectedTextState {
  text: string;
  position: { x: number; y: number } | null;
}

function SortableSection({
  id,
  title,
  content,
  isLoading,
  onGenerateContent,
  onContentChange,
  onStyleChange,
  onAddImage,
  onRemove,
  selectedText,
  onTextSelect,
}: SortableSectionProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
    transition,
  };

  const [imageUrl, setImageUrl] = useState('');

  const handleImageSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (imageUrl) {
      onAddImage(imageUrl);
      setImageUrl('');
    }
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().length > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      onTextSelect({
        text: selection.toString(),
        position: { x: rect.right, y: rect.top },
      });
    } else {
      onTextSelect(null);
    }
  };

  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      className="bg-white rounded-lg shadow-md mb-4 group"
      onMouseUp={handleTextSelection}
    >
      <div className="p-4 border-b border-gray-200 flex items-center justify-between bg-gray-50 rounded-t-lg">
        <div className="flex items-center gap-3 flex-1">
          <button
            className="cursor-grab hover:text-indigo-600 p-2"
            {...attributes}
            {...listeners}
          >
            <Grip className="w-5 h-5" />
          </button>
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
        </div>
        <button
          onClick={onRemove}
          className="p-2 text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
          title="Remove section"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>

      <div className="p-4">
        {isLoading ? (
          <div className="flex items-center gap-2 text-indigo-600">
            <Loader2 className="w-5 h-5 animate-spin" />
            Generating content...
          </div>
        ) : content ? (
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2 pb-4 border-b border-gray-200">
              <button
                onClick={() => onStyleChange({ textAlign: 'left' })}
                className="p-2 hover:bg-gray-100 rounded"
                title="Align Left"
              >
                <AlignLeft className="w-5 h-5" />
              </button>
              <button
                onClick={() => onStyleChange({ textAlign: 'center' })}
                className="p-2 hover:bg-gray-100 rounded"
                title="Align Center"
              >
                <AlignCenter className="w-5 h-5" />
              </button>
              <button
                onClick={() => onStyleChange({ textAlign: 'right' })}
                className="p-2 hover:bg-gray-100 rounded"
                title="Align Right"
              >
                <AlignRight className="w-5 h-5" />
              </button>
              <button
                onClick={() => document.execCommand('bold', false)}
                className="p-2 hover:bg-gray-100 rounded"
                title="Bold"
              >
                <Bold className="w-5 h-5" />
              </button>
              <button
                onClick={() => document.execCommand('italic', false)}
                className="p-2 hover:bg-gray-100 rounded"
                title="Italic"
              >
                <Italic className="w-5 h-5" />
              </button>
              <button
                onClick={() => document.execCommand('underline', false)}
                className="p-2 hover:bg-gray-100 rounded"
                title="Underline"
              >
                <Underline className="w-5 h-5" />
              </button>
              <select
                onChange={(e) => onStyleChange({ fontSize: e.target.value })}
                className="px-3 py-2 border rounded"
              >
                <option value="16px">Normal</option>
                <option value="20px">Large</option>
                <option value="24px">Extra Large</option>
              </select>
              <select
                onChange={(e) => onStyleChange({ padding: e.target.value })}
                className="px-3 py-2 border rounded"
              >
                <option value="1rem">Normal Spacing</option>
                <option value="2rem">Large Spacing</option>
                <option value="3rem">Extra Spacing</option>
              </select>
            </div>

            <div
              className="prose max-w-none"
              contentEditable
              onBlur={(e) => onContentChange(e.currentTarget.innerHTML)}
              dangerouslySetInnerHTML={{ __html: content.content }}
              style={{
                ...content.style,
                minHeight: '100px',
              }}
            />

            <div className="space-y-4">
              {content.images.map((img, index) => (
                <div key={index} className="relative group">
                  <img
                    src={img.url}
                    alt={img.alt}
                    className="max-w-full h-auto rounded-lg"
                  />
                  <button
                    onClick={() => {
                      const newImages = content.images.filter((_, i) => i !== index);
                      onStyleChange({ images: newImages });
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>

            <form onSubmit={handleImageSubmit} className="flex gap-2">
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="Enter image URL"
                className="flex-1 px-3 py-2 border rounded"
              />
              <button
                type="submit"
                className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
              >
                Add Image
              </button>
            </form>
          </div>
        ) : (
          <button
            onClick={onGenerateContent}
            className="px-4 py-2 text-indigo-600 border border-indigo-600 rounded-md hover:bg-indigo-50"
          >
            Generate content for this section
          </button>
        )}
      </div>
    </div>
  );
}

export default function Preview() {
  const location = useLocation();
  const { company, documentType, client, objective, sections: initialSections, selectedTemplate } = location.state as ProposalData;
  const contentRef = useRef<HTMLDivElement>(null);
  const { toPDF } = usePDF();
  const [sections, setSections] = useState(initialSections);
  const [sectionContents, setSectionContents] = useState<SectionContent>({});
  const [loadingSection, setLoadingSection] = useState<string | null>(null);
  const [selectedText, setSelectedText] = useState<SelectedTextState | null>(null);
  const [showAddSection, setShowAddSection] = useState(false);
  const [newSectionTitle, setNewSectionTitle] = useState('');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setSections((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const getTemplateStyles = () => {
    switch (selectedTemplate?.style) {
      case 'colorful':
        return 'bg-gradient-to-r from-blue-500 to-purple-500 text-white';
      case 'modern':
        return 'bg-gray-900 text-white';
      case 'heritage':
        return 'bg-amber-100 text-gray-900 border-2 border-amber-900';
      default:
        return 'bg-white text-gray-900';
    }
  };

  const handleGenerateContent = async (sectionId: string, sectionTitle: string) => {
    setLoadingSection(sectionId);
    try {
      const content = await generateContent(
        company,
        documentType,
        client,
        objective,
        sectionTitle
      );
      setSectionContents(prev => ({
        ...prev,
        [sectionId]: {
          content,
          style: {
            textAlign: 'left',
            padding: '1rem',
            fontSize: '16px',
          },
          images: [],
        },
      }));
    } finally {
      setLoadingSection(null);
    }
  };

  const handleContentChange = (sectionId: string, content: string) => {
    setSectionContents(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        content,
      },
    }));
  };

  const handleStyleChange = (sectionId: string, style: Partial<SectionContent[string]['style']> | { images: Array<{ url: string; alt: string }> }) => {
    setSectionContents(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId] || {
          content: '',
          style: {
            textAlign: 'left',
            padding: '1rem',
            fontSize: '16px',
          },
          images: [],
        },
        style: {
          ...(prev[sectionId]?.style || {}),
          ...style,
        },
      },
    }));
  };

  const handleAddImage = (sectionId: string, imageUrl: string) => {
    setSectionContents(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        images: [...prev[sectionId].images, { url: imageUrl, alt: 'Added image' }],
      },
    }));
  };

  const handleAddSection = () => {
    if (newSectionTitle.trim()) {
      const newSection = {
        id: `section-${Date.now()}`,
        title: newSectionTitle.trim()
      };
      setSections([...sections, newSection]);
      setNewSectionTitle('');
      setShowAddSection(false);
    }
  };

  const handleRemoveSection = (sectionId: string) => {
    setSections(sections.filter(section => section.id !== sectionId));
    const { [sectionId]: _, ...remainingContents } = sectionContents;
    setSectionContents(remainingContents);
  };

  const availableComponents: ComponentItem[] = [
    { id: 'text', type: 'text', label: 'Text Block', icon: <Type className="w-5 h-5" /> },
    { id: 'image', type: 'image', label: 'Image', icon: <ImageIcon className="w-5 h-5" /> },
    { id: 'quote', type: 'quote', label: 'Quote', icon: <AlignLeft className="w-5 h-5" /> },
    { id: 'section', type: 'section', label: 'New Section', icon: <Type className="w-5 h-5" /> },
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="py-8 px-16">
        <div className="">
          <div className="flex justify-between items-center mb-8">
            <div className="mb-12">
                <h1 className="text-4xl font-bold mb-4">{documentType} for {client}</h1>
                <p className="text-lg opacity-80">Prepared by {company}</p>
                <p className="text-lg opacity-80">Objective: {objective}</p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowAddSection(true)}
                className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Section
              </button>
              <button
                onClick={() => {
                  if (contentRef.current) {
                    toPDF({ element: contentRef.current, filename: 'document.pdf' });
                  }
                }}
                className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                <Download className="w-5 h-5" />
                Download PDF
              </button>
            </div>
          </div>

          {showAddSection && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-96">
                <h3 className="text-lg font-semibold mb-4">Add New Section</h3>
                <input
                  type="text"
                  value={newSectionTitle}
                  onChange={(e) => setNewSectionTitle(e.target.value)}
                  placeholder="Enter section title"
                  className="w-full px-3 py-2 border rounded-md mb-4"
                  autoFocus
                />
                <div className="flex justify-end gap-3">
                  <button
                    onClick={() => {
                      setShowAddSection(false);
                      setNewSectionTitle('');
                    }}
                    className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddSection}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                    disabled={!newSectionTitle.trim()}
                  >
                    Add Section
                  </button>
                </div>
              </div>
            </div>
          )}

          <div
            ref={contentRef}
            className={`overflow-hidden `}
          >
            <div>

              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={sections.map(s => s.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-6">
                    {sections.map((section) => (
                      <SortableSection
                        key={section.id}
                        id={section.id}
                        title={section.title}
                        content={sectionContents[section.id]}
                        isLoading={loadingSection === section.id}
                        onGenerateContent={() => handleGenerateContent(section.id, section.title)}
                        onContentChange={(content) => handleContentChange(section.id, content)}
                        onStyleChange={(style) => handleStyleChange(section.id, style)}
                        onAddImage={(imageUrl) => handleAddImage(section.id, imageUrl)}
                        onRemove={() => handleRemoveSection(section.id)}
                        selectedText={selectedText}
                        onTextSelect={setSelectedText}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            </div>
          </div>
        </div>
      </div>

      {selectedText && (
        <div 
          className="fixed shadow-lg p-4 w-64 rounded-lg"
          style={{
            top: selectedText.position?.y || 0,
            left: selectedText.position?.x || 0,
          }}
        >
          <h3 className="text-lg font-semibold mb-4">Text Styles</h3>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => document.execCommand('bold', false)}
                className="p-2 hover:bg-gray-100 rounded"
              >
                <Bold className="w-5 h-5" />
              </button>
            </div>
            <div className="space-y-2">
              <select className="w-full p-2 border rounded">
                <option>Normal Text</option>
                <option>Heading 1</option>
                <option>Heading 2</option>
              </select>
              <input 
                type="color" 
                className="w-full"
                onChange={(e) => document.execCommand('foreColor', false, e.target.value)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}