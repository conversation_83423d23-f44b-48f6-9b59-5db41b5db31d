import { environment } from '../../config/environment';
import OpenAI from 'openai';
import { encode } from 'gpt-tokenizer';
import { logLLMRequest, logLLMResponse, logError } from '../../utils/logger';

export class OpenRouterClient {
  private client: OpenAI;

  constructor() {
    console.log("Creating OpenRouter client with API key:", process.env.OPENROUTER_API_KEY ? "Key exists" : "No key found");
    this.client = new OpenAI({
      apiKey: environment.openRouterApiKey || process.env.OPENROUTER_API_KEY || '',
      baseURL: environment.openRouterBaseUrl || 'https://openrouter.ai/api/v1'
    });
    console.log(`OpenRouter configured to use model: ${environment.geminiModel}`);
  }

  // Standard chat method - renamed to avoid conflict with getter
  async directChat(messages: any[], stream = false) {
    try {
      // Use the Gemini model specifically from environment
      const model = environment.geminiModel;
      console.log(`OpenRouter using model: ${model}`);
      
      const completion = await this.client.chat.completions.create({
        model: model,
        messages,
        max_tokens: environment.maxTokens,
        temperature: 0.7,
        stream
      });

      if (stream) {
        return completion;
      }

      return (completion as any).choices[0].message.content;
    } catch (error) {
      console.error('Error in OpenRouter chat:', error);
      logError('OpenRouterClient:chat', error);
      throw error;
    }
  }

  // This is the method directly called by ScopingStreamService
  async createChatCompletion(options: any) {
    try {
      // Force the model to be Gemini regardless of what was passed
      options.model = environment.geminiModel;
      
      console.log(`OpenRouter creating completion with model: ${options.model}`);
      logLLMRequest(options.model, 'OpenRouterClient', JSON.stringify(options.messages || []));
      
      const startTime = Date.now();
      const result = await this.client.chat.completions.create(options);
      const timeTaken = Date.now() - startTime;
      
      // Log the response
      const responseText = result.choices[0]?.message?.content || '';
      logLLMResponse('OpenRouterClient', responseText, timeTaken);
      
      return result;
    } catch (error) {
      logError('OpenRouterClient:completions', error);
      throw error;
    }
  }

  async getTokenCount(text: string): Promise<number> {
    return encode(text).length;
  }

  // Add getter for completions to make it compatible with OpenAI client structure
  get chat() {
    return {
      completions: {
        create: this.createChatCompletion.bind(this)
      }
    };
  }
} 
