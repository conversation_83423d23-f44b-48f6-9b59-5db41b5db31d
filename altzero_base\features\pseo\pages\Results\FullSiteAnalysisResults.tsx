import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import { fullSiteAnalysisService } from '../../services/pseo/fullSiteAnalysisService';
import { useUser } from '../../../../base/contextapi/UserContext';
import type { PSEOWebsite } from '../../types';
import PSEOLayout from '../../components/PSEOLayout';

interface AnalysisResults {
  overview: {
    website: PSEOWebsite;
    job: any;
    summary: {
      pages_discovered: number;
      keywords_found: number;
      content_opportunities: number;
      backlinks_found: number;
      execution_time_ms: number;
    };
  };
  pages: any[];
  keywords: any[];
  contentOpportunities: any[];
  generatedContent: any[];
  backlinks: any[];
}

const FullSiteAnalysisResults: React.FC = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const { user } = useUser();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (jobId && user?.id) {
      loadAnalysisResults();
    }
  }, [jobId, user?.id]);

  const loadAnalysisResults = async () => {
    if (!jobId) return;

    try {
      setLoading(true);
      setError(null);

      // Get job details
      const job = await fullSiteAnalysisService.getJobStatus(jobId);
      if (!job) {
        throw new Error('Analysis job not found');
      }

      // Get website details
      const website = await databaseService.getWebsiteById(job.websiteId);
      if (!website) {
        throw new Error('Website not found');
      }

      // Load all analysis results including generated content
      const [pages, keywords, contentOpportunities, generatedContent, backlinks] = await Promise.all([
        databaseService.getWebsitePages(job.websiteId),
        databaseService.getKeywordResearch(job.websiteId),
        databaseService.getContentOpportunities(job.websiteId),
        databaseService.getGeneratedContentItems(job.websiteId),
        databaseService.getBacklinks?.(job.websiteId) || []
      ]);

      const analysisResults: AnalysisResults = {
        overview: {
          website,
          job,
          summary: {
            pages_discovered: pages.length,
            keywords_found: keywords.length,
            content_opportunities: contentOpportunities.length,
            backlinks_found: backlinks.length,
            execution_time_ms: job.results?.execution_time_ms || 0
          }
        },
        pages,
        keywords,
        contentOpportunities,
        generatedContent,
        backlinks
      };

      setResults(analysisResults);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analysis results';
      setError(errorMessage);
      console.error('Failed to load analysis results:', err);
    } finally {
      setLoading(false);
    }
  };

  const exportData = (type: string) => {
    if (!results) return;

    let data: any[] = [];
    let filename = '';

    switch (type) {
      case 'pages':
        data = results.pages;
        filename = `pages-analysis-${results.overview.website.domain}`;
        break;
      case 'keywords':
        data = results.keywords;
        filename = `keywords-analysis-${results.overview.website.domain}`;
        break;
      case 'content':
        data = results.contentOpportunities;
        filename = `content-opportunities-${results.overview.website.domain}`;
        break;
      case 'backlinks':
        data = results.backlinks;
        filename = `backlinks-analysis-${results.overview.website.domain}`;
        break;
    }

    if (data.length === 0) return;

    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatExecutionTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading analysis results...</p>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (error) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-12">
              <div className="text-4xl mb-4">❌</div>
              <h2 className="text-2xl font-bold text-foreground mb-2">Error Loading Results</h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={() => window.history.back()}
                className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (!results) return null;

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'pages', name: 'Page Discovery', icon: '🔍', count: results.pages.length },
    { id: 'keywords', name: 'Keywords', icon: '🎯', count: results.keywords.length },
    { id: 'content', name: 'Content', icon: '📝', count: results.contentOpportunities.length },
    { id: 'backlinks', name: 'Backlinks', icon: '🔗', count: results.backlinks.length }
  ];

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-2">
            <button
              onClick={() => window.history.back()}
              className="text-muted-foreground hover:text-foreground"
            >
              ← Back
            </button>
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Full Site Analysis Results
          </h1>
          <p className="text-muted-foreground">
            {results.overview.website.name} ({results.overview.website.domain})
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex flex-wrap border-b border-border mb-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-3 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
              {tab.count !== undefined && (
                <span className="bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="bg-card rounded-lg border p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-4">Analysis Summary</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                  <div className="bg-muted rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">🔍</span>
                      <h3 className="font-medium">Pages Discovered</h3>
                    </div>
                    <p className="text-2xl font-bold text-primary">{results.overview.summary.pages_discovered}</p>
                  </div>
                  <div className="bg-muted rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">🎯</span>
                      <h3 className="font-medium">Keywords Found</h3>
                    </div>
                    <p className="text-2xl font-bold text-primary">{results.overview.summary.keywords_found}</p>
                  </div>
                  <div className="bg-muted rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">📝</span>
                      <h3 className="font-medium">Content Opportunities</h3>
                    </div>
                    <p className="text-2xl font-bold text-primary">{results.overview.summary.content_opportunities}</p>
                  </div>
                  <div className="bg-muted rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">🤖</span>
                      <h3 className="font-medium">Generated Content</h3>
                    </div>
                    <p className="text-2xl font-bold text-green-600">{results.generatedContent.length}</p>
                  </div>
                  <div className="bg-muted rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">🔗</span>
                      <h3 className="font-medium">Backlinks Found</h3>
                    </div>
                    <p className="text-2xl font-bold text-primary">{results.overview.summary.backlinks_found}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Analysis Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p><span className="font-medium">Status:</span> <span className="text-green-600">{results.overview.job.status}</span></p>
                    <p><span className="font-medium">Started:</span> {formatDate(results.overview.job.startedAt)}</p>
                    {results.overview.job.completedAt && (
                      <p><span className="font-medium">Completed:</span> {formatDate(results.overview.job.completedAt)}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <p><span className="font-medium">Execution Time:</span> {formatExecutionTime(results.overview.summary.execution_time_ms)}</p>
                    <p><span className="font-medium">Job ID:</span> <code className="text-xs bg-muted px-2 py-1 rounded">{results.overview.job.id}</code></p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'pages' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Discovered Pages</h2>
                <button
                  onClick={() => exportData('pages')}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm"
                >
                  Export CSV
                </button>
              </div>
              {results.pages.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-border">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border border-border p-3 text-left">URL</th>
                        <th className="border border-border p-3 text-left">Title</th>
                        <th className="border border-border p-3 text-left">Page Type</th>
                        <th className="border border-border p-3 text-left">Status</th>
                        <th className="border border-border p-3 text-left">Word Count</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.pages.map((page, index) => (
                        <tr key={index} className="hover:bg-muted/50">
                          <td className="border border-border p-3">
                            <a href={page.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                              {page.url}
                            </a>
                          </td>
                          <td className="border border-border p-3">{page.title || '-'}</td>
                          <td className="border border-border p-3">{page.page_type || '-'}</td>
                          <td className="border border-border p-3">
                            <span className={`px-2 py-1 rounded text-xs ${
                              page.status === 'discovered' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {page.status}
                            </span>
                          </td>
                          <td className="border border-border p-3">{page.word_count || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No pages discovered yet.
                </div>
              )}
            </div>
          )}

          {activeTab === 'keywords' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Keyword Research</h2>
                <button
                  onClick={() => exportData('keywords')}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm"
                >
                  Export CSV
                </button>
              </div>
              {results.keywords.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-border">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border border-border p-3 text-left">Keyword</th>
                        <th className="border border-border p-3 text-left">Search Volume</th>
                        <th className="border border-border p-3 text-left">Difficulty</th>
                        <th className="border border-border p-3 text-left">CPC</th>
                        <th className="border border-border p-3 text-left">Competition</th>
                        <th className="border border-border p-3 text-left">Intent</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.keywords.map((keyword, index) => (
                        <tr key={index} className="hover:bg-muted/50">
                          <td className="border border-border p-3 font-medium">{keyword.keyword}</td>
                          <td className="border border-border p-3">{keyword.search_volume?.toLocaleString() || '-'}</td>
                          <td className="border border-border p-3">
                            <span className={`px-2 py-1 rounded text-xs ${
                              keyword.keyword_difficulty >= 70 ? 'bg-red-100 text-red-700' :
                              keyword.keyword_difficulty >= 40 ? 'bg-yellow-100 text-yellow-700' :
                              'bg-green-100 text-green-700'
                            }`}>
                              {keyword.keyword_difficulty || '-'}
                            </span>
                          </td>
                          <td className="border border-border p-3">${keyword.cpc || '-'}</td>
                          <td className="border border-border p-3">{keyword.competition || '-'}</td>
                          <td className="border border-border p-3">{keyword.intent || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No keywords found yet.
                </div>
              )}
            </div>
          )}

          {activeTab === 'content' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Content Opportunities</h2>
                <div className="flex gap-2">
                  <button
                    onClick={() => navigate(`/content-generation-results/${results.overview.website.id}`)}
                    className="bg-green-600 text-white hover:bg-green-700 px-4 py-2 rounded-md text-sm"
                  >
                    📝 View All Generated Content
                  </button>
                  <button
                    onClick={() => exportData('content')}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm"
                  >
                    Export CSV
                  </button>
                </div>
              </div>
              {results.contentOpportunities.length > 0 ? (
                <div className="space-y-4">
                  {results.contentOpportunities.map((content, index) => {
                    // Check if content has been generated for this opportunity
                    const hasGeneratedContent = results.generatedContent.some(generated => 
                      generated.opportunity_id === content.id || 
                      generated.title === content.title ||
                      (generated.target_keyword && generated.target_keyword === content.target_keyword)
                    );
                    
                    const generatedItem = results.generatedContent.find(generated => 
                      generated.opportunity_id === content.id || 
                      generated.title === content.title ||
                      (generated.target_keyword && generated.target_keyword === content.target_keyword)
                    );

                    return (
                      <div key={index} className="border border-border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-lg font-medium">{content.title}</h3>
                          <div className="flex gap-2">
                            <span className={`px-2 py-1 rounded text-xs ${
                              content.priority === 'high' ? 'bg-red-100 text-red-700' :
                              content.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                              'bg-green-100 text-green-700'
                            }`}>
                              {content.priority} priority
                            </span>
                            {hasGeneratedContent && (
                              <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-700">
                                ✅ Content Generated
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-4">
                          <div>
                            <span className="font-medium">Target Keyword:</span> {content.target_keyword}
                          </div>
                          <div>
                            <span className="font-medium">Content Type:</span> {content.content_type}
                          </div>
                          <div>
                            <span className="font-medium">Estimated Traffic:</span> {content.estimated_traffic || 0}
                          </div>
                        </div>
                        
                        {content.target_word_count && (
                          <div className="mb-4 text-sm">
                            <span className="font-medium">Target Word Count:</span> {content.target_word_count}
                          </div>
                        )}

                        {content.content_brief && (
                          <div className="mb-4 text-sm">
                            <span className="font-medium">Brief:</span>
                            <p className="text-muted-foreground mt-1">{content.content_brief}</p>
                          </div>
                        )}
                        
                        <div className="flex gap-2 pt-3 border-t border-border">
                          {hasGeneratedContent && generatedItem ? (
                            <div className="flex gap-2">
                              <button
                                onClick={() => {
                                  navigator.clipboard.writeText(generatedItem.content_markdown || generatedItem.generated_content);
                                }}
                                className="text-sm bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                              >
                                📋 Copy Content
                              </button>
                              <button
                                onClick={() => {
                                  const content = generatedItem.content_markdown || generatedItem.generated_content;
                                  const blob = new Blob([content], { type: 'text/markdown' });
                                  const url = URL.createObjectURL(blob);
                                  const a = document.createElement('a');
                                  a.href = url;
                                  a.download = `${generatedItem.title.replace(/[^a-z0-9]/gi, '-')}.md`;
                                  a.click();
                                  URL.revokeObjectURL(url);
                                }}
                                className="text-sm bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                              >
                                📄 Download Markdown
                              </button>
                              <span className="text-sm text-muted-foreground py-2">
                                {generatedItem.word_count || 0} words generated
                              </span>
                            </div>
                          ) : (
                            <button
                              onClick={() => navigate(`/create-blog-post?keyword=${encodeURIComponent(content.target_keyword)}`)}
                              className="text-sm bg-primary text-primary-foreground px-4 py-2 rounded hover:bg-primary/90"
                            >
                              🤖 Generate Content
                            </button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No content opportunities found yet.
                </div>
              )}
            </div>
          )}

          {activeTab === 'backlinks' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Backlink Analysis</h2>
                <button
                  onClick={() => exportData('backlinks')}
                  className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm"
                >
                  Export CSV
                </button>
              </div>
              {results.backlinks.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-border">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border border-border p-3 text-left">Source Domain</th>
                        <th className="border border-border p-3 text-left">Anchor Text</th>
                        <th className="border border-border p-3 text-left">Target URL</th>
                        <th className="border border-border p-3 text-left">DA</th>
                        <th className="border border-border p-3 text-left">Link Type</th>
                        <th className="border border-border p-3 text-left">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.backlinks.map((link, index) => (
                        <tr key={index} className="hover:bg-muted/50">
                          <td className="border border-border p-3">{link.source_domain}</td>
                          <td className="border border-border p-3">{link.anchor_text}</td>
                          <td className="border border-border p-3">
                            <a href={link.target_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline text-sm">
                              {link.target_url}
                            </a>
                          </td>
                          <td className="border border-border p-3">
                            <span className={`px-2 py-1 rounded text-xs ${
                              link.domain_authority >= 50 ? 'bg-green-100 text-green-700' :
                              link.domain_authority >= 30 ? 'bg-yellow-100 text-yellow-700' :
                              'bg-red-100 text-red-700'
                            }`}>
                              {link.domain_authority || '-'}
                            </span>
                          </td>
                          <td className="border border-border p-3">{link.link_type}</td>
                          <td className="border border-border p-3">
                            <span className={`px-2 py-1 rounded text-xs ${
                              link.status === 'active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {link.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No backlinks found yet.
                </div>
              )}
            </div>
          )}
        </div>
        </div>
      </div>
    </PSEOLayout>
  );
};

export default FullSiteAnalysisResults; 