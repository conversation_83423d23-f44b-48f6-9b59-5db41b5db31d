-- Create prompt_templates table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.prompt_templates (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL, 
    variables JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create section_definitions table
CREATE TABLE IF NOT EXISTS public.section_definitions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    default_order INTEGER,
    is_default BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create scope_templates table
CREATE TABLE IF NOT EXISTS public.scope_templates (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create clients table
CREATE TABLE IF NOT EXISTS public.clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users NOT NULL,
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    industry TEXT,
    company TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security for all tables
ALTER TABLE prompt_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE scope_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;

-- RLS policies for prompt_templates
CREATE POLICY "Users can view their own prompt templates" 
    ON prompt_templates FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own prompt templates" 
    ON prompt_templates FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own prompt templates" 
    ON prompt_templates FOR UPDATE 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own prompt templates" 
    ON prompt_templates FOR DELETE 
    USING (auth.uid() = user_id);

-- RLS policies for scope_templates
CREATE POLICY "Users can view their own scope templates" 
    ON scope_templates FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own scope templates" 
    ON scope_templates FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scope templates" 
    ON scope_templates FOR UPDATE 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own scope templates" 
    ON scope_templates FOR DELETE 
    USING (auth.uid() = user_id);

-- RLS policies for clients
CREATE POLICY "Users can view their own clients" 
    ON clients FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own clients" 
    ON clients FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own clients" 
    ON clients FOR UPDATE 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own clients" 
    ON clients FOR DELETE 
    USING (auth.uid() = user_id);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS prompt_templates_user_id_idx ON prompt_templates(user_id);
CREATE INDEX IF NOT EXISTS scope_templates_user_id_idx ON scope_templates(user_id);
CREATE INDEX IF NOT EXISTS clients_user_id_idx ON clients(user_id);

-- Add functions to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at columns
CREATE TRIGGER set_timestamp_prompt_templates
BEFORE UPDATE ON prompt_templates
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER set_timestamp_scope_templates
BEFORE UPDATE ON scope_templates
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER set_timestamp_clients
BEFORE UPDATE ON clients
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
