/**
 * Organization Service
 * Handles all API calls related to organizations
 */

import { supabase } from "../supabase/supabaseClient";
import {
  Organization,
  OrganizationMember,
  OrganizationInvitation,
  MemberRole,
} from "../types/organization";

/**
 * Create a new organization
 */
export const createOrganization = async (
  name: string,
  description?: string
): Promise<Organization | null> => {
  try {
    const { data, error } = await supabase.rpc("create_organization", {
      org_name: name,
      org_description: description || null,
    });

    if (error) throw error;

    // Fetch the newly created organization
    if (data) {
      const { data: orgData, error: orgError } = await supabase
        .from("organisations")
        .select("*")
        .eq("id", data)
        .single();

      if (orgError) throw orgError;
      return orgData;
    }

    return null;
  } catch (error) {
    console.error("Error creating organization:", error);
    throw error;
  }
};

/**
 * Get all organizations for the current user
 */
export const getUserOrganizations = async (): Promise<{
  organizations: Organization[];
  membership: OrganizationMember[];
}> => {
  try {
    const { data, error } = await supabase.rpc("get_user_organizations");

    if (error) throw error;

    // Fetch the full organization details for each returned organization
    const orgIds = data.map((org: any) => org.id);
    const { data: organizations, error: orgError } = await supabase
      .from("organisations")
      .select("*")
      .in("id", orgIds);

    if (orgError) throw orgError;

    // Get membership details
    const { data: memberships, error: memberError } = await supabase
      .from("organisation_members")
      .select("*")
      .in("organisation_id", orgIds);

    if (memberError) throw memberError;

    return {
      organizations: organizations || [],
      membership: memberships || [],
    };
  } catch (error) {
    console.error("Error fetching user organizations:", error);
    throw error;
  }
};

/**
 * Get a single organization by ID
 */
export const getOrganization = async (
  id: string
): Promise<Organization | null> => {
  try {
    const { data, error } = await supabase
      .from("organisations")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error fetching organization with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get all members of an organization
 */
export const getOrganizationMembers = async (
  organizationId: string
): Promise<OrganizationMember[]> => {
  try {
    // First get the organization members
    const { data: membersData, error: membersError } = await supabase
      .from("organisation_members")
      .select("*")
      .eq("organisation_id", organizationId);

    if (membersError) throw membersError;

    if (!membersData || membersData.length === 0) {
      return [];
    }

    // Get user profiles for all members
    const userIds = membersData.map((member) => member.user_id);
    const { data: profilesData, error: profilesError } = await supabase
      .from("profiles")
      .select("id, name, email")
      .in("id", userIds);

    if (profilesError) throw profilesError;

    // Combine the data
    return membersData.map((member: any) => {
      const profile = profilesData?.find((p) => p.id === member.user_id);
      return {
        id: member.id,
        organisation_id: member.organisation_id,
        user_id: member.user_id,
        role: member.role,
        created_at: member.joined_at,
        full_name: profile?.name,
        email: profile?.email,
        avatar_url: undefined, // Set to undefined since column doesn't exist
      };
    });
  } catch (error) {
    console.error(
      `Error fetching members for organization ${organizationId}:`,
      error
    );
    throw error;
  }
};

/**
 * Check if the current user is an admin of the organization
 */
export const isOrganizationAdmin = async (
  organizationId: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc("is_org_admin", {
      org_id: organizationId,
    });

    if (error) throw error;
    return !!data;
  } catch (error) {
    console.error(
      `Error checking admin status for organization ${organizationId}:`,
      error
    );
    throw error;
  }
};

/**
 * Invite a user to an organization
 */
export const inviteToOrganization = async (
  organizationId: string,
  email: string,
  role: MemberRole = "member"
): Promise<OrganizationInvitation | null> => {
  try {
    const { data, error } = await supabase.rpc("invite_to_organisation", {
      org_id: organizationId,
      user_email: email,
      user_role: role,
    });

    if (error) throw error;

    // Fetch the invitation details
    if (data) {
      const { data: invitationData, error: invitationError } = await supabase
        .from("organisation_invitations")
        .select("*")
        .eq("id", data)
        .single();

      if (invitationError) throw invitationError;
      return invitationData;
    }

    return null;
  } catch (error) {
    console.error("Error inviting user to organization:", error);
    throw error;
  }
};

/**
 * Get all invitations for an organization
 */
export const getOrganizationInvitations = async (
  organizationId: string
): Promise<OrganizationInvitation[]> => {
  try {
    const { data, error } = await supabase
      .from("organisation_invitations")
      .select("*")
      .eq("organisation_id", organizationId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error(
      `Error fetching invitations for organization ${organizationId}:`,
      error
    );
    throw error;
  }
};

/**
 * Accept an organization invitation
 */
export const acceptOrganizationInvitation = async (
  invitationId: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc(
      "accept_organisation_invitation",
      {
        invitation_id: invitationId,
      }
    );

    if (error) throw error;
    return !!data;
  } catch (error) {
    console.error(`Error accepting invitation ${invitationId}:`, error);
    throw error;
  }
};

/**
 * Get all pending invitations for the current user
 */
export const getUserPendingOrganizationInvitations = async (): Promise<
  OrganizationInvitation[]
> => {
  try {
    // First get the user's email
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user?.email) {
      throw new Error("User email not available");
    }

    // Get invitations for this email
    const { data, error } = await supabase
      .from("organisation_invitations")
      .select(
        `
        *,
        organization:organisation_id (name)
      `
      )
      .eq("email", user.email)
      .eq("status", "pending");

    if (error) throw error;

    // Format the response to include organization name
    return (data || []).map((invitation) => ({
      ...invitation,
      organization_name: invitation.organization?.name,
    }));
  } catch (error) {
    console.error("Error fetching user organization invitations:", error);
    throw error;
  }
};

/**
 * Update an organization
 */
export const updateOrganization = async (
  organizationId: string,
  name: string,
  description?: string
): Promise<Organization | null> => {
  try {
    const { data, error } = await supabase
      .from("organisations")
      .update({
        name,
        description,
        updated_at: new Date().toISOString(),
      })
      .eq("id", organizationId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error updating organization:", error);
    throw error;
  }
};

/**
 * Delete an organization
 */
export const deleteOrganization = async (
  organizationId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("organisations")
      .delete()
      .eq("id", organizationId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Error deleting organization:", error);
    throw error;
  }
};

/**
 * Remove a member from an organization
 */
export const removeOrganizationMember = async (
  organizationId: string,
  userId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("organisation_members")
      .delete()
      .eq("organisation_id", organizationId)
      .eq("user_id", userId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Error removing organization member:", error);
    throw error;
  }
};

/**
 * Reject an organization invitation
 */
export const rejectOrganizationInvitation = async (
  invitationId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("organisation_invitations")
      .update({ status: "rejected", updated_at: new Date().toISOString() })
      .eq("id", invitationId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error rejecting invitation ${invitationId}:`, error);
    throw error;
  }
};
