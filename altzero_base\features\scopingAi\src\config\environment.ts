// Environment configuration for the application
export const config = {
  // API Configuration
  apiUrl: import.meta.env.VITE_API_URL || "http://localhost:3001",

  // Application Configuration
  appName: import.meta.env.VITE_APP_NAME || "ScopingAI",
  appVersion: import.meta.env.VITE_APP_VERSION || "1.0.0",

  // Development Configuration
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,

  // Feature Flags
  enableDebugMode: import.meta.env.VITE_DEBUG_MODE === "true",
  enableAnalytics: import.meta.env.VITE_ENABLE_ANALYTICS === "true",
};

// API Endpoints
export const apiEndpoints = {
  // Document Generation
  generateDocument: "/api/scopingai/proposals/stream",
  regenerateSection: "/api/scopingai/regenerate-section",

  // Document Management
  documents: "/api/scopingai/documents",
  documentById: (id: string) => `/api/scopingai/documents/${id}`,
  saveDocument: "/api/scopingai/documents/save",
  documentLibrary: "/api/scopingai/documents/library",

  // Export
  exportPDF: (id: string) => `/api/scopingai/documents/${id}/export/pdf`,
  exportWord: (id: string) => `/api/scopingai/documents/${id}/export/docx`,

  // Client Management
  clients: "/api/scopingai/clients",
  clientById: (id: string) => `/api/scopingai/clients/${id}`,

  // Prompt Templates
  promptTemplates: "/api/scopingai/prompt-templates",
  promptTemplateById: (id: string) => `/api/scopingai/prompt-templates/${id}`,

  // Reference Documents
  referenceDocuments: "/api/reference-documents",
  referenceDocumentById: (id: string) => `/api/reference-documents/${id}`,

  // Health Check
  health: "/health",
};

// Default configuration values
export const defaults = {
  // Request timeouts
  apiTimeout: 30000,
  streamTimeout: 300000,

  // Pagination
  defaultPageSize: 20,
  maxPageSize: 100,

  // File upload
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: [".pdf", ".doc", ".docx", ".txt", ".md"],

  // Document generation
  defaultTemplate: "standard",
  maxSections: 20,
  maxSectionLength: 5000,
};

export default config;
