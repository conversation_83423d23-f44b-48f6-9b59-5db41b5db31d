import React, { useState, useEffect, useCallback } from 'react';
import { databaseService } from '../../services/pseo/databaseService';
import { workflowService, type WorkflowResult } from '../../services/SingleSiteAudit/workflowService';
import { useUser } from '../../../../base/contextapi/UserContext';
import { useNavigate } from 'react-router-dom';
import type { PSEOClient, PSEOWebsite, AuditProgress } from '../../types';

const AuditRunner: React.FC = () => {
  const { user } = useUser();
  const [clients, setClients] = useState<PSEOClient[]>([]);
  const [selectedClientId, setSelectedClientId] = useState('');
  const [websites, setWebsites] = useState<PSEOWebsite[]>([]);
  const [selectedWebsiteId, setSelectedWebsiteId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Audit state
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState<AuditProgress | null>(null);
  const [currentAuditId, setCurrentAuditId] = useState<string | null>(null);
  const [result, setResult] = useState<WorkflowResult | null>(null);

  const navigate = useNavigate();

  // Progress polling
  const pollProgress = useCallback(async (auditId: string) => {
    try {
      const progressData = await workflowService.getAuditProgress(auditId);
      setProgress(progressData);
      
      // If audit is completed, stop polling and navigate
      if (progressData.isComplete && !progressData.hasError) {
        return true; // Indicates completion
      }
      
      if (progressData.hasError) {
        setError(progressData.errorMessage || 'Audit failed');
        return true; // Stop polling on error
      }
      
      return false; // Continue polling
    } catch (err) {
      console.error('Failed to get progress:', err);
      return false; // Continue polling despite error
    }
  }, []);

  // Progress polling effect
  useEffect(() => {
    if (!currentAuditId || !isRunning) return;

    const interval = setInterval(async () => {
      const shouldStop = await pollProgress(currentAuditId);
      if (shouldStop) {
        clearInterval(interval);
        setIsRunning(false);
        setCurrentAuditId(null);
      }
    }, 2000); // Poll every 2 seconds

    return () => clearInterval(interval);
  }, [currentAuditId, isRunning, pollProgress]);

  // Add cleanup effect when component unmounts
  useEffect(() => {
    return () => {
      // Clean up any running intervals when component unmounts
      setIsRunning(false);
      setCurrentAuditId(null);
    };
  }, []);

  useEffect(() => {
    if (user?.id) {
      loadClients();
    }
  }, [user?.id]);

  useEffect(() => {
    if (selectedClientId) {
      loadWebsites();
    } else {
      setWebsites([]);
      setSelectedWebsiteId('');
    }
  }, [selectedClientId]);

  const loadClients = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const clientsData = await databaseService.getClientsByUserId(user.id);
      setClients(clientsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const loadWebsites = async () => {
    if (!selectedClientId) return;

    setLoading(true);
    try {
      const websitesData = await databaseService.getWebsitesByClientId(selectedClientId);
      setWebsites(websitesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load websites');
    } finally {
      setLoading(false);
    }
  };

  const handleStartAudit = async () => {
    if (!selectedWebsiteId) {
      setError('Please select a website to audit from your existing clients and websites.');
      return;
    }

    if (!user?.id) {
      setError('Please log in to perform SEO audits');
      return;
    }

    const selectedWebsite = websites.find(w => w.id === selectedWebsiteId);
    if (!selectedWebsite) {
      setError('Selected website not found');
      return;
    }

    setIsRunning(true);
    setError(null);
    setResult(null);
    setProgress(null);
    setCurrentAuditId(null);

    try {
      // Start the audit process in the background
      const auditPromise = workflowService.startAudit(selectedWebsite.url, selectedWebsite.name, {
        aiModel: 'gpt-4o-mini',
        userId: user.id,
        websiteId: selectedWebsiteId,
      });

      // Give the audit a moment to create the audit record
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Try to get the audit ID from the database by checking recent audits
      // Only do this once, not in a polling loop
      try {
        const recentAudits = await databaseService.getAuditsByUserId(user.id, 5); // Limit to 5 recent audits
        const latestAudit = recentAudits.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )[0];
        
        if (latestAudit && latestAudit.status !== 'completed' && latestAudit.status !== 'failed') {
          setCurrentAuditId(latestAudit.id);
          console.log('Found running audit ID:', latestAudit.id);
        }
      } catch (auditIdError) {
        console.warn('Could not get audit ID for progress tracking:', auditIdError);
        // Continue without progress tracking if audit ID fetch fails
      }

      // Wait for the audit to complete
      const workflowResult = await auditPromise;
      setResult(workflowResult);
      
      // Stop polling when audit completes
      setIsRunning(false);
      setCurrentAuditId(null);
      
      // Navigate to audit results if audit was completed and has an ID
      if (workflowResult.audit && workflowResult.audit.id) {
        navigate(`/audit-results/${workflowResult.audit.id}`);
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
    }
  };

  const selectedClient = clients.find(c => c.id === selectedClientId);
  const selectedWebsite = websites.find(w => w.id === selectedWebsiteId);

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            SEO Audit Runner
          </h1>
          <p className="text-muted-foreground">
            Select a website and run a comprehensive SEO audit
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {/* Selection Panel */}
          <div className="bg-card rounded-lg border p-6">
            <h2 className="text-xl font-semibold mb-4">Select Website to Audit</h2>
            
            <div className="space-y-4">
              {/* Client Selection */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Select Client
                </label>
                <select
                  value={selectedClientId}
                  onChange={(e) => setSelectedClientId(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  disabled={isRunning}
                >
                  <option value="">Choose a client...</option>
                  {clients.map((client) => (
                    <option key={client.id} value={client.id}>
                      {client.name} {client.company ? `(${client.company})` : ''}
                    </option>
                  ))}
                </select>
              </div>

              {/* Website Selection */}
              {selectedClientId && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Select Website
                  </label>
                  <select
                    value={selectedWebsiteId}
                    onChange={(e) => setSelectedWebsiteId(e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    disabled={isRunning || loading}
                  >
                    <option value="">Choose a website...</option>
                    {websites.map((website) => (
                      <option key={website.id} value={website.id}>
                        {website.name} - {website.url}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Selected Info */}
              {selectedClient && selectedWebsite && (
                <div className="p-4 bg-accent rounded-lg">
                  <h3 className="font-medium mb-2">Selected for Audit:</h3>
                  <p className="text-sm"><strong>Client:</strong> {selectedClient.name}</p>
                  <p className="text-sm"><strong>Website:</strong> {selectedWebsite.name}</p>
                  <p className="text-sm"><strong>URL:</strong> {selectedWebsite.url}</p>
                </div>
              )}

              {/* Start Audit Button */}
              <button
                onClick={handleStartAudit}
                disabled={!selectedWebsiteId || isRunning}
                className="w-full bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRunning ? 'Running Audit...' : 'Start SEO Audit'}
              </button>
            </div>

            {/* Error */}
            {error && (
              <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
                <h3 className="font-medium text-destructive mb-1">Error</h3>
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}

            {/* Progress Display */}
            {isRunning && progress && (
              <div className="mt-6 p-4 bg-accent rounded-md">
                <h3 className="font-medium mb-4">Audit Progress</h3>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Current Step:</span>
                    <span className="font-medium">{progress.currentStep}</span>
                  </div>
                  <div className="w-full">
                    <div className="flex justify-between text-sm text-muted-foreground mb-1">
                      <span>Progress</span>
                      <span>{progress.progressPercentage}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress.progressPercentage}%` }}
                      />
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {progress.completedSteps.length} of {progress.totalSteps} steps completed
                  </div>
                </div>
              </div>
            )}

            {/* Simple Running Indicator */}
            {isRunning && !progress && (
              <div className="mt-6 p-4 bg-accent rounded-md">
                <h3 className="font-medium mb-2">Starting Audit...</h3>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: '10%' }} />
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Initializing audit process and setting up progress tracking...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditRunner; 