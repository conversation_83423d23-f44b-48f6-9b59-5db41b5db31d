import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "../../../../../components/Layout";
import { useProgressiveScoping } from "../../../hooks/useProgressiveScoping";
import {
  Section,
  Client,
  ScopeTemplate,
  PromptTemplate,
  ScopingDocument
} from "../../../../../types/scoping";
import ProgressiveScopingResult from "./ProgressiveScopingResult";
import { supabase } from "../../../../../utils/supabaseClient";

// Define extended document type that includes all properties needed for both components
interface ExtendedScopingDocument {
  id: string;
  projectName: string;
  clientName: string;
  sections: Section[];
  templateInfo: {
    promptTemplate?: {
      id: string;
      name: string;
      content: string;
    };
    scopeTemplate?: {
      id: string;
      name: string;
      description?: string;
    };
    sectionTemplate?: {
      id: string;
      name: string;
      description?: string;
    };
  };
  baseContent?: {
    description: string;
    blocks?: any[];
  };
  clientInfo?: any;
  scopingInfo?: any;
}

// Define separate form data interfaces for each concern
interface ProjectFormData {
  projectName: string;
  additionalInfo: string;
}

enum AppStep {
  FORM,
  UPLOADING,
  CREATING_BASE_DOCUMENT,
  DOCUMENT,
}

// Add adapter function to convert ExtendedScopingDocument to ScopingDocument
const adaptToScopingDocument = (doc: ExtendedScopingDocument | null): ScopingDocument | null => {
  if (!doc) return null;
  
  return {
    id: doc.id,
    userId: 'current-user', // Use actual user ID if available
    name: doc.projectName,
    sections: doc.sections,
    baseContent: doc.baseContent || { description: '' },
    createdAt: new Date(),
    updatedAt: new Date(),
    clientName: doc.clientName,
    projectName: doc.projectName,
    // Add optional properties if they exist
    description: doc.baseContent?.description || '',
    clientId: doc.clientInfo?.id,
    scopeTemplateId: doc.templateInfo?.scopeTemplate?.id,
    promptTemplateId: doc.templateInfo?.promptTemplate?.id,
    sectionTemplateId: doc.templateInfo?.sectionTemplate?.id
  };
};

const ProgressiveScopeCreator: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<AppStep>(AppStep.FORM);
  const [progressMessage, setProgressMessage] = useState<string>("");
  const [progressPercentage, setProgressPercentage] = useState<number>(0);

  // Separate state for each concern
  const [projectData, setProjectData] = useState<ProjectFormData>({
    projectName: "",
    additionalInfo: "",
  });
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [selectedPromptTemplateId, setSelectedPromptTemplateId] =
    useState<string>("");
  const [selectedScopeTemplateId, setSelectedScopeTemplateId] =
    useState<string>("");
  const [selectedSectionTemplateId, setSelectedSectionTemplateId] =
    useState<string>("");

  // Added document file state
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [uploadedDocumentId, setUploadedDocumentId] = useState<string | null>(
    null
  );

  // Resources
  const [clients, setClients] = useState<Client[]>([]);
  const [promptTemplates, setPromptTemplates] = useState<PromptTemplate[]>([]);
  const [scopeTemplates, setScopeTemplates] = useState<ScopeTemplate[]>([]);
  const [sectionTemplates, setSectionTemplates] = useState<
    Array<{
      id: string;
      name: string;
      description: string;
      sections: Array<{
        title: string;
        content?: string;
        description?: string;
        order: number;
      }>;
    }>
  >([]);
  const [loadingResources, setLoadingResources] = useState(true);

  const {
    loading,
    error,
    document,
    createDocument,
    generateSection,
    generateAllSections,
    updateDocument,
    updateSection,
    reorderSections,
    exportDocument,
    resetDocument,
  } = useProgressiveScoping();

  // Near the beginning of the component, add a console.log to check when values change
  useEffect(() => {
    console.log("Selection state:", {
      clientId: selectedClientId,
      promptTemplateId: selectedPromptTemplateId,
      scopeTemplateId: selectedScopeTemplateId,
      sectionTemplateId: selectedSectionTemplateId,
      loading,
    });
  }, [
    selectedClientId,
    selectedPromptTemplateId,
    selectedScopeTemplateId,
    selectedSectionTemplateId,
    loading,
  ]);

  // Handle scope template selection
  const handleScopeTemplateChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const templateId = e.target.value;
    setSelectedScopeTemplateId(templateId);
  };

  // Handle section template selection
  const handleSectionTemplateChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedSectionTemplateId(e.target.value);
  };

  // Update fetchResources to get section templates
  useEffect(() => {
    const fetchResources = async () => {
      try {
        setLoadingResources(true);

        // Get current user
        const { data: userData, error: userError } =
          await supabase.auth.getUser();

        if (userError) {
          console.error("Error getting user:", userError);
          throw userError;
        }

        const userId = userData.user?.id;
        if (!userId) {
          console.error("No user ID found, user may not be authenticated");
          return;
        }

        // Fetch clients from Supabase
        const { data: clientsData, error: clientsError } = await supabase
          .from("clients")
          .select("*")
          .eq("user_id", userId)
          .order("name");

        if (clientsError) {
          console.error("Error fetching clients:", clientsError);
        } else {
          // Transform the data to match our Client type
          const formattedClients = clientsData.map((client) => ({
            id: client.id,
            name: client.name,
            contactPerson: client.contact_person,
            email: client.email,
            phone: client.phone || "",
            company: client.company,
            industry: client.industry,
            createdAt: new Date(client.created_at),
            updatedAt: new Date(client.updated_at),
          }));

          setClients(formattedClients);
        }

        // Fetch prompt templates from Supabase
        const { data: promptData, error: promptError } = await supabase
          .from("prompt_templates")
          .select("*")
          .eq("user_id", userId)
          .order("name");

        if (promptError) {
          console.error("Error fetching prompt templates:", promptError);
        } else {
          // Transform the data to match our PromptTemplate type
          const formattedPromptTemplates = promptData.map((template) => ({
            id: template.id,
            name: template.name,
            description: template.description || "",
            content: template.content,
            variables: template.variables || [],
            createdAt: new Date(template.created_at),
            updatedAt: new Date(template.updated_at),
          }));

          setPromptTemplates(formattedPromptTemplates);
        }

        // Fetch scope templates from Supabase
        const { data: scopeData, error: scopeError } = await supabase
          .from("scope_templates")
          .select("*")
          .eq("user_id", userId)
          .order("name");

        if (scopeError) {
          console.error("Error fetching scope templates:", scopeError);
        } else {
          // Transform the data to match our ScopeTemplate type
          const formattedScopeTemplates = scopeData.map((template) => ({
            id: template.id,
            name: template.name,
            description: template.description || "",
            content: template.content || {},
            sections: template.sections || [],
            createdAt: new Date(template.created_at),
            updatedAt: new Date(template.updated_at),
          }));

          setScopeTemplates(formattedScopeTemplates);
        }

        // Fetch section templates from Supabase
        try {
          // Query templates that belong to the user or are public
          const { data: templates, error } = await supabase
            .from("section_templates")
            .select("*")
            .or(`user_id.eq.${userId}`);

          if (error) {
            console.error("Error fetching section templates:", error);
            // Fallback to empty array if there's an error
            setSectionTemplates([]);
          } else {
            // Transform the data to match the expected format
            const formattedTemplates = templates.map((template) => ({
              id: template.id,
              name: template.name,
              description: template.description || "",
              sections: template.sections || [],
            }));

            setSectionTemplates(formattedTemplates);
          }
        } catch (error) {
          console.error("Error in section template fetch:", error);
          // Use empty array as fallback
          setSectionTemplates([]);
        }

        setLoadingResources(false);
      } catch (err) {
        console.error("Error fetching resources:", err);
        setLoadingResources(false);
      }
    };

    fetchResources();
  }, []);

  // Handle project data input changes
  const handleProjectDataChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setProjectData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle client selection
  const handleClientChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedClientId(e.target.value);
  };

  // Handle prompt template selection
  const handlePromptTemplateChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedPromptTemplateId(e.target.value);
  };

  // Handle file change (document upload)
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setDocumentFile(selectedFile);
      // Reset the uploaded document ID if the file changes
      setUploadedDocumentId(null);
    }
  };

  // Replace the uploadDocument function with this mock implementation
  const uploadDocument = async (): Promise<string | null> => {
    try {
      setProgressMessage("Uploading reference document...");
      setProgressPercentage(10);

      // In development mode, just simulate an upload and return a fake ID
      // In production, this would make an actual API call

      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Generate a fake document ID
      const fakeDocumentId =
        "doc_" + Math.random().toString(36).substring(2, 15);

      setProgressMessage("Document uploaded successfully");
      setProgressPercentage(30);

      return fakeDocumentId;
    } catch (error) {
      console.error("Document upload error:", error);
      setProgressMessage("Failed to upload document");
      return null;
    }
  };

  // Create base document with selected sections
  const createBaseDocument = async (documentId: string | null) => {
    try {
      setProgressMessage("Creating base document structure...");
      setProgressPercentage(40);

      // Find the selected resources
      const selectedClient = clients.find(
        (client) => client.id === selectedClientId
      );
      const selectedPromptTemplate = promptTemplates.find(
        (template) => template.id === selectedPromptTemplateId
      );
      const selectedScopeTemplate = scopeTemplates.find(
        (template) => template.id === selectedScopeTemplateId
      );
      const selectedSectionTemplate = sectionTemplates.find(
        (template) => template.id === selectedSectionTemplateId
      );

      // Get sections from the section template
      const sections = selectedSectionTemplate
        ? selectedSectionTemplate.sections
        : [];

      // Create proper sections based on the template
      const formattedSections = sections.map(
        (
          s: { title: string; content?: string; description?: string },
          index: number
        ) => ({
          id: `section-${Date.now()}-${index}`,
          title: s.title,
          content: s.content || "",
          description: s.description || "",
          status: "pending" as const,
          order: index,
          updatedAt: new Date(),
        })
      );

      // Combine all data for document creation
      const documentData = {
        name: `${projectData.projectName} - Scope Document`,
        projectName: projectData.projectName,
        clientName: selectedClient?.name || "Unknown Client",
        baseContent: {
          description: `# ${projectData.projectName}\n\n${
            projectData.additionalInfo || "No additional information provided."
          }\n\nClient: ${selectedClient?.name || "Unknown"}\nIndustry: ${
            selectedClient?.industry || "N/A"
          }`,
        },
        sections: formattedSections,
        clientInfo: {
          id: selectedClient?.id,
          name: selectedClient?.name,
          contactPerson: selectedClient?.contactPerson,
          email: selectedClient?.email,
          phone: selectedClient?.phone,
          company: selectedClient?.company,
          industry: selectedClient?.industry,
        },
        scopingInfo: {
          projectName: projectData.projectName,
          projectDescription: projectData.additionalInfo,
          projectType: selectedScopeTemplate?.content?.projectType,
          timeline: selectedScopeTemplate?.content?.timeline,
        },
        // Store comprehensive template information
        templateInfo: {
          promptTemplate: {
            id: selectedPromptTemplate?.id,
            name: selectedPromptTemplate?.name,
            content: selectedPromptTemplate?.content,
          },
          scopeTemplate: {
            id: selectedScopeTemplate?.id,
            name: selectedScopeTemplate?.name,
            description: selectedScopeTemplate?.description,
          },
          sectionTemplate: {
            id: selectedSectionTemplate?.id,
            name: selectedSectionTemplate?.name,
            description: selectedSectionTemplate?.description,
          },
        },
        documentId, // Reference document ID from upload
      };

      setProgressMessage("Initializing document structure...");
      setProgressPercentage(60);

      // Use the createDocument function from the hook
      await createDocument(documentData);

      setProgressMessage("Document structure created successfully!");
      setProgressPercentage(100);

      // Simply update the state to show the document
      setCurrentStep(AppStep.DOCUMENT);
    } catch (err) {
      console.error("Error creating base document:", err);
      setProgressMessage("Failed to create document structure");

      // Return to form on error
      setCurrentStep(AppStep.FORM);
    }
  };

  // Handle form submission - now a multi-step process
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Step 1: Move to uploading state
      setCurrentStep(AppStep.UPLOADING);
      setProgressPercentage(0);

      // Step 2: Upload document if provided (and not already uploaded)
      let docId = uploadedDocumentId;
      if (documentFile && !uploadedDocumentId) {
        docId = await uploadDocument();
        if (!docId) {
          throw new Error("Failed to upload reference document");
        }
        setUploadedDocumentId(docId);
      }

      // Step 3: Create base document
      setCurrentStep(AppStep.CREATING_BASE_DOCUMENT);
      await createBaseDocument(docId);
    } catch (err) {
      console.error("Error in document creation process:", err);
      setCurrentStep(AppStep.FORM);
    }
  };

  // Handle reset to form
  const handleReset = () => {
    resetDocument();
    setDocumentFile(null);
    setUploadedDocumentId(null);
    setCurrentStep(AppStep.FORM);
    setProgressMessage("");
    setProgressPercentage(0);
  };

  // Handle document export
  const handleExport = async () => {
    try {
      const downloadUrl = await exportDocument();
      window.open(downloadUrl, "_blank");
    } catch (err) {
      console.error("Export failed:", err);
    }
  };

  // Render the appropriate view based on current step
  const renderContent = () => {
    switch (currentStep) {
      case AppStep.FORM:
        return renderForm();

      case AppStep.UPLOADING:
      case AppStep.CREATING_BASE_DOCUMENT:
        return renderProgressBar();

      case AppStep.DOCUMENT:
        // Use the ProgressiveScopingResult component directly with document props
        return (
          <ProgressiveScopingResult
            document={adaptToScopingDocument(document as ExtendedScopingDocument | null)}
            onGenerateSection={handleGenerateSection}
            onGenerateAll={handleGenerateAll}
            onUpdateDocument={(docId, updates) =>
              handleUpdateDocument(docId, updates)
            }
            onUpdateSection={(docId, sectionId, updates) =>
              handleUpdateSection(docId, sectionId, updates)
            }
            onReorderSections={(docId, sections) =>
              handleReorderSections(docId, sections)
            }
            onReset={handleReset}
            onExport={(docId) => handleExport()}
          />
        );

      default:
        return renderForm();
    }
  };

  // Render form
  const renderForm = () => {
    return (
      <div className="max-w-6xl mx-auto py-6">
        <div className="md:flex md:items-center md:justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Create New Scope Document
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Fill in the details below to create a new progressive scoping
              document
            </p>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {loadingResources ? (
          <div className="text-center py-12">
            <div className="spinner mx-auto h-12 w-12 border-4 border-t-4 border-gray-200 rounded-full border-t-indigo-600 animate-spin"></div>
            <p className="mt-3 text-gray-600">Loading resources...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              {/* Project Information */}
              <div className="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  Project Information
                </h3>
              </div>

              <div className="px-4 py-5 sm:p-6 space-y-6">
                <div>
                  <label
                    htmlFor="projectName"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Project Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="projectName"
                    name="projectName"
                    value={projectData.projectName}
                    onChange={handleProjectDataChange}
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="additionalInfo"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Additional Information (optional)
                  </label>
                  <textarea
                    id="additionalInfo"
                    name="additionalInfo"
                    rows={3}
                    value={projectData.additionalInfo}
                    onChange={handleProjectDataChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Add any context or requirements that might help generate better content"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              {/* Client Selection */}
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 border-b border-gray-200 sm:px-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    Client Information
                  </h3>
                </div>

                <div className="px-4 py-5 sm:p-6">
                  <label
                    htmlFor="clientId"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Select Client <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="clientId"
                    name="clientId"
                    value={selectedClientId}
                    onChange={handleClientChange}
                    required
                    className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">Select a client</option>
                    {clients.map((client) => (
                      <option key={client.id} value={client.id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                  <div className="mt-2 text-sm">
                    <button
                      type="button"
                      onClick={() => navigate("/scoping/clients")}
                      className="text-indigo-600 hover:text-indigo-500"
                    >
                      Create new client
                    </button>
                  </div>
                </div>
              </div>

              {/* Scope Template Selection - Moved here */}
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 border-b border-gray-200 sm:px-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    Scope Template
                  </h3>
                </div>

                <div className="px-4 py-5 sm:p-6">
                  <label
                    htmlFor="scopeTemplateId"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Select Scope Template{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="scopeTemplateId"
                    name="scopeTemplateId"
                    value={selectedScopeTemplateId}
                    onChange={handleScopeTemplateChange}
                    required
                    className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">Select a scope template</option>
                    {scopeTemplates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                  <div className="mt-2 text-sm">
                    <button
                      type="button"
                      onClick={() => navigate("/scoping/scope-templates")}
                      className="text-indigo-600 hover:text-indigo-500"
                    >
                      Create new scope template
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              {/* Prompt Template Selection */}
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 border-b border-gray-200 sm:px-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    Prompt Template
                  </h3>
                </div>

                <div className="px-4 py-5 sm:p-6">
                  <label
                    htmlFor="promptTemplateId"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Select AI Prompt Template{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="promptTemplateId"
                    name="promptTemplateId"
                    value={selectedPromptTemplateId}
                    onChange={handlePromptTemplateChange}
                    required
                    className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">Select a prompt template</option>
                    {promptTemplates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                  <div className="mt-2 text-sm">
                    <button
                      type="button"
                      onClick={() => navigate("/scoping/templates")}
                      className="text-indigo-600 hover:text-indigo-500"
                    >
                      Create new prompt template
                    </button>
                  </div>
                </div>
              </div>

              {/* Section Template Selection */}
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <div className="px-4 py-5 border-b border-gray-200 sm:px-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">
                    Document Structure
                  </h3>
                </div>

                <div className="px-4 py-5 sm:p-6">
                  <label
                    htmlFor="sectionTemplateId"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Select Section Template{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="sectionTemplateId"
                    name="sectionTemplateId"
                    value={selectedSectionTemplateId}
                    onChange={handleSectionTemplateChange}
                    required
                    className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">Select a section template</option>
                    {sectionTemplates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                  <div className="mt-2 text-sm">
                    <button
                      type="button"
                      onClick={() => navigate("/scoping/section-templates")}
                      className="text-indigo-600 hover:text-indigo-500"
                    >
                      Create new section template
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Reference Document Upload */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <div className="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 className="text-lg font-medium leading-6 text-gray-900">
                  Reference Document (Optional)
                </h3>
              </div>

              <div className="px-4 py-5 sm:p-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload PDF Reference
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div className="flex text-sm text-gray-600 justify-center">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none"
                      >
                        <span>Upload a file</span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          accept=".pdf"
                          onChange={handleFileChange}
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">PDF up to 10MB</p>
                  </div>
                </div>
                {documentFile && (
                  <p className="mt-2 text-sm text-gray-500 flex items-center">
                    <svg
                      className="h-4 w-4 text-green-500 mr-1.5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {documentFile.name}
                  </p>
                )}
                <p className="mt-2 text-xs text-gray-500">
                  Upload a PDF document to use as a reference for generating
                  your scope document. This will provide the AI with specific
                  context about your project.
                </p>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={
                  loading ||
                  !selectedClientId ||
                  !selectedPromptTemplateId ||
                  !selectedScopeTemplateId ||
                  !selectedSectionTemplateId ||
                  !projectData.projectName.trim()
                }
                className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                  loading ||
                  !selectedClientId ||
                  !selectedPromptTemplateId ||
                  !selectedScopeTemplateId ||
                  !selectedSectionTemplateId ||
                  !projectData.projectName.trim()
                    ? "bg-indigo-300 cursor-not-allowed"
                    : "bg-indigo-600 hover:bg-indigo-700"
                }`}
              >
                {loading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  "Create Document"
                )}
              </button>
            </div>

            {/* Add validation hint */}
            {(!selectedClientId ||
              !selectedPromptTemplateId ||
              !selectedScopeTemplateId ||
              !selectedSectionTemplateId ||
              !projectData.projectName.trim()) && (
              <div className="mt-2 text-xs text-gray-500">
                <p>
                  Please complete all required fields to enable the create
                  button:
                </p>
                <ul className="list-disc ml-5 mt-1">
                  {!projectData.projectName.trim() && <li>Project Name</li>}
                  {!selectedClientId && <li>Client</li>}
                  {!selectedPromptTemplateId && <li>Prompt Template</li>}
                  {!selectedScopeTemplateId && <li>Scope Template</li>}
                  {!selectedSectionTemplateId && <li>Section Template</li>}
                </ul>
              </div>
            )}
          </form>
        )}
      </div>
    );
  };

  // Enhanced progress bar for document generation with percentage
  const renderProgressBar = () => {
    return (
      <div className="max-w-3xl mx-auto py-12">
        <div className="text-center mb-8">
          <h2 className="text-xl font-semibold text-gray-900">
            {currentStep === AppStep.UPLOADING
              ? "Uploading Document"
              : "Creating Your Scoping Document"}
          </h2>
          <p className="text-gray-500 mt-2">
            {progressMessage || "Please wait while we process your request..."}
          </p>
        </div>

        <div className="bg-white shadow overflow-hidden rounded-md">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex justify-between items-center">
              <div className="text-sm font-medium text-indigo-600">
                {currentStep === AppStep.UPLOADING
                  ? "Uploading document"
                  : "Preparing document structure"}
              </div>
              <div className="text-sm text-gray-500">
                {progressPercentage}% Complete
              </div>
            </div>
            <div className="mt-4 relative pt-1">
              <div className="overflow-hidden h-2 text-xs flex rounded bg-indigo-200">
                <div
                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-indigo-500"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="mt-6 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Added cancel button during processing */}
        <div className="mt-6 text-center">
          <button
            onClick={handleReset}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  };

  // Handle section generation
  const handleGenerateSection = async (sectionId: string) => {
    if (!document) return;

    console.log(`Generating section with ID: ${sectionId}`);

    try {
      await generateSection(sectionId);
      console.log(`Section generation completed for: ${sectionId}`);
    } catch (error) {
      console.error(`Error generating section ${sectionId}:`, error);
    }
  };

  // Handle generate all sections
  const handleGenerateAll = async () => {
    if (!document) return;

    try {
      await generateAllSections();
      console.log(`All sections generation completed`);
    } catch (error) {
      console.error(`Error generating all sections:`, error);
    }
  };

  // Handle document update
  const handleUpdateDocument = async (
    documentId: string,
    updates: Partial<ScopingDocument>
  ) => {
    // Convert ScopingDocument updates to ExtendedScopingDocument updates
    const extendedUpdates: Partial<ExtendedScopingDocument> = {
      ...(updates.projectName !== undefined && { projectName: updates.projectName }),
      ...(updates.clientName !== undefined && { clientName: updates.clientName }),
      ...(updates.baseContent !== undefined && { baseContent: updates.baseContent }),
      // Add other properties as needed
    };
    
    await updateDocument(extendedUpdates);
  };

  // Update section with changes
  const handleUpdateSection = async (
    documentId: string,
    sectionId: string,
    updates: Partial<Section>
  ) => {
    await updateSection(sectionId, updates);
  };

  // Handle section reordering
  const handleReorderSections = async (
    documentId: string,
    sections: Section[]
  ) => {
    await reorderSections(sections);
  };

  return <Layout>{renderContent()}</Layout>;
};

export default ProgressiveScopeCreator;
