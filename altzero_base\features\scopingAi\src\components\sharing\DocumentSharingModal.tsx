import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "../../../../../base/components/ui/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Tabs<PERSON>rigger,
} from "../../../../../base/components/ui/tabs";
import { But<PERSON> } from "../../../../../base/components/ui/button";
import { Input } from "../../../../../base/components/ui/input";
import { Label } from "../../../../../base/components/ui/label";
import { Textarea } from "../../../../../base/components/ui/textarea";
import { Badge } from "../../../../../base/components/ui/badge";
import {
  Avatar,
  AvatarFallback,
} from "../../../../../base/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import { Switch } from "../../../../../base/components/ui/switch";
import { Separator } from "../../../../../base/components/ui/separator";
import { useToast } from "../../../../../base/hooks/use-toast";
import {
  Users,
  UserPlus,
  Settings,
  Search,
  X,
  Eye,
  MessageSquare,
  Edit,
  Shield,
  Crown,
  Calendar,
  Building,
  UserCheck,
  AlertCircle,
  Check,
  MoreHorizontal,
  Trash2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../../../../base/components/ui/dropdown-menu";
import {
  SharePermissionLevel,
  ShareEntityType,
  ShareTarget,
  ShareRequest,
  DocumentShareData,
  PERMISSION_DESCRIPTIONS,
} from "../../types/sharing";
import {
  shareDocument,
  getDocumentShares,
  searchShareTargets,
  revokeDocumentShare,
  updateDocumentShare,
} from "../../services/documentSharingService";

interface DocumentSharingModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  documentTitle: string;
  canManage?: boolean;
  initialTab?: "share" | "manage";
}

export const DocumentSharingModal: React.FC<DocumentSharingModalProps> = ({
  isOpen,
  onClose,
  documentId,
  documentTitle,
  canManage = true,
  initialTab = "share",
}) => {
  const { toast } = useToast();

  // State management
  const [activeTab, setActiveTab] = useState<"share" | "manage">(initialTab);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<{
    users: ShareTarget[];
    teams: ShareTarget[];
    organizations: ShareTarget[];
  }>({ users: [], teams: [], organizations: [] });
  const [selectedTargets, setSelectedTargets] = useState<ShareTarget[]>([]);
  const [sharePermission, setSharePermission] =
    useState<SharePermissionLevel>("view");
  const [shareMessage, setShareMessage] = useState("");
  const [notifyTargets, setNotifyTargets] = useState(true);
  const [existingShares, setExistingShares] = useState<DocumentShareData[]>([]);

  // Permission icons mapping
  const getPermissionIcon = (permission: SharePermissionLevel) => {
    switch (permission) {
      case "view":
        return <Eye className="h-4 w-4" />;
      case "comment":
        return <MessageSquare className="h-4 w-4" />;
      case "suggest":
        return <Edit className="h-4 w-4" />;
      case "edit":
        return <Edit className="h-4 w-4" />;
      case "manage":
        return <Shield className="h-4 w-4" />;
      case "admin":
        return <Crown className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  // Entity type icons
  const getEntityIcon = (type: ShareEntityType) => {
    switch (type) {
      case "user":
        return <UserCheck className="h-4 w-4" />;
      case "team":
        return <Users className="h-4 w-4" />;
      case "organisation":
        return <Building className="h-4 w-4" />;
      default:
        return <UserCheck className="h-4 w-4" />;
    }
  };

  // Load existing shares
  const loadExistingShares = useCallback(async () => {
    try {
      setIsLoading(true);
      const { shares } = await getDocumentShares(documentId);
      setExistingShares(shares);
    } catch (error) {
      console.error("Error loading shares:", error);
      toast({
        title: "Error",
        description: "Failed to load existing shares",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [documentId, toast]);

  // Search for share targets
  const handleSearch = useCallback(
    async (query: string) => {
      if (!query.trim() || query.trim().length < 2) {
        setSearchResults({ users: [], teams: [], organizations: [] });
        return;
      }

      try {
        // Enhanced search that includes email-based searching
        const results = await searchShareTargets(query);

        // If the query looks like an email, prioritize user results
        const isEmailQuery = query.includes("@");
        if (isEmailQuery) {
          // Sort users first if searching by email
          const sortedResults = {
            users: results.users,
            teams: results.teams,
            organizations: results.organizations,
          };
          setSearchResults(sortedResults);
        } else {
          setSearchResults(results);
        }
      } catch (error) {
        console.error("Error searching targets:", error);
        // Show helpful error if no results for email
        if (query.includes("@")) {
          toast({
            title: "No user found",
            description: "No existing user found with that email address",
            variant: "destructive",
          });
        }
      }
    },
    [toast]
  );

  // Add target to selection
  const addTarget = (target: ShareTarget) => {
    if (
      !selectedTargets.find((t) => t.id === target.id && t.type === target.type)
    ) {
      setSelectedTargets([...selectedTargets, target]);
    }
    setSearchQuery("");
    setSearchResults({ users: [], teams: [], organizations: [] });
  };

  // Remove target from selection
  const removeTarget = (target: ShareTarget) => {
    setSelectedTargets(
      selectedTargets.filter(
        (t) => !(t.id === target.id && t.type === target.type)
      )
    );
  };

  // Handle sharing
  const handleShare = async () => {
    if (selectedTargets.length === 0) {
      toast({
        title: "No targets selected",
        description:
          "Please select at least one person, team, or organization to share with",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      const request: ShareRequest = {
        targets: selectedTargets,
        permissionLevel: sharePermission,
        message: shareMessage,
        notifyTargets,
      };

      const response = await shareDocument(documentId, request);

      if (response.success) {
        toast({
          title: "Document shared successfully",
          description: response.message,
        });
        setSelectedTargets([]);
        setShareMessage("");
        await loadExistingShares();
      } else {
        toast({
          title: "Sharing failed",
          description: response.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error sharing document:", error);
      toast({
        title: "Error",
        description: "Failed to share document",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Revoke share
  const handleRevokeShare = async (shareId: string) => {
    try {
      await revokeDocumentShare(shareId);
      toast({
        title: "Share revoked",
        description: "Access has been removed",
      });
      await loadExistingShares();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to revoke share",
        variant: "destructive",
      });
    }
  };

  // Effects
  useEffect(() => {
    if (isOpen) {
      loadExistingShares();
    }
  }, [isOpen, loadExistingShares]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      handleSearch(searchQuery);
    }, 100);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, handleSearch]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Share Document
          </DialogTitle>
          <DialogDescription>
            Share "{documentTitle}" with your team, organization, or via link
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as any)}
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="share" className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Share with People
            </TabsTrigger>
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Manage Access
            </TabsTrigger>
          </TabsList>

          {/* Share with People Tab */}
          <TabsContent value="share" className="space-y-4">
            {/* Search and Selection */}
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Type to search users, teams, or organizations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
                {searchQuery.includes("@") && (
                  <div className="absolute right-3 top-3">
                    <Badge variant="outline" className="text-xs">
                      Email Search
                    </Badge>
                  </div>
                )}
              </div>

              {/* Search Results */}
              {(searchResults.users.length > 0 ||
                searchResults.teams.length > 0 ||
                searchResults.organizations.length > 0) && (
                <div className="border rounded-lg p-3 max-h-48 overflow-y-auto space-y-2">
                  {/* Users Section */}
                  {searchResults.users.length > 0 && (
                    <div className="space-y-2">
                      {searchQuery.includes("@") &&
                        searchResults.users.length > 0 && (
                          <div className="text-xs font-medium text-muted-foreground px-2">
                            Users matching email
                          </div>
                        )}
                      {searchResults.users.map((user) => (
                        <div
                          key={`user-${user.id}`}
                          className="flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer border-l-2 border-l-blue-500"
                          onClick={() => addTarget(user)}
                        >
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="bg-blue-100 text-blue-700">
                                {user.name[0]?.toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium">{user.name}</p>
                              <p className="text-xs text-muted-foreground font-mono">
                                {user.email}
                              </p>
                              {(user as any).department && (
                                <p className="text-xs text-muted-foreground">
                                  {(user as any).department}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="bg-blue-50">
                              {getEntityIcon(user.type)} User
                            </Badge>
                            {searchQuery.includes("@") &&
                              user.email
                                ?.toLowerCase()
                                .includes(searchQuery.toLowerCase()) && (
                                <Badge variant="secondary" className="text-xs">
                                  Email Match
                                </Badge>
                              )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Teams Section */}
                  {searchResults.teams.length > 0 && (
                    <div className="space-y-2">
                      {searchResults.users.length > 0 && <Separator />}
                      <div className="text-xs font-medium text-muted-foreground px-2">
                        Teams
                      </div>
                      {searchResults.teams.map((team) => (
                        <div
                          key={`team-${team.id}`}
                          className="flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer border-l-2 border-l-green-500"
                          onClick={() => addTarget(team)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                              <Users className="h-4 w-4 text-green-600" />
                            </div>
                            <div>
                              <p className="text-sm font-medium">{team.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {team.memberCount} members
                              </p>
                            </div>
                          </div>
                          <Badge variant="outline" className="bg-green-50">
                            {getEntityIcon(team.type)} Team
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Organizations Section */}
                  {searchResults.organizations.length > 0 && (
                    <div className="space-y-2">
                      {(searchResults.users.length > 0 ||
                        searchResults.teams.length > 0) && <Separator />}
                      <div className="text-xs font-medium text-muted-foreground px-2">
                        Organizations
                      </div>
                      {searchResults.organizations.map((org) => (
                        <div
                          key={`org-${org.id}`}
                          className="flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer border-l-2 border-l-purple-500"
                          onClick={() => addTarget(org)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                              <Building className="h-4 w-4 text-purple-600" />
                            </div>
                            <div>
                              <p className="text-sm font-medium">{org.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {org.memberCount} members
                              </p>
                            </div>
                          </div>
                          <Badge variant="outline" className="bg-purple-50">
                            {getEntityIcon(org.type)} Organization
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* No Results State */}
              {searchQuery.trim() &&
                searchResults.users.length === 0 &&
                searchResults.teams.length === 0 &&
                searchResults.organizations.length === 0 && (
                  <div className="border rounded-lg p-4 text-center text-muted-foreground">
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    {searchQuery.includes("@") ? (
                      <div>
                        <p className="font-medium">
                          No user found with email "{searchQuery}"
                        </p>
                        <p className="text-xs mt-1">
                          Only existing users can be found by email
                        </p>
                      </div>
                    ) : (
                      <div>
                        <p className="font-medium">
                          No results found for "{searchQuery}"
                        </p>
                        <p className="text-xs mt-1">
                          Try searching by email, name, team, or organization
                        </p>
                      </div>
                    )}
                  </div>
                )}

              {/* Selected Targets */}
              {selectedTargets.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected ({selectedTargets.length})</Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedTargets.map((target) => (
                      <Badge
                        key={`${target.type}-${target.id}`}
                        variant="secondary"
                        className="flex items-center gap-2"
                      >
                        {getEntityIcon(target.type)}
                        {target.name}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 hover:bg-transparent"
                          onClick={() => removeTarget(target)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Permission Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Permission Level</Label>
                  <Select
                    value={sharePermission}
                    onValueChange={(value: SharePermissionLevel) =>
                      setSharePermission(value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="view">
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          <div>
                            <p className="font-medium">View</p>
                            <p className="text-xs text-muted-foreground">
                              Can only view the document
                            </p>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="edit">
                        <div className="flex items-center gap-2">
                          <Edit className="h-4 w-4" />
                          <div>
                            <p className="font-medium">Edit</p>
                            <p className="text-xs text-muted-foreground">
                              Can view and edit the document
                            </p>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Notification Options</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="notify-targets"
                      checked={notifyTargets}
                      onCheckedChange={setNotifyTargets}
                    />
                    <Label htmlFor="notify-targets">Notify recipients</Label>
                  </div>
                </div>
              </div>

              {/* Message */}
              <div className="space-y-2">
                <Label>Message (Optional)</Label>
                <Textarea
                  placeholder="Add a message to include with the share notification..."
                  value={shareMessage}
                  onChange={(e) => setShareMessage(e.target.value)}
                  rows={3}
                />
              </div>

              {/* Share Button */}
              <Button
                onClick={handleShare}
                disabled={selectedTargets.length === 0 || isLoading}
                className="w-full"
              >
                {isLoading
                  ? "Sharing..."
                  : `Share with ${selectedTargets.length} ${
                      selectedTargets.length === 1 ? "recipient" : "recipients"
                    }`}
              </Button>
            </div>
          </TabsContent>

          {/* Manage Access Tab */}
          <TabsContent value="manage" className="space-y-4">
            <div className="max-h-96 overflow-y-auto space-y-4">
              {/* Existing Shares */}
              {existingShares.length > 0 && (
                <div className="space-y-2">
                  <Label>Direct Shares ({existingShares.length})</Label>
                  <div className="space-y-2">
                    {existingShares.map((share) => (
                      <div
                        key={share.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
                            {getEntityIcon(share.entityType)}
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              {share.entityType === "user"
                                ? "User"
                                : share.entityType === "team"
                                ? "Team"
                                : "Organization"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {share.permissionLevel} access •{" "}
                              {share.accessCount} views
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {getPermissionIcon(share.permissionLevel)}
                            {share.permissionLevel}
                          </Badge>
                          {canManage && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem>
                                  <Settings className="h-4 w-4 mr-2" />
                                  Change Permission
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleRevokeShare(share.id)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Revoke Access
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {existingShares.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No existing shares found</p>
                  <p className="text-sm">
                    Use the other tabs to share this document
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
