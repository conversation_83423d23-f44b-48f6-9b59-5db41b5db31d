export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  status: DocumentStatus;
  userId: string;
  metadata?: DocumentMetadata;
  error?: string;
}

export interface DocumentMetadata {
  pageCount?: number;
  wordCount?: number;
  language?: string;
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  createdAt?: string;
  modifiedAt?: string;
}

export type DocumentStatus =
  | "uploading"
  | "parsing"
  | "indexing"
  | "processing"
  | "success"
  | "error";

export interface UploadProgress {
  documentId: string;
  progress: number;
  status: DocumentStatus;
  message?: string;
}

export interface ChatMessage {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: string;
  sources?: DocumentSource[];
  metadata?: ChatMessageMetadata;
}

export interface DocumentSource {
  documentId: string;
  documentName: string;
  pageNumber?: number;
  relevanceScore: number;
  excerpt: string;
}

export interface ChatMessageMetadata {
  model?: string;
  tokens?: number;
  processingTime?: number;
  retrievalResults?: number;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  selectedDocuments: string[];
  createdAt: string;
  updatedAt: string;
  userId: string;
}

export interface KnowledgeBaseStats {
  totalDocuments: number;
  totalSize: number;
  processingCount: number;
  errorCount: number;
  lastUpdated: string;
}

export interface SearchResult {
  documentId: string;
  documentName: string;
  content: string;
  relevanceScore: number;
  metadata: {
    pageNumber?: number;
    chunkIndex: number;
  };
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  selectedDocuments?: string[];
  systemMessage?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface ChatResponse {
  message: string;
  sources: DocumentSource[];
  sessionId: string;
  metadata: ChatMessageMetadata;
}

export interface UploadRequest {
  files: File[];
  metadata?: Partial<DocumentMetadata>;
}

export interface UploadResponse {
  documents: Document[];
  errors?: UploadError[];
}

export interface UploadError {
  fileName: string;
  error: string;
  code: string;
}

// Navigation types
export type NavigationTab = "knowledge" | "chat";

export interface NavigationState {
  activeTab: NavigationTab;
  selectedDocuments: string[];
  chatSessionId?: string;
}
