// Block types for rich document editing
export type BlockType = 
  | 'header' 
  | 'text' 
  | 'image' 
  | 'quote' 
  | 'list'
  | 'code'
  | 'table'
  | 'diagram';

export interface Block {
  id: string;
  type: BlockType;
  content: string;
  level?: number; // For headers (h1, h2, h3)
  url?: string;   // For images
  alt?: string;   // For images
  items?: string[]; // For lists
  language?: string; // For code blocks
  rows?: string[][];  // For tables
  columns?: string[]; // For tables
}

// SectionItem for templates
export interface SectionItem {
  id: string;
  title: string;
  content: string;
  order: number;
}

// Section status tracking
export type SectionStatus = 'pending' | 'generating' | 'completed' | 'failed';

export interface Section {
  id: string;
  title: string;
  content: string;
  blocks?: Block[];
  status: SectionStatus;
  order: number;
  updatedAt: Date;
  description?: string;
  error?: {
    message: string;
    details?: any;
    timestamp: string;
  };
}

// Document structure
export interface ScopingDocument {
  id: string;
  userId: string;
  name: string;
  description?: string;
  clientId?: string;
  scopeTemplateId?: string;
  promptTemplateId?: string;
  sectionTemplateId?: string;
  projectName?: string;
  clientName?: string;
  baseContent: {
    description: string;
    blocks?: Block[];
  };
  sections: Section[];
  createdAt: Date;
  updatedAt: Date;
}

// Client structure
export interface Client {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone?: string;
  company: string;
  industry: string;
  createdAt: Date;
  updatedAt: Date;
}

// Client information for forms and data sharing
export interface ClientInfo {
  id?: string;
  name: string;
  industry: string;
  company: string;
  contactPerson: string;
  email: string;
  phone?: string;
}

// Scope template structure
export interface ScopeTemplate {
  id: string;
  name: string;
  description: string;
  content: any; // Configuration specific to this template type
  sections: SectionItem[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Prompt template structure
export interface PromptTemplate {
  id: string;
  name: string;
  description?: string;
  content: string;
  variables?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Section template structure
export interface SectionTemplate {
  id: string;
  name: string;
  description?: string;
  sections: {
    title: string;
    description?: string;
    order: number;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

// Project scoping information
export interface ScopingInfo {
  projectName: string;
  projectDescription: string;
  projectType?: string;
  timeline: string;
  budget: string;
  goals?: string[];
  objectives?: string;
}

// Combined scoping document
export interface Scoping {
  id: string;
  clientName: string;
  projectName: string;
  description: string;
  clientInfo?: ClientInfo;
  scopingInfo?: ScopingInfo;
  sections: Section[];
  createdAt: Date;
  updatedAt: Date;
}

// Progressive Scoping Types
export interface ProgressiveScoping {
  id: string;
  projectName: string;
  clientInfo: ClientInfo;
  scopingInfo: ScopingInfo;
  sections: Section[];
  createdAt: Date;
  updatedAt: Date;
  status: 'draft' | 'in_progress' | 'completed';
}

// Form Data Types for the legacy workflow
export interface ProjectFormData {
  projectName: string;
  clientId: string;
  templateId: string;
  additionalInfo: string;
}

// Progressive Document Creation Data
export interface ProgressiveDocumentData {
  // Project information
  projectName: string;
  additionalInfo?: string;
  
  // Client information
  clientId: string;
  clientName?: string;
  clientInfo?: ClientInfo;
  
  // Template information
  promptTemplateId: string;
  scopeTemplateId: string;
  
  // Section information
  sections: SectionItem[];
} 