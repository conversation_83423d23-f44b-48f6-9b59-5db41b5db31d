/**
 * Team/Group Service
 * Handles all API calls related to teams/groups
 */

import { supabase } from "../supabase/supabaseClient";

export interface Team {
  id: string;
  name: string;
  description?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  organisation_id?: string;
  entities_ids?: string[];
}

export interface TeamMember {
  id: string;
  group_id: string;
  user_id: string;
  role: "admin" | "member";
  joined_at: string;
  full_name?: string;
  email?: string;
  avatar_url?: string;
}

export interface TeamInvitation {
  id: string;
  group_id: string;
  email: string;
  status: "pending" | "accepted" | "rejected";
  created_at: string;
  updated_at: string;
  team_name?: string;
  organization_name?: string;
}

export interface TeamWithDetails extends Team {
  role: string;
  member_count: number;
  organization_name?: string;
}

/**
 * Create a new team/group
 */
export const createTeam = async (
  name: string,
  description?: string,
  orgId?: string
): Promise<string | null> => {
  try {
    const { data, error } = await supabase.rpc("create_group", {
      group_name: name,
      group_description: description || null,
      org_id: orgId || null,
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error creating team:", error);
    throw error;
  }
};

/**
 * Get all teams for the current user
 */
export const getUserTeams = async (): Promise<TeamWithDetails[]> => {
  try {
    const { data, error } = await supabase.rpc("get_user_groups");

    if (error) throw error;

    // For each team, get member count and organization name
    const teamsWithDetails = await Promise.all(
      (data || []).map(async (team: any) => {
        // Get member count
        const { count: memberCount } = await supabase
          .from("group_members")
          .select("*", { count: "exact" })
          .eq("group_id", team.id);

        // Get organization name if team belongs to an organization
        let organizationName = null;
        if (team.organisation_id) {
          const { data: orgData } = await supabase
            .from("organisations")
            .select("name")
            .eq("id", team.organisation_id)
            .single();

          organizationName = orgData?.name;
        }

        return {
          ...team,
          member_count: memberCount || 0,
          organization_name: organizationName,
        };
      })
    );

    return teamsWithDetails;
  } catch (error) {
    console.error("Error fetching user teams:", error);
    throw error;
  }
};

/**
 * Get a single team by ID
 */
export const getTeam = async (id: string): Promise<Team | null> => {
  try {
    const { data, error } = await supabase
      .from("groups")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error fetching team with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get team members
 */
export const getTeamMembers = async (teamId: string): Promise<TeamMember[]> => {
  try {
    // First get the team members
    const { data: membersData, error: membersError } = await supabase
      .from("group_members")
      .select("*")
      .eq("group_id", teamId);

    if (membersError) throw membersError;

    if (!membersData || membersData.length === 0) {
      return [];
    }

    // Get user profiles for all members
    const userIds = membersData.map((member) => member.user_id);
    const { data: profilesData, error: profilesError } = await supabase
      .from("profiles")
      .select("id, full_name, email")
      .in("id", userIds);

    if (profilesError) throw profilesError;

    // Combine the data
    return membersData.map((member: any) => {
      const profile = profilesData?.find((p) => p.id === member.user_id);
      return {
        id: member.id,
        group_id: member.group_id,
        user_id: member.user_id,
        role: member.role,
        joined_at: member.joined_at,
        full_name: profile?.full_name,
        email: profile?.email,
        avatar_url: undefined, // Set to undefined since column doesn't exist
      };
    });
  } catch (error) {
    console.error(`Error fetching members for team ${teamId}:`, error);
    throw error;
  }
};

/**
 * Check if the current user is a team admin
 */
export const isTeamAdmin = async (teamId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc("is_group_admin", {
      group_id: teamId,
    });

    if (error) throw error;
    return !!data;
  } catch (error) {
    console.error(`Error checking admin status for team ${teamId}:`, error);
    throw error;
  }
};

/**
 * Invite a user to a team
 */
export const inviteToTeam = async (
  teamId: string,
  email: string
): Promise<TeamInvitation | null> => {
  try {
    // Check if user is already invited or is a member
    const { data: existingInvitation } = await supabase
      .from("group_invitations")
      .select("id")
      .eq("group_id", teamId)
      .eq("email", email)
      .eq("status", "pending")
      .maybeSingle();

    if (existingInvitation) {
      throw new Error("User is already invited to this team");
    }

    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from("group_members")
      .select("id")
      .eq("group_id", teamId)
      .eq(
        "user_id",
        (
          await supabase
            .from("profiles")
            .select("id")
            .eq("email", email)
            .maybeSingle()
        )?.data?.id
      )
      .maybeSingle();

    if (existingMember) {
      throw new Error("User is already a member of this team");
    }

    // Create the invitation
    const { data, error } = await supabase
      .from("group_invitations")
      .insert({
        group_id: teamId,
        email: email,
        status: "pending",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error inviting user to team:", error);
    throw error;
  }
};

/**
 * Accept a team invitation
 */
export const acceptTeamInvitation = async (
  invitationId: string
): Promise<boolean> => {
  try {
    // Get the invitation details
    const { data: invitation, error: inviteError } = await supabase
      .from("group_invitations")
      .select("*")
      .eq("id", invitationId)
      .eq("status", "pending")
      .single();

    if (inviteError || !invitation) {
      throw new Error("Invitation not found or already processed");
    }

    // Get the user ID from the email
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("id")
      .eq("email", invitation.email)
      .single();

    if (profileError || !profile) {
      throw new Error("User profile not found");
    }

    // Add user to the team
    const { error: memberError } = await supabase.from("group_members").insert({
      group_id: invitation.group_id,
      user_id: profile.id,
      role: "member",
      joined_at: new Date().toISOString(),
    });

    if (memberError) throw memberError;

    // Update invitation status
    const { error: updateError } = await supabase
      .from("group_invitations")
      .update({
        status: "accepted",
        updated_at: new Date().toISOString(),
      })
      .eq("id", invitationId);

    if (updateError) throw updateError;
    return true;
  } catch (error) {
    console.error(`Error accepting team invitation ${invitationId}:`, error);
    throw error;
  }
};

/**
 * Get all pending team invitations for the current user
 */
export const getUserPendingTeamInvitations = async (): Promise<
  TeamInvitation[]
> => {
  try {
    // First get the user's email
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user?.email) {
      throw new Error("User email not available");
    }

    // Get invitations for this email
    const { data, error } = await supabase
      .from("group_invitations")
      .select(
        `
        *,
        group:group_id (
          name,
          organisation:organisation_id (
            name
          )
        )
      `
      )
      .eq("email", user.email)
      .eq("status", "pending");

    if (error) throw error;

    // Format the response to include team and organization names
    return (data || []).map((invitation: any) => ({
      ...invitation,
      team_name: invitation.group?.name,
      organization_name: invitation.group?.organisation?.name,
    }));
  } catch (error) {
    console.error("Error fetching user team invitations:", error);
    throw error;
  }
};

/**
 * Get team invitations for a specific team
 */
export const getTeamInvitations = async (
  teamId: string
): Promise<TeamInvitation[]> => {
  try {
    const { data, error } = await supabase
      .from("group_invitations")
      .select("*")
      .eq("group_id", teamId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error(`Error fetching invitations for team ${teamId}:`, error);
    throw error;
  }
};

/**
 * Remove a user from a team
 */
export const removeFromTeam = async (
  teamId: string,
  userId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("group_members")
      .delete()
      .eq("group_id", teamId)
      .eq("user_id", userId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error removing user from team ${teamId}:`, error);
    throw error;
  }
};

/**
 * Update team details
 */
export const updateTeam = async (
  teamId: string,
  updates: Partial<Pick<Team, "name" | "description">>
): Promise<Team | null> => {
  try {
    const { data, error } = await supabase
      .from("groups")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", teamId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error updating team ${teamId}:`, error);
    throw error;
  }
};

/**
 * Delete a team
 */
export const deleteTeam = async (teamId: string): Promise<boolean> => {
  try {
    const { error } = await supabase.from("groups").delete().eq("id", teamId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting team ${teamId}:`, error);
    throw error;
  }
};

/**
 * Reject a team invitation
 */
export const rejectTeamInvitation = async (
  invitationId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("group_invitations")
      .update({ status: "rejected", updated_at: new Date().toISOString() })
      .eq("id", invitationId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error rejecting team invitation ${invitationId}:`, error);
    throw error;
  }
};

/**
 * Get teams that belong to a specific organization
 */
export const getOrganizationGroups = async (
  organizationId: string
): Promise<Team[]> => {
  try {
    const { data, error } = await supabase
      .from("groups")
      .select("*")
      .eq("organisation_id", organizationId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error(
      `Error fetching teams for organization ${organizationId}:`,
      error
    );
    throw error;
  }
};
