import React from "react";
import { formatDistanceToNow } from "date-fns";

import { But<PERSON> } from "../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Badge } from "../ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import { toast } from "../../hooks/use-toast";
import { OrganizationInvitation } from "../../types/organization";
import { GroupInvitation } from "../../types/team";
import { acceptOrganizationInvitation } from "../../services/organizationService";
import { acceptGroupInvitation } from "../../services/teamService";

interface InvitationsListProps {
  organizationInvitations?: OrganizationInvitation[];
  groupInvitations?: GroupInvitation[];
  onAcceptOrganization?: (invitationId: string) => void;
  onAcceptGroup?: (invitationId: string) => void;
  isLoading?: boolean;
}

export function InvitationsList({
  organizationInvitations = [],
  groupInvitations = [],
  onAcceptOrganization,
  onAcceptGroup,
  isLoading = false,
}: InvitationsListProps) {
  const [acceptingInvitation, setAcceptingInvitation] = React.useState<
    string | null
  >(null);

  const handleAcceptOrgInvitation = async (invitationId: string) => {
    setAcceptingInvitation(invitationId);
    try {
      const result = await acceptOrganizationInvitation(invitationId);
      if (result) {
        toast({
          title: "Invitation accepted",
          description: "You have joined the organization.",
        });

        if (onAcceptOrganization) {
          onAcceptOrganization(invitationId);
        }
      }
    } catch (error) {
      console.error("Error accepting organization invitation:", error);
      toast({
        title: "Error",
        description: "Failed to accept invitation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAcceptingInvitation(null);
    }
  };

  const handleAcceptGroupInvitation = async (invitationId: string) => {
    setAcceptingInvitation(invitationId);
    try {
      const result = await acceptGroupInvitation(invitationId);
      if (result) {
        toast({
          title: "Invitation accepted",
          description: "You have joined the team.",
        });

        if (onAcceptGroup) {
          onAcceptGroup(invitationId);
        }
      }
    } catch (error) {
      console.error("Error accepting group invitation:", error);
      toast({
        title: "Error",
        description: "Failed to accept invitation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAcceptingInvitation(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-6">
        <div className="animate-spin w-6 h-6 border-2 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (organizationInvitations.length === 0 && groupInvitations.length === 0) {
    return (
      <Card className="bg-muted/40">
        <CardContent className="pt-6 text-center text-muted-foreground">
          No pending invitations found.
        </CardContent>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="organizations" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="organizations">
          Organizations ({organizationInvitations.length})
        </TabsTrigger>
        <TabsTrigger value="teams">
          Teams ({groupInvitations.length})
        </TabsTrigger>
      </TabsList>

      <TabsContent value="organizations" className="mt-4 space-y-4">
        {organizationInvitations.length === 0 ? (
          <Card className="bg-muted/40">
            <CardContent className="pt-6 text-center text-muted-foreground">
              No pending organization invitations.
            </CardContent>
          </Card>
        ) : (
          organizationInvitations.map((invitation) => (
            <Card key={invitation.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">
                  {invitation.organization_name || "Organization"}
                </CardTitle>
                <CardDescription>
                  Invited{" "}
                  {formatDistanceToNow(new Date(invitation.created_at), {
                    addSuffix: true,
                  })}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">Role:</span>
                  <Badge variant="outline">{invitation.role}</Badge>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={() => handleAcceptOrgInvitation(invitation.id)}
                  disabled={acceptingInvitation === invitation.id}
                  className="mr-2"
                >
                  {acceptingInvitation === invitation.id
                    ? "Accepting..."
                    : "Accept"}
                </Button>
                <Button
                  variant="outline"
                  disabled={acceptingInvitation === invitation.id}
                >
                  Decline
                </Button>
              </CardFooter>
            </Card>
          ))
        )}
      </TabsContent>

      <TabsContent value="teams" className="mt-4 space-y-4">
        {groupInvitations.length === 0 ? (
          <Card className="bg-muted/40">
            <CardContent className="pt-6 text-center text-muted-foreground">
              No pending team invitations.
            </CardContent>
          </Card>
        ) : (
          groupInvitations.map((invitation) => (
            <Card key={invitation.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">
                  {invitation.group_name || "Team"}
                </CardTitle>
                <CardDescription>
                  Invited{" "}
                  {formatDistanceToNow(new Date(invitation.created_at), {
                    addSuffix: true,
                  })}
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  onClick={() => handleAcceptGroupInvitation(invitation.id)}
                  disabled={acceptingInvitation === invitation.id}
                  className="mr-2"
                >
                  {acceptingInvitation === invitation.id
                    ? "Accepting..."
                    : "Accept"}
                </Button>
                <Button
                  variant="outline"
                  disabled={acceptingInvitation === invitation.id}
                >
                  Decline
                </Button>
              </CardFooter>
            </Card>
          ))
        )}
      </TabsContent>
    </Tabs>
  );
}
