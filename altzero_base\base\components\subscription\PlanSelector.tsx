import React, { useState } from "react";
import { useSubscription } from "../../contextapi/SubscriptionContext";
import {
  PlanTier,
  BillingCycle,
  SubscriptionRecord,
} from "../../types/subscription";
import { Button } from "../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import { Check, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { toast } from "../../hooks/use-toast";

export interface PlanSelectorProps {
  subscription?: SubscriptionRecord | null;
  onPlanChange?: (plan: PlanTier) => void;
  className?: string;
}

export function PlanSelector({
  subscription,
  onPlanChange,
  className,
}: PlanSelectorProps) {
  const { availablePlans, isLoading, isChangingPlan, changePlan } =
    useSubscription();

  const [selectedPlan, setSelectedPlan] = useState<string | null>(
    subscription?.plan_id || null
  );
  const [billingCycle, setBillingCycle] = useState<BillingCycle>(
    subscription?.billing_cycle || BillingCycle.MONTHLY
  );
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleBillingCycleChange = (cycle: BillingCycle) => {
    setBillingCycle(cycle);
  };

  const handlePlanChange = async () => {
    if (!selectedPlan || !subscription) return;

    try {
      await changePlan(subscription.id, selectedPlan);
      toast({
        title: "Plan changed successfully",
        description: "Your subscription has been updated",
      });
      setConfirmDialogOpen(false);
      if (onPlanChange) {
        const newPlan = availablePlans.find((p) => p.id === selectedPlan);
        if (newPlan) {
          onPlanChange(newPlan);
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to change plan. Please try again.",
        variant: "destructive",
      });
    }
  };

  const isCurrentPlan = (planId: string) => {
    return subscription?.plan_id === planId;
  };

  const getPlanPrice = (plan: PlanTier) => {
    return billingCycle === BillingCycle.MONTHLY
      ? plan.monthly_price_id
      : plan.yearly_price_id;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-6">
        <div className="animate-spin rounded-full h-12 w-12 border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold tracking-tight">
          Subscription Plans
        </h2>
        <p className="text-muted-foreground">
          Choose the plan that best suits your needs
        </p>
      </div>

      <div className="flex justify-center mb-6">
        <RadioGroup
          className="flex bg-muted rounded-full p-1"
          defaultValue={billingCycle}
          onValueChange={(value) =>
            handleBillingCycleChange(value as BillingCycle)
          }
        >
          <div className="flex space-x-1">
            <Label
              className={`
                cursor-pointer px-4 py-2 rounded-full text-sm font-medium 
                ${
                  billingCycle === BillingCycle.MONTHLY
                    ? "bg-card shadow-sm"
                    : "text-muted-foreground"
                }
              `}
              htmlFor="monthly"
            >
              Monthly
              <RadioGroupItem
                id="monthly"
                value={BillingCycle.MONTHLY}
                className="sr-only"
              />
            </Label>
            <Label
              className={`
                cursor-pointer px-4 py-2 rounded-full text-sm font-medium 
                ${
                  billingCycle === BillingCycle.YEARLY
                    ? "bg-card shadow-sm"
                    : "text-muted-foreground"
                }
              `}
              htmlFor="yearly"
            >
              Yearly
              <Badge className="ml-2 bg-primary text-primary-foreground">
                Save 15%
              </Badge>
              <RadioGroupItem
                id="yearly"
                value={BillingCycle.YEARLY}
                className="sr-only"
              />
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availablePlans.map((plan) => (
          <Card
            key={plan.id}
            className={`
              relative overflow-hidden
              ${selectedPlan === plan.id ? "border-primary shadow-md" : ""}
              ${isCurrentPlan(plan.id) ? "bg-muted/50" : ""}
            `}
          >
            {isCurrentPlan(plan.id) && (
              <div className="absolute top-0 right-0 p-2">
                <Badge variant="secondary">Current Plan</Badge>
              </div>
            )}
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className="mt-2">
                <span className="text-3xl font-bold">
                  ${getPlanPrice(plan)}
                </span>
                <span className="text-muted-foreground">
                  /{billingCycle === BillingCycle.MONTHLY ? "month" : "year"}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {Object.entries(plan.features).map(([key, value], index) => (
                  <li key={index} className="flex items-start gap-2">
                    {value ? (
                      <Check className="text-green-500 h-5 w-5 mt-0.5 flex-shrink-0" />
                    ) : (
                      <X className="text-gray-300 h-5 w-5 mt-0.5 flex-shrink-0" />
                    )}
                    <span
                      className={
                        value ? "text-foreground" : "text-muted-foreground"
                      }
                    >
                      {key}
                    </span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {isCurrentPlan(plan.id) ? (
                <Button variant="outline" className="w-full" disabled>
                  Current Plan
                </Button>
              ) : (
                <Dialog
                  open={confirmDialogOpen && selectedPlan === plan.id}
                  onOpenChange={setConfirmDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      className="w-full"
                      onClick={() => {
                        handlePlanSelect(plan.id);
                        setConfirmDialogOpen(true);
                      }}
                    >
                      Select Plan
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Change to {plan.name} Plan</DialogTitle>
                      <DialogDescription>
                        You're about to change your subscription plan. This will
                        take effect immediately.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setConfirmDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handlePlanChange}
                        disabled={isChangingPlan}
                      >
                        {isChangingPlan ? (
                          <>
                            <span className="animate-spin mr-2">⟳</span>
                            Changing...
                          </>
                        ) : (
                          "Confirm Change"
                        )}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
