import { BaseExternalService, ExternalServiceResult, ExternalServiceConfig, SEOMetrics, SEOIssue } from './BaseExternalService';

export class SemrushService extends BaseExternalService {
  private readonly API_URL = 'https://api.semrush.com/analytics/v1/';

  constructor(config: ExternalServiceConfig) {
    super('Semrush', 'premium', config);
  }

  isConfigured(): boolean {
    return !!this.config.apiKey;
  }

  async testConnection(): Promise<boolean> {
    try {
      const params = new URLSearchParams({
        type: 'domain_organic',
        key: this.config.apiKey!,
        domain: 'example.com',
        database: 'us',
        display_limit: '1'
      });

      const response = await fetch(`${this.API_URL}?${params}`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async analyze(url: string): Promise<ExternalServiceResult> {
    if (!this.isEnabled()) {
      return this.createErrorResult('Semrush service is not enabled or configured');
    }

    try {
      await this.handleRateLimit();

      const domain = new URL(url).hostname;
      
      const [domainOverview, backlinks, keywords] = await Promise.all([
        this.getDomainOverview(domain),
        this.getBacklinks(domain),
        this.getKeywords(domain)
      ]);

      return this.parseResponse({ domainOverview, backlinks, keywords });

    } catch (error) {
      return this.createErrorResult(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  protected getFreeTierLimit(): number {
    return 0; // No free tier
  }

  protected getPremiumCost(): number {
    return 0.005; // $0.005 per request (example)
  }

  private async getDomainOverview(domain: string): Promise<any> {
    const params = new URLSearchParams({
      type: 'domain_organic',
      key: this.config.apiKey!,
      domain: domain,
      database: 'us',
      display_limit: '100'
    });

    const response = await fetch(`${this.API_URL}?${params}`, {
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      throw new Error(`Semrush domain overview failed: ${response.statusText}`);
    }

    return this.parseCSVResponse(await response.text());
  }

  private async getBacklinks(domain: string): Promise<any> {
    const params = new URLSearchParams({
      type: 'backlinks_overview',
      key: this.config.apiKey!,
      target: domain,
      target_type: 'domain'
    });

    const response = await fetch(`${this.API_URL}?${params}`, {
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      throw new Error(`Semrush backlinks failed: ${response.statusText}`);
    }

    return this.parseCSVResponse(await response.text());
  }

  private async getKeywords(domain: string): Promise<any> {
    const params = new URLSearchParams({
      type: 'domain_organic',
      key: this.config.apiKey!,
      domain: domain,
      database: 'us',
      display_limit: '50'
    });

    const response = await fetch(`${this.API_URL}?${params}`, {
      signal: AbortSignal.timeout(this.config.timeout || 30000)
    });

    if (!response.ok) {
      throw new Error(`Semrush keywords failed: ${response.statusText}`);
    }

    return this.parseCSVResponse(await response.text());
  }

  private parseCSVResponse(csvText: string): any[] {
    const lines = csvText.trim().split('\n');
    if (lines.length < 2) return [];
    
    const headers = lines[0].split(';');
    const data = lines.slice(1).map(line => {
      const values = line.split(';');
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = values[index] || '';
      });
      return obj;
    });

    return data;
  }

  private parseResponse(data: any): ExternalServiceResult {
    const { domainOverview, backlinks, keywords } = data;

    // Calculate metrics based on Semrush data
    const organicKeywords = domainOverview.length;
    const totalBacklinks = backlinks.length > 0 ? parseInt(backlinks[0]['Backlinks'] || '0') : 0;
    const avgPosition = keywords.length > 0 
      ? keywords.reduce((sum: number, k: any) => sum + parseFloat(k.Position || 0), 0) / keywords.length 
      : 0;

    const metrics: SEOMetrics = {
      overall: this.calculateOverallScore(organicKeywords, totalBacklinks, avgPosition),
      technical: 75, // Based on domain authority factors
      content: this.calculateContentScore(keywords),
      performance: 70, // Default as Semrush doesn't provide performance metrics
      accessibility: 65, // Default
      seo: this.calculateSEOScore(organicKeywords, totalBacklinks),
      details: data
    };

    const issues: SEOIssue[] = this.generateIssues(organicKeywords, totalBacklinks, avgPosition);

    return {
      provider: this.name,
      metrics,
      issues,
      rawData: data,
      timestamp: new Date().toISOString(),
      success: true
    };
  }

  private calculateOverallScore(keywords: number, backlinks: number, avgPosition: number): number {
    let score = 50; // Base score
    
    // Keyword bonus
    if (keywords > 1000) score += 20;
    else if (keywords > 500) score += 15;
    else if (keywords > 100) score += 10;
    
    // Backlink bonus
    if (backlinks > 10000) score += 20;
    else if (backlinks > 1000) score += 15;
    else if (backlinks > 100) score += 10;
    
    // Position penalty
    if (avgPosition > 50) score -= 10;
    else if (avgPosition > 20) score -= 5;
    
    return Math.min(100, Math.max(0, score));
  }

  private calculateContentScore(keywords: any[]): number {
    if (keywords.length === 0) return 30;
    
    const topPositions = keywords.filter(k => parseFloat(k.Position || 100) <= 10).length;
    const percentage = (topPositions / keywords.length) * 100;
    
    return Math.min(100, 40 + percentage);
  }

  private calculateSEOScore(keywords: number, backlinks: number): number {
    let score = 30; // Base SEO score
    
    if (keywords > 500) score += 30;
    else if (keywords > 100) score += 20;
    else if (keywords > 50) score += 15;
    
    if (backlinks > 1000) score += 40;
    else if (backlinks > 100) score += 25;
    else if (backlinks > 10) score += 15;
    
    return Math.min(100, score);
  }

  private generateIssues(keywords: number, backlinks: number, avgPosition: number): SEOIssue[] {
    const issues: SEOIssue[] = [];

    if (keywords < 50) {
      issues.push({
        category: 'Keyword Ranking',
        severity: 'critical',
        title: 'Low organic keyword count',
        description: `Only ${keywords} keywords ranking. Target websites typically have 500+ ranking keywords.`,
        recommendation: 'Expand content strategy and target more relevant keywords',
        impact: 'high'
      });
    }

    if (backlinks < 100) {
      issues.push({
        category: 'Link Building',
        severity: 'warning',
        title: 'Low backlink count',
        description: `Only ${backlinks} backlinks detected. Strong sites typically have 1000+ quality backlinks.`,
        recommendation: 'Implement a comprehensive link building strategy',
        impact: 'high'
      });
    }

    if (avgPosition > 30) {
      issues.push({
        category: 'Keyword Performance',
        severity: 'warning',
        title: 'Poor average keyword position',
        description: `Average keyword position is ${avgPosition.toFixed(1)}. Target top 20 positions.`,
        recommendation: 'Optimize content for better keyword rankings',
        impact: 'medium'
      });
    }

    return issues;
  }

  getRateLimit(): number {
    return 600; // 600 requests per minute for premium
  }
} 