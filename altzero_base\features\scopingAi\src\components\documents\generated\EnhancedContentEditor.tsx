"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "../../../../../base/components/ui/button";
import { Card, CardContent } from "../../../../../base/components/ui/card";
import { Badge } from "../../../../../base/components/ui/badge";
import { Input } from "../../../../../base/components/ui/input";
import { Textarea } from "../../../../../base/components/ui/textarea";
import {
  Plus,
  Trash2,
  Edit3,
  Save,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  Image as ImageIcon,
  GripVertical,
  FileText,
  Upload,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { useToast } from "../../../../../base/hooks/use-toast";
import { useDocumentContext } from "../../../contexts/DocumentContext";
import {
  convertContentToBlocks,
  renderBlocksToHTML,
} from "../../../utils/documentUtils";

interface EnhancedContentEditorProps {
  sections: any[];
  pages: any[];
  currentPage: number;
  onPageChange: (page: number) => void;
  onSectionUpdate: (sectionId: string, updates: any) => void;
  readonly?: boolean;
}

// Draggable Block Component
function DraggableBlock({
  block,
  sectionId,
  onUpdate,
  onDelete,
  onStartEdit,
  onStopEdit,
  isEditing,
  readonly = false,
}: {
  block: any;
  sectionId: string;
  onUpdate: (blockId: string, updates: any) => void;
  onDelete: (blockId: string) => void;
  onStartEdit: (blockId: string) => void;
  onStopEdit: () => void;
  isEditing: boolean;
  readonly?: boolean;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: block.id });

  const { toast } = useToast();
  const { documentTheme, handleUpdateBlock } = useDocumentContext();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height =
        Math.max(120, textareaRef.current.scrollHeight) + "px";
    }
  };

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      adjustTextareaHeight();
    }
  }, [isEditing]);

  // Handle save with proper context save
  const handleSave = () => {
    // Update using the onUpdate callback which properly saves
    onUpdate(block.id, block);
    onStopEdit();
    toast({
      title: "Content Saved",
      description: "Your changes have been saved successfully.",
      duration: 2000,
    });
  };

  // Handle image upload
  const handleImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageData = event.target?.result as string;
        const updatedBlock = {
          ...block,
          type: "image",
          content: imageData,
          caption: file.name,
        };
        onUpdate(block.id, updatedBlock);
        toast({
          title: "Image Added",
          description: "Image has been uploaded successfully.",
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const renderEditingMode = () => (
    <div className="border-2 border-blue-500 rounded-lg p-4 bg-blue-50">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className="text-xs bg-blue-100 text-blue-800"
          >
            {block.type === "header" ? `H${block.level || 2}` : block.type}
          </Badge>
          <span className="text-sm text-blue-700 font-medium">Editing...</span>
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            onClick={handleSave}
            className="h-7 px-3 bg-green-600 hover:bg-green-700 text-white"
          >
            <CheckCircle className="h-3 w-3 mr-1" />
            Save
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onDelete(block.id)}
            className="h-7 px-2 text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {block.type === "header" && (
        <div className="mb-3">
          <select
            value={block.level || 2}
            onChange={(e) =>
              onUpdate(block.id, { ...block, level: parseInt(e.target.value) })
            }
            className="px-3 py-1 border rounded-md text-sm bg-white"
          >
            <option value={1}>Heading 1 (Large)</option>
            <option value={2}>Heading 2 (Medium)</option>
            <option value={3}>Heading 3 (Small)</option>
          </select>
        </div>
      )}

      {block.type === "image" ? (
        <div className="space-y-3">
          {block.content ? (
            <div className="relative">
              <img
                src={block.content}
                alt={block.caption || "Uploaded image"}
                className="max-w-full h-auto rounded-lg border"
                style={{ maxHeight: "300px" }}
              />
              <Button
                size="sm"
                variant="outline"
                onClick={() =>
                  onUpdate(block.id, { ...block, content: "", caption: "" })
                }
                className="absolute top-2 right-2 bg-white"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-gray-500 mb-2">Click to upload an image</p>
              <Button size="sm" onClick={handleImageUpload}>
                Choose Image
              </Button>
            </div>
          )}
          <Input
            placeholder="Image caption (optional)"
            value={block.caption || ""}
            onChange={(e) =>
              onUpdate(block.id, { ...block, caption: e.target.value })
            }
            className="text-sm"
          />
        </div>
      ) : (
        <Textarea
          ref={textareaRef}
          value={block.content || ""}
          onChange={(e) => {
            const updatedBlock = { ...block, content: e.target.value };
            onUpdate(block.id, updatedBlock);
            adjustTextareaHeight();
          }}
          placeholder={`Enter ${block.type} content...`}
          className="w-full p-3 border-0 rounded-md resize-none min-h-[120px] font-medium bg-white shadow-sm focus:ring-2 focus:ring-blue-500"
          style={{
            fontSize:
              block.type === "header"
                ? block.level === 1
                  ? "1.75rem"
                  : block.level === 2
                    ? "1.5rem"
                    : "1.25rem"
                : "1rem",
            fontWeight: block.type === "header" ? "600" : "400",
            fontFamily: documentTheme?.fontFamily || "Inter",
          }}
        />
      )}
    </div>
  );

  const renderPreviewMode = () => (
    <div
      className="group relative hover:bg-gray-50 transition-colors rounded-lg p-3 cursor-pointer border border-transparent hover:border-gray-200"
      onClick={() => !readonly && onStartEdit(block.id)}
    >
      {!readonly && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onStartEdit(block.id);
              }}
              className="h-6 w-6 p-0 bg-white shadow-sm"
            >
              <Edit3 className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(block.id);
              }}
              className="h-6 w-6 p-0 bg-white shadow-sm text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Drag Handle */}
      {!readonly && (
        <div
          {...attributes}
          {...listeners}
          className="absolute left-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>
      )}

      <div className="ml-6">
        {block.type === "image" && block.content ? (
          <div className="my-2">
            <img
              src={block.content}
              alt={block.caption || "Image"}
              className="max-w-full h-auto rounded-lg shadow-sm"
              style={{ maxHeight: "300px" }}
            />
            {block.caption && (
              <p className="text-sm text-gray-600 mt-2 italic">
                {block.caption}
              </p>
            )}
          </div>
        ) : (
          <div
            className="prose prose-lg max-w-none"
            style={{
              fontFamily: documentTheme?.fontFamily || "Inter",
              color: documentTheme?.textColor,
            }}
            dangerouslySetInnerHTML={{
              __html: renderBlocksToHTML([block]),
            }}
          />
        )}
      </div>
    </div>
  );

  return (
    <div ref={setNodeRef} style={style} className="mb-3">
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageChange}
        className="hidden"
      />
      {isEditing ? renderEditingMode() : renderPreviewMode()}
    </div>
  );
}

export const EnhancedContentEditor: React.FC<EnhancedContentEditorProps> = ({
  sections,
  pages,
  currentPage,
  onPageChange,
  onSectionUpdate,
  readonly = false,
}) => {
  const {
    documentTheme,
    handleUpdateBlock,
    handleAddBlock,
    handleDeleteBlock,
  } = useDocumentContext();

  const { toast } = useToast();
  const [editingBlock, setEditingBlock] = useState<string | null>(null);
  const [activeBlockId, setActiveBlockId] = useState<string | null>(null);
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(
    null
  );

  // Use sections directly instead of page-based content
  const allSections = sections || [];
  const selectedSection = selectedSectionId
    ? allSections.find((s) => s.id === selectedSectionId)
    : allSections[0];

  // Set initial selected section
  useEffect(() => {
    if (!selectedSectionId && allSections.length > 0) {
      setSelectedSectionId(allSections[0].id);
    }
  }, [allSections, selectedSectionId]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleUpdateBlockContent = (
    sectionId: string,
    blockId: string,
    updates: any
  ) => {
    const section = allSections.find((s: any) => s.id === sectionId);
    if (section) {
      // Initialize blocks if they don't exist
      if (!section.blocks) {
        section.blocks = convertContentToBlocks(section.content || "");
      }

      const updatedBlocks = section.blocks.map((block: any) =>
        block.id === blockId ? { ...block, ...updates } : block
      );

      // Update the section and save it
      const updatedSection = { ...section, blocks: updatedBlocks };
      onSectionUpdate(sectionId, updatedSection);
    }
  };

  const handleDeleteBlockContent = (sectionId: string, blockId: string) => {
    const section = allSections.find((s: any) => s.id === sectionId);
    if (section) {
      // Initialize blocks if they don't exist
      if (!section.blocks) {
        section.blocks = convertContentToBlocks(section.content || "");
      }

      const updatedBlocks = section.blocks.filter(
        (block: any) => block.id !== blockId
      );
      const updatedSection = { ...section, blocks: updatedBlocks };
      onSectionUpdate(sectionId, updatedSection);

      if (editingBlock === blockId) {
        setEditingBlock(null);
      }

      toast({
        title: "Block Deleted",
        description: "The content block has been removed.",
      });
    }
  };

  const addNewBlock = (
    sectionId: string,
    type: "text" | "header" | "list" | "image"
  ) => {
    const section = allSections.find((s: any) => s.id === sectionId);
    if (section) {
      // Initialize blocks if they don't exist
      if (!section.blocks) {
        section.blocks = convertContentToBlocks(section.content || "");
      }

      const newBlock = {
        id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
        type,
        content: type === "image" ? "" : `New ${type} content...`,
        ...(type === "header" && { level: 2 }),
        ...(type === "list" && { items: ["New list item"] }),
      };

      const updatedBlocks = [...section.blocks, newBlock];
      const updatedSection = { ...section, blocks: updatedBlocks };
      onSectionUpdate(sectionId, updatedSection);

      // Start editing the new block
      setEditingBlock(newBlock.id);

      toast({
        title: "Block Added",
        description: `New ${type} block has been added.`,
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent, sectionId: string) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const section = allSections.find((s: any) => s.id === sectionId);
      if (section) {
        // Initialize blocks if they don't exist
        if (!section.blocks) {
          section.blocks = convertContentToBlocks(section.content || "");
        }

        const oldIndex = section.blocks.findIndex(
          (block: any) => block.id === active.id
        );
        const newIndex = section.blocks.findIndex(
          (block: any) => block.id === over?.id
        );

        const updatedBlocks = arrayMove(section.blocks, oldIndex, newIndex);
        const updatedSection = { ...section, blocks: updatedBlocks };
        onSectionUpdate(sectionId, updatedSection);
      }
    }

    setActiveBlockId(null);
  };

  // Initialize sections with blocks if they don't exist
  useEffect(() => {
    allSections.forEach((section: any) => {
      if (!section.blocks && section.content) {
        section.blocks = convertContentToBlocks(section.content);
      }
    });
  }, [allSections]);

  return (
    <div className="space-y-6">
      {/* Header */}

      {/* Section Navigation */}
      <Card className="border shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-gray-600" />
              <span className="font-semibold text-gray-900">
                Select Section to Edit:
              </span>
            </div>

            <div className="flex items-center gap-2 flex-wrap">
              {allSections.map((section: any, index: number) => (
                <Button
                  key={section.id}
                  size="sm"
                  variant={
                    section.id === selectedSectionId ? "default" : "outline"
                  }
                  onClick={() => setSelectedSectionId(section.id)}
                  className="max-w-[200px] truncate"
                >
                  {index + 1}. {section.title}
                </Button>
              ))}
            </div>

            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {allSections.length} sections
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Selected Section Content */}
      {selectedSection ? (
        <Card className="border shadow-lg">
          <CardContent className="p-6">
            {/* Section Header */}
            <div className="flex items-center justify-between mb-6 pb-4 border-b">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {allSections.findIndex((s) => s.id === selectedSection.id) +
                    1}
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {selectedSection.title}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {selectedSection.blocks?.length || 0} content blocks
                  </p>
                </div>
              </div>

              {!readonly && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addNewBlock(selectedSection.id, "text")}
                    className="gap-2"
                  >
                    <Type className="h-4 w-4" />
                    Text
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addNewBlock(selectedSection.id, "header")}
                    className="gap-2"
                  >
                    <Heading2 className="h-4 w-4" />
                    Heading
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addNewBlock(selectedSection.id, "list")}
                    className="gap-2"
                  >
                    <List className="h-4 w-4" />
                    List
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => addNewBlock(selectedSection.id, "image")}
                    className="gap-2"
                  >
                    <ImageIcon className="h-4 w-4" />
                    Image
                  </Button>
                </div>
              )}
            </div>

            {/* Section Content with Drag & Drop */}
            <div className="min-h-[200px]">
              {selectedSection.blocks && selectedSection.blocks.length > 0 ? (
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragStart={(event) =>
                    setActiveBlockId(event.active.id as string)
                  }
                  onDragEnd={(event) =>
                    handleDragEnd(event, selectedSection.id)
                  }
                >
                  <SortableContext
                    items={selectedSection.blocks.map((b: any) => b.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-2">
                      {selectedSection.blocks.map((block: any) => (
                        <DraggableBlock
                          key={block.id}
                          block={block}
                          sectionId={selectedSection.id}
                          onUpdate={(blockId, updates) =>
                            handleUpdateBlockContent(
                              selectedSection.id,
                              blockId,
                              updates
                            )
                          }
                          onDelete={(blockId) =>
                            handleDeleteBlockContent(
                              selectedSection.id,
                              blockId
                            )
                          }
                          onStartEdit={setEditingBlock}
                          onStopEdit={() => setEditingBlock(null)}
                          isEditing={editingBlock === block.id}
                          readonly={readonly}
                        />
                      ))}
                    </div>
                  </SortableContext>

                  <DragOverlay>
                    {activeBlockId ? (
                      <div className="opacity-75 transform rotate-3 shadow-2xl">
                        <DraggableBlock
                          block={selectedSection.blocks.find(
                            (b: any) => b.id === activeBlockId
                          )}
                          sectionId={selectedSection.id}
                          onUpdate={() => {}}
                          onDelete={() => {}}
                          onStartEdit={() => {}}
                          onStopEdit={() => {}}
                          isEditing={false}
                          readonly={true}
                        />
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              ) : selectedSection.content ? (
                // If no blocks but has content, create blocks from content
                <div className="p-4 bg-gray-50 rounded-lg border">
                  <p className="text-sm text-gray-600 mb-3">
                    Converting existing content to editable blocks...
                  </p>
                  <Button
                    size="sm"
                    onClick={() => {
                      const blocks = convertContentToBlocks(
                        selectedSection.content
                      );
                      const updatedSection = { ...selectedSection, blocks };
                      onSectionUpdate(selectedSection.id, updatedSection);
                    }}
                    className="gap-2"
                  >
                    <Edit3 className="h-4 w-4" />
                    Convert to Blocks
                  </Button>
                  <div className="mt-3 p-3 bg-white rounded border text-sm">
                    {selectedSection.content.substring(0, 200)}
                    {selectedSection.content.length > 200 && "..."}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                  <div className="flex flex-col items-center gap-4">
                    <div className="p-4 bg-gray-200 rounded-full">
                      <Type className="h-8 w-8 text-gray-400" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-700 mb-2">
                        No content in this section yet
                      </h4>
                      <p className="text-gray-500 mb-4">
                        Add your first content block to get started
                      </p>
                    </div>
                    {!readonly && (
                      <div className="flex gap-3">
                        <Button
                          size="sm"
                          onClick={() =>
                            addNewBlock(selectedSection.id, "text")
                          }
                          className="gap-2 bg-blue-600 hover:bg-blue-700"
                        >
                          <Type className="h-4 w-4" />
                          Add Text
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            addNewBlock(selectedSection.id, "header")
                          }
                          className="gap-2"
                        >
                          <Heading2 className="h-4 w-4" />
                          Add Heading
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            addNewBlock(selectedSection.id, "image")
                          }
                          className="gap-2"
                        >
                          <ImageIcon className="h-4 w-4" />
                          Add Image
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-2 border-dashed border-gray-300">
          <CardContent className="p-12 text-center">
            <AlertCircle className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No Sections Available
            </h3>
            <p className="text-gray-500 mb-4">
              No sections found in this document. Please check if the document
              has loaded properly.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedContentEditor;
