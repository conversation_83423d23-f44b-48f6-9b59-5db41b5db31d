{"version": 3, "sources": ["../../@radix-ui/react-use-callback-ref/src/use-callback-ref.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n"], "mappings": ";;;;;;;;AAAA,YAAuB;AAMvB,SAAS,eAAkD,UAA4B;AACrF,QAAM,cAAoB,aAAO,QAAQ;AAEnC,EAAA,gBAAU,MAAM;AACpB,gBAAY,UAAU;EACxB,CAAC;AAGD,SAAa,cAAQ,MAAO,IAAI,SAAA;;AAAS,6BAAY,YAAZ,qCAAsB,GAAG;KAAa,CAAC,CAAC;AACnF;", "names": []}