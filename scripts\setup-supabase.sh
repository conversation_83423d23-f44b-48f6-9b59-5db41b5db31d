#!/bin/bash

# Setup script for Supabase resources
echo "Setting up Supabase resources for proposal templates..."

# Apply migrations
echo "Applying database migrations..."
npx supabase migration up

# Create storage bucket for proposal templates
echo "Creating proposaltemplates storage bucket..."
npx supabase storage create-bucket proposaltemplates --public

# Set RLS policies for storage
echo "Setting up Row Level Security for storage bucket..."
npx supabase storage policy create proposaltemplates \
  --name "Allow users to select their own files" \
  --operation SELECT \
  --expression "auth.uid() = owner"

npx supabase storage policy create proposaltemplates \
  --name "Allow users to insert their own files" \
  --operation INSERT \
  --expression "auth.uid() = owner"

npx supabase storage policy create proposaltemplates \
  --name "Allow users to update their own files" \
  --operation UPDATE \
  --expression "auth.uid() = owner"

npx supabase storage policy create proposaltemplates \
  --name "Allow users to delete their own files" \
  --operation DELETE \
  --expression "auth.uid() = owner"

echo "Supabase setup complete!" 