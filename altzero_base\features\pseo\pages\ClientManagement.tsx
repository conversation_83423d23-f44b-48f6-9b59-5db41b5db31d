import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/pseo/databaseService';
import { useUser } from '../../../base/contextapi/UserContext';
import type { PSEOClient } from '../types';

const ClientManagement: React.FC = () => {
  const { user } = useUser();
  const [clients, setClients] = useState<PSEOClient[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingClient, setEditingClient] = useState<PSEOClient | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: ''
  });

  useEffect(() => {
    if (user?.id) {
      loadClients();
    }
  }, [user?.id]);

  const loadClients = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const clientsData = await databaseService.getClientsByUserId(user.id);
      setClients(clientsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.id) return;

    if (!formData.name || !formData.email) {
      setError('Name and email are required');
      return;
    }

    setLoading(true);
    try {
      if (editingClient) {
        // Update existing client
        await databaseService.updateClient(editingClient.id, {
          name: formData.name,
          email: formData.email,
          company: formData.company || undefined,
        });
      } else {
        // Create new client
        await databaseService.createClient({
          user_id: user.id,
          name: formData.name,
          email: formData.email,
          company: formData.company || undefined,
        });
      }

      await loadClients();
      resetForm();
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save client');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (client: PSEOClient) => {
    setEditingClient(client);
    setFormData({
      name: client.name,
      email: client.email || '',
      company: client.company || ''
    });
    setShowCreateForm(true);
  };

  const handleDelete = async (clientId: string) => {
    if (!confirm('Are you sure you want to delete this client? This will also delete all associated websites and audits.')) {
      return;
    }

    setLoading(true);
    try {
      await databaseService.deleteClient(clientId);
      await loadClients();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete client');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ name: '', email: '', company: '' });
    setEditingClient(null);
    setShowCreateForm(false);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Client Management
          </h1>
          <p className="text-muted-foreground">
            Manage your pSEO clients
          </p>
        </div>

        {/* Actions */}
        <div className="mb-6">
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
          >
            Add New Client
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Client Form */}
          {showCreateForm && (
            <div className="lg:col-span-1">
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold mb-4">
                  {editingClient ? 'Edit Client' : 'Create New Client'}
                </h2>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Client Name *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter client name"
                      className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="Enter email address"
                      className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Company
                    </label>
                    <input
                      type="text"
                      value={formData.company}
                      onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                      placeholder="Enter company name"
                      className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>

                  <div className="flex gap-3">
                    <button
                      type="button"
                      onClick={resetForm}
                      className="flex-1 px-4 py-2 border border-border rounded-md hover:bg-accent"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 disabled:opacity-50"
                    >
                      {loading ? 'Saving...' : editingClient ? 'Update' : 'Create'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Clients List */}
          <div className={showCreateForm ? 'lg:col-span-2' : 'lg:col-span-3'}>
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold mb-4">Your Clients</h2>
              
              {loading && !showCreateForm ? (
                <div className="text-center py-8">
                  <div className="text-muted-foreground">Loading clients...</div>
                </div>
              ) : clients.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-muted-foreground">
                    No clients found. Create your first client to get started.
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {clients.map((client) => (
                    <div key={client.id} className="border border-border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-medium text-foreground">{client.name}</h3>
                          <p className="text-sm text-muted-foreground">{client.email}</p>
                          {client.company && (
                            <p className="text-sm text-muted-foreground">{client.company}</p>
                          )}
                          <p className="text-xs text-muted-foreground mt-1">
                            Created: {new Date(client.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleEdit(client)}
                            className="text-sm px-3 py-1 border border-border rounded hover:bg-accent"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDelete(client.id)}
                            className="text-sm px-3 py-1 border border-destructive text-destructive rounded hover:bg-destructive/10"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientManagement; 