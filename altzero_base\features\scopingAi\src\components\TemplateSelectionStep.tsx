import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@base/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@base/components/ui/card";
import { FileText, Info, Check } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@base/components/ui/tooltip";
import { useDocumentForm } from "../contexts/DocumentFormContext";
import { StepProps, TemplateSection } from "@/types/document-form";

// Define templates
const templates: TemplateSection[] = [
  {
    id: "standard",
    name: "Standard Scope",
    description: "Comprehensive scope document for most projects",
    sections: [
      "Project Overview",
      "Objectives",
      "Deliverables",
      "Timeline",
      "Budget",
      "Resources",
    ],
  },
  {
    id: "agile",
    name: "Agile Project",
    description: "Focused on sprints and iterative development",
    sections: [
      "Project Vision",
      "User Stories",
      "Sprint Planning",
      "Acceptance Criteria",
      "Team Structure",
    ],
  },
  {
    id: "minimal",
    name: "Minimal Scope",
    description: "Simplified scope for smaller projects",
    sections: ["Overview", "Deliverables", "Timeline", "Budget"],
  },
  {
    id: "custom",
    name: "Custom Template",
    description: "Build your own template from scratch",
    sections: ["Custom sections"],
  },
];

const TemplateSelectionStep: React.FC<StepProps> = ({ onNext, onBack }) => {
  const { selectedTemplate, referenceDocument, handleTemplateSelect } =
    useDocumentForm();

  // Update the description of the custom template if a reference document is selected
  if (referenceDocument) {
    const customTemplate = templates.find((t) => t.id === "custom");
    if (customTemplate) {
      customTemplate.description = "Using structure from reference document";
      customTemplate.sections = referenceDocument.sections;
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Select a Template</h2>
          <p className="text-muted-foreground">
            Choose a template that best fits your project needs
          </p>
        </div>
        <div className="flex gap-2">
          {referenceDocument ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Button variant="outline" className="gap-2" disabled>
                      <FileText size={16} />
                      Browse Library
                    </Button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Remove reference document first to browse library</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            <Link to="/scopingai/documents/library">
              <Button variant="outline" className="gap-2">
                <FileText size={16} />
                Browse Library
              </Button>
            </Link>
          )}
        </div>
      </div>

      {referenceDocument && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-4">
          <div className="flex items-start">
            <Info size={18} className="text-amber-500 mr-2 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-amber-800">
                Template selection is set to "Custom" based on your reference
                document
              </p>
              <p className="text-xs text-amber-700 mt-1">
                The structure and format will be based on "
                {referenceDocument.title}". Remove the reference document if you
                want to select a different template.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {templates.map((template) => (
          <Card
            key={template.id}
            className={`transition-all ${
              referenceDocument && template.id !== "custom"
                ? "opacity-50 cursor-not-allowed"
                : selectedTemplate === template.id
                ? "border-primary ring-2 ring-primary/20 cursor-pointer"
                : "hover:border-primary/50 cursor-pointer"
            }`}
            onClick={() => handleTemplateSelect(template.id)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{template.name}</CardTitle>
                  <CardDescription>{template.description}</CardDescription>
                </div>
                {selectedTemplate === template.id && (
                  <div className="bg-primary text-primary-foreground rounded-full p-1">
                    <Check size={16} />
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                <strong>Includes:</strong>
                <ul className="mt-1 space-y-1">
                  {template.sections.map((section: string, idx: number) => (
                    <li key={idx} className="flex items-center">
                      <span className="w-1.5 h-1.5 rounded-full bg-primary mr-2"></span>
                      {section}
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onNext}>Next</Button>
      </div>
    </div>
  );
};

export default TemplateSelectionStep;
