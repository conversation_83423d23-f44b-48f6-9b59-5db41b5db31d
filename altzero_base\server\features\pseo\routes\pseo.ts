import * as express from 'express';
import { pSEOAIService } from '../services/analysis/pSEOAIService';

const router = express.Router();

// Note: LangGraph routes are mounted in the main routes.ts file

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const openaiHealthy = await pSEOAIService.healthCheck();
    
    res.status(200).json({
      status: 'ok',
      services: {
        openai: openaiHealthy ? 'healthy' : 'unhealthy',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('pSEO health check error:', error);
    res.status(500).json({
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get OpenAI service status and available models
router.get('/openai/status', async (req, res) => {
  try {
    const isEnabled = pSEOAIService.enabled;
    const models = isEnabled ? await pSEOAIService.getAvailableModels() : [];
    
    res.status(200).json({
      enabled: isEnabled,
      availableModels: models,
      defaultModel: 'gpt-4o-mini',
    });
  } catch (error) {
    console.error('OpenAI status check error:', error);
    res.status(500).json({
      error: 'Failed to get OpenAI status',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Test OpenAI connection with a simple request
router.post('/openai/test', async (req, res) => {
  try {
    const { prompt, model, temperature, maxTokens } = req.body;
    const userId = req.headers['x-user-id'] as string;

    if (!prompt) {
      return res.status(400).json({
        error: 'Missing required field',
        message: 'Prompt is required',
      });
    }

    const response = await pSEOAIService.generateResponse({
      prompt,
      model,
      temperature,
      maxTokens,
      userId,
    });

    res.status(200).json({
      success: true,
      response: response.content,
      metadata: {
        model: response.model,
        processingTime: response.processingTime,
        usage: response.usage,
      },
    });
  } catch (error) {
    console.error('OpenAI test error:', error);
    res.status(500).json({
      error: 'OpenAI test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Perform pSEO analysis on scraped content
router.post('/analyze', async (req, res) => {
  try {
    const { scrapedContent, url } = req.body;
    const userId = req.headers['x-user-id'] as string;

    if (!scrapedContent || !url) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'scrapedContent and url are required',
      });
    }

    console.log(`Starting pSEO analysis for URL: ${url}`);

    const analysis = await pSEOAIService.performPSEOAnalysis({
      scrapedContent,
      url,
      userId,
    });

    res.status(200).json({
      success: true,
      analysis,
      metadata: {
        url,
        processingTime: analysis.processingTime,
        tokensUsed: analysis.tokensUsed,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('pSEO analysis error:', error);
    res.status(500).json({
      error: 'Analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Generate markdown report from analysis results
router.post('/report/generate', async (req, res) => {
  try {
    const { technicalAnalysis, contentAnalysis, url, domain } = req.body;

    if (!technicalAnalysis || !contentAnalysis || !url || !domain) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'technicalAnalysis, contentAnalysis, url, and domain are required',
      });
    }

    console.log(`Generating markdown report for URL: ${url}`);

    const markdownReport = await pSEOAIService.generateMarkdownReport(
      technicalAnalysis,
      contentAnalysis,
      url,
      domain
    );

    res.status(200).json({
      success: true,
      report: markdownReport,
      metadata: {
        url,
        domain,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Report generation error:', error);
    res.status(500).json({
      error: 'Report generation failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Convert markdown to HTML (utility endpoint)
router.post('/report/convert-to-html', async (req, res) => {
  try {
    const { markdown } = req.body;

    if (!markdown) {
      return res.status(400).json({
        error: 'Missing required field',
        message: 'markdown content is required',
      });
    }

    // Simple markdown to HTML conversion
    // In a production environment, you might want to use a proper markdown parser
    const html = markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
      .replace(/^##### (.*$)/gim, '<h5>$1</h5>')
      .replace(/^###### (.*$)/gim, '<h6>$1</h6>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/!\[([^\]]*)\]\(([^\)]*)\)/gim, '<img alt="$1" src="$2" />')
      .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2">$1</a>')
      .replace(/\n$/gim, '<br />')
      .replace(/^\* (.*$)/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gim, '<ul>$1</ul>')
      .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gim, '<ol>$1</ol>')
      .replace(/```([^`]*)```/gim, '<pre><code>$1</code></pre>')
      .replace(/`([^`]*)`/gim, '<code>$1</code>')
      .replace(/\n/gim, '<br />');

    res.status(200).json({
      success: true,
      html,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Markdown conversion error:', error);
    res.status(500).json({
      error: 'Markdown conversion failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Server-side SEO scoring endpoint (keeps API keys secure)
router.post('/seo-scoring', async (req, res) => {
  try {
    const { htmlContent, url } = req.body;

    if (!htmlContent || !url) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'htmlContent and url are required',
      });
    }

    console.log(`🔒 Server-side SEO scoring for URL: ${url}`);

    // Enhanced server-side analysis
    const { serverHTMLAnalysisService } = await import('../services/analysis/htmlAnalysisService');
    const { serverSEOScoringService } = await import('../services/analysis/seoScoringService');

    // Perform HTML analysis
    const htmlAnalysis = await serverHTMLAnalysisService.analyzeHTML(htmlContent);
    console.log(`✅ Server HTML analysis: ${htmlAnalysis.totalIssues} issues found`);

    // Perform SEO scoring (API keys are safely accessible server-side)
    const seoScoring = await serverSEOScoringService.performSEOScoring(htmlContent, url);
    console.log(`✅ Server SEO scoring: ${seoScoring.overallScore}/100 (${seoScoring.grade})`);

    res.status(200).json({
      success: true,
      htmlAnalysis,
      seoScoring,
      metadata: {
        url,
        analyzedAt: new Date().toISOString(),
        processingTime: htmlAnalysis.performanceMetrics.analysisTime + seoScoring.processingTime,
      },
    });
  } catch (error) {
    console.error('Server-side SEO scoring error:', error);
    res.status(500).json({
      error: 'SEO scoring failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Batch analysis endpoint for multiple URLs
router.post('/analyze/batch', async (req, res) => {
  try {
    const { urls, scrapedContents } = req.body;
    const userId = req.headers['x-user-id'] as string;

    if (!urls || !scrapedContents || urls.length !== scrapedContents.length) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'urls and scrapedContents arrays must be provided and have the same length',
      });
    }

    if (urls.length > 10) {
      return res.status(400).json({
        error: 'Batch size limit exceeded',
        message: 'Maximum 10 URLs can be analyzed in a single batch',
      });
    }

    console.log(`Starting batch pSEO analysis for ${urls.length} URLs`);

    const analyses = await Promise.allSettled(
      urls.map((url: string, index: number) =>
        pSEOAIService.performPSEOAnalysis({
          scrapedContent: scrapedContents[index],
          url,
          userId,
        })
      )
    );

    const results = analyses.map((result, index) => ({
      url: urls[index],
      status: result.status,
      ...(result.status === 'fulfilled'
        ? { analysis: result.value }
        : { error: result.reason?.message || 'Analysis failed' }),
    }));

    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const failureCount = results.length - successCount;

    res.status(200).json({
      success: true,
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: failureCount,
      },
      metadata: {
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Batch analysis error:', error);
    res.status(500).json({
      error: 'Batch analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Get analysis statistics
router.get('/stats', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'] as string;
    
    // This would typically query the database for user-specific stats
    // For now, return basic service stats
    res.status(200).json({
      success: true,
      stats: {
        serviceStatus: pSEOAIService.enabled ? 'active' : 'inactive',
        availableModels: await pSEOAIService.getAvailableModels(),
        // Add more stats as needed from database
      },
      metadata: {
        userId,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Stats retrieval error:', error);
    res.status(500).json({
      error: 'Failed to retrieve stats',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// Website scraping endpoint
router.post('/scrape', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        error: 'Missing required field',
        message: 'url is required',
      });
    }

    console.log(`Starting website scraping for URL: ${url}`);

    // Validate URL format
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return res.status(400).json({
          error: 'Invalid URL',
          message: 'URL must use HTTP or HTTPS protocol',
        });
      }
    } catch (urlError) {
      return res.status(400).json({
        error: 'Invalid URL format',
        message: 'Please provide a valid URL',
      });
    }

    const startTime = Date.now();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; AltZero-SEO-Bot/1.0)',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        return res.status(400).json({
          error: 'HTTP Error',
          message: `HTTP ${response.status}: ${response.statusText}`,
          details: { statusCode: response.status, statusText: response.statusText }
        });
      }

      const html = await response.text();
      const loadTime = Date.now() - startTime;

      if (!html || html.trim().length === 0) {
        return res.status(400).json({
          error: 'Empty Response',
          message: 'Received empty response from website',
        });
      }

      const scrapedData = {
        url,
        statusCode: response.status,
        html,
        headers: Object.fromEntries(response.headers.entries()),
        loadTime,
        timestamp: new Date().toISOString(),
      };

      res.status(200).json({
        success: true,
        data: scrapedData,
        metadata: {
          url,
          loadTime,
          htmlLength: html.length,
          timestamp: new Date().toISOString(),
        },
      });

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        return res.status(408).json({
          error: 'Request Timeout',
          message: 'Request timeout after 30 seconds',
        });
      }

      throw fetchError;
    }

  } catch (error) {
    console.error('Website scraping error:', error);
    res.status(500).json({
      error: 'Scraping failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// =====================================================
// FULL SITE ANALYSIS ENDPOINTS
// =====================================================

// Start full site analysis
router.post('/full-site-analysis/start', async (req, res) => {
  try {
    const { websiteId, analysisTypes, options } = req.body;
    
    if (!websiteId || !analysisTypes || !Array.isArray(analysisTypes)) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'websiteId and analysisTypes array are required'
      });
    }

    console.log(`🚀 Starting full site analysis for website ${websiteId} with types:`, analysisTypes);

    // Import the orchestrator service
    const { EnhancedAgentOrchestrator } = await import('../agents/orchestration/EnhancedAgentOrchestrator');
    const orchestrator = new EnhancedAgentOrchestrator();
    
    // Start the analysis
    const jobId = await orchestrator.startFullSiteAnalysis(
      websiteId,
      analysisTypes,
      req.headers['x-user-id'] as string,
      options
    );

    res.status(200).json({
      success: true,
      jobId,
      status: 'queued',
      message: 'Full site analysis started successfully'
    });

  } catch (error) {
    console.error('Failed to start full site analysis:', error);
    res.status(500).json({
      error: 'Failed to start analysis',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get analysis status
router.get('/full-site-analysis/status/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const userId = req.headers['x-user-id'] as string;
    
    console.log(`📊 Getting status for job ${jobId}`);

    // Import the orchestrator service
    const { EnhancedAgentOrchestrator } = await import('../agents/orchestration/EnhancedAgentOrchestrator');
    const orchestrator = new EnhancedAgentOrchestrator();
    
    // Get the analysis progress
    const progress = await orchestrator.getAnalysisProgress(jobId, userId);
    
    if (!progress) {
      return res.status(404).json({
        error: 'Job not found',
        message: `No analysis job found with ID: ${jobId}`
      });
    }

    res.status(200).json(progress);

  } catch (error) {
    console.error(`Failed to get status for job ${req.params.jobId}:`, error);
    res.status(500).json({
      error: 'Failed to get job status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get analysis results
router.get('/full-site-analysis/results/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const userId = req.headers['x-user-id'] as string;
    
    console.log(`📋 Getting results for job ${jobId}`);

    // Import the orchestrator service
    const { EnhancedAgentOrchestrator } = await import('../agents/orchestration/EnhancedAgentOrchestrator');
    const orchestrator = new EnhancedAgentOrchestrator();
    
    // Get the analysis results
    const results = await orchestrator.getAnalysisResults(jobId, userId);
    
    if (!results) {
      return res.status(404).json({
        error: 'Results not found',
        message: `No results found for job ID: ${jobId}`
      });
    }

    res.status(200).json(results);

  } catch (error) {
    console.error(`Failed to get results for job ${req.params.jobId}:`, error);
    res.status(500).json({
      error: 'Failed to get job results',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Cancel analysis
router.post('/full-site-analysis/cancel/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const userId = req.headers['x-user-id'] as string;
    
    console.log(`❌ Cancelling job ${jobId}`);

    // Import the orchestrator service
    const { EnhancedAgentOrchestrator } = await import('../agents/orchestration/EnhancedAgentOrchestrator');
    const orchestrator = new EnhancedAgentOrchestrator();
    
    // Cancel the analysis
    await orchestrator.cancelAnalysis(jobId, userId);

    res.status(200).json({
      success: true,
      message: 'Analysis cancelled successfully'
    });

  } catch (error) {
    console.error(`Failed to cancel job ${req.params.jobId}:`, error);
    res.status(500).json({
      error: 'Failed to cancel analysis',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// =====================================================
// AGENT ENDPOINTS
// =====================================================

// Content Generation Agent endpoint
router.post('/agents/content-generation', async (req, res) => {
  try {
    const { website_id, parameters } = req.body;
    const userId = req.headers['x-user-id'] as string;

    if (!website_id || !parameters) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'website_id and parameters are required'
      });
    }

    console.log(`🤖 Starting content generation for website ${website_id}`);

    // Import and execute ContentGenerationAgent
    const { ContentGenerationAgent } = await import('../agents/specialists/ContentGenerationAgent');
    const { PSEODatabaseService } = await import('../services/PSEODatabaseService');
    
    const agent = new ContentGenerationAgent();
    const dbService = new PSEODatabaseService();
    
    // Get website data
    const website = await dbService.getWebsiteById(website_id);
    if (!website) {
      return res.status(404).json({
        error: 'Website not found',
        message: `No website found with ID: ${website_id}`
      });
    }

    // Create agent context
    const context = {
      website,
      job: {
        id: `content_gen_${Date.now()}`,
        website_id,
        job_type: 'content_generation' as const,
        agent_name: 'ContentGenerationAgent',
        parent_job_id: undefined,
        status: 'running' as const,
        priority: 1,
        started_at: new Date().toISOString(),
        completed_at: undefined,
        processing_time_seconds: undefined,
        result_data: {
          website_id,
          parameters
        },
        error_message: undefined,
        retry_count: 0,
        max_retries: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      config: {
        max_execution_time_seconds: 300,
        retry_attempts: 3,
        batch_size: 10,
        rate_limit_delay_ms: 100
      },
      logger: {
        info: (msg: string, data?: any) => console.log('INFO:', msg, data),
        warn: (msg: string, data?: any) => console.warn('WARN:', msg, data),
        error: (msg: string, error: Error, data?: any) => console.error('ERROR:', msg, error, data),
        debug: (msg: string, data?: any) => console.debug('DEBUG:', msg, data),
        metric: (name: string, value: number, metadata?: Record<string, unknown>) => console.log('METRIC:', name, '=', value, metadata)
      },
      tools: {
        ai: {
          generateText: async (prompt: string, options: any) => {
            try {
              // Use the existing pSEOAIService for AI generation
              const response = await pSEOAIService.generateResponse({
                prompt,
                model: options.model || 'gpt-4o',
                temperature: options.temperature || 0.7,
                maxTokens: options.max_tokens || 2000,
                systemMessage: 'You are an expert SEO content writer. Create high-quality, engaging content that ranks well in search engines.',
                userId
              });
              return response.content;
            } catch (error) {
              if (error instanceof Error && error.message.includes('not enabled')) {
                throw new Error('AI service not configured - Configure OpenAI API key to enable content generation');
              }
              throw error;
            }
          },
          analyzeContent: async (content: string, analysis_type: string) => {
            try {
              const prompt = `Analyze the following content for ${analysis_type}:\n\n${content}`;
              const response = await pSEOAIService.generateResponse({
                prompt,
                model: 'gpt-4o',
                temperature: 0.3,
                maxTokens: 1000,
                systemMessage: `You are an expert content analyst specializing in ${analysis_type}.`,
                userId
              });
              return { analysis: response.content, type: analysis_type };
            } catch (error) {
              if (error instanceof Error && error.message.includes('not enabled')) {
                throw new Error('AI service not configured - Configure OpenAI API key to enable content analysis');
              }
              throw error;
            }
          },
          generateEmbeddings: async (text: string) => {
            // For now, return empty array - embeddings require specific API setup
            throw new Error('Embeddings service not configured - Configure OpenAI embeddings API to enable this feature');
          },
          classifyContent: async (content: string, categories: string[]) => {
            try {
              const prompt = `Classify the following content into one of these categories: ${categories.join(', ')}\n\nContent: ${content}\n\nReturn only the category name.`;
              const response = await pSEOAIService.generateResponse({
                prompt,
                model: 'gpt-4o',
                temperature: 0.1,
                maxTokens: 50,
                systemMessage: 'You are a content classifier. Return only the most appropriate category name.',
                userId
              });
              return response.content.trim();
            } catch (error) {
              if (error instanceof Error && error.message.includes('not enabled')) {
                throw new Error('AI service not configured - Configure OpenAI API key to enable content classification');
              }
              throw error;
            }
          }
        },
        database: {
          query: async <T>(sql: string, params: any[] = []): Promise<T[]> => {
            // For now, throw an error encouraging use of direct table operations
            throw new Error('Raw SQL queries not supported. Use database.insert/update/delete methods instead.');
          },
          insert: async <T>(table: string, data: Record<string, unknown>): Promise<T> => {
            const { supabase } = await import('../../../base/common/apps/supabase');
            const { data: result, error } = await supabase
              .from(table)
              .insert(data)
              .select()
              .single();
            if (error) throw new Error(`Database insert failed: ${error.message}`);
            return result;
          },
          update: async <T>(table: string, id: string, data: Record<string, unknown>): Promise<T> => {
            const { supabase } = await import('../../../base/common/apps/supabase');
            const { data: result, error } = await supabase
              .from(table)
              .update(data)
              .eq('id', id)
              .select()
              .single();
            if (error) throw new Error(`Database update failed: ${error.message}`);
            return result;
          },
          delete: async (table: string, id: string): Promise<boolean> => {
            const { supabase } = await import('../../../base/common/apps/supabase');
            const { error } = await supabase
              .from(table)
              .delete()
              .eq('id', id);
            if (error) throw new Error(`Database delete failed: ${error.message}`);
            return true;
          },
          transaction: async <T>(operations: (() => Promise<T>)[]): Promise<T[]> => {
            // Simple sequential execution for now
            // In a real implementation, you'd use proper database transactions
            const results = [];
            for (const operation of operations) {
              results.push(await operation());
            }
            return results;
          }
        },
        http: {
          get: async (url: string, options?: any) => {
            const response = await fetch(url, { method: 'GET', ...options });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return response.json();
          },
          post: async (url: string, data: any, options?: any) => {
            const response = await fetch(url, { 
              method: 'POST', 
              headers: { 'Content-Type': 'application/json', ...options?.headers },
              body: JSON.stringify(data),
              ...options 
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return response.json();
          },
          put: async (url: string, data: any, options?: any) => {
            const response = await fetch(url, { 
              method: 'PUT', 
              headers: { 'Content-Type': 'application/json', ...options?.headers },
              body: JSON.stringify(data),
              ...options 
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return response.json();
          },
          delete: async (url: string, options?: any) => {
            const response = await fetch(url, { method: 'DELETE', ...options });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return response.json();
          }
        },
        crawler: {
          crawlPage: async (url: string, options?: any) => {
            throw new Error('Web crawler not configured - Configure a crawling service to enable page discovery');
          },
          parseSitemap: async (url: string) => {
            throw new Error('Web crawler not configured - Configure a crawling service to parse sitemaps');
          },
          checkRobotsTxt: async (domain: string) => {
            throw new Error('Web crawler not configured - Configure a crawling service to check robots.txt');
          },
          batchCrawl: async (urls: string[], options?: any) => {
            throw new Error('Web crawler not configured - Configure a crawling service for batch crawling');
          }
        },
        parser: {
          parseHTML: (html: string) => {
            throw new Error('HTML parser not configured - Configure a parsing service');
          },
          extractMetadata: (html: string) => {
            throw new Error('HTML parser not configured - Configure a parsing service');
          },
          extractLinks: (html: string) => {
            throw new Error('HTML parser not configured - Configure a parsing service');
          },
          extractText: (html: string) => {
            throw new Error('HTML parser not configured - Configure a parsing service');
          },
          extractStructuredData: (html: string) => {
            throw new Error('HTML parser not configured - Configure a parsing service');
          }
        },
        validator: {
          validateURL: (url: string) => {
            try {
              new URL(url);
              return true;
            } catch {
              return false;
            }
          },
          validateEmail: (email: string) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
          },
          validateDomain: (domain: string) => {
            const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
            return domainRegex.test(domain);
          },
          validateSEOData: (data: unknown) => {
            return { valid: true, errors: [], warnings: [] };
          }
        },
        cache: {
          get: async (key: string) => {
            // Simple in-memory cache for now
            return null;
          },
          set: async (key: string, value: any, ttl?: number) => {
            // Simple in-memory cache for now
            return;
          },
          delete: async (key: string) => {
            return;
          },
          clear: async () => {
            return;
          }
        }
      }
    };

    // Execute the agent
    const result = await agent.execute(context);

    if (result.success) {
      res.status(200).json({
        success: true,
        data: result.data,
        metrics: result.metrics
      });
    } else {
      res.status(500).json({
        error: 'Content generation failed',
        message: result.error
      });
    }

  } catch (error) {
    console.error('Content generation agent error:', error);
    res.status(500).json({
      error: 'Content generation failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// =====================================================
// CONTENT MANAGEMENT ENDPOINTS
// =====================================================

// Create content item
router.post('/content', async (req, res) => {
  try {
    const contentData = req.body;
    const userId = req.headers['x-user-id'] as string;

    if (!contentData.website_id || !contentData.title || !contentData.content_markdown) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'website_id, title, and content_markdown are required'
      });
    }

    console.log(`💾 Creating content item: ${contentData.title}`);

    // Import ContentManagementService
    const { ContentManagementService } = await import('../services/content/ContentManagementService');
    const contentService = new ContentManagementService();

    // Create content item
    const createdContent = await contentService.createContent(contentData);

    res.status(201).json({
      success: true,
      data: createdContent
    });

  } catch (error) {
    console.error('Content creation error:', error);
    res.status(500).json({
      error: 'Failed to create content',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get content by website
router.get('/content/website/:websiteId', async (req, res) => {
  try {
    const { websiteId } = req.params;
    const { 
      status, 
      content_type, 
      priority, 
      ai_generated, 
      generated_by_agent,
      limit = 20,
      offset = 0,
      search 
    } = req.query;

    console.log(`📋 Getting content for website ${websiteId}`);

    // Import ContentManagementService
    const { ContentManagementService } = await import('../services/content/ContentManagementService');
    const contentService = new ContentManagementService();

    // Build filters
    const filters: any = {
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    };

    if (status) filters.status = Array.isArray(status) ? status : [status];
    if (content_type) filters.content_type = Array.isArray(content_type) ? content_type : [content_type];
    if (priority) filters.priority = Array.isArray(priority) ? priority : [priority];
    if (ai_generated !== undefined) filters.ai_generated = ai_generated === 'true';
    if (generated_by_agent) filters.generated_by_agent = generated_by_agent as string;
    if (search) filters.search = search as string;

    // Get content
    const result = await contentService.getContentByWebsite(websiteId, filters);

    res.status(200).json({
      success: true,
      data: result.items,
      total: result.total,
      pagination: {
        limit: filters.limit,
        offset: filters.offset,
        hasMore: result.total > filters.offset + filters.limit
      }
    });

  } catch (error) {
    console.error('Content retrieval error:', error);
    res.status(500).json({
      error: 'Failed to retrieve content',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update content item
router.put('/content/:contentId', async (req, res) => {
  try {
    const { contentId } = req.params;
    const updates = req.body;
    const userId = req.headers['x-user-id'] as string;

    console.log(`✏️ Updating content item ${contentId}`);

    // Import ContentManagementService
    const { ContentManagementService } = await import('../services/content/ContentManagementService');
    const contentService = new ContentManagementService();

    // Update content item
    const updatedContent = await contentService.updateContent(contentId, updates);

    res.status(200).json({
      success: true,
      data: updatedContent
    });

  } catch (error) {
    console.error('Content update error:', error);
    res.status(500).json({
      error: 'Failed to update content',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete content item
router.delete('/content/:contentId', async (req, res) => {
  try {
    const { contentId } = req.params;

    console.log(`🗑️ Deleting content item ${contentId}`);

    // Import ContentManagementService
    const { ContentManagementService } = await import('../services/content/ContentManagementService');
    const contentService = new ContentManagementService();

    // Delete content item
    await contentService.deleteContent(contentId);

    res.status(200).json({
      success: true,
      message: 'Content deleted successfully'
    });

  } catch (error) {
    console.error('Content deletion error:', error);
    res.status(500).json({
      error: 'Failed to delete content',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get content analytics
router.get('/content/analytics/:websiteId', async (req, res) => {
  try {
    const { websiteId } = req.params;

    console.log(`📊 Getting content analytics for website ${websiteId}`);

    // Import ContentManagementService
    const { ContentManagementService } = await import('../services/content/ContentManagementService');
    const contentService = new ContentManagementService();

    // Get analytics
    const analytics = await contentService.getContentAnalytics(websiteId);

    res.status(200).json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Content analytics error:', error);
    res.status(500).json({
      error: 'Failed to get content analytics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router; 