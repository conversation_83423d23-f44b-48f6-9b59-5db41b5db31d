// =====================================================
// TOOL REGISTRY FOR LANGGRAPH WORKFLOWS
// =====================================================

import { WorkflowTools, WorkflowConfig } from '../types/WorkflowState';
import { SemrushTool } from '../tools/SemrushTool';
import { UbersuggestTool } from '../tools/UbersuggestTool';
import { RapidAPITool } from '../tools/RapidAPITool';
import { pSEOAIService } from '../../services/analysis/pSEOAIService';

export class ToolRegistry {
  private config: WorkflowConfig;
  private tools: WorkflowTools | null = null;
  private cache: Map<string, any> = new Map();

  constructor(config: WorkflowConfig) {
    this.config = config;
  }

  // Get all available tools
  public async getTools(): Promise<WorkflowTools> {
    if (this.tools) {
      return this.tools;
    }

    this.tools = {
      ai: this.createAITools(),
      http: this.createHTTPTools(),
      database: this.createDatabaseTools(),
      cache: this.createCacheTools(),
      seo: this.createSEOTools()
    };

    return this.tools;
  }

  // Create AI tools
  private createAITools() {
    return {
      generateText: async (prompt: string, options: any = {}) => {
        try {
          const response = await pSEOAIService.generateResponse({
            prompt,
            model: options.model || 'gpt-4o-mini',
            temperature: options.temperature || 0.7,
            maxTokens: options.maxTokens || 2000,
            systemMessage: options.systemMessage || 'You are an expert SEO analyst.',
            userId: options.userId || 'system'
          });
          
          return response.content;
        } catch (error) {
          if (error instanceof Error && error.message.includes('not enabled')) {
            throw new Error('AI service not configured - Configure OpenAI API key to enable AI-powered analysis');
          }
          throw error;
        }
      },

      analyzeContent: async (content: string, analysisType: string) => {
        const prompt = this.getAnalysisPrompt(analysisType, content);
        return await this.tools!.ai.generateText(prompt, {
          model: 'gpt-4o-mini',
          temperature: 0.3,
          response_format: 'json'
        });
      }
    };
  }

  // Create HTTP tools
  private createHTTPTools() {
    return {
      get: async (url: string, options: any = {}) => {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), options.timeout || 15000);

        try {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'User-Agent': 'AltZero-pSEO-Bot/1.0',
              ...options.headers
            },
            signal: controller.signal,
            ...options
          });

          clearTimeout(timeout);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const contentType = response.headers.get('content-type');
          if (contentType?.includes('application/json')) {
            return await response.json();
          } else {
            return await response.text();
          }
        } catch (error) {
          clearTimeout(timeout);
          throw error;
        }
      },

      post: async (url: string, data: any, options: any = {}) => {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), options.timeout || 30000);

        try {
          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'AltZero-pSEO-Bot/1.0',
              ...options.headers
            },
            body: JSON.stringify(data),
            signal: controller.signal,
            ...options
          });

          clearTimeout(timeout);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          return await response.json();
        } catch (error) {
          clearTimeout(timeout);
          throw error;
        }
      }
    };
  }

  // Create database tools
  private createDatabaseTools() {
    return {
      query: async (sql: string, params: any[] = []) => {
        // This would integrate with your existing database service
        // For now, we'll create a placeholder that integrates with Supabase
        try {
          // Import your existing database service
          const { databaseService } = await import('../../services/databaseService');
          
          // Execute raw SQL query (you may need to adapt this based on your database service)
          return await databaseService.executeQuery(sql, params);
        } catch (error) {
          console.error('Database query failed:', error);
          throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      insert: async (table: string, data: any) => {
        try {
          const { databaseService } = await import('../../services/databaseService');
          return await databaseService.insert(table, data);
        } catch (error) {
          console.error('Database insert failed:', error);
          throw new Error(`Database insert failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      update: async (table: string, data: any, where: any) => {
        try {
          const { databaseService } = await import('../../services/databaseService');
          return await databaseService.update(table, data, where);
        } catch (error) {
          console.error('Database update failed:', error);
          throw new Error(`Database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },



      saveKeywordData: async (websiteId: string, keywords: any[]) => {
        try {
          const { PSEODatabaseService } = await import('../../services/PSEODatabaseService');
          const pseoDbService = new PSEODatabaseService();
          return await pseoDbService.saveKeywordData(websiteId, keywords);
        } catch (error) {
          console.error('Save keyword data failed:', error);
          throw new Error(`Save keyword data failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      updateKeywordClusters: async (websiteId: string, workflowId: string, clusters: any[]) => {
        try {
          const { supabase } = await import('../../../../base/common/apps/supabase');

          // Update the clusters column for keywords from this workflow
          const { error } = await supabase
            .from('pseo_keywords')
            .update({
              clusters: clusters,
              updated_at: new Date().toISOString()
            })
            .eq('website_id', websiteId)
            .order('created_at', { ascending: false })
            .limit(1); // Only update the most recent record to store the clusters

          if (error) {
            throw new Error(`Database update failed: ${error.message}`);
          }

          return { success: true, clusters_saved: clusters.length };
        } catch (error) {
          console.error('Update keyword clusters failed:', error);
          throw new Error(`Update keyword clusters failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };
  }

  // Create cache tools
  private createCacheTools() {
    return {
      get: async <T>(key: string): Promise<T | null> => {
        try {
          const cached = this.cache.get(key);
          if (cached && cached.expires > Date.now()) {
            return cached.value as T;
          }
          this.cache.delete(key);
          return null;
        } catch (error) {
          console.warn('Cache get failed:', error);
          return null;
        }
      },

      set: async (key: string, value: any, ttl: number = 3600): Promise<void> => {
        try {
          this.cache.set(key, {
            value,
            expires: Date.now() + (ttl * 1000)
          });
        } catch (error) {
          console.warn('Cache set failed:', error);
        }
      },

      delete: async (key: string): Promise<void> => {
        try {
          this.cache.delete(key);
        } catch (error) {
          console.warn('Cache delete failed:', error);
        }
      }
    };
  }

  // Create SEO tools
  private createSEOTools() {
    const seoTools: any = {};

    // Initialize Semrush if configured
    if (this.config.semrush_api_key) {
      try {
        seoTools.semrush = new SemrushTool({
          apiKey: this.config.semrush_api_key,
          enabled: true,
          rateLimit: 10,
          timeout: 30000,
          retries: 3
        });
      } catch (error) {
        console.warn('Failed to initialize Semrush tool:', error);
      }
    }

    // Initialize Ubersuggest if configured
    if (this.config.ubersuggest_api_key) {
      try {
        seoTools.ubersuggest = new UbersuggestTool({
          apiKey: this.config.ubersuggest_api_key,
          enabled: true,
          rateLimit: 8,
          timeout: 30000,
          retries: 3
        });
      } catch (error) {
        console.warn('Failed to initialize Ubersuggest tool:', error);
      }
    }

    // Initialize RapidAPI Gateway if configured
    if (process.env.RAPIDAPI_KEY) {
      try {
        seoTools.rapidapi = new RapidAPITool({
          apiKey: process.env.RAPIDAPI_KEY,
          enabled: true,
          rateLimit: 10,
          timeout: 30000,
          retries: 3
        });
      } catch (error) {
        console.warn('Failed to initialize RapidAPI tool:', error);
      }
    }

    return seoTools;
  }

  // Get analysis prompt based on type
  private getAnalysisPrompt(analysisType: string, content: string): string {
    const prompts: Record<string, string> = {
      keyword_extraction: `
        Analyze the following website content and extract relevant SEO keywords and phrases.
        Focus on the main topics, products, services, and industry terms.
        Return 10-20 high-value keywords that this website should rank for.
        
        Content: ${content.substring(0, 2000)}...
        
        Return keywords as a JSON array of strings, like: ["keyword1", "keyword2", ...]
      `,
      
      content_analysis: `
        Analyze the following content for SEO optimization opportunities.
        Identify missing keywords, content gaps, and improvement suggestions.
        
        Content: ${content.substring(0, 2000)}...
        
        Return analysis as JSON with structure:
        {
          "missing_keywords": ["keyword1", "keyword2"],
          "content_gaps": ["gap1", "gap2"],
          "suggestions": ["suggestion1", "suggestion2"]
        }
      `,
      
      competitor_analysis: `
        Analyze the following competitor content and identify keyword opportunities.
        Focus on keywords they rank for that we could target.
        
        Content: ${content.substring(0, 2000)}...
        
        Return analysis as JSON with structure:
        {
          "competitor_keywords": ["keyword1", "keyword2"],
          "opportunities": ["opportunity1", "opportunity2"],
          "content_themes": ["theme1", "theme2"]
        }
      `
    };

    return prompts[analysisType] || prompts.keyword_extraction;
  }

  // Check if a tool is available
  public isToolAvailable(toolName: string): boolean {
    switch (toolName) {
      case 'ai':
        return !!this.config.openai_api_key;
      case 'semrush':
        return !!this.config.semrush_api_key;
      case 'ahrefs':
        return !!this.config.ahrefs_api_key;
      case 'ubersuggest':
        return !!this.config.ubersuggest_api_key;
      case 'http':
      case 'database':
      case 'cache':
        return true;
      default:
        return false;
    }
  }

  // Get available tools list
  public getAvailableTools(): string[] {
    const allTools = ['ai', 'http', 'database', 'cache', 'semrush', 'ahrefs', 'ubersuggest'];
    return allTools.filter(tool => this.isToolAvailable(tool));
  }

  // Validate tool configuration
  public validateConfiguration(): { valid: boolean; missing: string[]; warnings: string[] } {
    const missing: string[] = [];
    const warnings: string[] = [];

    if (!this.config.openai_api_key) {
      missing.push('OpenAI API key - AI-powered analysis will be disabled');
    }

    if (!this.config.semrush_api_key && !this.config.ahrefs_api_key && !this.config.ubersuggest_api_key) {
      warnings.push('No SEO tool API keys configured - keyword research will use AI fallback only');
    }

    return {
      valid: missing.length === 0,
      missing,
      warnings
    };
  }

  // Clear cache
  public clearCache(): void {
    this.cache.clear();
  }

  // Get cache statistics
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
