import React, { useState, useEffect } from "react";
import Layout from "../../../../../components/Layout";
import { PromptTemplate } from "../../../../../types/scoping";
import { supabase } from "../../../../../utils/supabaseClient";

// API functions to interact with Supabase
const fetchPromptTemplates = async (): Promise<PromptTemplate[]> => {
  try {
    console.log("Fetching prompt templates from Supabase...");
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting user:", userError);
      throw userError;
    }

    const userId = userData.user?.id;
    if (!userId) {
      console.error("No user ID found, user may not be authenticated");
      return [];
    }

    const { data, error } = await supabase
      .from("prompt_templates")
      .select("*")
      .eq("user_id", userId)
      .order("name");

    if (error) {
      console.error("Error fetching prompt templates:", error);
      throw error;
    }

    console.log("Fetched prompt templates:", data);

    if (!data || data.length === 0) {
      console.log("No prompt templates found in database");
      return [];
    }

    // Transform the data to match our PromptTemplate type
    return data.map((template) => ({
      id: template.id,
      name: template.name,
      description: template.description,
      content: template.content,
      variables: template.variables || [],
      createdAt: new Date(template.created_at),
      updatedAt: new Date(template.updated_at),
    }));
  } catch (error) {
    console.error("Error fetching prompt templates:", error);
    return [];
  }
};

const savePromptTemplate = async (
  template: PromptTemplate
): Promise<PromptTemplate> => {
  const isNewTemplate = !template.id;
  console.log("Saving template:", template);

  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting user:", userError);
      throw userError;
    }

    const userId = userData.user?.id;
    if (!userId) {
      throw new Error("No user ID found, user may not be authenticated");
    }

    // Format the data for Supabase - excluding ID for new templates
    const templateData = {
      user_id: userId,
      name: template.name,
      description: template.description,
      content: template.content,
      variables: template.variables || [],
      updated_at: new Date(),
    };

    console.log("Template data to be saved:", templateData);

    let result;

    if (isNewTemplate) {
      // Insert new template - Supabase will generate the ID
      console.log("Creating new template...");
      const { data, error } = await supabase
        .from("prompt_templates")
        .insert(templateData)
        .select();

      console.log("Insert response:", data);

      if (error) {
        console.error("Error inserting template:", error);
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error("Failed to create template, no data returned");
      }

      result = data[0]; // Access the first item of the array
    } else {
      // Update existing template - include the ID
      console.log("Updating existing template:", template.id);
      const { data, error } = await supabase
        .from("prompt_templates")
        .update({ ...templateData, id: template.id })
        .eq("id", template.id)
        .select();

      console.log("Update response:", data);

      if (error) {
        console.error("Error updating template:", error);
        throw error;
      }

      if (!data || data.length === 0) {
        throw new Error("Failed to update template, no data returned");
      }

      result = data[0]; // Access the first item of the array
    }

    console.log("Template save result:", result);

    // Transform the result back to our PromptTemplate type
    return {
      id: result.id,
      name: result.name,
      description: result.description,
      content: result.content,
      variables: result.variables || [],
      createdAt: new Date(result.created_at),
      updatedAt: new Date(result.updated_at),
    };
  } catch (error) {
    console.error("Error saving prompt template:", error);
    throw error;
  }
};

// Add delete function
const deletePromptTemplate = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from("prompt_templates")
      .delete()
      .eq("id", id);

    if (error) throw error;
  } catch (error) {
    console.error("Error deleting prompt template:", error);
    throw error;
  }
};

const TemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] =
    useState<PromptTemplate | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const data = await fetchPromptTemplates();
      setTemplates(data);
    } catch (error) {
      console.error("Error loading prompt templates:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = () => {
    const newTemplate: PromptTemplate = {
      id: "",
      name: "",
      description: "",
      content: "",
      variables: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setSelectedTemplate(newTemplate);
    setIsEditing(true);
  };

  const handleEditTemplate = (template: PromptTemplate) => {
    setSelectedTemplate(template);
    setIsEditing(true);
  };

  const handleSaveTemplate = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedTemplate) return;

    try {
      const updatedTemplate = await savePromptTemplate(selectedTemplate);

      setTemplates((prevTemplates) => {
        const existingIndex = prevTemplates.findIndex(
          (t) => t.id === updatedTemplate.id
        );
        if (existingIndex >= 0) {
          // Update existing template
          const newTemplates = [...prevTemplates];
          newTemplates[existingIndex] = updatedTemplate;
          return newTemplates;
        } else {
          // Add new template
          return [...prevTemplates, updatedTemplate];
        }
      });

      setIsEditing(false);
      setSelectedTemplate(null);
    } catch (error) {
      console.error("Error saving prompt template:", error);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSelectedTemplate(null);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!selectedTemplate) return;

    setSelectedTemplate({
      ...selectedTemplate,
      [e.target.name]: e.target.value,
    });
  };

  // Handle variable management for prompt templates
  const handleAddVariable = () => {
    if (!selectedTemplate) return;

    const newVariable = "";
    setSelectedTemplate({
      ...selectedTemplate,
      variables: [...(selectedTemplate.variables || []), newVariable],
    });
  };

  const handleUpdateVariable = (index: number, value: string) => {
    if (!selectedTemplate || !selectedTemplate.variables) return;

    const newVariables = [...selectedTemplate.variables];
    newVariables[index] = value;

    setSelectedTemplate({
      ...selectedTemplate,
      variables: newVariables,
    });
  };

  const handleRemoveVariable = (index: number) => {
    if (!selectedTemplate || !selectedTemplate.variables) return;

    setSelectedTemplate({
      ...selectedTemplate,
      variables: selectedTemplate.variables.filter(
        (_: string, i: number) => i !== index
      ),
    });
  };

  // Add delete handler
  const handleDeleteTemplate = async (template: PromptTemplate) => {
    if (
      !window.confirm(`Are you sure you want to delete "${template.name}"?`)
    ) {
      return;
    }

    try {
      await deletePromptTemplate(template.id);
      setTemplates(templates.filter((t) => t.id !== template.id));
    } catch (error) {
      console.error("Error deleting template:", error);
    }
  };

  const filteredTemplates = templates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description === null ||
      template.description === undefined
  );

  // Render the prompt template form
  const renderTemplateForm = () => {
    if (!selectedTemplate) return null;

    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Template Name
          </label>
          <input
            type="text"
            name="name"
            value={selectedTemplate.name}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            name="description"
            value={selectedTemplate.description ?? ""}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded"
            rows={2}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Prompt Content
          </label>
          <textarea
            name="content"
            value={selectedTemplate.content}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 rounded"
            rows={6}
            placeholder="Enter your prompt content with {{variable}} placeholders"
            required
          />
          <p className="text-sm text-gray-500 mt-1">
            Use &#123;&#123;variableName&#125;&#125; syntax for variables that
            will be replaced with actual values.
          </p>
        </div>

        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-700">
              Variables
            </label>
            <button
              type="button"
              onClick={handleAddVariable}
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              + Add Variable
            </button>
          </div>

          {selectedTemplate.variables &&
          selectedTemplate.variables.length > 0 ? (
            <ul className="border rounded-lg divide-y">
              {selectedTemplate.variables.map(
                (variable: string, index: number) => (
                  <li key={index} className="p-2 flex items-center">
                    <input
                      type="text"
                      value={variable}
                      onChange={(e) =>
                        handleUpdateVariable(index, e.target.value)
                      }
                      className="flex-1 p-1 border-b border-dashed border-gray-300 focus:border-indigo-500 focus:outline-none"
                      placeholder="Variable name"
                    />
                    <button
                      type="button"
                      onClick={() => handleRemoveVariable(index)}
                      className="ml-2 text-red-500 hover:text-red-700"
                    >
                      &times;
                    </button>
                  </li>
                )
              )}
            </ul>
          ) : (
            <p className="text-sm text-gray-500 italic">No variables defined</p>
          )}
        </div>
      </div>
    );
  };

  // Render template cards
  const renderTemplateCards = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-32">
          <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      );
    }

    if (filteredTemplates.length === 0) {
      return (
        <div className="text-center py-8">
          {searchTerm ? (
            <p className="text-gray-500">
              No templates found matching "{searchTerm}"
            </p>
          ) : (
            <p className="text-gray-500">
              No prompt templates created yet. Create your first template!
            </p>
          )}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 gap-4">
        {filteredTemplates.map((template) => (
          <div
            key={template.id}
            className="border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition bg-white overflow-hidden"
          >
            <div className="p-4">
              <h3 className="font-semibold text-lg mb-1">{template.name}</h3>
              {template.description && (
                <p className="text-gray-600 text-sm mb-2">
                  {template.description}
                </p>
              )}

              <div className="mt-4 flex flex-wrap gap-2">
                {template.variables?.map((variable, index) => (
                  <span
                    key={index}
                    className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded"
                  >
                    {"{{"}
                    {variable}
                    {"}}"}
                  </span>
                ))}
              </div>

              <div className="mt-4 flex justify-end space-x-2">
                <button
                  onClick={() => handleEditTemplate(template)}
                  className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDeleteTemplate(template)}
                  className="text-red-600 hover:text-red-800 text-sm font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Layout>
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Prompt Templates</h1>
          <p className="mt-2 text-gray-600">
            Create and manage AI prompt templates for document generation
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left side - Template form or instructions */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {isEditing ? (
                <>
                  <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <h2 className="text-xl font-semibold text-gray-900">
                      {selectedTemplate?.id
                        ? "Edit Template"
                        : "Create Template"}
                    </h2>
                  </div>
                  <form onSubmit={handleSaveTemplate} className="p-6">
                    {renderTemplateForm()}

                    <div className="mt-6 flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={handleCancelEdit}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md shadow-sm hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700"
                      >
                        Save Template
                      </button>
                    </div>
                  </form>
                </>
              ) : (
                <div className="p-6 space-y-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    About Prompt Templates
                  </h2>
                  <p className="text-gray-600">
                    Prompt templates help generate consistent AI responses for
                    document creation.
                  </p>
                  <p className="text-gray-600">
                    Use &#123;&#123;variables&#125;&#125; in your prompts to
                    customize content for each document.
                  </p>
                  <button
                    onClick={handleCreateTemplate}
                    className="w-full mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700"
                  >
                    Create New Template
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right side - Templates listing */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 p-4 rounded-xl shadow-sm border border-gray-200">
              <div className="mb-4">
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                />
              </div>

              {renderTemplateCards()}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TemplateManager;
