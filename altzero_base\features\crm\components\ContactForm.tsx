import React, { useState, useEffect } from "react";
import { Contact, ContactFormData, Company } from "../types";
import { crmService } from "../services/crmService";

// Organization interface for selection
interface Organization {
  id: string;
  name: string;
  role?: string;
}

interface ContactFormProps {
  contact?: Contact;
  onSubmit: (data: ContactFormData) => Promise<void>;
  onCancel: () => void;
}

const ContactForm: React.FC<ContactFormProps> = ({
  contact,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState<ContactFormData>({
    full_name: "",
    organisation_id: "", // Add organization selection
    email: "",
    phone: "",
    phone2: "",
    mobile: "",
    fax: "",
    job_title: "",
    company_id: "",
    salutation: "",
    skypename: "",
    webpage: "",
    note: "",
    tags: [],
    custom_fields: {},
  });

  const [companies, setCompanies] = useState<Company[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingOrganizations, setLoadingOrganizations] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tagInput, setTagInput] = useState("");

  useEffect(() => {
    loadCompanies();
    loadOrganizations();

    if (contact) {
      setFormData({
        full_name: contact.full_name || "",
        organisation_id: contact.organisation_id || "",
        email: contact.email || "",
        phone: contact.phone || "",
        phone2: contact.phone2 || "",
        mobile: contact.mobile || "",
        fax: contact.fax || "",
        job_title: contact.job_title || "",
        company_id: contact.company_id || "",
        salutation: contact.salutation || "",
        skypename: contact.skypename || "",
        webpage: contact.webpage || "",
        note: contact.note || "",
        tags: contact.tags || [],
        custom_fields: contact.custom_fields || {},
      });
    }
  }, [contact]);

  const loadCompanies = async () => {
    try {
      const response = await crmService.getCompanies({ limit: 100 });
      setCompanies(response.data);
    } catch (error) {
      console.error("Error loading companies:", error);
    }
  };

  const loadOrganizations = async () => {
    try {
      setLoadingOrganizations(true);
      // Get user's organizations from the CRM service
      const response = await crmService.getUserOrganizations();
      setOrganizations(response.data || []);

      // Auto-select first organization if only one exists and no contact is being edited
      if (!contact && response.data && response.data.length === 1) {
        setFormData((prev) => ({
          ...prev,
          organisation_id: response.data[0].id,
        }));
      }

      console.log(
        `✅ Loaded ${response.data?.length || 0} organizations for user`
      );
    } catch (error) {
      console.error("Error loading organizations:", error);
      setErrors((prev) => ({
        ...prev,
        organisation_id: "Failed to load organizations",
      }));
    } finally {
      setLoadingOrganizations(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && tagInput.trim()) {
      e.preventDefault();
      const newTag = tagInput.trim();
      const currentTags = formData.tags || [];
      if (!currentTags.includes(newTag)) {
        setFormData((prev) => ({
          ...prev,
          tags: [...currentTags, newTag],
        }));
      }
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: (prev.tags || []).filter((tag) => tag !== tagToRemove),
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = "Full name is required";
    }

    if (!formData.organisation_id || formData.organisation_id.trim() === "") {
      newErrors.organisation_id = "Organization is required";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (formData.webpage && !/^https?:\/\/.+/.test(formData.webpage)) {
      newErrors.webpage =
        "Please enter a valid URL (starting with http:// or https://)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Clean up form data - convert empty strings to undefined for optional fields
      const cleanedFormData = {
        ...formData,
        // Required fields - keep as is
        full_name: formData.full_name.trim(),
        organisation_id: formData.organisation_id,
        // Optional fields - convert empty strings to undefined
        email: formData.email?.trim() || undefined,
        phone: formData.phone?.trim() || undefined,
        phone2: formData.phone2?.trim() || undefined,
        mobile: formData.mobile?.trim() || undefined,
        fax: formData.fax?.trim() || undefined,
        job_title: formData.job_title?.trim() || undefined,
        company_id: formData.company_id?.trim() || undefined,
        salutation: formData.salutation?.trim() || undefined,
        skypename: formData.skypename?.trim() || undefined,
        webpage: formData.webpage?.trim() || undefined,
        note: formData.note?.trim() || undefined,
      };

      await onSubmit(cleanedFormData);
    } catch (error) {
      console.error("Error submitting form:", error);
      setErrors({
        submit:
          error instanceof Error ? error.message : "Failed to save contact",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Salutation
          </label>
          <select
            name="salutation"
            value={formData.salutation}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select...</option>
            <option value="Mr.">Mr.</option>
            <option value="Ms.">Ms.</option>
            <option value="Mrs.">Mrs.</option>
            <option value="Dr.">Dr.</option>
            <option value="Prof.">Prof.</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            name="full_name"
            value={formData.full_name}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.full_name ? "border-red-300" : "border-gray-300"
            }`}
            placeholder="Enter full name"
          />
          {errors.full_name && (
            <p className="text-red-600 text-sm mt-1">{errors.full_name}</p>
          )}
        </div>
      </div>

      {/* Organization Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Organization *
        </label>
        {loadingOrganizations ? (
          <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
            <span className="text-gray-500">Loading organizations...</span>
          </div>
        ) : (
          <select
            name="organisation_id"
            value={formData.organisation_id}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.organisation_id ? "border-red-300" : "border-gray-300"
            }`}
            disabled={organizations.length === 0}
          >
            <option value="">
              {organizations.length === 0
                ? "No organizations available"
                : "Select organization..."}
            </option>
            {organizations.map((org) => (
              <option key={org.id} value={org.id}>
                {org.name} {org.role && `(${org.role})`}
              </option>
            ))}
          </select>
        )}
        {errors.organisation_id && (
          <p className="text-red-600 text-sm mt-1">{errors.organisation_id}</p>
        )}
        {organizations.length === 0 && !loadingOrganizations && (
          <p className="text-amber-600 text-sm mt-1">
            ⚠️ You are not a member of any organization. Contact your
            administrator.
          </p>
        )}
      </div>

      {/* Contact Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.email ? "border-red-300" : "border-gray-300"
            }`}
            placeholder="Enter email address"
          />
          {errors.email && (
            <p className="text-red-600 text-sm mt-1">{errors.email}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone
          </label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter phone number"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Mobile
          </label>
          <input
            type="tel"
            name="mobile"
            value={formData.mobile}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter mobile number"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Fax
          </label>
          <input
            type="tel"
            name="fax"
            value={formData.fax}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter fax number"
          />
        </div>
      </div>

      {/* Professional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Company
          </label>
          <select
            name="company_id"
            value={formData.company_id}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select company...</option>
            {companies.map((company) => (
              <option key={company.id} value={company.id}>
                {company.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Job Title
          </label>
          <input
            type="text"
            name="job_title"
            value={formData.job_title}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter job title"
          />
        </div>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Skype Name
          </label>
          <input
            type="text"
            name="skypename"
            value={formData.skypename}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter Skype name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Website
          </label>
          <input
            type="url"
            name="webpage"
            value={formData.webpage}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.webpage ? "border-red-300" : "border-gray-300"
            }`}
            placeholder="https://example.com"
          />
          {errors.webpage && (
            <p className="text-red-600 text-sm mt-1">{errors.webpage}</p>
          )}
        </div>
      </div>

      {/* Tags */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Tags
        </label>
        <div className="flex flex-wrap gap-2 mb-2">
          {(formData.tags || []).map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {tag}
              <button
                type="button"
                onClick={() => handleRemoveTag(tag)}
                className="ml-1 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
        <input
          type="text"
          value={tagInput}
          onChange={(e) => setTagInput(e.target.value)}
          onKeyDown={handleAddTag}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Type a tag and press Enter"
        />
      </div>

      {/* Notes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Notes
        </label>
        <textarea
          name="note"
          value={formData.note}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter any additional notes..."
        />
      </div>

      {/* Error Display */}
      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-800 text-sm">{errors.submit}</p>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading
            ? "Saving..."
            : contact
            ? "Update Contact"
            : "Create Contact"}
        </button>
      </div>
    </form>
  );
};

export default ContactForm;
