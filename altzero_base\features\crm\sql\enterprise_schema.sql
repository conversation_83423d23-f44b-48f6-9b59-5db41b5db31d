-- Template for Private Schema Creation
DO
$$
DECLARE
  schema_name text := 'tenant_enterprise1';
BEGIN
  EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', schema_name);

  -- Clone CRM Tables in Private Schema
  EXECUTE format('
    CREATE TABLE %I.crm_contacts (LIKE public.crm_contacts INCLUDING ALL);
    CREATE TABLE %I.crm_companies (LIKE public.crm_companies INCLUDING ALL);
    CREATE TABLE %I.crm_events (LIKE public.crm_events INCLUDING ALL);
    CREATE TABLE %I.crm_groups (LIKE public.crm_groups INCLUDING ALL);
    CREATE TABLE %I.crm_contact_groups (LIKE public.crm_contact_groups INCLUDING ALL);
    CREATE TABLE %I.crm_regions (LIKE public.crm_regions INCLUDING ALL);
    CREATE TABLE %I.crm_pipelines (LIKE public.crm_pipelines INCLUDING ALL);
    CREATE TABLE %I.crm_pipeline_stages (LIKE public.crm_pipeline_stages INCLUDING ALL);
    CREATE TABLE %I.crm_opportunities (LIKE public.crm_opportunities INCLUDING ALL);
    CREATE TABLE %I.crm_activities (LIKE public.crm_activities INCLUDING ALL);
    CREATE TABLE %I.crm_jobs (LIKE public.crm_jobs INCLUDING ALL);
    CREATE TABLE %I.crm_job_applications (LIKE public.crm_job_applications INCLUDING ALL);
  ', schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name, schema_name);
END;
$$;
