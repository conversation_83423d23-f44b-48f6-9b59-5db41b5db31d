import express from "express";
import { ContactService } from "../services/ContactService";
import { CompanyService } from "../services/CompanyService";
import { OpportunityService } from "../services/OpportunityService";
import { ActivityService } from "../services/ActivityService";
import { EventService } from "../services/EventService";

const router = express.Router();

// Initialize services
const contactService = new ContactService();
const companyService = new CompanyService();
const opportunityService = new OpportunityService();
const activityService = new ActivityService();
const eventService = new EventService();

// Health check endpoint
router.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    service: "CRM API",
    timestamp: new Date().toISOString(),
  });
});

// ===== ORGANIZATION ROUTES =====
router.get("/organizations", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;

    if (!userId) {
      return res.status(401).json({ error: "User ID is required" });
    }

    const organizations = await contactService.getUserOrganizations(userId);
    res.json({
      data: organizations,
      total: organizations.length,
      page: 1,
      limit: organizations.length,
    });
  } catch (error) {
    console.error("Error fetching user organizations:", error);
    res.status(500).json({ error: "Failed to fetch organizations" });
  }
});

// ===== CONTACT ROUTES =====
router.get("/contacts", async (req, res) => {
  try {
    const { page = 1, limit = 20, search, tags } = req.query;
    const userId = req.headers["x-user-id"] as string;

    const contacts = await contactService.getContacts({
      page: Number(page),
      limit: Number(limit),
      search: search as string,
      tags: tags as string[],
      userId,
    });
    res.json(contacts);
  } catch (error) {
    console.error("Error fetching contacts:", error);
    res.status(500).json({ error: "Failed to fetch contacts" });
  }
});

router.post("/contacts", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const contact = await contactService.createContact(req.body, userId);
    res.status(201).json(contact);
  } catch (error) {
    console.error("Error creating contact:", error);
    res.status(500).json({ error: "Failed to create contact" });
  }
});

router.get("/contacts/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const contact = await contactService.getContactById(req.params.id, userId);
    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }
    res.json(contact);
  } catch (error) {
    console.error("Error fetching contact:", error);
    res.status(500).json({ error: "Failed to fetch contact" });
  }
});

router.put("/contacts/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const contact = await contactService.updateContact(
      req.params.id,
      req.body,
      userId
    );
    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }
    res.json(contact);
  } catch (error) {
    console.error("Error updating contact:", error);
    res.status(500).json({ error: "Failed to update contact" });
  }
});

router.delete("/contacts/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const success = await contactService.deleteContact(req.params.id, userId);
    if (!success) {
      return res.status(404).json({ error: "Contact not found" });
    }
    res.status(204).send();
  } catch (error) {
    console.error("Error deleting contact:", error);
    res.status(500).json({ error: "Failed to delete contact" });
  }
});

// ===== COMPANY ROUTES =====
router.get("/companies", async (req, res) => {
  try {
    const { page = 1, limit = 20, search } = req.query;
    const userId = req.headers["x-user-id"] as string;

    const companies = await companyService.getCompanies({
      page: Number(page),
      limit: Number(limit),
      search: search as string,
      userId,
    });
    res.json(companies);
  } catch (error) {
    console.error("Error fetching companies:", error);
    res.status(500).json({ error: "Failed to fetch companies" });
  }
});

router.post("/companies", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const company = await companyService.createCompany(req.body, userId);
    res.status(201).json(company);
  } catch (error) {
    console.error("Error creating company:", error);
    res.status(500).json({ error: "Failed to create company" });
  }
});

router.get("/companies/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const company = await companyService.getCompanyById(req.params.id, userId);
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }
    res.json(company);
  } catch (error) {
    console.error("Error fetching company:", error);
    res.status(500).json({ error: "Failed to fetch company" });
  }
});

router.put("/companies/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const company = await companyService.updateCompany(
      req.params.id,
      req.body,
      userId
    );
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }
    res.json(company);
  } catch (error) {
    console.error("Error updating company:", error);
    res.status(500).json({ error: "Failed to update company" });
  }
});

router.delete("/companies/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const success = await companyService.deleteCompany(req.params.id, userId);
    if (!success) {
      return res.status(404).json({ error: "Company not found" });
    }
    res.status(204).send();
  } catch (error) {
    console.error("Error deleting company:", error);
    res.status(500).json({ error: "Failed to delete company" });
  }
});

// ===== OPPORTUNITY ROUTES =====
router.get("/opportunities", async (req, res) => {
  try {
    const { page = 1, limit = 20, stage, assigned_to } = req.query;
    const userId = req.headers["x-user-id"] as string;

    const opportunities = await opportunityService.getOpportunities({
      page: Number(page),
      limit: Number(limit),
      stage: stage as string,
      assignedTo: assigned_to as string,
      userId,
    });
    res.json(opportunities);
  } catch (error) {
    console.error("Error fetching opportunities:", error);
    res.status(500).json({ error: "Failed to fetch opportunities" });
  }
});

router.post("/opportunities", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const opportunity = await opportunityService.createOpportunity(
      req.body,
      userId
    );
    res.status(201).json(opportunity);
  } catch (error) {
    console.error("Error creating opportunity:", error);
    res.status(500).json({ error: "Failed to create opportunity" });
  }
});

router.get("/opportunities/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const opportunity = await opportunityService.getOpportunityById(
      req.params.id,
      userId
    );
    if (!opportunity) {
      return res.status(404).json({ error: "Opportunity not found" });
    }
    res.json(opportunity);
  } catch (error) {
    console.error("Error fetching opportunity:", error);
    res.status(500).json({ error: "Failed to fetch opportunity" });
  }
});

router.put("/opportunities/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const opportunity = await opportunityService.updateOpportunity(
      req.params.id,
      req.body,
      userId
    );
    if (!opportunity) {
      return res.status(404).json({ error: "Opportunity not found" });
    }
    res.json(opportunity);
  } catch (error) {
    console.error("Error updating opportunity:", error);
    res.status(500).json({ error: "Failed to update opportunity" });
  }
});

router.delete("/opportunities/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const success = await opportunityService.deleteOpportunity(
      req.params.id,
      userId
    );
    if (!success) {
      return res.status(404).json({ error: "Opportunity not found" });
    }
    res.status(204).send();
  } catch (error) {
    console.error("Error deleting opportunity:", error);
    res.status(500).json({ error: "Failed to delete opportunity" });
  }
});

// ===== ACTIVITY ROUTES =====
router.get("/activities", async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      contact_id,
      opportunity_id,
    } = req.query;
    const userId = req.headers["x-user-id"] as string;

    const activities = await activityService.getActivities({
      page: Number(page),
      limit: Number(limit),
      type: type as string,
      contactId: contact_id as string,
      opportunityId: opportunity_id as string,
      userId,
    });
    res.json(activities);
  } catch (error) {
    console.error("Error fetching activities:", error);
    res.status(500).json({ error: "Failed to fetch activities" });
  }
});

router.post("/activities", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const activity = await activityService.createActivity(req.body, userId);
    res.status(201).json(activity);
  } catch (error) {
    console.error("Error creating activity:", error);
    res.status(500).json({ error: "Failed to create activity" });
  }
});

// ===== EVENT ROUTES =====
router.get("/events", async (req, res) => {
  try {
    const { page = 1, limit = 20, start_date, end_date } = req.query;
    const userId = req.headers["x-user-id"] as string;

    const events = await eventService.getEvents({
      page: Number(page),
      limit: Number(limit),
      startDate: start_date as string,
      endDate: end_date as string,
      userId,
    });
    res.json(events);
  } catch (error) {
    console.error("Error fetching events:", error);
    res.status(500).json({ error: "Failed to fetch events" });
  }
});

router.post("/events", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const event = await eventService.createEvent(req.body, userId);
    res.status(201).json(event);
  } catch (error) {
    console.error("Error creating event:", error);
    res.status(500).json({ error: "Failed to create event" });
  }
});

router.get("/events/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const event = await eventService.getEventById(req.params.id, userId);
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }
    res.json(event);
  } catch (error) {
    console.error("Error fetching event:", error);
    res.status(500).json({ error: "Failed to fetch event" });
  }
});

router.put("/events/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const event = await eventService.updateEvent(
      req.params.id,
      req.body,
      userId
    );
    if (!event) {
      return res.status(404).json({ error: "Event not found" });
    }
    res.json(event);
  } catch (error) {
    console.error("Error updating event:", error);
    res.status(500).json({ error: "Failed to update event" });
  }
});

router.delete("/events/:id", async (req, res) => {
  try {
    const userId = req.headers["x-user-id"] as string;
    const success = await eventService.deleteEvent(req.params.id, userId);
    if (!success) {
      return res.status(404).json({ error: "Event not found" });
    }
    res.status(204).send();
  } catch (error) {
    console.error("Error deleting event:", error);
    res.status(500).json({ error: "Failed to delete event" });
  }
});

export default router;
