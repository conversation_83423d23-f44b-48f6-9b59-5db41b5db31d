// =====================================================
// FINALIZATION NODE - SCOPINGAI LANGGRAPH
// =====================================================

import { BaseNode, FinalizationNodeResult } from '../types/NodeTypes';
import { ScopingAiWorkflowState, WorkflowContext } from '../types/WorkflowState';

export class FinalizationNode implements BaseNode {
  name = 'finalization';
  description = 'Finalizes the proposal, saves to database, and prepares final output';

  async execute(context: WorkflowContext): Promise<Partial<ScopingAiWorkflowState>> {
    const { state, tools, logger } = context;
    
    logger.info('Starting proposal finalization', {
      workflow_id: state.workflow_id,
      sections_count: state.proposal_sections?.length || 0,
      has_executive_summary: !!(state.executive_summary?.content)
    });

    try {
      const startTime = Date.now();
      
      // Finalize the proposal
      const finalizationResult = await this.finalizeProposal(state, tools, logger);
      
      const processingTime = Date.now() - startTime;

      logger.info('Proposal finalization completed', {
        proposal_id: finalizationResult.final_proposal.id,
        database_saved: finalizationResult.database_saved,
        total_sections: finalizationResult.final_proposal.sections.length
      });

      return {
        final_proposal: finalizationResult.final_proposal,
        status: 'completed',
        current_step: 'finalization_completed',
        progress: 100,
        completed_at: new Date().toISOString(),
        processing_time: (state.processing_time || 0) + processingTime,
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          finalization: finalizationResult
        }
      };

    } catch (error) {
      logger.error('Proposal finalization failed', error);
      throw error;
    }
  }

  private async finalizeProposal(
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<FinalizationNodeResult> {
    
    // Generate unique proposal ID
    const proposalId = this.generateProposalId(state);
    
    // Compile final proposal structure
    const finalProposal = await this.compileProposal(proposalId, state, logger);
    
    // Save to database
    const databaseSaved = await this.saveToDatabase(finalProposal, state, tools, logger);
    
    // Generate file (optional)
    const fileGenerated = await this.generateFile(finalProposal, state, tools, logger);
    
    // Send notification (optional)
    const notificationSent = await this.sendNotification(finalProposal, state, tools, logger);

    return {
      final_proposal: finalProposal,
      database_saved: databaseSaved,
      file_generated: fileGenerated,
      notification_sent: notificationSent
    };
  }

  private generateProposalId(state: ScopingAiWorkflowState): string {
    const timestamp = Date.now();
    const clientPrefix = (state.client?.name || 'client')
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 8);
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    
    return `proposal_${clientPrefix}_${timestamp}_${randomSuffix}`;
  }

  private async compileProposal(
    proposalId: string,
    state: ScopingAiWorkflowState,
    logger: any
  ): Promise<any> {
    
    logger.info('Compiling final proposal structure');

    // Compile all sections in order
    const compiledSections = [];

    // Add executive summary as first section
    if (state.executive_summary?.content) {
      compiledSections.push({
        id: 'executive_summary',
        title: 'Executive Summary',
        content: state.executive_summary.content,
        order: 0,
        word_count: state.executive_summary.content.split(/\s+/).length,
        section_type: 'executive_summary'
      });
    }

    // Add proposal sections
    if (state.proposal_sections) {
      state.proposal_sections.forEach((section, index) => {
        compiledSections.push({
          id: `section_${index + 1}`,
          title: section.title,
          content: section.content,
          order: index + 1,
          word_count: section.word_count,
          quality_score: section.quality_score,
          section_type: 'content_section'
        });
      });
    }

    // Calculate proposal metrics
    const totalWordCount = compiledSections.reduce((sum, section) => sum + (section.word_count || 0), 0);
    const averageQualityScore = this.calculateAverageQuality(state);
    const qualityMetrics = this.compileQualityMetrics(state);

    // Create final proposal object
    const finalProposal = {
      id: proposalId,
      title: this.generateProposalTitle(state),
      client: {
        id: state.client?.id,
        name: state.client?.name,
        industry: state.client?.industry,
        contact_person: state.client?.contact_person
      },
      project: {
        title: state.project?.title,
        description: state.project?.description,
        timeline: state.project?.timeline,
        budget_range: state.project?.budget_range
      },
      sections: compiledSections,
      metadata: {
        workflow_id: state.workflow_id,
        user_id: state.user_id,
        created_at: new Date().toISOString(),
        total_word_count: totalWordCount,
        total_sections: compiledSections.length,
        average_quality_score: averageQualityScore,
        processing_time_ms: state.processing_time,
        data_sources_used: state.data_sources_used || [],
        knowledge_documents_used: state.selected_knowledge_documents?.length || 0,
        api_calls_made: state.api_calls_made?.length || 0,
        total_cost: state.total_cost || 0,
        template_used: state.template?.name,
        generation_method: 'langgraph_workflow'
      },
      quality_metrics: qualityMetrics,
      status: 'completed',
      version: '1.0'
    };

    logger.info('Proposal compilation completed', {
      proposal_id: proposalId,
      sections: compiledSections.length,
      total_words: totalWordCount,
      quality_score: averageQualityScore
    });

    return finalProposal;
  }

  private generateProposalTitle(state: ScopingAiWorkflowState): string {
    const clientName = state.client?.name || 'Client';
    const projectTitle = state.project?.title || 'Project';
    const timestamp = new Date().toLocaleDateString();
    
    return `${projectTitle} - Proposal for ${clientName} (${timestamp})`;
  }

  private calculateAverageQuality(state: ScopingAiWorkflowState): number {
    const qualityReview = state.node_data?.quality_review;
    
    if (qualityReview?.overall_quality_score) {
      return qualityReview.overall_quality_score;
    }

    // Fallback calculation from section scores
    if (state.proposal_sections) {
      const scores = state.proposal_sections
        .map(section => section.quality_score || 70)
        .filter(score => score > 0);
      
      if (scores.length > 0) {
        return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
      }
    }

    return 75; // Default quality score
  }

  private compileQualityMetrics(state: ScopingAiWorkflowState): any {
    const qualityReview = state.node_data?.quality_review;
    
    if (qualityReview?.quality_metrics) {
      return {
        ...qualityReview.quality_metrics,
        overall_score: qualityReview.overall_quality_score,
        passed_threshold: qualityReview.passed_quality_threshold,
        improvement_suggestions: qualityReview.improvement_suggestions || []
      };
    }

    // Fallback quality metrics
    return {
      content_relevance: 75,
      technical_accuracy: 75,
      business_value: 75,
      readability: 75,
      completeness: 75,
      consistency: 75,
      overall_score: this.calculateAverageQuality(state),
      passed_threshold: true,
      improvement_suggestions: []
    };
  }

  private async saveToDatabase(
    proposal: any,
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<boolean> {
    try {
      logger.info('Saving proposal to database', { proposal_id: proposal.id });

      // Save main proposal record
      await tools.database.insert('scopingAi_proposals', {
        id: proposal.id,
        title: proposal.title,
        client_name: proposal.client.name,
        client_industry: proposal.client.industry,
        project_title: proposal.project.title,
        user_id: state.user_id,
        workflow_id: state.workflow_id,
        status: 'completed',
        total_word_count: proposal.metadata.total_word_count,
        total_sections: proposal.metadata.total_sections,
        quality_score: proposal.metadata.average_quality_score,
        processing_time_ms: proposal.metadata.processing_time_ms,
        total_cost: proposal.metadata.total_cost,
        template_used: proposal.metadata.template_used,
        created_at: proposal.metadata.created_at,
        proposal_data: JSON.stringify(proposal)
      });

      // Save individual sections
      for (const section of proposal.sections) {
        await tools.database.insert('scopingAi_proposal_sections', {
          proposal_id: proposal.id,
          section_id: section.id,
          title: section.title,
          content: section.content,
          section_order: section.order,
          word_count: section.word_count,
          quality_score: section.quality_score || null,
          section_type: section.section_type,
          created_at: new Date().toISOString()
        });
      }

      logger.info('Proposal saved to database successfully');
      return true;
    } catch (error) {
      logger.error('Failed to save proposal to database', error);
      return false;
    }
  }

  private async generateFile(
    proposal: any,
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<boolean> {
    try {
      // In a real implementation, this would generate PDF, DOCX, or other formats
      logger.info('File generation would be implemented here', { 
        proposal_id: proposal.id 
      });
      
      // For now, just return true as if file was generated
      return true;
    } catch (error) {
      logger.error('File generation failed', error);
      return false;
    }
  }

  private async sendNotification(
    proposal: any,
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<boolean> {
    try {
      // In a real implementation, this would send email notifications
      logger.info('Notification would be sent here', { 
        proposal_id: proposal.id,
        user_id: state.user_id
      });
      
      // For now, just return true as if notification was sent
      return true;
    } catch (error) {
      logger.error('Notification sending failed', error);
      return false;
    }
  }

  // Generate proposal summary for quick reference
  private generateProposalSummary(proposal: any): string {
    return `Proposal "${proposal.title}" completed successfully:
- Client: ${proposal.client.name} (${proposal.client.industry})
- Project: ${proposal.project.title}
- Sections: ${proposal.sections.length}
- Total Words: ${proposal.metadata.total_word_count}
- Quality Score: ${proposal.metadata.average_quality_score}/100
- Processing Time: ${Math.round(proposal.metadata.processing_time_ms / 1000)}s
- Generated: ${proposal.metadata.created_at}`;
  }

  // Validate final proposal before saving
  private validateFinalProposal(proposal: any): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!proposal.id) issues.push('Missing proposal ID');
    if (!proposal.title) issues.push('Missing proposal title');
    if (!proposal.client?.name) issues.push('Missing client name');
    if (!proposal.sections || proposal.sections.length === 0) issues.push('No sections generated');
    if (proposal.metadata?.total_word_count < 500) issues.push('Proposal is too short');

    return {
      valid: issues.length === 0,
      issues
    };
  }
}
