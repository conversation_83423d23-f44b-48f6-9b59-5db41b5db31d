"use client";

import { useState, useRef, Suspense, useEffect } from "react";
import { Link, useSearchParams } from "react-router-dom";
import {
  ArrowLeft,
  Download,
  Edit,
  FileText,
  Share2,
  Check,
  Loader2,
  ChevronDown,
  Save,
  X,
  Wand2,
  Eye,
  Sparkles,
  Blocks,
  Image as ImageIcon,
  Edit3,
  Pages,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useToast } from "../../../../base/hooks/use-toast";
import { Button } from "../../../../base/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../../../base/components/ui/tabs";
import { Badge } from "../../../../base/components/ui/badge";
import { Card, CardContent } from "../../../../base/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../../base/components/ui/dropdown-menu";
import {
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";

// Import components, types, and utilities
import {
  DocumentProvider,
  useDocumentContext,
} from "../contexts/DocumentContext";
import { SectionEditor } from "../components/SectionEditor";
import { ThemeControls } from "../components/ThemeControls";
import { PagedDocumentPreview } from "../components/PagedDocumentPreview";
import { UnifiedContentEditor } from "../components/documents/generated/UnifiedContentEditor";
import {
  generatePDFFromHTML,
  generatePDFFromContent,
} from "../utils/pdfGenerator";
import { generateDOCX } from "../utils/docxGenerator";
import { DocumentSharingModal } from "../components/sharing/DocumentSharingModal";
import { getUserDocumentPermission } from "../services/documentSharingService";
import { useUser } from "../../../../base/contextapi/UserContext";
import Layout from "../components/layout";

// Main document component wrapped in Suspense
function DocumentContent() {
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { user } = useUser();

  // Use the document context
  const {
    documentData,
    editableSections,
    documentTheme,
    isLoading,
    isSaving,
    isEdited,
    documentDbId,
    clientId,
    filePath,
    pages,
    currentPage,
    setCurrentPage,
    regeneratingSection,
    handleUpdateSectionTitle,
    handleUpdateBlock,
    handleAddBlock,
    handleDeleteBlock,
    handleRegenerateWithAI,
    saveDocumentToSupabase,
    applyThemePreset,
    saveDocumentTheme,
  } = useDocumentContext();

  // Debug logging for pages and sections
  useEffect(() => {
    console.log("DocumentsGenerated: Component state changed");
    console.log("- isLoading:", isLoading);
    console.log("- pages length:", pages?.length || 0);
    console.log("- editableSections length:", editableSections?.length || 0);
    console.log("- pages:", pages);
    console.log("- editableSections:", editableSections);

    // Emergency fix: If loading is done but pages is empty, try to create pages from sections
    if (
      !isLoading &&
      (!pages || pages.length === 0) &&
      editableSections &&
      editableSections.length > 0
    ) {
      console.warn(
        "EMERGENCY FIX: Pages is empty but we have sections, manually triggering page creation"
      );
      // Import the splitIntoPages function
      import("../utils/documentUtils").then(({ splitIntoPages }) => {
        const newPages = splitIntoPages(editableSections);
        console.log("Emergency created pages:", newPages);
        // We can't directly call setPages here since it's from context, but this will help debug
      });
    }
  }, [isLoading, pages, editableSections]);

  // Permission state
  const [userPermission, setUserPermission] = useState<string | null>(null);
  const [permissionLoading, setPermissionLoading] = useState(true);

  // Check user permissions for this document
  useEffect(() => {
    const checkPermissions = async () => {
      if (!user || !documentDbId) {
        setPermissionLoading(false);
        return;
      }

      try {
        const permission = await getUserDocumentPermission(
          documentDbId,
          user.id
        );
        setUserPermission(permission);
      } catch (error) {
        console.error("Error checking permissions:", error);
        setUserPermission(null);
      } finally {
        setPermissionLoading(false);
      }
    };

    checkPermissions();
  }, [user, documentDbId]);

  // Check if user can edit this document
  const canEdit =
    userPermission === null ||
    userPermission === "admin" ||
    userPermission === "edit";
  const isViewOnly = userPermission === "view";

  // Local UI state - Enhanced with new features
  const [activeTab, setActiveTab] = useState("preview");
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isGeneratingDOCX, setIsGeneratingDOCX] = useState(false);
  const [isSettingsPanelOpen, setIsSettingsPanelOpen] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState<
    "theme" | "branding"
  >("theme");
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedContent, setEditedContent] = useState<{
    [sectionId: string]: string;
  }>({});

  // Sharing modal state
  const [isSharingModalOpen, setIsSharingModalOpen] = useState(false);

  const titleFromURL = searchParams?.get("title");
  const preserveOriginal = searchParams?.get("preserveOriginal") === "true";

  const fileInputRef = useRef<HTMLInputElement>(null);
  const logoInputRef = useRef<HTMLInputElement>(null);
  const documentContainerRef = useRef<HTMLDivElement>(null);

  // Function to handle PDF download
  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true);
    try {
      toast({
        title: "Generating PDF",
        description: "Creating your PDF document...",
      });

      const filename = `${documentData.title || "document"}.pdf`;

      // Method 1: HTML to PDF (preserves styling)
      if (documentContainerRef.current) {
        await generatePDFFromHTML(documentContainerRef.current, filename);
      } else {
        // Method 2: Content-based PDF
        const dataToPass = {
          title: documentData.title,
          client: documentData.client,
          type: documentData.type,
          sections: editableSections || documentData.sections,
          editableSections: editableSections,
        };
        generatePDFFromContent(dataToPass, documentTheme, filename);
      }

      toast({
        title: "PDF Downloaded",
        description: "Your document has been saved as PDF.",
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Download Failed",
        description: "There was an error creating your PDF",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Function to handle DOCX download
  const handleDownloadDOCX = async () => {
    setIsGeneratingDOCX(true);
    try {
      toast({
        title: "Generating DOCX",
        description: "Creating your Word document...",
      });

      const filename = `${documentData.title || "document"}.docx`;
      const dataToPass = {
        title: documentData.title,
        client: documentData.client,
        type: documentData.type,
        sections: editableSections || documentData.sections,
        editableSections: editableSections,
      };

      await generateDOCX(dataToPass, documentTheme, filename);

      toast({
        title: "DOCX Downloaded",
        description: "Your document has been saved as Word file.",
      });
    } catch (error) {
      console.error("Error generating DOCX:", error);
      toast({
        title: "Download Failed",
        description: "There was an error creating your DOCX file",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingDOCX(false);
    }
  };

  // Add function to handle image upload
  const handleImageUpload = (sectionId: string) => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
      fileInputRef.current.onchange = (e: Event) => {
        const target = e.target as HTMLInputElement;
        if (target.files && target.files.length > 0) {
          const file = target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
              if (event.target && typeof event.target.result === "string") {
                const imageUrl = event.target.result;

                // Create and add image block using context functions
                const newBlock = {
                  id: `block-${Date.now()}-${Math.random()
                    .toString(36)
                    .slice(2)}`,
                  type: "image" as const,
                  content: imageUrl,
                };

                handleAddBlock(sectionId, "image");

                toast({
                  title: "Image added",
                  description: "The image has been added to the section.",
                });
              }
            };
            reader.readAsDataURL(file);
          }
        }
      };
    }
  };

  // Enhanced section update function
  const handleSectionUpdate = (sectionId: string, updates: any) => {
    // Use the existing context functions to update sections
    if (updates.blocks) {
      // Convert blocks back to text content for compatibility
      const textContent = updates.blocks
        .map((block: any) => {
          switch (block.type) {
            case "text":
              return block.content.text || "";
            case "heading":
              return `# ${block.content.text || ""}`;
            default:
              return "";
          }
        })
        .join("\n\n");

      handleUpdateBlock(sectionId, "content", { content: textContent });
    }
  };

  // Logo display check with proper typing
  const documentLogo = documentTheme?.logo;
  const hasHeaderLogo =
    documentLogo && typeof documentLogo === "string" && documentLogo.length > 0;

  const logoSrc = hasHeaderLogo ? documentLogo : "";
  const logoOpacity = 1;
  const logoFilter = "none";

  return (
    <>
      <div className="px-16 py-6 w-full">
        <header className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/scopingai/proposals">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Link>
              </Button>
              <h1 className="text-2xl font-bold">
                {documentData.title || titleFromURL || "Generated Document"}
              </h1>
              {/* Logo Display in Header */}
              {hasHeaderLogo && logoSrc && (
                <div className="ml-auto">
                  <img
                    src={logoSrc}
                    alt="Logo"
                    className="h-8 object-contain"
                    style={{
                      opacity: logoOpacity,
                      filter: logoFilter,
                    }}
                  />
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSharingModalOpen(true)}
                disabled={isViewOnly}
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Download className="h-4 w-4" />
                    Download
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={handleDownloadPDF}
                    className="cursor-pointer"
                    disabled={isGeneratingPDF || isGeneratingDOCX}
                  >
                    {isGeneratingPDF ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <FileText className="h-4 w-4 mr-2" />
                    )}
                    {isGeneratingPDF ? "Generating PDF..." : "Download as PDF"}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDownloadDOCX}
                    className="cursor-pointer"
                    disabled={isGeneratingPDF || isGeneratingDOCX}
                  >
                    {isGeneratingDOCX ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <FileText className="h-4 w-4 mr-2" />
                    )}
                    {isGeneratingDOCX
                      ? "Generating DOCX..."
                      : "Download as DOCX"}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {canEdit && (
                <Button
                  onClick={saveDocumentToSupabase}
                  disabled={isSaving || !documentData.title.trim()}
                  size="sm"
                  className="gap-2"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  {isSaving
                    ? "Saving..."
                    : !documentData.title.trim()
                    ? "Title Required"
                    : documentDbId || filePath
                    ? "Save"
                    : "Save"}
                </Button>
              )}

              {isViewOnly && (
                <div className="flex items-center px-3 py-1 bg-blue-50 border border-blue-200 rounded text-blue-700 text-sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View Only
                </div>
              )}
            </div>
          </div>
        </header>

        <Tabs
          defaultValue="preview"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="mb-4 relative z-10">
            <TabsTrigger value="preview">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </TabsTrigger>
            {canEdit && (
              <TabsTrigger value="editor" className="relative">
                <Edit3 className="h-4 w-4 mr-2" />
                Content Editor
                {preserveOriginal && (
                  <div className="absolute -top-1 -right-1">
                    <Badge
                      variant="outline"
                      className="text-[0.6rem] h-4 px-1 bg-amber-50 text-amber-800 border-amber-200"
                    >
                      Reference
                    </Badge>
                  </div>
                )}
              </TabsTrigger>
            )}
            <TabsTrigger value="metadata">
              <FileText className="h-4 w-4 mr-2" />
              Document Info
            </TabsTrigger>
            {canEdit && (
              <TabsTrigger value="theme">
                <Sparkles className="h-4 w-4 mr-2" />
                Theme & Branding
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="preview" className="pt-4">
            {isLoading ? (
              <Card>
                <CardContent className="p-6 flex justify-center">
                  <div className="animate-pulse flex flex-col space-y-4 w-full">
                    <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <>
                {/* Settings button positioned at the right end of the preview tab, outside the document */}
                <div className="flex justify-end mb-2 gap-2 relative mt-[-76px]">
                  {/* Edit/Save Button */}
                  {/* {canEdit && (
                    <Button
                      variant={isEditMode ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (isEditMode) {
                          // Save the edited content
                          const updatedSections = [...editableSections];

                          // Update each section with edited content
                          Object.keys(editedContent).forEach((sectionId) => {
                            const sectionIndex = updatedSections.findIndex(
                              (s) => s.id === sectionId
                            );
                            if (sectionIndex >= 0) {
                              // Use the context function to update
                              handleUpdateBlock(sectionId, "content", {
                                content: editedContent[sectionId],
                              });
                            }
                          });

                          // Clear edited content
                          setEditedContent({});

                          // Show success toast
                          toast({
                            title: "Document updated",
                            description: "Your changes have been saved",
                          });
                        }

                        // Toggle edit mode
                        setIsEditMode(!isEditMode);
                      }}
                      className="gap-2"
                    >
                      {isEditMode ? (
                        <>
                          <Check className="h-4 w-4" />
                          Save Changes
                        </>
                      ) : (
                        <>
                          <Edit className="h-4 w-4" />
                          Quick Edit
                        </>
                      )}
                    </Button>
                  )} */}

                  {/* Settings Button */}
                  {canEdit && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setIsSettingsPanelOpen(!isSettingsPanelOpen)
                      }
                      className="gap-2"
                    >
                      <Wand2 className="h-4 w-4" />
                      Settings
                    </Button>
                  )}
                </div>

                {/* Document Preview */}
                <div className="flex gap-6">
                  <div ref={documentContainerRef} className="flex-1">
                    <PagedDocumentPreview
                      document={documentData}
                      pages={pages}
                      currentPage={currentPage}
                      setCurrentPage={setCurrentPage}
                      isEditMode={isEditMode && canEdit}
                      editedContent={editedContent}
                      onEditContent={
                        canEdit
                          ? (sectionId: string, content: string) => {
                              setEditedContent((prev) => ({
                                ...prev,
                                [sectionId]: content,
                              }));
                            }
                          : () => {}
                      }
                    />
                  </div>

                  {/* Settings Panel */}
                  {isSettingsPanelOpen && canEdit && (
                    <div className="w-80 bg-white border rounded-lg p-4 h-fit">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="font-semibold">Theme & Branding</h3>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsSettingsPanelOpen(false)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      <ThemeControls />
                    </div>
                  )}
                </div>
              </>
            )}
          </TabsContent>

          {/* Unified Content Editor Tab */}
          <TabsContent value="editor" className="pt-4">
            <UnifiedContentEditor
              sections={editableSections}
              readonly={isViewOnly}
            />
          </TabsContent>

          <TabsContent value="metadata" className="pt-4">
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">
                      Document Title
                    </label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {documentData.title}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Client</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {documentData.client}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Document Type</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {documentData.type}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Pages</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pages?.length || 1} pages
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Sections</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {documentData.sections?.length || 0} sections total
                    </p>
                  </div>
                  {hasHeaderLogo && logoSrc && (
                    <div>
                      <label className="text-sm font-medium">Logo</label>
                      <div className="mt-2 flex items-center gap-3">
                        <img
                          src={logoSrc}
                          alt="Document Logo"
                          className="h-12 object-contain border rounded"
                        />
                        <div className="text-sm text-muted-foreground">
                          <p>Logo is enabled for this document</p>
                        </div>
                      </div>
                    </div>
                  )}
                  {preserveOriginal && (
                    <div>
                      <label className="text-sm font-medium">Mode</label>
                      <Badge
                        variant="outline"
                        className="ml-2 bg-amber-50 text-amber-800 border-amber-200"
                      >
                        Reference Document
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="theme" className="pt-4">
            <Card>
              <CardContent className="p-6">
                <ThemeControls activeTab="theme" />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Hidden file inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          style={{ display: "none" }}
        />
        <input
          ref={logoInputRef}
          type="file"
          accept="image/*"
          style={{ display: "none" }}
        />
      </div>

      {/* Advanced Document Sharing Modal */}
      <DocumentSharingModal
        isOpen={isSharingModalOpen}
        onClose={() => setIsSharingModalOpen(false)}
        documentId={documentDbId || searchParams?.get("id") || ""}
        documentTitle={
          documentData.title || titleFromURL || "Generated Document"
        }
        canManage={userPermission === null || userPermission === "admin"} // Only document owners can manage sharing
      />
    </>
  );
}

// Default export wrapped in Suspense and provider
export default function DocumentsGenerated() {
  return (
    <Layout>
      <Suspense
        fallback={
          <div className="flex flex-col items-center justify-center min-h-screen">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">Loading document...</p>
          </div>
        }
      >
        <DocumentProvider>
          <DocumentContent />
        </DocumentProvider>
      </Suspense>
    </Layout>
  );
}
