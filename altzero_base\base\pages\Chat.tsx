import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import {
  Send,
  Bot,
  User,
  FileText,
  CheckCircle2,
  X,
  Plus,
  MessageSquare,
  Sparkles,
  Brain,
  Zap,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  Settings,
  Filter,
  Search,
  Clock,
  ExternalLink,
} from "lucide-react";
import { useKnowledge } from "../contextapi/KnowledgeContext";
import {
  useSuccessToast,
  useErrorToast,
  useInfoToast,
} from "../components/ui/toast";
import { knowledgeService } from "../services/knowledgeService";
import {
  ChatMessage,
  ChatSession,
  Document,
  DocumentSource,
} from "../types/knowledge";
import { TOAST_MESSAGES } from "../utils/constants";

const Chat: React.FC = () => {
  const {
    state: {
      documents,
      selectedDocuments,
      currentSession,
      chatSessions,
      isLoading,
    },
    setSelectedDocuments,
    setCurrentSession,
    addMessage,
    setChatSessions,
    setLoading,
  } = useKnowledge();

  const [message, setMessage] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState("");
  const [showDocumentSelector, setShowDocumentSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [sessionSearchQuery, setSessionSearchQuery] = useState("");

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const showSuccess = useSuccessToast();
  const showError = useErrorToast();
  const showInfo = useInfoToast();

  // Load chat sessions on mount
  useEffect(() => {
    loadChatSessions();
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages, streamingMessage]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadChatSessions = async (): Promise<void> => {
    try {
      const sessions = await knowledgeService.getChatSessions();
      setChatSessions(sessions);
    } catch (error) {
      console.error("Failed to load chat sessions:", error);
    }
  };

  const createNewSession = async (): Promise<void> => {
    if (selectedDocuments.length === 0) {
      showError(
        "No documents selected",
        "Please select at least one document to start chatting."
      );
      return;
    }

    try {
      const title = `New Chat - ${new Date().toLocaleDateString()}`;
      const session = await knowledgeService.createChatSession(
        title,
        selectedDocuments
      );
      setCurrentSession(session);
      setChatSessions((prev) => [session, ...prev]);
      showSuccess("New chat session created");
    } catch (error) {
      console.error("Failed to create chat session:", error);
      showError(
        "Failed to create chat session",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  };

  const selectSession = (session: ChatSession): void => {
    setCurrentSession(session);
    setSelectedDocuments(session.selectedDocuments);
  };

  const sendMessage = async (): Promise<void> => {
    if (!message.trim() || isStreaming) return;

    const userMessage = message.trim();
    setMessage("");

    // Create or use existing session
    let session = currentSession;
    if (!session) {
      if (selectedDocuments.length === 0) {
        showError(
          "No documents selected",
          "Please select documents before sending a message."
        );
        return;
      }

      try {
        const title = knowledgeService.generateSessionTitle(userMessage);
        session = await knowledgeService.createChatSession(
          title,
          selectedDocuments
        );
        setCurrentSession(session);
        setChatSessions((prev) => [session, ...prev]);
      } catch (error) {
        console.error("Failed to create session:", error);
        showError("Failed to create chat session");
        return;
      }
    }

    // Add user message
    const userChatMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: userMessage,
      role: "user",
      timestamp: new Date().toISOString(),
    };

    addMessage(session.id, userChatMessage);

    // Start streaming response
    setIsStreaming(true);
    setStreamingMessage("");

    try {
      await knowledgeService.streamChatMessage(
        {
          message: userMessage,
          sessionId: session.id,
          selectedDocuments: session.selectedDocuments,
        },
        // On chunk received
        (chunk: string) => {
          setStreamingMessage((prev) => prev + chunk);
        },
        // On complete
        (response) => {
          const assistantMessage: ChatMessage = {
            id: `assistant-${Date.now()}`,
            content: response.message,
            role: "assistant",
            timestamp: new Date().toISOString(),
            sources: response.sources,
            metadata: response.metadata,
          };

          addMessage(session!.id, assistantMessage);
          setStreamingMessage("");
          setIsStreaming(false);
        },
        // On error
        (error) => {
          console.error("Streaming error:", error);
          showError(TOAST_MESSAGES.CHAT_ERROR, error.message);
          setStreamingMessage("");
          setIsStreaming(false);
        }
      );
    } catch (error) {
      console.error("Failed to send message:", error);
      showError(
        TOAST_MESSAGES.CHAT_ERROR,
        error instanceof Error ? error.message : "Unknown error"
      );
      setIsStreaming(false);
      setStreamingMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent): void => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const copyToClipboard = async (text: string): Promise<void> => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess("Copied to clipboard");
    } catch (error) {
      showError("Failed to copy to clipboard");
    }
  };

  const filteredDocuments = documents.filter(
    (doc) =>
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      doc.status === "success"
  );

  const filteredSessions = chatSessions.filter((session) =>
    session.title.toLowerCase().includes(sessionSearchQuery.toLowerCase())
  );

  const getDocumentIcon = (type: string) => knowledgeService.getFileIcon(type);

  return (
    <div className="h-screen flex bg-gradient-to-br from-background via-background to-muted/20">
      {/* Sidebar */}
      <div className="w-80 border-r border-border/50 glass-effect flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-border/50">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center">
              <Brain className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="text-xl font-bold">AI Chat</h2>
              <p className="text-sm text-muted-foreground">
                Intelligent document analysis
              </p>
            </div>
          </div>

          <button
            onClick={createNewSession}
            className="w-full px-4 py-3 gradient-primary text-primary-foreground rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>New Chat</span>
          </button>
        </div>

        {/* Document Selection */}
        <div className="p-4 border-b border-border/50">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm">Selected Documents</h3>
            <button
              onClick={() => setShowDocumentSelector(!showDocumentSelector)}
              className="text-primary hover:text-primary/80 transition-colors"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>

          {selectedDocuments.length === 0 ? (
            <div className="text-center py-4">
              <FileText className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                No documents selected
              </p>
              <button
                onClick={() => setShowDocumentSelector(true)}
                className="text-xs text-primary hover:text-primary/80 mt-1"
              >
                Select documents
              </button>
            </div>
          ) : (
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {selectedDocuments.map((docId) => {
                const doc = documents.find((d) => d.id === docId);
                if (!doc) return null;
                return (
                  <div
                    key={docId}
                    className="flex items-center space-x-2 p-2 bg-muted/30 rounded-lg"
                  >
                    <span className="text-sm">{getDocumentIcon(doc.type)}</span>
                    <span className="text-xs flex-1 truncate" title={doc.name}>
                      {doc.name}
                    </span>
                    <button
                      onClick={() =>
                        setSelectedDocuments((prev) =>
                          prev.filter((id) => id !== docId)
                        )
                      }
                      className="text-muted-foreground hover:text-destructive transition-colors"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Chat Sessions */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="p-4 border-b border-border/50">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search chats..."
                value={sessionSearchQuery}
                onChange={(e) => setSessionSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-muted/30 border border-border/50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all"
              />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            {filteredSessions.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  No chat sessions
                </p>
              </div>
            ) : (
              filteredSessions.map((session) => (
                <motion.button
                  key={session.id}
                  onClick={() => selectSession(session)}
                  className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                    currentSession?.id === session.id
                      ? "bg-primary/10 border border-primary/20"
                      : "hover:bg-muted/30"
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <h4 className="font-medium text-sm truncate mb-1">
                    {session.title}
                  </h4>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {session.messages.length} messages
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {new Date(session.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                </motion.button>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-6 border-b border-border/50 glass-effect">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-bold">
                {currentSession?.title || "Select or create a chat session"}
              </h1>
              {currentSession && (
                <div className="mt-2 space-y-1">
                  <p className="text-sm text-muted-foreground">
                    {currentSession.messages.length} messages
                  </p>
                  {selectedDocuments.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        <FileText className="w-3 h-3 text-primary" />
                        <span className="text-xs font-medium text-primary">
                          Context: {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {selectedDocuments.slice(0, 2).map((docId, idx) => {
                          const doc = documents.find(d => d.id === docId);
                          return doc ? (
                            <span key={docId} className="text-xs text-muted-foreground">
                              {idx > 0 && "• "}
                              {doc.name.length > 15 ? `${doc.name.substring(0, 15)}...` : doc.name}
                            </span>
                          ) : null;
                        })}
                        {selectedDocuments.length > 2 && (
                          <span className="text-xs text-muted-foreground">
                            • +{selectedDocuments.length - 2} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {selectedDocuments.length > 0 && (
                <div className="flex items-center space-x-1 px-3 py-1 bg-green-500/10 border border-green-500/20 rounded-full">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium text-green-500">
                    RAG Active
                  </span>
                </div>
              )}
              <div className="flex items-center space-x-1 px-3 py-1 bg-primary/10 rounded-full">
                <Sparkles className="w-4 h-4 text-primary" />
                <span className="text-sm font-medium text-primary">
                  AI Powered
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {!currentSession ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center space-y-4">
                <div className="w-20 h-20 gradient-primary rounded-full flex items-center justify-center mx-auto">
                  <MessageSquare className="w-10 h-10 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">
                    Start a conversation
                  </h3>
                  <p className="text-muted-foreground max-w-md">
                    Select documents and create a new chat session to begin
                    analyzing your knowledge base with AI.
                  </p>
                </div>
                <button
                  onClick={() => setShowDocumentSelector(true)}
                  className="px-6 py-3 gradient-primary text-primary-foreground rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                >
                  Select Documents
                </button>
              </div>
            </div>
          ) : (
            <>
              <AnimatePresence>
                {currentSession.messages.map((msg, index) => (
                  <motion.div
                    key={msg.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className={`flex ${
                      msg.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`max-w-[80%] ${
                        msg.role === "user" ? "order-2" : "order-1"
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        {msg.role === "assistant" && (
                          <div className="w-8 h-8 gradient-primary rounded-full flex items-center justify-center flex-shrink-0">
                            <Bot className="w-4 h-4 text-primary-foreground" />
                          </div>
                        )}

                        <div className="flex-1">
                          <div
                            className={`chat-message ${
                              msg.role === "user"
                                ? "chat-bubble-user ml-auto"
                                : "chat-bubble-ai"
                            }`}
                          >
                            {msg.role === "assistant" ? (
                              <ReactMarkdown
                                components={{
                                  code({
                                    node,
                                    inline,
                                    className,
                                    children,
                                    ...props
                                  }) {
                                    const match = /language-(\w+)/.exec(
                                      className || ""
                                    );
                                    return !inline && match ? (
                                      <SyntaxHighlighter
                                        style={oneDark}
                                        language={match[1]}
                                        PreTag="div"
                                        className="rounded-md"
                                        {...props}
                                      >
                                        {String(children).replace(/\n$/, "")}
                                      </SyntaxHighlighter>
                                    ) : (
                                      <code
                                        className="bg-muted px-1 py-0.5 rounded text-sm"
                                        {...props}
                                      >
                                        {children}
                                      </code>
                                    );
                                  },
                                }}
                              >
                                {msg.content}
                              </ReactMarkdown>
                            ) : (
                              <p className="whitespace-pre-wrap">
                                {msg.content}
                              </p>
                            )}
                          </div>

                          {/* Message Actions */}
                          <div className="flex items-center space-x-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              onClick={() => copyToClipboard(msg.content)}
                              className="p-1 hover:bg-muted rounded transition-colors"
                              title="Copy message"
                            >
                              <Copy className="w-3 h-3" />
                            </button>
                            {msg.role === "assistant" && (
                              <>
                                <button
                                  className="p-1 hover:bg-muted rounded transition-colors"
                                  title="Good response"
                                >
                                  <ThumbsUp className="w-3 h-3" />
                                </button>
                                <button
                                  className="p-1 hover:bg-muted rounded transition-colors"
                                  title="Poor response"
                                >
                                  <ThumbsDown className="w-3 h-3" />
                                </button>
                              </>
                            )}
                          </div>

                          {/* Sources */}
                          {msg.sources && msg.sources.length > 0 && (
                            <div className="mt-3 space-y-2">
                              <p className="text-xs font-medium text-muted-foreground">
                                Sources:
                              </p>
                              <div className="space-y-1">
                                {msg.sources.map((source, idx) => (
                                  <div
                                    key={idx}
                                    className="text-xs p-2 bg-muted/30 rounded border border-border/50 hover:bg-muted/50 transition-colors"
                                  >
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="font-medium">
                                        {source.documentName}
                                      </span>
                                      <span className="text-muted-foreground">
                                        {Math.round(
                                          source.relevanceScore * 100
                                        )}
                                        % match
                                      </span>
                                    </div>
                                    <p className="text-muted-foreground line-clamp-2">
                                      {source.excerpt}
                                    </p>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Metadata */}
                          {msg.metadata && (
                            <div className="mt-2 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3 inline mr-1" />
                              {new Date(msg.timestamp).toLocaleTimeString()}
                              {msg.metadata.processingTime && (
                                <span className="ml-2">
                                  • {msg.metadata.processingTime}ms
                                </span>
                              )}
                            </div>
                          )}
                        </div>

                        {msg.role === "user" && (
                          <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <User className="w-4 h-4 text-primary" />
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Streaming Message */}
              {isStreaming && streamingMessage && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="max-w-[80%]">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 gradient-primary rounded-full flex items-center justify-center flex-shrink-0">
                        <Bot className="w-4 h-4 text-primary-foreground" />
                      </div>
                      <div className="chat-message chat-bubble-ai">
                        <ReactMarkdown>{streamingMessage}</ReactMarkdown>
                        <div className="inline-block w-2 h-4 bg-primary animate-pulse ml-1"></div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Input Area */}
        {currentSession && (
          <div className="border-t border-border/50 glass-effect">
            {/* Selected Documents Display */}
            {selectedDocuments.length > 0 && (
              <div className="px-6 pt-4 pb-2">
                <div className="bg-primary/5 border border-primary/20 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-primary" />
                      <span className="text-sm font-medium text-primary">
                        Using {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''} for context
                      </span>
                    </div>
                    <button
                      onClick={() => setShowDocumentSelector(true)}
                      className="text-xs text-muted-foreground hover:text-primary transition-colors"
                    >
                      Change
                    </button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedDocuments.slice(0, 3).map((docId) => {
                      const doc = documents.find(d => d.id === docId);
                      return doc ? (
                        <div
                          key={docId}
                          className="flex items-center space-x-1 px-2 py-1 bg-primary/10 rounded-md"
                        >
                          <FileText className="w-3 h-3 text-primary" />
                          <span className="text-xs text-primary font-medium truncate max-w-[120px]">
                            {doc.name}
                          </span>
                        </div>
                      ) : null;
                    })}
                    {selectedDocuments.length > 3 && (
                      <div className="flex items-center px-2 py-1 bg-muted/50 rounded-md">
                        <span className="text-xs text-muted-foreground">
                          +{selectedDocuments.length - 3} more
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            <div className="p-6">
              <div className="chat-input-container">
                <div className="flex items-end space-x-4">
                  <div className="flex-1">
                    <textarea
                      ref={textareaRef}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Ask anything about your documents..."
                      className="w-full resize-none bg-transparent border-none outline-none placeholder-muted-foreground text-sm max-h-32"
                      rows={1}
                      disabled={isStreaming}
                    />
                  </div>
                  <button
                    onClick={sendMessage}
                    disabled={!message.trim() || isStreaming}
                    className="p-3 gradient-primary text-primary-foreground rounded-lg hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isStreaming ? (
                      <div className="loading-spinner w-4 h-4 border-primary-foreground"></div>
                    ) : (
                      <Send className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Document Selector Modal */}
      <AnimatePresence>
        {showDocumentSelector && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowDocumentSelector(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-card border border-border rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Select Documents</h3>
                <button
                  onClick={() => setShowDocumentSelector(false)}
                  className="p-1 hover:bg-muted rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-muted/30 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all"
                />
              </div>

              <div className="flex-1 overflow-y-auto space-y-2">
                {filteredDocuments.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">
                      No ready documents found
                    </p>
                  </div>
                ) : (
                  filteredDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        selectedDocuments.includes(doc.id)
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      }`}
                      onClick={() => {
                        if (selectedDocuments.includes(doc.id)) {
                          setSelectedDocuments((prev) =>
                            prev.filter((id) => id !== doc.id)
                          );
                        } else {
                          setSelectedDocuments((prev) => [...prev, doc.id]);
                        }
                      }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="text-lg">
                          {getDocumentIcon(doc.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium truncate">{doc.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {knowledgeService.formatFileSize(doc.size)} •{" "}
                            {new Date(doc.uploadedAt).toLocaleDateString()}
                          </p>
                        </div>
                        {selectedDocuments.includes(doc.id) && (
                          <CheckCircle2 className="w-5 h-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
                <p className="text-sm text-muted-foreground">
                  {selectedDocuments.length} document(s) selected
                </p>
                <button
                  onClick={() => setShowDocumentSelector(false)}
                  className="px-4 py-2 gradient-primary text-primary-foreground rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                >
                  Done
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Chat;
