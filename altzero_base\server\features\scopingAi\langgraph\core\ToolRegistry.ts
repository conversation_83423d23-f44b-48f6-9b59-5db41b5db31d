// =====================================================
// TOOL REGISTRY FOR SCOPINGAI LANGGRAPH WORKFLOWS
// =====================================================

import { WorkflowTools, WorkflowConfig } from '../types/WorkflowState';
import { knowledgeBaseService } from '../../services/integration/KnowledgeBaseService';
import { crmContactService } from '../../services/integration/crmContactService';

export class ToolRegistry {
  private config: WorkflowConfig;
  private tools: WorkflowTools | null = null;

  constructor(config: WorkflowConfig) {
    this.config = config;
  }

  // Get all available tools
  async getTools(): Promise<WorkflowTools> {
    if (this.tools) {
      return this.tools;
    }

    this.tools = {
      ai: this.createAITools(),
      knowledgeBase: this.createKnowledgeBaseTools(),
      database: this.createDatabaseTools(),
      crm: this.createCRMTools(),
      market: this.createMarketTools(),
      http: this.createHTTPTools()
    };

    return this.tools;
  }

  // Create AI tools
  private createAITools() {
    return {
      generateText: async (prompt: string, options: any = {}) => {
        try {
          // Use OpenAI for text generation
          const { default: OpenAI } = await import('openai');
          const openai = new OpenAI({
            apiKey: this.config.openai_api_key || process.env.OPENAI_API_KEY
          });

          const response = await openai.chat.completions.create({
            model: options.model || 'gpt-4o-mini',
            messages: [{ role: 'user', content: prompt }],
            temperature: options.temperature || 0.7,
            max_tokens: options.max_tokens || 2000,
            response_format: options.response_format === 'json' ? { type: 'json_object' } : undefined
          });

          return response.choices[0].message.content || '';
        } catch (error) {
          console.error('AI text generation failed:', error);
          throw new Error(`AI text generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      generateStructuredData: async (prompt: string, schema: any) => {
        try {
          const structuredPrompt = `${prompt}\n\nPlease respond with valid JSON that matches this schema: ${JSON.stringify(schema)}`;
          
          const { default: OpenAI } = await import('openai');
          const openai = new OpenAI({
            apiKey: this.config.openai_api_key || process.env.OPENAI_API_KEY
          });

          const response = await openai.chat.completions.create({
            model: 'gpt-4o-mini',
            messages: [{ role: 'user', content: structuredPrompt }],
            temperature: 0.3,
            max_tokens: 2000,
            response_format: { type: 'json_object' }
          });

          const content = response.choices[0].message.content || '{}';
          return JSON.parse(content);
        } catch (error) {
          console.error('AI structured data generation failed:', error);
          throw new Error(`AI structured data generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      analyzeContent: async (content: string, criteria: string[]) => {
        try {
          const prompt = `Analyze the following content based on these criteria: ${criteria.join(', ')}\n\nContent: ${content}\n\nProvide analysis as JSON with scores (0-100) for each criterion and overall recommendations.`;
          
          const { default: OpenAI } = await import('openai');
          const openai = new OpenAI({
            apiKey: this.config.openai_api_key || process.env.OPENAI_API_KEY
          });

          const response = await openai.chat.completions.create({
            model: 'gpt-4o-mini',
            messages: [{ role: 'user', content: prompt }],
            temperature: 0.3,
            max_tokens: 1500,
            response_format: { type: 'json_object' }
          });

          const content_result = response.choices[0].message.content || '{}';
          return JSON.parse(content_result);
        } catch (error) {
          console.error('AI content analysis failed:', error);
          throw new Error(`AI content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };
  }

  // Create Knowledge Base tools
  private createKnowledgeBaseTools() {
    return {
      searchByRequirements: async (requirements: Record<string, string>, userId: string, documentIds?: string[]) => {
        try {
          return await knowledgeBaseService.searchByRequirements(requirements, userId, documentIds);
        } catch (error) {
          console.error('Knowledge base search failed:', error);
          throw new Error(`Knowledge base search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      getUserDocuments: async (userId: string) => {
        try {
          return await knowledgeBaseService.getUserDocuments(userId);
        } catch (error) {
          console.error('Get user documents failed:', error);
          throw new Error(`Get user documents failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      formatDocumentsForAI: (documents: any[]) => {
        try {
          return knowledgeBaseService.formatDocumentsForAI(documents);
        } catch (error) {
          console.error('Format documents failed:', error);
          return '';
        }
      }
    };
  }

  // Create Database tools
  private createDatabaseTools() {
    return {
      query: async (sql: string, params: any[] = []) => {
        try {
          // In a real implementation, this would use your database connection
          console.log('Database query:', sql, params);
          return [];
        } catch (error) {
          console.error('Database query failed:', error);
          throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      insert: async (table: string, data: any) => {
        try {
          console.log('Database insert:', table, data);
          return { id: Date.now(), ...data };
        } catch (error) {
          console.error('Database insert failed:', error);
          throw new Error(`Database insert failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      update: async (table: string, data: any, where: any) => {
        try {
          console.log('Database update:', table, data, where);
          return { affected: 1 };
        } catch (error) {
          console.error('Database update failed:', error);
          throw new Error(`Database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };
  }

  // Create CRM tools
  private createCRMTools() {
    return {
      getClientData: async (clientId: string) => {
        try {
          // Use existing CRM service
          return await crmContactService.getContactById(clientId);
        } catch (error) {
          console.error('Get client data failed:', error);
          return null;
        }
      },

      getIndustryData: async (industry: string) => {
        try {
          // Get industry-specific data
          return {
            industry,
            market_size: 'Unknown',
            growth_rate: 'Unknown',
            key_trends: [],
            challenges: [],
            opportunities: []
          };
        } catch (error) {
          console.error('Get industry data failed:', error);
          return null;
        }
      },

      getCompetitorData: async (industry: string, location?: string) => {
        try {
          // Get competitor information
          return [];
        } catch (error) {
          console.error('Get competitor data failed:', error);
          return [];
        }
      }
    };
  }

  // Create Market tools
  private createMarketTools() {
    return {
      getIndustryTrends: async (industry: string) => {
        try {
          // Mock implementation - in production, this would call market research APIs
          return [
            { trend: 'Digital transformation', impact: 'high', timeline: '2024-2025' },
            { trend: 'AI adoption', impact: 'medium', timeline: '2024-2026' },
            { trend: 'Sustainability focus', impact: 'medium', timeline: '2024-2030' }
          ];
        } catch (error) {
          console.error('Get industry trends failed:', error);
          return [];
        }
      },

      getMarketData: async (industry: string, location?: string) => {
        try {
          // Mock implementation
          return {
            market_size: '$1B+',
            growth_rate: '5-10%',
            key_players: [],
            market_segments: []
          };
        } catch (error) {
          console.error('Get market data failed:', error);
          return null;
        }
      },

      getCompetitiveAnalysis: async (industry: string, competitors: string[]) => {
        try {
          // Mock implementation
          return {
            competitive_landscape: 'Moderate',
            market_leaders: competitors.slice(0, 3),
            opportunities: ['Market gap in X', 'Underserved segment Y'],
            threats: ['New entrants', 'Price competition']
          };
        } catch (error) {
          console.error('Get competitive analysis failed:', error);
          return null;
        }
      }
    };
  }

  // Create HTTP tools
  private createHTTPTools() {
    return {
      get: async (url: string, options: any = {}) => {
        try {
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'User-Agent': 'ScopingAI-Workflow/1.0',
              ...options.headers
            },
            ...options
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          return await response.text();
        } catch (error) {
          console.error('HTTP GET failed:', error);
          throw new Error(`HTTP GET failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      },

      post: async (url: string, data: any, options: any = {}) => {
        try {
          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'ScopingAI-Workflow/1.0',
              ...options.headers
            },
            body: JSON.stringify(data),
            ...options
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          return await response.json();
        } catch (error) {
          console.error('HTTP POST failed:', error);
          throw new Error(`HTTP POST failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };
  }

  // Get tool availability status
  getToolStatus(): Record<string, boolean> {
    return {
      ai: !!(this.config.openai_api_key || process.env.OPENAI_API_KEY),
      knowledgeBase: knowledgeBaseService.isAvailable(),
      database: true, // Always available (mock)
      crm: true, // Always available
      market: true, // Always available (mock)
      http: true // Always available
    };
  }
}
