import { Request, Response, NextFunction } from 'express';
import { environment } from "../apps/config/environment";

/**
 * Middleware to validate API key in requests
 */
export const validateApiKey = (
  req: Request,
  res: Response,
  next: NextFunction
): void | Response => {
  const apiKey = req.query.apiKey || req.headers["x-api-key"];

  if (!apiKey) {
    return res.status(401).json({ error: "API key is required" });
  }

  // Check against API key from environment
  if (apiKey !== environment.apiKey) {
    return res.status(403).json({ error: "Invalid API key" });
  }

  next();
};
