import React from "react";
import { useUser } from "../contextapi/UserContext";
import { SubscriptionCard } from "../components/dashboard/SubscriptionCard";

const Dashboard = () => {
  const { user } = useUser();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-card rounded-lg shadow-md p-6 text-card-foreground">
        <h1 className="text-2xl font-bold mb-6">Dashboard</h1>

        <div className="bg-secondary rounded-lg p-4 mb-6">
          <h2 className="text-lg font-medium text-primary mb-2">
            Welcome back, {user?.email?.split("@")[0] || "User"}!
          </h2>
          <p className="text-primary">
            This is your personal dashboard where you can manage your account
            and access AltZero features.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Subscription Card */}
          <SubscriptionCard />

          {/* Analytics Card */}
          <div className="dashboard-card">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-secondary rounded-lg mr-4">
                <svg
                  className="w-6 h-6 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-lg">Analytics</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              View your usage statistics and performance metrics
            </p>
          </div>

          {/* Documents Card */}
          <div className="dashboard-card">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-secondary rounded-lg mr-4">
                <svg
                  className="w-6 h-6 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-lg">Documents</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Access and manage your documents and files
            </p>
          </div>

          {/* Settings Card */}
          <div className="dashboard-card">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-secondary rounded-lg mr-4">
                <svg
                  className="w-6 h-6 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-lg">Settings</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Configure your account settings and preferences
            </p>
          </div>
        </div>

        <div className="mt-8 border-t pt-6">
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <div className="bg-muted rounded-lg p-4">
            <p className="text-muted-foreground text-center py-4">
              No recent activity to display
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
