import React from 'react';
import { RouteObject } from 'react-router-dom';
import { PluginModule, NavigationItem } from '../../plugins/types';
import Copilot<PERSON>hat from './pages/CopilotChat';

// Define routes for the AI Chat plugin
const routes: RouteObject[] = [
  {
    path: '/copilot-chat',
    element: React.createElement(CopilotChat)
  }
];

// Define navigation items
const navigation: NavigationItem[] = [
  {
    name: 'AI Chat',
    route: '/copilot-chat',
    icon: 'MessageSquare',
    order: 4,
    permissions: ['chat:read']
  }
];

// AI Chat plugin module
const aichatPlugin: PluginModule = {
  routes,
  navigation,
  providers: [], // No specific providers for AI Chat yet
  config: {
    name: 'AI Chat',
    version: '1.0.0',
    description: 'CopilotKit-powered AI assistant'
  },
  initialize: async () => {
    console.log('🤖 AI Chat plugin initialized');
  },
  cleanup: async () => {
    console.log('🤖 AI Chat plugin cleaned up');
  }
};

export default aichatPlugin; 