"use client";

import React, { useState, useCallback } from "react";
import { useDroppable } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button } from "../../../../../base/components/ui/button";
import { Card, CardContent } from "../../../../../base/components/ui/card";
import { Badge } from "../../../../../base/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import {
  Plus,
  Trash2,
  Settings,
  Columns,
  Columns2,
  Columns3,
  Grid,
  GripVertical,
  ArrowLeft,
  ArrowRight,
} from "lucide-react";
import { EnhancedSortableBlock, EnhancedBlock } from "./EnhancedDragDropEditor";
import { useToast } from "../../../../../base/hooks/use-toast";

interface ColumnLayoutProps {
  block: EnhancedBlock;
  sectionId: string;
  onUpdate: (blockId: string, updates: Partial<EnhancedBlock>) => void;
  onDelete: (blockId: string) => void;
  onDuplicate: (blockId: string) => void;
  readonly?: boolean;
}

// Enhanced Droppable Column Component
function EnhancedDroppableColumn({
  columnId,
  blocks,
  sectionId,
  onUpdate,
  onDelete,
  onDuplicate,
  onAddBlock,
  readonly = false,
  columnWidth = "1fr",
  onUpdateColumnWidth,
  totalColumns = 2,
}: {
  columnId: string;
  blocks: EnhancedBlock[];
  sectionId: string;
  onUpdate: (blockId: string, updates: Partial<EnhancedBlock>) => void;
  onDelete: (blockId: string) => void;
  onDuplicate: (blockId: string) => void;
  onAddBlock: (type: EnhancedBlock["type"], columnId?: string) => void;
  readonly?: boolean;
  columnWidth?: string;
  onUpdateColumnWidth?: (columnId: string, width: string) => void;
  totalColumns?: number;
}) {
  const { setNodeRef, isOver } = useDroppable({
    id: columnId,
    data: {
      type: "column",
      columnId,
      sectionId,
    },
  });

  const [isEditingWidth, setIsEditingWidth] = useState(false);
  const [tempWidth, setTempWidth] = useState(columnWidth);

  const handleWidthSave = () => {
    onUpdateColumnWidth?.(columnId, tempWidth);
    setIsEditingWidth(false);
  };

  // Convert flex values to percentages for display
  const getPercentageWidth = () => {
    const flexValue = parseFloat(columnWidth.replace("fr", ""));
    const percentage = (flexValue / totalColumns) * 100;
    return Math.round(percentage);
  };

  // Predefined width options based on column count
  const getWidthOptions = () => {
    switch (totalColumns) {
      case 2:
        return [
          { label: "Equal (50%)", value: "1fr" },
          { label: "Narrow (33%)", value: "0.67fr" },
          { label: "Wide (67%)", value: "1.33fr" },
        ];
      case 3:
        return [
          { label: "Equal (33%)", value: "1fr" },
          { label: "Narrow (25%)", value: "0.75fr" },
          { label: "Wide (42%)", value: "1.25fr" },
        ];
      default:
        return [{ label: "Full Width", value: "1fr" }];
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`h-full min-h-[200px] relative transition-all duration-200 ${
        isOver ? "bg-blue-50 ring-2 ring-blue-200" : "bg-gray-50/50"
      }`}
      style={{ flex: columnWidth }}
    >
      {/* Column Header */}
      {!readonly && (
        <div className="flex items-center justify-between p-2 bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Column {columnId.split("-").pop()}
            </Badge>
            <span className="text-xs text-gray-500">
              {getPercentageWidth()}%
            </span>
          </div>

          {onUpdateColumnWidth && (
            <div className="flex items-center gap-1">
              {isEditingWidth ? (
                <div className="flex items-center gap-1">
                  <Select value={tempWidth} onValueChange={setTempWidth}>
                    <SelectTrigger className="w-24 h-6 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {getWidthOptions().map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleWidthSave}
                    className="h-6 px-2"
                  >
                    ✓
                  </Button>
                </div>
              ) : (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setTempWidth(columnWidth);
                    setIsEditingWidth(true);
                  }}
                  className="h-6 px-2"
                >
                  <Settings className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Column Content */}
      <div className="p-3 space-y-3">
        <SortableContext
          items={blocks.map((b) => b.id)}
          strategy={verticalListSortingStrategy}
        >
          {blocks.map((block) => (
            <EnhancedSortableBlock
              key={block.id}
              block={block}
              sectionId={sectionId}
              onUpdate={onUpdate}
              onDelete={onDelete}
              onDuplicate={onDuplicate}
              readonly={readonly}
              isInColumn={true}
              columnId={columnId}
            />
          ))}
        </SortableContext>

        {/* Drop Zone Indicator */}
        {isOver && blocks.length === 0 && (
          <div className="border-2 border-dashed border-blue-300 rounded-lg p-8 text-center">
            <div className="text-blue-500 mb-2">
              <Plus className="h-8 w-8 mx-auto" />
            </div>
            <p className="text-blue-600 font-medium">Drop blocks here</p>
          </div>
        )}

        {/* Add Block Button */}
        {!readonly && (
          <div className="pt-2">
            <AddBlockMenu onAddBlock={(type) => onAddBlock(type, columnId)} />
          </div>
        )}
      </div>
    </div>
  );
}

// Add Block Menu Component
function AddBlockMenu({
  onAddBlock,
}: {
  onAddBlock: (type: EnhancedBlock["type"]) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);

  const blockTypes = [
    {
      type: "text" as const,
      icon: Type,
      label: "Text",
      description: "Add paragraph text",
    },
    {
      type: "heading" as const,
      icon: Grid,
      label: "Heading",
      description: "Add a heading",
    },
    {
      type: "list" as const,
      icon: Plus,
      label: "List",
      description: "Add a bulleted list",
    },
    {
      type: "image" as const,
      icon: Plus,
      label: "Image",
      description: "Add an image",
    },
  ];

  return (
    <div className="relative">
      <Button
        size="sm"
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full border-dashed hover:border-solid"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add Block
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-lg shadow-lg z-20">
          {blockTypes.map((blockType) => (
            <button
              key={blockType.type}
              onClick={() => {
                onAddBlock(blockType.type);
                setIsOpen(false);
              }}
              className="w-full text-left p-3 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg border-b last:border-b-0"
            >
              <div className="flex items-center gap-3">
                <blockType.icon className="h-4 w-4 text-gray-500" />
                <div>
                  <div className="font-medium text-sm">{blockType.label}</div>
                  <div className="text-xs text-gray-500">
                    {blockType.description}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Backdrop to close menu */}
      {isOpen && (
        <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
}

// Main Enhanced Column Layout Component
export function EnhancedColumnLayout({
  block,
  sectionId,
  onUpdate,
  onDelete,
  onDuplicate,
  readonly = false,
}: ColumnLayoutProps) {
  const [showSettings, setShowSettings] = useState(false);
  const { toast } = useToast();

  // Ensure we have proper column structure
  const columns = block.layout?.columns || 2;
  const columnData = block.layout?.children || {};
  const columnWidths = block.layout?.columnWidths || Array(columns).fill("1fr");

  // Generate column IDs
  const columnIds = Array.from(
    { length: columns },
    (_, i) => `${block.id}-col-${i + 1}`
  );

  // Ensure all columns exist
  columnIds.forEach((colId) => {
    if (!columnData[colId]) {
      columnData[colId] = [];
    }
  });

  const handleColumnCountChange = useCallback(
    (newCount: number) => {
      const newColumnData = { ...columnData };
      const newColumnWidths = [...columnWidths];

      if (newCount > columns) {
        // Adding columns
        for (let i = columns; i < newCount; i++) {
          const newColId = `${block.id}-col-${i + 1}`;
          newColumnData[newColId] = [];
          newColumnWidths.push("1fr");
        }
      } else if (newCount < columns) {
        // Removing columns - move blocks to remaining columns
        const blocksToMove: EnhancedBlock[] = [];

        for (let i = newCount; i < columns; i++) {
          const colIdToRemove = `${block.id}-col-${i + 1}`;
          if (newColumnData[colIdToRemove]) {
            blocksToMove.push(...newColumnData[colIdToRemove]);
            delete newColumnData[colIdToRemove];
          }
        }

        // Distribute moved blocks across remaining columns
        if (blocksToMove.length > 0 && newCount > 0) {
          const targetColId = `${block.id}-col-${newCount}`;
          newColumnData[targetColId] = [
            ...(newColumnData[targetColId] || []),
            ...blocksToMove,
          ];
        }

        // Adjust column widths
        newColumnWidths.splice(newCount);
      }

      onUpdate(block.id, {
        layout: {
          ...block.layout,
          columns: newCount,
          children: newColumnData,
          columnWidths: newColumnWidths,
        },
      });

      toast({
        title: "Columns Updated",
        description: `Changed to ${newCount} column${newCount !== 1 ? "s" : ""}`,
        duration: 1500,
      });
    },
    [block.id, columns, columnData, columnWidths, onUpdate, toast]
  );

  const handleAddBlockToColumn = useCallback(
    (type: EnhancedBlock["type"], columnId?: string) => {
      const targetColumnId = columnId || `${block.id}-col-1`;
      const newBlock: EnhancedBlock = {
        id: `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type,
        content: getDefaultContent(type),
        style: { textAlign: "left" },
      };

      const newColumnData = { ...columnData };
      newColumnData[targetColumnId] = [
        ...(newColumnData[targetColumnId] || []),
        newBlock,
      ];

      onUpdate(block.id, {
        layout: {
          ...block.layout,
          children: newColumnData,
        },
      });
    },
    [block.id, block.layout, columnData, onUpdate]
  );

  const handleUpdateColumnWidth = useCallback(
    (columnId: string, width: string) => {
      const columnIndex = columnIds.indexOf(columnId);
      if (columnIndex === -1) return;

      const newWidths = [...columnWidths];
      newWidths[columnIndex] = width;

      onUpdate(block.id, {
        layout: {
          ...block.layout,
          columnWidths: newWidths,
        },
      });
    },
    [block.id, block.layout, columnIds, columnWidths, onUpdate]
  );

  const handleBlockUpdate = useCallback(
    (blockId: string, updates: Partial<EnhancedBlock>) => {
      const newColumnData = { ...columnData };

      // Find and update the block in the correct column
      for (const colId of columnIds) {
        const columnBlocks = newColumnData[colId] || [];
        const blockIndex = columnBlocks.findIndex((b) => b.id === blockId);

        if (blockIndex !== -1) {
          newColumnData[colId] = columnBlocks.map((b) =>
            b.id === blockId ? { ...b, ...updates } : b
          );
          break;
        }
      }

      onUpdate(block.id, {
        layout: {
          ...block.layout,
          children: newColumnData,
        },
      });
    },
    [block.id, block.layout, columnData, columnIds, onUpdate]
  );

  const handleBlockDelete = useCallback(
    (blockId: string) => {
      const newColumnData = { ...columnData };

      // Find and remove the block from the correct column
      for (const colId of columnIds) {
        const columnBlocks = newColumnData[colId] || [];
        const blockIndex = columnBlocks.findIndex((b) => b.id === blockId);

        if (blockIndex !== -1) {
          newColumnData[colId] = columnBlocks.filter((b) => b.id !== blockId);
          break;
        }
      }

      onUpdate(block.id, {
        layout: {
          ...block.layout,
          children: newColumnData,
        },
      });

      toast({
        title: "Block Removed",
        description: "Block has been removed from the column.",
        duration: 1500,
      });
    },
    [block.id, block.layout, columnData, columnIds, onUpdate, toast]
  );

  const handleBlockDuplicate = useCallback(
    (blockId: string) => {
      const newColumnData = { ...columnData };

      // Find the block and duplicate it in the same column
      for (const colId of columnIds) {
        const columnBlocks = newColumnData[colId] || [];
        const blockIndex = columnBlocks.findIndex((b) => b.id === blockId);

        if (blockIndex !== -1) {
          const originalBlock = columnBlocks[blockIndex];
          const duplicatedBlock = {
            ...originalBlock,
            id: `block-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          };

          newColumnData[colId] = [
            ...columnBlocks.slice(0, blockIndex + 1),
            duplicatedBlock,
            ...columnBlocks.slice(blockIndex + 1),
          ];
          break;
        }
      }

      onUpdate(block.id, {
        layout: {
          ...block.layout,
          children: newColumnData,
        },
      });

      toast({
        title: "Block Duplicated",
        description: "Block has been duplicated successfully.",
        duration: 1500,
      });
    },
    [block.id, block.layout, columnData, columnIds, onUpdate, toast]
  );

  if (readonly) {
    return (
      <div className="mb-4">
        <div
          className="grid gap-4"
          style={{ gridTemplateColumns: columnWidths.join(" ") }}
        >
          {columnIds.map((colId) => (
            <div key={colId} className="space-y-3">
              {(columnData[colId] || []).map((columnBlock) => (
                <EnhancedSortableBlock
                  key={columnBlock.id}
                  block={columnBlock}
                  sectionId={sectionId}
                  onUpdate={handleBlockUpdate}
                  onDelete={handleBlockDelete}
                  onDuplicate={handleBlockDuplicate}
                  readonly={true}
                  isInColumn={true}
                  columnId={colId}
                />
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 group">
      <Card className="border border-gray-200 hover:shadow-md transition-shadow">
        <CardContent className="p-0">
          {/* Column Layout Header */}
          <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                {columns === 1 && <Columns className="h-5 w-5 text-gray-600" />}
                {columns === 2 && (
                  <Columns2 className="h-5 w-5 text-gray-600" />
                )}
                {columns === 3 && (
                  <Columns3 className="h-5 w-5 text-gray-600" />
                )}
                <Badge variant="outline">
                  {columns} Column{columns !== 1 ? "s" : ""} Layout
                </Badge>
              </div>

              <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Select
                value={columns.toString()}
                onValueChange={(value) =>
                  handleColumnCountChange(parseInt(value))
                }
              >
                <SelectTrigger className="w-28">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Column</SelectItem>
                  <SelectItem value="2">2 Columns</SelectItem>
                  <SelectItem value="3">3 Columns</SelectItem>
                </SelectContent>
              </Select>

              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={() => onDelete(block.id)}
                className="text-red-500 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <div className="p-4 bg-yellow-50 border-b">
              <h4 className="font-medium text-sm mb-3">
                Column Layout Settings
              </h4>
              <div className="space-y-3">
                <div>
                  <label className="text-sm text-gray-600">
                    Background Color
                  </label>
                  <input
                    type="color"
                    value={block.style?.backgroundColor || "#ffffff"}
                    onChange={(e) => {
                      onUpdate(block.id, {
                        style: {
                          ...block.style,
                          backgroundColor: e.target.value,
                        },
                      });
                    }}
                    className="w-full h-8 mt-1 rounded border"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Columns Container */}
          <div
            className="grid gap-px bg-gray-200"
            style={{
              gridTemplateColumns: columnWidths.join(" "),
              minHeight: "300px",
            }}
          >
            {columnIds.map((colId, index) => (
              <EnhancedDroppableColumn
                key={colId}
                columnId={colId}
                blocks={columnData[colId] || []}
                sectionId={sectionId}
                onUpdate={handleBlockUpdate}
                onDelete={handleBlockDelete}
                onDuplicate={handleBlockDuplicate}
                onAddBlock={handleAddBlockToColumn}
                readonly={readonly}
                columnWidth={columnWidths[index]}
                onUpdateColumnWidth={handleUpdateColumnWidth}
                totalColumns={columns}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper function to get default content based on block type
function getDefaultContent(type: EnhancedBlock["type"]): any {
  switch (type) {
    case "heading":
      return "New Heading";
    case "text":
      return "Enter your text here...";
    case "list":
      return ["New list item"];
    case "image":
      return "";
    default:
      return "";
  }
}
