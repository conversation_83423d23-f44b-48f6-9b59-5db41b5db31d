import { seoConfig, SEOProviderType, SEOFunctionType } from '../../config/seoConfig';
import { htmlAnalysisService, HTMLAnalysisResult, HTMLIssueLocation } from './htmlAnalysisService';

export interface SEOMetrics {
  // Technical SEO
  technicalScore: number;
  technicalIssues: {
    metaTags: number;
    headingStructure: number;
    imageOptimization: number;
    linkQuality: number;
    pageStructure: number;
  };
  
  // Content SEO
  contentScore: number;
  contentMetrics: {
    wordCount: number;
    keywordDensity: number;
    readabilityScore: number;
    contentQuality: number;
    uniqueWords: number;
    contentDensity: number;
  };
  
  // Performance SEO
  performanceScore: number;
  performanceMetrics: {
    pageSize: number;
    loadTime: number;
    coreWebVitals: {
      lcp: number; // Largest Contentful Paint
      fid: number; // First Input Delay
      cls: number; // Cumulative Layout Shift
    };
  };
  
  // Backlink SEO (premium providers only)
  backlinkScore?: number;
  backlinkMetrics?: {
    domainAuthority: number;
    backlinksCount: number;
    referringDomains: number;
    qualityScore: number;
  };
  
  // Keyword SEO (premium providers only)
  keywordScore?: number;
  keywordMetrics?: {
    targetKeywords: string[];
    keywordRankings: { [keyword: string]: number };
    searchVolume: { [keyword: string]: number };
    competitionScore: number;
  };
}

export interface SEOScoringResult {
  url: string;
  analyzedAt: Date;
  overallScore: number;
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D+' | 'D' | 'F';
  metrics: SEOMetrics;
  htmlAnalysis: HTMLAnalysisResult;
  recommendations: SEORecommendation[];
  providerBreakdown: {
    [key in SEOFunctionType]?: {
      provider: SEOProviderType;
      score: number;
      confidence: number;
    };
  };
  processingTime: number;
}

export interface SEORecommendation {
  category: 'technical' | 'content' | 'performance' | 'backlink' | 'keyword';
  priority: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  action: string;
  expectedImpact: string;
  estimatedEffort: 'low' | 'medium' | 'high';
  relatedIssues: string[];
}

class SEOScoringService {
  private readonly SCORING_WEIGHTS = {
    technical: 0.30,    // 30% - HTML structure, meta tags, technical issues
    content: 0.25,      // 25% - Content quality, keywords, readability  
    performance: 0.20,  // 20% - Page speed, core web vitals
    backlink: 0.15,     // 15% - Domain authority, backlink quality (premium)
    keyword: 0.10       // 10% - Keyword rankings, search volume (premium)
  };

  private readonly GRADE_THRESHOLDS = {
    'A+': 95,
    'A': 90,
    'B+': 85,
    'B': 80,
    'C+': 75,
    'C': 70,
    'D+': 65,
    'D': 60,
    'F': 0
  };

  /**
   * Perform comprehensive SEO scoring analysis
   */
  async performSEOScoring(htmlContent: string, url: string): Promise<SEOScoringResult> {
    const startTime = Date.now();
    
    try {
      // Step 1: Perform HTML analysis
      const htmlAnalysis = await htmlAnalysisService.analyzeHTML(htmlContent);
      
      // Step 2: Calculate technical scores from HTML analysis
      const technicalMetrics = this.calculateTechnicalMetrics(htmlAnalysis);
      
      // Step 3: Calculate content scores
      const contentMetrics = this.calculateContentMetrics(htmlContent, htmlAnalysis);
      
      // Step 4: Get provider-specific scores configuration
      const providerBreakdown: any = {};
      const configuredFunctions = seoConfig.getAllConfiguredFunctions();
      
      // Step 5: Calculate performance scores (try real API first, fallback to HTML analysis)
      let performanceMetrics;
      const realPerformanceData = await this.getPageSpeedMetrics(url);
      
      if (realPerformanceData) {
        performanceMetrics = realPerformanceData;
        providerBreakdown.pagespeed = {
          provider: 'lighthouse',
          score: realPerformanceData.score,
          confidence: realPerformanceData.confidence
        };
      } else {
        // Fallback to HTML-based performance estimation
        performanceMetrics = this.calculatePerformanceMetrics(htmlAnalysis);
        if (configuredFunctions.pagespeed) {
          providerBreakdown.pagespeed = {
            provider: configuredFunctions.pagespeed.provider,
            score: performanceMetrics.score,
            confidence: 0.85
          };
        }
      }
      
      // Step 6: Initialize metrics object
      const metrics: SEOMetrics = {
        technicalScore: technicalMetrics.score,
        technicalIssues: technicalMetrics.issues,
        contentScore: contentMetrics.score,
        contentMetrics: contentMetrics.metrics,
        performanceScore: performanceMetrics.score,
        performanceMetrics: performanceMetrics.metrics
      };

      // Add backlink analysis if provider is configured
      if (configuredFunctions.backlink) {
        const backlinkResult = await this.analyzeBacklinks(url, configuredFunctions.backlink.provider);
        if (backlinkResult) {
          metrics.backlinkScore = backlinkResult.score;
          metrics.backlinkMetrics = backlinkResult.metrics;
          providerBreakdown.backlink = {
            provider: configuredFunctions.backlink.provider,
            score: backlinkResult.score,
            confidence: backlinkResult.confidence
          };
        }
      }

      // Add keyword analysis if provider is configured
      if (configuredFunctions.keyword) {
        const keywordResult = await this.analyzeKeywords(htmlContent, url, configuredFunctions.keyword.provider);
        if (keywordResult) {
          metrics.keywordScore = keywordResult.score;
          metrics.keywordMetrics = keywordResult.metrics;
          providerBreakdown.keyword = {
            provider: configuredFunctions.keyword.provider,
            score: keywordResult.score,
            confidence: keywordResult.confidence
          };
        }
      }

      // Add generator and pagespeed provider info
      if (configuredFunctions.generator) {
        providerBreakdown.generator = {
          provider: configuredFunctions.generator.provider,
          score: technicalMetrics.score,
          confidence: 0.9
        };
      }

      // Step 7: Calculate overall score
      const overallScore = this.calculateOverallScore(metrics);
      
      // Step 8: Determine grade
      const grade = this.calculateGrade(overallScore);
      
      // Step 9: Generate recommendations
      const recommendations = this.generateRecommendations(htmlAnalysis, metrics);
      
      const processingTime = Date.now() - startTime;
      
      return {
        url,
        analyzedAt: new Date(),
        overallScore,
        grade,
        metrics,
        htmlAnalysis,
        recommendations,
        providerBreakdown,
        processingTime
      };
      
    } catch (error) {
      throw new Error(`SEO scoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate technical SEO metrics from HTML analysis
   */
  private calculateTechnicalMetrics(htmlAnalysis: HTMLAnalysisResult): {
    score: number;
    issues: SEOMetrics['technicalIssues'];
  } {
    const issues = htmlAnalysis.issues;
    
    // Calculate scores for each technical area
    const metaTagScore = this.calculateMetaTagScore(issues);
    const headingScore = this.calculateHeadingScore(issues);
    const imageScore = this.calculateImageScore(issues);
    const linkScore = this.calculateLinkScore(issues);
    const structureScore = this.calculateStructureScore(issues);
    
    // Weight the technical scores
    const technicalScore = (
      metaTagScore * 0.30 +
      headingScore * 0.25 +
      imageScore * 0.20 +
      linkScore * 0.15 +
      structureScore * 0.10
    );

    return {
      score: Math.round(technicalScore),
      issues: {
        metaTags: Math.round(metaTagScore),
        headingStructure: Math.round(headingScore),
        imageOptimization: Math.round(imageScore),
        linkQuality: Math.round(linkScore),
        pageStructure: Math.round(structureScore)
      }
    };
  }

  /**
   * Calculate content scores based on HTML content and analysis
   */
  private calculateContentMetrics(htmlContent: string, htmlAnalysis: HTMLAnalysisResult): {
    score: number;
    metrics: SEOMetrics['contentMetrics'];
  } {
    // Use DOMParser for browser compatibility instead of JSDOM
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    const textContent = doc.body?.textContent || '';
    const wordCount = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    
    // Calculate content density
    const htmlLength = htmlContent.length;
    const textLength = textContent.length;
    const contentDensity = textLength / htmlLength;
    
    // Basic content scoring
    let contentScore = 100;
    
    // Penalize based on HTML analysis issues
    const criticalIssues = htmlAnalysis.issuesByType.critical;
    const warningIssues = htmlAnalysis.issuesByType.warning;
    
    contentScore -= criticalIssues * 15; // -15 per critical
    contentScore -= warningIssues * 5;   // -5 per warning
    
    // Penalize for thin content
    if (wordCount < 300) {
      contentScore -= 20;
    }
    
    // Penalize for low content density (too much HTML markup)
    if (contentDensity < 0.1) {
      contentScore -= 15;
    }
    
    contentScore = Math.max(0, contentScore);
    
    return {
      score: contentScore,
      metrics: {
        wordCount,
        contentDensity,
        readabilityScore: this.calculateReadabilityScore(textContent),
        keywordDensity: this.calculateKeywordDensity(textContent.split(/\s+/)),
        uniqueWords: this.countUniqueWords(textContent),
        contentQuality: contentScore
      }
    };
  }

  /**
   * Calculate performance metrics from HTML analysis
   */
  private calculatePerformanceMetrics(htmlAnalysis: HTMLAnalysisResult): {
    score: number;
    metrics: SEOMetrics['performanceMetrics'];
  } {
    const { contentSize, analysisTime } = htmlAnalysis.performanceMetrics;
    
    // Basic performance scoring based on content size
    let performanceScore = 100;
    
    // Penalize large content
    if (contentSize > 1000000) { // 1MB
      performanceScore -= 30;
    } else if (contentSize > 500000) { // 500KB
      performanceScore -= 15;
    }
    
    // Estimate load time based on content size (simplified)
    const estimatedLoadTime = Math.max(1000, contentSize / 1000); // 1ms per KB minimum
    
    // Basic Core Web Vitals estimates (would be replaced by real data from providers)
    const coreWebVitals = {
      lcp: Math.min(4000, estimatedLoadTime * 1.5), // Largest Contentful Paint
      fid: Math.min(300, analysisTime / 10), // First Input Delay  
      cls: 0.1 // Cumulative Layout Shift (default estimate)
    };

    return {
      score: Math.round(performanceScore),
      metrics: {
        pageSize: contentSize,
        loadTime: estimatedLoadTime,
        coreWebVitals
      }
    };
  }

  /**
   * Get real performance metrics using Google PageSpeed Insights API
   */
  private async getPageSpeedMetrics(url: string): Promise<{
    score: number;
    metrics: SEOMetrics['performanceMetrics'];
    confidence: number;
  } | null> {
    try {
      const apiKey = this.getProviderApiKey('lighthouse');
      if (!apiKey) {
        console.warn('No Google PageSpeed Insights API key configured');
        return null;
      }

      console.log(`📊 Fetching PageSpeed Insights for: ${url}`);

      const apiUrl = `https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url=${encodeURIComponent(url)}&key=${apiKey}&category=PERFORMANCE&category=SEO&strategy=MOBILE`;
      
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        console.error(`PageSpeed API error: ${response.status} ${response.statusText}`);
        return null;
      }

      const data = await response.json();
      
      // Extract performance metrics
      const performanceScore = Math.round(data.lighthouseResult?.categories?.performance?.score * 100) || 0;
      const loadingExperience = data.loadingExperience?.metrics || {};
      
      // Extract Core Web Vitals
      const coreWebVitals = {
        lcp: loadingExperience.LARGEST_CONTENTFUL_PAINT_MS?.percentile || 0,
        fid: loadingExperience.FIRST_INPUT_DELAY_MS?.percentile || 0,
        cls: loadingExperience.CUMULATIVE_LAYOUT_SHIFT_SCORE?.percentile || 0
      };

      // Extract other metrics
      const audits = data.lighthouseResult?.audits || {};
      const pageSize = audits['total-byte-weight']?.numericValue || 0;
      const loadTime = audits['speed-index']?.numericValue || 0;

      console.log(`✅ PageSpeed Insights completed - Performance score: ${performanceScore}`);

      return {
        score: performanceScore,
        metrics: {
          pageSize,
          loadTime,
          coreWebVitals
        },
        confidence: 0.95 // High confidence for Google's own data
      };

    } catch (error) {
      console.error('PageSpeed Insights API failed:', error);
      return null;
    }
  }

  /**
   * Analyze backlinks using configured provider
   */
  private async analyzeBacklinks(url: string, provider: SEOProviderType): Promise<{
    score: number;
    metrics: SEOMetrics['backlinkMetrics'];
    confidence: number;
  } | null> {
    try {
      // Check if provider has API key configured
      const apiKey = this.getProviderApiKey(provider);
      if (!apiKey) {
        console.warn(`No API key configured for ${provider} provider`);
        return null;
      }

      switch (provider) {
        case 'semrush':
          return this.analyzeSemrushBacklinks(url, apiKey);
        case 'ahrefs':
          return this.analyzeAhrefsBacklinks(url, apiKey);
        default:
          console.warn(`Backlink analysis not supported for provider: ${provider}`);
          return null;
      }
      
    } catch (error) {
      console.error(`Backlink analysis failed for provider ${provider}:`, error);
      return null;
    }
  }

  /**
   * Analyze keywords using configured provider
   */
  private async analyzeKeywords(htmlContent: string, url: string, provider: SEOProviderType): Promise<{
    score: number;
    metrics: SEOMetrics['keywordMetrics'];
    confidence: number;
  } | null> {
    try {
      // Check if provider has API key configured
      const apiKey = this.getProviderApiKey(provider);
      if (!apiKey) {
        console.warn(`No API key configured for ${provider} provider`);
        return null;
      }

      // Extract potential keywords from content
      const targetKeywords = this.extractKeywordsFromContent(htmlContent);

      switch (provider) {
        case 'semrush':
          return this.analyzeSemrushKeywords(url, targetKeywords, apiKey);
        case 'ahrefs':
          return this.analyzeAhrefsKeywords(url, targetKeywords, apiKey);
        default:
          console.warn(`Keyword analysis not supported for provider: ${provider}`);
          return null;
      }
      
    } catch (error) {
      console.error(`Keyword analysis failed for provider ${provider}:`, error);
      return null;
    }
  }

  /**
   * Get API key for provider from environment
   */
  private getProviderApiKey(provider: SEOProviderType): string | null {
    // Check if we're in a browser environment
    const isBrowser = typeof window !== 'undefined' && typeof process === 'undefined';
    
    if (isBrowser) {
      // In browser, try to get from window.ENV (if set by build process)
      const env = (window as any).ENV || {};
      switch (provider) {
        case 'lighthouse':
          return env.SEO_GENERATOR_API_KEY || env.SEO_PAGESPEED_API_KEY || null;
        case 'gtmetrix':
          return env.SEO_PAGESPEED_API_KEY || null;
        case 'semrush':
          return env.SEO_BACKLINK_API_KEY || env.SEO_KEYWORD_API_KEY || null;
        case 'ahrefs':
          return env.SEO_BACKLINK_API_KEY || env.SEO_KEYWORD_API_KEY || null;
        default:
          return null;
      }
    } else {
      // In Node.js environment
      switch (provider) {
        case 'lighthouse':
          return process.env.SEO_GENERATOR_API_KEY || process.env.SEO_PAGESPEED_API_KEY || null;
        case 'gtmetrix':
          return process.env.SEO_PAGESPEED_API_KEY || null;
        case 'semrush':
          return process.env.SEO_BACKLINK_API_KEY || process.env.SEO_KEYWORD_API_KEY || null;
        case 'ahrefs':
          return process.env.SEO_BACKLINK_API_KEY || process.env.SEO_KEYWORD_API_KEY || null;
        default:
          return null;
      }
    }
  }

  /**
   * Extract keywords from HTML content
   */
  private extractKeywordsFromContent(htmlContent: string): string[] {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    const textContent = doc.body?.textContent || '';
    const words = textContent.toLowerCase().split(/\s+/);
    
    // Find most common words as target keywords (simplified)
    const wordFreq = new Map<string, number>();
    words.forEach((word: string) => {
      if (word.length > 3 && !/^\d+$/.test(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });
    
    return Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word);
  }

  /**
   * Analyze backlinks using Semrush API
   */
  private async analyzeSemrushBacklinks(url: string, apiKey: string): Promise<{
    score: number;
    metrics: SEOMetrics['backlinkMetrics'];
    confidence: number;
  } | null> {
    try {
      // Implement actual Semrush API call here
      // For now, return null until API is implemented
      console.log(`Would call Semrush API for ${url} with key ${apiKey.substring(0, 10)}...`);
      return null;
    } catch (error) {
      console.error('Semrush backlink analysis failed:', error);
      return null;
    }
  }

  /**
   * Analyze backlinks using Ahrefs API
   */
  private async analyzeAhrefsBacklinks(url: string, apiKey: string): Promise<{
    score: number;
    metrics: SEOMetrics['backlinkMetrics'];
    confidence: number;
  } | null> {
    try {
      // Implement actual Ahrefs API call here
      // For now, return null until API is implemented
      console.log(`Would call Ahrefs API for ${url} with key ${apiKey.substring(0, 10)}...`);
      return null;
    } catch (error) {
      console.error('Ahrefs backlink analysis failed:', error);
      return null;
    }
  }

  /**
   * Analyze keywords using Semrush API
   */
  private async analyzeSemrushKeywords(url: string, keywords: string[], apiKey: string): Promise<{
    score: number;
    metrics: SEOMetrics['keywordMetrics'];
    confidence: number;
  } | null> {
    try {
      // Implement actual Semrush API call here
      // For now, return null until API is implemented
      console.log(`Would call Semrush keyword API for ${url} with keywords:`, keywords);
      return null;
    } catch (error) {
      console.error('Semrush keyword analysis failed:', error);
      return null;
    }
  }

  /**
   * Analyze keywords using Ahrefs API
   */
  private async analyzeAhrefsKeywords(url: string, keywords: string[], apiKey: string): Promise<{
    score: number;
    metrics: SEOMetrics['keywordMetrics'];
    confidence: number;
  } | null> {
    try {
      // Implement actual Ahrefs API call here
      // For now, return null until API is implemented
      console.log(`Would call Ahrefs keyword API for ${url} with keywords:`, keywords);
      return null;
    } catch (error) {
      console.error('Ahrefs keyword analysis failed:', error);
      return null;
    }
  }

  /**
   * Calculate overall SEO score with weighted components
   */
  private calculateOverallScore(metrics: SEOMetrics): number {
    let totalScore = 0;
    let totalWeight = 0;

    // Always include technical, content, and performance
    totalScore += metrics.technicalScore * this.SCORING_WEIGHTS.technical;
    totalScore += metrics.contentScore * this.SCORING_WEIGHTS.content;
    totalScore += metrics.performanceScore * this.SCORING_WEIGHTS.performance;
    totalWeight += this.SCORING_WEIGHTS.technical + this.SCORING_WEIGHTS.content + this.SCORING_WEIGHTS.performance;

    // Include backlink score if available
    if (metrics.backlinkScore !== undefined) {
      totalScore += metrics.backlinkScore * this.SCORING_WEIGHTS.backlink;
      totalWeight += this.SCORING_WEIGHTS.backlink;
    }

    // Include keyword score if available
    if (metrics.keywordScore !== undefined) {
      totalScore += metrics.keywordScore * this.SCORING_WEIGHTS.keyword;
      totalWeight += this.SCORING_WEIGHTS.keyword;
    }

    return Math.round(totalScore / totalWeight);
  }

  /**
   * Calculate letter grade from overall score
   */
  private calculateGrade(score: number): SEOScoringResult['grade'] {
    for (const [grade, threshold] of Object.entries(this.GRADE_THRESHOLDS)) {
      if (score >= threshold) {
        return grade as SEOScoringResult['grade'];
      }
    }
    return 'F';
  }

  /**
   * Generate actionable recommendations
   */
  private generateRecommendations(htmlAnalysis: HTMLAnalysisResult, metrics: SEOMetrics): SEORecommendation[] {
    const recommendations: SEORecommendation[] = [];

    // Technical recommendations from HTML analysis
    htmlAnalysis.issues.forEach(issue => {
      let category: SEORecommendation['category'] = 'technical';
      let priority: SEORecommendation['priority'] = issue.severity === 'critical' ? 'critical' : 
                                                   issue.severity === 'warning' ? 'high' : 'medium';

      recommendations.push({
        category,
        priority,
        title: issue.issue,
        description: issue.impact,
        action: issue.recommendation,
        expectedImpact: issue.severity === 'critical' ? 'High SEO impact' : 
                       issue.severity === 'warning' ? 'Medium SEO impact' : 'Low SEO impact',
        estimatedEffort: issue.severity === 'critical' ? 'high' : 'medium',
        relatedIssues: [issue.cssSelector]
      });
    });

    // Content recommendations
    if (metrics.contentMetrics.wordCount < 300) {
      recommendations.push({
        category: 'content',
        priority: 'high',
        title: 'Increase content length',
        description: 'Your page has insufficient content for good SEO rankings',
        action: `Add ${300 - metrics.contentMetrics.wordCount} more words of valuable content`,
        expectedImpact: 'Significant improvement in search rankings',
        estimatedEffort: 'medium',
        relatedIssues: ['content-length']
      });
    }

    // Performance recommendations
    if (metrics.performanceMetrics.pageSize > 500000) {
      recommendations.push({
        category: 'performance',
        priority: 'high',
        title: 'Optimize page size',
        description: 'Large page size impacts loading speed and user experience',
        action: 'Compress images, minify CSS/JS, and remove unnecessary content',
        expectedImpact: 'Better Core Web Vitals and user experience',
        estimatedEffort: 'medium',
        relatedIssues: ['page-size']
      });
    }

    // Sort by priority
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

    return recommendations.slice(0, 10); // Return top 10 recommendations
  }

  // Helper methods for calculating specific scores

  private calculateMetaTagScore(issues: HTMLIssueLocation[]): number {
    const metaIssues = issues.filter(issue => 
      issue.issue.toLowerCase().includes('meta') || 
      issue.issue.toLowerCase().includes('title')
    );
    
    let score = 100;
    metaIssues.forEach(issue => {
      score -= issue.severity === 'critical' ? 25 : issue.severity === 'warning' ? 15 : 5;
    });
    
    return Math.max(0, score);
  }

  private calculateHeadingScore(issues: HTMLIssueLocation[]): number {
    const headingIssues = issues.filter(issue => 
      issue.issue.toLowerCase().includes('h1') || 
      issue.issue.toLowerCase().includes('heading')
    );
    
    let score = 100;
    headingIssues.forEach(issue => {
      score -= issue.severity === 'critical' ? 20 : issue.severity === 'warning' ? 10 : 5;
    });
    
    return Math.max(0, score);
  }

  private calculateImageScore(issues: HTMLIssueLocation[]): number {
    const imageIssues = issues.filter(issue => 
      issue.issue.toLowerCase().includes('image') || 
      issue.issue.toLowerCase().includes('alt')
    );
    
    let score = 100;
    imageIssues.forEach(issue => {
      score -= issue.severity === 'critical' ? 15 : issue.severity === 'warning' ? 8 : 3;
    });
    
    return Math.max(0, score);
  }

  private calculateLinkScore(issues: HTMLIssueLocation[]): number {
    const linkIssues = issues.filter(issue => 
      issue.issue.toLowerCase().includes('link')
    );
    
    let score = 100;
    linkIssues.forEach(issue => {
      score -= issue.severity === 'critical' ? 10 : issue.severity === 'warning' ? 5 : 2;
    });
    
    return Math.max(0, score);
  }

  private calculateStructureScore(issues: HTMLIssueLocation[]): number {
    const structureIssues = issues.filter(issue => 
      issue.issue.toLowerCase().includes('structure') || 
      issue.issue.toLowerCase().includes('main') ||
      issue.issue.toLowerCase().includes('header') ||
      issue.issue.toLowerCase().includes('footer')
    );
    
    let score = 100;
    structureIssues.forEach(issue => {
      score -= issue.severity === 'critical' ? 15 : issue.severity === 'warning' ? 8 : 3;
    });
    
    return Math.max(0, score);
  }

  private calculateKeywordDensity(words: string[]): number {
    if (words.length === 0) return 0;
    
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      if (cleanWord.length > 3) {
        wordFreq.set(cleanWord, (wordFreq.get(cleanWord) || 0) + 1);
      }
    });
    
    const maxFreq = Math.max(...Array.from(wordFreq.values()));
    return Math.round((maxFreq / words.length) * 100 * 100) / 100; // 2 decimal places
  }

  private calculateReadabilityScore(text: string): number {
    // Simplified Flesch Reading Ease formula
    const sentences = text.split(/[.!?]+/).length;
    const words = text.split(/\s+/).length;
    const syllables = this.countSyllables(text);
    
    if (sentences === 0 || words === 0) return 0;
    
    const flesch = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
    return Math.max(0, Math.min(100, Math.round(flesch)));
  }

  private countSyllables(text: string): number {
    // Simple syllable counting (not perfect but adequate)
    const words = text.toLowerCase().split(/\s+/);
    let syllableCount = 0;
    
    words.forEach(word => {
      const vowels = word.match(/[aeiouy]+/g);
      syllableCount += vowels ? vowels.length : 1;
    });
    
    return syllableCount;
  }

  private countUniqueWords(text: string): number {
    const words = text.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    return uniqueWords.size;
  }
}

export const seoScoringService = new SEOScoringService(); 