import React, { useState, useEffect, useMemo } from "react";
import {
  Search,
  Users,
  Building2,
  ChevronDown,
  Check,
  RefreshCw,
} from "lucide-react";
import { Button } from "../../../../base/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../../base/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../../../../base/components/ui/command";
import { useToast } from "../../../../base/hooks/use-toast";
import {
  enhancedClientService,
  EnhancedClient,
} from "../services/enhancedClientService";

interface EnhancedClientSelectorProps {
  onClientSelect: (client: EnhancedClient) => void;
  selectedClientId?: string;
  className?: string;
}

export const EnhancedClientSelector: React.FC<EnhancedClientSelectorProps> = ({
  onClientSelect,
  selectedClientId,
  className = "",
}) => {
  const [contacts, setContacts] = useState<EnhancedClient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedContact, setSelectedContact] = useState<EnhancedClient | null>(
    null
  );
  const [isOpen, setIsOpen] = useState(false);

  const { toast } = useToast();

  // Optimized filtering with useMemo for performance
  const filteredContacts = useMemo(() => {
    let filtered = contacts;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (contact) =>
          contact.name.toLowerCase().includes(query) ||
          contact.email.toLowerCase().includes(query) ||
          contact.company.toLowerCase().includes(query) ||
          contact.contactPerson.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [contacts, searchQuery]);

  // Load contacts on component mount
  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setLoading(true);
      const fetchedContacts =
        await enhancedClientService.getOrganizationContacts();
      setContacts(fetchedContacts);
      console.log(`✅ Loaded ${fetchedContacts.length} contacts`);
    } catch (error) {
      console.error("Error loading contacts:", error);
      toast({
        title: "Error",
        description: "Failed to load contacts. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleContactSelect = (contact: EnhancedClient) => {
    setSelectedContact(contact);
    onClientSelect(contact);
    setIsOpen(false);
  };

  // Get stats for display
  const stats = useMemo(() => {
    const total = contacts.length;
    const crmContacts = contacts.filter((c) => c.source === "crm").length;
    const scopingaiClients = contacts.filter(
      (c) => c.source === "scopingai"
    ).length;

    return {
      total,
      crmContacts,
      scopingaiClients,
    };
  }, [contacts]);

  if (loading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-center p-4 border rounded-lg">
          <RefreshCw className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">
            Loading contacts...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Compact Header with Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Select Client</span>
          <span className="text-xs text-muted-foreground">
            ({stats.total} contacts)
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={loadContacts}
          className="h-7 px-2"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      </div>
      {/* Optimized Contact Selector - Perfect for 1000+ contacts */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className="w-full justify-between h-auto min-h-[2.5rem] p-3"
          >
            {selectedContact ? (
              <div className="flex items-center gap-2 text-left">
                <div className="flex items-center gap-1">
                  {selectedContact.source === "crm" ? (
                    <Building2 className="h-3 w-3 text-muted-foreground" />
                  ) : (
                    <Users className="h-3 w-3 text-muted-foreground" />
                  )}
                  <span className="font-medium">{selectedContact.name}</span>
                </div>
                {selectedContact.company && (
                  <span className="text-xs text-muted-foreground">
                    • {selectedContact.company}
                  </span>
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">Select a contact...</span>
            )}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="Search contacts..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <CommandList className="max-h-[300px]">
              <CommandEmpty>
                {searchQuery ? "No contacts found." : "No contacts available."}
              </CommandEmpty>

              {/* CRM Contacts ONLY - No grouping needed since we only show CRM contacts */}
              {filteredContacts.length > 0 && (
                <CommandGroup
                  heading={`Organization Contacts (${stats.total})`}
                >
                  {filteredContacts.map((contact) => (
                    <CommandItem
                      key={contact.id}
                      value={contact.id}
                      onSelect={() => handleContactSelect(contact)}
                      className="flex items-center gap-2 p-2"
                    >
                      <Building2 className="h-3 w-3 text-muted-foreground" />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {contact.name}
                        </div>
                        <div className="text-xs text-muted-foreground truncate">
                          {contact.company && `${contact.company} • `}
                          {contact.email}
                        </div>
                        {contact.organisationName && (
                          <div className="text-xs text-blue-600 truncate mt-0.5">
                            📍 {contact.organisationName}
                          </div>
                        )}
                      </div>
                      {selectedClientId === contact.id && (
                        <Check className="h-3 w-3" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default EnhancedClientSelector;
