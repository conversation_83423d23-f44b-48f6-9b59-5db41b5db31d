import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import type { PSEOWebsite } from '../../types';
import PSEOLayout from '../../components/PSEOLayout';

interface PageDiscoveryResultsProps {}

const PageDiscoveryResults: React.FC<PageDiscoveryResultsProps> = () => {
  const { websiteId } = useParams<{ websiteId: string }>();
  const navigate = useNavigate();
  
  const [website, setWebsite] = useState<PSEOWebsite | null>(null);
  const [pages, setPages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'pages' | 'analytics'>('overview');

  useEffect(() => {
    if (websiteId) {
      loadPageDiscoveryResults();
    }
  }, [websiteId]);

  const loadPageDiscoveryResults = async () => {
    try {
      setLoading(true);
      
      // Get website info
      const websiteData = await databaseService.getWebsiteById(websiteId!);
      if (!websiteData) {
        throw new Error('Website not found');
      }
      setWebsite(websiteData);
      
      // Get discovered pages
      const pagesData = await databaseService.getWebsitePages(websiteId!, {
        limit: 500
      });
      setPages(pagesData);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load page discovery results');
    } finally {
      setLoading(false);
    }
  };

  const getPageTypeColor = (pageType: string) => {
    switch (pageType) {
      case 'homepage': return 'bg-purple-100 text-purple-800';
      case 'blog': return 'bg-blue-100 text-blue-800';
      case 'product': return 'bg-green-100 text-green-800';
      case 'category': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'crawled': return 'bg-green-100 text-green-800';
      case 'discovered': return 'bg-blue-100 text-blue-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate stats
  const stats = {
    totalPages: pages.length,
    pageTypes: pages.reduce((acc, page) => {
      acc[page.page_type] = (acc[page.page_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    statusCounts: pages.reduce((acc, page) => {
      acc[page.status] = (acc[page.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    avgWordCount: pages.length > 0 ? Math.round(pages.reduce((sum, page) => sum + (page.word_count || 0), 0) / pages.length) : 0,
    avgLoadTime: pages.length > 0 ? Math.round(pages.reduce((sum, page) => sum + (page.load_time_ms || 0), 0) / pages.length) : 0
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (error) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="bg-destructive/10 border border-destructive/20 rounded-md p-6">
              <h2 className="text-lg font-semibold text-destructive mb-2">Error Loading Results</h2>
              <p className="text-destructive">{error}</p>
              <button
                onClick={() => navigate('/full-site-analysis')}
                className="mt-4 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
              >
                Back to Full Site Analysis
              </button>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              🔍 Page Discovery Results
            </h1>
            <p className="text-muted-foreground">
              {website?.name} - {website?.domain}
            </p>
            <p className="text-sm text-muted-foreground">
              {stats.totalPages} pages discovered through sitemap and crawling
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => navigate('/full-site-analysis')}
              className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90"
            >
              ← Back to Analysis
            </button>
            <button
              onClick={() => {
                const csvContent = pages.map(page => 
                  `"${page.url}","${page.title || ''}","${page.page_type}","${page.status}","${page.word_count || 0}","${page.response_code || ''}"`
                ).join('\n');
                const header = 'URL,Title,Page Type,Status,Word Count,Response Code\n';
                const csv = header + csvContent;
                const blob = new Blob([csv], { type: 'text/csv' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `page-discovery-${website?.name || 'results'}.csv`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
            >
              📄 Export CSV
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-border mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'pages', label: 'All Pages' },
              { id: 'analytics', label: 'Analytics' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-blue-600">{stats.totalPages}</div>
                <p className="text-sm text-muted-foreground">Total Pages Discovered</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-green-600">{stats.statusCounts.crawled || 0}</div>
                <p className="text-sm text-muted-foreground">Successfully Crawled</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-purple-600">{stats.avgWordCount}</div>
                <p className="text-sm text-muted-foreground">Avg Word Count</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-orange-600">{stats.avgLoadTime}ms</div>
                <p className="text-sm text-muted-foreground">Avg Load Time</p>
              </div>
            </div>

            {/* Page Types Breakdown */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">📊 Page Types Distribution</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(stats.pageTypes).map(([type, count]) => (
                  <div key={type} className="text-center">
                    <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getPageTypeColor(type)}`}>
                      {type}
                    </div>
                    <div className="text-xl font-bold mt-2">{count as number}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Pages by Word Count */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">📝 Top Pages by Content</h3>
              <div className="space-y-3">
                {pages
                  .filter(page => page.word_count > 0)
                  .sort((a, b) => (b.word_count || 0) - (a.word_count || 0))
                  .slice(0, 10)
                  .map((page, index) => (
                    <div key={page.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{page.title || page.url}</p>
                        <p className="text-xs text-muted-foreground truncate">{page.url}</p>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className={`px-2 py-1 rounded text-xs ${getPageTypeColor(page.page_type)}`}>
                          {page.page_type}
                        </span>
                        <span className="text-sm font-medium text-foreground">{page.word_count} words</span>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'pages' && (
          <div className="space-y-6">
            {/* Search and Filter */}
            <div className="bg-card rounded-lg border p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search pages by URL or title..."
                    className="w-full p-2 border rounded-md"
                  />
                </div>
                <select className="p-2 border rounded-md">
                  <option value="">All Page Types</option>
                  <option value="homepage">Homepage</option>
                  <option value="blog">Blog</option>
                  <option value="product">Product</option>
                  <option value="category">Category</option>
                  <option value="page">Page</option>
                </select>
                <select className="p-2 border rounded-md">
                  <option value="">All Status</option>
                  <option value="crawled">Crawled</option>
                  <option value="discovered">Discovered</option>
                  <option value="error">Error</option>
                </select>
              </div>
            </div>

            {/* Pages Table */}
            <div className="bg-card rounded-lg border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-muted">
                    <tr>
                      <th className="text-left p-4 font-medium">Page</th>
                      <th className="text-left p-4 font-medium">Type</th>
                      <th className="text-left p-4 font-medium">Status</th>
                      <th className="text-left p-4 font-medium">Words</th>
                      <th className="text-left p-4 font-medium">Load Time</th>
                      <th className="text-left p-4 font-medium">Response</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pages.slice(0, 100).map((page) => (
                      <tr key={page.id} className="border-t">
                        <td className="p-4">
                          <div>
                            <p className="font-medium text-sm truncate max-w-md">
                              {page.title || 'No Title'}
                            </p>
                            <a 
                              href={page.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-xs text-primary hover:underline truncate max-w-md block"
                            >
                              {page.url}
                            </a>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded text-xs ${getPageTypeColor(page.page_type)}`}>
                            {page.page_type}
                          </span>
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(page.status)}`}>
                            {page.status}
                          </span>
                        </td>
                        <td className="p-4 text-sm">{page.word_count || '-'}</td>
                        <td className="p-4 text-sm">{page.load_time_ms ? `${page.load_time_ms}ms` : '-'}</td>
                        <td className="p-4 text-sm">{page.response_code || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {pages.length > 100 && (
                <div className="p-4 text-center text-sm text-muted-foreground bg-muted">
                  Showing first 100 pages. Export CSV for complete data.
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Crawl Performance */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">⚡ Crawl Performance</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Response Codes</h4>
                  <div className="space-y-2">
                    {Object.entries(
                      pages.reduce((acc, page) => {
                        if (page.response_code) {
                          acc[page.response_code] = (acc[page.response_code] || 0) + 1;
                        }
                        return acc;
                      }, {} as Record<string, number>)
                    ).map(([code, count]) => (
                      <div key={code} className="flex justify-between">
                        <span className={`text-sm ${
                          code.startsWith('2') ? 'text-green-600' :
                          code.startsWith('3') ? 'text-yellow-600' :
                          code.startsWith('4') || code.startsWith('5') ? 'text-red-600' :
                          'text-gray-600'
                        }`}>
                          {code}
                        </span>
                        <span className="text-sm font-medium">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Load Time Distribution</h4>
                  <div className="space-y-2">
                    {[
                      { label: '< 1s', min: 0, max: 1000 },
                      { label: '1-3s', min: 1000, max: 3000 },
                      { label: '3-5s', min: 3000, max: 5000 },
                      { label: '> 5s', min: 5000, max: Infinity }
                    ].map(({ label, min, max }) => {
                      const count = pages.filter(page => 
                        page.load_time_ms && page.load_time_ms >= min && page.load_time_ms < max
                      ).length;
                      return (
                        <div key={label} className="flex justify-between">
                          <span className="text-sm">{label}</span>
                          <span className="text-sm font-medium">{count}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Content Analysis</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Pages with content</span>
                      <span className="text-sm font-medium">
                        {pages.filter(page => (page.word_count || 0) > 0).length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Avg words/page</span>
                      <span className="text-sm font-medium">{stats.avgWordCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Longest page</span>
                      <span className="text-sm font-medium">
                        {Math.max(...pages.map(page => page.word_count || 0))} words
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* SEO Issues Summary */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">⚠️ Potential SEO Issues</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <div>
                    <p className="font-medium text-yellow-800">Pages without titles</p>
                    <p className="text-sm text-yellow-600">Missing or empty title tags hurt SEO</p>
                  </div>
                  <span className="text-lg font-bold text-yellow-700">
                    {pages.filter(page => !page.title || page.title.trim() === '').length}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded">
                  <div>
                    <p className="font-medium text-orange-800">Low content pages</p>
                    <p className="text-sm text-orange-600">Pages with less than 300 words</p>
                  </div>
                  <span className="text-lg font-bold text-orange-700">
                    {pages.filter(page => (page.word_count || 0) < 300 && (page.word_count || 0) > 0).length}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded">
                  <div>
                    <p className="font-medium text-red-800">Error pages</p>
                    <p className="text-sm text-red-600">Pages with 4xx or 5xx response codes</p>
                  </div>
                  <span className="text-lg font-bold text-red-700">
                    {pages.filter(page => 
                      page.response_code && (page.response_code >= 400)
                    ).length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default PageDiscoveryResults; 