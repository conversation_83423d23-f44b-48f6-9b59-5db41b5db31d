import { supabase } from "../lib/supabase";

export interface ScopingDocument {
  id: string;
  title: string;
  status: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  content?: any;
  sections?: any;
  metadata?: any;
  client_id?: string;
  template_id?: string;
  project_data?: any;
  proposalFilePath?: string;
}

export interface DocumentStats {
  totalDocuments: number;
  draftDocuments: number;
  completedDocuments: number;
  recentDocuments: number;
}

class ScopingDocumentService {
  /**
   * Get all scoping documents for the current user
   */
  async getDocuments(): Promise<ScopingDocument[]> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("scopingai_documents")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching scoping documents:", error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error("Failed to get scoping documents:", error);
      throw error;
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(): Promise<DocumentStats> {
    try {
      const documents = await this.getDocuments();

      const totalDocuments = documents.length;
      const draftDocuments = documents.filter(
        (doc) => doc.status === "draft"
      ).length;
      const completedDocuments = documents.filter(
        (doc) => doc.status === "created" || doc.status === "completed"
      ).length;

      // Recent documents (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentDocuments = documents.filter(
        (doc) => new Date(doc.created_at) > thirtyDaysAgo
      ).length;

      return {
        totalDocuments,
        draftDocuments,
        completedDocuments,
        recentDocuments,
      };
    } catch (error) {
      console.error("Failed to get document stats:", error);
      return {
        totalDocuments: 0,
        draftDocuments: 0,
        completedDocuments: 0,
        recentDocuments: 0,
      };
    }
  }

  /**
   * Get a single document by ID
   */
  async getDocument(id: string): Promise<ScopingDocument | null> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("scopingai_documents")
        .select("*")
        .eq("id", id)
        .eq("user_id", user.id)
        .single();

      if (error) {
        console.error("Error fetching document:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Failed to get document:", error);
      throw error;
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(id: string): Promise<void> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const { error } = await supabase
        .from("scopingai_documents")
        .delete()
        .eq("id", id)
        .eq("user_id", user.id);

      if (error) {
        console.error("Error deleting document:", error);
        throw error;
      }
    } catch (error) {
      console.error("Failed to delete document:", error);
      throw error;
    }
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  /**
   * Get status icon based on document status
   */
  getStatusInfo(status: string) {
    switch (status) {
      case "created":
      case "completed":
        return {
          icon: "CheckCircle",
          color: "text-green-500",
          label: "Completed",
        };
      case "draft":
        return { icon: "Clock", color: "text-yellow-500", label: "Draft" };
      case "processing":
        return { icon: "Loader2", color: "text-blue-500", label: "Processing" };
      case "error":
        return { icon: "AlertCircle", color: "text-red-500", label: "Error" };
      default:
        return { icon: "FileText", color: "text-gray-500", label: "Unknown" };
    }
  }
}

export const scopingDocumentService = new ScopingDocumentService();
