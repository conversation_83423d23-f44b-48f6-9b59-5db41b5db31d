// =====================================================
// LANGGRAPH WORKFLOW STATE DEFINITIONS
// =====================================================

export interface KeywordData {
  keyword: string;
  search_volume: number;
  keyword_difficulty: number;
  cpc: number;
  competition: 'low' | 'medium' | 'high';
  intent: 'informational' | 'navigational' | 'commercial' | 'transactional';
  trend: 'rising' | 'stable' | 'declining';
  data_source: string;
  ranking_position?: number;
  ranking_url?: string;
}

export interface CompetitorData {
  domain: string;
  keywords: KeywordData[];
  ranking_overlap: number;
  authority_score: number;
}

export interface ContentSuggestion {
  title: string;
  content_type: 'blog' | 'landing' | 'product' | 'guide' | 'faq';
  target_keywords: string[];
  estimated_traffic: number;
  difficulty_score: number;
}

export interface KeywordCluster {
  cluster_name: string;
  primary_keyword: string;
  related_keywords: string[];
  search_volume: number;
  difficulty_score: number;
  intent_category: string;
}

export interface WorkflowError {
  node_name: string;
  error_message: string;
  error_code: string;
  timestamp: string;
  recoverable: boolean;
}

export interface APICallMetrics {
  provider: string;
  endpoint: string;
  calls_made: number;
  success_rate: number;
  average_response_time: number;
  cost_estimate: number;
}

// Main workflow state interface
export interface PSEOWorkflowState {
  // Input parameters
  workflow_id: string;
  user_id: string;
  website_id: string;
  domain: string;
  seed_keywords: string[];
  research_method: 'website' | 'topic';
  topic_input?: string;
  competitor_domains?: string[];
  max_keywords?: number;
  data_sources: string[];
  
  // Processing state
  current_step: string;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  errors: WorkflowError[];
  
  // Results data
  keywords: KeywordData[];
  keyword_clusters: KeywordCluster[];
  competitor_data: CompetitorData[];
  content_suggestions: ContentSuggestion[];
  
  // Metadata and metrics
  api_calls_made: APICallMetrics[];
  processing_time: number;
  data_sources_used: string[];
  total_cost: number;
  
  // Timestamps
  started_at: string;
  completed_at?: string;
  last_updated: string;
  
  // Node-specific data (for passing data between nodes)
  node_data: Record<string, any>;
  
  // Configuration
  config: {
    timeout_seconds: number;
    retry_attempts: number;
    enable_caching: boolean;
    quality_threshold: number;
  };
}

// Node execution result interface
export interface NodeResult {
  success: boolean;
  data?: any;
  error?: string;
  metrics?: {
    execution_time: number;
    api_calls: number;
    data_points_processed: number;
  };
  next_node?: string;
}

// Workflow execution context
export interface WorkflowContext {
  state: PSEOWorkflowState;
  tools: WorkflowTools;
  logger: WorkflowLogger;
  config: WorkflowConfig;
}

export interface WorkflowTools {
  ai: {
    generateText: (prompt: string, options?: any) => Promise<string>;
    analyzeContent: (content: string, analysis_type: string) => Promise<any>;
  };
  http: {
    get: (url: string, options?: any) => Promise<any>;
    post: (url: string, data: any, options?: any) => Promise<any>;
  };
  database: {
    query: (sql: string, params?: any[]) => Promise<any>;
    insert: (table: string, data: any) => Promise<any>;
    update: (table: string, data: any, where: any) => Promise<any>;
  };
  cache: {
    get: <T>(key: string) => Promise<T | null>;
    set: (key: string, value: any, ttl?: number) => Promise<void>;
    delete: (key: string) => Promise<void>;
  };
  seo: {
    semrush?: any;
    ahrefs?: any;
    ubersuggest?: any;
  };
}

export interface WorkflowLogger {
  info: (message: string, data?: any) => void;
  warn: (message: string, data?: any) => void;
  error: (message: string, error?: any) => void;
  debug: (message: string, data?: any) => void;
}

export interface WorkflowConfig {
  openai_api_key?: string;
  semrush_api_key?: string;
  ahrefs_api_key?: string;
  ubersuggest_api_key?: string;
  max_concurrent_requests: number;
  request_timeout: number;
  retry_attempts: number;
  cache_ttl: number;
}

// Input validation schemas
export interface KeywordResearchInput {
  website_id: string;
  seed_keywords?: string[];
  research_method: 'website' | 'topic';
  topic_input?: string;
  competitor_domains?: string[];
  max_keywords?: number;
  data_sources: string[];
}

export interface WorkflowValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
