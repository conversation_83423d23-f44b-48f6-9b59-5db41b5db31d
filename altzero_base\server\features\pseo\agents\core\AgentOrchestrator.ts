// =====================================================
// AGENT ORCHESTRATOR - MULTI-AGENT COORDINATION
// =====================================================

import { 
  IAgent,
  AgentContext,
  AgentResult,
  ExecutionPlan,
  AgentExecution,
  OrchestratorConfig,
  OrchestratorState,
  SystemMetrics,
  AgentError
} from './AgentTypes';

// Import all agent implementations
import { PageDiscoveryAgent } from '../specialists/PageDiscoveryAgent';
import { KeywordResearchAgent } from '../specialists/KeywordResearchAgent';
import { ContentGenerationAgent } from '../specialists/ContentGenerationAgent';

// Basic types (replicated to avoid import issues)
interface PSEOWebsite {
  id: string;
  client_id: string;
  url: string;
  domain: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface PSEOAgentJob {
  id: string;
  website_id: string;
  job_type: 'page_discovery' | 'keyword_research' | 'backlink_analysis' | 'content_generation' | 'full_site_audit';
  agent_name: string;
  parent_job_id?: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  started_at?: string;
  completed_at?: string;
  processing_time_seconds?: number;
  result_data: Record<string, unknown>;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  created_at: string;
  updated_at: string;
}

export class AgentOrchestrator {
  private config: OrchestratorConfig;
  private state: OrchestratorState;
  private executionQueue: Map<string, ExecutionPlan>;
  private activeExecutions: Map<string, Promise<AgentResult>>;

  constructor(config?: Partial<OrchestratorConfig>) {
    this.config = {
      max_concurrent_agents: 3,
      default_timeout_seconds: 300,
      retry_failed_jobs: true,
      max_retry_attempts: 3,
      job_queue_size: 100,
      enable_job_persistence: true,
      ...config
    };

    this.state = {
      active_jobs: new Map(),
      agent_registry: new Map(),
      job_queue: [],
      system_metrics: this.initializeMetrics()
    };

    this.executionQueue = new Map();
    this.activeExecutions = new Map();

    // Register all available agents
    this.registerAgents();
  }

  // =====================================================
  // AGENT REGISTRATION
  // =====================================================

  private registerAgents(): void {
    // Register specialized agents
    this.registerAgent(new PageDiscoveryAgent());
    this.registerAgent(new KeywordResearchAgent());
    this.registerAgent(new ContentGenerationAgent());
    
    console.log(`Registered ${this.state.agent_registry.size} agents`);
  }

  private registerAgent(agent: IAgent): void {
    this.state.agent_registry.set(agent.name, agent);
    console.log(`Registered agent: ${agent.name}`);
  }

  // =====================================================
  // JOB EXECUTION ORCHESTRATION
  // =====================================================

  async executeFullSiteAnalysis(
    website: PSEOWebsite,
    analysisTypes: string[],
    options: Record<string, unknown> = {},
    context: AgentContext
  ): Promise<{ success: boolean; results: Record<string, AgentResult>; executionPlan: ExecutionPlan }> {
    try {
      context.logger.info('Starting full site analysis orchestration', {
        website_id: website.id,
        domain: website.domain,
        analysis_types: analysisTypes
      });

      // Create execution plan
      const plan = await this.createExecutionPlan(website, analysisTypes, options, context);
      
      // Execute the plan
      const results = await this.executePlan(plan, context);
      
      // Update metrics
      this.updateMetrics(plan, results);

      context.logger.info('Full site analysis completed', {
        website_id: website.id,
        successful_agents: Object.values(results).filter(r => r.success).length,
        failed_agents: Object.values(results).filter(r => !r.success).length
      });

      return {
        success: Object.values(results).every(r => r.success),
        results,
        executionPlan: plan
      };

    } catch (error) {
      context.logger.error('Full site analysis orchestration failed', error as Error);
      throw new AgentError(
        'Orchestration failed',
        'AgentOrchestrator',
        'ORCHESTRATION_ERROR',
        false,
        { website_id: website.id, analysis_types: analysisTypes }
      );
    }
  }

  async executeAgentJob(
    job: PSEOAgentJob,
    website: PSEOWebsite,
    context: AgentContext
  ): Promise<AgentResult> {
    try {
      context.logger.info('Executing agent job', {
        job_id: job.id,
        agent_name: job.agent_name,
        job_type: job.job_type
      });

      const agent = this.state.agent_registry.get(job.agent_name);
      if (!agent) {
        throw new AgentError(
          `Agent not found: ${job.agent_name}`,
          'AgentOrchestrator',
          'AGENT_NOT_FOUND',
          false
        );
      }

      // Prepare agent context
      const agentContext: AgentContext = {
        ...context,
        website,
        job
      };

      // Execute the agent
      const result = await agent.execute(agentContext);

      // Log result
      context.logger.info('Agent job completed', {
        job_id: job.id,
        agent_name: job.agent_name,
        success: result.success,
        execution_time: result.metrics.execution_time_ms
      });

      return result;

    } catch (error) {
      context.logger.error(`Agent job failed: ${job.id}`, error as Error);
      throw error;
    }
  }

  // =====================================================
  // EXECUTION PLAN CREATION
  // =====================================================

  private async createExecutionPlan(
    website: PSEOWebsite,
    analysisTypes: string[],
    options: Record<string, unknown>,
    context: AgentContext
  ): Promise<ExecutionPlan> {
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const agents: AgentExecution[] = [];
    const dependencies: any[] = [];

    // Define agent execution order and dependencies
    const agentDefinitions = this.getAgentDefinitions(analysisTypes);

    for (const [index, agentDef] of agentDefinitions.entries()) {
      agents.push({
        agent_name: agentDef.name,
        execution_order: index,
        depends_on: agentDef.dependencies || [],
        input_mapping: agentDef.input_mapping || {},
        output_mapping: agentDef.output_mapping || {},
        timeout_seconds: agentDef.timeout || this.config.default_timeout_seconds
      });
    }

    const plan: ExecutionPlan = {
      job_id: planId,
      website_id: website.id,
      agents,
      dependencies,
      estimated_duration_minutes: this.estimateExecutionTime(agents),
      priority: options.priority as number || 0
    };

    this.executionQueue.set(planId, plan);
    this.state.active_jobs.set(planId, plan);

    context.logger.info('Created execution plan', {
      plan_id: planId,
      agents_count: agents.length,
      estimated_duration: plan.estimated_duration_minutes
    });

    return plan;
  }

  private getAgentDefinitions(analysisTypes: string[]): any[] {
    const definitions: any[] = [];

    // Page discovery (usually first)
    if (analysisTypes.includes('page_discovery')) {
      definitions.push({
        name: 'PageDiscoveryAgent',
        dependencies: [],
        input_mapping: {
          discovery_methods: ['sitemap', 'crawl'],
          max_pages: 100,
          crawl_depth: 3
        },
        timeout: 600 // 10 minutes
      });
    }

    // Keyword research (can run in parallel with page discovery)
    if (analysisTypes.includes('keyword_research')) {
      definitions.push({
        name: 'KeywordResearchAgent',
        dependencies: [],
        input_mapping: {
          data_sources: ['google_planner', 'ubersuggest'],
          max_keywords: 500
        },
        timeout: 300 // 5 minutes
      });
    }

    // Content generation (depends on both page discovery and keyword research)
    if (analysisTypes.includes('content_generation')) {
      const dependencies = [];
      if (analysisTypes.includes('page_discovery')) dependencies.push('PageDiscoveryAgent');
      if (analysisTypes.includes('keyword_research')) dependencies.push('KeywordResearchAgent');

      definitions.push({
        name: 'ContentGenerationAgent',
        dependencies,
        input_mapping: {
          target_keywords: [], // Will be populated from keyword research results
          content_types: ['blog', 'guide', 'faq'],
          ai_model: 'gpt-4o'
        },
        timeout: 900 // 15 minutes
      });
    }

    return definitions;
  }

  // =====================================================
  // PLAN EXECUTION
  // =====================================================

  private async executePlan(
    plan: ExecutionPlan,
    context: AgentContext
  ): Promise<Record<string, AgentResult>> {
    const results: Record<string, AgentResult> = {};
    const completedAgents = new Set<string>();

    context.logger.info('Starting plan execution', {
      plan_id: plan.job_id,
      agents_count: plan.agents.length
    });

    // Sort agents by execution order
    const sortedAgents = [...plan.agents].sort((a, b) => a.execution_order - b.execution_order);

    for (const agentExecution of sortedAgents) {
      try {
        // Check dependencies
        const dependenciesMet = agentExecution.depends_on.every(dep => completedAgents.has(dep));
        
        if (!dependenciesMet) {
          const missingDeps = agentExecution.depends_on.filter(dep => !completedAgents.has(dep));
          throw new AgentError(
            `Dependencies not met: ${missingDeps.join(', ')}`,
            agentExecution.agent_name,
            'DEPENDENCY_ERROR',
            false
          );
        }

        // Prepare agent input with dependency results
        const agentInput = this.prepareAgentInput(agentExecution, results, context);

        // Create agent job
        const job: PSEOAgentJob = {
          id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          website_id: plan.website_id,
          job_type: this.getJobType(agentExecution.agent_name),
          agent_name: agentExecution.agent_name,
          status: 'running',
          priority: plan.priority,
          result_data: agentInput,
          retry_count: 0,
          max_retries: this.config.max_retry_attempts,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Execute agent
        context.logger.info(`Executing agent: ${agentExecution.agent_name}`, {
          plan_id: plan.job_id,
          job_id: job.id
        });

        const result = await this.executeAgentJob(job, context.website, context);
        results[agentExecution.agent_name] = result;

        if (result.success) {
          completedAgents.add(agentExecution.agent_name);
          context.logger.info(`Agent completed successfully: ${agentExecution.agent_name}`);
        } else {
          context.logger.error(`Agent failed: ${agentExecution.agent_name}`, {
            error: result.error
          } as any);
          
          // Decide whether to continue or abort based on agent criticality
          if (this.isCriticalAgent(agentExecution.agent_name)) {
            throw new AgentError(
              `Critical agent failed: ${agentExecution.agent_name}`,
              'AgentOrchestrator',
              'CRITICAL_AGENT_FAILURE',
              false
            );
          }
        }

      } catch (error) {
        const agentError = error instanceof AgentError ? error : new AgentError(
          `Agent execution failed: ${agentExecution.agent_name}`,
          agentExecution.agent_name,
          'EXECUTION_ERROR',
          true
        );

        results[agentExecution.agent_name] = {
          success: false,
          error: agentError.message,
          metrics: {
            execution_time_ms: 0,
            api_calls_made: 0,
            data_points_processed: 0,
            errors_encountered: 1,
            cache_hits: 0
          }
        };

        context.logger.error(`Agent execution failed: ${agentExecution.agent_name}`, agentError);
      }
    }

    // Clean up
    this.state.active_jobs.delete(plan.job_id);
    this.executionQueue.delete(plan.job_id);

    return results;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  private prepareAgentInput(
    agentExecution: AgentExecution,
    previousResults: Record<string, AgentResult>,
    context: AgentContext
  ): Record<string, unknown> {
    const input: Record<string, unknown> = {
      website_id: context.website.id,
      parameters: { ...agentExecution.input_mapping }
    };

    // Map outputs from dependency agents to this agent's input
    for (const [outputKey, inputKey] of Object.entries(agentExecution.output_mapping)) {
      const [agentName, resultField] = outputKey.split('.');
      const dependencyResult = previousResults[agentName];
      
      if (dependencyResult?.success && dependencyResult.data) {
        const value = this.getNestedValue(dependencyResult.data, resultField);
        if (value !== undefined) {
          this.setNestedValue(input.parameters as Record<string, unknown>, inputKey, value);
        }
      }
    }

    // Special handling for keyword research -> content generation
    if (agentExecution.agent_name === 'ContentGenerationAgent') {
      const keywordResult = previousResults['KeywordResearchAgent'];
      if (keywordResult?.success && keywordResult.data?.keywords_found) {
        const keywords = (keywordResult.data.keywords_found as any[])
          .slice(0, 20) // Limit for efficiency
          .map(k => k.keyword);
        
        (input.parameters as any).target_keywords = keywords;
      }
    }

    return input;
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' ? (current as any)[key] : undefined;
    }, obj);
  }

  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key] as Record<string, unknown>;
    }, obj);
    
    target[lastKey] = value;
  }

  private getJobType(agentName: string): PSEOAgentJob['job_type'] {
    const mapping: Record<string, PSEOAgentJob['job_type']> = {
      'PageDiscoveryAgent': 'page_discovery',
      'KeywordResearchAgent': 'keyword_research',
      'BacklinkAnalysisAgent': 'backlink_analysis',
      'ContentGenerationAgent': 'content_generation'
    };

    return mapping[agentName] || 'full_site_audit';
  }

  private isCriticalAgent(agentName: string): boolean {
    const criticalAgents = ['PageDiscoveryAgent']; // Page discovery is critical for other agents
    return criticalAgents.includes(agentName);
  }

  private estimateExecutionTime(agents: AgentExecution[]): number {
    // Estimate based on agent timeouts and parallelization opportunities
    let totalTime = 0;
    const parallelGroups = this.groupAgentsByDependencies(agents);
    
    for (const group of parallelGroups) {
      const maxTimeInGroup = Math.max(...group.map(agent => agent.timeout_seconds || 300));
      totalTime += maxTimeInGroup;
    }

    return Math.ceil(totalTime / 60); // Convert to minutes
  }

  private groupAgentsByDependencies(agents: AgentExecution[]): AgentExecution[][] {
    const groups: AgentExecution[][] = [];
    const processedAgents = new Set<string>();

    // Simple grouping: agents with no unmet dependencies can run in parallel
    while (processedAgents.size < agents.length) {
      const currentGroup: AgentExecution[] = [];
      
      for (const agent of agents) {
        if (processedAgents.has(agent.agent_name)) continue;
        
        const dependenciesMet = agent.depends_on.every(dep => processedAgents.has(dep));
        if (dependenciesMet) {
          currentGroup.push(agent);
        }
      }

      if (currentGroup.length === 0) {
        // Circular dependency or other issue
        const remaining = agents.filter(a => !processedAgents.has(a.agent_name));
        currentGroup.push(...remaining);
      }

      groups.push(currentGroup);
      currentGroup.forEach(agent => processedAgents.add(agent.agent_name));
    }

    return groups;
  }

  // =====================================================
  // METRICS AND MONITORING
  // =====================================================

  private initializeMetrics(): SystemMetrics {
    return {
      total_jobs_processed: 0,
      average_job_duration_minutes: 0,
      success_rate: 100,
      active_agents: 0,
      queue_length: 0,
      system_load: 0
    };
  }

  private updateMetrics(plan: ExecutionPlan, results: Record<string, AgentResult>): void {
    const successfulJobs = Object.values(results).filter(r => r.success).length;
    const totalJobs = Object.values(results).length;
    
    this.state.system_metrics.total_jobs_processed += totalJobs;
    this.state.system_metrics.success_rate = (
      (this.state.system_metrics.success_rate * (this.state.system_metrics.total_jobs_processed - totalJobs) + 
       (successfulJobs / totalJobs) * 100) / this.state.system_metrics.total_jobs_processed
    );

    // Calculate average execution time
    const totalExecutionTime = Object.values(results)
      .reduce((sum, result) => sum + (result.metrics.execution_time_ms || 0), 0);
    
    const avgTimeMinutes = totalExecutionTime / (1000 * 60 * totalJobs);
    this.state.system_metrics.average_job_duration_minutes = (
      (this.state.system_metrics.average_job_duration_minutes * (this.state.system_metrics.total_jobs_processed - totalJobs) +
       avgTimeMinutes * totalJobs) / this.state.system_metrics.total_jobs_processed
    );

    this.state.system_metrics.queue_length = this.executionQueue.size;
    this.state.system_metrics.active_agents = this.activeExecutions.size;
  }

  // =====================================================
  // PUBLIC API
  // =====================================================

  getSystemMetrics(): SystemMetrics {
    return { ...this.state.system_metrics };
  }

  getActiveJobs(): ExecutionPlan[] {
    return Array.from(this.state.active_jobs.values());
  }

  getRegisteredAgents(): string[] {
    return Array.from(this.state.agent_registry.keys());
  }

  async cancelJob(jobId: string): Promise<boolean> {
    const plan = this.state.active_jobs.get(jobId);
    if (!plan) {
      return false;
    }

    // Cancel active execution if running
    const activeExecution = this.activeExecutions.get(jobId);
    if (activeExecution) {
      // Note: In a production system, you'd implement proper cancellation
      console.log(`Cancelling job: ${jobId}`);
    }

    // Clean up
    this.state.active_jobs.delete(jobId);
    this.executionQueue.delete(jobId);
    this.activeExecutions.delete(jobId);

    return true;
  }

  // Validation method
  validateExecutionPlan(plan: ExecutionPlan): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if all agents exist
    for (const agentExecution of plan.agents) {
      if (!this.state.agent_registry.has(agentExecution.agent_name)) {
        errors.push(`Agent not registered: ${agentExecution.agent_name}`);
      }
    }

    // Check for circular dependencies
    const hasCycles = this.detectCycles(plan.agents);
    if (hasCycles) {
      errors.push('Circular dependencies detected in execution plan');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private detectCycles(agents: AgentExecution[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (agentName: string): boolean => {
      if (recursionStack.has(agentName)) {
        return true;
      }
      if (visited.has(agentName)) {
        return false;
      }

      visited.add(agentName);
      recursionStack.add(agentName);

      const agent = agents.find(a => a.agent_name === agentName);
      if (agent) {
        for (const dependency of agent.depends_on) {
          if (hasCycle(dependency)) {
            return true;
          }
        }
      }

      recursionStack.delete(agentName);
      return false;
    };

    for (const agent of agents) {
      if (hasCycle(agent.agent_name)) {
        return true;
      }
    }

    return false;
  }
} 