import React from "react";
import { useSubscription } from "../../contextapi/SubscriptionContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Progress } from "../ui/progress";
import { ResourceSummary } from "../../types/subscription";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";

export interface ResourceUsageProps {
  className?: string;
}

export function ResourceUsage({ className }: ResourceUsageProps) {
  const { subscriptionSummary, isLoading } = useSubscription();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
          <CardDescription>Loading your resource usage...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscriptionSummary) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
          <CardDescription>
            No subscription information available
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Subscribe to a plan to see your resource usage.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Find resources over 80% usage
  const resourcesAtRisk = subscriptionSummary.resources.filter(
    (resource) => resource.percentage_used > 80
  );

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Resource Usage</CardTitle>
        <CardDescription>Monitor your resource consumption</CardDescription>
      </CardHeader>
      <CardContent>
        {resourcesAtRisk.length > 0 && (
          <Alert variant="warning" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Approaching limit</AlertTitle>
            <AlertDescription>
              You're approaching the limit on {resourcesAtRisk.length} resource
              {resourcesAtRisk.length > 1 ? "s" : ""}.
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-5">
          {subscriptionSummary.resources.map((resource) => (
            <ResourceUsageItem
              key={resource.resource_type_id}
              resource={resource}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface ResourceUsageItemProps {
  resource: ResourceSummary;
}

function ResourceUsageItem({ resource }: ResourceUsageItemProps) {
  // Calculate progress color based on usage percentage
  const getProgressColor = (percentage: number) => {
    if (percentage > 90) return "bg-destructive";
    if (percentage > 80) return "bg-yellow-500";
    return "bg-primary";
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <div>
          <h4 className="font-medium text-sm">{resource.name}</h4>
          <p className="text-xs text-muted-foreground">{resource.unit_label}</p>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium">
            {resource.current_usage} / {resource.total_limit}
          </p>
          <p className="text-xs text-muted-foreground">
            {resource.percentage_used.toFixed(0)}% Used
          </p>
        </div>
      </div>
      <Progress
        value={resource.percentage_used}
        className="h-2"
        indicatorClassName={getProgressColor(resource.percentage_used)}
      />

      {resource.base_limit < resource.total_limit && (
        <div className="text-xs text-muted-foreground flex justify-between">
          <span>Base: {resource.base_limit}</span>
          <span>Additional: +{resource.additional}</span>
        </div>
      )}
    </div>
  );
}
