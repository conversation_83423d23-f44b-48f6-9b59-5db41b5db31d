# AltZero Platform

A modular, scalable platform built with React, TypeScript, and Supabase featuring a **dynamic plugin architecture** that enables seamless feature addition without modifying core files.

## 🏗️ Plugin Architecture

AltZero uses a **revolutionary plugin system** where new features can be added by editing only a single registry file. No core application files need to be modified!

### Key Benefits
- ✅ **Zero Core Impact**: Add features without touching App.tsx, Header.tsx, or other core files
- ✅ **Automatic Discovery**: Plugins self-register and auto-load
- ✅ **Dynamic Navigation**: Menu items appear automatically
- ✅ **Hot Swappable**: Enable/disable features with one line
- ✅ **TypeScript Safe**: Full type checking and contracts

## 📂 Project Structure

```
altzero_base/
├── App.tsx                     # 🚀 Main app (plugin-agnostic)
├── main.tsx                    # 🚀 Application entry point
├── index.css                   # 🎨 Global styles with theme system
│
├── plugins/                    # 🔧 Core Plugin System
│   ├── registry.ts            # 🎯 SINGLE SOURCE OF TRUTH for all plugins
│   ├── types.ts               # 📋 Plugin interfaces & contracts
│   └── loader.tsx             # ⚡ Frontend plugin loader & hooks
│
├── features/                   # 📦 Frontend Plugin Features
│   ├── knowledge/             # 🧠 Knowledge Base Plugin
│   │   ├── index.ts          # 🔌 Plugin export
│   │   ├── components/       # ⚛️ React components
│   │   ├── context/          # 🔄 State management
│   │   ├── services/         # 🌐 API calls
│   │   ├── types/            # 📋 TypeScript types
│   │   └── utils/            # 🔧 Utilities
│   ├── pseo/                 # 📊 Programmatic SEO Plugin
│   │   ├── index.ts          # 🔌 Plugin export
│   │   ├── pages/            # 📄 All pSEO pages
│   │   ├── services/         # 🌐 Database & API services
│   │   ├── utilities/        # 🔧 SEO utilities
│   │   └── types.ts          # 📋 TypeScript types
│   └── aichat/               # 🤖 AI Chat Plugin (CopilotKit)
│       ├── index.ts          # 🔌 Plugin export
│       ├── pages/            # 📄 Chat interface
│       ├── components/       # ⚛️ Chat components
│       ├── contexts/         # 🔄 Chat state
│       └── hooks/            # 🪝 Chat hooks
│
├── server/                    # 🖥️ Backend Server
│   ├── app.ts                # 🚀 Express server with auto plugin loading
│   ├── plugins/              # 🔧 Backend Plugin System
│   │   ├── registry.ts       # 🎯 Backend plugin registry
│   │   └── loader.ts         # ⚡ Backend plugin auto-loader
│   ├── features/             # 📦 Backend Plugin Features
│   │   ├── knowledge/        # 🧠 Knowledge Base API
│   │   │   ├── backend/index.ts  # 🔌 Plugin export
│   │   │   ├── routes/knowledge.ts # 🛣️ API routes
│   │   │   └── services/     # ⚙️ Pinecone, RAG, LangGraph
│   │   ├── pseo/             # 📊 pSEO API
│   │   │   ├── backend/index.ts  # 🔌 Plugin export
│   │   │   ├── routes/pseo.ts    # 🛣️ SEO API routes
│   │   │   └── services/     # ⚙️ SEO services
│   │   └── aichat/           # 🤖 AI Chat API
│   │       └── backend/index.ts  # 🔌 CopilotKit integration
│   └── base/                 # 🏗️ Core backend infrastructure
│       └── common/           # 🔧 Shared utilities, auth, config
│
├── base/                     # 🏗️ Core Frontend Infrastructure
│   ├── components/           # 🧱 Shared UI components
│   ├── contextapi/           # 🔄 Core contexts (UserContext, etc.)
│   ├── utils/                # 🔧 Core utilities
│   └── hooks/                # 🪝 Shared hooks
├── header/                   # 🧭 Dynamic navigation
│   └── Header.tsx            # 🧭 Auto-loads menu from plugins
├── layout/                   # 🎨 Layout components
│   └── MainLayout.tsx        # 🎨 Plugin-agnostic layout
└── supabase/                 # 🗄️ Database configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn
- Supabase account and project

### Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd altzero-base
   ```

2. **Install Dependencies**:
   ```bash
   # Frontend
   npm install
   
   # Backend
   cd server && npm install && cd ..
   ```

3. **Environment Setup**:
   ```bash
   # Copy template and configure
   cp env.template .env
   # Edit .env with your Supabase and API keys
   ```

4. **Start Development**:
   ```bash
   # Start both frontend and backend
   npm run dev
   
   # Or start separately:
   npm run dev:client  # Frontend only
   npm run dev:server  # Backend only
   ```

5. **Access Application**:
   - Frontend: `http://localhost:3000`
   - Backend API: `http://localhost:3001`

## 🔧 Environment Variables

### Frontend (.env)
```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# API Configuration
VITE_API_URL=http://localhost:3001

# Feature Flags
VITE_ENABLE_KNOWLEDGE_BASE=true
VITE_ENABLE_PSEO=true
VITE_ENABLE_AI_CHAT=true
```

### Backend (server/.env)
```bash
# Server Configuration
PORT=3001
NODE_ENV=development

# Database
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# AI Services
OPENAI_API_KEY=your_openai_key
LLAMA_CLOUD_API_KEY=your_llama_cloud_key
PINECONE_API_KEY=your_pinecone_key
PINECONE_INDEX_NAME=your_pinecone_index

# Authentication
SESSION_SECRET=your_session_secret
API_KEY=your_api_key

# SEO Services
SEO_PAGESPEED_API_KEY=your_lighthouse_api_key
SEO_GENERATOR_API_KEY=your_seo_generator_key
```

## 🔌 Current Plugins

### 🧠 Knowledge Base
- **Frontend**: Document upload, AI chat, search
- **Backend**: Pinecone vector storage, RAG with LangGraph
- **Features**: PDF parsing, semantic search, conversational AI

### 📊 Programmatic SEO (pSEO)
- **Frontend**: Client management, website audits, dashboards
- **Backend**: SEO analysis, Lighthouse integration
- **Features**: Multi-client SEO auditing, automated reporting

### 🤖 AI Chat (CopilotKit)
- **Frontend**: Chat interface with document context
- **Backend**: OpenAI integration, context management
- **Features**: Document-aware conversations, streaming responses

## 🎯 Adding New Plugins

### Step 1: Register Plugin
```typescript
// plugins/registry.ts
export const PLUGIN_REGISTRY = {
  // ... existing plugins
  mynewplugin: {
    enabled: true,
    name: 'My New Plugin',
    icon: 'Star',
    route: '/mynewplugin',
    backend: true,
    version: '1.0.0'
  }
};
```

### Step 2: Create Frontend Plugin
```bash
mkdir -p features/mynewplugin/{pages,components,services}
```

```typescript
// features/mynewplugin/index.ts
import React from 'react';
import { PluginModule } from '../../plugins/types';
import MyPluginPage from './pages/MyPluginPage';

const mynewpluginPlugin: PluginModule = {
  routes: [{ path: '/mynewplugin', element: React.createElement(MyPluginPage) }],
  navigation: [{ name: 'My New Plugin', route: '/mynewplugin', icon: 'Star' }],
  config: { name: 'My New Plugin', version: '1.0.0' }
};

export default mynewpluginPlugin;
```

### Step 3: Create Backend Plugin (Optional)
```bash
mkdir -p server/features/mynewplugin/{backend,routes}
```

```typescript
// server/features/mynewplugin/backend/index.ts
import express from 'express';
import { BackendPlugin } from '../../../plugins/loader';

const mynewpluginBackend: BackendPlugin = {
  router: express.Router(),
  config: { name: 'My New Plugin API', version: '1.0.0', apiPrefix: '/api/mynewplugin' }
};

export default mynewpluginBackend;
```

**That's it!** Your plugin automatically:
- ✅ Appears in navigation
- ✅ Handles routes
- ✅ Mounts backend APIs
- ✅ Integrates with authentication

## 🎨 Theme System

AltZero uses a sophisticated theme system with CSS variables:

```css
/* Always use theme variables, never hardcoded colors */
.my-component {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: 1px solid hsl(var(--border));
}
```

## 🔒 Security Features

- **Authentication**: Supabase Auth with email/password and social login
- **Authorization**: Role-based access control
- **API Security**: Rate limiting, CORS, input validation
- **Data Protection**: Row-level security (RLS) in Supabase

## 🚀 Deployment

### Frontend (Vercel/Netlify)
```bash
npm run build
# Deploy dist/ folder
```

### Backend (Railway/Heroku)
```bash
cd server
npm run build
# Deploy with start command: npm start
```

### Environment Variables
Set all production environment variables in your hosting platform.

## 📚 Development Guidelines

### Cursor Rules Compliance
This project follows strict development rules defined in `.cursorrules`:
- ❌ No hardcoded values - use environment variables and constants
- ✅ TypeScript strict mode with proper typing
- ✅ Theme system compliance - use CSS variables
- ✅ Plugin architecture - keep features modular
- ✅ Supabase best practices - RLS policies, typed queries

### Code Organization
- **Plugin-First**: All features are plugins
- **Type Safety**: Complete TypeScript coverage
- **Separation of Concerns**: Clear boundaries between UI, logic, and data
- **Reusability**: Shared components in `base/`

## 🧪 Testing

```bash
# Run frontend tests
npm run test

# Run backend tests
cd server && npm run test

# Run all tests
npm run test:all
```

## 📖 Documentation

- **Plugin Architecture**: `wiki/plugin_arch.md`
- **API Documentation**: Auto-generated from OpenAPI specs
- **Component Docs**: Storybook integration
- **Database Schema**: Supabase documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-new-plugin`
3. Follow the plugin architecture guidelines
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
