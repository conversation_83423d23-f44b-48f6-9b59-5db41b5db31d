import React from "react";
import { useNavigate } from "react-router-dom";
import { formatDistanceToNow } from "date-fns";

import { Button } from "../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Organization, MemberRole } from "../../types/organization";

interface OrganizationCardProps {
  organization: Organization;
  role: MemberRole;
  actions?: React.ReactNode;
}

export function OrganizationCard({
  organization,
  role,
  actions,
}: OrganizationCardProps) {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/organizations/${organization.id}`);
  };

  return (
    <Card className="transition-all hover:border-primary/50 cursor-pointer">
      <CardHeader className="pb-2">
        <CardTitle className="flex justify-between items-start">
          <span onClick={handleClick}>{organization.name}</span>
          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
            {role}
          </span>
        </CardTitle>
        <CardDescription>
          Created{" "}
          {formatDistanceToNow(new Date(organization.created_at), {
            addSuffix: true,
          })}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {organization.description || "No description provided."}
        </p>
      </CardContent>
      <CardFooter className="pt-2 flex justify-between">
        <Button variant="outline" size="sm" onClick={handleClick}>
          View Details
        </Button>
        {actions}
      </CardFooter>
    </Card>
  );
}
