import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../../../../base/components/ui/dialog";
import { Button } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import { Label } from "../../../../base/components/ui/label";
import { Loader2 } from "lucide-react";
import axios from "axios";

interface WaitlistModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function WaitlistModal({ isOpen, onClose }: WaitlistModalProps) {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");
  const [errorMessage, setErrorMessage] = useState("");

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset status and error message
    setStatus("loading");
    setErrorMessage("");

    if (!isValidEmail(email)) {
      setStatus("error");
      setErrorMessage("Please enter a valid email address");
      return;
    }

    try {
      // Get API URL from env or use default
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";
      // Update URL to match backend router structure
      const response = await axios.post(`${apiUrl}/api/waitlist`, { email });

      if (response.data.success) {
        setStatus("success");
      } else {
        throw new Error(response.data.error || "Failed to join waitlist");
      }
    } catch (error: any) {
      setStatus("error");
      // Handle different types of errors
      if (error.response?.status === 409) {
        setErrorMessage("This email is already on our waitlist!");
      } else {
        setErrorMessage(
          error.response?.data?.error ||
            "Something went wrong. Please try again."
        );
      }
      console.error("Waitlist signup error:", error);
    }
  };

  const handleClose = () => {
    // Reset the form when closing
    if (status === "success") {
      setEmail("");
      setStatus("idle");
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-2xl">Join Our Waitlist</DialogTitle>
          <DialogDescription>
            Be the first to know when ScopingAI launches. We'll email you with
            updates.
          </DialogDescription>
        </DialogHeader>

        {status === "success" ? (
          <div className="py-6 text-center space-y-3">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <svg
                className="h-6 w-6 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-center">Thank You!</h3>
            <p className="text-sm text-gray-500">
              You've been added to our waitlist. We'll{" "}
              <br className="hidden md:block" />
              notify you when we launch!
            </p>
            <Button className="mt-4" onClick={handleClose} variant="outline">
              Close
            </Button>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className={status === "error" ? "border-red-500" : ""}
                disabled={status === "loading"}
                required
              />
              {status === "error" && (
                <p className="text-sm text-red-500">{errorMessage}</p>
              )}
            </div>
            <DialogFooter>
              <Button
                type="submit"
                disabled={status === "loading" || !email}
                className="w-full"
              >
                {status === "loading" && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {status === "loading" ? "Submitting..." : "Join Waitlist"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
