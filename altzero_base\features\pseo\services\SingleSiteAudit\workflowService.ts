import { auditOrchestrationService } from './auditOrchestrationService';
import { PSEO_CONSTANTS } from '../../utilities/pseo/constants';
import type { 
  AuditProgress,
  PSEOError
} from '../../types';

export interface WorkflowOptions {
  aiModel?: string;
  userId?: string;
  skipSteps?: string[];
  customPrompts?: {
    technical?: string;
    content?: string;
  };
  websiteId?: string;
}

export interface WorkflowResult {
  audit: any;
  scrapedData: any;
  analysis: any;
  report: any;
  processingTime: number;
}

class WorkflowService {
  /**
   * Register progress callback for an audit
   */
  onProgress(auditId: string, callback: (progress: AuditProgress) => void): void {
    auditOrchestrationService.onProgress(auditId, callback);
  }

  /**
   * Register error callback for an audit
   */
  onError(auditId: string, callback: (error: PSEOError) => void): void {
    auditOrchestrationService.onError(auditId, callback);
  }

  /**
   * Remove callbacks for an audit
   */
  removeCallbacks(auditId: string): void {
    auditOrchestrationService.removeCallbacks(auditId);
  }

  /**
   * Start a simplified pSEO audit workflow
   */
  async startAudit(
    websiteUrl: string,
    websiteName: string,
    options: WorkflowOptions = {}
  ): Promise<WorkflowResult> {
    console.log(`🔄 WorkflowService delegating to AuditOrchestrationService for: ${websiteUrl}`);
    
    return auditOrchestrationService.startAudit(websiteUrl, websiteName, {
      aiModel: options.aiModel,
      userId: options.userId,
      skipSteps: options.skipSteps,
      customPrompts: options.customPrompts,
      websiteId: options.websiteId,
    });
  }

  /**
   * Get audit progress
   */
  async getAuditProgress(auditId: string): Promise<AuditProgress> {
    return auditOrchestrationService.getAuditProgress(auditId);
  }

  /**
   * Cancel a running audit
   */
  async cancelAudit(auditId: string): Promise<void> {
    return auditOrchestrationService.cancelAudit(auditId);
  }

  /**
   * Validate workflow configuration
   */
  async validateConfiguration(): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    return auditOrchestrationService.validateConfiguration();
  }
}

export const workflowService = new WorkflowService(); 