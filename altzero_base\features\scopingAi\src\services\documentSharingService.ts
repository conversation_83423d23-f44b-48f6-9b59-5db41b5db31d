import { supabase } from "../lib/supabase";
import {
  SharePermissionLevel,
  ShareTarget,
  ShareRequest,
  ShareResponse,
  DocumentShareData,
  ShareSearchResult,
  ShareEntityType,
  PERMISSION_LEVELS,
} from "../types/sharing";
import { generateSecureToken, hashPassword } from "../utils/cryptoUtils";

/**
 * Check if a permission level includes another permission
 */
const hasPermission = (
  userPermission: SharePermissionLevel,
  requiredPermission: SharePermissionLevel
): boolean => {
  return (
    PERMISSION_LEVELS[userPermission] >= PERMISSION_LEVELS[requiredPermission]
  );
};

// =============================================
// PERMISSION UTILITIES
// =============================================

/**
 * Check user's permission level on a document
 */
export const getUserDocumentPermission = async (
  documentId: string,
  userId?: string
): Promise<SharePermissionLevel | null> => {
  try {
    const targetUserId =
      userId || (await supabase.auth.getUser()).data.user?.id;
    if (!targetUserId) return null;

    // Check if user is document owner (using scopingai_documents table)
    const { data: document, error: docError } = await supabase
      .from("scopingai_documents")
      .select("user_id")
      .eq("id", documentId)
      .single();

    if (docError) throw docError;

    if (document?.user_id === targetUserId) {
      return "admin";
    }

    // Get all permission levels for this user
    const permissions: SharePermissionLevel[] = [];

    // Check direct user shares
    const { data: userShares, error: userSharesError } = await supabase
      .from("scopingai_documentshares")
      .select("permission_level")
      .eq("document_id", documentId)
      .eq("entity_type", "user")
      .eq("entity_id", targetUserId)
      .eq("is_active", true)
      .or("expires_at.is.null,expires_at.gt.now()");

    if (!userSharesError && userShares?.length > 0) {
      permissions.push(...userShares.map((s) => s.permission_level));
    }

    // Check team shares - get user's groups first
    const { data: userGroups, error: groupsError } = await supabase
      .from("group_members")
      .select("group_id")
      .eq("user_id", targetUserId);

    if (!groupsError && userGroups?.length > 0) {
      const groupIds = userGroups.map((g) => g.group_id);
      const { data: teamShares, error: teamSharesError } = await supabase
        .from("scopingai_documentshares")
        .select("permission_level")
        .eq("document_id", documentId)
        .eq("entity_type", "team")
        .eq("is_active", true)
        .or("expires_at.is.null,expires_at.gt.now()")
        .in("entity_id", groupIds);

      if (!teamSharesError && teamShares?.length > 0) {
        permissions.push(...teamShares.map((s) => s.permission_level));
      }
    }

    // Check organization shares - get user's organizations first
    const { data: userOrgs, error: orgsError } = await supabase
      .from("organisation_members")
      .select("organisation_id")
      .eq("user_id", targetUserId);

    if (!orgsError && userOrgs?.length > 0) {
      const orgIds = userOrgs.map((o) => o.organisation_id);
      const { data: orgShares, error: orgSharesError } = await supabase
        .from("scopingai_documentshares")
        .select("permission_level")
        .eq("document_id", documentId)
        .eq("entity_type", "organisation")
        .eq("is_active", true)
        .or("expires_at.is.null,expires_at.gt.now()")
        .in("entity_id", orgIds);

      if (!orgSharesError && orgShares?.length > 0) {
        permissions.push(...orgShares.map((s) => s.permission_level));
      }
    }

    // Return highest permission level
    if (permissions.length === 0) return null;

    const highestPermission = permissions.reduce((highest, current) => {
      return PERMISSION_LEVELS[current] > PERMISSION_LEVELS[highest]
        ? current
        : highest;
    });

    return highestPermission;
  } catch (error) {
    console.error("Error checking document permission:", error);
    return null;
  }
};

// =============================================
// DOCUMENT SHARING OPERATIONS
// =============================================

/**
 * Share a document with a user using direct queries
 */
export const shareDocumentWithUser = async (
  documentId: string,
  userId: string,
  permissionLevel: SharePermissionLevel,
  customMessage?: string,
  expiresAt?: string
): Promise<string> => {
  // Check if current user has permission to share
  const currentUser = await supabase.auth.getUser();
  if (!currentUser.data.user) throw new Error("Not authenticated");

  // Check if user is document owner
  const { data: document, error: docError } = await supabase
    .from("scopingai_documents")
    .select("user_id")
    .eq("id", documentId)
    .single();

  if (docError) throw docError;
  if (document?.user_id !== currentUser.data.user.id) {
    throw new Error("You do not have permission to share this document");
  }

  // Check if target user exists
  const { data: targetUser, error: userError } = await supabase
    .from("profiles")
    .select("id")
    .eq("id", userId)
    .single();

  if (userError) throw new Error("Target user does not exist");

  // Create or update share
  const { data, error } = await supabase
    .from("scopingai_documentshares")
    .upsert(
      {
        document_id: documentId,
        entity_type: "user",
        entity_id: userId,
        permission_level: permissionLevel,
        shared_by: currentUser.data.user.id,
        custom_message: customMessage,
        expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
        shared_at: new Date().toISOString(),
        is_active: true,
      },
      {
        onConflict: "document_id,entity_type,entity_id",
      }
    )
    .select()
    .single();

  if (error) throw error;
  return data.id;
};

/**
 * Share a document with a team using direct queries
 */
export const shareDocumentWithTeam = async (
  documentId: string,
  groupId: string,
  permissionLevel: SharePermissionLevel,
  customMessage?: string,
  expiresAt?: string
): Promise<string> => {
  // Check if current user has permission to share
  const currentUser = await supabase.auth.getUser();
  if (!currentUser.data.user) throw new Error("Not authenticated");

  // Check if user is document owner
  const { data: document, error: docError } = await supabase
    .from("scopingai_documents")
    .select("user_id")
    .eq("id", documentId)
    .single();

  if (docError) throw docError;
  if (document?.user_id !== currentUser.data.user.id) {
    throw new Error("You do not have permission to share this document");
  }

  // Check if group exists
  const { data: group, error: groupError } = await supabase
    .from("groups")
    .select("id")
    .eq("id", groupId)
    .single();

  if (groupError) throw new Error("Team does not exist");

  // Create or update share
  const { data, error } = await supabase
    .from("scopingai_documentshares")
    .upsert(
      {
        document_id: documentId,
        entity_type: "team",
        entity_id: groupId,
        permission_level: permissionLevel,
        shared_by: currentUser.data.user.id,
        custom_message: customMessage,
        expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
        shared_at: new Date().toISOString(),
        is_active: true,
      },
      {
        onConflict: "document_id,entity_type,entity_id",
      }
    )
    .select()
    .single();

  if (error) throw error;
  return data.id;
};

/**
 * Share a document with an organization using direct queries
 */
export const shareDocumentWithOrganisation = async (
  documentId: string,
  organisationId: string,
  permissionLevel: SharePermissionLevel,
  customMessage?: string,
  expiresAt?: string
): Promise<string> => {
  // Check if current user has permission to share
  const currentUser = await supabase.auth.getUser();
  if (!currentUser.data.user) throw new Error("Not authenticated");

  // Check if user is document owner
  const { data: document, error: docError } = await supabase
    .from("scopingai_documents")
    .select("user_id")
    .eq("id", documentId)
    .single();

  if (docError) throw docError;
  if (document?.user_id !== currentUser.data.user.id) {
    throw new Error("You do not have permission to share this document");
  }

  // Check if organization exists
  const { data: org, error: orgError } = await supabase
    .from("organisations")
    .select("id")
    .eq("id", organisationId)
    .single();

  if (orgError) throw new Error("Organization does not exist");

  // Create or update share
  const { data, error } = await supabase
    .from("scopingai_documentshares")
    .upsert(
      {
        document_id: documentId,
        entity_type: "organisation",
        entity_id: organisationId,
        permission_level: permissionLevel,
        shared_by: currentUser.data.user.id,
        custom_message: customMessage,
        expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
        shared_at: new Date().toISOString(),
        is_active: true,
      },
      {
        onConflict: "document_id,entity_type,entity_id",
      }
    )
    .select()
    .single();

  if (error) throw error;
  return data.id;
};

/**
 * Share a document with multiple targets (users, teams, organizations)
 */
export const shareDocument = async (
  documentId: string,
  request: ShareRequest
): Promise<ShareResponse> => {
  try {
    const currentUser = await supabase.auth.getUser();
    if (!currentUser.data.user) {
      return { success: false, message: "Not authenticated" };
    }

    // Check if current user has permission to share
    const userPermission = await getUserDocumentPermission(
      documentId,
      currentUser.data.user.id
    );

    if (!userPermission || !hasPermission(userPermission, "manage")) {
      return {
        success: false,
        message: "Insufficient permissions to share document",
      };
    }

    const shares: DocumentShareData[] = [];
    const errors: string[] = [];

    // Process each target
    for (const target of request.targets) {
      try {
        let shareId: string;

        switch (target.type) {
          case "user":
            shareId = await shareDocumentWithUser(
              documentId,
              target.id,
              request.permissionLevel,
              request.message,
              request.expiresAt
            );
            break;
          case "team":
            shareId = await shareDocumentWithTeam(
              documentId,
              target.id,
              request.permissionLevel,
              request.message,
              request.expiresAt
            );
            break;
          case "organisation":
            shareId = await shareDocumentWithOrganisation(
              documentId,
              target.id,
              request.permissionLevel,
              request.message,
              request.expiresAt
            );
            break;
          default:
            throw new Error(`Unsupported target type: ${target.type}`);
        }

        // Fetch the created share
        const { data: shareData, error: shareError } = await supabase
          .from("scopingai_documentshares")
          .select("*")
          .eq("id", shareId)
          .single();

        if (shareError) throw shareError;

        shares.push(transformShareData(shareData));

        // Log activity
        await logDocumentActivity(documentId, "share", {
          entity_type: target.type,
          entity_id: target.id,
          permission_level: request.permissionLevel,
        });

        // Send notifications if requested
        if (request.notifyTargets) {
          await sendShareNotification(target, documentId, request.message);
        }
      } catch (err) {
        console.error(`Failed to share with ${target.name}:`, err);
        errors.push(
          `Failed to share with ${target.name}: ${
            err instanceof Error ? err.message : "Unknown error"
          }`
        );
      }
    }

    return {
      success: shares.length > 0,
      shares,
      message:
        errors.length > 0
          ? `Shared with ${shares.length} targets. ${
              errors.length
            } failed: ${errors.join(", ")}`
          : `Successfully shared with ${shares.length} targets`,
    };
  } catch (error) {
    console.error("Error sharing document:", error);
    return {
      success: false,
      message: "Failed to share document",
    };
  }
};

/**
 * Get all shares for a document
 */
export const getDocumentShares = async (
  documentId: string
): Promise<{
  shares: DocumentShareData[];
}> => {
  try {
    // Get all shares for the document (excluding link shares)
    const { data: allShares, error } = await supabase
      .from("scopingai_documentshares")
      .select("*")
      .eq("document_id", documentId)
      .eq("is_active", true)
      .neq("entity_type", "link");

    if (error) throw error;

    if (!allShares || allShares.length === 0) {
      return { shares: [] };
    }

    // Get entity details for shares
    const sharesWithDetails = await Promise.all(
      allShares.map(async (share) => {
        let entityDetails = null;

        try {
          switch (share.entity_type) {
            case "user":
              if (share.entity_id) {
                const { data: user } = await supabase
                  .from("profiles")
                  .select("id, full_name, email")
                  .eq("id", share.entity_id)
                  .single();
                entityDetails = user;
              }
              break;
            case "team":
              if (share.entity_id) {
                const { data: group } = await supabase
                  .from("groups")
                  .select("id, name, description")
                  .eq("id", share.entity_id)
                  .single();
                entityDetails = group;
              }
              break;
            case "organisation":
              if (share.entity_id) {
                const { data: org } = await supabase
                  .from("organisations")
                  .select("id, name, description")
                  .eq("id", share.entity_id)
                  .single();
                entityDetails = org;
              }
              break;
          }
        } catch (err) {
          console.warn(
            `Failed to fetch entity details for ${share.entity_type} ${share.entity_id}:`,
            err
          );
        }

        return {
          ...share,
          entityDetails,
        };
      })
    );

    return {
      shares: sharesWithDetails.map(transformShareData),
    };
  } catch (error) {
    console.error("Error fetching document shares:", error);
    return { shares: [] };
  }
};

/**
 * Get shared documents for current user using direct queries
 * Includes documents shared directly with user, with their teams, and with their organizations
 */
export const getSharedDocuments = async (): Promise<any[]> => {
  try {
    const currentUser = await supabase.auth.getUser();
    if (!currentUser.data.user) throw new Error("Not authenticated");

    const userId = currentUser.data.user.id;
    const allSharedDocuments: any[] = [];

    // 1. Get documents shared directly with the user
    const { data: userShares, error: userSharesError } = await supabase
      .from("scopingai_documentshares")
      .select(
        `
        document_id,
        permission_level,
        shared_at,
        expires_at,
        entity_type,
        entity_id,
        shared_by,
        scopingai_documents!inner(
          id,
          title,
          status,
          scopingai_clients (
            name
          )
        )
      `
      )
      .eq("entity_type", "user")
      .eq("entity_id", userId)
      .eq("is_active", true)
      .or("expires_at.is.null,expires_at.gt.now()");

    if (!userSharesError && userShares) {
      allSharedDocuments.push(
        ...userShares.map((share) => ({
          ...share,
          share_source: "direct",
          share_source_name: "Direct Share",
        }))
      );
    }

    // 2. Get user's group memberships and fetch team shares
    const { data: userGroups, error: groupsError } = await supabase
      .from("group_members")
      .select(
        `
        group_id, 
        groups!inner(name)
      `
      )
      .eq("user_id", userId);

    if (!groupsError && userGroups && userGroups.length > 0) {
      const groupIds = userGroups.map((g) => g.group_id);

      const { data: teamShares, error: teamSharesError } = await supabase
        .from("scopingai_documentshares")
        .select(
          `
          document_id,
          permission_level,
          shared_at,
          expires_at,
          entity_type,
          entity_id,
          shared_by,
          scopingai_documents!inner(
            id,
            title,
            status,
            scopingai_clients (
              name
            )
          )
        `
        )
        .eq("entity_type", "team")
        .eq("is_active", true)
        .or("expires_at.is.null,expires_at.gt.now()")
        .in("entity_id", groupIds);

      if (!teamSharesError && teamShares) {
        // Add team shares with team name information
        const teamSharesWithSource = teamShares.map((share) => {
          const teamInfo = userGroups.find(
            (g) => g.group_id === share.entity_id
          );
          return {
            ...share,
            share_source: "team",
            share_source_name: (teamInfo?.groups as any)?.name || "Team",
          };
        });

        allSharedDocuments.push(...teamSharesWithSource);
      }
    }

    // 3. Get user's organization memberships and fetch org shares
    const { data: userOrgs, error: orgsError } = await supabase
      .from("organisation_members")
      .select(
        `
        organisation_id, 
        organisations!inner(name)
      `
      )
      .eq("user_id", userId);

    if (!orgsError && userOrgs && userOrgs.length > 0) {
      const orgIds = userOrgs.map((o) => o.organisation_id);

      const { data: orgShares, error: orgSharesError } = await supabase
        .from("scopingai_documentshares")
        .select(
          `
          document_id,
          permission_level,
          shared_at,
          expires_at,
          entity_type,
          entity_id,
          shared_by,
          scopingai_documents!inner(
            id,
            title,
            status,
            scopingai_clients (
              name
            )
          )
        `
        )
        .eq("entity_type", "organisation")
        .eq("is_active", true)
        .or("expires_at.is.null,expires_at.gt.now()")
        .in("entity_id", orgIds);

      if (!orgSharesError && orgShares) {
        // Add org shares with organization name information
        const orgSharesWithSource = orgShares.map((share) => {
          const orgInfo = userOrgs.find(
            (o) => o.organisation_id === share.entity_id
          );
          return {
            ...share,
            share_source: "organisation",
            share_source_name:
              (orgInfo?.organisations as any)?.name || "Organization",
          };
        });

        allSharedDocuments.push(...orgSharesWithSource);
      }
    }

    // 4. Remove duplicates (same document shared through multiple channels)
    // Keep the highest permission level if document is shared multiple ways
    const documentMap = new Map();

    allSharedDocuments.forEach((share) => {
      const docId = share.document_id;
      const existingShare = documentMap.get(docId);

      if (
        !existingShare ||
        PERMISSION_LEVELS[share.permission_level as SharePermissionLevel] >
          PERMISSION_LEVELS[
            existingShare.permission_level as SharePermissionLevel
          ]
      ) {
        documentMap.set(docId, share);
      }
    });

    // 5. Get unique shared_by user IDs and fetch their details
    const uniqueSharedByIds = [
      ...new Set(
        Array.from(documentMap.values())
          .map((share) => share.shared_by)
          .filter(Boolean)
      ),
    ];
    const sharedByUsers = new Map();

    if (uniqueSharedByIds.length > 0) {
      const { data: profiles } = await supabase
        .from("profiles")
        .select("id, name")
        .in("id", uniqueSharedByIds);

      if (profiles) {
        profiles.forEach((profile) => {
          sharedByUsers.set(profile.id, profile.name);
        });
      }
    }

    // 6. Transform the data to match the expected format
    return Array.from(documentMap.values()).map((item) => ({
      document_id: item.document_id,
      title: item.scopingai_documents?.title,
      client_name:
        item.scopingai_documents?.scopingai_clients?.name || "Unknown Client",
      status: item.scopingai_documents?.status,
      permission_level: item.permission_level,
      shared_by_name: sharedByUsers.get(item.shared_by) || "Unknown User",
      shared_at: item.shared_at,
      expires_at: item.expires_at,
      share_source: item.share_source,
      share_source_name: item.share_source_name,
      entity_type: item.entity_type,
    }));
  } catch (error) {
    console.error("Error fetching shared documents:", error);
    return [];
  }
};

/**
 * Add a comment to a document using direct queries
 */
export const addDocumentComment = async (
  documentId: string,
  content: string,
  sectionId?: string,
  parentCommentId?: string
): Promise<string> => {
  // Check if current user has permission
  const currentUser = await supabase.auth.getUser();
  if (!currentUser.data.user) throw new Error("Not authenticated");

  // Check if user has access to the document
  const permission = await getUserDocumentPermission(
    documentId,
    currentUser.data.user.id
  );
  if (!permission || !hasPermission(permission, "comment")) {
    throw new Error("You do not have permission to comment on this document");
  }

  // Add comment
  const { data, error } = await supabase
    .from("document_interactions")
    .insert({
      document_id: documentId,
      user_id: currentUser.data.user.id,
      interaction_type: "comment",
      content,
      section_id: sectionId,
      parent_comment_id: parentCommentId,
    })
    .select()
    .single();

  if (error) throw error;
  return data.id;
};

/**
 * Update a document share
 */
export const updateDocumentShare = async (
  shareId: string,
  updates: Partial<DocumentShareData>
): Promise<void> => {
  try {
    const { error } = await supabase
      .from("scopingai_documentshares")
      .update({
        permission_level: updates.permissionLevel,
        expires_at: updates.expiresAt,
        custom_message: updates.customMessage,
        notify_on_changes: updates.notifyOnChanges,
        is_active: updates.isActive,
      })
      .eq("id", shareId);

    if (error) throw error;
  } catch (error) {
    console.error("Error updating document share:", error);
    throw error;
  }
};

/**
 * Revoke a document share
 */
export const revokeDocumentShare = async (shareId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from("scopingai_documentshares")
      .update({ is_active: false })
      .eq("id", shareId);

    if (error) throw error;
  } catch (error) {
    console.error("Error revoking document share:", error);
    throw error;
  }
};

/**
 * Search for share targets (users, teams, organizations)
 */
export const searchShareTargets = async (
  query: string
): Promise<ShareSearchResult> => {
  try {
    console.log("Searching for:", query);

    // Search users with correct column names
    const usersResult = await supabase
      .from("profiles")
      .select("id, name, email")
      .or(`name.ilike.%${query}%,email.ilike.%${query}%`)
      .limit(10);

    console.log("Users search result:", usersResult);

    // Search teams/groups with member count
    const teamsResult = await supabase
      .from("groups")
      .select(
        `
        id, 
        name, 
        description,
        group_members(count)
      `
      )
      .ilike("name", `%${query}%`)
      .limit(10);

    console.log("Teams search result:", teamsResult);

    // Search organizations with member count
    const orgsResult = await supabase
      .from("organisations")
      .select(
        `
        id, 
        name, 
        description,
        organisation_members(count)
      `
      )
      .ilike("name", `%${query}%`)
      .limit(10);

    console.log("Organizations search result:", orgsResult);

    const results = {
      users: (usersResult?.data || []).map((user) => ({
        id: user.id,
        name: user.name || user.email,
        email: user.email,
        type: "user" as ShareEntityType,
        avatar: null,
      })),
      teams: (teamsResult.data || []).map((team) => ({
        id: team.id,
        name: team.name,
        description: team.description,
        type: "team" as ShareEntityType,
        avatar: null,
        memberCount: (team as any).group_members?.length || 0,
      })),
      organizations: (orgsResult.data || []).map((org) => ({
        id: org.id,
        name: org.name,
        description: org.description,
        type: "organisation" as ShareEntityType,
        avatar: null,
        memberCount: (org as any).organisation_members?.length || 0,
      })),
    };

    console.log("Final search results:", results);
    return results;
  } catch (error) {
    console.error("Error searching share targets:", error);
    return { users: [], teams: [], organizations: [] };
  }
};

/**
 * Log document activity
 */
export const logDocumentActivity = async (
  documentId: string,
  action: string,
  metadata?: Record<string, any>
): Promise<void> => {
  try {
    const { error } = await supabase.from("document_interactions").insert({
      document_id: documentId,
      user_id: (await supabase.auth.getUser()).data.user?.id,
      interaction_type: "activity",
      action,
      metadata: metadata || {},
    });

    if (error) throw error;
  } catch (error) {
    console.error("Error logging document activity:", error);
    // Don't throw - activity logging shouldn't break functionality
  }
};

// =============================================
// UTILITY FUNCTIONS
// =============================================

/**
 * Send notification for document sharing
 */
const sendShareNotification = async (
  target: ShareTarget,
  documentId: string,
  message?: string
): Promise<void> => {
  // TODO: Implement notification system
  // This could send emails, in-app notifications, etc.
  console.log(`Notification sent to ${target.name} for document ${documentId}`);
};

/**
 * Transform database share data to frontend format
 */
const transformShareData = (data: any): DocumentShareData => ({
  id: data.id,
  documentId: data.document_id,
  entityType: data.entity_type,
  entityId: data.entity_id,
  permissionLevel: data.permission_level,
  sharedBy: data.shared_by,
  sharedAt: data.shared_at,
  expiresAt: data.expires_at,
  accessToken: undefined, // Not used in unified schema
  isActive: data.is_active,
  customMessage: data.custom_message,
  notifyOnChanges: data.notify_on_changes,
  lastAccessedAt: data.last_accessed_at,
  accessCount: data.access_count,
  // Include entity details if available
  entityDetails: data.entityDetails,
});
