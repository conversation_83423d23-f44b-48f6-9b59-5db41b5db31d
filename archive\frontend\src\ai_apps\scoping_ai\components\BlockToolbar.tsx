import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { BlockType } from './ProposalEditor';

interface BlockToolbarProps {
  onAddBlock: (type: BlockType) => void;
}

interface DraggableBlockProps {
  type: BlockType;
  label: string;
  icon: string;
  onAddBlock: (type: BlockType) => void;
}

const DraggableBlock: React.FC<DraggableBlockProps> = ({ 
  type, 
  label, 
  icon, 
  onAddBlock 
}) => {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: `new-${type}`,
    data: {
      type,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`flex items-center p-3 bg-white rounded-lg border ${
        isDragging ? 'border-indigo-500 shadow-md' : 'border-gray-200'
      } hover:bg-gray-50 hover:border-indigo-300 transition-colors cursor-grab active:cursor-grabbing`}
      {...listeners}
      {...attributes}
      onClick={() => onAddBlock(type)}
    >
      <div className="flex-shrink-0 w-8 h-8 bg-indigo-50 rounded-md flex items-center justify-center">
        <svg 
          className="w-5 h-5 text-indigo-600" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d={icon} 
          />
        </svg>
      </div>
      <div className="ml-3">
        <p className="text-sm font-medium text-gray-900">{label}</p>
      </div>
    </div>
  );
};

export const BlockToolbar: React.FC<BlockToolbarProps> = ({ onAddBlock }) => {
  const blockTypes: { type: BlockType; label: string; icon: string }[] = [
    { 
      type: 'header', 
      label: 'Heading', 
      icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    },
    { 
      type: 'text', 
      label: 'Text', 
      icon: 'M4 6h16M4 12h16M4 18h7'
    },
    { 
      type: 'image', 
      label: 'Image', 
      icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'
    },
    { 
      type: 'quote', 
      label: 'Quote', 
      icon: 'M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z'
    },
    { 
      type: 'section', 
      label: 'Section', 
      icon: 'M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z'
    },
    { 
      type: 'diagram', 
      label: 'Diagram', 
      icon: 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z'
    },
    { 
      type: 'list', 
      label: 'List', 
      icon: 'M4 6h16M4 10h16M4 14h16M4 18h16'
    },
  ];

  return (
    <div className="flex flex-col space-y-2">
      {blockTypes.map((blockType) => (
        <DraggableBlock
          key={blockType.type}
          type={blockType.type}
          label={blockType.label}
          icon={blockType.icon}
          onAddBlock={onAddBlock}
        />
      ))}
    </div>
  );
}; 