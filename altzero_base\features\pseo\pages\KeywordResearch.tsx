import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/pseo/databaseService';
import { useUser } from '../../../base/contextapi/UserContext';
import type { PSEOClient, PSEOWebsite } from '../types';
import PSEOLayout from '../components/PSEOLayout';

interface KeywordData {
  keyword: string;
  search_volume: number;
  keyword_difficulty: number;
  cpc: number;
  competition: 'low' | 'medium' | 'high';
  intent: 'informational' | 'navigational' | 'commercial' | 'transactional';
  trend: 'rising' | 'stable' | 'declining';
}

interface KeywordGroup {
  theme: string;
  keywords: KeywordData[];
}

const KeywordResearch: React.FC = () => {
  const { user } = useUser();
  
  // State for website selection
  const [clients, setClients] = useState<PSEOClient[]>([]);
  const [selectedClient, setSelectedClient] = useState<PSEOClient | null>(null);
  const [websites, setWebsites] = useState<PSEOWebsite[]>([]);
  const [selectedWebsite, setSelectedWebsite] = useState<PSEOWebsite | null>(null);
  
  // State for research inputs
  const [researchMethod, setResearchMethod] = useState<'website' | 'topic' | 'keywords'>('website');
  const [topicInput, setTopicInput] = useState<string>('');
  const [seedKeywords, setSeedKeywords] = useState<string[]>([]);
  const [seedKeywordInput, setSeedKeywordInput] = useState<string>('');

  // State for API testing
  const [apiTestResults, setApiTestResults] = useState<any>(null);
  const [testingApis, setTestingApis] = useState(false);
  
  // State for research process
  const [loading, setLoading] = useState(false);
  const [researching, setResearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // State for results
  const [keywordGroups, setKeywordGroups] = useState<KeywordGroup[]>([]);
  const [totalKeywords, setTotalKeywords] = useState(0);
  const [selectedKeywords, setSelectedKeywords] = useState<KeywordData[]>([]);
  const [existingKeywords, setExistingKeywords] = useState<KeywordGroup[]>([]);
  const [existingKeywordsCount, setExistingKeywordsCount] = useState(0);
  const [existingClusters, setExistingClusters] = useState<any[]>([]);
  const [loadingExisting, setLoadingExisting] = useState(false);
  
  // State for filters
  const [filters, setFilters] = useState({
    minVolume: 0,
    maxDifficulty: 100,
    intent: 'all',
    competition: 'all'
  });

  // New Phase 3 features
  const [dataSources, setDataSources] = useState<string[]>([]);
  const [availableDataSources, setAvailableDataSources] = useState<any[]>([]);
  const [dataSourcesLoading, setDataSourcesLoading] = useState(true);
  const [lastDataSourcesFetch, setLastDataSourcesFetch] = useState<number>(0);
  const [maxKeywords, setMaxKeywords] = useState(100);
  const [workflowId, setWorkflowId] = useState<string | null>(null);
  const [workflowStatus, setWorkflowStatus] = useState<any>(null);

  useEffect(() => {
    if (user?.id) {
      loadClients();
    }
  }, [user?.id]);

  useEffect(() => {
    if (selectedClient) {
      loadWebsites();
    }
  }, [selectedClient]);

  // Fetch existing keywords when website is selected
  useEffect(() => {
    if (selectedWebsite?.id && researchMethod === 'website') {
      fetchExistingKeywords(selectedWebsite.id);
    } else {
      setExistingKeywords([]);
      setExistingKeywordsCount(0);
    }
  }, [selectedWebsite?.id, researchMethod]);

  // Fetch available data sources on component mount
  useEffect(() => {
    fetchAvailableDataSources();

    // Refresh data sources every 5 minutes to catch configuration changes (with caching)
    const interval = setInterval(() => {
      fetchAvailableDataSources(false); // Use cache if available
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, []);

  // Fetch available data sources from backend with caching
  const fetchAvailableDataSources = async (forceRefresh = false) => {
    const now = Date.now();
    const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

    // Skip if we have recent data and not forcing refresh
    if (!forceRefresh && lastDataSourcesFetch > 0 && (now - lastDataSourcesFetch) < CACHE_DURATION && availableDataSources.length > 0) {
      console.log('📋 Using cached data sources');
      return;
    }

    setDataSourcesLoading(true);
    try {
      console.log('🔍 Fetching available data sources...');
      const response = await fetch('/api/pseo/langgraph/data-sources');

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Data sources fetched successfully:', data);
        setAvailableDataSources(data.data_sources);
        setDataSources(data.default_selection || ['rapidapi']);
        setLastDataSourcesFetch(now);
      } else {
        console.warn('⚠️ Failed to fetch data sources, using defaults. Status:', response.status);
        const fallbackSources = [
          {
            id: 'rapidapi',
            name: 'RapidAPI (Ubersuggest + SimilarWeb)',
            description: 'Multi-provider API gateway with real data',
            enabled: true,
            recommended: true
          }
        ];
        setAvailableDataSources(fallbackSources);
        setDataSources(['rapidapi']);
        setLastDataSourcesFetch(now);
      }
    } catch (error) {
      console.error('❌ Error fetching data sources:', error);
      const fallbackSources = [
        {
          id: 'rapidapi',
          name: 'RapidAPI (Ubersuggest + SimilarWeb)',
          description: 'Multi-provider API gateway with real data',
          enabled: true,
          recommended: true
        }
      ];
      setAvailableDataSources(fallbackSources);
      setDataSources(['rapidapi']);
      setLastDataSourcesFetch(now);
    } finally {
      setDataSourcesLoading(false);
    }
  };

  const loadClients = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const clientsData = await databaseService.getClientsByUserId(user.id);
      setClients(clientsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const loadWebsites = async () => {
    if (!selectedClient) return;
    
    try {
      const websitesData = await databaseService.getWebsitesByClientId(selectedClient.id);
      setWebsites(websitesData);
    } catch (err) {
      console.error('Failed to load websites:', err);
    }
  };

  const addSeedKeyword = () => {
    if (seedKeywordInput.trim() && !seedKeywords.includes(seedKeywordInput.trim())) {
      setSeedKeywords(prev => [...prev, seedKeywordInput.trim()]);
      setSeedKeywordInput('');
    }
  };

  const removeSeedKeyword = (keyword: string) => {
    setSeedKeywords(prev => prev.filter(k => k !== keyword));
  };

  // Helper functions for data sources

  const toggleDataSource = (source: string) => {
    setDataSources(prev => {
      if (prev.includes(source)) {
        // Don't allow removing all sources
        if (prev.length === 1) return prev;
        return prev.filter(s => s !== source);
      } else {
        return [...prev, source];
      }
    });
  };

  // Test RapidAPI endpoints
  const testRapidAPIs = async () => {
    setTestingApis(true);
    setApiTestResults(null);

    try {
      console.log('🧪 Testing RapidAPI endpoints...');

      const response = await fetch('/api/pseo/langgraph/test-apis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user?.id || 'anonymous'
        },
        body: JSON.stringify({
          testKeyword: 'digital marketing',
          testDomain: 'example.com'
        })
      });

      if (response.ok) {
        const results = await response.json();
        setApiTestResults(results.data);
        console.log('✅ API test results:', results);
      } else {
        const errorData = await response.json();
        setApiTestResults({
          summary: { overall_status: 'failed' },
          tests: [],
          error: errorData.message || 'API test failed'
        });
      }
    } catch (error) {
      console.error('❌ API test error:', error);
      setApiTestResults({
        summary: { overall_status: 'failed' },
        tests: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setTestingApis(false);
    }
  };

  const startKeywordResearch = async () => {
    if (researchMethod === 'website' && !selectedWebsite) {
      setError('Please select a website for domain analysis');
      return;
    }

    if (researchMethod === 'topic' && !topicInput.trim()) {
      setError('Please enter a topic for research');
      return;
    }

    if (researchMethod === 'keywords' && seedKeywords.length === 0) {
      setError('Please add at least one seed keyword for research');
      return;
    }

    try {
      setResearching(true);
      setError(null);
      setKeywordGroups([]);

      // Get selected data source names for logging
      const selectedSourceNames = dataSources.map(sourceId => {
        const source = availableDataSources.find(ds => ds.id === sourceId);
        return source?.name || sourceId;
      });

      console.log('🔍 Starting LangGraph keyword research workflow...', {
        method: researchMethod,
        website: selectedWebsite?.domain,
        topic: topicInput,
        seedKeywords: seedKeywords,
        dataSources: selectedSourceNames,
        maxKeywords: maxKeywords
      });

      // Call LangGraph keyword research workflow
      const response = await fetch('/api/pseo/langgraph/keyword-research', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user?.id || 'anonymous'
        },
        body: JSON.stringify({
          website_id: selectedWebsite?.id || 'temp-website-id',
          domain: selectedWebsite?.domain || '',
          seed_keywords: seedKeywords,
          research_method: researchMethod,
          topic_input: topicInput,
          competitor_domains: [],
          max_keywords: maxKeywords,
          data_sources: dataSources
        })
      });

      if (!response.ok) {
        let errorMessage = 'Failed to start keyword research workflow';
        try {
          const errorData = await response.json();
          if (errorData && typeof errorData === 'object' && 'message' in errorData) {
            errorMessage = errorData.message;
          } else if (errorData && typeof errorData === 'object' && 'error' in errorData) {
            errorMessage = errorData.error;
          }
        } catch (jsonError) {
          // If JSON parsing fails, use the response status text
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const workflowResult = await response.json();
      console.log('🚀 Workflow started:', workflowResult);

      if (workflowResult.success && workflowResult.workflow_id) {
        setWorkflowId(workflowResult.workflow_id);

        // Start streaming workflow status updates
        const cleanup = streamWorkflowStatus(workflowResult.workflow_id);

        // Store cleanup function for component unmount
        (window as any).cleanupWorkflowStream = cleanup;
      } else {
        throw new Error('Failed to start workflow');
      }

    } catch (err) {
      console.error('Keyword research error:', err);

      let errorMessage = 'Failed to complete keyword research';
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      } else if (err && typeof err === 'object' && 'message' in err) {
        errorMessage = String(err.message);
      }

      setError(errorMessage);
    } finally {
      setResearching(false);
    }
  };

  // Stream workflow status using Server-Sent Events
  const streamWorkflowStatus = (workflowId: string) => {
    console.log('🔄 Setting up workflow status stream for:', workflowId);

    const streamUrl = `/api/pseo/langgraph/workflow/${workflowId}/stream?userId=${user?.id || 'anonymous'}`;
    const eventSource = new EventSource(streamUrl);

    eventSource.onopen = () => {
      console.log('✅ Workflow stream connected');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📡 Received workflow update:', data);

        switch (data.type) {
          case 'connected':
            console.log('🔗 Stream connected for workflow:', data.workflowId);
            break;

          case 'status':
            // Map workflow steps to user-friendly messages
            const stepMessages: Record<string, string> = {
              'validation': 'Validating inputs...',
              'keyword_research': 'Researching keywords with SimilarWeb and Ubersuggest...',
              'completion': 'Finalizing results and saving to database...',
              'workflow_completed': 'Completed!'
            };

            const currentStep = data.data.current_step || 'initializing';
            const message = stepMessages[currentStep] || `Processing: ${currentStep}...`;

            setWorkflowStatus({
              ...data.data,
              message: message
            });

            console.log(`📊 Workflow status: ${data.data.status}, step: ${currentStep}`);
            break;

          case 'completed':
            console.log('🎉 Workflow completed! Processing results...');

            const results = convertLangGraphResultsToKeywordGroups(data.data);
            setKeywordGroups(results);
            const totalKeywordCount = results.reduce((sum: number, group: KeywordGroup) => sum + group.keywords.length, 0);
            setTotalKeywords(totalKeywordCount);

            console.log('✅ Keyword research completed!', {
              groups: results.length,
              totalKeywords: totalKeywordCount,
              processingTime: data.data.processing_time,
              dataSources: data.data.data_sources_used
            });

            // Show success message
            if (totalKeywordCount > 0) {
              setWorkflowStatus({
                status: 'completed',
                current_step: 'workflow_completed',
                progress: 100,
                message: `✅ Found ${totalKeywordCount} keywords from ${results.length} sources!`
              });
            } else {
              setWorkflowStatus({
                status: 'completed',
                current_step: 'workflow_completed',
                progress: 100,
                message: '⚠️ Workflow completed but no keywords found. Try different search terms.'
              });
            }

            // Reset researching state and close stream
            setResearching(false);
            eventSource.close();
            break;

          case 'error':
            console.error('❌ Workflow stream error:', data.message);
            setError(data.message || 'Workflow stream error');
            setResearching(false);
            eventSource.close();
            break;

          default:
            console.log('📡 Unknown message type:', data.type);
        }
      } catch (error) {
        console.error('❌ Error parsing workflow update:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('❌ Workflow stream error:', error);
      setError('Connection to workflow stream lost. Please refresh and try again.');
      setResearching(false);
      eventSource.close();
    };

    // Return cleanup function
    return () => {
      eventSource.close();
    };
  };

  // Fetch existing keywords for selected website
  const fetchExistingKeywords = async (websiteId: string) => {
    if (!websiteId || !user?.id) return;

    try {
      setLoadingExisting(true);
      console.log('🔍 Fetching existing keywords for website:', websiteId);

      const response = await fetch(`/api/pseo/langgraph/keywords/${websiteId}`, {
        headers: {
          'x-user-id': user.id
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 Existing keywords data:', data.data);

        // Convert to keyword groups format
        const groups: KeywordGroup[] = [];

        if (data.data.keywords_by_source) {
          Object.entries(data.data.keywords_by_source).forEach(([source, keywords]: [string, any]) => {
            const sourceName = source === 'ubersuggest' ? 'Ubersuggest (Existing)' :
                              source === 'dataforseo' ? 'DataForSEO (Existing)' :
                              source === 'manual' ? 'Manual Entry (Existing)' :
                              `${source.charAt(0).toUpperCase() + source.slice(1)} (Existing)`;

            if (Array.isArray(keywords) && keywords.length > 0) {
              groups.push({
                theme: sourceName,
                keywords: keywords.map((kw: any) => ({
                  keyword: kw.keyword || 'Unknown',
                  search_volume: parseInt(kw.search_volume) || 0,
                  keyword_difficulty: parseInt(kw.keyword_difficulty) || 0,
                  cpc: parseFloat(kw.cpc) || 0,
                  competition: kw.competition || 'medium',
                  intent: kw.intent || 'informational',
                  trend: 'stable' as const
                }))
              });
            }
          });
        }

        setExistingKeywords(groups);
        setExistingKeywordsCount(data.data.total_keywords || 0);
        setExistingClusters(data.data.keyword_clusters || []);

        console.log(`✅ Loaded ${data.data.total_keywords} existing keywords in ${groups.length} groups and ${data.data.total_clusters || 0} clusters`);
      } else {
        console.warn('⚠️ Failed to fetch existing keywords:', response.status);
        setExistingKeywords([]);
        setExistingKeywordsCount(0);
        setExistingClusters([]);
      }
    } catch (error) {
      console.error('❌ Error fetching existing keywords:', error);
      setExistingKeywords([]);
      setExistingKeywordsCount(0);
      setExistingClusters([]);
    } finally {
      setLoadingExisting(false);
    }
  };

  // Convert LangGraph workflow results to our UI format
  const convertLangGraphResultsToKeywordGroups = (workflowData: any): KeywordGroup[] => {
    console.log('🔄 Converting workflow data to keyword groups:', workflowData);

    // Handle different data structures
    let keywords: any[] = [];

    if (workflowData.keywords && Array.isArray(workflowData.keywords)) {
      keywords = workflowData.keywords;
    } else if (typeof workflowData.keywords === 'string') {
      try {
        keywords = JSON.parse(workflowData.keywords);
      } catch (e) {
        console.warn('Failed to parse keywords JSON string');
      }
    }

    if (!Array.isArray(keywords) || keywords.length === 0) {
      console.warn('No valid keywords found in workflow data');
      return [];
    }

    console.log(`📊 Processing ${keywords.length} keywords`);

    // Group keywords by data source first, then by intent
    const keywordsBySource: Record<string, any[]> = {};
    keywords.forEach((keyword: any) => {
      const source = keyword.data_source || 'unknown';
      if (!keywordsBySource[source]) {
        keywordsBySource[source] = [];
      }
      keywordsBySource[source].push(keyword);
    });

    const groups: KeywordGroup[] = [];

    // Create groups by data source
    Object.entries(keywordsBySource).forEach(([source, sourceKeywords]) => {
      const sourceName = source === 'rapidapi' ? 'RapidAPI Keywords' :
                        source === 'ubersuggest' ? 'Ubersuggest Keywords' :
                        source === 'similarweb' ? 'SimilarWeb Keywords' :
                        `${source.charAt(0).toUpperCase() + source.slice(1)} Keywords`;

      groups.push({
        theme: sourceName,
        keywords: sourceKeywords.map(kw => ({
          keyword: kw.keyword || 'Unknown',
          search_volume: parseInt(kw.search_volume) || 0,
          keyword_difficulty: parseInt(kw.keyword_difficulty) || 0,
          cpc: parseFloat(kw.cpc) || 0,
          competition: kw.competition || 'medium',
          intent: kw.intent || 'informational',
          trend: kw.trend || 'stable'
        }))
      });
    });

    // Add keyword clusters if available
    let clusters: any[] = [];
    if (workflowData.keyword_clusters && Array.isArray(workflowData.keyword_clusters)) {
      clusters = workflowData.keyword_clusters;
    } else if (typeof workflowData.keyword_clusters === 'string') {
      try {
        clusters = JSON.parse(workflowData.keyword_clusters);
      } catch (e) {
        console.warn('Failed to parse keyword_clusters JSON string');
      }
    }

    if (Array.isArray(clusters) && clusters.length > 0) {
      clusters.forEach((cluster: any) => {
        if (cluster.related_keywords && Array.isArray(cluster.related_keywords)) {
          groups.push({
            theme: cluster.cluster_name || 'Keyword Cluster',
            keywords: cluster.related_keywords.map((keyword: string) => {
              // Find the keyword data from the main keywords array
              const keywordData = keywords.find((kw: any) => kw.keyword === keyword);
              return keywordData ? {
                keyword: keywordData.keyword,
                search_volume: parseInt(keywordData.search_volume) || 0,
                keyword_difficulty: parseInt(keywordData.keyword_difficulty) || 0,
                cpc: parseFloat(keywordData.cpc) || 0,
                competition: keywordData.competition || 'medium',
                intent: keywordData.intent || 'informational',
                trend: keywordData.trend || 'stable'
              } : {
                keyword,
                search_volume: 0,
                keyword_difficulty: 0,
                cpc: 0,
                competition: 'medium' as const,
                intent: 'informational' as const,
                trend: 'stable' as const
              };
            })
          });
        }
      });
    }

    const validGroups = groups.filter(group => group.keywords.length > 0);
    console.log(`✅ Created ${validGroups.length} keyword groups with ${validGroups.reduce((sum, g) => sum + g.keywords.length, 0)} total keywords`);

    return validGroups;
  };





  const toggleKeywordSelection = (keyword: KeywordData) => {
    setSelectedKeywords(prev => {
      const exists = prev.find(k => k.keyword === keyword.keyword);
      if (exists) {
        return prev.filter(k => k.keyword !== keyword.keyword);
      } else {
        return [...prev, keyword];
      }
    });
  };

  const saveSelectedKeywords = async () => {
    if (selectedKeywords.length === 0 || !selectedWebsite) {
      setError('Please select keywords and a website to save');
      return;
    }

    try {
      setLoading(true);
      
      console.log('💾 Saving selected keywords...', {
        website: selectedWebsite.domain,
        keywords: selectedKeywords.map(k => k.keyword)
      });
      
      // This would use the KeywordManagementService
      const keywordData = selectedKeywords.map(keyword => ({
        website_id: selectedWebsite.id,
        keyword: keyword.keyword,
        search_volume: keyword.search_volume,
        keyword_difficulty: keyword.keyword_difficulty,
        cpc: keyword.cpc,
        competition: keyword.competition,
        intent: keyword.intent,
        data_source: 'ai_research'
      }));

      console.log('Keywords to save:', keywordData);
      
      // Simulate save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('✅ Keywords saved successfully!');
      setSelectedKeywords([]);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save keywords');
    } finally {
      setLoading(false);
    }
  };

  const filteredKeywords = keywordGroups.map(group => ({
    ...group,
    keywords: group.keywords.filter(keyword => {
      if (keyword.search_volume < filters.minVolume) return false;
      if (keyword.keyword_difficulty > filters.maxDifficulty) return false;
      if (filters.intent !== 'all' && keyword.intent !== filters.intent) return false;
      if (filters.competition !== 'all' && keyword.competition !== filters.competition) return false;
      return true;
    })
  })).filter(group => group.keywords.length > 0);

  const getIntentColor = (intent: string) => {
    switch (intent) {
      case 'informational': return 'bg-blue-100 text-blue-800';
      case 'navigational': return 'bg-green-100 text-green-800';
      case 'commercial': return 'bg-yellow-100 text-yellow-800';
      case 'transactional': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty < 30) return 'text-green-600';
    if (difficulty < 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            🔍 Keyword Research
          </h1>
          <p className="text-muted-foreground">
            Discover high-value keywords for your content strategy using AI-powered research
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Research Configuration */}
        <div className="space-y-6">
            
            {/* Research Method */}
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-foreground">
                  Research Method
                </h2>
                <button
                  onClick={testRapidAPIs}
                  disabled={testingApis}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  {testingApis ? '🧪 Testing...' : '🧪 Test APIs'}
                </button>
              </div>

              <div className="space-y-4">
                <label className="flex items-start gap-3 cursor-pointer p-3 rounded-lg border border-transparent hover:border-border hover:bg-muted/50 transition-colors">
                  <input
                    type="radio"
                    name="researchMethod"
                    value="website"
                    checked={researchMethod === 'website'}
                    onChange={(e) => setResearchMethod(e.target.value as 'website')}
                    className="mt-1"
                  />
                  <div>
                    <div className="font-medium">🌐 Domain Analysis</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Analyze website traffic and extract keywords using <strong>SimilarWeb API</strong>
                    </div>
                    <div className="text-xs text-blue-600 mt-1">
                      Uses: SimilarWeb → AI Analysis → Ubersuggest expansion
                    </div>
                  </div>
                </label>

                <label className="flex items-start gap-3 cursor-pointer p-3 rounded-lg border border-transparent hover:border-border hover:bg-muted/50 transition-colors">
                  <input
                    type="radio"
                    name="researchMethod"
                    value="keywords"
                    checked={researchMethod === 'keywords'}
                    onChange={(e) => setResearchMethod(e.target.value as 'keywords')}
                    className="mt-1"
                  />
                  <div>
                    <div className="font-medium">🔍 Keyword Research</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Research specific keywords with search volumes and metrics using <strong>Ubersuggest API</strong>
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      Uses: Ubersuggest API directly for keyword data
                    </div>
                  </div>
                </label>

                <label className="flex items-start gap-3 cursor-pointer p-3 rounded-lg border border-transparent hover:border-border hover:bg-muted/50 transition-colors">
                  <input
                    type="radio"
                    name="researchMethod"
                    value="topic"
                    checked={researchMethod === 'topic'}
                    onChange={(e) => setResearchMethod(e.target.value as 'topic')}
                    className="mt-1"
                  />
                  <div>
                    <div className="font-medium">💡 Topic Research</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Generate keywords for a topic using AI, then expand with real data
                    </div>
                    <div className="text-xs text-purple-600 mt-1">
                      Uses: AI Generation → Ubersuggest expansion
                    </div>
                  </div>
                </label>
              </div>

              {/* API Test Results */}
              {apiTestResults && (
                <div className="mt-4 p-3 rounded-lg border">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium">API Test Results:</span>
                    <span className={`px-2 py-1 text-xs rounded ${
                      apiTestResults.summary?.overall_status === 'all_passed' ? 'bg-green-100 text-green-800' :
                      apiTestResults.summary?.overall_status === 'partial_success' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {apiTestResults.summary?.overall_status?.replace(/_/g, ' ').toUpperCase() || 'FAILED'}
                    </span>
                  </div>

                  <div className="space-y-2">
                    {apiTestResults.tests?.map((test: any, index: number) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <span className={test.status === 'success' ? 'text-green-600' : 'text-red-600'}>
                          {test.status === 'success' ? '✅' : '❌'}
                        </span>
                        <span className="font-medium">{test.api}:</span>
                        <span className="text-muted-foreground">{test.message}</span>
                      </div>
                    )) || []}

                    {apiTestResults.error && (
                      <div className="text-sm text-red-600">
                        Error: {apiTestResults.error}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Website Selection */}
            {researchMethod === 'website' && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  Select Website
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Client:</label>
                    <select
                      value={selectedClient?.id || ''}
                      onChange={(e) => {
                        const client = clients.find(c => c.id === e.target.value);
                        setSelectedClient(client || null);
                        setSelectedWebsite(null);
                      }}
                      className="w-full p-2 border rounded"
                      disabled={loading}
                    >
                      <option value="">Choose a client...</option>
                      {clients.map(client => (
                        <option key={client.id} value={client.id}>{client.name}</option>
                      ))}
                    </select>
                  </div>
                  
                  {websites.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium mb-2">Website:</label>
                      <select
                        value={selectedWebsite?.id || ''}
                        onChange={(e) => {
                          const website = websites.find(w => w.id === e.target.value);
                          setSelectedWebsite(website || null);
                        }}
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Choose a website...</option>
                        {websites.map(website => (
                          <option key={website.id} value={website.id}>{website.name}</option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Topic Input */}
            {researchMethod === 'topic' && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  Topic/Industry
                </h2>

                <div>
                  <label className="block text-sm font-medium mb-2">Research Topic:</label>
                  <input
                    type="text"
                    value={topicInput}
                    onChange={(e) => setTopicInput(e.target.value)}
                    placeholder="e.g., digital marketing, web development..."
                    className="w-full p-2 border rounded"
                  />
                </div>
              </div>
            )}

            {/* Keywords Input */}
            {researchMethod === 'keywords' && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  Keywords to Research
                </h2>

                <div className="space-y-4">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={seedKeywordInput}
                      onChange={(e) => setSeedKeywordInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && addSeedKeyword()}
                      placeholder="Enter keyword to research..."
                      className="flex-1 p-2 border rounded"
                    />
                    <button
                      onClick={addSeedKeyword}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
                    >
                      Add
                    </button>
                  </div>

                  {seedKeywords.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Keywords to research:</p>
                      <div className="flex flex-wrap gap-2">
                        {seedKeywords.map(keyword => (
                          <span
                            key={keyword}
                            className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm flex items-center gap-2"
                          >
                            {keyword}
                            <button
                              onClick={() => removeSeedKeyword(keyword)}
                              className="text-green-600 hover:text-green-800"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {seedKeywords.length === 0 && (
                    <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded">
                      💡 Add keywords you want to research. Ubersuggest will provide search volume, difficulty, CPC, and related keyword suggestions.
                    </div>
                  )}
                </div>
              </div>
            )}



            {/* Data Sources */}
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-foreground">
                  Data Sources
                </h2>
                <button
                  onClick={() => fetchAvailableDataSources(true)}
                  disabled={dataSourcesLoading}
                  className="px-3 py-1 text-sm bg-muted hover:bg-muted/80 rounded-md transition-colors disabled:opacity-50"
                  title="Refresh data sources"
                >
                  {dataSourcesLoading ? (
                    <div className="flex items-center gap-1">
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                      <span>Refreshing...</span>
                    </div>
                  ) : (
                    '🔄 Refresh'
                  )}
                </button>
              </div>

              {/* Configuration Status Banner */}
              {!dataSourcesLoading && (
                <>
                  {availableDataSources.some(ds => ds.id === 'rapidapi') ? (
                    <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <span className="text-blue-600 text-lg">⭐</span>
                        <div>
                          <p className="text-sm font-medium text-blue-900">
                            RapidAPI Gateway Active
                          </p>
                          <p className="text-xs text-blue-700 mt-1">
                            Professional SEO data through unified API gateway with automatic fallbacks
                          </p>
                          {availableDataSources.find(ds => ds.id === 'rapidapi')?.provider && (
                            <p className="text-xs text-blue-600 mt-1 font-medium">
                              Current Provider: {availableDataSources.find(ds => ds.id === 'rapidapi')?.provider?.charAt(0).toUpperCase() + availableDataSources.find(ds => ds.id === 'rapidapi')?.provider?.slice(1)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="mb-4 p-3 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <span className="text-amber-600 text-lg">💡</span>
                        <div>
                          <p className="text-sm font-medium text-amber-900">
                            Using Free Data Sources
                          </p>
                          <p className="text-xs text-amber-700 mt-1">
                            Configure RapidAPI for professional SEO data access
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Loading State */}
              {dataSourcesLoading && (
                <div className="text-center py-8">
                  <div className="inline-flex items-center gap-2 text-muted-foreground">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    <span>Loading available data sources...</span>
                  </div>
                </div>
              )}

              {/* Data Sources List */}
              {!dataSourcesLoading && (
                <div className="space-y-3">
                  {availableDataSources.map(source => (
                    <label key={source.id} className="flex items-start gap-3 cursor-pointer p-3 rounded-lg border border-transparent hover:border-border hover:bg-muted/50 transition-colors">
                      <input
                        type="checkbox"
                        checked={dataSources.includes(source.id)}
                        onChange={() => toggleDataSource(source.id)}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className={`${source.recommended ? 'font-semibold text-primary' : 'font-medium'}`}>
                            {source.name}
                          </span>
                          {source.recommended && (
                            <span className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-full">
                              Recommended
                            </span>
                          )}
                        </div>
                        {source.description && (
                          <p className="text-sm text-muted-foreground mt-1">{source.description}</p>
                        )}
                        {source.provider && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Provider: {source.provider.charAt(0).toUpperCase() + source.provider.slice(1)}
                          </p>
                        )}
                      </div>
                    </label>
                  ))}

                  {availableDataSources.length === 0 && !dataSourcesLoading && (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No data sources available. Please check your configuration.</p>
                    </div>
                  )}
                </div>
              )}

              {/* Selected Sources Summary */}
              {!dataSourcesLoading && dataSources.length > 0 && (
                <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                  <p className="text-sm font-medium text-foreground mb-1">
                    Selected Sources ({dataSources.length}):
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {dataSources.map(sourceId => {
                      const source = availableDataSources.find(ds => ds.id === sourceId);
                      return (
                        <span key={sourceId} className="px-2 py-1 text-xs bg-primary/10 text-primary rounded">
                          {source?.name?.replace(/\s*\([^)]*\)\s*⭐?/g, '') || sourceId}
                        </span>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Advanced Options */}
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold text-foreground mb-4">
                Advanced Options
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Max Keywords:</label>
                  <input
                    type="number"
                    value={maxKeywords}
                    onChange={(e) => setMaxKeywords(parseInt(e.target.value) || 100)}
                    min="10"
                    max="500"
                    className="w-full p-2 border rounded"
                  />
                </div>


              </div>
            </div>

            {/* Existing Keywords Display */}
            {researchMethod === 'website' && selectedWebsite && (
              <div className="bg-card rounded-lg border p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-foreground">
                    📊 Existing Keywords
                  </h2>
                  {loadingExisting && (
                    <div className="text-sm text-muted-foreground">Loading...</div>
                  )}
                </div>

                {existingKeywordsCount > 0 ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>📈 Total: {existingKeywordsCount} keywords</span>
                      <span>🗂️ Sources: {existingKeywords.length}</span>
                      <span>🌐 Website: {selectedWebsite.domain}</span>
                    </div>

                    {/* Existing Keywords Groups */}
                    <div className="space-y-3">
                      {existingKeywords.map((group, groupIndex) => (
                        <div key={groupIndex} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h3 className="font-medium text-base">{group.theme}</h3>
                            <span className="text-sm text-muted-foreground">
                              {group.keywords.length} keywords
                            </span>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            {group.keywords.map((keyword, keywordIndex) => (
                              <span
                                key={keywordIndex}
                                className="px-3 py-2 bg-blue-50 text-blue-700 rounded-lg text-sm hover:bg-blue-100 transition-colors cursor-pointer"
                                title={`Volume: ${keyword.search_volume?.toLocaleString() || 'N/A'} | Difficulty: ${keyword.keyword_difficulty || 'N/A'} | CPC: $${keyword.cpc?.toFixed(2) || '0.00'} | Intent: ${keyword.intent || 'N/A'}`}
                              >
                                {keyword.keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="text-xs text-muted-foreground">
                      💡 New research will add to your existing keywords
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <div className="text-4xl mb-2">🔍</div>
                    <div className="font-medium">No existing keywords found</div>
                    <div className="text-sm">Start your first keyword research for this website</div>
                  </div>
                )}
              </div>
            )}

            {/* Existing Clusters Display */}
            {researchMethod === 'website' && selectedWebsite && existingClusters.length > 0 && (
              <div className="bg-card rounded-lg border p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-foreground">
                    🗂️ Keyword Clusters
                  </h2>
                  <span className="text-sm text-muted-foreground">
                    {existingClusters.length} clusters found
                  </span>
                </div>

                <div className="space-y-4">
                  {existingClusters.map((cluster, clusterIndex) => (
                    <div key={clusterIndex} className="border rounded-lg p-4 bg-gradient-to-r from-purple-50 to-indigo-50">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium text-base text-purple-900">
                          {cluster.cluster_name || `Cluster ${clusterIndex + 1}`}
                        </h3>
                        <span className="text-sm text-purple-600">
                          {cluster.related_keywords?.length || 0} keywords
                        </span>
                      </div>

                      {cluster.primary_keyword && (
                        <div className="mb-3">
                          <span className="px-3 py-2 bg-purple-100 text-purple-800 rounded-lg text-sm font-medium">
                            Primary: {cluster.primary_keyword}
                          </span>
                        </div>
                      )}

                      {cluster.related_keywords && cluster.related_keywords.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-3">
                          {cluster.related_keywords.map((keyword: string, keywordIndex: number) => (
                            <span
                              key={keywordIndex}
                              className="px-3 py-2 bg-indigo-50 text-indigo-700 rounded-lg text-sm hover:bg-indigo-100 transition-colors cursor-pointer"
                              title={`Related keyword in ${cluster.cluster_name || 'cluster'}`}
                            >
                              {keyword}
                            </span>
                          ))}
                        </div>
                      )}

                      <div className="flex items-center gap-3">
                        {cluster.intent_category && (
                          <span className={`px-3 py-1 rounded-lg text-sm ${getIntentColor(cluster.intent_category)}`}>
                            {cluster.intent_category}
                          </span>
                        )}
                        {cluster.search_volume && (
                          <span className="text-sm text-purple-600">
                            Volume: {cluster.search_volume.toLocaleString()}
                          </span>
                        )}
                        {cluster.difficulty_score && (
                          <span className="text-sm text-purple-600">
                            Difficulty: {cluster.difficulty_score}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-xs text-muted-foreground mt-3">
                  🧠 AI-generated keyword clusters help organize your content strategy
                </div>
              </div>
            )}

            {/* Start Research Button */}
            <div className="bg-card rounded-lg border p-6">
              <button
                onClick={startKeywordResearch}
                disabled={researching ||
                  (researchMethod === 'website' && !selectedWebsite) ||
                  (researchMethod === 'topic' && !topicInput.trim()) ||
                  (researchMethod === 'keywords' && seedKeywords.length === 0)
                }
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed py-3 px-6 rounded-lg font-medium transition-colors"
              >
                {researching ? '🔍 Researching...' : '🚀 Start Research'}
              </button>
              
              {researching && (
                <div className="mt-4 text-center">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  {workflowStatus ? (
                    <div className="mt-2 space-y-2">
                      <p className="text-sm text-muted-foreground">
                        {workflowStatus.current_step?.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </p>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${workflowStatus.progress || 0}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {workflowStatus.progress || 0}% complete
                      </p>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-2">Starting workflow...</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Workflow Status */}
          {researching && workflowStatus && (
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <h3 className="text-lg font-semibold">Processing Keyword Research</h3>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="font-medium">Status: {workflowStatus.status}</span>
                  <span className="text-muted-foreground">
                    {workflowStatus.current_step === 'validation' && '1/3'}
                    {workflowStatus.current_step === 'keyword_research' && '2/3'}
                    {(workflowStatus.current_step === 'completion' || workflowStatus.current_step === 'workflow_completed') && '3/3'}
                  </span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${
                        workflowStatus.current_step === 'validation' ? 33 :
                        workflowStatus.current_step === 'keyword_research' ? 66 :
                        (workflowStatus.current_step === 'completion' || workflowStatus.current_step === 'workflow_completed') ? 100 :
                        workflowStatus.progress || 0
                      }%`
                    }}
                  ></div>
                </div>

                {workflowStatus.message && (
                  <p className="text-sm text-muted-foreground">{workflowStatus.message}</p>
                )}

                {/* Step indicators */}
                <div className="flex justify-between text-xs text-muted-foreground mt-2">
                  <span className={workflowStatus.current_step === 'validation' ? 'text-primary font-medium' : ''}>
                    ✓ Validation
                  </span>
                  <span className={workflowStatus.current_step === 'keyword_research' ? 'text-primary font-medium' : ''}>
                    🔍 Keyword Research
                  </span>
                  <span className={(workflowStatus.current_step === 'completion' || workflowStatus.current_step === 'workflow_completed') ? 'text-primary font-medium' : ''}>
                    💾 Completion
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Results Section */}
          {keywordGroups.length > 0 && (
            <div className="space-y-6">
                {/* Results Summary */}
                <div className="bg-card rounded-lg border p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold text-foreground">
                      Research Results
                    </h2>
                    <div className="flex items-center gap-4 text-sm">
                      <span>Total: {totalKeywords} keywords</span>
                      <span>Selected: {selectedKeywords.length}</span>
                      {selectedKeywords.length > 0 && (
                        <button
                          onClick={saveSelectedKeywords}
                          disabled={loading}
                          className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                        >
                          💾 Save Selected
                        </button>
                      )}
                    </div>
                  </div>
                  
                  {/* Filters */}
                  <div className="grid grid-cols-4 gap-4 mb-4">
                    <div>
                      <label className="block text-xs font-medium mb-1">Min Volume:</label>
                      <input
                        type="number"
                        value={filters.minVolume}
                        onChange={(e) => setFilters(prev => ({ ...prev, minVolume: parseInt(e.target.value) || 0 }))}
                        className="w-full p-1 border rounded text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium mb-1">Max Difficulty:</label>
                      <input
                        type="number"
                        value={filters.maxDifficulty}
                        onChange={(e) => setFilters(prev => ({ ...prev, maxDifficulty: parseInt(e.target.value) || 100 }))}
                        className="w-full p-1 border rounded text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium mb-1">Intent:</label>
                      <select
                        value={filters.intent}
                        onChange={(e) => setFilters(prev => ({ ...prev, intent: e.target.value }))}
                        className="w-full p-1 border rounded text-sm"
                      >
                        <option value="all">All</option>
                        <option value="informational">Informational</option>
                        <option value="commercial">Commercial</option>
                        <option value="transactional">Transactional</option>
                        <option value="navigational">Navigational</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium mb-1">Competition:</label>
                      <select
                        value={filters.competition}
                        onChange={(e) => setFilters(prev => ({ ...prev, competition: e.target.value }))}
                        className="w-full p-1 border rounded text-sm"
                      >
                        <option value="all">All</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Keyword Groups */}
                {filteredKeywords.map((group, groupIndex) => (
                  <div key={groupIndex} className="bg-card rounded-lg border p-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4">
                      {group.theme} ({group.keywords.length})
                    </h3>
                    
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Select</th>
                            <th className="text-left p-2">Keyword</th>
                            <th className="text-left p-2">Volume</th>
                            <th className="text-left p-2">Difficulty</th>
                            <th className="text-left p-2">CPC</th>
                            <th className="text-left p-2">Intent</th>
                            <th className="text-left p-2">Competition</th>
                            <th className="text-left p-2">Trend</th>
                          </tr>
                        </thead>
                        <tbody>
                          {group.keywords.map((keyword, keywordIndex) => (
                            <tr key={keywordIndex} className="border-b hover:bg-muted">
                              <td className="p-2">
                                <input
                                  type="checkbox"
                                  checked={selectedKeywords.some(k => k.keyword === keyword.keyword)}
                                  onChange={() => toggleKeywordSelection(keyword)}
                                />
                              </td>
                              <td className="p-2 font-medium">{keyword.keyword}</td>
                              <td className="p-2">{keyword.search_volume.toLocaleString()}</td>
                              <td className={`p-2 font-medium ${getDifficultyColor(keyword.keyword_difficulty)}`}>
                                {keyword.keyword_difficulty}
                              </td>
                              <td className="p-2">${keyword.cpc.toFixed(2)}</td>
                              <td className="p-2">
                                <span className={`px-2 py-1 rounded text-xs ${getIntentColor(keyword.intent)}`}>
                                  {keyword.intent}
                                </span>
                              </td>
                              <td className="p-2 capitalize">{keyword.competition}</td>
                              <td className="p-2">
                                <span className={`px-2 py-1 rounded text-xs ${
                                  keyword.trend === 'rising' ? 'bg-green-100 text-green-800' :
                                  keyword.trend === 'declining' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {keyword.trend}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ))}


            </div>
          )}

          {keywordGroups.length === 0 && !researching && (
            <div className="bg-card rounded-lg border p-12 text-center">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Ready for Research!</h3>
              <p className="text-muted-foreground">
                Configure your research method and click "Start Research" to discover valuable keywords.
              </p>
            </div>
          )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default KeywordResearch; 