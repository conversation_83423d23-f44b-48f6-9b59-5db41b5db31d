import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "../../../../../base/components/ui/dialog";
import { <PERSON><PERSON> } from "../../../../../base/components/ui/button";
import { Input } from "../../../../../base/components/ui/input";
import { Label } from "../../../../../base/components/ui/label";
import { Textarea } from "../../../../../base/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import { Badge } from "../../../../../base/components/ui/badge";
import {
  Tabs,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "../../../../../base/components/ui/tabs";
import { ScrollArea } from "../../../../../base/components/ui/scroll-area";
import {
  Plus,
  X,
  Save,
  Eye,
  Code,
  FileText,
  Tag,
  Settings,
} from "lucide-react";
import {
  ProposalTemplate,
  TemplateFormData,
  TemplateSection,
} from "../../services/templateService";

interface TemplateEditorProps {
  template: ProposalTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (templateData: TemplateFormData) => void;
}

export const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  isOpen,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState<TemplateFormData>({
    name: "",
    description: "",
    content: "",
    sections: [],
    variables: [],
    category: "general",
    is_public: false,
  });

  const [activeTab, setActiveTab] = useState("basic");
  const [newVariable, setNewVariable] = useState("");

  // Initialize form data when template changes
  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        description: template.description,
        content: template.content || "",
        sections: template.sections || [],
        variables: template.variables || [],
        category: template.category || "general",
        is_public: template.is_public,
      });
    } else {
      setFormData({
        name: "",
        description: "",
        content: "",
        sections: [],
        variables: [],
        category: "general",
        is_public: false,
      });
    }
  }, [template]);

  // Extract variables from content
  const extractVariables = (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = content.match(variableRegex);
    if (!matches) return [];

    return Array.from(
      new Set(matches.map((match) => match.replace(/[{}]/g, "")))
    );
  };

  // Update variables when content changes
  useEffect(() => {
    const extractedVars = extractVariables(formData.content);
    const allVars = Array.from(
      new Set([...formData.variables, ...extractedVars])
    );
    setFormData((prev) => ({ ...prev, variables: allVars }));
  }, [formData.content]);

  const handleSave = () => {
    onSave(formData);
  };

  const handleAddVariable = () => {
    if (
      newVariable.trim() &&
      !formData.variables.includes(newVariable.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        variables: [...prev.variables, newVariable.trim()],
      }));
      setNewVariable("");
    }
  };

  const handleRemoveVariable = (variable: string) => {
    setFormData((prev) => ({
      ...prev,
      variables: prev.variables.filter((v) => v !== variable),
    }));
  };

  const handleAddSection = () => {
    const newSection: TemplateSection = {
      id: `section-${Date.now()}`,
      title: "New Section",
      content: "",
      order: formData.sections.length,
      type: "text",
      required: false,
    };

    setFormData((prev) => ({
      ...prev,
      sections: [...prev.sections, newSection],
    }));
  };

  const handleUpdateSection = (
    sectionId: string,
    updates: Partial<TemplateSection>
  ) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.map((section) =>
        section.id === sectionId ? { ...section, ...updates } : section
      ),
    }));
  };

  const handleRemoveSection = (sectionId: string) => {
    setFormData((prev) => ({
      ...prev,
      sections: prev.sections.filter((section) => section.id !== sectionId),
    }));
  };

  const isFormValid = formData.name.trim() && formData.description.trim();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-2xl font-bold flex items-center gap-2">
            <FileText className="h-6 w-6 text-primary" />
            {template ? "Edit Template" : "Create New Template"}
          </DialogTitle>
          <DialogDescription>
            {template
              ? "Modify your proposal template"
              : "Create a new proposal template"}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <TabsList className="grid w-full grid-cols-4 flex-shrink-0">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="content" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Content
              </TabsTrigger>
              <TabsTrigger value="sections" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Sections
              </TabsTrigger>
              <TabsTrigger
                value="variables"
                className="flex items-center gap-2"
              >
                <Tag className="h-4 w-4" />
                Variables
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 min-h-0 mt-4">
              <TabsContent value="basic" className="h-full">
                <ScrollArea className="h-full">
                  <div className="space-y-4 pr-4">
                    <div>
                      <Label htmlFor="name">Template Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            name: e.target.value,
                          }))
                        }
                        placeholder="Enter template name"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            description: e.target.value,
                          }))
                        }
                        placeholder="Describe what this template is for"
                        className="mt-1"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={formData.category}
                        onValueChange={(value) =>
                          setFormData((prev) => ({ ...prev, category: value }))
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">General</SelectItem>
                          <SelectItem value="technical">Technical</SelectItem>
                          <SelectItem value="consulting">Consulting</SelectItem>
                          <SelectItem value="design">Design</SelectItem>
                          <SelectItem value="research">Research</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="is_public"
                        checked={formData.is_public}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            is_public: e.target.checked,
                          }))
                        }
                        className="rounded"
                      />
                      <Label htmlFor="is_public">
                        Make this template public
                      </Label>
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="content" className="h-full">
                <div className="h-full">
                  <Label htmlFor="content">Template Content (Markdown)</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        content: e.target.value,
                      }))
                    }
                    placeholder="Enter your template content in Markdown format. Use {{variableName}} for variables."
                    className="mt-1 h-[calc(100%-2rem)] font-mono text-sm"
                  />
                </div>
              </TabsContent>

              <TabsContent value="sections" className="h-full">
                <ScrollArea className="h-full">
                  <div className="space-y-4 pr-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Template Sections</h4>
                      <Button
                        onClick={handleAddSection}
                        size="sm"
                        variant="outline"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Section
                      </Button>
                    </div>

                    {formData.sections.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No sections added yet. Click "Add Section" to get
                        started.
                      </div>
                    ) : (
                      formData.sections.map((section) => (
                        <div
                          key={section.id}
                          className="border rounded-lg p-4 space-y-3"
                        >
                          <div className="flex items-center justify-between">
                            <Input
                              value={section.title}
                              onChange={(e) =>
                                handleUpdateSection(section.id, {
                                  title: e.target.value,
                                })
                              }
                              placeholder="Section title"
                              className="font-medium"
                            />
                            <Button
                              onClick={() => handleRemoveSection(section.id)}
                              size="sm"
                              variant="ghost"
                              className="text-destructive"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>

                          <Textarea
                            value={section.content}
                            onChange={(e) =>
                              handleUpdateSection(section.id, {
                                content: e.target.value,
                              })
                            }
                            placeholder="Section content"
                            rows={4}
                            className="font-mono text-sm"
                          />

                          <div className="flex items-center gap-4">
                            <Select
                              value={section.type}
                              onValueChange={(value: any) =>
                                handleUpdateSection(section.id, { type: value })
                              }
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="text">Text</SelectItem>
                                <SelectItem value="list">List</SelectItem>
                                <SelectItem value="table">Table</SelectItem>
                                <SelectItem value="image">Image</SelectItem>
                                <SelectItem value="variable">
                                  Variable
                                </SelectItem>
                              </SelectContent>
                            </Select>

                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`required-${section.id}`}
                                checked={section.required}
                                onChange={(e) =>
                                  handleUpdateSection(section.id, {
                                    required: e.target.checked,
                                  })
                                }
                                className="rounded"
                              />
                              <Label htmlFor={`required-${section.id}`}>
                                Required
                              </Label>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="variables" className="h-full">
                <ScrollArea className="h-full">
                  <div className="space-y-4 pr-4">
                    <div>
                      <h4 className="font-medium mb-2">Template Variables</h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Variables are automatically detected from your content.
                        You can also add custom ones.
                      </p>
                    </div>

                    <div className="flex gap-2">
                      <Input
                        value={newVariable}
                        onChange={(e) => setNewVariable(e.target.value)}
                        placeholder="Add new variable"
                        onKeyPress={(e) =>
                          e.key === "Enter" && handleAddVariable()
                        }
                      />
                      <Button onClick={handleAddVariable} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {formData.variables.map((variable) => (
                        <Badge
                          key={variable}
                          variant="secondary"
                          className="flex items-center gap-1 px-3 py-1"
                        >
                          <Tag className="h-3 w-3" />
                          {variable}
                          <button
                            onClick={() => handleRemoveVariable(variable)}
                            className="ml-1 hover:text-destructive"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>

                    {formData.variables.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No variables found. Add {"{{"} variableName {"}}"} to
                        your content to create variables.
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!isFormValid}>
            <Save className="h-4 w-4 mr-2" />
            {template ? "Update Template" : "Create Template"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
