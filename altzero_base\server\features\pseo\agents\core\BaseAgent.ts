// =====================================================
// BASE AGENT CLASS - ABSTRACT IMPLEMENTATION
// =====================================================

import { 
  IAgent, 
  AgentCapability, 
  AgentContext, 
  AgentResult, 
  AgentInput, 
  AgentMetrics,
  AgentError 
} from './AgentTypes';

export abstract class BaseAgent implements IAgent {
  public readonly name: string;
  public readonly description: string;
  public readonly capabilities: AgentCapability[];

  constructor(name: string, description: string, capabilities: AgentCapability[]) {
    this.name = name;
    this.description = description;
    this.capabilities = capabilities;
  }

  // Abstract methods that must be implemented by subclasses
  abstract execute(context: AgentContext): Promise<AgentResult>;
  abstract validateInput(input: AgentInput): boolean;
  abstract getRequiredTools(): string[];

  // Common utility methods available to all agents
  protected async executeWithMetrics(
    context: AgentContext,
    operation: () => Promise<AgentResult>
  ): Promise<AgentResult> {
    const startTime = Date.now();
    const startMemory = process.memoryUsage().heapUsed / 1024 / 1024; // MB

    try {
      // Log execution start
      context.logger.info(`Starting agent execution: ${this.name}`, {
        agent_name: this.name,
        job_id: context.job.id,
        website_id: context.website.id
      });

      // Execute the operation
      const result = await operation();

      // Calculate metrics
      const endTime = Date.now();
      const endMemory = process.memoryUsage().heapUsed / 1024 / 1024; // MB
      const executionTime = endTime - startTime;
      const memoryUsed = endMemory - startMemory;

      // Enhance result with metrics
      result.metrics = {
        ...result.metrics,
        execution_time_ms: executionTime,
        memory_usage_mb: memoryUsed
      };

      // Log success
      context.logger.info(`Agent execution completed: ${this.name}`, {
        agent_name: this.name,
        job_id: context.job.id,
        website_id: context.website.id,
        execution_time_ms: executionTime,
        success: result.success
      });

      return result;

    } catch (error) {
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Log error
      context.logger.error(`Agent execution failed: ${this.name}`, error as Error, {
        agent_name: this.name,
        job_id: context.job.id,
        website_id: context.website.id,
        execution_time_ms: executionTime
      });

      // Return error result
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metrics: {
          execution_time_ms: executionTime,
          api_calls_made: 0,
          data_points_processed: 0,
          errors_encountered: 1,
          cache_hits: 0
        }
      };
    }
  }

  // Rate limiting helper
  protected async applyRateLimit(context: AgentContext): Promise<void> {
    const rateLimits = context.config.rate_limits;
    if (!rateLimits) return;

    // Simple rate limiting implementation
    // In production, this would use Redis or similar
    const now = Date.now();
    const cacheKey = `rate_limit_${this.name}_${now}`;
    
    // This is a placeholder - implement actual rate limiting
    context.logger.debug(`Rate limiting applied for agent: ${this.name}`);
  }

  // Input validation helper
  protected validateCommonInput(input: AgentInput): boolean {
    if (!input.website_id) {
      throw new AgentError('Missing website_id', this.name, 'INVALID_INPUT', false);
    }

    if (!input.parameters) {
      throw new AgentError('Missing parameters', this.name, 'INVALID_INPUT', false);
    }

    return true;
  }

  // Error handling helper
  protected handleError(error: unknown, context: string): AgentError {
    if (error instanceof AgentError) {
      return error;
    }

    const message = error instanceof Error ? error.message : 'Unknown error';
    return new AgentError(
      `${context}: ${message}`,
      this.name,
      'EXECUTION_ERROR',
      true,
      { original_error: error }
    );
  }

  // Data validation helper
  protected validateWebsiteData(website: any): boolean {
    if (!website?.id || !website?.url || !website?.domain) {
      throw new AgentError('Invalid website data', this.name, 'INVALID_WEBSITE', false);
    }
    return true;
  }

  // URL validation helper
  protected isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Domain extraction helper
  protected extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      throw new AgentError(`Invalid URL: ${url}`, this.name, 'INVALID_URL', false);
    }
  }

  // Cache key generation helper
  protected generateCacheKey(prefix: string, ...parts: string[]): string {
    return `pseo_${prefix}_${parts.join('_')}`;
  }

  // Progress reporting helper
  protected async reportProgress(
    context: AgentContext, 
    percentage: number, 
    message: string,
    currentStep?: string
  ): Promise<void> {
    context.logger.info(`Progress update: ${this.name}`, {
      agent_name: this.name,
      job_id: context.job.id,
      progress_percentage: percentage,
      message,
      current_step: currentStep
    });

    // Update job progress in database
    // This would integrate with your job tracking system
  }

  // Result aggregation helper
  protected aggregateResults<T>(results: T[]): {
    total: number;
    successful: number;
    failed: number;
    data: T[];
  } {
    return {
      total: results.length,
      successful: results.filter(r => r !== null && r !== undefined).length,
      failed: results.filter(r => r === null || r === undefined).length,
      data: results.filter(r => r !== null && r !== undefined)
    };
  }

  // Batch processing helper
  protected async processBatch<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    batchSize: number = 10,
    context?: AgentContext
  ): Promise<R[]> {
    const results: R[] = [];
    const batches = this.chunkArray(items, batchSize);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      
      if (context) {
        const progress = ((i + 1) / batches.length) * 100;
        await this.reportProgress(
          context, 
          progress, 
          `Processing batch ${i + 1} of ${batches.length}`,
          `batch_${i + 1}`
        );
      }

      // Process batch in parallel
      const batchResults = await Promise.allSettled(
        batch.map(item => processor(item))
      );

      // Extract successful results
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else if (context) {
          context.logger.warn(`Batch item failed: ${result.reason}`);
        }
      }

      // Small delay between batches to be respectful
      if (i < batches.length - 1) {
        await this.delay(100);
      }
    }

    return results;
  }

  // Array chunking helper
  protected chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // Delay helper
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Configuration validation
  protected validateConfig(context: AgentContext): void {
    if (!context.config.max_execution_time_seconds || context.config.max_execution_time_seconds <= 0) {
      throw new AgentError('Invalid execution timeout', this.name, 'INVALID_CONFIG', false);
    }

    if (context.config.retry_attempts < 0) {
      throw new AgentError('Invalid retry attempts', this.name, 'INVALID_CONFIG', false);
    }
  }

  // Tool availability check
  protected checkRequiredTools(context: AgentContext): void {
    const requiredTools = this.getRequiredTools();
    const availableTools = Object.keys(context.tools);

    const missingTools = requiredTools.filter(tool => !availableTools.includes(tool));
    
    if (missingTools.length > 0) {
      throw new AgentError(
        `Missing required tools: ${missingTools.join(', ')}`,
        this.name,
        'MISSING_TOOLS',
        false
      );
    }
  }

  // Standard metrics initialization
  protected initializeMetrics(): AgentMetrics {
    return {
      execution_time_ms: 0,
      api_calls_made: 0,
      data_points_processed: 0,
      errors_encountered: 0,
      cache_hits: 0
    };
  }

  // Standard success result
  protected createSuccessResult(data: Record<string, unknown>, metrics?: Partial<AgentMetrics>): AgentResult {
    return {
      success: true,
      data,
      metrics: {
        ...this.initializeMetrics(),
        ...metrics
      }
    };
  }

  // Standard error result
  protected createErrorResult(error: string, metrics?: Partial<AgentMetrics>): AgentResult {
    return {
      success: false,
      error,
      metrics: {
        ...this.initializeMetrics(),
        errors_encountered: 1,
        ...metrics
      }
    };
  }
} 