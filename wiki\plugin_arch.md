# 🏗️ **AltZero Plugin Architecture - IMPLEMENTED**

## 📂 **Current File Structure**

Our plugin system is now fully implemented with this structure:

```
altzero_base/
├── plugins/                    # 🔧 Core Plugin System
│   ├── registry.ts            # 🔍 Master plugin registry (SINGLE SOURCE OF TRUTH)
│   ├── types.ts               # 📋 Plugin interfaces & contracts
│   └── loader.tsx             # ⚡ Frontend plugin loader & hooks
│
├── server/                    # 🖥️ Backend
│   ├── plugins/               # 🔧 Backend Plugin System
│   │   ├── registry.ts        # 🔍 Backend plugin registry
│   │   └── loader.ts          # ⚡ Backend plugin loader
│   ├── features/              # 📦 Backend Plugin Features
│   │   ├── knowledge/         # 🧠 Knowledge Base Backend
│   │   │   ├── backend/
│   │   │   │   └── index.ts   # 🔌 Backend plugin export
│   │   │   ├── routes/
│   │   │   │   └── knowledge.ts # 🛣️ API routes
│   │   │   └── services/      # ⚙️ Business logic
│   │   │       ├── pineconeService.ts
│   │   │       ├── ragService.ts
│   │   │       ├── langGraphService.ts
│   │   │       └── llamaCloudService.ts
│   │   ├── pseo/              # 📊 pSEO Backend
│   │   │   ├── backend/
│   │   │   │   └── index.ts   # 🔌 Backend plugin export
│   │   │   ├── routes/
│   │   │   │   └── pseo.ts    # 🛣️ API routes
│   │   │   └── services/      # ⚙️ Business logic
│   │   └── aichat/            # 🤖 AI Chat Backend
│   │       └── backend/
│   │           └── index.ts   # 🔌 Backend plugin export
│   ├── base/                  # 🏗️ Core backend infrastructure
│   └── app.ts                 # ✨ Auto-loads plugins via PluginLoader
│
├── features/                  # 📦 Frontend Plugin Features
│   ├── knowledge/             # 🧠 Knowledge Base Plugin
│   │   ├── index.ts          # 🔌 Plugin export (PluginModule)
│   │   ├── components/       # ⚛️ React components
│   │   ├── context/          # 🔄 State management
│   │   ├── services/         # 🌐 API calls
│   │   ├── types/            # 📋 TypeScript types
│   │   └── utils/            # 🔧 Utilities
│   ├── pseo/                 # 📊 pSEO Plugin
│   │   ├── index.ts          # 🔌 Plugin export
│   │   ├── pages/            # 📄 Page components
│   │   │   ├── PSEODashboard.tsx
│   │   │   ├── ClientManagement.tsx
│   │   │   ├── WebsiteManagement.tsx
│   │   │   ├── AuditRunner.tsx
│   │   │   ├── AuditResults.tsx
│   │   │   └── AuditHistory.tsx
│   │   ├── services/         # 🌐 API calls
│   │   ├── utilities/        # 🔧 Utilities
│   │   └── types.ts          # 📋 TypeScript types
│   └── aichat/               # 🤖 AI Chat Plugin
│       ├── index.ts          # 🔌 Plugin export
│       ├── pages/            # 📄 Page components
│       ├── components/       # ⚛️ React components
│       ├── contexts/         # 🔄 State management
│       ├── hooks/            # 🪝 Custom hooks
│       ├── lib/              # 📚 Libraries
│       ├── providers/        # 🔌 Context providers
│       ├── types/            # 📋 TypeScript types
│       └── utils/            # 🔧 Utilities
│
├── base/                     # 🏗️ Core frontend infrastructure
│   ├── components/           # 🧱 Shared UI components
│   ├── contextapi/           # 🔄 Core contexts (UserContext, etc.)
│   ├── utils/                # 🔧 Core utilities
│   └── hooks/                # 🪝 Shared hooks
├── header/                   # 🧭 Navigation (uses usePluginNavigation)
│   └── Header.tsx            # 🧭 Dynamic navigation via usePluginNavigation()
├── layout/                   # 🎨 Layout components
│   └── MainLayout.tsx        # 🎨 Plugin-agnostic (already perfect!)
└── App.tsx                   # ✨ Zero plugin knowledge! Uses PluginRoutes
```

---

## 🎯 **How It Works**

### **🔍 Single Source of Truth**
```typescript
// plugins/registry.ts - Controls everything!
export const PLUGIN_REGISTRY = {
  knowledge: { enabled: true, name: 'Knowledge Base', route: '/knowledge' },
  pseo: { enabled: true, name: 'pSEO', route: '/pseo' },
  aichat: { enabled: true, name: 'AI Chat', route: '/copilot-chat' }
};
```

### **⚡ Automatic Loading**
- **Frontend**: `PluginLoader` dynamically imports from `features/{pluginName}/index.ts`
- **Backend**: `PluginLoader` auto-mounts APIs from `server/features/{pluginName}/`
- **Navigation**: Header loads menu items from enabled plugins automatically
- **Routes**: App.tsx delegates all non-core routes to `PluginRoutes`

### **🔌 Plugin Contract**
Each plugin exports a standardized module:
```typescript
// features/myplugin/index.ts
const myPlugin: PluginModule = {
  routes: [{ path: '/myplugin', element: <MyComponent /> }],
  navigation: [{ name: 'My Plugin', route: '/myplugin', icon: 'Star' }],
  providers: [MyContextProvider], // Optional
  config: { name: 'My Plugin', version: '1.0.0' }
};
export default myPlugin;
```

---

# 🚀 **How to Add a New Plugin**

## **📋 Step-by-Step Guide**

### **Step 1: Add to Registry**
```typescript
// plugins/registry.ts
export const PLUGIN_REGISTRY = {
  // ... existing plugins
  mynewplugin: {
    enabled: true,
    name: 'My New Plugin',
    icon: 'Star',
    route: '/mynewplugin',
    backend: true,
    description: 'An amazing new feature',
    permissions: ['mynewplugin:read'],
    version: '1.0.0'
  }
};
```

### **Step 2: Create Frontend Plugin**
```bash
# Create plugin structure
mkdir -p altzero_base/features/mynewplugin/{components,pages,services,types}
```

```typescript
// features/mynewplugin/index.ts
import React from 'react';
import { PluginModule } from '../../plugins/types';
import MyPluginPage from './pages/MyPluginPage';

const routes = [
  { path: '/mynewplugin', element: React.createElement(MyPluginPage) }
];

const navigation = [
  { name: 'My New Plugin', route: '/mynewplugin', icon: 'Star', order: 6 }
];

const mynewpluginPlugin: PluginModule = {
  routes,
  navigation,
  providers: [], // Add context providers if needed
  config: { name: 'My New Plugin', version: '1.0.0' }
};

export default mynewpluginPlugin;
```

### **Step 3: Create Page Component**
```typescript
// features/mynewplugin/pages/MyPluginPage.tsx
import React from 'react';

const MyPluginPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-3xl font-bold mb-6">My New Plugin</h1>
      <p>Welcome to my amazing new feature!</p>
    </div>
  );
};

export default MyPluginPage;
```

### **Step 4: Add Backend (Optional)**
```typescript
// server/plugins/registry.ts
export const BACKEND_PLUGIN_REGISTRY = {
  // ... existing plugins
  mynewplugin: {
    enabled: true,
    name: 'My New Plugin API',
    apiPrefix: '/api/mynewplugin',
    version: '1.0.0'
  }
};
```

```bash
# Create backend structure
mkdir -p altzero_base/server/features/mynewplugin/{routes,services}
```

```typescript
// server/features/mynewplugin/routes/index.ts
import express from 'express';
const router = express.Router();

router.get('/test', (req, res) => {
  res.json({ message: 'My new plugin API works!' });
});

export default router;
```

### **Step 5: Test It! 🎉**
```bash
npm run dev
```

**That's it!** Your plugin will automatically:
- ✅ Appear in navigation
- ✅ Handle routes
- ✅ Mount backend APIs
- ✅ Work with authentication
- ✅ Support all features

---

# 🎯 **Benefits Achieved**

## **✅ Zero Core Impact**
- Adding plugins **never touches** App.tsx, Header.tsx, or MainLayout.tsx
- Core application remains stable and clean
- Single registry file controls everything

## **✅ Automatic Discovery**
- Frontend: Dynamic imports from `features/{name}/index.ts`
- Backend: Auto-mount APIs at `/api/{name}`
- Navigation: Self-registering menu items

## **✅ Easy Management**
```typescript
// Disable any plugin instantly
mynewplugin: { enabled: false, ... }

// Enable with one line
mynewplugin: { enabled: true, ... }
```

## **✅ Standardized Development**
- Every plugin follows same structure
- Predictable file locations
- TypeScript contracts ensure consistency
- Easy onboarding for new developers

---

# 🔧 **Plugin Development Tips**

## **Frontend Best Practices**
- Use `React.createElement()` in routes (no JSX in .ts files)
- Add `order` to navigation for menu positioning
- Include proper TypeScript types
- Use absolute imports: `../../plugins/types`

## **Backend Best Practices**
- Export Express router as default
- Use consistent API patterns
- Add proper error handling
- Include health checks

## **File Organization**
```
features/myplugin/
├── index.ts           # Plugin export (REQUIRED)
├── components/        # Reusable components
├── pages/            # Route components
├── services/         # API calls
├── types/            # TypeScript types
├── utils/            # Helper functions
└── context/          # State management (if needed)
```

---

# 🎉 **Migration Complete!**

The AltZero platform now has a **fully dynamic plugin architecture** where:

1. **Core files never change** when adding features
2. **Plugins self-register** through the registry
3. **Frontend and backend** work seamlessly together
4. **Navigation, routes, and APIs** load automatically
5. **Enable/disable plugins** with a single config change

**Ready to build the next amazing plugin?** Follow the guide above! 🚀