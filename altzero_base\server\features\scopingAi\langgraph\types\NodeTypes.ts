// =====================================================
// SCOPINGAI NODE TYPES - LANGGRAPH
// =====================================================

import { ScopingAiWorkflowState, WorkflowContext } from './WorkflowState';

export interface BaseNode {
  name: string;
  description: string;
  execute(context: WorkflowContext): Promise<Partial<ScopingAiWorkflowState>>;
}

export interface NodeMetadata {
  node_name: string;
  execution_order: number;
  dependencies: string[];
  optional: boolean;
  timeout_seconds: number;
  retry_attempts: number;
  resource_requirements: {
    memory_mb: number;
    cpu_cores: number;
  };
  tags?: string[];
  version?: string;
}

export interface DetailedNodeResult {
  success: boolean;
  data: Partial<ScopingAiWorkflowState>;
  node_name: string;
  status: NodeStatus;
  started_at: string;
  completed_at: string;
  execution_time_ms: number;
  retry_count: number;
  warnings: string[];
  metrics?: NodeMetrics;
  error?: string;
}

export type NodeStatus = 'pending' | 'running' | 'completed' | 'failed' | 'skipped' | 'cancelled';

export interface NodeMetrics {
  data_points_processed: number;
  api_calls_made: number;
  tokens_used: number;
  cost_estimate: number;
  quality_score?: number;
  performance_score?: number;
}

// Specific node result types
export interface ValidationNodeResult {
  validation_passed: boolean;
  validation_errors: string[];
  validation_warnings: string[];
  input_summary: {
    client_name: string;
    project_title: string;
    sections_count: number;
    knowledge_docs_count: number;
  };
}

export interface KnowledgeRetrievalNodeResult {
  retrieved_documents: any[];
  relevant_content: string;
  search_results: any[];
  content_summary: string;
  retrieval_metrics: {
    documents_found: number;
    total_content_length: number;
    average_relevance_score: number;
    search_time_ms: number;
  };
}

export interface ClientAnalysisNodeResult {
  industry_insights: any[];
  market_position: string;
  key_challenges: string[];
  opportunities: string[];
  competitive_landscape: any[];
  recommendations: string[];
  analysis_confidence: number;
}

export interface MarketResearchNodeResult {
  industry_trends: any[];
  market_size: any;
  growth_projections: any[];
  key_players: any[];
  regulatory_factors: string[];
  research_sources: string[];
  data_freshness: string;
}

export interface ResearchAnalysisNodeResult {
  content: string;
  key_findings: string[];
  recommendations: string[];
  data_sources: string[];
  analysis_depth: number;
  confidence_score: number;
}

export interface ExecutiveSummaryNodeResult {
  content: string;
  key_points: string[];
  value_proposition: string;
  word_count: number;
  readability_score: number;
}

export interface SectionGenerationNodeResult {
  sections: Array<{
    title: string;
    content: string;
    word_count: number;
    quality_score: number;
    generation_time_ms: number;
  }>;
  total_word_count: number;
  average_quality_score: number;
  generation_metrics: {
    total_tokens_used: number;
    total_cost: number;
    average_generation_time: number;
  };
}

export interface QualityReviewNodeResult {
  overall_quality_score: number;
  section_scores: Record<string, number>;
  quality_metrics: {
    content_relevance: number;
    technical_accuracy: number;
    business_value: number;
    readability: number;
    completeness: number;
    consistency: number;
  };
  improvement_suggestions: string[];
  passed_quality_threshold: boolean;
}

export interface FinalizationNodeResult {
  final_proposal: {
    id: string;
    title: string;
    sections: any[];
    metadata: any;
    quality_metrics: any;
  };
  database_saved: boolean;
  file_generated: boolean;
  notification_sent: boolean;
}

// Node execution context extensions
export interface NodeExecutionContext extends WorkflowContext {
  node_metadata: NodeMetadata;
  execution_id: string;
  start_time: number;
  timeout_handle?: NodeJS.Timeout;
}

// Error types specific to nodes
export interface NodeExecutionError extends Error {
  node_name: string;
  error_code: string;
  recoverable: boolean;
  retry_count: number;
  context?: any;
}

// Node configuration types
export interface NodeConfig {
  enabled: boolean;
  timeout_override?: number;
  retry_override?: number;
  custom_parameters?: Record<string, any>;
  quality_threshold?: number;
  cache_enabled?: boolean;
  parallel_execution?: boolean;
}

// Progress tracking for nodes
export interface NodeProgress {
  node_name: string;
  status: NodeStatus;
  progress_percentage: number;
  current_operation: string;
  estimated_completion?: string;
  sub_tasks?: Array<{
    name: string;
    status: NodeStatus;
    progress: number;
  }>;
}
