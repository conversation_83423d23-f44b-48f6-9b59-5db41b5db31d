// Document section interfaces
export interface Section {
  id?: string;
  title: string;
  content?: string;
  description?: string;
  order: number;
  elements?: Array<{
    type: string;
    content: string;
    items?: string[];
  }>;
  imageData?: Array<{
    data: string;
    width?: number;
    height?: number;
    position?: {
      x: number;
      y: number;
      page?: number;
    };
  }>;
  data?: unknown[];
}

// Interface for processed sections with required content
export interface ProcessedSection extends Omit<Section, 'content'> {
  title: string;
  content: string; // Making content required
  description?: string;
  images?: string[];
  imageData?: ProcessedImageData[];
  data?: Array<Array<string | number | boolean | null>>;
  order?: number;
  elements?: DocumentElement[];
  pageStart?: number;
  pageEnd?: number;
}

export interface ImagePosition {
  x: number;
  y: number;
  width: number;
  height: number;
  page: number;
}

export interface DocumentElement {
  type: string;
  content: string;
  level?: number;
  items?: string[];
  isNumbered?: boolean;
  order?: number;
  style?: ElementStyle;
  position?: {
    x: number;
    y: number;
    page: number;
    width?: number;
    height?: number;
  };
  imageData?: ProcessedImageData;
}

export interface ElementStyle {
  fontSize?: number;
  fontFamily?: string;
  isBold?: boolean;
  isItalic?: boolean;
  alignment?: "left" | "center" | "right";
  color?: string;
  lineHeight?: number;
  marginTop?: number;
  marginBottom?: number;
  marginLeft?: number;
  marginRight?: number;
}

export interface ProcessedDocument {
  title: string;
  sections: ProcessedSection[];
  metadata?: {
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    pageCount?: number;
    fileType?: string;
    sheetCount?: number;
  };
  fullContent?: string;
  structured_content?: {
    pages: PageContent[];
  };
  pages?: {
    pageNumber: number;
    elements: DocumentElement[];
  }[];
}

export interface PageContent {
  page_number: number;
  content: StructuredContent[];
}

export interface StructuredContent {
  type: string;
  content: string;
  position: {
    x: number;
    y: number;
    page: number;
  };
  font?: string;
  fontSize?: number;
  color?: string;
}

// Update ImageData interface to match the actual data structure
export interface ProcessedImageData {
  data: string;
  width?: number;
  height?: number;
  position: ImagePosition;
  format?: string;
  alt?: string;
}

export interface DocumentData {
  id?: number | string;
  title: string;
  content?: string;
  metadata?: {
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    pageCount?: number;
    fileType?: string;
  };
  sections?: ProcessedSection[];
  structured_content?: {
    pages: Array<{
      page_number: number;
      content: Array<{
        type: string;
        content: string;
        position: {
          x: number;
          y: number;
          page: number;
        };
        font?: string;
        fontSize?: number;
        color?: string;
      }>;
    }>;
  };
}

export interface TemplateDocument {
  id: string;
  title: string;
  description: string;
  date: string;
  sections: string[];
  rawData: {
    content: string;
    sections?: ProcessedSection[];
    structured_content?: {
      pages: PageContent[];
    };
    metadata?: {
      author?: string;
      createdDate?: string;
      modifiedDate?: string;
      pageCount?: number;
      fileType?: string;
    }
  };
} 