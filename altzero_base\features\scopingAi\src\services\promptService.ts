import { supabase } from "../lib/supabase";

export interface PromptTemplate {
  id: string;
  user_id: string;
  name: string;
  description: string;
  content: string;
  variables: string[];
  created_at: string;
  updated_at: string;
}

export interface PromptFormData {
  name: string;
  description: string;
  content: string;
  variables?: string[];
}

// Fetch all prompts for the current user
export const fetchPrompts = async (): Promise<PromptTemplate[]> => {
  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting user:", userError);
      throw userError;
    }

    const userId = userData.user?.id;
    if (!userId) {
      console.error("No user ID found, user may not be authenticated");
      throw new Error("User not authenticated");
    }

    // Fetch prompts for this user
    const { data, error } = await supabase
      .from("scopingai_prompttemplates")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching prompts:", error);
      throw error;
    }

    return data || [];
  } catch (err) {
    console.error("Error in fetchPrompts:", err);
    throw err;
  }
};

// Save a new prompt
export const savePrompt = async (
  promptData: PromptFormData
): Promise<PromptTemplate> => {
  try {
    if (!promptData.name.trim() || !promptData.content.trim()) {
      throw new Error("Prompt name and content are required");
    }

    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) throw userError;

    const userId = userData.user?.id;
    if (!userId) throw new Error("User not authenticated");

    // Extract variables from the content using {{variableName}} pattern
    const variableMatches = promptData.content.match(/\{\{([^}]+)\}\}/g) || [];
    const variables = variableMatches.map((match) =>
      match.substring(2, match.length - 2).trim()
    );

    const { data, error } = await supabase
      .from("scopingai_prompttemplates")
      .insert([
        {
          user_id: userId,
          name: promptData.name,
          description: promptData.description || "",
          content: promptData.content,
          variables: variables,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (err) {
    console.error("Error saving prompt:", err);
    throw err;
  }
};

// Update an existing prompt
export const updatePrompt = async (
  id: string,
  promptData: PromptFormData
): Promise<PromptTemplate> => {
  try {
    if (!promptData.name.trim() || !promptData.content.trim()) {
      throw new Error("Prompt name and content are required");
    }

    // Extract variables from the content using {{variableName}} pattern
    const variableMatches = promptData.content.match(/\{\{([^}]+)\}\}/g) || [];
    const variables = variableMatches.map((match) =>
      match.substring(2, match.length - 2).trim()
    );

    const { data, error } = await supabase
      .from("scopingai_prompttemplates")
      .update({
        name: promptData.name,
        description: promptData.description || "",
        content: promptData.content,
        variables: variables,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return data;
  } catch (err) {
    console.error("Error updating prompt:", err);
    throw err;
  }
};

// Delete a prompt
export const deletePrompt = async (id: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from("scopingai_prompttemplates")
      .delete()
      .eq("id", id);

    if (error) throw error;
  } catch (err) {
    console.error("Error deleting prompt:", err);
    throw err;
  }
};

// Get prompt by ID
export const getPromptById = async (
  id: string
): Promise<PromptTemplate | null> => {
  try {
    const { data, error } = await supabase
      .from("scopingai_prompttemplates")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // Row not found
        return null;
      }
      throw error;
    }

    return data;
  } catch (err) {
    console.error("Error fetching prompt by ID:", err);
    throw err;
  }
};

// Search prompts
export const searchPrompts = async (
  query: string
): Promise<PromptTemplate[]> => {
  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) throw userError;

    const userId = userData.user?.id;
    if (!userId) throw new Error("User not authenticated");

    const { data, error } = await supabase
      .from("scopingai_prompttemplates")
      .select("*")
      .eq("user_id", userId)
      .or(
        `name.ilike.%${query}%,description.ilike.%${query}%,content.ilike.%${query}%`
      )
      .order("created_at", { ascending: false });

    if (error) throw error;

    return data || [];
  } catch (err) {
    console.error("Error searching prompts:", err);
    throw err;
  }
};
