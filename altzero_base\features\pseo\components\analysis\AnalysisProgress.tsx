import React from 'react';

interface AnalysisJob {
  id: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentStep: string;
  results?: Record<string, any>;
  error?: string;
  startedAt?: string;
  completedAt?: string;
}

interface AnalysisProgressProps {
  job: AnalysisJob;
  onCancel: () => void;
}

export const AnalysisProgress: React.FC<AnalysisProgressProps> = ({
  job,
  onCancel
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'cancelled':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'running':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'queued':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'cancelled':
        return '⚠️';
      case 'running':
        return '⚡';
      case 'queued':
        return '⏳';
      default:
        return '⭕';
    }
  };

  const formatElapsedTime = (startTime?: string) => {
    if (!startTime) return '';
    
    const start = new Date(startTime);
    const now = new Date();
    const elapsed = Math.floor((now.getTime() - start.getTime()) / 1000);
    
    if (elapsed < 60) return `${elapsed}s`;
    if (elapsed < 3600) return `${Math.floor(elapsed / 60)}m ${elapsed % 60}s`;
    return `${Math.floor(elapsed / 3600)}h ${Math.floor((elapsed % 3600) / 60)}m`;
  };

  const isActive = ['queued', 'running'].includes(job.status);
  const canCancel = isActive;

  return (
    <div className="space-y-4">
      {/* Status Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-xl">{getStatusIcon(job.status)}</span>
          <div>
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(job.status)}`}>
              {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
            </div>
            {job.startedAt && (
              <p className="text-xs text-muted-foreground mt-1">
                Started {formatElapsedTime(job.startedAt)} ago
              </p>
            )}
          </div>
        </div>

        {canCancel && (
          <button
            onClick={onCancel}
            className="text-red-600 hover:text-red-700 text-sm font-medium"
          >
            Cancel
          </button>
        )}
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-foreground font-medium">Progress</span>
          <span className="text-muted-foreground">{job.progress}%</span>
        </div>
        
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              job.status === 'completed' ? 'bg-green-500' :
              job.status === 'failed' ? 'bg-red-500' :
              job.status === 'cancelled' ? 'bg-orange-500' :
              'bg-primary'
            }`}
            style={{ width: `${job.progress}%` }}
          />
        </div>
      </div>

      {/* Current Step */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-foreground">Current Step:</span>
          {isActive && (
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          )}
        </div>
        
        <p className="text-sm text-muted-foreground">{job.currentStep}</p>
      </div>

      {/* Error Message */}
      {job.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-700 font-medium mb-1">Error Details:</p>
          <p className="text-sm text-red-600">{job.error}</p>
        </div>
      )}

      {/* Job Details */}
      <div className="text-xs text-muted-foreground space-y-1 pt-2 border-t border-border">
        <div className="flex justify-between">
          <span>Job ID:</span>
          <span className="font-mono">{job.id.slice(-8)}</span>
        </div>
        
        {job.startedAt && (
          <div className="flex justify-between">
            <span>Started:</span>
            <span>{new Date(job.startedAt).toLocaleTimeString()}</span>
          </div>
        )}
        
        {job.completedAt && (
          <div className="flex justify-between">
            <span>Completed:</span>
            <span>{new Date(job.completedAt).toLocaleTimeString()}</span>
          </div>
        )}
      </div>

      {/* Analysis Steps Preview */}
      {isActive && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-foreground">Analysis Steps</h4>
          <div className="space-y-1 text-xs">
            <div className={`flex items-center gap-2 ${job.currentStep.includes('Page') ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              <span>{job.currentStep.includes('Page') ? '▶️' : '⏸️'}</span>
              <span>Page Discovery & Crawling</span>
            </div>
            <div className={`flex items-center gap-2 ${job.currentStep.includes('Keyword') ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              <span>{job.currentStep.includes('Keyword') ? '▶️' : '⏸️'}</span>
              <span>Keyword Research & Analysis</span>
            </div>
            <div className={`flex items-center gap-2 ${job.currentStep.includes('Content') ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              <span>{job.currentStep.includes('Content') ? '▶️' : '⏸️'}</span>
              <span>Content Gap Analysis</span>
            </div>
            <div className={`flex items-center gap-2 ${job.currentStep.includes('completed') ? 'text-green-600 font-medium' : 'text-muted-foreground'}`}>
              <span>{job.currentStep.includes('completed') ? '✅' : '⏸️'}</span>
              <span>Final Report Generation</span>
            </div>
          </div>
        </div>
      )}

      {/* Success Summary */}
      {job.status === 'completed' && job.results && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg space-y-2">
          <h4 className="text-sm font-medium text-green-800">Analysis Complete! 🎉</h4>
          <div className="text-xs text-green-700 space-y-1">
            {job.results.pages_discovered && (
              <div>📄 {job.results.pages_discovered} pages discovered</div>
            )}
            {job.results.keywords_found && (
              <div>🎯 {job.results.keywords_found} keywords analyzed</div>
            )}
            {job.results.content_opportunities && (
              <div>💡 {job.results.content_opportunities} content opportunities identified</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}; 