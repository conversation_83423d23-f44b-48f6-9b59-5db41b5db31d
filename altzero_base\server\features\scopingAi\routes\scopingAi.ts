import express, { Request, Response } from "express";
import fileUpload from "express-fileupload";
import os from "os";
import path from "path";
import OpenAI from "openai";
import { crmContactService } from "../services/integration/crmContactService";
import { organizationSyncService } from "../services/integration/organizationSyncService";
import { knowledgeBaseService } from "../services/integration/KnowledgeBaseService";
import { documentGenerationService } from "../services/core/DocumentGenerationService";
import { scopingAiDbService } from "../services/core/ScopingAiDatabaseService";
import { supabase } from "../../../base/common/apps/supabase";
import langgraphRoutes from "../langgraph/routes/langgraph";

const router = express.Router();

// Knowledge Base service is now handled through the new KnowledgeBaseService
console.log("✅ ScopingAI routes initialized with new service architecture");

// Simple auth middleware
const validateApiKey = (req: Request, res: Response, next: any) => {
  const apiKey = req.query.apiKey || req.headers["x-api-key"];

  console.log("🔑 API Key validation:", {
    received: apiKey,
    expected: "scopingai",
    headers: req.headers["x-api-key"],
    query: req.query.apiKey,
  });

  if (!apiKey) {
    return res.status(401).json({ error: "API key is required" });
  }

  if (apiKey !== "scopingai") {
    return res.status(403).json({ error: "Invalid API key" });
  }

  next();
};

// Configure file upload middleware
const fileUploadMiddleware = fileUpload({
  useTempFiles: true,
  tempFileDir: path.join(os.tmpdir(), "document-processor"),
  createParentPath: true,
  safeFileNames: true,
  preserveExtension: 4,
  abortOnLimit: true,
  uploadTimeout: 60000,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1,
  },
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Helper function to provide section-specific requirements
function getSectionRequirements(
  sectionTitle: string,
  hasReferenceData: boolean
): string {
  const requirements: Record<string, string> = {
    Introduction: `
- Company background and credentials
- Understanding of client's business and challenges
- Project overview and objectives
- Value proposition and unique differentiators
${
  hasReferenceData
    ? "- Reference to relevant case studies and proven track record"
    : ""
}`,

    Scope: `
- Detailed project scope and deliverables
- Work breakdown structure
- Inclusions and exclusions
- Success criteria and acceptance criteria
- Dependencies and assumptions
${
  hasReferenceData
    ? "- Reference to similar project scopes and methodologies from knowledge base"
    : ""
}`,

    Timeline: `
- Project phases and milestones
- Detailed timeline with key dates
- Critical path analysis
- Risk mitigation for timeline delays
- Resource allocation timeline
${
  hasReferenceData
    ? "- Benchmarking against similar project timelines from reference materials"
    : ""
}`,

    Budget: `
- Comprehensive cost breakdown
- Resource costs and allocation
- Payment terms and schedule
- Cost justification and ROI analysis
- Risk contingencies
${
  hasReferenceData
    ? "- Cost validation using reference data and industry benchmarks"
    : ""
}`,

    Methodology: `
- Detailed approach and methodology
- Best practices and frameworks
- Quality assurance processes
- Risk management approach
- Communication and reporting structure
${
  hasReferenceData
    ? "- Integration of proven methodologies from reference documents"
    : ""
}`,

    Team: `
- Team structure and roles
- Key personnel qualifications
- Relevant experience and expertise
- Team availability and commitment
- Escalation procedures
${
  hasReferenceData
    ? "- Team credentials validated against similar project requirements"
    : ""
}`,

    Deliverables: `
- Comprehensive list of deliverables
- Quality standards and specifications
- Delivery schedule and format
- Acceptance criteria for each deliverable
- Intellectual property considerations
${
  hasReferenceData
    ? "- Deliverable specifications based on reference standards and best practices"
    : ""
}`,
  };

  return (
    requirements[sectionTitle] ||
    `
- Section-specific content relevant to "${sectionTitle}"
- Professional presentation with clear structure
- Client-focused value proposition
- Actionable recommendations and next steps
${
  hasReferenceData
    ? "- Integration of relevant reference materials and best practices"
    : ""
}`
  );
}

// Health check for scopingAi
router.get("/health", (req, res) => {
  res.json({
    status: "ok",
    service: "scopingAi",
    timestamp: new Date().toISOString(),
  });
});

// Debug endpoint to check user and organization data
router.get(
  "/debug/user/:userId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { userId } = req.params;

      console.log(`🔍 DEBUG: Checking user data for: ${userId}`);

      // Check if user exists in auth.users
      const { data: authUser, error: authError } =
        await supabase.auth.admin.getUserById(userId);

      // Check organization memberships
      const { data: memberships, error: membershipError } = await supabase
        .from("organisation_members")
        .select(
          `
          organisation_id,
          role,
          created_at,
          organisations (
            id,
            name,
            created_at
          )
        `
        )
        .eq("user_id", userId);

      // Check if user has any CRM contacts
      const { data: crmContacts, error: crmError } = await supabase
        .from("crm_contacts")
        .select("id, full_name, organisation_id")
        .limit(5);

      // Check if user has any ScopingAI clients
      const { data: scopingClients, error: scopingError } = await supabase
        .from("scopingai_clients")
        .select("id, name, user_id")
        .eq("user_id", userId)
        .limit(5);

      const debugInfo = {
        userId,
        authUser: {
          exists: !!authUser.user,
          email: authUser.user?.email,
          error: authError?.message,
        },
        memberships: {
          count: memberships?.length || 0,
          data: memberships,
          error: membershipError?.message,
        },
        crmContacts: {
          totalSample: crmContacts?.length || 0,
          data: crmContacts,
          error: crmError?.message,
        },
        scopingClients: {
          count: scopingClients?.length || 0,
          data: scopingClients,
          error: scopingError?.message,
        },
      };

      console.log(`📊 DEBUG INFO:`, debugInfo);

      res.json({
        success: true,
        data: debugInfo,
      });
    } catch (error: any) {
      console.error("❌ Debug endpoint error:", error);
      res.status(500).json({
        error: "Debug check failed",
        details: error.message,
      });
    }
  }
);

// Knowledge base documents endpoint - using existing pineconeService
router.get(
  "/knowledge/documents",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const userId =
        req.query.userId || req.headers["x-user-id"] || "anonymous";

      console.log(
        `📚 GET /api/scopingai/knowledge/documents - User ID: ${userId}`
      );
      console.log(`📋 Request headers:`, {
        "x-user-id": req.headers["x-user-id"],
        "x-api-key": req.headers["x-api-key"],
      });
      console.log(`📋 Query params:`, req.query);

      // Fetch documents using the new Knowledge Base service
      const documents = await knowledgeBaseService.getUserDocuments(
        userId as string
      );

      console.log(
        `✅ Returning ${documents.length} documents for user ${userId}`
      );
      res.json(documents);
    } catch (error) {
      console.error("❌ Error fetching documents from Pinecone:", error);
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  }
);

// Debug endpoint to test search functionality
router.post(
  "/debug/search-test",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { query, userId, documentId } = req.body;

      console.log(`🔍 DEBUG SEARCH TEST:`, { query, userId, documentId });

      // Test basic search without filters
      const basicSearch = await knowledgeBaseService.searchDocuments(userId, {
        query: query,
        limit: 10,
        minScore: 0.3,
      });

      // Test targeted search if documentId provided
      let targetedSearch = null;
      if (documentId) {
        targetedSearch = await knowledgeBaseService.searchByRequirements(
          { [documentId]: query },
          userId,
          [documentId]
        );
      }

      res.json({
        success: true,
        basicSearch: {
          documentsFound: basicSearch.documents.length,
          totalFound: basicSearch.totalFound,
          processingTime: basicSearch.processingTime,
          documents: basicSearch.documents.map((doc) => ({
            id: doc.id,
            title: doc.title,
            contentPreview: doc.content.substring(0, 200) + "...",
            type: doc.type,
            size: doc.size,
          })),
        },
        targetedSearch: targetedSearch
          ? {
              relevantContentLength: targetedSearch.relevantContent.length,
              searchResultsCount: targetedSearch.searchResults.length,
              contentPreview:
                targetedSearch.relevantContent.substring(0, 500) + "...",
              searchResults: targetedSearch.searchResults.map((result) => ({
                documentId: result.documentId,
                requirement: result.requirement,
                chunksFound: result.relevantChunks.length,
                avgScore:
                  result.relevantChunks.reduce(
                    (sum, chunk) => sum + chunk.score,
                    0
                  ) / result.relevantChunks.length,
              })),
            }
          : null,
      });
    } catch (error: any) {
      console.error("❌ Debug search test failed:", error);
      res.status(500).json({
        error: "Debug search test failed",
        details: error.message,
      });
    }
  }
);

// =============================================
// CRM CONTACT INTEGRATION ENDPOINTS (Phase 2)
// =============================================

// Get all available contacts for organization (CRM + ScopingAI)
router.get(
  "/contacts/organization/:orgId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🏢 GET /api/scopingai/contacts/organization/${orgId} - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const contacts = await crmContactService.getOrganizationContacts(
        orgId,
        userId
      );

      console.log(
        `✅ Returning ${contacts.length} contacts for organization ${orgId}`
      );
      res.json({
        success: true,
        data: contacts,
        meta: {
          total: contacts.length,
          crmContacts: contacts.filter((c) => c.source === "crm").length,
          scopingaiClients: contacts.filter((c) => c.source === "scopingai")
            .length,
        },
      });
    } catch (error: any) {
      console.error("❌ Error fetching organization contacts:", error);
      res.status(500).json({
        error: "Failed to fetch organization contacts",
        details: error.message,
      });
    }
  }
);

// Note: CRM contacts are now directly available in ScopingAI without linking

// Convert CRM contact to dedicated ScopingAI client
router.post(
  "/contacts/convert-crm/:crmContactId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { crmContactId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔄 POST /api/scopingai/contacts/convert-crm/${crmContactId} - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!crmContactId) {
        return res.status(400).json({ error: "CRM Contact ID is required" });
      }

      const newClientId =
        await crmContactService.convertCrmContactToScopingClient(
          crmContactId,
          userId
        );

      console.log(
        `✅ Successfully converted CRM contact to ScopingAI client: ${newClientId}`
      );
      res.json({
        success: true,
        message: "CRM contact converted to ScopingAI client successfully",
        data: { newClientId },
      });
    } catch (error: any) {
      console.error("❌ Error converting CRM contact:", error);
      res.status(500).json({
        error: "Failed to convert CRM contact",
        details: error.message,
      });
    }
  }
);

// Debug endpoint to test organization contacts directly
router.get(
  "/debug/test-contacts/:orgId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔍 DEBUG: Testing contacts for org ${orgId}, user ${userId}`
      );

      // Test 1: Direct CRM contacts query
      const { data: directCrmContacts, error: crmError } = await supabase
        .from("crm_contacts")
        .select("id, full_name, email, organisation_id")
        .eq("organisation_id", orgId)
        .limit(5);

      // Test 2: User organization memberships
      const { data: userMemberships, error: membershipError } = await supabase
        .from("organisation_members")
        .select("organisation_id, role")
        .eq("user_id", userId);

      // Test 3: All CRM contacts for user's organizations
      const userOrgIds = userMemberships?.map((m) => m.organisation_id) || [];
      const { data: allUserCrmContacts, error: allCrmError } = await supabase
        .from("crm_contacts")
        .select("id, full_name, email, organisation_id")
        .in("organisation_id", userOrgIds)
        .limit(10);

      // Test 4: ScopingAI clients
      const { data: scopingClients, error: scopingError } = await supabase
        .from("scopingai_clients")
        .select("id, name, email, user_id")
        .eq("user_id", userId)
        .limit(5);

      // Test 5: Use the actual service method
      let serviceResult = null;
      let serviceError = null;
      try {
        serviceResult = await crmContactService.getOrganizationContacts(
          orgId,
          userId
        );
      } catch (error: any) {
        serviceError = error.message;
      }

      const debugData = {
        orgId,
        userId,
        tests: {
          directCrmContacts: {
            count: directCrmContacts?.length || 0,
            data: directCrmContacts,
            error: crmError?.message,
          },
          userMemberships: {
            count: userMemberships?.length || 0,
            data: userMemberships,
            error: membershipError?.message,
          },
          allUserCrmContacts: {
            count: allUserCrmContacts?.length || 0,
            data: allUserCrmContacts,
            error: allCrmError?.message,
          },
          scopingClients: {
            count: scopingClients?.length || 0,
            data: scopingClients,
            error: scopingError?.message,
          },
          serviceMethod: {
            count: serviceResult?.length || 0,
            crmContacts:
              serviceResult?.filter((c: any) => c.source === "crm").length || 0,
            scopingaiClients:
              serviceResult?.filter((c: any) => c.source === "scopingai")
                .length || 0,
            error: serviceError,
          },
        },
      };

      console.log(`📊 DEBUG RESULTS:`, debugData);

      res.json({
        success: true,
        data: debugData,
      });
    } catch (error: any) {
      console.error("❌ Debug test error:", error);
      res.status(500).json({
        error: "Debug test failed",
        details: error.message,
      });
    }
  }
);

// Quick check - do CRM contacts exist in organization?
router.get(
  "/debug/crm-check/:orgId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;

      console.log(`🔍 QUICK CHECK: CRM contacts in org ${orgId}`);

      // Direct query to see if ANY CRM contacts exist
      const { data: crmContacts, error } = await supabase
        .from("crm_contacts")
        .select("id, full_name, email, organisation_id")
        .eq("organisation_id", orgId);

      console.log(
        `📊 Found ${crmContacts?.length || 0} CRM contacts in org ${orgId}`
      );

      res.json({
        success: true,
        data: {
          orgId,
          crmContactsFound: crmContacts?.length || 0,
          contacts: crmContacts || [],
          error: error?.message,
        },
      });
    } catch (error: any) {
      console.error("❌ CRM check error:", error);
      res.status(500).json({
        error: "CRM check failed",
        details: error.message,
      });
    }
  }
);

// Get user's organization ID (helper endpoint)
router.get(
  "/user/organization",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const userId = req.headers["x-user-id"] as string;

      console.log(`👤 GET /api/scopingai/user/organization - User: ${userId}`);
      console.log(`📋 Request headers:`, {
        "x-user-id": req.headers["x-user-id"],
        "x-api-key": req.headers["x-api-key"],
        "content-type": req.headers["content-type"],
      });

      if (!userId) {
        console.log("❌ No user ID provided in headers");
        return res.status(401).json({ error: "User ID is required" });
      }

      console.log(`🔍 Looking up organization for user: ${userId}`);
      const organisationId = await crmContactService.getUserOrganization(
        userId
      );

      console.log(`📊 Organization lookup result:`, {
        userId,
        organisationId,
        found: !!organisationId,
      });

      if (!organisationId) {
        console.log(`❌ User ${userId} is not a member of any organization`);
        return res.status(404).json({
          error: "User is not a member of any organization",
        });
      }

      console.log(
        `✅ User ${userId} belongs to organization ${organisationId}`
      );
      res.json({
        success: true,
        data: { organisationId },
      });
    } catch (error: any) {
      console.error("❌ Error getting user organization:", error);
      console.error("❌ Error stack:", error.stack);
      res.status(500).json({
        error: "Failed to get user organization",
        details: error.message,
      });
    }
  }
);

// =============================================
// ORGANIZATION SYNC ENDPOINTS (Phase 4)
// =============================================

// Get organization statistics
router.get(
  "/organization/:orgId/stats",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `📊 GET /api/scopingai/organization/${orgId}/stats - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const stats = await organizationSyncService.getOrganizationStats(orgId);

      if (!stats) {
        return res.status(404).json({ error: "Organization not found" });
      }

      console.log(`✅ Returning organization stats for ${orgId}`);
      res.json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      console.error("❌ Error fetching organization stats:", error);
      res.status(500).json({
        error: "Failed to fetch organization stats",
        details: error.message,
      });
    }
  }
);

// Auto-link all CRM contacts in organization
router.post(
  "/organization/:orgId/auto-link-contacts",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔗 POST /api/scopingai/organization/${orgId}/auto-link-contacts - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const linkedCount = await organizationSyncService.autoLinkCrmContacts(
        orgId,
        userId
      );

      console.log(
        `✅ Auto-linked ${linkedCount} CRM contacts for organization ${orgId}`
      );
      res.json({
        success: true,
        message: `Successfully auto-linked ${linkedCount} CRM contacts`,
        data: { linkedCount },
      });
    } catch (error: any) {
      console.error("❌ Error auto-linking CRM contacts:", error);
      res.status(500).json({
        error: "Failed to auto-link CRM contacts",
        details: error.message,
      });
    }
  }
);

// Sync organization data
router.post(
  "/organization/:orgId/sync",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔄 POST /api/scopingai/organization/${orgId}/sync - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const syncResult = await organizationSyncService.syncOrganizationData(
        orgId,
        userId
      );

      console.log(`✅ Organization sync completed for ${orgId}:`, syncResult);
      res.json({
        success: true,
        message: "Organization data synced successfully",
        data: syncResult,
      });
    } catch (error: any) {
      console.error("❌ Error syncing organization data:", error);
      res.status(500).json({
        error: "Failed to sync organization data",
        details: error.message,
      });
    }
  }
);

// Get user's organization info
router.get(
  "/user/organization-info",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `👤 GET /api/scopingai/user/organization-info - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      const orgInfo = await organizationSyncService.getUserOrganizationInfo(
        userId
      );

      if (!orgInfo) {
        return res.status(404).json({
          error: "User is not a member of any organization",
        });
      }

      console.log(`✅ User organization info:`, orgInfo);
      res.json({
        success: true,
        data: orgInfo,
      });
    } catch (error: any) {
      console.error("❌ Error getting user organization info:", error);
      res.status(500).json({
        error: "Failed to get user organization info",
        details: error.message,
      });
    }
  }
);

// Document upload endpoint
router.post(
  "/documents",
  validateApiKey,
  fileUploadMiddleware as any,
  async (req: any, res) => {
    try {
      if (!req.files || Object.keys(req.files).length === 0) {
        return res.status(400).json({ error: "No files were uploaded" });
      }

      const files = req.files as any;
      let uploadedFile: any;

      if (Array.isArray(files.file)) {
        uploadedFile = files.file[0];
      } else {
        uploadedFile = files.file as any;
      }

      // Store basic file info
      const documentId = `doc_${Date.now()}`;

      return res.status(200).json({
        id: documentId,
        filename: uploadedFile.name,
        size: uploadedFile.size,
      });
    } catch (error: any) {
      console.error("Error uploading document:", error);
      return res.status(500).json({
        error: error.message || "Failed to process document",
      });
    }
  }
);

// Main proposal generation streaming endpoint - NOW USING LANGGRAPH!
router.post(
  "/proposals/stream",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      console.log(
        "🔥 Proposal stream POST request received - USING LANGGRAPH WORKFLOW"
      );

      const {
        client,
        template,
        requirements,
        aiPrompts,
        project,
        selectedKnowledgeDocuments, // Add knowledge base document IDs
        documentRequirements, // Add document-specific requirements
        userId, // Add userId for Pinecone filtering
      } = req.body;

      // Get user ID from headers if not in body
      const user_id = userId || (req.headers["x-user-id"] as string);

      if (!user_id) {
        return res.status(400).json({ error: "User ID is required" });
      }

      console.log(
        "🚀 Redirecting to LangGraph workflow for advanced proposal generation"
      );

      // Prepare LangGraph workflow input
      const workflowInput = {
        user_id,
        client,
        project,
        template,
        requirements: requirements || {},
        selected_knowledge_documents: selectedKnowledgeDocuments || [],
        document_requirements: documentRequirements || {},
        ai_prompts: aiPrompts,
        config: {
          timeout_seconds: 600,
          quality_threshold: 0.75,
          enable_market_research: true,
          enable_quality_review: true,
          max_retries: 2,
        },
      };

      // Import and execute LangGraph workflow
      const { ProposalWorkflow } = await import(
        "../langgraph/workflows/ProposalWorkflow"
      );

      // Set up SSE headers
      res.writeHead(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      });

      // Send initial event
      res.write(`event: started\n`);
      res.write(
        `data: ${JSON.stringify({
          message: "LangGraph workflow started",
          workflow_type: "advanced_proposal_generation",
          client_name: client?.name,
        })}\n\n`
      );

      // Send research phase event (mimicking old behavior for UI compatibility)
      res.write(`event: research\n`);
      res.write(
        `data: ${JSON.stringify({
          status: "started",
          message: "Starting LangGraph workflow with knowledge retrieval...",
        })}\n\n`
      );

      // Create and execute workflow
      const workflow = await ProposalWorkflow.create(workflowInput.config);
      const result = await workflow.executeProposal(workflowInput);

      console.log("✅ LangGraph workflow execution completed:", {
        workflow_id: result.workflow_id,
        status: result.status,
        processing_time: result.processing_time,
        sections_generated: result.proposal_sections?.length || 0,
      });

      // Send research completion event
      res.write(`event: research\n`);
      res.write(
        `data: ${JSON.stringify({
          status: "completed",
          content:
            result.research_analysis?.content ||
            "Research analysis completed using LangGraph workflow",
          referenceDocumentsUsed:
            result.selected_knowledge_documents?.length || 0,
          resourceSummary:
            "LangGraph workflow processed knowledge base documents",
        })}\n\n`
      );

      // Send summary phase
      res.write(`event: summary\n`);
      res.write(
        `data: ${JSON.stringify({
          status: "started",
          message: "Creating executive summary...",
        })}\n\n`
      );

      res.write(`event: summary\n`);
      res.write(
        `data: ${JSON.stringify({
          status: "completed",
          content:
            result.executive_summary?.content ||
            "Executive summary generated by LangGraph",
        })}\n\n`
      );

      // Send progress events for each section
      if (result.proposal_sections) {
        for (let i = 0; i < result.proposal_sections.length; i++) {
          const section = result.proposal_sections[i];

          // Send section start event
          res.write(`event: section\n`);
          res.write(
            `data: ${JSON.stringify({
              status: "started",
              title: section.title,
            })}\n\n`
          );

          // Send section completion event
          res.write(`event: section\n`);
          res.write(
            `data: ${JSON.stringify({
              status: "completed",
              section: {
                title: section.title,
                content: section.content,
              },
            })}\n\n`
          );
        }
      }

      // Send completion event with full proposal (matching old format for UI compatibility)
      res.write(`event: completed\n`);
      res.write(
        `data: ${JSON.stringify({
          proposal: {
            id: result.final_proposal?.id || `proposal_${Date.now()}`,
            type: "client_proposal",
            clientName: client?.name || "Unknown",
            clientIndustry: client?.industry || "Unknown",
            projectTitle: project?.description || "Project Proposal",
            sections:
              result.proposal_sections?.map((section) => ({
                title: section.title,
                content: section.content,
              })) || [],
            createdAt: new Date(),
            updatedAt: new Date(),
            summary: result.executive_summary?.content || "",
            research: result.research_analysis?.content || "",
            metadata: {
              generatedBy: "LangGraph Workflow",
              version: "2.0",
              totalSections: result.proposal_sections?.length || 0,
              referenceDocumentsUsed:
                result.selected_knowledge_documents?.length || 0,
              referenceDocuments: [],
              documentsWithRequirements: 0,
              hasKnowledgeBase:
                (result.selected_knowledge_documents?.length || 0) > 0,
              knowledgeBaseContentLength: 0,
              proposalQuality: "Enhanced with LangGraph Workflow",
              aiModel: "LangGraph Multi-Node Workflow",
              generationTimestamp: new Date().toISOString(),
              workflow_id: result.workflow_id,
              processing_time_ms: result.processing_time,
              total_cost: result.total_cost,
              quality_score:
                result.final_proposal?.quality_metrics?.overall_score || 85,
              word_count:
                result.final_proposal?.metadata?.total_word_count || 0,
            },
          },
        })}\n\n`
      );

      // End stream
      res.write(`event: end\n`);
      res.write(
        `data: ${JSON.stringify({
          message: "Stream complete",
        })}\n\n`
      );
      res.end();
    } catch (error: any) {
      console.error("❌ LangGraph workflow execution failed:", error);

      if (!res.headersSent) {
        res.status(500).json({
          error: "Failed to start LangGraph workflow",
          details: error.message,
        });
      } else {
        res.write(`event: error\n`);
        res.write(
          `data: ${JSON.stringify({
            error: "LangGraph workflow execution failed",
            details: error.message,
          })}\n\n`
        );
        res.end();
      }
    }
  }
);

// =============================================
// LANGGRAPH WORKFLOW ROUTES
// =============================================

// Mount LangGraph routes under /langgraph prefix
router.use("/langgraph", langgraphRoutes);

console.log("🚀 LangGraph routes mounted at /api/scopingai/langgraph");

export default router;
