import express, { Request, Response } from "express";
import fileUpload from "express-fileupload";
import os from "os";
import path from "path";
import OpenAI from "openai";
import { crmContactService } from "../services/integration/crmContactService";
import { organizationSyncService } from "../services/integration/organizationSyncService";
import { knowledgeBaseService } from "../services/integration/KnowledgeBaseService";
import { documentGenerationService } from "../services/core/DocumentGenerationService";
import { scopingAiDbService } from "../services/core/ScopingAiDatabaseService";
import { supabase } from "../../../base/common/apps/supabase";
import langgraphRoutes from "../langgraph/routes/langgraph";

const router = express.Router();

// Knowledge Base service is now handled through the new KnowledgeBaseService
console.log("✅ ScopingAI routes initialized with new service architecture");

// Simple auth middleware
const validateApiKey = (req: Request, res: Response, next: any) => {
  const apiKey = req.query.apiKey || req.headers["x-api-key"];

  console.log("🔑 API Key validation:", {
    received: apiKey,
    expected: "scopingai",
    headers: req.headers["x-api-key"],
    query: req.query.apiKey,
  });

  if (!apiKey) {
    return res.status(401).json({ error: "API key is required" });
  }

  if (apiKey !== "scopingai") {
    return res.status(403).json({ error: "Invalid API key" });
  }

  next();
};

// Configure file upload middleware
const fileUploadMiddleware = fileUpload({
  useTempFiles: true,
  tempFileDir: path.join(os.tmpdir(), "document-processor"),
  createParentPath: true,
  safeFileNames: true,
  preserveExtension: 4,
  abortOnLimit: true,
  uploadTimeout: 60000,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1,
  },
});

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Helper function to provide section-specific requirements
function getSectionRequirements(
  sectionTitle: string,
  hasReferenceData: boolean
): string {
  const requirements: Record<string, string> = {
    Introduction: `
- Company background and credentials
- Understanding of client's business and challenges
- Project overview and objectives
- Value proposition and unique differentiators
${
  hasReferenceData
    ? "- Reference to relevant case studies and proven track record"
    : ""
}`,

    Scope: `
- Detailed project scope and deliverables
- Work breakdown structure
- Inclusions and exclusions
- Success criteria and acceptance criteria
- Dependencies and assumptions
${
  hasReferenceData
    ? "- Reference to similar project scopes and methodologies from knowledge base"
    : ""
}`,

    Timeline: `
- Project phases and milestones
- Detailed timeline with key dates
- Critical path analysis
- Risk mitigation for timeline delays
- Resource allocation timeline
${
  hasReferenceData
    ? "- Benchmarking against similar project timelines from reference materials"
    : ""
}`,

    Budget: `
- Comprehensive cost breakdown
- Resource costs and allocation
- Payment terms and schedule
- Cost justification and ROI analysis
- Risk contingencies
${
  hasReferenceData
    ? "- Cost validation using reference data and industry benchmarks"
    : ""
}`,

    Methodology: `
- Detailed approach and methodology
- Best practices and frameworks
- Quality assurance processes
- Risk management approach
- Communication and reporting structure
${
  hasReferenceData
    ? "- Integration of proven methodologies from reference documents"
    : ""
}`,

    Team: `
- Team structure and roles
- Key personnel qualifications
- Relevant experience and expertise
- Team availability and commitment
- Escalation procedures
${
  hasReferenceData
    ? "- Team credentials validated against similar project requirements"
    : ""
}`,

    Deliverables: `
- Comprehensive list of deliverables
- Quality standards and specifications
- Delivery schedule and format
- Acceptance criteria for each deliverable
- Intellectual property considerations
${
  hasReferenceData
    ? "- Deliverable specifications based on reference standards and best practices"
    : ""
}`,
  };

  return (
    requirements[sectionTitle] ||
    `
- Section-specific content relevant to "${sectionTitle}"
- Professional presentation with clear structure
- Client-focused value proposition
- Actionable recommendations and next steps
${
  hasReferenceData
    ? "- Integration of relevant reference materials and best practices"
    : ""
}`
  );
}

// Health check for scopingAi
router.get("/health", (req, res) => {
  res.json({
    status: "ok",
    service: "scopingAi",
    timestamp: new Date().toISOString(),
  });
});

// Debug endpoint to check user and organization data
router.get(
  "/debug/user/:userId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { userId } = req.params;

      console.log(`🔍 DEBUG: Checking user data for: ${userId}`);

      // Check if user exists in auth.users
      const { data: authUser, error: authError } =
        await supabase.auth.admin.getUserById(userId);

      // Check organization memberships
      const { data: memberships, error: membershipError } = await supabase
        .from("organisation_members")
        .select(
          `
          organisation_id,
          role,
          created_at,
          organisations (
            id,
            name,
            created_at
          )
        `
        )
        .eq("user_id", userId);

      // Check if user has any CRM contacts
      const { data: crmContacts, error: crmError } = await supabase
        .from("crm_contacts")
        .select("id, full_name, organisation_id")
        .limit(5);

      // Check if user has any ScopingAI clients
      const { data: scopingClients, error: scopingError } = await supabase
        .from("scopingai_clients")
        .select("id, name, user_id")
        .eq("user_id", userId)
        .limit(5);

      const debugInfo = {
        userId,
        authUser: {
          exists: !!authUser.user,
          email: authUser.user?.email,
          error: authError?.message,
        },
        memberships: {
          count: memberships?.length || 0,
          data: memberships,
          error: membershipError?.message,
        },
        crmContacts: {
          totalSample: crmContacts?.length || 0,
          data: crmContacts,
          error: crmError?.message,
        },
        scopingClients: {
          count: scopingClients?.length || 0,
          data: scopingClients,
          error: scopingError?.message,
        },
      };

      console.log(`📊 DEBUG INFO:`, debugInfo);

      res.json({
        success: true,
        data: debugInfo,
      });
    } catch (error: any) {
      console.error("❌ Debug endpoint error:", error);
      res.status(500).json({
        error: "Debug check failed",
        details: error.message,
      });
    }
  }
);

// Knowledge base documents endpoint - using existing pineconeService
router.get(
  "/knowledge/documents",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const userId =
        req.query.userId || req.headers["x-user-id"] || "anonymous";

      console.log(
        `📚 GET /api/scopingai/knowledge/documents - User ID: ${userId}`
      );
      console.log(`📋 Request headers:`, {
        "x-user-id": req.headers["x-user-id"],
        "x-api-key": req.headers["x-api-key"],
      });
      console.log(`📋 Query params:`, req.query);

      // Fetch documents using the new Knowledge Base service
      const documents = await knowledgeBaseService.getUserDocuments(
        userId as string
      );

      console.log(
        `✅ Returning ${documents.length} documents for user ${userId}`
      );
      res.json(documents);
    } catch (error) {
      console.error("❌ Error fetching documents from Pinecone:", error);
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  }
);

// Debug endpoint to test search functionality
router.post(
  "/debug/search-test",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { query, userId, documentId } = req.body;

      console.log(`🔍 DEBUG SEARCH TEST:`, { query, userId, documentId });

      // Test basic search without filters
      const basicSearch = await knowledgeBaseService.searchDocuments(userId, {
        query: query,
        limit: 10,
        minScore: 0.3,
      });

      // Test targeted search if documentId provided
      let targetedSearch = null;
      if (documentId) {
        targetedSearch = await knowledgeBaseService.searchByRequirements(
          { [documentId]: query },
          userId,
          [documentId]
        );
      }

      res.json({
        success: true,
        basicSearch: {
          documentsFound: basicSearch.documents.length,
          totalFound: basicSearch.totalFound,
          processingTime: basicSearch.processingTime,
          documents: basicSearch.documents.map((doc) => ({
            id: doc.id,
            title: doc.title,
            contentPreview: doc.content.substring(0, 200) + "...",
            type: doc.type,
            size: doc.size,
          })),
        },
        targetedSearch: targetedSearch
          ? {
              relevantContentLength: targetedSearch.relevantContent.length,
              searchResultsCount: targetedSearch.searchResults.length,
              contentPreview:
                targetedSearch.relevantContent.substring(0, 500) + "...",
              searchResults: targetedSearch.searchResults.map((result) => ({
                documentId: result.documentId,
                requirement: result.requirement,
                chunksFound: result.relevantChunks.length,
                avgScore:
                  result.relevantChunks.reduce(
                    (sum, chunk) => sum + chunk.score,
                    0
                  ) / result.relevantChunks.length,
              })),
            }
          : null,
      });
    } catch (error: any) {
      console.error("❌ Debug search test failed:", error);
      res.status(500).json({
        error: "Debug search test failed",
        details: error.message,
      });
    }
  }
);

// =============================================
// CRM CONTACT INTEGRATION ENDPOINTS (Phase 2)
// =============================================

// Get all available contacts for organization (CRM + ScopingAI)
router.get(
  "/contacts/organization/:orgId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🏢 GET /api/scopingai/contacts/organization/${orgId} - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const contacts = await crmContactService.getOrganizationContacts(
        orgId,
        userId
      );

      console.log(
        `✅ Returning ${contacts.length} contacts for organization ${orgId}`
      );
      res.json({
        success: true,
        data: contacts,
        meta: {
          total: contacts.length,
          crmContacts: contacts.filter((c) => c.source === "crm").length,
          scopingaiClients: contacts.filter((c) => c.source === "scopingai")
            .length,
        },
      });
    } catch (error: any) {
      console.error("❌ Error fetching organization contacts:", error);
      res.status(500).json({
        error: "Failed to fetch organization contacts",
        details: error.message,
      });
    }
  }
);

// Note: CRM contacts are now directly available in ScopingAI without linking

// Convert CRM contact to dedicated ScopingAI client
router.post(
  "/contacts/convert-crm/:crmContactId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { crmContactId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔄 POST /api/scopingai/contacts/convert-crm/${crmContactId} - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!crmContactId) {
        return res.status(400).json({ error: "CRM Contact ID is required" });
      }

      const newClientId =
        await crmContactService.convertCrmContactToScopingClient(
          crmContactId,
          userId
        );

      console.log(
        `✅ Successfully converted CRM contact to ScopingAI client: ${newClientId}`
      );
      res.json({
        success: true,
        message: "CRM contact converted to ScopingAI client successfully",
        data: { newClientId },
      });
    } catch (error: any) {
      console.error("❌ Error converting CRM contact:", error);
      res.status(500).json({
        error: "Failed to convert CRM contact",
        details: error.message,
      });
    }
  }
);

// Debug endpoint to test organization contacts directly
router.get(
  "/debug/test-contacts/:orgId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔍 DEBUG: Testing contacts for org ${orgId}, user ${userId}`
      );

      // Test 1: Direct CRM contacts query
      const { data: directCrmContacts, error: crmError } = await supabase
        .from("crm_contacts")
        .select("id, full_name, email, organisation_id")
        .eq("organisation_id", orgId)
        .limit(5);

      // Test 2: User organization memberships
      const { data: userMemberships, error: membershipError } = await supabase
        .from("organisation_members")
        .select("organisation_id, role")
        .eq("user_id", userId);

      // Test 3: All CRM contacts for user's organizations
      const userOrgIds = userMemberships?.map((m) => m.organisation_id) || [];
      const { data: allUserCrmContacts, error: allCrmError } = await supabase
        .from("crm_contacts")
        .select("id, full_name, email, organisation_id")
        .in("organisation_id", userOrgIds)
        .limit(10);

      // Test 4: ScopingAI clients
      const { data: scopingClients, error: scopingError } = await supabase
        .from("scopingai_clients")
        .select("id, name, email, user_id")
        .eq("user_id", userId)
        .limit(5);

      // Test 5: Use the actual service method
      let serviceResult = null;
      let serviceError = null;
      try {
        serviceResult = await crmContactService.getOrganizationContacts(
          orgId,
          userId
        );
      } catch (error: any) {
        serviceError = error.message;
      }

      const debugData = {
        orgId,
        userId,
        tests: {
          directCrmContacts: {
            count: directCrmContacts?.length || 0,
            data: directCrmContacts,
            error: crmError?.message,
          },
          userMemberships: {
            count: userMemberships?.length || 0,
            data: userMemberships,
            error: membershipError?.message,
          },
          allUserCrmContacts: {
            count: allUserCrmContacts?.length || 0,
            data: allUserCrmContacts,
            error: allCrmError?.message,
          },
          scopingClients: {
            count: scopingClients?.length || 0,
            data: scopingClients,
            error: scopingError?.message,
          },
          serviceMethod: {
            count: serviceResult?.length || 0,
            crmContacts:
              serviceResult?.filter((c: any) => c.source === "crm").length || 0,
            scopingaiClients:
              serviceResult?.filter((c: any) => c.source === "scopingai")
                .length || 0,
            error: serviceError,
          },
        },
      };

      console.log(`📊 DEBUG RESULTS:`, debugData);

      res.json({
        success: true,
        data: debugData,
      });
    } catch (error: any) {
      console.error("❌ Debug test error:", error);
      res.status(500).json({
        error: "Debug test failed",
        details: error.message,
      });
    }
  }
);

// Quick check - do CRM contacts exist in organization?
router.get(
  "/debug/crm-check/:orgId",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;

      console.log(`🔍 QUICK CHECK: CRM contacts in org ${orgId}`);

      // Direct query to see if ANY CRM contacts exist
      const { data: crmContacts, error } = await supabase
        .from("crm_contacts")
        .select("id, full_name, email, organisation_id")
        .eq("organisation_id", orgId);

      console.log(
        `📊 Found ${crmContacts?.length || 0} CRM contacts in org ${orgId}`
      );

      res.json({
        success: true,
        data: {
          orgId,
          crmContactsFound: crmContacts?.length || 0,
          contacts: crmContacts || [],
          error: error?.message,
        },
      });
    } catch (error: any) {
      console.error("❌ CRM check error:", error);
      res.status(500).json({
        error: "CRM check failed",
        details: error.message,
      });
    }
  }
);

// Get user's organization ID (helper endpoint)
router.get(
  "/user/organization",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const userId = req.headers["x-user-id"] as string;

      console.log(`👤 GET /api/scopingai/user/organization - User: ${userId}`);
      console.log(`📋 Request headers:`, {
        "x-user-id": req.headers["x-user-id"],
        "x-api-key": req.headers["x-api-key"],
        "content-type": req.headers["content-type"],
      });

      if (!userId) {
        console.log("❌ No user ID provided in headers");
        return res.status(401).json({ error: "User ID is required" });
      }

      console.log(`🔍 Looking up organization for user: ${userId}`);
      const organisationId = await crmContactService.getUserOrganization(
        userId
      );

      console.log(`📊 Organization lookup result:`, {
        userId,
        organisationId,
        found: !!organisationId,
      });

      if (!organisationId) {
        console.log(`❌ User ${userId} is not a member of any organization`);
        return res.status(404).json({
          error: "User is not a member of any organization",
        });
      }

      console.log(
        `✅ User ${userId} belongs to organization ${organisationId}`
      );
      res.json({
        success: true,
        data: { organisationId },
      });
    } catch (error: any) {
      console.error("❌ Error getting user organization:", error);
      console.error("❌ Error stack:", error.stack);
      res.status(500).json({
        error: "Failed to get user organization",
        details: error.message,
      });
    }
  }
);

// =============================================
// ORGANIZATION SYNC ENDPOINTS (Phase 4)
// =============================================

// Get organization statistics
router.get(
  "/organization/:orgId/stats",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `📊 GET /api/scopingai/organization/${orgId}/stats - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const stats = await organizationSyncService.getOrganizationStats(orgId);

      if (!stats) {
        return res.status(404).json({ error: "Organization not found" });
      }

      console.log(`✅ Returning organization stats for ${orgId}`);
      res.json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      console.error("❌ Error fetching organization stats:", error);
      res.status(500).json({
        error: "Failed to fetch organization stats",
        details: error.message,
      });
    }
  }
);

// Auto-link all CRM contacts in organization
router.post(
  "/organization/:orgId/auto-link-contacts",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔗 POST /api/scopingai/organization/${orgId}/auto-link-contacts - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const linkedCount = await organizationSyncService.autoLinkCrmContacts(
        orgId,
        userId
      );

      console.log(
        `✅ Auto-linked ${linkedCount} CRM contacts for organization ${orgId}`
      );
      res.json({
        success: true,
        message: `Successfully auto-linked ${linkedCount} CRM contacts`,
        data: { linkedCount },
      });
    } catch (error: any) {
      console.error("❌ Error auto-linking CRM contacts:", error);
      res.status(500).json({
        error: "Failed to auto-link CRM contacts",
        details: error.message,
      });
    }
  }
);

// Sync organization data
router.post(
  "/organization/:orgId/sync",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const { orgId } = req.params;
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `🔄 POST /api/scopingai/organization/${orgId}/sync - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      if (!orgId) {
        return res.status(400).json({ error: "Organization ID is required" });
      }

      const syncResult = await organizationSyncService.syncOrganizationData(
        orgId,
        userId
      );

      console.log(`✅ Organization sync completed for ${orgId}:`, syncResult);
      res.json({
        success: true,
        message: "Organization data synced successfully",
        data: syncResult,
      });
    } catch (error: any) {
      console.error("❌ Error syncing organization data:", error);
      res.status(500).json({
        error: "Failed to sync organization data",
        details: error.message,
      });
    }
  }
);

// Get user's organization info
router.get(
  "/user/organization-info",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      const userId = req.headers["x-user-id"] as string;

      console.log(
        `👤 GET /api/scopingai/user/organization-info - User: ${userId}`
      );

      if (!userId) {
        return res.status(401).json({ error: "User ID is required" });
      }

      const orgInfo = await organizationSyncService.getUserOrganizationInfo(
        userId
      );

      if (!orgInfo) {
        return res.status(404).json({
          error: "User is not a member of any organization",
        });
      }

      console.log(`✅ User organization info:`, orgInfo);
      res.json({
        success: true,
        data: orgInfo,
      });
    } catch (error: any) {
      console.error("❌ Error getting user organization info:", error);
      res.status(500).json({
        error: "Failed to get user organization info",
        details: error.message,
      });
    }
  }
);

// Document upload endpoint
router.post(
  "/documents",
  validateApiKey,
  fileUploadMiddleware as any,
  async (req: any, res) => {
    try {
      if (!req.files || Object.keys(req.files).length === 0) {
        return res.status(400).json({ error: "No files were uploaded" });
      }

      const files = req.files as any;
      let uploadedFile: any;

      if (Array.isArray(files.file)) {
        uploadedFile = files.file[0];
      } else {
        uploadedFile = files.file as any;
      }

      // Store basic file info
      const documentId = `doc_${Date.now()}`;

      return res.status(200).json({
        id: documentId,
        filename: uploadedFile.name,
        size: uploadedFile.size,
      });
    } catch (error: any) {
      console.error("Error uploading document:", error);
      return res.status(500).json({
        error: error.message || "Failed to process document",
      });
    }
  }
);

// Main proposal generation streaming endpoint with REAL knowledge base integration
router.post(
  "/proposals/stream",
  validateApiKey,
  async (req: Request, res: Response) => {
    try {
      console.log("🔥 Proposal stream POST request received");

      const {
        client,
        template,
        requirements,
        resources,
        aiPrompts,
        project,
        referenceDocument,
        selectedKnowledgeDocuments, // Add knowledge base document IDs
        documentRequirements, // Add document-specific requirements
        userId, // Add userId for Pinecone filtering
      } = req.body;

      console.log("📝 Document requirements received:", documentRequirements);

      // Set headers for Server-Sent Events
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");
      res.setHeader("Access-Control-Allow-Origin", "*");

      // Send initial event
      res.write(
        `event: started\ndata: ${JSON.stringify({
          message: "Starting document generation...",
          clientName: client?.name || "Client",
        })}\n\n`
      );

      // Research phase
      res.write(
        `event: research\ndata: ${JSON.stringify({
          status: "started",
          message:
            selectedKnowledgeDocuments && selectedKnowledgeDocuments.length > 0
              ? "Retrieving knowledge base documents from Pinecone..."
              : "Researching project requirements...",
        })}\n\n`
      );

      // Retrieve knowledge base documents if provided - USING REAL PINECONE SERVICE AS REFERENCE DATA
      let knowledgeBaseContent = "";
      let referenceDocuments: any[] = [];
      let resourceSummary = "";

      if (
        selectedKnowledgeDocuments &&
        selectedKnowledgeDocuments.length > 0 &&
        userId
      ) {
        try {
          console.log(
            `🎯 Searching for targeted content based on requirements for user ${userId}`
          );
          console.log(
            `📝 Document requirements provided:`,
            documentRequirements
          );

          // Use OPTIMIZED targeted search instead of fetching entire documents
          const searchResult = await knowledgeBaseService.searchByRequirements(
            documentRequirements,
            userId,
            selectedKnowledgeDocuments
          );

          console.log(
            `✅ Found targeted content for ${searchResult.searchResults.length} requirements`
          );

          if (searchResult.searchResults.length > 0) {
            // Structure the search results for reference tracking
            referenceDocuments = searchResult.searchResults.map(
              (result: any, index: number) => {
                return {
                  id: result.documentId,
                  title: `Requirement ${index + 1}: ${result.requirement}`,
                  type: "targeted_search",
                  content: result.relevantChunks
                    .map((chunk: any) => chunk.content)
                    .join("\n\n"),
                  uploadedAt: new Date().toISOString(),
                  size: result.relevantChunks.reduce(
                    (total: number, chunk: any) => total + chunk.content.length,
                    0
                  ),
                  userRequirement: result.requirement,
                  relevanceScores: result.relevantChunks.map((chunk: any) =>
                    Math.round(chunk.score * 100)
                  ),
                  chunksFound: result.relevantChunks.length,
                };
              }
            );

            // Create a structured reference summary for targeted search results
            resourceSummary = `
TARGETED SEARCH RESULTS:
${referenceDocuments
  .map((doc, index) => {
    const relevanceInfo = doc.relevanceScores
      ? ` | RELEVANCE: ${doc.relevanceScores.join(", ")}%`
      : "";
    return `${index + 1}. REQUIREMENT: "${
      doc.userRequirement
    }" | CHUNKS FOUND: ${doc.chunksFound}${relevanceInfo}`;
  })
  .join("\n")}

Total Requirements Processed: ${referenceDocuments.length}
Total Relevant Chunks Found: ${referenceDocuments.reduce(
              (total, doc) => total + (doc.chunksFound || 0),
              0
            )}
Targeted Content Length: ${knowledgeBaseContent.length} characters
Search Method: Semantic similarity search based on user requirements
`;

            // Use the targeted search results directly (already optimized for AI)
            knowledgeBaseContent = searchResult.relevantContent;

            console.log(
              `📄 Structured ${referenceDocuments.length} reference documents (${knowledgeBaseContent.length} characters total)`
            );
          }
        } catch (error) {
          console.error(
            "❌ Error retrieving reference documents from knowledge base:",
            error
          );
          // Continue with generation but log the error
        }
      }

      try {
        const researchPrompt = `You are a senior business analyst creating a comprehensive research foundation for a client proposal. 

CLIENT & PROJECT OVERVIEW:
- Client Name: ${client?.name || "Unknown"}
- Industry: ${client?.industry || "Unknown"}
- Project Description: ${project?.description || ""}
- Project Requirements: ${JSON.stringify(requirements || {})}

${
  knowledgeBaseContent
    ? `
REFERENCE MATERIALS & RESOURCES:
${resourceSummary}

DETAILED REFERENCE CONTENT:
${knowledgeBaseContent}

INSTRUCTIONS FOR REFERENCE MATERIAL USAGE:
- Analyze the reference documents for relevant methodologies, best practices, and technical specifications
- Extract case studies, success metrics, and proven approaches that apply to this client's needs
- Identify industry-specific insights and compliance requirements
- Note any relevant cost models, timelines, or resource requirements from the reference materials
- Use reference data to validate and enhance your recommendations
`
    : ""
}

RESEARCH OBJECTIVES:
1. Conduct thorough analysis of the client's industry and specific needs
2. Identify key challenges and opportunities for this project
3. Research industry best practices and standards
4. Analyze competitive landscape and market trends
5. Determine technical requirements and constraints
6. Assess resource needs and potential risks
${
  knowledgeBaseContent
    ? "7. Leverage reference materials to provide data-driven insights and proven methodologies"
    : ""
}

DELIVERABLE:
Provide a comprehensive research analysis that will serve as the foundation for a professional client proposal. Focus on actionable insights, data-driven recommendations, and industry-specific considerations.

Additional Context: ${aiPrompts?.additionalInstructions || ""}`;

        const researchResponse = await openai.chat.completions.create({
          messages: [{ role: "user", content: researchPrompt }],
          model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
          max_tokens: knowledgeBaseContent ? 3000 : 1500, // Increased for comprehensive analysis
          temperature: 0.7,
        });

        const researchContent =
          researchResponse.choices[0].message.content || "";

        res.write(
          `event: research\ndata: ${JSON.stringify({
            status: "completed",
            content: researchContent,
            referenceDocumentsUsed: referenceDocuments.length,
            resourceSummary: resourceSummary,
          })}\n\n`
        );

        // Summary phase
        res.write(
          `event: summary\ndata: ${JSON.stringify({
            status: "started",
            message: "Creating executive summary for proposal...",
          })}\n\n`
        );

        const summaryPrompt = `Create a compelling executive summary for a professional client proposal.

CLIENT INFORMATION:
- Client: ${client?.name || "Unknown"}
- Industry: ${client?.industry || "Unknown"}
- Project: ${project?.description || ""}

RESEARCH FOUNDATION:
${researchContent}

${
  knowledgeBaseContent
    ? `
SUPPORTING REFERENCE MATERIALS:
${resourceSummary}
`
    : ""
}

EXECUTIVE SUMMARY REQUIREMENTS:
1. Professional tone suitable for C-level executives
2. Clear value proposition and business impact
3. High-level solution overview
4. Key benefits and expected outcomes
5. Strategic alignment with client's business objectives
${
  knowledgeBaseContent
    ? "6. Reference to proven methodologies and best practices from knowledge base"
    : ""
}

Create an executive summary that positions our solution as the optimal choice for achieving the client's objectives.`;

        const summaryResponse = await openai.chat.completions.create({
          messages: [{ role: "user", content: summaryPrompt }],
          model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
          max_tokens: 1500,
          temperature: 0.7,
        });

        const summaryContent = summaryResponse.choices[0].message.content || "";

        res.write(
          `event: summary\ndata: ${JSON.stringify({
            status: "completed",
            content: summaryContent,
          })}\n\n`
        );

        // Generate sections
        const templateSections = template?.sections || [
          "Introduction",
          "Scope",
          "Timeline",
          "Budget",
        ];
        const generatedSections: any[] = [];

        for (const sectionTitle of templateSections) {
          res.write(
            `event: section\ndata: ${JSON.stringify({
              status: "started",
              title: sectionTitle,
            })}\n\n`
          );

          const sectionPrompt = `You are a senior proposal writer creating the "${sectionTitle}" section for a professional client proposal.

CLIENT CONTEXT:
- Client: ${client?.name || "Unknown"}
- Industry: ${client?.industry || "Unknown"}
- Project: ${project?.description || ""}

RESEARCH FOUNDATION:
${researchContent}

EXECUTIVE SUMMARY:
${summaryContent}

${
  knowledgeBaseContent
    ? `
REFERENCE MATERIALS & RESOURCES:
${resourceSummary}

DETAILED REFERENCE CONTENT FOR THIS SECTION:
${knowledgeBaseContent}

REFERENCE MATERIAL INTEGRATION GUIDELINES:
- Incorporate relevant methodologies, frameworks, and best practices from reference documents
- Use specific data points, case studies, and proven approaches where applicable
- Reference industry standards, compliance requirements, and technical specifications
- Include relevant cost models, timelines, and resource allocations from reference materials
- Cite specific examples and success metrics from the knowledge base
- Ensure all reference material usage directly supports the client's objectives
`
    : ""
}

SECTION-SPECIFIC REQUIREMENTS FOR "${sectionTitle}":
${getSectionRequirements(sectionTitle, knowledgeBaseContent.length > 0)}

WRITING GUIDELINES:
- Professional tone appropriate for executive-level stakeholders
- Clear, concise, and actionable content
- Data-driven insights and recommendations
- Industry-specific terminology and considerations
- Compelling value proposition aligned with client needs
- Structured format with clear headings and bullet points where appropriate
${
  knowledgeBaseContent
    ? "- Strategic integration of reference materials to strengthen credibility and demonstrate expertise"
    : ""
}

STYLE PREFERENCES:
${aiPrompts?.styleGuidance || "Professional business proposal style"}

CONTENT FOCUS:
${aiPrompts?.contentFocus || "Client value and business impact"}

ADDITIONAL INSTRUCTIONS:
${aiPrompts?.additionalInstructions || ""}

Create a comprehensive, professional "${sectionTitle}" section that demonstrates deep understanding of the client's needs and positions our solution as the optimal choice.`;

          const sectionResponse = await openai.chat.completions.create({
            messages: [{ role: "user", content: sectionPrompt }],
            model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
            max_tokens: knowledgeBaseContent ? 2000 : 1500, // Increased for comprehensive sections
            temperature: 0.7,
          });

          const sectionContent =
            sectionResponse.choices[0].message.content || "";

          generatedSections.push({
            title: sectionTitle,
            content: sectionContent,
          });

          res.write(
            `event: section\ndata: ${JSON.stringify({
              status: "completed",
              section: {
                title: sectionTitle,
                content: sectionContent,
              },
            })}\n\n`
          );
        }

        // Send completion event
        const documentId = `proposal_${Date.now()}`;

        res.write(
          `event: completed\ndata: ${JSON.stringify({
            proposal: {
              id: documentId,
              type: "client_proposal",
              clientName: client?.name || "Unknown",
              clientIndustry: client?.industry || "Unknown",
              projectTitle: project?.description || "Project Proposal",
              sections: generatedSections,
              createdAt: new Date(),
              updatedAt: new Date(),
              summary: summaryContent,
              research: researchContent,
              metadata: {
                generatedBy: "AltZero Proposal Generator",
                version: "1.0",
                totalSections: generatedSections.length,
                referenceDocumentsUsed: referenceDocuments.length,
                referenceDocuments: referenceDocuments.map((doc) => ({
                  id: doc.id,
                  title: doc.title,
                  type: doc.type,
                  size: doc.size,
                  userRequirement: doc.userRequirement || null,
                })),
                documentsWithRequirements: referenceDocuments.filter(
                  (doc) => doc.userRequirement
                ).length,
                hasKnowledgeBase: knowledgeBaseContent.length > 0,
                knowledgeBaseContentLength: knowledgeBaseContent.length,
                proposalQuality:
                  knowledgeBaseContent.length > 0
                    ? referenceDocuments.filter((doc) => doc.userRequirement)
                        .length > 0
                      ? "Enhanced with Targeted Reference Materials"
                      : "Enhanced with Reference Materials"
                    : "Standard",
                aiModel: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
                generationTimestamp: new Date().toISOString(),
              },
            },
          })}\n\n`
        );
      } catch (error: any) {
        console.error("Error in generation:", error);
        res.write(
          `event: error\ndata: ${JSON.stringify({
            error: "Error generating content",
            details: error.message || "Unknown error",
          })}\n\n`
        );
      }

      // End stream
      res.write(
        `event: end\ndata: ${JSON.stringify({
          message: "Stream complete",
        })}\n\n`
      );

      res.end();
    } catch (error: any) {
      console.error("Error in streaming:", error);
      if (!res.headersSent) {
        res.status(500).json({
          error: "Failed to start document generation",
          details: error.message,
        });
      }
    }
  }
);

// =============================================
// LANGGRAPH WORKFLOW ROUTES
// =============================================

// Mount LangGraph routes under /langgraph prefix
router.use("/langgraph", langgraphRoutes);

console.log("🚀 LangGraph routes mounted at /api/scopingai/langgraph");

export default router;
