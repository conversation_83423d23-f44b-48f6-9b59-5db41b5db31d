import { Link } from 'react-router-dom';
import { useState } from 'react';
import Layout from '../components/Layout';
import { FilePlus, Users, MessageSquare, FileText, LayoutGrid, RefreshCw } from 'lucide-react';

const AIApps = () => {
  const [showScopingMenu, setShowScopingMenu] = useState(false);
  
  const apps = [
    {
      id: 1,
      name: "Proposal Generator",
      description: "Generate professional proposals with AI assistance",
      credits: 50,
      free: true,
      features: [
        "Customizable templates",
        "Cost estimation tools",
        "Export to PDF/DOCX"
      ],
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      link: "/proposal-generator",
      color: "indigo",
      bgColor: "#eef2ff", // indigo-50
      primaryColor: "#6366f1", // indigo-500
      lightColor: "#e0e7ff", // indigo-100
      darkColor: "#4f46e5", // indigo-600
      hoverColor: "#4338ca" // indigo-700
    },
    {
      id: 2,
      name: "RAG Chatbot",
      description: "Chat with your documents using AI",
      credits: 100,
      free: true,
      features: [
        "Document indexing",
        "Semantic search",
        "Cited responses"
      ],
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
      ),
      link: "/rag-chat",
      color: "blue",
      bgColor: "#eff6ff", // blue-50
      primaryColor: "#3b82f6", // blue-500
      lightColor: "#dbeafe", // blue-100
      darkColor: "#2563eb", // blue-600
      hoverColor: "#1d4ed8" // blue-700
    },
    {
      id: 3,
      name: "AI Scoping",
      description: "Create complete scoping documents with AI (standard workflow)",
      credits: 75,
      free: true,
      features: [
        "Requirements analysis",
        "Risk assessment",
        "Timeline generation"
      ],
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
      link: "/ai-scoping",
      color: "emerald",
      bgColor: "#ecfdf5", // emerald-50
      primaryColor: "#10b981", // emerald-500
      lightColor: "#d1fae5", // emerald-100
      darkColor: "#059669", // emerald-600
      hoverColor: "#047857" // emerald-700
    },
    {
      id: 4,
      name: "Progressive Scoping",
      description: "Create progressive scoping documents with section-by-section AI generation",
      credits: 150,
      free: true,
      features: [
        "Section-by-section editing",
        "Incremental updates",
        "Collaborative tools"
      ],
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
      ),
      link: "/scoping",
      color: "teal",
      bgColor: "#f0fdfa", // teal-50
      primaryColor: "#14b8a6", // teal-500
      lightColor: "#ccfbf1", // teal-100
      darkColor: "#0d9488", // teal-600
      hoverColor: "#0f766e" // teal-700
    },
    {
      id: 5,
      name: "AI Deck",
      description: "Create presentations with AI assistance",
      credits: 60,
      free: true,
      features: [
        "Slide templates",
        "Smart layout tools",
        "Content suggestions"
      ],
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
        </svg>
      ),
      link: "/ai-deck",
      color: "green",
      bgColor: "#f0fdf4", // green-50
      primaryColor: "#22c55e", // green-500
      lightColor: "#dcfce7", // green-100
      darkColor: "#16a34a", // green-600
      hoverColor: "#15803d" // green-700
    }
  ];

  const scopingTools = [
    {
      name: "Create Document",
      description: "Create a new scoping document from templates",
      link: "/scoping/create",
      icon: <FilePlus className="w-6 h-6" />
    },
    {
      name: "Clients Information",
      description: "Add and manage client information",
      link: "/scoping/clients",
      icon: <Users className="w-6 h-6" />
    },
    {
      name: "Manage Prompt Templates",
      description: "Create and edit AI prompt templates for document generation",
      link: "/scoping/templates",
      icon: <MessageSquare className="w-6 h-6" />
    },
    {
      name: "Manage Requirements",
      description: "Create and edit scope templates with section definitions",
      link: "/scoping/scope-templates",
      icon: <FileText className="w-6 h-6" />
    },
    {
      name: "Manage Sections",
      description: "Create and edit individual section templates",
      link: "/scoping/sections",
      icon: <LayoutGrid className="w-6 h-6" />
    },
    {
      name: "Progressive Generation",
      description: "Generate scoping documents section-by-section with AI",
      link: "/scoping/progressive",
      icon: <RefreshCw className="w-6 h-6" />
    }
  ];

  return (
    <Layout>
      <div className="p-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">AI Apps</h1>
          <p className="mt-2 text-gray-600">Select an AI-powered application to get started</p>
        </div>

        {/* Apps Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {apps.map((app) => (
            <Link
              key={app.id}
              to={app.link}
              className="group block p-5 rounded-xl shadow-lg border border-gray-100 
              hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden h-full"
              style={{ 
                background: `linear-gradient(to bottom right, white, ${app.bgColor})` 
              }}
            >
              {/* <div 
                className="absolute top-0 right-0 w-24 h-24 rounded-full -mr-8 -mt-8 z-0 opacity-10"
                style={{ backgroundColor: app.primaryColor }}
              ></div> */}
              <div className="relative z-10 flex flex-col justify-between h-full">
                <div className="">
                  <div 
                    className="w-12 h-12 rounded-xl flex items-center justify-center mb-3 shadow-sm transition-colors duration-300"
                    style={{ 
                      backgroundColor: app.lightColor,
                    }}
                  >
                    <div style={{ color: app.darkColor }}>
                      {app.icon}
                    </div>
                  </div>
                  <h3 className="text-base font-semibold text-gray-900 mb-1">{app.name}</h3>
                  <p className="text-xs text-gray-600 mb-3">{app.description}</p>
                  
                  {/* Bullet Points */}
                  <ul className="space-y-1 mb-3">
                    {app.features.map((feature, index) => (
                      <li key={index} className="flex items-start text-xs">
                        <span style={{ color: app.darkColor }} className="inline-block mr-1.5 mt-0.5">•</span>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="flex flex-col  pt-2 border-t border-gray-200">
                  
                  {/* FREE tag */}
                  {/* <div 
                    className="text-[10px] font-medium mb-1 self-start text-gray-500"
                  >
                    FREE
                  </div> */}
                  <div className="flex justify-between items-center">
                  
                    <div className="flex items-center text-xs font-medium" style={{ color: app.darkColor }}>
                      <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {app.credits}
                    </div>
                    <div 
                      className="text-xs py-1 px-2 rounded-full"
                      style={{ 
                        backgroundColor: app.lightColor,
                        color: app.darkColor
                      }}
                    >
                      Open App
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Scoping Tools Section */}
        <div className="mt-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Progressive Scoping Tools</h2>
            <button 
              onClick={() => setShowScopingMenu(!showScopingMenu)} 
              className="text-teal-600 hover:text-teal-800 flex items-center"
            >
              {showScopingMenu ? "Hide Tools" : "Show All Tools"}
              <svg 
                className={`ml-1 w-5 h-5 transform transition-transform ${showScopingMenu ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
          
          {showScopingMenu && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {scopingTools.map((tool, index) => (
                <Link
                  key={index}
                  to={tool.link}
                  className="flex items-start p-4 bg-white rounded-lg border border-gray-200 hover:border-teal-400 hover:shadow-md transition-all"
                >
                  <div className="flex-shrink-0 w-8 h-8 bg-teal-100 rounded-md flex items-center justify-center">
                    <div className="text-teal-600">
                      {tool.icon}
                    </div>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900">{tool.name}</h3>
                    <p className="mt-1 text-xs text-gray-500">{tool.description}</p>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* Quick Start Guide */}
        <div className="mt-6 bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Start Guide</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium">1</span>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-900">Select an App</h3>
                <p className="mt-1 text-sm text-gray-500">Choose the AI app that matches your needs</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium">2</span>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-900">Input Requirements</h3>
                <p className="mt-1 text-sm text-gray-500">Provide the necessary information</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium">3</span>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-900">Generate & Review</h3>
                <p className="mt-1 text-sm text-gray-500">Get AI-generated results and refine them</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AIApps; 