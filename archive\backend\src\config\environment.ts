import dotenv from "dotenv";
import path from "path";

dotenv.config();

export const environment = {
  // API Keys
  openRouterApiKey: process.env.OPENROUTER_API_KEY || "",
  pineconeApiKey: process.env.PINECONE_API_KEY || "",
  cohereApiKey: process.env.COHERE_API_KEY,

  // Server Configuration
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || "development",

  // Vector Store Configuration
  pineconeEnvironment: process.env.PINECONE_ENVIRONMENT || "",
  pineconeIndexName: process.env.PINECONE_INDEX_NAME,
  pineconeNamespace: process.env.PINECONE_NAMESPACE,

  // OpenRouter Configuration
  openRouterBaseUrl:
    process.env.OPENROUTER_URL || "https://openrouter.ai/api/v1",
  openRouterModel: process.env.OPENROUTER_MODEL || "openai/gpt-4-turbo-preview",

  // AI Model Configuration
  useOpenRouter: true,
  geminiModel: process.env.GEMINI_MODEL || "google/gemini-pro",
  openAIModel: process.env.OPENAI_MODEL || "gpt-3.5-turbo",

  // Document Processing
  chunkSize: parseInt(process.env.CHUNK_SIZE || "512", 10),
  chunkOverlap: parseInt(process.env.CHUNK_OVERLAP || "20", 10),
  maxTokens: parseInt(process.env.MAX_TOKENS || "4096", 10),

  // Cache Configuration
  cacheDir: path.resolve(process.env.CACHE_DIR || "./cache"),
  documentsDir: path.resolve(process.env.DOCUMENTS_DIR || "./documents"),

  // Auth settings
  apiKey: process.env.API_KEY || "scopingai",

  // Session settings
  sessionSecret: process.env.SESSION_SECRET || "scopingai-secret",

  // Database settings
  mongoUri: process.env.MONGO_URI || "mongodb://localhost:27017/scopingai",
} as const;

// Validate required environment variables
const requiredEnvVars = [
  "OPENROUTER_API_KEY",
  "PINECONE_API_KEY",
  "PINECONE_ENVIRONMENT",
  "PINECONE_INDEX_NAME",
  "PINECONE_NAMESPACE",
] as const;

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}
