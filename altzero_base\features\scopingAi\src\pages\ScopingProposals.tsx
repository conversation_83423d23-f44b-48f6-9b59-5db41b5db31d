import { useState, useEffect, useCallback } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Plus,
  FileEdit,
  Filter,
  Search,
  Loader2,
  Clock,
  Calendar,
  User,
  MoreHorizontal,
  Check,
  Trash2,
  Eye,
  Download,
  Grid3X3,
  List,
  Share2,
  <PERSON>,
  User<PERSON>he<PERSON>,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { Button } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../base/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../../base/components/ui/dropdown-menu";
import { Badge } from "../../../../base/components/ui/badge";
import { supabase } from "../lib/supabase";
import { useToast } from "../../../../base/hooks/use-toast";
import { useUser } from "../../../../base/contextapi/UserContext";
import { getSharedDocuments } from "../services/documentSharingService";
import ScopingAILayout from "../components/ScopingAILayout";

interface ScopingDocument {
  id: string;
  title: string;
  status: string;
  client_id?: string;
  created_at: string;
  updated_at: string;
  client_name?: string;
  // Shared document specific fields
  is_shared?: boolean;
  shared_by_name?: string;
  permission_level?: string;
  shared_at?: string;
  expires_at?: string;
  share_source?: "direct" | "team" | "organisation";
  share_source_name?: string;
  entity_type?: string;
}

export default function ScopingProposals() {
  const navigate = useNavigate();
  const { user, isLoading: userIsLoading } = useUser();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [documentTypeFilter, setDocumentTypeFilter] = useState("all"); // New filter for owned vs shared
  const [proposals, setProposals] = useState<ScopingDocument[]>([]);
  const [sharedProposals, setSharedProposals] = useState<ScopingDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  const { toast } = useToast();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userIsLoading && !user) {
      navigate("/login");
    }
  }, [user, userIsLoading, navigate]);

  // Fetch documents from Supabase - both owned and shared
  useEffect(() => {
    const fetchDocuments = async () => {
      // Skip if still loading auth or no user
      if (userIsLoading || !user) {
        return;
      }

      // Only load once to prevent continuous loading loops
      if (hasLoadedOnce) {
        return;
      }

      setIsLoading(true);

      try {
        // Use the user ID from @base context instead of direct supabase call
        const userId = user.id;
        if (!userId) {
          console.error("No user ID found");
          return;
        }

        // Fetch owned documents
        const { data: ownedData, error: ownedError } = await supabase
          .from("scopingai_documents")
          .select(
            `
            id,
            title,
            created_at,
            updated_at,
            status,
            proposalFilePath,
            client_id,
            scopingai_clients (
              name
            )
          `
          )
          .eq("user_id", userId)
          .order("created_at", { ascending: false });

        if (ownedError) {
          console.error("Error fetching owned documents:", ownedError);
          toast({
            title: "Error",
            description: "Failed to load your proposals",
            variant: "destructive",
          });
        }

        // Fetch shared documents
        const sharedData = await getSharedDocuments();

        // Transform owned documents
        const transformedOwned = (ownedData || []).map((doc: any) => ({
          id: doc.id,
          title: doc.title,
          status: doc.status || "draft",
          client_id: doc.client_id,
          created_at: doc.created_at,
          updated_at: doc.updated_at,
          client_name: doc.scopingai_clients?.name || "Unknown Client",
          is_shared: false,
        }));

        // Get owned document IDs to prevent duplicates
        const ownedDocumentIds = new Set(
          (ownedData || []).map((doc: any) => doc.id)
        );

        // Transform shared documents, excluding ones the user owns
        const transformedShared = sharedData
          .filter((doc: any) => !ownedDocumentIds.has(doc.document_id)) // Exclude owned documents
          .map((doc: any) => ({
            id: doc.document_id,
            title: doc.title,
            status: doc.status || "draft",
            client_name: doc.client_name || "Unknown Client",
            created_at: doc.shared_at, // Use shared date for sorting
            updated_at: doc.shared_at,
            is_shared: true,
            shared_by_name: doc.shared_by_name,
            permission_level: doc.permission_level,
            shared_at: doc.shared_at,
            expires_at: doc.expires_at,
            share_source: doc.share_source,
            share_source_name: doc.share_source_name,
            entity_type: doc.entity_type,
          }));

        setProposals(transformedOwned);
        setSharedProposals(transformedShared);
        setHasLoadedOnce(true);
      } catch (error) {
        console.error("Error in fetchDocuments:", error);
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, [user, userIsLoading, hasLoadedOnce]); // Simplified dependencies

  // Combine and filter documents based on search query, status, and document type
  const allDocuments = [...proposals, ...sharedProposals];
  const filteredProposals = allDocuments.filter((proposal) => {
    const matchesSearch =
      proposal.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      proposal.client_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (proposal.shared_by_name &&
        proposal.shared_by_name
          .toLowerCase()
          .includes(searchQuery.toLowerCase())) ||
      (proposal.share_source_name &&
        proposal.share_source_name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()));

    const matchesStatus =
      statusFilter === "all" ||
      proposal.status.toLowerCase() === statusFilter.toLowerCase();

    const matchesDocumentType =
      documentTypeFilter === "all" ||
      (documentTypeFilter === "owned" && !proposal.is_shared) ||
      (documentTypeFilter === "shared" && proposal.is_shared);

    return matchesSearch && matchesStatus && matchesDocumentType;
  });

  // Sort by created date (or shared date for shared documents)
  const sortedProposals = filteredProposals.sort(
    (a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get permission level display
  const getPermissionDisplay = (level?: string) => {
    if (!level) return "";
    switch (level) {
      case "view":
        return "View Only";
      case "edit":
        return "Can Edit";
      default:
        return level;
    }
  };

  // Get permission color
  const getPermissionColor = (level?: string) => {
    switch (level) {
      case "view":
        return "bg-blue-100 text-blue-800";
      case "edit":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get share source icon
  const getShareSourceIcon = (source?: string) => {
    switch (source) {
      case "direct":
        return <UserCheck className="h-3 w-3" />;
      case "team":
        return <Users className="h-3 w-3" />;
      case "organisation":
        return <Users className="h-3 w-3" />;
      default:
        return <Share2 className="h-3 w-3" />;
    }
  };

  // Get share source display text
  const getShareSourceDisplay = (source?: string, sourceName?: string) => {
    switch (source) {
      case "direct":
        return "Direct Share";
      case "team":
        return `Team: ${sourceName || "Unknown"}`;
      case "organisation":
        return `Org: ${sourceName || "Unknown"}`;
      default:
        return "Shared";
    }
  };

  // Delete a document (only for owned documents)
  const handleDeleteDocument = async (id: string, isShared: boolean) => {
    if (isShared) {
      toast({
        title: "Cannot Delete",
        description:
          "You cannot delete shared documents. Contact the owner to revoke access.",
        variant: "destructive",
      });
      return;
    }

    try {
      // First ask for confirmation
      if (!confirm("Are you sure you want to delete this document?")) {
        return;
      }

      const { error } = await supabase
        .from("scopingai_documents")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Document deleted successfully",
      });

      // Remove the deleted document from the state
      setProposals((prev) => prev.filter((doc) => doc.id !== id));
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Error",
        description: "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  // Show loading state while checking authentication
  if (userIsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <ScopingAILayout>
      <div className="container mx-auto px-6 py-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Scoping Proposals</h1>
              <p className="text-muted-foreground mt-1">
                Manage all your scoping documents and shared proposals in one
                place
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
              <Link to="/scopingai/documents/new">
                <Button className="gap-2 ml-4">
                  <Plus size={16} />
                  New Proposal
                </Button>
              </Link>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search proposals, clients, or shared by..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <div className="w-full sm:w-48">
                <Select
                  value={documentTypeFilter}
                  onValueChange={setDocumentTypeFilter}
                >
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <SelectValue placeholder="Document Type" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Documents</SelectItem>
                    <SelectItem value="owned">My Documents</SelectItem>
                    <SelectItem value="shared">Shared with Me</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      <SelectValue placeholder="Filter by Status" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="created">Created</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Document Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <FileEdit className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium">My Documents</p>
                    <p className="text-2xl font-bold">{proposals.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Share2 className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Shared with Me</p>
                    <p className="text-2xl font-bold">
                      {sharedProposals.length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium">Total Documents</p>
                    <p className="text-2xl font-bold">{allDocuments.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Document List */}
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading documents...</span>
            </div>
          ) : sortedProposals.length === 0 ? (
            <Card className="w-full">
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-10 space-y-4">
                  <FileEdit size={48} className="text-muted-foreground" />
                  <h3 className="text-lg font-medium">No proposals found</h3>
                  <p className="text-muted-foreground max-w-md">
                    {searchQuery ||
                    statusFilter !== "all" ||
                    documentTypeFilter !== "all"
                      ? "Try adjusting your search or filter to find what you're looking for."
                      : "Get started by creating your first scoping proposal."}
                  </p>
                  <Link to="/scoping-proposals/new">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Proposal
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ) : viewMode === "grid" ? (
            // Grid View
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedProposals.map((proposal) => (
                <Card
                  key={proposal.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg flex items-center gap-2">
                          {proposal.title}
                          {proposal.is_shared && (
                            <Share2 className="h-4 w-4 text-blue-500" />
                          )}
                        </CardTitle>
                        {proposal.is_shared && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Shared by {proposal.shared_by_name}
                          </p>
                        )}
                        {proposal.is_shared && proposal.share_source && (
                          <div className="flex items-center gap-1 text-xs text-blue-600 mt-1">
                            {getShareSourceIcon(proposal.share_source)}
                            {getShareSourceDisplay(
                              proposal.share_source,
                              proposal.share_source_name
                            )}
                          </div>
                        )}
                      </div>
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          proposal.status === "created"
                            ? "bg-green-100 text-green-800"
                            : proposal.status === "draft"
                            ? "bg-amber-100 text-amber-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {proposal.status === "created" && (
                          <Check className="mr-1 h-3 w-3 inline" />
                        )}
                        {proposal.status.charAt(0).toUpperCase() +
                          proposal.status.slice(1)}
                      </div>
                    </div>
                    <CardDescription>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {proposal.client_name}
                      </div>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {proposal.is_shared ? "Shared" : "Created"}:{" "}
                        {formatDate(proposal.created_at)}
                      </div>
                      {proposal.is_shared && proposal.permission_level && (
                        <Badge
                          className={`text-xs ${getPermissionColor(
                            proposal.permission_level
                          )}`}
                        >
                          <UserCheck className="h-3 w-3 mr-1" />
                          {getPermissionDisplay(proposal.permission_level)}
                        </Badge>
                      )}
                      {proposal.expires_at && (
                        <div className="text-xs text-orange-600">
                          Expires: {formatDate(proposal.expires_at)}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Link
                      to={`/scopingai/documents/generated?id=${proposal.id}`}
                    >
                      <Button variant="outline" size="sm">
                        <Eye className="mr-1 h-3 w-3" />
                        View
                      </Button>
                    </Link>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link
                            to={`/scopingai/documents/generated?id=${proposal.id}`}
                          >
                            <FileEdit className="mr-2 h-4 w-4" />
                            {proposal.is_shared &&
                            proposal.permission_level === "view"
                              ? "View"
                              : "Edit"}
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </DropdownMenuItem>
                        {!proposal.is_shared && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() =>
                                handleDeleteDocument(
                                  proposal.id,
                                  proposal.is_shared || false
                                )
                              }
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            // List View
            <div className="overflow-hidden rounded-md border">
              <table className="w-full border-collapse bg-white text-sm">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="px-4 py-3 text-left font-medium">Title</th>
                    <th className="px-4 py-3 text-left font-medium">Client</th>
                    <th className="px-4 py-3 text-left font-medium">Status</th>
                    <th className="px-4 py-3 text-left font-medium">Type</th>
                    <th className="px-4 py-3 text-left font-medium">Date</th>
                    <th className="px-4 py-3 text-right font-medium">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedProposals.map((doc) => (
                    <tr key={doc.id} className="border-b hover:bg-muted/50">
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{doc.title}</span>
                          {doc.is_shared && (
                            <Share2 className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {doc.client_name}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                            doc.status === "created"
                              ? "bg-green-100 text-green-800"
                              : doc.status === "draft"
                              ? "bg-amber-100 text-amber-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {doc.status === "created" && (
                            <Check className="mr-1 h-3 w-3" />
                          )}
                          {doc.status.charAt(0).toUpperCase() +
                            doc.status.slice(1)}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        {doc.is_shared ? (
                          <div className="space-y-1">
                            <Badge
                              className={`text-xs ${getPermissionColor(
                                doc.permission_level
                              )}`}
                            >
                              <UserCheck className="h-3 w-3 mr-1" />
                              {getPermissionDisplay(doc.permission_level)}
                            </Badge>
                            <div className="text-xs text-muted-foreground">
                              by {doc.shared_by_name}
                            </div>
                            {doc.share_source && (
                              <div className="flex items-center gap-1 text-xs text-blue-600">
                                {getShareSourceIcon(doc.share_source)}
                                {getShareSourceDisplay(
                                  doc.share_source,
                                  doc.share_source_name
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <Badge className="text-xs bg-primary/10 text-primary">
                            <FileEdit className="h-3 w-3 mr-1" />
                            Owner
                          </Badge>
                        )}
                      </td>
                      <td className="px-4 py-3 text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <div>
                            <div>{formatDate(doc.created_at)}</div>
                            {doc.expires_at && (
                              <div className="text-xs text-orange-600">
                                Expires: {formatDate(doc.expires_at)}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link
                                to={`/scopingai/documents/generated?id=${doc.id}`}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </Link>
                            </DropdownMenuItem>
                            {(!doc.is_shared ||
                              (doc.permission_level &&
                                doc.permission_level !== "view")) && (
                              <DropdownMenuItem asChild>
                                <Link
                                  to={`/scopingai/documents/generated?id=${doc.id}`}
                                >
                                  <FileEdit className="mr-2 h-4 w-4" />
                                  Edit
                                </Link>
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            {!doc.is_shared && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteDocument(
                                      doc.id,
                                      doc.is_shared || false
                                    )
                                  }
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </ScopingAILayout>
  );
}
