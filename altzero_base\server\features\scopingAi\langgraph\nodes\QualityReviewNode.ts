// =====================================================
// QUALITY REVIEW NODE - SCOPINGAI LANGGRAPH
// =====================================================

import { BaseNode, QualityReviewNodeResult } from "../types/NodeTypes";
import {
  ScopingAiWorkflowState,
  WorkflowContext,
} from "../types/WorkflowState";

export class QualityReviewNode implements BaseNode {
  name = "quality_review";
  description =
    "Reviews and validates the quality of generated proposal content";

  async execute(
    context: WorkflowContext
  ): Promise<Partial<ScopingAiWorkflowState>> {
    const { state, tools, logger } = context;

    logger.info("Starting quality review", {
      workflow_id: state.workflow_id,
      sections_to_review: state.proposal_sections?.length || 0,
      quality_threshold: state.config.quality_threshold,
    });

    try {
      const startTime = Date.now();

      // Perform comprehensive quality review
      const reviewResult = await this.performQualityReview(
        state,
        tools,
        logger
      );

      const processingTime = Date.now() - startTime;

      logger.info("Quality review completed", {
        overall_score: reviewResult.overall_quality_score,
        passed_threshold: reviewResult.passed_quality_threshold,
        improvement_suggestions: reviewResult.improvement_suggestions.length,
      });

      return {
        current_step: "quality_review_completed",
        progress: 95,
        processing_time: (state.processing_time || 0) + processingTime,
        api_calls_made: [
          ...(state.api_calls_made || []),
          {
            provider: "ai_quality",
            endpoint: "quality_assessment",
            calls_made: (state.proposal_sections?.length || 0) + 1, // Per section + overall
            success_rate: 1.0,
            average_response_time:
              processingTime / ((state.proposal_sections?.length || 0) + 1),
            cost_estimate: 0.15,
            timestamp: new Date().toISOString(),
          },
        ],
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          quality_review: reviewResult,
        },
      };
    } catch (error) {
      logger.error("Quality review failed", error);

      // Continue without quality review rather than failing
      logger.warn("Continuing without quality review");

      return {
        current_step: "quality_review_completed",
        progress: 95,
        warnings: [
          ...(state.warnings || []),
          "Quality review failed - proposal generated without quality validation",
        ],
        last_updated: new Date().toISOString(),
      };
    }
  }

  private async performQualityReview(
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<QualityReviewNodeResult> {
    // Review each section individually
    const sectionScores: Record<string, number> = {};
    const allImprovements: string[] = [];

    if (state.proposal_sections) {
      for (const section of state.proposal_sections) {
        try {
          const sectionReview = await this.reviewSection(
            section,
            state,
            tools,
            logger
          );
          sectionScores[section.title] = sectionReview.score;
          allImprovements.push(...sectionReview.improvements);
        } catch (error) {
          logger.warn(`Failed to review section: ${section.title}`, error);
          sectionScores[section.title] = 70; // Default score
        }
      }
    }

    // Perform overall quality assessment
    const overallMetrics = await this.assessOverallQuality(
      state,
      tools,
      logger
    );

    // Calculate overall quality score
    const sectionScoreValues = Object.values(sectionScores);
    const avgSectionScore =
      sectionScoreValues.length > 0
        ? sectionScoreValues.reduce((sum, score) => sum + score, 0) /
          sectionScoreValues.length
        : 70;

    const overallScore = Math.round(
      (avgSectionScore +
        overallMetrics.consistency +
        overallMetrics.completeness) /
        3
    );

    // Check if quality threshold is met
    const passedThreshold =
      overallScore >= state.config.quality_threshold * 100;

    // Generate improvement suggestions
    const improvementSuggestions = await this.generateImprovementSuggestions(
      state,
      sectionScores,
      overallMetrics,
      tools,
      logger
    );

    return {
      overall_quality_score: overallScore,
      section_scores: sectionScores,
      quality_metrics: {
        content_relevance: overallMetrics.content_relevance,
        technical_accuracy: overallMetrics.technical_accuracy,
        business_value: overallMetrics.business_value,
        readability: overallMetrics.readability,
        completeness: overallMetrics.completeness,
        consistency: overallMetrics.consistency,
      },
      improvement_suggestions: improvementSuggestions,
      passed_quality_threshold: passedThreshold,
    };
  }

  private async reviewSection(
    section: any,
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<{ score: number; improvements: string[] }> {
    const prompt = `Review this ${section.title} section of a business proposal for quality and effectiveness.

SECTION CONTENT:
${section.content}

CLIENT CONTEXT:
- Client: ${state.client?.name}
- Industry: ${state.client?.industry}
- Project: ${state.project?.title}

EVALUATION CRITERIA:
1. Relevance to section purpose (0-100)
2. Clarity and readability (0-100)
3. Professional tone and language (0-100)
4. Specific details vs generic content (0-100)
5. Business value focus (0-100)
6. Logical structure and flow (0-100)

Provide assessment as JSON:
{
  "overall_score": 85,
  "criteria_scores": {
    "relevance": 90,
    "clarity": 85,
    "professionalism": 88,
    "specificity": 80,
    "business_value": 87,
    "structure": 85
  },
  "improvements": [
    "Add more specific metrics or examples",
    "Improve transition between paragraphs"
  ]
}`;

    try {
      const response = await tools.ai.generateStructuredData(prompt, {
        type: "object",
        properties: {
          overall_score: { type: "number" },
          criteria_scores: { type: "object" },
          improvements: { type: "array", items: { type: "string" } },
        },
      });

      return {
        score: response.overall_score || 70,
        improvements: response.improvements || [],
      };
    } catch (error) {
      logger.warn(`Section review failed for ${section.title}`, error);
      return {
        score: 70,
        improvements: [
          `Review ${section.title} section for clarity and specificity`,
        ],
      };
    }
  }

  private async assessOverallQuality(
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<any> {
    // Combine all content for overall assessment
    const allContent = [
      state.executive_summary?.content || "",
      ...(state.proposal_sections?.map((s) => s.content) || []),
    ].join("\n\n");

    const prompt = `Assess the overall quality of this business proposal across multiple dimensions.

FULL PROPOSAL CONTENT:
${allContent.substring(0, 8000)} ${
      allContent.length > 8000 ? "...[truncated]" : ""
    }

CLIENT CONTEXT:
- Client: ${state.client?.name}
- Industry: ${state.client?.industry}
- Project: ${state.project?.title}

ASSESSMENT DIMENSIONS (score 0-100 each):
1. Content Relevance: How well does content address client needs?
2. Technical Accuracy: Are technical details accurate and appropriate?
3. Business Value: Does proposal clearly articulate business benefits?
4. Readability: Is content clear and easy to understand?
5. Completeness: Does proposal cover all necessary aspects?
6. Consistency: Is tone, style, and messaging consistent throughout?

Provide assessment as JSON:
{
  "content_relevance": 85,
  "technical_accuracy": 90,
  "business_value": 88,
  "readability": 82,
  "completeness": 87,
  "consistency": 85
}`;

    try {
      const response = await tools.ai.generateStructuredData(prompt, {
        type: "object",
        properties: {
          content_relevance: { type: "number" },
          technical_accuracy: { type: "number" },
          business_value: { type: "number" },
          readability: { type: "number" },
          completeness: { type: "number" },
          consistency: { type: "number" },
        },
      });

      return response;
    } catch (error) {
      logger.warn("Overall quality assessment failed", error);
      return {
        content_relevance: 75,
        technical_accuracy: 75,
        business_value: 75,
        readability: 75,
        completeness: 75,
        consistency: 75,
      };
    }
  }

  private async generateImprovementSuggestions(
    state: ScopingAiWorkflowState,
    sectionScores: Record<string, number>,
    overallMetrics: any,
    tools: any,
    logger: any
  ): Promise<string[]> {
    // Identify areas needing improvement
    const lowScoringSections = Object.entries(sectionScores)
      .filter(([_, score]) => (score as number) < 75)
      .map(([title, _]) => title);

    const lowMetrics = Object.entries(overallMetrics)
      .filter(([_, score]) => (score as number) < 75)
      .map(([metric, _]) => metric);

    const prompt = `Generate specific improvement suggestions for this business proposal.

QUALITY ISSUES IDENTIFIED:
- Low-scoring sections: ${lowScoringSections.join(", ") || "None"}
- Areas needing improvement: ${lowMetrics.join(", ") || "None"}

OVERALL SCORES:
${Object.entries(overallMetrics)
  .map(([key, value]) => `- ${key}: ${value}`)
  .join("\n")}

CLIENT CONTEXT:
- Client: ${state.client?.name}
- Industry: ${state.client?.industry}
- Project: ${state.project?.title}

Generate 3-7 specific, actionable improvement suggestions that would enhance the proposal's effectiveness and client appeal.

Provide as JSON array of strings:
["suggestion 1", "suggestion 2", ...]`;

    try {
      const response = await tools.ai.generateStructuredData(prompt, {
        type: "array",
        items: { type: "string" },
      });

      return Array.isArray(response) ? response : response.suggestions || [];
    } catch (error) {
      logger.warn("Improvement suggestions generation failed", error);
      return this.getFallbackImprovements(lowScoringSections, lowMetrics);
    }
  }

  private getFallbackImprovements(
    lowScoringSections: string[],
    lowMetrics: string[]
  ): string[] {
    const suggestions: string[] = [];

    if (lowScoringSections.length > 0) {
      suggestions.push(
        `Review and enhance the following sections: ${lowScoringSections.join(
          ", "
        )}`
      );
    }

    if (lowMetrics.includes("content_relevance")) {
      suggestions.push("Add more client-specific details and industry context");
    }

    if (lowMetrics.includes("business_value")) {
      suggestions.push(
        "Strengthen the business value proposition with specific benefits and ROI"
      );
    }

    if (lowMetrics.includes("readability")) {
      suggestions.push(
        "Improve readability with shorter sentences and clearer structure"
      );
    }

    if (lowMetrics.includes("completeness")) {
      suggestions.push(
        "Ensure all proposal sections are comprehensive and complete"
      );
    }

    if (lowMetrics.includes("consistency")) {
      suggestions.push(
        "Review for consistent tone, terminology, and messaging throughout"
      );
    }

    if (suggestions.length === 0) {
      suggestions.push(
        "Consider adding more specific examples and case studies"
      );
      suggestions.push(
        "Enhance visual presentation with charts or diagrams where appropriate"
      );
    }

    return suggestions;
  }

  // Calculate quality score based on multiple factors
  private calculateQualityScore(
    content: string,
    requirements: string[]
  ): number {
    let score = 70; // Base score

    // Length check
    const wordCount = content.split(/\s+/).length;
    if (wordCount < 100) score -= 20;
    if (wordCount > 200 && wordCount < 500) score += 10;

    // Professional language check
    const professionalTerms = [
      "solution",
      "approach",
      "methodology",
      "deliver",
      "ensure",
      "provide",
    ];
    const foundTerms = professionalTerms.filter((term) =>
      content.toLowerCase().includes(term)
    ).length;
    score += foundTerms * 2;

    // Structure check
    if (
      content.includes("•") ||
      content.includes("-") ||
      content.match(/\d+\./)
    ) {
      score += 5;
    }

    // Requirements fulfillment
    const fulfilledRequirements = requirements.filter((req) =>
      content.toLowerCase().includes(req.toLowerCase())
    ).length;
    score += (fulfilledRequirements / requirements.length) * 20;

    return Math.max(50, Math.min(95, score));
  }

  // Validate proposal completeness
  private validateCompleteness(state: ScopingAiWorkflowState): {
    score: number;
    missing: string[];
  } {
    const missing: string[] = [];
    let score = 100;

    if (!state.executive_summary?.content) {
      missing.push("Executive Summary");
      score -= 20;
    }

    if (!state.proposal_sections || state.proposal_sections.length === 0) {
      missing.push("Proposal Sections");
      score -= 30;
    }

    if (!state.research_analysis?.content) {
      missing.push("Research Analysis");
      score -= 15;
    }

    if (!state.client_analysis) {
      missing.push("Client Analysis");
      score -= 10;
    }

    return { score: Math.max(0, score), missing };
  }
}
