import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import { useUser } from '../../../../base/contextapi/UserContext';
import type { PSEOWebsite } from '../../types';
import PSEOLayout from '../../components/PSEOLayout';

interface KeywordResearchResultsProps {}

const KeywordResearchResults: React.FC<KeywordResearchResultsProps> = () => {
  const { websiteId } = useParams<{ websiteId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();
  
  const [website, setWebsite] = useState<PSEOWebsite | null>(null);
  const [allWebsites, setAllWebsites] = useState<PSEOWebsite[]>([]);
  const [keywords, setKeywords] = useState<any[]>([]);
  const [opportunities, setOpportunities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'keywords' | 'opportunities' | 'analytics'>('overview');

  useEffect(() => {
    if (websiteId) {
      loadSingleWebsiteResults();
    } else if (user?.id) {
      loadAllWebsitesResults();
    }
  }, [websiteId, user?.id]);

  const loadSingleWebsiteResults = async () => {
    try {
      setLoading(true);
      
      // Get website info
      const websiteData = await databaseService.getWebsiteById(websiteId!);
      if (!websiteData) {
        throw new Error('Website not found');
      }
      setWebsite(websiteData);
      
      // Get keyword research data
      const keywordsData = await databaseService.getKeywordResearch(websiteId!, {
        limit: 1000
      });
      setKeywords(keywordsData);
      
      // Get keyword opportunities
      const opportunitiesData = await databaseService.getKeywordOpportunities(websiteId!, {
        limit: 100
      });
      setOpportunities(opportunitiesData);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load keyword research results');
    } finally {
      setLoading(false);
    }
  };

  const loadAllWebsitesResults = async () => {
    try {
      setLoading(true);
      
      // Get all clients for this user
      const clients = await databaseService.getClientsByUserId(user!.id);
      
      // Get all websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsitesData = websitesArrays.flat();
      setAllWebsites(allWebsitesData);
      
      if (allWebsitesData.length === 0) {
        setError('No websites found. Please add a website first.');
        return;
      }
      
      // Get keyword data for all websites
      const allKeywords: any[] = [];
      const allOpportunities: any[] = [];
      
      for (const website of allWebsitesData) {
        try {
          const [keywordsData, opportunitiesData] = await Promise.all([
            databaseService.getKeywordResearch(website.id, { limit: 200 }),
            databaseService.getKeywordOpportunities(website.id, { limit: 50 })
          ]);
          
          // Add website info to each item
          allKeywords.push(...keywordsData.map(item => ({ ...item, website_name: website.name, website_url: website.url })));
          allOpportunities.push(...opportunitiesData.map(item => ({ ...item, website_name: website.name, website_url: website.url })));
        } catch (err) {
          console.warn(`Failed to load keyword data for website ${website.name}:`, err);
        }
      }
      
      setKeywords(allKeywords);
      setOpportunities(allOpportunities);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load keyword research results');
    } finally {
      setLoading(false);
    }
  };

  const getIntentColor = (intent: string) => {
    switch (intent) {
      case 'commercial': return 'bg-green-100 text-green-800';
      case 'transactional': return 'bg-purple-100 text-purple-800';
      case 'informational': return 'bg-blue-100 text-blue-800';
      case 'navigational': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty >= 80) return 'bg-red-100 text-red-800';
    if (difficulty >= 60) return 'bg-orange-100 text-orange-800';
    if (difficulty >= 40) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getDifficultyLabel = (difficulty: number) => {
    if (difficulty >= 80) return 'Very Hard';
    if (difficulty >= 60) return 'Hard';
    if (difficulty >= 40) return 'Medium';
    return 'Easy';
  };

  // Calculate stats
  const stats = {
    totalKeywords: keywords.length,
    avgSearchVolume: keywords.length > 0 ? Math.round(keywords.reduce((sum, kw) => sum + (kw.search_volume || 0), 0) / keywords.length) : 0,
    avgDifficulty: keywords.length > 0 ? Math.round(keywords.reduce((sum, kw) => sum + (kw.keyword_difficulty || 0), 0) / keywords.length) : 0,
    highVolumeKeywords: keywords.filter(kw => (kw.search_volume || 0) > 1000).length,
    lowCompetition: keywords.filter(kw => (kw.keyword_difficulty || 0) < 40).length,
    commercialKeywords: keywords.filter(kw => kw.intent === 'commercial' || kw.intent === 'transactional').length,
    intentBreakdown: keywords.reduce((acc, kw) => {
      acc[kw.intent || 'unknown'] = (acc[kw.intent || 'unknown'] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (error) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="bg-destructive/10 border border-destructive/20 rounded-md p-6">
              <h2 className="text-lg font-semibold text-destructive mb-2">Error Loading Results</h2>
              <p className="text-destructive">{error}</p>
              <button
                onClick={() => navigate('/full-site-analysis')}
                className="mt-4 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
              >
                Back to Full Site Analysis
              </button>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              🎯 Keyword Research Results
            </h1>
            {website ? (
              <>
                <p className="text-muted-foreground">
                  {website.name} - {website.domain}
                </p>
                <p className="text-sm text-muted-foreground">
                  {stats.totalKeywords} keywords discovered with {stats.highVolumeKeywords} high-volume opportunities
                </p>
              </>
            ) : (
              <>
                <p className="text-muted-foreground">
                  All Websites ({allWebsites.length} total)
                </p>
                <p className="text-sm text-muted-foreground">
                  {stats.totalKeywords} keywords discovered across all websites with {stats.highVolumeKeywords} high-volume opportunities
                </p>
              </>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => navigate('/full-site-analysis')}
              className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90"
            >
              ← Back to Analysis
            </button>
            <button
              onClick={() => {
                const csvContent = keywords.map(kw => 
                  `"${kw.keyword}","${kw.search_volume || 0}","${kw.keyword_difficulty || 0}","${kw.intent || ''}","${kw.cpc || 0}","${kw.competition || ''}","${kw.data_source || ''}"`
                ).join('\n');
                const header = 'Keyword,Search Volume,Difficulty,Intent,CPC,Competition,Data Source\n';
                const csv = header + csvContent;
                const blob = new Blob([csv], { type: 'text/csv' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `keyword-research-${website?.name || 'results'}.csv`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
            >
              📄 Export CSV
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-border mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'keywords', label: 'All Keywords' },
              { id: 'opportunities', label: 'Opportunities' },
              { id: 'analytics', label: 'Analytics' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-blue-600">{stats.totalKeywords}</div>
                <p className="text-sm text-muted-foreground">Total Keywords</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-green-600">{stats.highVolumeKeywords}</div>
                <p className="text-sm text-muted-foreground">High Volume (1K+)</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-purple-600">{stats.lowCompetition}</div>
                <p className="text-sm text-muted-foreground">Low Competition</p>
              </div>
              <div className="bg-card rounded-lg border p-6">
                <div className="text-2xl font-bold text-orange-600">{stats.commercialKeywords}</div>
                <p className="text-sm text-muted-foreground">Commercial Intent</p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4">📊 Search Volume Distribution</h3>
                <div className="space-y-3">
                  {[
                    { label: '10,000+', min: 10000, color: 'text-red-600' },
                    { label: '1,000-9,999', min: 1000, max: 9999, color: 'text-orange-600' },
                    { label: '100-999', min: 100, max: 999, color: 'text-yellow-600' },
                    { label: '10-99', min: 10, max: 99, color: 'text-blue-600' },
                    { label: '0-9', min: 0, max: 9, color: 'text-gray-600' }
                  ].map(({ label, min, max, color }) => {
                    const count = keywords.filter(kw => {
                      const volume = kw.search_volume || 0;
                      return max ? volume >= min && volume <= max : volume >= min;
                    }).length;
                    
                    return (
                      <div key={label} className="flex justify-between items-center">
                        <span className="text-sm">{label}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${color.replace('text-', 'bg-')}`}
                              style={{ width: `${Math.min(100, (count / stats.totalKeywords) * 200)}%` }}
                            />
                          </div>
                          <span className={`text-sm font-medium ${color}`}>{count}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4">🎯 Search Intent Breakdown</h3>
                <div className="space-y-3">
                  {Object.entries(stats.intentBreakdown).map(([intent, count]) => (
                    <div key={intent} className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs ${getIntentColor(intent)}`}>
                          {intent}
                        </span>
                      </div>
                      <span className="text-sm font-medium">{count as number}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Top Opportunities */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">🚀 Top Keyword Opportunities</h3>
              <div className="space-y-3">
                {keywords
                  .filter(kw => (kw.search_volume || 0) > 500 && (kw.keyword_difficulty || 100) < 60)
                  .sort((a, b) => (b.search_volume || 0) - (a.search_volume || 0))
                  .slice(0, 10)
                  .map((keyword, index) => (
                    <div key={keyword.id} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground">{keyword.keyword}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className={`px-2 py-1 rounded text-xs ${getIntentColor(keyword.intent)}`}>
                            {keyword.intent || 'unknown'}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {keyword.data_source || 'AI Research'}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium text-blue-600">{(keyword.search_volume || 0).toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">volume</div>
                        </div>
                        <div className="text-center">
                          <div className={`font-medium ${getDifficultyColor(keyword.keyword_difficulty || 0).replace('bg-', 'text-').replace('-100', '-600')}`}>
                            {keyword.keyword_difficulty || 0}
                          </div>
                          <div className="text-xs text-muted-foreground">difficulty</div>
                        </div>
                        {keyword.cpc && (
                          <div className="text-center">
                            <div className="font-medium text-green-600">${keyword.cpc.toFixed(2)}</div>
                            <div className="text-xs text-muted-foreground">CPC</div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'keywords' && (
          <div className="space-y-6">
            {/* Search and Filter */}
            <div className="bg-card rounded-lg border p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search keywords..."
                    className="w-full p-2 border rounded-md"
                  />
                </div>
                <select className="p-2 border rounded-md">
                  <option value="">All Intents</option>
                  <option value="commercial">Commercial</option>
                  <option value="transactional">Transactional</option>
                  <option value="informational">Informational</option>
                  <option value="navigational">Navigational</option>
                </select>
                <select className="p-2 border rounded-md">
                  <option value="">All Difficulty</option>
                  <option value="easy">Easy (0-40)</option>
                  <option value="medium">Medium (41-60)</option>
                  <option value="hard">Hard (61-80)</option>
                  <option value="very-hard">Very Hard (81-100)</option>
                </select>
                <select className="p-2 border rounded-md">
                  <option value="">All Volume</option>
                  <option value="high">High (1000+)</option>
                  <option value="medium">Medium (100-999)</option>
                  <option value="low">Low (10-99)</option>
                  <option value="very-low">Very Low (0-9)</option>
                </select>
              </div>
            </div>

            {/* Keywords Table */}
            <div className="bg-card rounded-lg border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-muted">
                    <tr>
                      <th className="text-left p-4 font-medium">Keyword</th>
                      <th className="text-left p-4 font-medium">Search Volume</th>
                      <th className="text-left p-4 font-medium">Difficulty</th>
                      <th className="text-left p-4 font-medium">Intent</th>
                      <th className="text-left p-4 font-medium">CPC</th>
                      <th className="text-left p-4 font-medium">Competition</th>
                      <th className="text-left p-4 font-medium">Source</th>
                    </tr>
                  </thead>
                  <tbody>
                    {keywords.slice(0, 100).map((keyword) => (
                      <tr key={keyword.id} className="border-t">
                        <td className="p-4">
                          <div className="font-medium text-sm">{keyword.keyword}</div>
                          {keyword.ranking_position && (
                            <div className="text-xs text-muted-foreground">
                              Currently ranking #{keyword.ranking_position}
                            </div>
                          )}
                        </td>
                        <td className="p-4 text-sm font-medium">
                          {(keyword.search_volume || 0).toLocaleString()}
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded text-xs ${getDifficultyColor(keyword.keyword_difficulty || 0)}`}>
                              {keyword.keyword_difficulty || 0}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {getDifficultyLabel(keyword.keyword_difficulty || 0)}
                            </span>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded text-xs ${getIntentColor(keyword.intent)}`}>
                            {keyword.intent || 'unknown'}
                          </span>
                        </td>
                        <td className="p-4 text-sm">
                          {keyword.cpc ? `$${keyword.cpc.toFixed(2)}` : '-'}
                        </td>
                        <td className="p-4 text-sm">{keyword.competition || '-'}</td>
                        <td className="p-4">
                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            {keyword.data_source || 'AI Research'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {keywords.length > 100 && (
                <div className="p-4 text-center text-sm text-muted-foreground bg-muted">
                  Showing first 100 keywords. Export CSV for complete data.
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'opportunities' && (
          <div className="space-y-6">
            {/* Opportunity Categories */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* High Volume, Low Competition */}
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4 text-green-700">🎯 Quick Wins</h3>
                <p className="text-sm text-muted-foreground mb-4">High volume, low competition keywords</p>
                <div className="space-y-3">
                  {keywords
                    .filter(kw => (kw.search_volume || 0) > 500 && (kw.keyword_difficulty || 100) < 30)
                    .slice(0, 5)
                    .map((keyword) => (
                      <div key={keyword.id} className="p-3 bg-green-50 border border-green-200 rounded">
                        <div className="font-medium text-sm">{keyword.keyword}</div>
                        <div className="text-xs text-green-600 mt-1">
                          {(keyword.search_volume || 0).toLocaleString()} volume • {keyword.keyword_difficulty || 0} difficulty
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Commercial Intent */}
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4 text-purple-700">💰 Commercial Opportunities</h3>
                <p className="text-sm text-muted-foreground mb-4">Keywords with buying intent</p>
                <div className="space-y-3">
                  {keywords
                    .filter(kw => kw.intent === 'commercial' || kw.intent === 'transactional')
                    .sort((a, b) => (b.search_volume || 0) - (a.search_volume || 0))
                    .slice(0, 5)
                    .map((keyword) => (
                      <div key={keyword.id} className="p-3 bg-purple-50 border border-purple-200 rounded">
                        <div className="font-medium text-sm">{keyword.keyword}</div>
                        <div className="text-xs text-purple-600 mt-1">
                          {(keyword.search_volume || 0).toLocaleString()} volume • {keyword.intent} intent
                          {keyword.cpc && ` • $${keyword.cpc.toFixed(2)} CPC`}
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Long-tail Keywords */}
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4 text-blue-700">📝 Long-tail Opportunities</h3>
                <p className="text-sm text-muted-foreground mb-4">Specific, low competition keywords</p>
                <div className="space-y-3">
                  {keywords
                    .filter(kw => kw.keyword.split(' ').length >= 3 && (kw.keyword_difficulty || 100) < 50)
                    .sort((a, b) => (b.search_volume || 0) - (a.search_volume || 0))
                    .slice(0, 5)
                    .map((keyword) => (
                      <div key={keyword.id} className="p-3 bg-blue-50 border border-blue-200 rounded">
                        <div className="font-medium text-sm">{keyword.keyword}</div>
                        <div className="text-xs text-blue-600 mt-1">
                          {(keyword.search_volume || 0).toLocaleString()} volume • {keyword.keyword.split(' ').length} words
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            {/* Content Gap Analysis */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">🔍 Content Gap Analysis</h3>
              <p className="text-muted-foreground mb-4">
                Keywords where you could create content to capture traffic
              </p>
              <div className="space-y-4">
                {[
                  { category: 'How-to guides', keywords: keywords.filter(kw => kw.keyword.toLowerCase().includes('how to')).length },
                  { category: 'Best practices', keywords: keywords.filter(kw => kw.keyword.toLowerCase().includes('best')).length },
                  { category: 'Comparisons', keywords: keywords.filter(kw => kw.keyword.toLowerCase().includes('vs') || kw.keyword.toLowerCase().includes('versus')).length },
                  { category: 'Reviews', keywords: keywords.filter(kw => kw.keyword.toLowerCase().includes('review')).length }
                ].map(({ category, keywords: count }) => (
                  <div key={category} className="flex items-center justify-between p-3 bg-muted rounded">
                    <span className="font-medium">{category}</span>
                    <span className="text-sm text-muted-foreground">{count} keywords</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Keyword Analytics */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">📈 Keyword Research Analytics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Search Volume Analysis</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total potential traffic:</span>
                      <span className="font-medium">{keywords.reduce((sum, kw) => sum + (kw.search_volume || 0), 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average search volume:</span>
                      <span className="font-medium">{stats.avgSearchVolume.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Highest volume keyword:</span>
                      <span className="font-medium">{Math.max(...keywords.map(kw => kw.search_volume || 0)).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Competition Analysis</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Average difficulty:</span>
                      <span className="font-medium">{stats.avgDifficulty}/100</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Easy targets (0-40):</span>
                      <span className="font-medium text-green-600">{keywords.filter(kw => (kw.keyword_difficulty || 0) <= 40).length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Hard targets (80+):</span>
                      <span className="font-medium text-red-600">{keywords.filter(kw => (kw.keyword_difficulty || 0) >= 80).length}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Monetization Potential</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Commercial keywords:</span>
                      <span className="font-medium">{stats.commercialKeywords}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg CPC:</span>
                      <span className="font-medium">
                        ${keywords.filter(kw => kw.cpc).length > 0 
                          ? (keywords.reduce((sum, kw) => sum + (kw.cpc || 0), 0) / keywords.filter(kw => kw.cpc).length).toFixed(2)
                          : '0.00'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>High CPC (&gt;$2):</span>
                      <span className="font-medium text-green-600">{keywords.filter(kw => (kw.cpc || 0) > 2).length}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Data Sources */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">📊 Data Sources</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(
                  keywords.reduce((acc, kw) => {
                    const source = kw.data_source || 'AI Research';
                    acc[source] = (acc[source] || 0) + 1;
                    return acc;
                  }, {} as Record<string, number>)
                ).map(([source, count]) => (
                  <div key={source} className="text-center p-3 bg-muted rounded">
                    <div className="text-lg font-bold">{count as number}</div>
                    <div className="text-sm text-muted-foreground">{source}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Ranking Analysis */}
            {keywords.some(kw => kw.ranking_position) && (
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4">🏆 Current Ranking Analysis</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded">
                    <span>Keywords ranking in top 10:</span>
                    <span className="font-bold text-green-600">
                      {keywords.filter(kw => kw.ranking_position && kw.ranking_position <= 10).length}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <span>Keywords ranking 11-50:</span>
                    <span className="font-bold text-yellow-600">
                      {keywords.filter(kw => kw.ranking_position && kw.ranking_position > 10 && kw.ranking_position <= 50).length}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-red-50 border border-red-200 rounded">
                    <span>Keywords ranking 50+:</span>
                    <span className="font-bold text-red-600">
                      {keywords.filter(kw => kw.ranking_position && kw.ranking_position > 50).length}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default KeywordResearchResults; 