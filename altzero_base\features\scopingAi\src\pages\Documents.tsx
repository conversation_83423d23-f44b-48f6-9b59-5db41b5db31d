import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";

export default function DocumentsRedirect() {
  const navigate = useNavigate();

  useEffect(() => {
    navigate("/scopingai/knowledge-base/documents");
  }, [navigate]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <p className="mt-4 text-muted-foreground">
        Redirecting to Document Library...
      </p>
    </div>
  );
}
