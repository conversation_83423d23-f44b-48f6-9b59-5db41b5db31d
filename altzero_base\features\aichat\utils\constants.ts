/**
 * CopilotKit Module Constants
 * Following AltZero platform .cursorrules for avoiding hardcoded values
 */

// API Configuration
export const COPILOT_API_CONFIG = {
  BASE_URL: "/api/copilotkit",
  OPENAI_URL: "/api/copilotkit/openai",
  MCP_URL: "/api/copilotkit/mcp",
  TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

// MCP Configuration
export const MCP_CONFIG = {
  DEFAULT_TIMEOUT: 10000, // 10 seconds
  MAX_RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 2000, // 2 seconds
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
  CONNECTION_TIMEOUT: 5000, // 5 seconds
} as const;

// Supabase MCP Configuration
export const SUPABASE_MCP_CONFIG = {
  SCHEMA: "public",
  ENABLE_RLS: true,
  MAX_QUERY_TIMEOUT: 15000, // 15 seconds
  MAX_RESULTS: 1000,
  ENABLE_CACHING: true,
  CACHE_TTL: 300000, // 5 minutes
} as const;

// Query Parser Configuration
export const QUERY_PARSER_CONFIG = {
  ENTITY_PREFIX: "@",
  MAX_QUERY_LENGTH: 1000,
  SUPPORTED_ENTITIES: [
    "customer",
    "customers", 
    "user",
    "users",
    "profile",
    "profiles",
    "order",
    "orders",
    "document",
    "documents",
    "team",
    "teams",
    "organization",
    "organizations",
    "subscription",
    "subscriptions",
  ] as const,
  TIME_KEYWORDS: [
    "today",
    "yesterday", 
    "week",
    "month",
    "year",
    "last week",
    "last month",
    "this week",
    "this month",
    "this year",
  ] as const,
  AGGREGATION_FUNCTIONS: [
    "count",
    "sum", 
    "avg",
    "average",
    "min",
    "max",
    "total",
  ] as const,
} as const;

// Database Entity Mappings
export const DATABASE_ENTITY_MAPPINGS = {
  customer: "profiles",
  customers: "profiles",
  user: "profiles", 
  users: "profiles",
  profile: "profiles",
  profiles: "profiles",
  order: "orders",
  orders: "orders",
  document: "documents",
  documents: "documents",
  team: "teams",
  teams: "teams",
  organization: "organizations",
  organizations: "organizations",
  subscription: "subscriptions",
  subscriptions: "subscriptions",
} as const;

// CopilotKit Action Names
export const COPILOT_ACTIONS = {
  // Database Operations
  QUERY_DATABASE: "queryDatabase",
  EXPORT_DATA: "exportData",
  ANALYZE_DATA: "analyzeData",
  
  // Navigation
  NAVIGATE_TO_PAGE: "navigateToPage",
  OPEN_MODAL: "openModal",
  CLOSE_DIALOG: "closeDialog",
  GO_BACK: "goBack",
  
  // Knowledge Base
  UPLOAD_DOCUMENT: "uploadDocument",
  SEARCH_DOCUMENTS: "searchDocuments",
  DELETE_DOCUMENT: "deleteDocument",
  CHAT_WITH_DOCUMENTS: "chatWithDocuments",
  
  // Team Management
  CREATE_TEAM: "createTeam",
  INVITE_MEMBER: "inviteMember",
  UPDATE_TEAM: "updateTeam",
  DELETE_TEAM: "deleteTeam",
  
  // Organization Management
  CREATE_ORGANIZATION: "createOrganization",
  UPDATE_ORGANIZATION: "updateOrganization",
  MANAGE_SUBSCRIPTION: "manageSubscription",
  
  // Component Interaction
  UPDATE_COMPONENT_STATE: "updateComponentState",
  TRIGGER_COMPONENT_ACTION: "triggerComponentAction",
  GET_COMPONENT_STATE: "getComponentState",
  
  // Workflow Automation
  RUN_SEO_AUDIT: "runSEOAudit",
  GENERATE_REPORT: "generateReport",
  SCHEDULE_TASK: "scheduleTask",
  EXECUTE_WORKFLOW: "executeWorkflow",
} as const;

// Component Registry Categories
export const COMPONENT_CATEGORIES = {
  NAVIGATION: "navigation",
  DATA: "data", 
  UI: "ui",
  WORKFLOW: "workflow",
  INTEGRATION: "integration",
} as const;

// Available Components for Interaction
export const AVAILABLE_COMPONENTS = {
  DASHBOARD: "dashboard",
  KNOWLEDGE_BASE: "knowledgeBase",
  CHAT: "chat",
  PROFILE: "profile",
  TEAMS: "teams",
  ORGANIZATIONS: "organizations",
  SETTINGS: "settings",
  PSEO: "pseo",
  SUBSCRIPTION: "subscription",
} as const;

// Message Types
export const MESSAGE_TYPES = {
  TEXT: "text",
  ACTION_RESULT: "action_result",
  QUERY_RESULT: "query_result", 
  COMPONENT_INTERACTION: "component_interaction",
  ERROR: "error",
  SYSTEM: "system",
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  INFO: "info",
  SUCCESS: "success",
  WARNING: "warning", 
  ERROR: "error",
} as const;

// Connection Status Types  
export const CONNECTION_STATUS = {
  DISCONNECTED: "disconnected",
  CONNECTING: "connecting",
  CONNECTED: "connected", 
  ERROR: "error",
  RECONNECTING: "reconnecting",
} as const;

// Query History Limits
export const QUERY_HISTORY_CONFIG = {
  MAX_HISTORY: 100,
  CLEANUP_THRESHOLD: 150,
  RETENTION_DAYS: 30,
} as const;

// UI Configuration
export const UI_CONFIG = {
  THEMES: {
    LIGHT: "light",
    DARK: "dark",
    AUTO: "auto",
  },
  POSITIONS: {
    SIDEBAR: "sidebar",
    POPUP: "popup", 
    INLINE: "inline",
  },
  SIDEBAR_WIDTH: {
    DEFAULT: 400,
    MIN: 300,
    MAX: 600,
  },
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
} as const;

// Error Codes
export const ERROR_CODES = {
  // Connection Errors
  CONNECTION_FAILED: "CONNECTION_FAILED",
  CONNECTION_TIMEOUT: "CONNECTION_TIMEOUT",
  CONNECTION_LOST: "CONNECTION_LOST",
  
  // Authentication Errors
  UNAUTHORIZED: "UNAUTHORIZED",
  INVALID_API_KEY: "INVALID_API_KEY",
  SESSION_EXPIRED: "SESSION_EXPIRED",
  
  // Query Errors
  INVALID_QUERY: "INVALID_QUERY",
  QUERY_TIMEOUT: "QUERY_TIMEOUT",
  QUERY_FAILED: "QUERY_FAILED",
  INVALID_ENTITY: "INVALID_ENTITY",
  
  // Component Errors
  COMPONENT_NOT_FOUND: "COMPONENT_NOT_FOUND",
  ACTION_NOT_AVAILABLE: "ACTION_NOT_AVAILABLE",
  COMPONENT_ERROR: "COMPONENT_ERROR",
  
  // MCP Errors
  MCP_CONNECTION_FAILED: "MCP_CONNECTION_FAILED",
  MCP_REQUEST_FAILED: "MCP_REQUEST_FAILED",
  MCP_TIMEOUT: "MCP_TIMEOUT",
  
  // General Errors
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
} as const;

// Default Configuration
export const DEFAULT_COPILOT_CONFIG = {
  apiUrl: COPILOT_API_CONFIG.BASE_URL,
  model: "gpt-4",
  temperature: 0.7,
  maxTokens: 2048,
  enableMCP: true,
  enableComponentInteraction: true,
  features: {
    databaseQuerying: true,
    documentChat: true,
    componentControl: true,
    workflowAutomation: true,
  },
  ui: {
    theme: UI_CONFIG.THEMES.AUTO,
    position: UI_CONFIG.POSITIONS.SIDEBAR,
    defaultOpen: false,
    shortcuts: {
      openChat: "cmd+k",
      closeChat: "escape",
      newSession: "cmd+n",
      clearHistory: "cmd+shift+c",
    },
  },
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  QUERY_MIN_LENGTH: 3,
  QUERY_MAX_LENGTH: 1000,
  ACTION_NAME_MAX_LENGTH: 50,
  SESSION_TITLE_MAX_LENGTH: 100,
  NOTIFICATION_MESSAGE_MAX_LENGTH: 500,
} as const;

// Rate Limiting
export const RATE_LIMITS = {
  QUERIES_PER_MINUTE: 60,
  ACTIONS_PER_MINUTE: 30,
  CONNECTIONS_PER_MINUTE: 10,
} as const;

// Cache Configuration
export const CACHE_CONFIG = {
  SCHEMA_CACHE_TTL: 3600000, // 1 hour
  QUERY_CACHE_TTL: 300000, // 5 minutes
  COMPONENT_STATE_CACHE_TTL: 60000, // 1 minute
  MAX_CACHE_SIZE: 100, // Maximum number of cached items
} as const;

// Export types for constants
export type CopilotAction = typeof COPILOT_ACTIONS[keyof typeof COPILOT_ACTIONS];
export type ComponentCategory = typeof COMPONENT_CATEGORIES[keyof typeof COMPONENT_CATEGORIES];
export type AvailableComponent = typeof AVAILABLE_COMPONENTS[keyof typeof AVAILABLE_COMPONENTS];
export type MessageType = typeof MESSAGE_TYPES[keyof typeof MESSAGE_TYPES];
export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];
export type ConnectionStatus = typeof CONNECTION_STATUS[keyof typeof CONNECTION_STATUS];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type SupportedEntity = typeof QUERY_PARSER_CONFIG.SUPPORTED_ENTITIES[number];
export type TimeKeyword = typeof QUERY_PARSER_CONFIG.TIME_KEYWORDS[number];
export type AggregationFunction = typeof QUERY_PARSER_CONFIG.AGGREGATION_FUNCTIONS[number]; 