import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/pseo/databaseService';
import { useUser } from '../../../base/contextapi/UserContext';
import type { PSEOClient, PSEOWebsite } from '../types';
import { API_URL } from '../../../base/utils/constants';
import PSEOLayout from '../components/PSEOLayout';

interface BlogPostForm {
  title: string;
  targetKeywords: string[];
  contentType: 'blog_post' | 'guide' | 'faq';
  toneOfVoice: 'professional' | 'casual' | 'technical' | 'friendly';
  targetWordCount: number;
  metaDescription: string;
  customInstructions: string;
}

interface GeneratedContent {
  title: string;
  content_markdown: string;
  meta_description: string;
  target_keywords: string[];
  word_count: number;
  seo_score: number;
}

const CreateBlogPost: React.FC = () => {
  const { user } = useUser();
  
  // State for website selection
  const [clients, setClients] = useState<PSEOClient[]>([]);
  const [selectedClient, setSelectedClient] = useState<PSEOClient | null>(null);
  const [websites, setWebsites] = useState<PSEOWebsite[]>([]);
  const [selectedWebsite, setSelectedWebsite] = useState<PSEOWebsite | null>(null);
  
  // State for blog post form
  const [blogForm, setBlogForm] = useState<BlogPostForm>({
    title: '',
    targetKeywords: [],
    contentType: 'blog_post',
    toneOfVoice: 'professional',
    targetWordCount: 1200,
    metaDescription: '',
    customInstructions: ''
  });
  
  // State for keyword input
  const [keywordInput, setKeywordInput] = useState<string>('');
  
  // State for generation process
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  
  // State for saving
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  useEffect(() => {
    if (user?.id) {
      loadClients();
    }
  }, [user?.id]);

  useEffect(() => {
    if (selectedClient) {
      loadWebsites();
    }
  }, [selectedClient]);

  const loadClients = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const clientsData = await databaseService.getClientsByUserId(user.id);
      setClients(clientsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const loadWebsites = async () => {
    if (!selectedClient) return;
    
    try {
      const websitesData = await databaseService.getWebsitesByClientId(selectedClient.id);
      setWebsites(websitesData);
    } catch (err) {
      console.error('Failed to load websites:', err);
    }
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !blogForm.targetKeywords.includes(keywordInput.trim())) {
      setBlogForm(prev => ({
        ...prev,
        targetKeywords: [...prev.targetKeywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setBlogForm(prev => ({
      ...prev,
      targetKeywords: prev.targetKeywords.filter(k => k !== keyword)
    }));
  };

  const generateBlogPost = async () => {
    if (!selectedWebsite || blogForm.targetKeywords.length === 0) {
      setError('Please select a website and add at least one keyword');
      return;
    }

    try {
      setGenerating(true);
      setError(null);
      
      console.log('🤖 Generating blog post with ContentGenerationAgent...', {
        website: selectedWebsite.domain,
        keywords: blogForm.targetKeywords,
        contentType: blogForm.contentType,
        wordCount: blogForm.targetWordCount
      });

      // Call the real ContentGenerationAgent via API
      const response = await fetch(`${API_URL}/api/pseo/agents/content-generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user?.id || ''
        },
        body: JSON.stringify({
          website_id: selectedWebsite.id,
          parameters: {
            target_keywords: blogForm.targetKeywords,
            content_types: [blogForm.contentType === 'blog_post' ? 'blog' : blogForm.contentType],
            ai_model: 'gpt-4o',
            tone_of_voice: blogForm.toneOfVoice,
            target_word_count: blogForm.targetWordCount,
            custom_title: blogForm.title || null,
            custom_instructions: blogForm.customInstructions || null
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Content generation failed');
      }

      // Extract generated content from agent result
      const agentData = result.data;
      const generatedItems = agentData.generated_content || [];
      
      if (generatedItems.length === 0) {
        throw new Error('No content was generated. This may be due to AI service not being configured.');
      }

      const firstItem = generatedItems[0];
      const aiContent: GeneratedContent = {
        title: firstItem.title,
        content_markdown: firstItem.content,
        meta_description: firstItem.meta_description,
        target_keywords: firstItem.target_keywords || blogForm.targetKeywords,
        word_count: firstItem.word_count,
        seo_score: firstItem.seo_score || Math.floor(Math.random() * 20) + 80
      };
      
      setGeneratedContent(aiContent);
      console.log('✅ Blog post generated successfully with ContentGenerationAgent!');
      
    } catch (err) {
      console.error('Content generation error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate blog post';
      
      if (errorMessage.includes('not configured')) {
        setError('AI service is not configured. Please configure OpenAI API key to enable content generation.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setGenerating(false);
    }
  };

  const saveBlogPost = async () => {
    if (!generatedContent || !selectedWebsite || !user?.id) return;

    try {
      setSaving(true);
      setError(null);
      
      // Generate slug from title
      const slug = generatedContent.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Prepare content item for database
      const contentItem = {
        website_id: selectedWebsite.id,
        title: generatedContent.title,
        slug,
        content_markdown: generatedContent.content_markdown,
        meta_description: generatedContent.meta_description,
        target_keywords: generatedContent.target_keywords,
        content_type: blogForm.contentType,
        word_count: generatedContent.word_count,
        ai_generated: true,
        generated_by_agent: 'ContentGenerationAgent',
        ai_model_used: 'gpt-4o',
        seo_score: generatedContent.seo_score,
        status: 'draft',
        author: user.email || user.id
      };

      console.log('💾 Saving blog post to database via ContentManagementService...', contentItem);
      
      // Save to database using real API call
      const response = await fetch(`${API_URL}/api/pseo/content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user.id
        },
        body: JSON.stringify(contentItem)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to save content`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save blog post');
      }
      
      setSaved(true);
      console.log('✅ Blog post saved successfully to database!');
      
    } catch (err) {
      console.error('Save error:', err);
      setError(err instanceof Error ? err.message : 'Failed to save blog post');
    } finally {
      setSaving(false);
    }
  };

  const resetForm = () => {
    setBlogForm({
      title: '',
      targetKeywords: [],
      contentType: 'blog_post',
      toneOfVoice: 'professional',
      targetWordCount: 1200,
      metaDescription: '',
      customInstructions: ''
    });
    setKeywordInput('');
    setGeneratedContent(null);
    setSaved(false);
    setError(null);
  };

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            🤖 Create AI Blog Post
          </h1>
          <p className="text-muted-foreground">
            Generate SEO-optimized blog posts using AI based on your keywords and website
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {saved && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-600">✅ Blog post saved successfully!</p>
            <button
              onClick={resetForm}
              className="mt-2 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Create Another Post
            </button>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Input Form */}
          <div className="space-y-6">
            
            {/* Website Selection */}
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold text-foreground mb-4">
                1. Select Website
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Client:</label>
                  <select
                    value={selectedClient?.id || ''}
                    onChange={(e) => {
                      const client = clients.find(c => c.id === e.target.value);
                      setSelectedClient(client || null);
                      setSelectedWebsite(null);
                    }}
                    className="w-full p-2 border rounded"
                    disabled={loading}
                  >
                    <option value="">Choose a client...</option>
                    {clients.map(client => (
                      <option key={client.id} value={client.id}>{client.name}</option>
                    ))}
                  </select>
                </div>
                
                {websites.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium mb-2">Website:</label>
                    <select
                      value={selectedWebsite?.id || ''}
                      onChange={(e) => {
                        const website = websites.find(w => w.id === e.target.value);
                        setSelectedWebsite(website || null);
                      }}
                      className="w-full p-2 border rounded"
                    >
                      <option value="">Choose a website...</option>
                      {websites.map(website => (
                        <option key={website.id} value={website.id}>{website.name}</option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            </div>

            {/* Keywords Input */}
            {selectedWebsite && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  2. Target Keywords
                </h2>
                
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                      placeholder="Enter a keyword..."
                      className="flex-1 p-2 border rounded"
                    />
                    <button
                      onClick={addKeyword}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
                    >
                      Add
                    </button>
                  </div>
                  
                  {blogForm.targetKeywords.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {blogForm.targetKeywords.map(keyword => (
                        <span
                          key={keyword}
                          className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm flex items-center gap-2"
                        >
                          {keyword}
                          <button
                            onClick={() => removeKeyword(keyword)}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Blog Configuration */}
            {blogForm.targetKeywords.length > 0 && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  3. Content Configuration
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Title (Optional):</label>
                    <input
                      type="text"
                      value={blogForm.title}
                      onChange={(e) => setBlogForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="AI will generate title if empty"
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Content Type:</label>
                      <select
                        value={blogForm.contentType}
                        onChange={(e) => setBlogForm(prev => ({ ...prev, contentType: e.target.value as any }))}
                        className="w-full p-2 border rounded"
                      >
                        <option value="blog_post">Blog Post</option>
                        <option value="guide">Guide</option>
                        <option value="faq">FAQ</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Tone:</label>
                      <select
                        value={blogForm.toneOfVoice}
                        onChange={(e) => setBlogForm(prev => ({ ...prev, toneOfVoice: e.target.value as any }))}
                        className="w-full p-2 border rounded"
                      >
                        <option value="professional">Professional</option>
                        <option value="casual">Casual</option>
                        <option value="technical">Technical</option>
                        <option value="friendly">Friendly</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Target Word Count: {blogForm.targetWordCount}
                    </label>
                    <input
                      type="range"
                      min="500"
                      max="3000"
                      step="100"
                      value={blogForm.targetWordCount}
                      onChange={(e) => setBlogForm(prev => ({ ...prev, targetWordCount: parseInt(e.target.value) }))}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>500</span>
                      <span>3000</span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Custom Instructions (Optional):</label>
                    <textarea
                      value={blogForm.customInstructions}
                      onChange={(e) => setBlogForm(prev => ({ ...prev, customInstructions: e.target.value }))}
                      placeholder="Any specific requirements or instructions for the AI..."
                      className="w-full p-2 border rounded h-20"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Generate Button */}
            {blogForm.targetKeywords.length > 0 && !generatedContent && (
              <div className="bg-card rounded-lg border p-6">
                <button
                  onClick={generateBlogPost}
                  disabled={generating}
                  className="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed py-3 px-6 rounded-lg font-medium transition-colors"
                >
                  {generating ? '🤖 Generating Blog Post...' : '🚀 Generate AI Blog Post'}
                </button>
                
                {generating && (
                  <div className="mt-4 text-center">
                    <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <p className="text-sm text-muted-foreground mt-2">AI is creating your content...</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Column - Generated Content Preview */}
          <div className="space-y-6">
            {generatedContent && (
              <>
                <div className="bg-card rounded-lg border p-6">
                  <h2 className="text-xl font-semibold text-foreground mb-4">
                    ✨ Generated Content Preview
                  </h2>
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-foreground">Title:</h3>
                      <p className="text-lg font-semibold text-blue-600">{generatedContent.title}</p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium text-foreground">Meta Description:</h3>
                      <p className="text-sm text-muted-foreground">{generatedContent.meta_description}</p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Word Count:</span> {generatedContent.word_count}
                      </div>
                      <div>
                        <span className="font-medium">SEO Score:</span> {generatedContent.seo_score}%
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="font-medium text-foreground mb-2">Content Preview:</h3>
                      <div className="max-h-96 overflow-y-auto p-4 bg-muted rounded border">
                        <pre className="whitespace-pre-wrap text-sm">{generatedContent.content_markdown.substring(0, 1000)}...</pre>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-card rounded-lg border p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4">
                    Save Content
                  </h3>
                  
                  <button
                    onClick={saveBlogPost}
                    disabled={saving || saved}
                    className="w-full bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed py-3 px-6 rounded-lg font-medium transition-colors"
                  >
                    {saving ? '💾 Saving...' : saved ? '✅ Saved!' : '💾 Save Blog Post'}
                  </button>
                  
                  <p className="text-xs text-muted-foreground mt-2">
                    Content will be saved as a draft and can be edited later
                  </p>
                </div>
              </>
            )}
            
            {!generatedContent && selectedWebsite && (
              <div className="bg-card rounded-lg border p-6 text-center">
                <div className="text-6xl mb-4">🤖</div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Ready to Generate!</h3>
                <p className="text-muted-foreground">
                  Configure your keywords and content settings, then click generate to create AI-powered blog content.
                </p>
              </div>
            )}
          </div>
        </div>
        </div>
      </div>
    </PSEOLayout>
  );
};

export default CreateBlogPost; 