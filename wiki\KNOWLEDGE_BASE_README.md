# AltZero Knowledge Base & AI Chat System

## Overview

The AltZero platform now includes a comprehensive Knowledge Base and AI Chat system that allows users to:

- **Upload Documents**: Support for PDF, Word, Text, and Markdown files
- **AI-Powered Chat**: Intelligent conversations based on your document content
- **Document Management**: Organize, search, and manage your knowledge base
- **Real-time Streaming**: Live AI responses with source citations

## Features

### 🗂️ Knowledge Base

- **File Upload**: Drag-and-drop interface with support for multiple file types
- **Document Processing**: Automatic parsing and indexing with LlamaParser
- **Status Tracking**: Real-time progress monitoring for uploads and processing
- **Search & Filter**: Find documents quickly with advanced filtering
- **Statistics Dashboard**: Overview of your knowledge base metrics

### 💬 AI Chat

- **Document-Based Chat**: Ask questions about your uploaded documents
- **Source Citations**: See exactly which documents informed each response
- **Session Management**: Organize conversations into separate chat sessions
- **Streaming Responses**: Real-time AI responses with typing indicators
- **Multi-Document Context**: Chat across multiple selected documents

## Getting Started

### 1. Navigation

Use the header navigation to switch between:

- **Knowledge Base**: Upload and manage documents
- **AI Chat**: Start conversations with your documents

### 2. Upload Documents

1. Go to the Knowledge Base page
2. Drag and drop files or click "Choose Files"
3. Supported formats: PDF, DOCX, TXT, MD, DOC, RTF, ODT
4. Maximum file size: 50MB per file
5. Wait for processing to complete

### 3. Start Chatting

1. Go to the AI Chat page
2. Select documents you want to chat about
3. Create a new chat session
4. Ask questions about your documents
5. Get AI responses with source citations

## Technical Architecture

### Frontend Components

- **KnowledgeBase.tsx**: Document upload and management interface
- **Chat.tsx**: AI chat interface with streaming support
- **KnowledgeContext**: State management for documents and chat sessions
- **knowledgeService**: API integration and file handling

### Backend API

- **Document Upload**: `/api/knowledge/documents/upload`
- **Document Management**: `/api/knowledge/documents`
- **Chat Streaming**: `/api/knowledge/chat/stream`
- **Session Management**: `/api/knowledge/chat/sessions`

### Key Technologies

- **Frontend**: React, TypeScript, Tailwind CSS, Framer Motion
- **File Upload**: react-dropzone with validation
- **Streaming**: Server-Sent Events (SSE)
- **State Management**: React Context with useReducer
- **UI Components**: Radix UI primitives
- **Markdown Rendering**: react-markdown with syntax highlighting

## Environment Setup

### Required Environment Variables

Copy `env.template` to `.env` and fill in your API keys:

```bash
# Frontend Environment Variables
VITE_API_BASE_URL=http://localhost:3001
VITE_API_KEY=your-api-key-here

# Backend Environment Variables
PORT=3001
NODE_ENV=development

# AI Services
OPENAI_API_KEY=your-openai-api-key-here
LLAMA_CLOUD_API_KEY=your-llama-cloud-api-key-here
LLAMA_CLOUD_BASE_URL=https://api.cloud.llamaindex.ai

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_INDEX_NAME=altzero-knowledge-base

# LangSmith (Optional - for debugging and monitoring)
LANGSMITH_API_KEY=your-langsmith-api-key-here
LANGSMITH_PROJECT=altzero-knowledge-base
LANGSMITH_TRACING=true

# Chat Configuration
CHAT_MODEL=gpt-4
CHAT_TEMPERATURE=0.7
MAX_TOKENS=2048
MAX_RETRIEVAL_RESULTS=10
MIN_RELEVANCE_SCORE=0.7

# Document Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

### Development Setup

1. **Install Dependencies**:

   ```bash
   npm install
   # or
   yarn install
   ```

2. **Start Backend Server**:

   ```bash
   cd server
   npm run dev
   ```

3. **Start Frontend**:

   ```bash
   npm run dev
   ```

4. **Access the Application**:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000

## Usage Examples

### Uploading Documents

1. Navigate to Knowledge Base
2. Drag PDF files into the upload zone
3. Monitor processing status
4. Documents appear in the grid when ready

### Starting a Chat Session

1. Navigate to AI Chat
2. Click "Select Documents" to choose files
3. Click "New Chat" to start a session
4. Type your question and press Enter
5. Watch the AI response stream in real-time

### Managing Documents

- **Delete**: Click the trash icon on any document
- **Retry**: Click retry on failed documents
- **Search**: Use the search bar to find specific files
- **Filter**: Filter by processing status

## Troubleshooting

### Common Issues

1. **Upload Fails**:

   - Check file size (max 50MB)
   - Verify file type is supported
   - Ensure stable internet connection

2. **Chat Not Working**:

   - Verify documents are selected
   - Check that documents have "Ready" status
   - Ensure backend server is running

3. **Slow Processing**:
   - Large files take longer to process
   - Check server resources
   - Verify API keys are configured

### Error Messages

- **"No documents selected"**: Select documents before chatting
- **"Failed to upload"**: Check file size and format
- **"Processing failed"**: Document parsing error, try re-uploading

## Future Enhancements

### Planned Features

- **Advanced Search**: Semantic search across document content
- **Document Versioning**: Track changes and updates
- **Collaboration**: Share knowledge bases with team members
- **Export Options**: Download chat transcripts and summaries
- **Integration**: Connect with external knowledge sources

### AI Implementation Details

The system now includes full AI functionality:

#### Document Processing Pipeline

1. **LlamaParse**: Extracts text from documents with high accuracy
2. **Chunking**: Splits documents into overlapping chunks for better context
3. **Embeddings**: Generates vector embeddings using OpenAI's text-embedding-3-small
4. **Indexing**: Stores vectors in Pinecone for fast similarity search

#### RAG (Retrieval-Augmented Generation)

1. **Query Processing**: User questions are converted to embeddings
2. **Similarity Search**: Finds relevant document chunks using vector similarity
3. **Context Assembly**: Combines relevant chunks with conversation history
4. **Response Generation**: GPT-4 generates responses based on retrieved context
5. **Source Attribution**: Responses include citations with relevance scores

#### Streaming Architecture

- **Server-Sent Events**: Real-time response streaming
- **Async Processing**: Non-blocking document processing
- **Error Recovery**: Robust error handling with retry mechanisms

#### Services Architecture

- **LlamaCloudService**: Document parsing and content extraction
- **PineconeService**: Vector storage and similarity search
- **RAGService**: Retrieval-augmented generation with LangChain

## Support

For issues or questions:

1. Check the browser console for error messages
2. Verify environment variables are set correctly
3. Ensure all required services are running
4. Check network connectivity

## Contributing

When contributing to the knowledge base system:

1. Follow the existing code patterns
2. Add proper TypeScript types
3. Include error handling
4. Test file upload and chat functionality
5. Update documentation as needed
