import { Pinecone } from "@pinecone-database/pinecone";
import { OpenAIEmbeddings } from "@langchain/openai";
import { PineconeStore } from "@langchain/pinecone";
import { Document as LangChainDocument } from "@langchain/core/documents";
import { environment } from "../../../base/common/apps/config/environment";

export interface PineconeDocument {
  id: string;
  content: string;
  metadata: {
    filename: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    uploadedAt: string;
    userId: string;
    chunkIndex?: number;
    pageNumber?: number;
    llamaCloudId?: string;
  };
  embedding?: number[];
}

export interface SearchResult {
  id: string;
  text: string;
  score: number;
  metadata?: Record<string, any>;
}

class PineconeService {
  private client: Pinecone | null = null;
  private indexName: string = "";
  private embeddings: OpenAIEmbeddings | null = null;
  private vectorStore: PineconeStore | null = null;
  private isEnabled: boolean = false;

  constructor() {
    if (!environment.pineconeApiKey) {
      console.warn(
        "PINECONE_API_KEY not found. Pinecone features will be disabled."
      );
      this.isEnabled = false;
      return;
    }

    if (!process.env.OPENAI_API_KEY) {
      console.warn(
        "OPENAI_API_KEY not found. Pinecone features will be disabled."
      );
      this.isEnabled = false;
      return;
    }

    try {
      this.client = new Pinecone({
        apiKey: environment.pineconeApiKey,
      });

      this.indexName = environment.pineconeIndexName || "altzerotest";

      this.embeddings = new OpenAIEmbeddings({
        openAIApiKey: process.env.OPENAI_API_KEY,
        model: "text-embedding-3-small",
        dimensions: 1024,
      });

      this.isEnabled = true;
      this.initializeVectorStore();
    } catch (error) {
      console.error("Failed to initialize Pinecone client:", error);
      this.isEnabled = false;
    }
  }

  private async initializeVectorStore(): Promise<void> {
    if (!this.isEnabled || !this.client || !this.embeddings) {
      return;
    }

    try {
      console.log(`🔄 Initializing Pinecone vector store...`);
      const index = this.client.index(this.indexName);

      this.vectorStore = await PineconeStore.fromExistingIndex(
        this.embeddings,
        {
          pineconeIndex: index,
          textKey: "text",
          namespace: environment.pineconeNamespace || "default",
        }
      );

      console.log("✅ Pinecone vector store initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize Pinecone vector store:", error);
      this.vectorStore = null;
      this.isEnabled = false;
    }
  }

  // Ensure vectorStore is initialized before use
  private async ensureVectorStoreInitialized(): Promise<boolean> {
    if (!this.isEnabled) {
      console.log(`❌ Pinecone not enabled`);
      return false;
    }

    if (this.vectorStore) {
      console.log(`✅ VectorStore already initialized`);
      return true;
    }

    console.log(`🔄 VectorStore not ready, initializing...`);
    await this.initializeVectorStore();

    if (this.vectorStore) {
      console.log(`✅ VectorStore initialization completed`);
      return true;
    } else {
      console.log(`❌ VectorStore initialization failed`);
      return false;
    }
  }

  private sanitizeText(text: string): string {
    // Remove invalid unicode characters and control characters
    return text
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, "") // Remove control characters
      .replace(/[\uD800-\uDFFF]/g, "") // Remove unpaired surrogate characters
      .replace(/\uFFFD/g, "") // Remove replacement characters
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "") // Remove additional control chars
      .trim();
  }

  async storeDocuments(documents: PineconeDocument[]): Promise<string[]> {
    console.log(
      `🚨 ENTRY: storeDocuments called with ${documents.length} documents`
    );
    console.log(`📋 Debug state:`, {
      isEnabled: this.isEnabled,
      hasVectorStore: !!this.vectorStore,
      hasClient: !!this.client,
      hasEmbeddings: !!this.embeddings,
    });

    console.log(
      `💾 Starting storeDocuments with ${documents.length} documents`
    );

    // Ensure vectorStore is properly initialized
    console.log(`🔄 Ensuring vectorStore is initialized for storage...`);
    const isInitialized = await this.ensureVectorStoreInitialized();

    if (!isInitialized) {
      console.warn("❌ Pinecone not available, skipping document storage");
      console.log(
        `Debug: isEnabled=${this.isEnabled}, vectorStore=${!!this.vectorStore}`
      );
      return documents.map((doc) => doc.id); // Return document IDs as if stored
    }

    try {
      console.log(`✅ Pinecone is enabled and vectorStore exists`);
      console.log(`📋 Sample document metadata:`, documents[0]?.metadata);

      console.log(`📋 About to sanitize and prepare documents...`);
      // Sanitize and prepare documents
      const langChainDocs = documents
        .map(
          (doc) =>
            new LangChainDocument({
              pageContent: this.sanitizeText(doc.content),
              metadata: {
                ...doc.metadata,
                id: doc.id,
              },
            })
        )
        .filter((doc) => doc.pageContent.length > 0); // Filter out empty documents

      console.log(
        `📦 Prepared ${langChainDocs.length} LangChain documents (filtered from ${documents.length})`
      );
      console.log(`📋 Sample LangChain doc:`, {
        pageContentLength: langChainDocs[0]?.pageContent.length,
        metadata: langChainDocs[0]?.metadata,
      });

      if (langChainDocs.length === 0) {
        console.warn(`⚠️ No valid documents to store after filtering!`);
        return [];
      }

      console.log(`📋 About to start batch processing...`);
      // Process in batches for better performance
      const batchSize = 50; // Reduced batch size for better reliability
      const allIds: string[] = [];

      for (let i = 0; i < langChainDocs.length; i += batchSize) {
        const batch = langChainDocs.slice(i, i + batchSize);
        console.log(
          `🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
            langChainDocs.length / batchSize
          )} (${batch.length} documents)`
        );

        try {
          console.log(`📤 Calling vectorStore.addDocuments with timeout...`);
          console.log(`📋 Batch details:`, {
            batchSize: batch.length,
            sampleContent: batch[0]?.pageContent.substring(0, 100),
            sampleMetadata: batch[0]?.metadata,
          });

          console.log(
            `🚨 ABOUT TO CALL vectorStore.addDocuments - THIS IS WHERE IT MIGHT HANG`
          );

          // Add timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(
              () =>
                reject(
                  new Error("vectorStore.addDocuments timeout after 30 seconds")
                ),
              30000
            );
          });

          if (!this.vectorStore) {
            throw new Error("VectorStore is null after initialization check");
          }

          const addDocumentsPromise = this.vectorStore.addDocuments(batch);

          const batchIds = (await Promise.race([
            addDocumentsPromise,
            timeoutPromise,
          ])) as string[];
          console.log(`✅ Batch stored successfully. IDs:`, batchIds);
          allIds.push(...batchIds);

          // Small delay between batches to avoid rate limiting
          if (i + batchSize < langChainDocs.length) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        } catch (batchError) {
          console.error(
            `❌ Error processing batch ${Math.floor(i / batchSize) + 1}:`,
            batchError
          );
          console.error(`❌ Batch error details:`, {
            message:
              batchError instanceof Error
                ? batchError.message
                : "Unknown error",
            stack:
              batchError instanceof Error ? batchError.stack : "No stack trace",
          });
          // Continue with next batch instead of failing completely
          continue;
        }
      }

      console.log(
        `🎯 Successfully stored ${allIds.length} documents in Pinecone`
      );
      console.log(`📋 All stored IDs:`, allIds);

      // Verify storage by checking index stats
      try {
        const stats = await this.getIndexStats();
        console.log(`📊 Index stats after storage:`, stats);
      } catch (statsError) {
        console.error(`❌ Failed to get stats after storage:`, statsError);
      }

      return allIds;
    } catch (error) {
      console.error("❌ Error storing documents in Pinecone:", error);
      console.error("❌ Storage error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace",
      });
      throw error;
    }
  }

  async searchSimilar(
    query: string,
    options: {
      topK?: number;
      filter?: Record<string, any>;
      minScore?: number;
    } = {}
  ): Promise<SearchResult[]> {
    if (!this.isEnabled || !this.vectorStore) {
      console.warn("Pinecone not available, returning empty search results");
      return [];
    }

    try {
      const { topK = 10, filter, minScore = 0.0 } = options;

      console.log(`Searching Pinecone for: "${query}" with topK=${topK}`);

      const results = await this.vectorStore.similaritySearchWithScore(
        query,
        topK,
        filter
      );

      const searchResults: SearchResult[] = results
        .filter(([_, score]) => score >= minScore)
        .map(([doc, score]) => ({
          id: doc.metadata.id || Math.random().toString(36),
          text: doc.pageContent,
          score: score,
          metadata: doc.metadata,
        }));

      console.log(`Found ${searchResults.length} results in Pinecone`);
      return searchResults;
    } catch (error) {
      console.error("Error searching Pinecone:", error);
      return [];
    }
  }

  async deleteDocuments(documentIds: string[]): Promise<void> {
    if (!this.isEnabled || !this.client) {
      console.warn("Pinecone not available, skipping document deletion");
      return;
    }

    try {
      const namespace = environment.pineconeNamespace || "default";
      console.log(
        `🗑️ Starting deletion of ${
          documentIds.length
        } document(s): ${documentIds.join(", ")}`
      );
      console.log(`🗑️ Using namespace: ${namespace}`);
      console.log(
        `🗑️ Note: Using direct Pinecone queries to find correct vector IDs`
      );

      const index = this.client.index(this.indexName);
      const ns = index.namespace(namespace);

      // For each document, find and delete all its chunks
      for (const documentId of documentIds) {
        console.log(`🗑️ Deleting all chunks for document: ${documentId}`);

        try {
          // Use direct Pinecone query to find all chunks for this document
          let allVectorIds: string[] = [];

          // Search by multiple possible metadata fields using direct Pinecone queries
          const searchFields = [
            { llamaCloudId: documentId },
            { originalLlamaCloudId: documentId },
            { id: documentId },
            { documentId: documentId },
          ];

          for (const filter of searchFields) {
            try {
              console.log(`🔍 Direct Pinecone search with filter:`, filter);
              const randomVector = Array.from(
                { length: 1024 },
                () => Math.random() * 0.01
              );

              const queryResult = await ns.query({
                vector: randomVector,
                topK: 1000, // Get a large number to find all chunks
                includeMetadata: true,
                filter: filter,
              });

              if (queryResult.matches && queryResult.matches.length > 0) {
                const vectorIds = queryResult.matches.map(
                  (match: any) => match.id
                );
                console.log(
                  `🔍 Found ${vectorIds.length} chunks with filter:`,
                  filter
                );
                console.log(`🔍 Vector IDs:`, vectorIds);
                allVectorIds.push(...vectorIds);
              }
            } catch (searchError) {
              console.log(
                `⚠️ Direct search failed for filter ${JSON.stringify(filter)}:`,
                searchError
              );
            }
          }

          // Remove duplicates
          const uniqueVectorIds = [...new Set(allVectorIds)];
          console.log(
            `🗑️ Total unique chunks to delete: ${uniqueVectorIds.length}`
          );
          console.log(`🗑️ Unique vector IDs:`, uniqueVectorIds);

          if (uniqueVectorIds.length > 0) {
            // Try batch deletion first
            try {
              console.log(`🗑️ Attempting batch deletion with ns.deleteMany()`);
              const deleteResponse = await ns.deleteMany(uniqueVectorIds);
              console.log(`🗑️ Batch delete response:`, deleteResponse);

              // Wait for consistency
              await new Promise((resolve) => setTimeout(resolve, 2000));

              // Verify deletion using direct Pinecone query
              console.log(`🔍 Verifying batch deletion with direct query...`);
              const randomVector = Array.from(
                { length: 1024 },
                () => Math.random() * 0.01
              );
              const verifyResult = await ns.query({
                vector: randomVector,
                topK: 1000,
                includeMetadata: true,
                filter: { llamaCloudId: documentId },
              });

              if (!verifyResult.matches || verifyResult.matches.length === 0) {
                console.log(
                  `✅ Batch deletion successful for document: ${documentId}`
                );
              } else {
                console.log(
                  `⚠️ Batch deletion incomplete, ${verifyResult.matches.length} chunks still exist`
                );
                console.log(
                  `🔍 Remaining chunks:`,
                  verifyResult.matches.map((m: any) => m.id)
                );
                throw new Error("Batch deletion incomplete");
              }
            } catch (batchError) {
              console.error(
                `❌ Batch deletion failed, trying individual deletion:`,
                batchError
              );

              // Fallback to individual deletion
              let deletedCount = 0;
              for (const vectorId of uniqueVectorIds) {
                try {
                  console.log(`🗑️ Deleting individual vector: ${vectorId}`);
                  const individualResponse = await ns.deleteOne(vectorId);
                  console.log(
                    `🗑️ Individual delete response for ${vectorId}:`,
                    individualResponse
                  );
                  deletedCount++;

                  // Small delay between individual deletions
                  await new Promise((resolve) => setTimeout(resolve, 100));
                } catch (individualError) {
                  console.error(
                    `❌ Failed to delete individual vector ${vectorId}:`,
                    individualError
                  );
                }
              }
              console.log(
                `✅ Individually deleted ${deletedCount}/${uniqueVectorIds.length} chunks for document: ${documentId}`
              );

              // Final verification after individual deletion
              const randomVector = Array.from(
                { length: 1024 },
                () => Math.random() * 0.01
              );
              const finalVerifyResult = await ns.query({
                vector: randomVector,
                topK: 1000,
                includeMetadata: true,
                filter: { llamaCloudId: documentId },
              });

              if (
                !finalVerifyResult.matches ||
                finalVerifyResult.matches.length === 0
              ) {
                console.log(
                  `✅ Individual deletion successful for document: ${documentId}`
                );
              } else {
                console.log(
                  `⚠️ Individual deletion incomplete, ${finalVerifyResult.matches.length} chunks still exist`
                );
              }
            }
          } else {
            console.log(`⚠️ No chunks found for document: ${documentId}`);
          }
        } catch (docError) {
          console.error(`❌ Error deleting document ${documentId}:`, docError);
          // Continue with next document instead of failing completely
        }
      }

      console.log(`✅ Completed deletion process for all documents`);
    } catch (error) {
      console.error("❌ Error deleting documents from Pinecone:", error);
      throw error;
    }
  }

  // Get user-specific documents from Pinecone
  async getUserDocuments(userId: string): Promise<any[]> {
    console.log(`🔍 Starting getUserDocuments for userId: ${userId}`);

    if (!this.isEnabled || !this.client) {
      console.warn("❌ Pinecone not available, returning empty documents list");
      console.log(
        `Debug: isEnabled=${this.isEnabled}, client=${!!this.client}`
      );
      return [];
    }

    try {
      console.log(`✅ Pinecone is enabled and client exists`);
      console.log(`📋 Index name: ${this.indexName}`);
      console.log(
        `📋 Namespace: ${environment.pineconeNamespace || "default"}`
      );

      // Get index with namespace - this is the correct way to handle namespaces
      const namespace = environment.pineconeNamespace || "default";
      const index = this.client.index(this.indexName).namespace(namespace);
      console.log(
        `📋 Index object created successfully with namespace: ${namespace}`
      );

      // First, let's check if the index has any data at all
      try {
        const stats = await this.client
          .index(this.indexName)
          .describeIndexStats();
        console.log(`📊 Index stats:`, {
          totalRecordCount: stats.totalRecordCount,
          dimension: stats.dimension,
          namespaces: Object.keys(stats.namespaces || {}),
        });

        if (stats.totalRecordCount === 0) {
          console.log(`⚠️ Index is empty! No documents stored yet.`);
          return [];
        }

        // Check if our namespace has data
        const namespaceStats = stats.namespaces?.[namespace];
        if (namespaceStats) {
          console.log(`📊 Namespace '${namespace}' stats:`, {
            vectorCount: namespaceStats.recordCount,
          });
        } else {
          console.log(`⚠️ Namespace '${namespace}' not found in index stats`);
          console.log(
            `📋 Available namespaces:`,
            Object.keys(stats.namespaces || {})
          );
        }
      } catch (statsError) {
        console.error(`❌ Failed to get index stats:`, statsError);
      }

      // Use query with a random vector and filter by userId
      // This is a workaround since Pinecone requires a vector for queries
      console.log(`📋 Using query with random vector and userId filter...`);

      // Generate a small random vector to get diverse results
      const randomVector = Array.from(
        { length: 1024 },
        () => Math.random() * 0.01
      );

      const queryResponse = await index.query({
        vector: randomVector,
        topK: 10000, // Large number to get all user documents
        includeMetadata: true,
        filter: {
          userId: { $eq: userId },
        },
      });

      console.log(`📊 Query response with userId filter:`, {
        matchesCount: queryResponse.matches?.length || 0,
      });

      // If no results with $eq, try simple filter
      if (!queryResponse.matches || queryResponse.matches.length === 0) {
        console.log(`📋 Trying with simple userId filter...`);

        const simpleQueryResponse = await index.query({
          vector: randomVector,
          topK: 10000,
          includeMetadata: true,
          filter: {
            userId: userId,
          },
        });

        console.log(`📊 Simple filter response:`, {
          matchesCount: simpleQueryResponse.matches?.length || 0,
        });

        // If still no results, try without filter to see what's available
        if (
          !simpleQueryResponse.matches ||
          simpleQueryResponse.matches.length === 0
        ) {
          console.log(
            `📋 No results with filter, checking all data in namespace...`
          );

          const allDataResponse = await index.query({
            vector: randomVector,
            topK: 100, // Smaller sample to see what's there
            includeMetadata: true,
            // No filter
          });

          console.log(`📊 All data sample in namespace '${namespace}':`, {
            matchesCount: allDataResponse.matches?.length || 0,
          });

          // If no data in our namespace, try the default namespace directly
          if (
            !allDataResponse.matches ||
            allDataResponse.matches.length === 0
          ) {
            console.log(`📋 Trying default namespace directly...`);

            const defaultIndex = this.client
              .index(this.indexName)
              .namespace("default");
            const defaultResponse = await defaultIndex.query({
              vector: randomVector,
              topK: 100,
              includeMetadata: true,
            });

            console.log(`📊 Default namespace response:`, {
              matchesCount: defaultResponse.matches?.length || 0,
            });

            if (defaultResponse.matches && defaultResponse.matches.length > 0) {
              console.log(
                `✅ Found data in default namespace! Using this data.`
              );
              allDataResponse.matches = defaultResponse.matches;
            }
          }

          if (allDataResponse.matches && allDataResponse.matches.length > 0) {
            // Show all unique userIds
            const userIds = new Set<string>();
            allDataResponse.matches.forEach((match) => {
              if (match.metadata?.userId) {
                userIds.add(String(match.metadata.userId));
              }
            });
            console.log(
              `🔍 All userIds found in Pinecone:`,
              Array.from(userIds)
            );
            console.log(
              `🎯 Looking for userId: "${userId}" (type: ${typeof userId})`
            );

            // Manual filter
            const userMatches = allDataResponse.matches.filter(
              (match) => String(match.metadata?.userId) === String(userId)
            );

            if (userMatches.length > 0) {
              console.log(
                `✅ Found ${userMatches.length} matches after manual filtering`
              );
              queryResponse.matches = userMatches;
            } else {
              console.log(`❌ No matches found even after manual filtering`);
              return [];
            }
          } else {
            console.log(`❌ No data found in Pinecone at all`);
            return [];
          }
        } else {
          queryResponse.matches = simpleQueryResponse.matches;
        }
      }

      // Group by document ID and deduplicate
      const documentMap = new Map();
      const processedChunkIds = new Set<string>(); // Track processed chunk IDs to avoid duplicates

      queryResponse.matches?.forEach((match, index) => {
        const metadata = match.metadata;

        // Skip if we've already processed this chunk
        if (processedChunkIds.has(match.id)) {
          return;
        }
        processedChunkIds.add(match.id);

        if (metadata) {
          const docId = metadata.llamaCloudId || metadata.id || match.id;
          const filename =
            metadata.filename || metadata.fileName || "Unknown Document";

          if (!documentMap.has(docId)) {
            const document = {
              id: docId,
              name: filename,
              type: metadata.fileType || "application/octet-stream",
              size: metadata.fileSize || 0,
              status: "success",
              uploadedAt: metadata.uploadedAt || new Date().toISOString(),
              userId: metadata.userId,
              error: null,
            };

            documentMap.set(docId, document);
          }
        }
      });

      const documents = Array.from(documentMap.values());

      // Final deduplication check by document ID
      const uniqueDocuments = documents.filter(
        (doc, index, self) => index === self.findIndex((d) => d.id === doc.id)
      );

      console.log(
        `🎯 Final result: Found ${uniqueDocuments.length} unique documents for user ${userId}`
      );

      return uniqueDocuments;
    } catch (error) {
      console.error("❌ Error fetching user documents from Pinecone:", error);
      console.error("❌ Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace",
      });
      throw error;
    }
  }

  async getIndexStats(): Promise<{
    totalVectors: number;
    dimension: number;
    namespaces: string[];
  }> {
    if (!this.isEnabled || !this.client) {
      return {
        totalVectors: 0,
        dimension: 0,
        namespaces: [],
      };
    }

    try {
      const index = this.client.index(this.indexName);
      const stats = await index.describeIndexStats();

      return {
        totalVectors: stats.totalRecordCount || 0,
        dimension: stats.dimension || 0,
        namespaces: Object.keys(stats.namespaces || {}),
      };
    } catch (error) {
      console.error("Error getting Pinecone index stats:", error);
      return {
        totalVectors: 0,
        dimension: 0,
        namespaces: [],
      };
    }
  }

  // Get specific documents by their IDs for a user
  async getDocumentsByIds(
    documentIds: string[],
    userId: string
  ): Promise<any[]> {
    console.log(
      `🔍 Starting getDocumentsByIds for ${documentIds.length} documents, userId: ${userId}`
    );

    if (!this.isEnabled || !this.client) {
      console.warn("❌ Pinecone not available, returning empty documents list");
      return [];
    }

    try {
      const allResults: any[] = [];
      const namespace = environment.pineconeNamespace || "default";
      const index = this.client.index(this.indexName).namespace(namespace);

      // Generate a small random vector for querying
      const randomVector = Array.from(
        { length: 1024 },
        () => Math.random() * 0.01
      );

      for (const docId of documentIds) {
        try {
          console.log(`🔍 Retrieving document: ${docId}`);

          // Try different filter combinations to find the document
          const filterOptions = [
            { userId: { $eq: userId }, llamaCloudId: { $eq: docId } },
            { userId: { $eq: userId }, id: { $eq: docId } },
            { userId: { $eq: userId }, filename: { $eq: docId } },
            { userId: { $eq: userId }, fileName: { $eq: docId } },
          ];

          let documentFound = false;
          for (const filter of filterOptions) {
            try {
              const queryResult = await index.query({
                vector: randomVector,
                topK: 100, // Get multiple chunks for the document
                includeMetadata: true,
                filter: filter,
              });

              if (queryResult.matches && queryResult.matches.length > 0) {
                console.log(
                  `✅ Found ${queryResult.matches.length} chunks for document ${docId} with filter:`,
                  filter
                );

                // Debug: Log the structure of the first match to understand the data format
                console.log(`🔍 Sample match structure:`, {
                  id: queryResult.matches[0].id,
                  score: queryResult.matches[0].score,
                  metadata: queryResult.matches[0].metadata,
                  hasValues: !!queryResult.matches[0].values,
                  valuesLength: queryResult.matches[0].values?.length || 0,
                });

                // Combine all chunks for this document
                // The content might be in different properties, try multiple options
                const documentContent = queryResult.matches
                  .map((match: any) => {
                    // Try to get content from various possible locations
                    const content =
                      match.metadata?.text ||
                      match.metadata?.content ||
                      match.pageContent ||
                      match.text ||
                      "";

                    // Log if we can't find content to help debug
                    if (!content && match.metadata) {
                      console.log(`⚠️ No content found in match:`, {
                        matchId: match.id,
                        metadataKeys: Object.keys(match.metadata),
                        sampleMetadata: match.metadata,
                      });
                    }

                    return content;
                  })
                  .filter((content) => content.trim().length > 0)
                  .join("\n\n");

                // Use the first match's metadata as the base
                const firstMatch = queryResult.matches[0];
                const documentData = {
                  id: docId,
                  content: documentContent,
                  metadata: {
                    ...firstMatch.metadata,
                    fileName:
                      firstMatch.metadata?.fileName ||
                      firstMatch.metadata?.filename ||
                      docId,
                    fileType: firstMatch.metadata?.fileType || "document",
                    fileSize: firstMatch.metadata?.fileSize || 0,
                    uploadedAt:
                      firstMatch.metadata?.uploadedAt ||
                      new Date().toISOString(),
                    userId: userId,
                    chunkCount: queryResult.matches.length,
                  },
                };

                allResults.push(documentData);
                documentFound = true;
                break; // Found the document, no need to try other filters
              }
            } catch (filterError) {
              console.log(
                `⚠️ Filter failed for document ${docId}:`,
                filterError
              );
            }
          }

          if (!documentFound) {
            console.warn(`❌ Document ${docId} not found for user ${userId}`);
          }
        } catch (docError) {
          console.error(`❌ Error retrieving document ${docId}:`, docError);
        }
      }

      console.log(
        `✅ Retrieved ${allResults.length} documents out of ${documentIds.length} requested`
      );

      // If we didn't get all documents, try using vectorStore as fallback
      if (allResults.length < documentIds.length && this.vectorStore) {
        console.log(`🔄 Trying vectorStore fallback for missing documents...`);

        const missingDocIds = documentIds.filter(
          (id) => !allResults.some((result) => result.id === id)
        );

        for (const docId of missingDocIds) {
          try {
            // Use vectorStore similarity search with document ID as query
            const vectorResults = await this.vectorStore.similaritySearch(
              docId, // Use document ID as search query
              10, // Get multiple chunks
              { userId: userId, llamaCloudId: docId } // Filter by user and document
            );

            if (vectorResults.length > 0) {
              console.log(
                `✅ Found ${vectorResults.length} chunks for ${docId} via vectorStore`
              );

              const documentContent = vectorResults
                .map((doc) => doc.pageContent)
                .filter((content) => content.trim().length > 0)
                .join("\n\n");

              const documentData = {
                id: docId,
                content: documentContent,
                metadata: {
                  ...vectorResults[0].metadata,
                  fileName:
                    vectorResults[0].metadata?.fileName ||
                    vectorResults[0].metadata?.filename ||
                    docId,
                  fileType: vectorResults[0].metadata?.fileType || "document",
                  fileSize: vectorResults[0].metadata?.fileSize || 0,
                  uploadedAt:
                    vectorResults[0].metadata?.uploadedAt ||
                    new Date().toISOString(),
                  userId: userId,
                  chunkCount: vectorResults.length,
                },
              };

              allResults.push(documentData);
            }
          } catch (vectorError) {
            console.error(
              `❌ VectorStore fallback failed for ${docId}:`,
              vectorError
            );
          }
        }
      }

      console.log(
        `✅ Final result: Retrieved ${allResults.length} documents out of ${documentIds.length} requested`
      );
      return allResults;
    } catch (error) {
      console.error("❌ Error in getDocumentsByIds:", error);
      return [];
    }
  }

  async healthCheck(): Promise<boolean> {
    if (!this.isEnabled) {
      return false;
    }

    try {
      if (!this.vectorStore) {
        return false;
      }

      const stats = await this.getIndexStats();
      return stats.totalVectors >= 0; // Basic health check
    } catch (error) {
      console.error("Pinecone health check failed:", error);
      return false;
    }
  }

  // Method to process LlamaCloud parsed documents and store in Pinecone
  async processLlamaCloudDocument(
    llamaCloudDocument: {
      id: string;
      content: string;
      metadata: any;
    },
    userId: string
  ): Promise<string[]> {
    console.log(`🚨 ENTRY: processLlamaCloudDocument called with:`, {
      id: llamaCloudDocument.id,
      contentLength: llamaCloudDocument.content.length,
      userId: userId,
      isEnabled: this.isEnabled,
      hasVectorStore: !!this.vectorStore,
      hasClient: !!this.client,
    });

    // Ensure vectorStore is properly initialized
    console.log(`🔄 Ensuring vectorStore is initialized...`);
    const isInitialized = await this.ensureVectorStoreInitialized();

    if (!isInitialized) {
      console.warn(
        "❌ Pinecone not available, skipping LlamaCloud document processing"
      );
      console.warn(
        `Debug: isEnabled=${this.isEnabled}, vectorStore=${!!this
          .vectorStore}, client=${!!this.client}`
      );
      return [`${llamaCloudDocument.id}-processed`];
    }

    try {
      console.log(`🔄 Processing document for Pinecone:`, {
        id: llamaCloudDocument.id,
        contentLength: llamaCloudDocument.content.length,
        userId: userId,
        metadata: llamaCloudDocument.metadata,
      });

      console.log(`📋 About to call chunkContent...`);
      // Chunk the document content for better retrieval
      const chunks = this.chunkContent(llamaCloudDocument.content, 1000, 200);

      console.log(`📦 Created ${chunks.length} chunks from document`);

      console.log(`📋 About to create pineconeDocuments array...`);
      const pineconeDocuments: PineconeDocument[] = chunks.map(
        (chunk, index) => ({
          id: `${llamaCloudDocument.id}-chunk-${index}`,
          content: chunk,
          metadata: {
            filename: llamaCloudDocument.metadata.fileName || "Unknown",
            fileName: llamaCloudDocument.metadata.fileName || "Unknown",
            fileType: llamaCloudDocument.metadata.fileType || "unknown",
            fileSize: llamaCloudDocument.metadata.fileSize || 0,
            uploadedAt: new Date().toISOString(),
            userId: userId,
            chunkIndex: index,
            pageNumber: llamaCloudDocument.metadata.pageNumber,
            llamaCloudId: llamaCloudDocument.id,
          },
        })
      );

      console.log(
        `💾 About to call storeDocuments with ${pineconeDocuments.length} document chunks...`
      );
      console.log(`📋 Sample chunk:`, {
        id: pineconeDocuments[0]?.id,
        contentLength: pineconeDocuments[0]?.content.length,
        metadata: pineconeDocuments[0]?.metadata,
      });

      const result = await this.storeDocuments(pineconeDocuments);
      console.log(`✅ Successfully stored document chunks. Result:`, result);

      return result;
    } catch (error) {
      console.error(
        "❌ Error processing LlamaCloud document for Pinecone:",
        error
      );
      throw error;
    }
  }

  private chunkContent(
    content: string,
    chunkSize: number,
    overlap: number
  ): string[] {
    // Sanitize content first to remove invalid unicode characters
    const sanitizedContent = this.sanitizeText(content);

    const chunks: string[] = [];
    let startIndex = 0;

    while (startIndex < sanitizedContent.length) {
      const endIndex = Math.min(
        startIndex + chunkSize,
        sanitizedContent.length
      );
      let chunk = sanitizedContent.slice(startIndex, endIndex);

      // Try to break at sentence boundaries
      if (endIndex < sanitizedContent.length) {
        const lastSentenceEnd = chunk.lastIndexOf(".");
        const lastNewline = chunk.lastIndexOf("\n");
        const breakPoint = Math.max(lastSentenceEnd, lastNewline);

        if (breakPoint > startIndex + chunkSize * 0.5) {
          chunk = sanitizedContent.slice(
            startIndex,
            startIndex + breakPoint + 1
          );
          startIndex = startIndex + breakPoint + 1 - overlap;
        } else {
          startIndex = endIndex - overlap;
        }
      } else {
        startIndex = endIndex;
      }

      const trimmedChunk = chunk.trim();
      if (trimmedChunk.length > 0) {
        chunks.push(trimmedChunk);
      }
    }

    return chunks;
  }

  // Getter to check if Pinecone is enabled
  get enabled(): boolean {
    return this.isEnabled;
  }
}

export const pineconeService = new PineconeService();
