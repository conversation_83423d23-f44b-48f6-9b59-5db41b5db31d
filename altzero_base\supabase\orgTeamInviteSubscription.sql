-- =============================================
-- COMMON FUNCTIONALITY: ORGANIZATION, TEAM, INVITE, SUBSCRIPTION
-- =============================================
-- Author: Generated file
-- Description: This file contains all SQL schemas and functions needed to implement
--              organization, team, invite, and subscription functionality.

-- =============================================
-- SECTION 1: CORE ORGANIZATION FUNCTIONALITY  
-- =============================================

-- -----------------------------
-- 1.1 ORGANIZATION SCHEMA
-- -----------------------------

CREATE TYPE "public"."user_role" AS ENUM ('retail', 'corporate');

-- Core Organizations Table
CREATE TABLE IF NOT EXISTS "public"."organisations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "created_by" "uuid" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "organisations_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "one_organisation_per_user" UNIQUE ("created_by"),
    CONSTRAINT "organisations_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id")
);

-- Organization Members Table
CREATE TABLE IF NOT EXISTS "public"."organisation_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organisation_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "role" "text" DEFAULT 'member'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "organisation_members_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "organisation_members_organisation_id_user_id_key" UNIQUE ("organisation_id", "user_id"),
    CONSTRAINT "organisation_members_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'member'::"text"]))),
    CONSTRAINT "organisation_members_organisation_id_fkey" FOREIGN KEY ("organisation_id") REFERENCES "public"."organisations"("id") ON DELETE CASCADE,
    CONSTRAINT "organisation_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE
);

-- Enable RLS on organization tables
ALTER TABLE "public"."organisations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."organisation_members" ENABLE ROW LEVEL SECURITY;

-- -----------------------------
-- 1.2 ORGANIZATION RLS POLICIES
-- -----------------------------

-- Organizations
CREATE POLICY "Enable read access for all users" 
ON "public"."organisations" 
FOR SELECT TO "authenticated" 
USING (true);

CREATE POLICY "Enable insert for authenticated users only" 
ON "public"."organisations" 
FOR INSERT TO "authenticated" 
WITH CHECK (true);

-- Organization Members
CREATE POLICY "Enable read access for all users" 
ON "public"."organisation_members" 
FOR SELECT 
USING (true);

CREATE POLICY "Enable insert for authenticated users only" 
ON "public"."organisation_members" 
FOR INSERT TO "authenticated" 
WITH CHECK (true);

-- -----------------------------
-- 1.3 ORGANIZATION FUNCTIONS
-- -----------------------------

-- Function to create a new organization and add creator as admin
CREATE OR REPLACE FUNCTION create_organization(
    org_name TEXT,
    org_description TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_org_id UUID;
    v_user_id UUID;
BEGIN
    -- Get current user ID
    SELECT auth.uid() INTO v_user_id;
    
    -- Create organization
    INSERT INTO organisations (name, description, created_by)
    VALUES (org_name, org_description, v_user_id)
    RETURNING id INTO v_org_id;
    
    -- Add creator as admin
    INSERT INTO organisation_members (organisation_id, user_id, role)
    VALUES (v_org_id, v_user_id, 'admin');
    
    RETURN v_org_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is organization admin
CREATE OR REPLACE FUNCTION is_org_admin(org_id UUID) 
RETURNS BOOLEAN AS $$
DECLARE
    is_admin BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM organisation_members
        WHERE organisation_id = org_id 
        AND user_id = auth.uid()
        AND role = 'admin'
    ) INTO is_admin;
    
    RETURN is_admin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all organizations for current user
CREATE OR REPLACE FUNCTION get_user_organizations()
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    created_at TIMESTAMPTZ,
    role TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.id,
        o.name,
        o.description,
        o.created_at,
        om.role
    FROM organisations o
    JOIN organisation_members om ON o.id = om.organisation_id
    WHERE om.user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- SECTION 2: TEAM & GROUP FUNCTIONALITY
-- =============================================

-- -----------------------------
-- 2.1 TEAMS/GROUPS SCHEMA
-- -----------------------------

CREATE TYPE "public"."entity_type" AS ENUM ('address', 'group');

-- Core Groups/Teams Table
CREATE TABLE IF NOT EXISTS "public"."groups" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "created_by" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "organisation_id" "uuid",
    "entities_ids" "uuid"[],
    CONSTRAINT "groups_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "groups_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id"),
    CONSTRAINT "groups_organisation_id_fkey" FOREIGN KEY ("organisation_id") REFERENCES "public"."organisations"("id") ON DELETE CASCADE
);

-- Group Members Table
CREATE TABLE IF NOT EXISTS "public"."group_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "group_id" "uuid",
    "user_id" "uuid",
    "role" "text" NOT NULL,
    "joined_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "group_members_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "group_members_group_id_user_id_key" UNIQUE ("group_id", "user_id"),
    CONSTRAINT "group_members_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'member'::"text"]))),
    CONSTRAINT "group_members_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE CASCADE,
    CONSTRAINT "group_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE
);

-- Organization Groups Mapping Table
CREATE TABLE IF NOT EXISTS "public"."organisation_groups" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organisation_id" "uuid",
    "group_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "organisation_groups_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "organisation_groups_organisation_id_group_id_key" UNIQUE ("organisation_id", "group_id"),
    CONSTRAINT "organisation_groups_organisation_id_fkey" FOREIGN KEY ("organisation_id") REFERENCES "public"."organisations"("id") ON DELETE CASCADE,
    CONSTRAINT "organisation_groups_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE CASCADE
);

-- Enable RLS on group tables
ALTER TABLE "public"."groups" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."group_members" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."organisation_groups" ENABLE ROW LEVEL SECURITY;

-- Create indexes
CREATE INDEX "idx_group_members_group_id" ON "public"."group_members" USING "btree" ("group_id");
CREATE INDEX "idx_groups_created_by" ON "public"."groups" USING "btree" ("created_by");

-- -----------------------------
-- 2.2 TEAM/GROUP RLS POLICIES
-- -----------------------------

-- Groups
CREATE POLICY "Enable read access for all users" 
ON "public"."groups" 
FOR SELECT 
USING (true);

CREATE POLICY "Users can create groups" 
ON "public"."groups" 
FOR INSERT 
WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Group creators can update their groups" 
ON "public"."groups" 
FOR UPDATE 
USING (auth.uid() = created_by);

CREATE POLICY "Group Admin can delete their groups" 
ON "public"."groups" 
FOR DELETE TO "authenticated" 
USING (true);

-- Group Members
CREATE POLICY "Enable read access for all users" 
ON "public"."group_members" 
FOR SELECT 
USING (true);

CREATE POLICY "Enable insert for authenticated users only" 
ON "public"."group_members" 
FOR INSERT TO "authenticated" 
WITH CHECK (true);

CREATE POLICY "Enable update for authentiocated users" 
ON "public"."group_members" 
FOR UPDATE TO "authenticated" 
USING (true) 
WITH CHECK (true);

CREATE POLICY "Users can leave groups" 
ON "public"."group_members" 
FOR DELETE 
USING (auth.uid() = user_id);

-- Organization Groups
CREATE POLICY "All users can able to delete" 
ON "public"."organisation_groups" 
FOR DELETE TO "authenticated" 
USING (true);

CREATE POLICY "Users can manage their organisation's groups" 
ON "public"."organisation_groups" 
USING (EXISTS (
    SELECT 1 
    FROM "public"."organisations" 
    WHERE organisations.id = organisation_groups.organisation_id 
    AND organisations.created_by = auth.uid()
));

-- -----------------------------
-- 2.3 GROUP/TEAM FUNCTIONS
-- -----------------------------

-- Function to create a new group/team
CREATE OR REPLACE FUNCTION create_group(
    group_name TEXT,
    group_description TEXT DEFAULT NULL,
    org_id UUID DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_group_id UUID;
    v_user_id UUID;
BEGIN
    -- Get current user ID
    SELECT auth.uid() INTO v_user_id;
    
    -- Create group
    INSERT INTO groups (name, description, created_by, organisation_id)
    VALUES (group_name, group_description, v_user_id, org_id)
    RETURNING id INTO v_group_id;
    
    -- Add creator as admin
    INSERT INTO group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'admin');
    
    -- If organization provided, create mapping
    IF org_id IS NOT NULL THEN
        INSERT INTO organisation_groups (organisation_id, group_id)
        VALUES (org_id, v_group_id);
    END IF;
    
    RETURN v_group_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is group admin
CREATE OR REPLACE FUNCTION is_group_admin(group_id UUID) 
RETURNS BOOLEAN AS $$
DECLARE
    is_admin BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM group_members
        WHERE group_id = $1
        AND user_id = auth.uid()
        AND role = 'admin'
    ) INTO is_admin;
    
    RETURN is_admin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all groups for current user
CREATE OR REPLACE FUNCTION get_user_groups()
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    created_at TIMESTAMPTZ,
    organisation_id UUID,
    role TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g.description,
        g.created_at,
        g.organisation_id,
        gm.role
    FROM groups g
    JOIN group_members gm ON g.id = gm.group_id
    WHERE gm.user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- SECTION 3: INVITATION SYSTEM
-- =============================================

-- -----------------------------
-- 3.1 INVITATION SCHEMA
-- -----------------------------

-- Group Invitations Table
CREATE TABLE IF NOT EXISTS "public"."group_invitations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "group_id" "uuid",
    "email" "text" NOT NULL,
    "status" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "group_invitations_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "group_invitations_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'accepted'::"text", 'rejected'::"text"]))),
    CONSTRAINT "group_invitations_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE CASCADE
);

-- Organization Invitations Table
CREATE TABLE IF NOT EXISTS "public"."organisation_invitations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organisation_id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "role" "text" DEFAULT 'member'::"text" NOT NULL,
    "status" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "organisation_invitations_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "organisation_invitations_role_check" CHECK (("role" = ANY (ARRAY['admin'::"text", 'member'::"text"]))),
    CONSTRAINT "organisation_invitations_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'accepted'::"text", 'rejected'::"text"]))),
    CONSTRAINT "organisation_invitations_organisation_id_fkey" FOREIGN KEY ("organisation_id") REFERENCES "public"."organisations"("id") ON DELETE CASCADE
);

-- User profiles table (for email lookup)
CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "id" "uuid" NOT NULL,
    "full_name" "text",
    "avatar_url" "text",
    "updated_at" timestamp with time zone,
    "about" "text",
    "role" "public"."user_role" DEFAULT 'retail'::"public"."user_role",
    "email" "text",
    "is_onboarded" boolean DEFAULT false,
    CONSTRAINT "profiles_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE
);

-- Enable RLS on invitation tables
ALTER TABLE "public"."group_invitations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."organisation_invitations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."profiles" ENABLE ROW LEVEL SECURITY;

-- -----------------------------
-- 3.2 INVITATION RLS POLICIES
-- -----------------------------

-- Group Invitations
CREATE POLICY "Group admins can create invitations" 
ON "public"."group_invitations" 
FOR INSERT TO "authenticated" 
WITH CHECK (EXISTS (
    SELECT 1 
    FROM "public"."group_members" 
    WHERE group_members.group_id = group_invitations.group_id 
    AND group_members.user_id = auth.uid() 
    AND group_members.role = 'admin'
));

CREATE POLICY "Group admins can view invitations" 
ON "public"."group_invitations" 
FOR SELECT TO "authenticated" 
USING (EXISTS (
    SELECT 1 
    FROM "public"."group_members" 
    WHERE group_members.group_id = group_invitations.group_id 
    AND group_members.user_id = auth.uid() 
    AND group_members.role = 'admin'
));

CREATE POLICY "Users can view their own invitations" 
ON "public"."group_invitations" 
FOR SELECT TO "authenticated" 
USING (email = (auth.jwt() ->> 'email'));

CREATE POLICY "Enable update for users based on email" 
ON "public"."group_invitations" 
FOR UPDATE 
USING (email = (auth.jwt() ->> 'email')) 
WITH CHECK (email = (auth.jwt() ->> 'email'));

-- Organization Invitations
CREATE POLICY "Organization admins can create invitations" 
ON "public"."organisation_invitations" 
FOR INSERT TO "authenticated" 
WITH CHECK (EXISTS (
    SELECT 1 
    FROM "public"."organisation_members" 
    WHERE organisation_members.organisation_id = organisation_invitations.organisation_id 
    AND organisation_members.user_id = auth.uid() 
    AND organisation_members.role = 'admin'
));

CREATE POLICY "Organization admins can view invitations" 
ON "public"."organisation_invitations" 
FOR SELECT TO "authenticated" 
USING (EXISTS (
    SELECT 1 
    FROM "public"."organisation_members" 
    WHERE organisation_members.organisation_id = organisation_invitations.organisation_id 
    AND organisation_members.user_id = auth.uid() 
    AND organisation_members.role = 'admin'
));

CREATE POLICY "Users can view their own org invitations" 
ON "public"."organisation_invitations" 
FOR SELECT TO "authenticated" 
USING (email = (auth.jwt() ->> 'email'));

CREATE POLICY "Enable update for users based on email" 
ON "public"."organisation_invitations" 
FOR UPDATE 
USING (email = (auth.jwt() ->> 'email')) 
WITH CHECK (email = (auth.jwt() ->> 'email'));

-- Profiles
CREATE POLICY "Public profiles are viewable by everyone" 
ON "public"."profiles" 
FOR SELECT 
USING (true);

CREATE POLICY "Users can insert their own profile" 
ON "public"."profiles" 
FOR INSERT 
WITH CHECK (auth.uid() = id OR auth.role() = 'service_role');

CREATE POLICY "Users can update own profile" 
ON "public"."profiles" 
FOR UPDATE 
USING (auth.uid() = id);

-- -----------------------------
-- 3.3 INVITATION FUNCTIONS
-- -----------------------------

-- Function to send a group invitation
CREATE OR REPLACE FUNCTION invite_to_group(
    group_id UUID,
    user_email TEXT
) RETURNS UUID AS $$
DECLARE
    v_invitation_id UUID;
    v_user_exists BOOLEAN;
    v_is_admin BOOLEAN;
BEGIN
    -- Check if user is group admin
    SELECT EXISTS (
        SELECT 1 FROM group_members
        WHERE group_id = $1
        AND user_id = auth.uid()
        AND role = 'admin'
    ) INTO v_is_admin;
    
    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Only group admins can send invitations';
    END IF;
    
    -- Check if user exists
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE email = user_email
    ) INTO v_user_exists;
    
    IF NOT v_user_exists THEN
        RAISE EXCEPTION 'User with email % does not exist', user_email;
    END IF;
    
    -- Create invitation
    INSERT INTO group_invitations (group_id, email, status)
    VALUES (group_id, user_email, 'pending')
    RETURNING id INTO v_invitation_id;
    
    RETURN v_invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to accept a group invitation
CREATE OR REPLACE FUNCTION accept_group_invitation(
    invitation_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_group_id UUID;
    v_email TEXT;
    v_user_id UUID;
    v_status TEXT;
BEGIN
    -- Get current user
    SELECT auth.uid() INTO v_user_id;
    
    -- Get invitation details
    SELECT 
        group_id, 
        email,
        status
    INTO 
        v_group_id, 
        v_email,
        v_status
    FROM group_invitations
    WHERE id = invitation_id;
    
    -- Check if invitation exists
    IF v_group_id IS NULL THEN
        RAISE EXCEPTION 'Invitation not found';
    END IF;
    
    -- Check if invitation is still pending
    IF v_status != 'pending' THEN
        RAISE EXCEPTION 'Invitation is no longer pending';
    END IF;
    
    -- Check if current user is the invited user
    IF v_email != (auth.jwt() ->> 'email') THEN
        RAISE EXCEPTION 'This invitation is not for you';
    END IF;
    
    -- Add user to group
    INSERT INTO group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'member')
    ON CONFLICT (group_id, user_id) DO NOTHING;
    
    -- Update invitation status
    UPDATE group_invitations
    SET status = 'accepted', updated_at = now()
    WHERE id = invitation_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send an organization invitation
CREATE OR REPLACE FUNCTION invite_to_organisation(
    org_id UUID,
    user_email TEXT,
    user_role TEXT DEFAULT 'member'
) RETURNS UUID AS $$
DECLARE
    v_invitation_id UUID;
    v_user_exists BOOLEAN;
    v_is_admin BOOLEAN;
BEGIN
    -- Check if user is org admin
    SELECT EXISTS (
        SELECT 1 FROM organisation_members
        WHERE organisation_id = $1
        AND user_id = auth.uid()
        AND role = 'admin'
    ) INTO v_is_admin;
    
    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Only organization admins can send invitations';
    END IF;
    
    -- Check if user exists
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE email = user_email
    ) INTO v_user_exists;
    
    IF NOT v_user_exists THEN
        RAISE EXCEPTION 'User with email % does not exist', user_email;
    END IF;
    
    -- Verify valid role
    IF user_role NOT IN ('admin', 'member') THEN
        RAISE EXCEPTION 'Invalid role: %', user_role;
    END IF;
    
    -- Create invitation
    INSERT INTO organisation_invitations (organisation_id, email, role, status)
    VALUES (org_id, user_email, user_role, 'pending')
    RETURNING id INTO v_invitation_id;
    
    RETURN v_invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to accept an organization invitation
CREATE OR REPLACE FUNCTION accept_organisation_invitation(
    invitation_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_org_id UUID;
    v_email TEXT;
    v_role TEXT;
    v_status TEXT;
    v_user_id UUID;
BEGIN
    -- Get current user
    SELECT auth.uid() INTO v_user_id;
    
    -- Get invitation details
    SELECT 
        organisation_id, 
        email,
        role,
        status
    INTO 
        v_org_id, 
        v_email,
        v_role,
        v_status
    FROM organisation_invitations
    WHERE id = invitation_id;
    
    -- Check if invitation exists
    IF v_org_id IS NULL THEN
        RAISE EXCEPTION 'Invitation not found';
    END IF;
    
    -- Check if invitation is still pending
    IF v_status != 'pending' THEN
        RAISE EXCEPTION 'Invitation is no longer pending';
    END IF;
    
    -- Check if current user is the invited user
    IF v_email != (auth.jwt() ->> 'email') THEN
        RAISE EXCEPTION 'This invitation is not for you';
    END IF;
    
    -- Add user to organization
    INSERT INTO organisation_members (organisation_id, user_id, role)
    VALUES (v_org_id, v_user_id, v_role)
    ON CONFLICT (organisation_id, user_id) DO NOTHING;
    
    -- Update invitation status
    UPDATE organisation_invitations
    SET status = 'accepted', updated_at = now()
    WHERE id = invitation_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 

-- =============================================
-- SECTION 4: SUBSCRIPTION SYSTEM
-- =============================================

-- -----------------------------
-- 4.1 SUBSCRIPTION SCHEMA
-- -----------------------------

-- Plan Tiers
CREATE TABLE IF NOT EXISTS "public"."plan_tiers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" text NOT NULL,
    "description" text,
    "tier_level" integer DEFAULT 0 NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "is_public" boolean DEFAULT true NOT NULL,
    "monthly_price_id" text,
    "yearly_price_id" text,
    "features" jsonb DEFAULT '{}' NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "plan_tiers_pkey" PRIMARY KEY ("id")
);

-- Resource Types
CREATE TABLE IF NOT EXISTS "public"."resource_types" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "code" text NOT NULL,
    "name" text NOT NULL,
    "description" text,
    "unit_label" text NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "resource_types_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "resource_types_code_key" UNIQUE ("code")
);

-- Plan Resource Limits
CREATE TABLE IF NOT EXISTS "public"."plan_resource_limits" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "plan_id" "uuid" NOT NULL,
    "resource_type_id" "uuid" NOT NULL,
    "base_limit" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "plan_resource_limits_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "plan_resource_limits_plan_id_resource_type_id_key" UNIQUE ("plan_id", "resource_type_id"),
    CONSTRAINT "plan_resource_limits_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "public"."plan_tiers"("id") ON DELETE CASCADE,
    CONSTRAINT "plan_resource_limits_resource_type_id_fkey" FOREIGN KEY ("resource_type_id") REFERENCES "public"."resource_types"("id") ON DELETE CASCADE
);

-- Subscription Records
CREATE TABLE IF NOT EXISTS "public"."subscription_records" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "owner_id" "uuid" NOT NULL,
    "owner_type" text NOT NULL,
    "plan_id" "uuid" NOT NULL,
    "status" text NOT NULL,
    "stripe_subscription_id" text,
    "stripe_customer_id" text,
    "current_period_start" timestamp with time zone,
    "current_period_end" timestamp with time zone,
    "cancel_at_period_end" boolean DEFAULT false,
    "trial_start" timestamp with time zone,
    "trial_end" timestamp with time zone,
    "billing_cycle" text DEFAULT 'monthly' NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "subscription_records_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "subscription_records_owner_id_owner_type_key" UNIQUE ("owner_id", "owner_type"),
    CONSTRAINT "subscription_records_owner_type_check" CHECK (("owner_type" = ANY (ARRAY['user'::"text", 'organisation'::"text"]))),
    CONSTRAINT "subscription_records_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'trialing'::"text", 'past_due'::"text", 'canceled'::"text", 'incomplete'::"text", 'incomplete_expired'::"text", 'unpaid'::"text"]))),
    CONSTRAINT "subscription_records_billing_cycle_check" CHECK (("billing_cycle" = ANY (ARRAY['monthly'::"text", 'yearly'::"text"]))),
    CONSTRAINT "subscription_records_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "public"."plan_tiers"("id") ON DELETE RESTRICT
);

-- Subscription Resource Additions
CREATE TABLE IF NOT EXISTS "public"."subscription_resource_additions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "resource_type_id" "uuid" NOT NULL,
    "additional_amount" integer DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "subscription_resource_additions_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "subscription_resource_additions_subscription_id_resource_type_id_key" UNIQUE ("subscription_id", "resource_type_id"),
    CONSTRAINT "subscription_resource_additions_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE,
    CONSTRAINT "subscription_resource_additions_resource_type_id_fkey" FOREIGN KEY ("resource_type_id") REFERENCES "public"."resource_types"("id") ON DELETE CASCADE
);

-- Subscription Resource Usage
CREATE TABLE IF NOT EXISTS "public"."subscription_resource_usage" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "resource_type_id" "uuid" NOT NULL,
    "current_usage" integer DEFAULT 0 NOT NULL,
    "last_updated" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "subscription_resource_usage_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "subscription_resource_usage_subscription_id_resource_type_id_key" UNIQUE ("subscription_id", "resource_type_id"),
    CONSTRAINT "subscription_resource_usage_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE,
    CONSTRAINT "subscription_resource_usage_resource_type_id_fkey" FOREIGN KEY ("resource_type_id") REFERENCES "public"."resource_types"("id") ON DELETE CASCADE
);

-- Resource Addon Packs
CREATE TABLE IF NOT EXISTS "public"."resource_addon_packs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "resource_type_id" "uuid" NOT NULL,
    "name" text NOT NULL,
    "description" text,
    "quantity" integer NOT NULL,
    "monthly_price_id" text,
    "yearly_price_id" text,
    "is_active" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "resource_addon_packs_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "resource_addon_packs_resource_type_id_fkey" FOREIGN KEY ("resource_type_id") REFERENCES "public"."resource_types"("id") ON DELETE CASCADE
);

-- Subscription Addons
CREATE TABLE IF NOT EXISTS "public"."subscription_addons" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "addon_pack_id" "uuid" NOT NULL,
    "stripe_item_id" text,
    "quantity" integer DEFAULT 1 NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "start_date" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "subscription_addons_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "subscription_addons_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE,
    CONSTRAINT "subscription_addons_addon_pack_id_fkey" FOREIGN KEY ("addon_pack_id") REFERENCES "public"."resource_addon_packs"("id") ON DELETE RESTRICT
);

-- Subscription Users
CREATE TABLE IF NOT EXISTS "public"."subscription_users" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "added_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "added_by" "uuid" NOT NULL,
    "status" text DEFAULT 'active' NOT NULL,
    CONSTRAINT "subscription_users_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "subscription_users_subscription_id_user_id_key" UNIQUE ("subscription_id", "user_id"),
    CONSTRAINT "subscription_users_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'suspended'::"text", 'pending'::"text"]))),
    CONSTRAINT "subscription_users_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE,
    CONSTRAINT "subscription_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE,
    CONSTRAINT "subscription_users_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "auth"."users"("id")
);

-- Billing Contacts
CREATE TABLE IF NOT EXISTS "public"."billing_contacts" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "email" text NOT NULL,
    "full_name" text,
    "phone" text,
    "billing_address" jsonb,
    "tax_id" text,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "billing_contacts_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "billing_contacts_subscription_id_key" UNIQUE ("subscription_id"),
    CONSTRAINT "billing_contacts_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE
);

-- Payment Methods
CREATE TABLE IF NOT EXISTS "public"."payment_methods" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "stripe_payment_method_id" text NOT NULL,
    "card_brand" text,
    "card_last4" text,
    "card_exp_month" integer,
    "card_exp_year" integer,
    "is_default" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "payment_methods_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "payment_methods_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE
);

-- Invoices
CREATE TABLE IF NOT EXISTS "public"."invoices" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "subscription_id" "uuid" NOT NULL,
    "stripe_invoice_id" text NOT NULL,
    "amount" integer NOT NULL,
    "currency" text DEFAULT 'usd' NOT NULL,
    "status" text NOT NULL,
    "invoice_date" timestamp with time zone NOT NULL,
    "due_date" timestamp with time zone,
    "paid_date" timestamp with time zone,
    "invoice_pdf_url" text,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    CONSTRAINT "invoices_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "invoices_status_check" CHECK (("status" = ANY (ARRAY['draft'::"text", 'open'::"text", 'paid'::"text", 'uncollectible'::"text", 'void'::"text"]))),
    CONSTRAINT "invoices_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX "idx_subscription_records_owner_id_owner_type" ON "public"."subscription_records" USING "btree" ("owner_id", "owner_type");
CREATE INDEX "idx_subscription_records_plan_id" ON "public"."subscription_records" USING "btree" ("plan_id");
CREATE INDEX "idx_subscription_records_status" ON "public"."subscription_records" USING "btree" ("status");
CREATE INDEX "idx_subscription_resource_additions_subscription_id" ON "public"."subscription_resource_additions" USING "btree" ("subscription_id");
CREATE INDEX "idx_subscription_resource_usage_subscription_id" ON "public"."subscription_resource_usage" USING "btree" ("subscription_id");
CREATE INDEX "idx_subscription_addons_subscription_id" ON "public"."subscription_addons" USING "btree" ("subscription_id");
CREATE INDEX "idx_subscription_users_subscription_id" ON "public"."subscription_users" USING "btree" ("subscription_id");
CREATE INDEX "idx_subscription_users_user_id" ON "public"."subscription_users" USING "btree" ("user_id");
CREATE INDEX "idx_invoices_subscription_id" ON "public"."invoices" USING "btree" ("subscription_id");
CREATE INDEX "idx_payment_methods_subscription_id" ON "public"."payment_methods" USING "btree" ("subscription_id");

-- Enable RLS on subscription tables
ALTER TABLE "public"."subscription_records" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."subscription_resource_usage" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."subscription_resource_additions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."subscription_addons" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."subscription_users" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."billing_contacts" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."payment_methods" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."invoices" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."plan_tiers" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."resource_types" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."plan_resource_limits" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."resource_addon_packs" ENABLE ROW LEVEL SECURITY;

-- -----------------------------
-- 4.2 SUBSCRIPTION RLS POLICIES
-- -----------------------------

-- User can read their own subscription
CREATE POLICY "Users can read their own subscription"
ON "public"."subscription_records"
FOR SELECT
USING (auth.uid() = owner_id AND owner_type = 'user');

-- User can read their organization subscription if they are a member
CREATE POLICY "Users can read organization subscriptions they belong to"
ON "public"."subscription_records"
FOR SELECT
USING (
    owner_type = 'organisation' AND EXISTS (
        SELECT 1 FROM organisation_members
        WHERE organisation_members.organisation_id = owner_id
        AND organisation_members.user_id = auth.uid()
    )
);

-- Allow users to view public plan tiers
CREATE POLICY "Anyone can view public plan tiers"
ON "public"."plan_tiers"
FOR SELECT
USING (is_public = true);

-- Allow authenticated users to view resource types
CREATE POLICY "Authenticated users can view resource types"
ON "public"."resource_types"
FOR SELECT
TO authenticated
USING (true);

-- Allow users to view plan resource limits
CREATE POLICY "Users can view plan resource limits"
ON "public"."plan_resource_limits"
FOR SELECT
USING (true);

-- Allow users to view addon packs
CREATE POLICY "Users can view addon packs"
ON "public"."resource_addon_packs"
FOR SELECT
USING (is_active = true);

-- -----------------------------
-- 4.3 SUBSCRIPTION FUNCTIONS
-- -----------------------------

-- Check if a subscription has enough resources
CREATE OR REPLACE FUNCTION "public"."check_subscription_resource_limit"(
    "subscription_id" "uuid", 
    "resource_type_code" "text", 
    "count_to_add" integer DEFAULT 1
) RETURNS boolean
LANGUAGE "plpgsql"
AS $$
DECLARE
    v_resource_type_id UUID;
    v_plan_id UUID;
    v_base_limit INTEGER;
    v_additional_amount INTEGER;
    v_current_usage INTEGER;
    v_total_limit INTEGER;
BEGIN
    -- Get the resource type ID
    SELECT id INTO v_resource_type_id
    FROM resource_types
    WHERE code = resource_type_code;
    
    IF v_resource_type_id IS NULL THEN
        RAISE EXCEPTION 'Resource type with code % not found', resource_type_code;
    END IF;
    
    -- Get the plan ID for this subscription
    SELECT plan_id INTO v_plan_id
    FROM subscription_records
    WHERE id = subscription_id;
    
    -- Get the base limit for this resource from the plan
    SELECT base_limit INTO v_base_limit
    FROM plan_resource_limits
    WHERE plan_id = v_plan_id AND resource_type_id = v_resource_type_id;
    
    -- Get any additional resources purchased
    SELECT additional_amount INTO v_additional_amount
    FROM subscription_resource_additions
    WHERE subscription_id = subscription_id AND resource_type_id = v_resource_type_id;
    
    -- Default to 0 if no additional resources
    IF v_additional_amount IS NULL THEN
        v_additional_amount := 0;
    END IF;
    
    -- Calculate total limit
    v_total_limit := v_base_limit + v_additional_amount;
    
    -- Get current usage
    SELECT current_usage INTO v_current_usage
    FROM subscription_resource_usage
    WHERE subscription_id = subscription_id AND resource_type_id = v_resource_type_id;
    
    -- If no usage record exists yet, assume zero
    IF v_current_usage IS NULL THEN
        v_current_usage := 0;
    END IF;
    
    -- Check if adding the requested count would exceed the limit
    RETURN (v_current_usage + count_to_add) <= v_total_limit;
END;
$$;

-- Update resource usage
CREATE OR REPLACE FUNCTION "public"."update_resource_usage"(
    "subscription_id" "uuid", 
    "resource_type_code" "text", 
    "change_amount" integer
) RETURNS boolean
LANGUAGE "plpgsql"
AS $$
DECLARE
    v_resource_type_id UUID;
    v_current_usage INTEGER;
    v_new_usage INTEGER;
BEGIN
    -- Get the resource type ID
    SELECT id INTO v_resource_type_id
    FROM resource_types
    WHERE code = resource_type_code;
    
    IF v_resource_type_id IS NULL THEN
        RAISE EXCEPTION 'Resource type with code % not found', resource_type_code;
    END IF;
    
    -- Try to update existing usage record
    UPDATE subscription_resource_usage
    SET 
        current_usage = GREATEST(0, current_usage + change_amount),
        last_updated = now()
    WHERE 
        subscription_id = $1 
        AND resource_type_id = v_resource_type_id
    RETURNING current_usage INTO v_new_usage;
    
    -- If no record exists, create one
    IF v_new_usage IS NULL THEN
        INSERT INTO subscription_resource_usage 
            (subscription_id, resource_type_id, current_usage, last_updated)
        VALUES 
            ($1, v_resource_type_id, GREATEST(0, change_amount), now())
        RETURNING current_usage INTO v_new_usage;
    END IF;
    
    RETURN TRUE;
END;
$$;

-- Apply an addon to a subscription
CREATE OR REPLACE FUNCTION "public"."apply_addon_to_subscription"(
    "subscription_id" "uuid", 
    "addon_pack_id" "uuid", 
    "quantity" integer DEFAULT 1
) RETURNS boolean
LANGUAGE "plpgsql"
AS $$
DECLARE
    v_addon_record_id UUID;
    v_resource_type_id UUID;
    v_addon_quantity INTEGER;
    v_total_addition INTEGER;
BEGIN
    -- Get the resource type and quantity for this addon
    SELECT 
        resource_type_id, 
        quantity
    INTO 
        v_resource_type_id, 
        v_addon_quantity
    FROM resource_addon_packs
    WHERE id = addon_pack_id;
    
    -- Calculate total resource addition
    v_total_addition := v_addon_quantity * quantity;
    
    -- Add the addon to the subscription
    INSERT INTO subscription_addons 
        (subscription_id, addon_pack_id, quantity)
    VALUES 
        (subscription_id, addon_pack_id, quantity)
    RETURNING id INTO v_addon_record_id;
    
    -- Update or insert resource additions
    INSERT INTO subscription_resource_additions 
        (subscription_id, resource_type_id, additional_amount)
    VALUES 
        (subscription_id, v_resource_type_id, v_total_addition)
    ON CONFLICT (subscription_id, resource_type_id) 
    DO UPDATE SET 
        additional_amount = subscription_resource_additions.additional_amount + v_total_addition,
        updated_at = now();
    
    RETURN TRUE;
END;
$$;

-- Create a default subscription for a new user
CREATE OR REPLACE FUNCTION "public"."create_default_subscription"() 
RETURNS "trigger"
LANGUAGE "plpgsql" 
SECURITY DEFINER
AS $$
DECLARE
    free_plan_id UUID;
    new_subscription_id UUID;
    resource_type_record RECORD;
BEGIN
    -- Get the free plan ID
    SELECT id INTO free_plan_id
    FROM public.plan_tiers
    WHERE tier_level = 0 AND is_active = true
    LIMIT 1;
    
    -- If no free plan exists, we can't create a subscription
    IF free_plan_id IS NULL THEN
        RAISE WARNING 'No free plan found, skipping default subscription creation';
        RETURN NEW;
    END IF;
    
    -- Create subscription record
    INSERT INTO subscription_records (
        owner_id,
        owner_type,
        plan_id,
        status
    ) VALUES (
        NEW.id,
        'user',
        free_plan_id,
        'active'
    ) RETURNING id INTO new_subscription_id;
    
    -- Initialize usage records for all resource types in the plan
    FOR resource_type_record IN 
        SELECT prl.resource_type_id
        FROM plan_resource_limits prl
        WHERE prl.plan_id = free_plan_id
    LOOP
        INSERT INTO subscription_resource_usage (
            subscription_id,
            resource_type_id,
            current_usage
        ) VALUES (
            new_subscription_id,
            resource_type_record.resource_type_id,
            0
        );
    END LOOP;
    
    RETURN NEW;
END;
$$;

-- Create a trigger to automatically create a free subscription for new users
CREATE TRIGGER create_free_subscription_for_new_user
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION create_default_subscription();

-- Check feature access for a subscription
CREATE OR REPLACE FUNCTION "public"."check_feature_access"(
    "subscription_id" "uuid", 
    "feature_path" "text", 
    "sub_property" "text" DEFAULT NULL
) RETURNS "jsonb"
LANGUAGE "plpgsql"
AS $$
DECLARE
    v_plan_id UUID;
    v_features JSONB;
    v_feature_value JSONB;
    v_result JSONB;
BEGIN
    -- Get the plan features
    SELECT 
        p.id, 
        p.features 
    INTO 
        v_plan_id, 
        v_features
    FROM subscription_records s
    JOIN plan_tiers p ON s.plan_id = p.id
    WHERE s.id = subscription_id;
    
    -- If no plan found, return null
    IF v_plan_id IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Get the feature value
    v_feature_value := v_features #> string_to_array(feature_path, '.');
    
    -- If a sub-property is specified, extract it
    IF sub_property IS NOT NULL AND v_feature_value IS NOT NULL THEN
        v_result := jsonb_build_object(sub_property, v_feature_value -> sub_property);
    ELSE
        v_result := v_feature_value;
    END IF;
    
    RETURN v_result;
END;
$$;

-- Get a summary of subscription resource usage
CREATE OR REPLACE FUNCTION "public"."get_subscription_usage_summary"(
    "subscription_id" "uuid"
) RETURNS "jsonb"
LANGUAGE "plpgsql"
AS $$
DECLARE
    v_plan_id UUID;
    v_result JSONB;
BEGIN
    -- Get the plan ID
    SELECT plan_id INTO v_plan_id
    FROM subscription_records
    WHERE id = subscription_id;
    
    -- Create a summary view of resource usage
    WITH resource_summary AS (
        SELECT 
            sru.subscription_id,
            sru.resource_type_id,
            rt.code AS resource_code,
            rt.name AS resource_name,
            rt.unit_label,
            COALESCE(prl.base_limit, 0) AS base_limit,
            COALESCE(sra.additional_amount, 0) AS additional_amount,
            (COALESCE(prl.base_limit, 0) + COALESCE(sra.additional_amount, 0)) AS total_limit,
            COALESCE(sru.current_usage, 0) AS current_usage,
            CASE 
                WHEN (COALESCE(prl.base_limit, 0) + COALESCE(sra.additional_amount, 0)) > 0 
                THEN ROUND((COALESCE(sru.current_usage, 0)::numeric / 
                     (COALESCE(prl.base_limit, 0) + COALESCE(sra.additional_amount, 0))::numeric) * 100) 
                ELSE 0 
            END AS percentage_used
        FROM 
            subscription_resource_usage sru
        JOIN 
            resource_types rt ON sru.resource_type_id = rt.id
        LEFT JOIN 
            plan_resource_limits prl ON prl.resource_type_id = rt.id AND prl.plan_id = v_plan_id
        LEFT JOIN 
            subscription_resource_additions sra ON sra.resource_type_id = rt.id AND sra.subscription_id = sru.subscription_id
        WHERE 
            sru.subscription_id = $1
    )
    
    SELECT 
        jsonb_build_object(
            'subscription_id', subscription_id,
            'plan_id', v_plan_id,
            'resources', jsonb_agg(
                jsonb_build_object(
                    'resource_type_id', rs.resource_type_id,
                    'code', rs.resource_code,
                    'name', rs.resource_name,
                    'unit_label', rs.unit_label,
                    'base_limit', rs.base_limit,
                    'additional', rs.additional_amount,
                    'total_limit', rs.total_limit,
                    'current_usage', rs.current_usage,
                    'percentage_used', rs.percentage_used,
                    'available', rs.total_limit - rs.current_usage
                )
            )
        ) INTO v_result
    FROM 
        resource_summary rs;
    
    RETURN v_result;
END;
$$; 

-- =============================================
-- SECTION 5: USAGE EXAMPLES AND DOCUMENTATION
-- =============================================

-- -----------------------------
-- 5.1 ORGANIZATION EXAMPLES
-- -----------------------------

/*
-- Creating a new organization
SELECT create_organization('My Company', 'A description of my company');

-- Checking if user is an organization admin
SELECT is_org_admin('organization-uuid-here');

-- Get all organizations for current user
SELECT * FROM get_user_organizations();
*/

-- -----------------------------
-- 5.2 TEAM/GROUP EXAMPLES
-- -----------------------------

/*
-- Creating a new team/group (standalone)
SELECT create_group('My Team', 'A description of my team');

-- Creating a team within an organization
SELECT create_group('My Team', 'A team in my organization', 'organization-uuid-here');

-- Checking if user is a group admin
SELECT is_group_admin('group-uuid-here');

-- Get all groups for current user
SELECT * FROM get_user_groups();
*/

-- -----------------------------
-- 5.3 INVITATION EXAMPLES
-- -----------------------------

/*
-- Inviting a user to a group
SELECT invite_to_group('group-uuid-here', '<EMAIL>');

-- Accepting a group invitation
SELECT accept_group_invitation('invitation-uuid-here');

-- Inviting a user to an organization
SELECT invite_to_organisation('organization-uuid-here', '<EMAIL>', 'member');

-- Accepting an organization invitation
SELECT accept_organisation_invitation('invitation-uuid-here');
*/

-- -----------------------------
-- 5.4 SUBSCRIPTION EXAMPLES
-- -----------------------------

/*
-- Checking resource limits
SELECT check_subscription_resource_limit('subscription-uuid-here', 'users', 1);

-- Updating resource usage
SELECT update_resource_usage('subscription-uuid-here', 'users', 1);

-- Adding an addon to subscription
SELECT apply_addon_to_subscription('subscription-uuid-here', 'addon-pack-uuid-here', 2);

-- Checking feature access
SELECT check_feature_access('subscription-uuid-here', 'features.can_export');

-- Getting subscription usage summary
SELECT get_subscription_usage_summary('subscription-uuid-here');
*/

-- =============================================
-- HOW IT WORKS
-- =============================================

/*
This system provides a complete solution for organizations, teams/groups, invitations, and 
subscription management. Here's how each component works:

1. ORGANIZATION FUNCTIONALITY:
   - Organizations are top-level entities that can contain users and groups
   - Each organization has members with roles (admin or member)
   - Organization admins can manage the organization, invite members, and create groups

2. TEAM/GROUP FUNCTIONALITY:
   - Groups are collections of users that can belong to an organization or stand alone
   - Groups have members with roles (admin or member)
   - Groups can be used to organize users and manage permissions
   - Group admins can invite members and manage the group

3. INVITATION SYSTEM:
   - Provides email-based invitations to both organizations and groups
   - Only admins can send invitations
   - Invitations have status tracking (pending, accepted, rejected)
   - Users must have a profile with matching email to accept invitations

4. SUBSCRIPTION SYSTEM:
   - Supports both user and organization subscriptions
   - Plans with tiered features and resource limits
   - Resource tracking and usage monitoring
   - Add-on packs for additional resources
   - Billing information, payment methods, and invoice tracking
   - Integration with Stripe for payment processing

INTEGRATING WITH YOUR APPLICATION:

1. SETUP DATABASE:
   - Execute this SQL file to create all necessary tables and functions
   - Create initial plan tiers, resource types, and resource limits

2. USER SIGNUP FLOW:
   - When users sign up, they automatically get a free subscription
   - They can create an organization and invite others
   - They can join existing organizations via invitations

3. SUBSCRIPTION MANAGEMENT:
   - Implement UI for users to upgrade/downgrade plans
   - Use the subscription functions to check resource limits before operations
   - Update resource usage when resources are consumed
   - Show usage metrics to users

4. TEAM COLLABORATION:
   - Allow users to create and join groups
   - Implement invitation flows for organizations and groups
   - Manage permissions based on roles in organizations and groups

This system is designed to be modular, so you can implement only the parts you need
while maintaining the ability to add other components later.
*/ 