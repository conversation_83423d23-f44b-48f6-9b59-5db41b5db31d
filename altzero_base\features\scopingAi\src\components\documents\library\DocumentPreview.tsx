import React from "react";
import { Button } from "@base/components/ui/button";
import { ProcessedDocument } from "../../../types/document-library";

interface DocumentPreviewProps {
  processedDocument: ProcessedDocument;
  onSave: () => void;
  onCancel: () => void;
  isLoading: boolean;
}

export function DocumentPreview({
  processedDocument,
  onSave,
  onCancel,
  isLoading,
}: DocumentPreviewProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto">
        <h2 className="text-xl font-bold mb-4">{processedDocument.title}</h2>
        <div className="prose max-w-none mb-4">
          <pre className="whitespace-pre-wrap">
            {processedDocument.fullContent}
          </pre>
        </div>
        <div className="flex gap-2">
          <Button onClick={onSave} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Template"}
          </Button>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
