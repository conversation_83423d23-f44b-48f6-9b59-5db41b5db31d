/**
 * API utility for making authenticated requests to the backend
 */

// Get API configuration from environment variables
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';
const API_KEY = import.meta.env.VITE_API_KEY;

/**
 * Make an authenticated API request
 * @param endpoint - API endpoint path (without leading slash)
 * @param options - Fetch options
 * @returns Response data
 */
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_URL}/${endpoint}`;
  
  // Add API key to headers
  const headers = {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY,
    ...options.headers,
  };
  
  const response = await fetch(url, {
    ...options,
    headers,
  });
  
  // Handle error responses
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error || `API request failed with status ${response.status}`
    );
  }
  
  // Parse JSON response
  return response.json();
}

/**
 * Make a GET request to the API
 * @param endpoint - API endpoint
 * @param queryParams - Query parameters object
 * @returns Response data
 */
export function get<T>(endpoint: string, queryParams?: Record<string, string>): Promise<T> {
  const url = queryParams
    ? `${endpoint}?${new URLSearchParams(queryParams)}`
    : endpoint;
    
  return apiRequest<T>(url, { method: 'GET' });
}

/**
 * Make a POST request to the API
 * @param endpoint - API endpoint
 * @param data - Request body data
 * @returns Response data
 */
export function post<T>(endpoint: string, data: any): Promise<T> {
  return apiRequest<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

/**
 * Make a PUT request to the API
 * @param endpoint - API endpoint
 * @param data - Request body data
 * @returns Response data
 */
export function put<T>(endpoint: string, data: any): Promise<T> {
  return apiRequest<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

/**
 * Make a DELETE request to the API
 * @param endpoint - API endpoint
 * @returns Response data
 */
export function del<T>(endpoint: string): Promise<T> {
  return apiRequest<T>(endpoint, { method: 'DELETE' });
} 