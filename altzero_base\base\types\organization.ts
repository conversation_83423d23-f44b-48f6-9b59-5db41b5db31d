/**
 * Organization related type definitions
 */

export type UserRole = "retail" | "corporate";

export type MemberRole = "admin" | "member";

export interface Organization {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  created_by: string;
  updated_at: string;
}

export interface OrganizationMember {
  id: string;
  organisation_id: string;
  user_id: string;
  role: MemberRole;
  created_at: string;
  // Include user info when joined with profiles
  full_name?: string;
  email?: string;
  avatar_url?: string;
}

export interface OrganizationInvitation {
  id: string;
  organisation_id: string;
  email: string;
  role: MemberRole;
  status: "pending" | "accepted" | "rejected";
  created_at: string;
  updated_at: string;
  // Include organization info when joined
  organization_name?: string;
}
