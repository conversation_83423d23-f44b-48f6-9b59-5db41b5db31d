import React from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@base/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@base/components/ui/card";
import { Badge } from "@base/components/ui/badge";
import { TemplateDocument } from "../../../types/document-library";

interface TemplateCardProps {
  template: TemplateDocument;
  onPreview: (id: string) => void;
}

export function TemplateCard({ template, onPreview }: TemplateCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <CardTitle className="text-lg">{template.title}</CardTitle>
        <CardDescription>{template.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground mb-3">
          Created: {template.date}
        </div>
        <div className="text-sm">
          <p className="font-medium mb-1">Sections:</p>
          <div className="flex flex-wrap gap-1">
            {template.sections.map((section: string, idx: number) => (
              <Badge key={idx} variant="secondary" className="text-xs">
                {section}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPreview(template.id)}
          >
            Preview
          </Button>
        </div>
        <Link to={`/documents/new?template=${template.id}`}>
          <Button size="sm">Use Template</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
