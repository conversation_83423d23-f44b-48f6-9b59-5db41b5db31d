export const PSEO_CONSTANTS = {
  AI_MODELS: {
    DEFAULT: 'gpt-4o-mini',
    PREMIUM: 'gpt-4o'
  },
  AUDIT_STATUS: {
    PENDING: 'pending',
    SCRAPING: 'scraping',
    ANALYZING: 'analyzing',
    COMPLETED: 'completed',
    FAILED: 'failed'
  } as const,
  STEP_STATUS: {
    PENDING: 'pending',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed'
  } as const,
  AUDIT_STEPS: {
    SETUP: 'setup',
    CLIENT_CREATION: 'client_creation',
    WEBSITE_CREATION: 'website_creation',
    SCRAPING: 'scraping',
    TECHNICAL_ANALYSIS: 'technical_analysis',
    CONTENT_ANALYSIS: 'content_analysis',
    AGGREGATION: 'aggregation'
  } as const,
  TIMEOUTS: {
    SCRAPING: 30000,
    AI_ANALYSIS: 120000
  },
  WEBSITE_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    ARCHIVED: 'archived'
  } as const,
  TABLE_NAMES: {
    CLIENTS: 'pseo_clients',
    WEBSITES: 'pseo_websites',
    AUDITS: 'pseo_audits',
    AUDIT_STEPS: 'pseo_audit_steps'
  } as const,
  ERROR_MESSAGES: {
    OPENAI_KEY_MISSING: 'OPENAI_API_KEY environment variable is required',
    SCRAPING_TIMEOUT: 'Request timeout during website scraping',
    TECHNICAL_ANALYSIS_FAILED: 'Technical analysis failed',
    CONTENT_ANALYSIS_FAILED: 'Content analysis failed',
    INVALID_URL: 'Invalid URL provided',
    CLIENT_NOT_FOUND: 'Client not found',
    WEBSITE_NOT_FOUND: 'Website not found',
    AUDIT_NOT_FOUND: 'Audit not found'
  } as const
} as const;

export const API_ENDPOINTS = {
  BASE: '/api/pseo',
  HEALTH: '/api/pseo/health',
  SCRAPE: '/api/pseo/scrape',
  OPENAI_STATUS: '/api/pseo/openai/status',
  OPENAI_TEST: '/api/pseo/openai/test',
  ANALYZE: '/api/pseo/analyze',
  ANALYZE_BATCH: '/api/pseo/analyze/batch',
  REPORT_GENERATE: '/api/pseo/report/generate',
  REPORT_CONVERT: '/api/pseo/report/convert-to-html',
  STATS: '/api/pseo/stats'
} as const;

export const PSEO_PROMPTS = {
  TECHNICAL_AUDIT: `You are the best SEO Manager in the country—a world-class expert in optimizing websites to rank on Google.
In this task, you will analyze the HTML code of a webpage and perform a detailed and structured On-Page Technical SEO Audit.

Audit Structure
You will review all technical SEO aspects of the page. Once completed, you will present your findings and recommendations in clear, organized bullet points, categorized into three sections:
- Critical Issues – Must be fixed immediately.
- Quick Wins – Easy fixes with a big impact.
- Opportunities for Improvement – Require more effort but offer potential benefits.

Ensure the output is properly formatted, clean, and highly readable. Do not include any introductory or explanatory text—only the audit findings.

Here is the content of my landing page: {content}`,

  CONTENT_AUDIT: `You are the best SEO Manager in the country—a world-class expert in optimizing websites to rank on Google.

In this task, you will analyze the content of the webpage and perform a detailed and structured SEO Content Audit.

Audit Structure
You will divide your audit in 2 parts:
- The first part is the Analysis
- The second is the Recommendations

In the Analysis, you will include:
- Content Quality Assessment – Evaluate the content's overall quality, accuracy, and relevance to the target audience.
- Keyword Research and Analysis – Identify primary and secondary keywords, keyword density, and keyword placement strategies.
- Readability Analysis – Assess the content's readability score using metrics such as Flesch-Kincaid Grade Level, Flesch Reading Ease, and Gunning-Fog Index.

In the Recommendations, you will present your recommendations and actionable suggestions in clear, organized bullet points. Recommendations must improve the rankings in Google but also the user engagement. 

Ensure the output is properly formatted, clean, and highly readable. Do not include any introductory or explanatory text—only the audit findings.

Here is the content of my landing page: {content}`
} as const;

export const PSEO_UI_TEXT = {
  NAVIGATION: {
    PSEO: 'pSEO',
    DASHBOARD: 'Dashboard',
    CLIENTS: 'Clients',
    WEBSITES: 'Websites',
    AUDITS: 'Audits',
    SETTINGS: 'Settings'
  },
  BUTTONS: {
    ADD_CLIENT: 'Add Client',
    ADD_WEBSITE: 'Add Website',
    START_AUDIT: 'Start SEO Audit',
    VIEW_REPORT: 'View Report',
    CANCEL: 'Cancel',
    SAVE: 'Save',
    DELETE: 'Delete',
    EDIT: 'Edit'
  },
  LABELS: {
    CLIENT_NAME: 'Client Name',
    CLIENT_EMAIL: 'Client Email',
    COMPANY: 'Company',
    WEBSITE_URL: 'Website URL',
    WEBSITE_NAME: 'Website Name',
    DOMAIN: 'Domain',
    STATUS: 'Status',
    CREATED_AT: 'Created',
    LAST_AUDIT: 'Last Audit',
    AUDIT_COUNT: 'Total Audits'
  },
  MESSAGES: {
    AUDIT_STARTED: 'SEO audit started successfully',
    AUDIT_COMPLETED: 'SEO audit completed',
    AUDIT_FAILED: 'SEO audit failed',
    CLIENT_CREATED: 'Client created successfully',
    WEBSITE_ADDED: 'Website added successfully',
    LOADING: 'Loading...',
    NO_DATA: 'No data available',
    CONFIRM_DELETE: 'Are you sure you want to delete this item?'
  },
  PLACEHOLDERS: {
    SEARCH_CLIENTS: 'Search clients...',
    SEARCH_WEBSITES: 'Search websites...',
    ENTER_URL: 'Enter website URL (e.g., https://example.com)',
    ENTER_CLIENT_NAME: 'Enter client name',
    ENTER_EMAIL: 'Enter email address'
  }
} as const;

// Type helpers for better TypeScript support
export type AuditStatus = typeof PSEO_CONSTANTS.AUDIT_STATUS[keyof typeof PSEO_CONSTANTS.AUDIT_STATUS];
export type StepStatus = typeof PSEO_CONSTANTS.STEP_STATUS[keyof typeof PSEO_CONSTANTS.STEP_STATUS];
export type WebsiteStatus = typeof PSEO_CONSTANTS.WEBSITE_STATUS[keyof typeof PSEO_CONSTANTS.WEBSITE_STATUS];
export type AuditStepName = typeof PSEO_CONSTANTS.AUDIT_STEPS[keyof typeof PSEO_CONSTANTS.AUDIT_STEPS]; 