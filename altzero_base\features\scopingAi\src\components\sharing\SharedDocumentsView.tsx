import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardH<PERSON>er,
  CardTitle,
} from "../../../../base/components/ui/card";
import { Badge } from "../../../../base/components/ui/badge";
import { Button } from "../../../../base/components/ui/button";
import { Avatar, AvatarFallback } from "../../../../base/components/ui/avatar";
import { useToast } from "../../../../base/hooks/use-toast";
import {
  FileText,
  Clock,
  User,
  Eye,
  MessageSquare,
  Edit,
  Shield,
  Crown,
  Calendar,
  ExternalLink,
} from "lucide-react";
import { getSharedDocuments } from "../../services/documentSharingService";
import { SharePermissionLevel, getPermissionIcon } from "../../types/sharing";
import { formatDistanceToNow } from "date-fns";

interface SharedDocument {
  document_id: string;
  title: string;
  client_name: string;
  status: string;
  permission_level: SharePermissionLevel;
  shared_by_name: string;
  shared_at: string;
  expires_at?: string;
}

export const SharedDocumentsView: React.FC = () => {
  const [documents, setDocuments] = useState<SharedDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadSharedDocuments();
  }, []);

  const loadSharedDocuments = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getSharedDocuments();
      setDocuments(data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load shared documents";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getPermissionIcon = (level: SharePermissionLevel) => {
    switch (level) {
      case "view":
        return <Eye className="h-4 w-4 text-blue-500" />;
      case "comment":
        return <MessageSquare className="h-4 w-4 text-green-500" />;
      case "edit":
        return <Edit className="h-4 w-4 text-orange-500" />;
      case "manage":
        return <Shield className="h-4 w-4 text-purple-500" />;
      case "admin":
        return <Crown className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "published":
        return "default";
      case "draft":
        return "secondary";
      case "in_review":
        return "outline";
      default:
        return "secondary";
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return "Unknown";
    }
  };

  const openDocument = (documentId: string) => {
    // Navigate to the document - adjust this URL according to your routing
    window.open(`/documents/${documentId}`, "_blank");
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">
            Shared Documents
          </h2>
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">
            Shared Documents
          </h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">
                Failed to load shared documents
              </p>
              <p className="text-sm mt-2">{error}</p>
              <Button
                onClick={loadSharedDocuments}
                variant="outline"
                className="mt-4"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">
            Shared Documents
          </h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No shared documents</p>
              <p className="text-sm mt-2">
                Documents shared with you will appear here
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Shared Documents</h2>
        <Badge variant="secondary" className="ml-2">
          {documents.length} document{documents.length !== 1 ? "s" : ""}
        </Badge>
      </div>

      <div className="grid gap-4">
        {documents.map((doc) => (
          <Card
            key={doc.document_id}
            className="hover:shadow-md transition-shadow"
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="h-5 w-5 text-primary" />
                    <h3
                      className="font-semibold text-lg hover:text-primary cursor-pointer"
                      onClick={() => openDocument(doc.document_id)}
                    >
                      {doc.title}
                    </h3>
                    {doc.expires_at && (
                      <Badge variant="outline" className="text-xs">
                        <Calendar className="h-3 w-3 mr-1" />
                        Expires {formatDate(doc.expires_at)}
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                    {doc.client_name && (
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>{doc.client_name}</span>
                      </div>
                    )}
                    <Badge
                      variant={getStatusBadgeVariant(doc.status)}
                      className="text-xs"
                    >
                      {doc.status}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {doc.shared_by_name?.charAt(0) || "?"}
                        </AvatarFallback>
                      </Avatar>
                      <span>Shared by {doc.shared_by_name}</span>
                      <span>•</span>
                      <Clock className="h-4 w-4" />
                      <span>{formatDate(doc.shared_at)}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 text-sm">
                        {getPermissionIcon(doc.permission_level)}
                        <span className="capitalize">
                          {doc.permission_level}
                        </span>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openDocument(doc.document_id)}
                      >
                        <ExternalLink className="h-4 w-4 mr-1" />
                        Open
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
