import express from 'express';
import { BackendPlugin } from '../../../plugins/loader';
import pseoRoutes from '../routes/pseo';

// Create the backend plugin for pSEO
const pseoBackendPlugin: BackendPlugin = {
  router: pseoRoutes,
  config: {
    name: 'pSEO API',
    version: '1.0.0',
    apiPrefix: '/api/pseo'
  },
  initialize: async () => {
    console.log('📊 pSEO backend plugin initialized');
  },
  cleanup: async () => {
    console.log('📊 pSEO backend plugin cleaned up');
  },
  healthCheck: async () => {
    try {
      return true;
    } catch (error) {
      console.error('pSEO backend health check failed:', error);
      return false;
    }
  }
};

export default pseoBackendPlugin; 