import fs from 'fs';
import path from 'path';

export interface ProcessedDocument {
  id: string;
  title: string;
  content: string;
  type: string;
  size: number;
  metadata: Record<string, unknown>;
}

export interface DocumentProcessingOptions {
  extractText?: boolean;
  preserveFormatting?: boolean;
  maxSize?: number;
  allowedTypes?: string[];
}

export class DocumentProcessorService {
  private readonly defaultOptions: DocumentProcessingOptions = {
    extractText: true,
    preserveFormatting: false,
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['pdf', 'doc', 'docx', 'txt', 'md']
  };

  constructor(private options: DocumentProcessingOptions = {}) {
    this.options = { ...this.defaultOptions, ...options };
  }

  /**
   * Process a document file and extract content
   */
  async processDocument(
    filePath: string,
    originalName: string,
    options?: DocumentProcessingOptions
  ): Promise<ProcessedDocument> {
    try {
      const processingOptions = { ...this.options, ...options };
      
      console.log(`📄 Processing document: ${originalName}`);
      
      // Check file size
      const stats = fs.statSync(filePath);
      if (stats.size > (processingOptions.maxSize || this.defaultOptions.maxSize!)) {
        throw new Error(`File size exceeds maximum allowed size of ${processingOptions.maxSize} bytes`);
      }

      // Get file extension
      const ext = path.extname(originalName).toLowerCase().replace('.', '');
      
      // Check allowed types
      if (processingOptions.allowedTypes && !processingOptions.allowedTypes.includes(ext)) {
        throw new Error(`File type '${ext}' is not allowed`);
      }

      let content = '';
      let metadata: Record<string, unknown> = {
        originalName,
        fileType: ext,
        fileSize: stats.size,
        processedAt: new Date().toISOString(),
        processingMethod: 'basic'
      };

      // Process based on file type
      switch (ext) {
        case 'txt':
        case 'md':
          content = await this.processTextFile(filePath);
          break;
        case 'pdf':
          content = await this.processPdfFile(filePath);
          metadata.processingMethod = 'pdf-extraction';
          break;
        case 'doc':
        case 'docx':
          content = await this.processWordFile(filePath);
          metadata.processingMethod = 'word-extraction';
          break;
        default:
          // Try to read as text
          content = await this.processTextFile(filePath);
          metadata.processingMethod = 'fallback-text';
      }

      // Clean and validate content
      content = this.cleanContent(content);
      
      if (!content || content.trim().length === 0) {
        throw new Error('No readable content found in document');
      }

      const processedDoc: ProcessedDocument = {
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: originalName,
        content,
        type: ext,
        size: stats.size,
        metadata: {
          ...metadata,
          contentLength: content.length,
          wordCount: this.countWords(content)
        }
      };

      console.log(`✅ Document processed successfully: ${processedDoc.id}`);
      console.log(`📊 Content length: ${content.length} characters, Word count: ${metadata.wordCount}`);

      return processedDoc;
    } catch (error: any) {
      console.error(`❌ Error processing document ${originalName}:`, error);
      throw new Error(`Document processing failed: ${error.message}`);
    }
  }

  /**
   * Process text files (txt, md)
   */
  private async processTextFile(filePath: string): Promise<string> {
    try {
      return fs.readFileSync(filePath, 'utf-8');
    } catch (error: any) {
      throw new Error(`Failed to read text file: ${error.message}`);
    }
  }

  /**
   * Process PDF files
   */
  private async processPdfFile(filePath: string): Promise<string> {
    try {
      // For now, return a placeholder - in production, you'd use a PDF parsing library
      console.log('⚠️ PDF processing not implemented - using placeholder');
      return `[PDF Content from ${path.basename(filePath)}]\n\nThis is a placeholder for PDF content extraction. In production, this would use a PDF parsing library to extract the actual text content.`;
    } catch (error: any) {
      throw new Error(`Failed to process PDF file: ${error.message}`);
    }
  }

  /**
   * Process Word documents (doc, docx)
   */
  private async processWordFile(filePath: string): Promise<string> {
    try {
      // For now, return a placeholder - in production, you'd use a Word parsing library
      console.log('⚠️ Word document processing not implemented - using placeholder');
      return `[Word Document Content from ${path.basename(filePath)}]\n\nThis is a placeholder for Word document content extraction. In production, this would use a Word parsing library to extract the actual text content.`;
    } catch (error: any) {
      throw new Error(`Failed to process Word file: ${error.message}`);
    }
  }

  /**
   * Clean and normalize content
   */
  private cleanContent(content: string): string {
    return content
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove control characters
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Normalize line breaks
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Remove excessive line breaks
      .replace(/\n{3,}/g, '\n\n')
      // Trim
      .trim();
  }

  /**
   * Count words in content
   */
  private countWords(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Validate file type
   */
  isValidFileType(filename: string): boolean {
    const ext = path.extname(filename).toLowerCase().replace('.', '');
    return this.options.allowedTypes?.includes(ext) || false;
  }

  /**
   * Get supported file types
   */
  getSupportedTypes(): string[] {
    return this.options.allowedTypes || [];
  }

  /**
   * Check if file size is within limits
   */
  isValidFileSize(size: number): boolean {
    return size <= (this.options.maxSize || this.defaultOptions.maxSize!);
  }

  /**
   * Get maximum file size
   */
  getMaxFileSize(): number {
    return this.options.maxSize || this.defaultOptions.maxSize!;
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  /**
   * Extract metadata from processed document
   */
  extractMetadata(doc: ProcessedDocument): Record<string, unknown> {
    return {
      id: doc.id,
      title: doc.title,
      type: doc.type,
      size: doc.size,
      sizeFormatted: this.formatFileSize(doc.size),
      contentLength: doc.content.length,
      wordCount: this.countWords(doc.content),
      ...doc.metadata
    };
  }
}

export const documentProcessorService = new DocumentProcessorService();
