import React from 'react';
import { RouteObject } from 'react-router-dom';
import { PluginModule, NavigationItem } from '../../plugins/types';
import CRMDashboard from './pages/CRMDashboard';
import ContactManagement from './pages/ContactManagement';
import CompanyManagement from './pages/CompanyManagement';
import OpportunityManagement from './pages/OpportunityManagement';
import ActivityManagement from './pages/ActivityManagement';
import EventManagement from './pages/EventManagement';
import ContactGroupManagement from './pages/ContactGroupManagement';
import RegionManagement from './pages/RegionManagement';
import PipelineManagement from './pages/PipelineManagement';
import PipelineStageManagement from './pages/PipelineStageManagement';
import JobManagement from './pages/JobManagement';
import JobApplicationManagement from './pages/JobApplicationManagement';

// Define routes for the CRM plugin
const routes: RouteObject[] = [
  {
    path: '/crm',
    element: React.createElement(CRMDashboard)
  },
  {
    path: '/crm/dashboard',
    element: React.createElement(CRMDashboard)
  },
  {
    path: '/crm/contacts',
    element: React.createElement(ContactManagement)
  },
  {
    path: '/crm/companies',
    element: React.createElement(CompanyManagement)
  },
  {
    path: '/crm/opportunities',
    element: React.createElement(OpportunityManagement)
  },
  {
    path: '/crm/activities',
    element: React.createElement(ActivityManagement)
  },
  {
    path: '/crm/events',
    element: React.createElement(EventManagement)
  },
  {
    path: '/crm/contact-groups',
    element: React.createElement(ContactGroupManagement)
  },
  {
    path: '/crm/regions',
    element: React.createElement(RegionManagement)
  },
  {
    path: '/crm/pipelines',
    element: React.createElement(PipelineManagement)
  },
  {
    path: '/crm/pipeline-stages',
    element: React.createElement(PipelineStageManagement)
  },
  {
    path: '/crm/jobs',
    element: React.createElement(JobManagement)
  },
  {
    path: '/crm/job-applications',
    element: React.createElement(JobApplicationManagement)
  }
];

// Define navigation items
const navigation: NavigationItem[] = [
  {
    name: 'CRM',
    route: '/crm',
    icon: 'Users',
    order: 4,
    permissions: ['crm:read'],
    children: [
      {
        name: 'Dashboard',
        route: '/crm/dashboard',
        icon: 'BarChart3',
        order: 1,
        permissions: ['crm:read']
      },
      {
        name: 'Contacts',
        route: '/crm/contacts',
        icon: 'User',
        order: 2,
        permissions: ['crm:read']
      },
      {
        name: 'Companies',
        route: '/crm/companies',
        icon: 'Building',
        order: 3,
        permissions: ['crm:read']
      },
      {
        name: 'Opportunities',
        route: '/crm/opportunities',
        icon: 'Target',
        order: 4,
        permissions: ['crm:read']
      },
      {
        name: 'Activities',
        route: '/crm/activities',
        icon: 'Activity',
        order: 5,
        permissions: ['crm:read']
      },
      {
        name: 'Events',
        route: '/crm/events',
        icon: 'Calendar',
        order: 6,
        permissions: ['crm:read']
      }
    ]
  }
];

// CRM plugin module
const crmPlugin: PluginModule = {
  routes,
  navigation,
  providers: [], // No specific providers for CRM yet
  config: {
    name: 'CRM',
    version: '1.0.0',
    description: 'Customer Relationship Management system with contacts, companies, opportunities, and activities'
  },
  initialize: async () => {
    console.log('🏢 CRM plugin initialized');
  },
  cleanup: async () => {
    console.log('🏢 CRM plugin cleaned up');
  }
};

export default crmPlugin;
