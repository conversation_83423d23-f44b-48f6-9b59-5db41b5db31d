import React from 'react';
import PSEOLayout from '../components/PSEOLayout';

interface PSEOLayoutWrapperProps {
  children: React.ReactNode;
  stats?: {
    totalClients: number;
    totalWebsites: number;
    totalAudits: number;
    totalAnalysisJobs: number;
  };
  fullSiteAnalysisResults?: any[];
  contentOpportunities?: any[];
  keywordResearch?: any[];
}

/**
 * Hook that provides a consistent layout wrapper for pSEO pages
 * with fixed side navigation
 */
export const usePSEOLayout = () => {
  const PSEOLayoutWrapper: React.FC<PSEOLayoutWrapperProps> = ({
    children,
    stats = {
      totalClients: 0,
      totalWebsites: 0,
      totalAudits: 0,
      totalAnalysisJobs: 0
    },
    fullSiteAnalysisResults = [],
    contentOpportunities = [],
    keywordResearch = []
  }) => {
    return (
      <PSEOLayout
        stats={stats}
        fullSiteAnalysisResults={fullSiteAnalysisResults}
        contentOpportunities={contentOpportunities}
        keywordResearch={keywordResearch}
      >
        {children}
      </PSEOLayout>
    );
  };

  return { PSEOLayoutWrapper };
};

/**
 * Simple wrapper component for pages that don't need dynamic navigation data
 */
export const SimplePSEOLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { PSEOLayoutWrapper } = usePSEOLayout();
  
  return (
    <PSEOLayoutWrapper>
      {children}
    </PSEOLayoutWrapper>
  );
};

export default usePSEOLayout;
