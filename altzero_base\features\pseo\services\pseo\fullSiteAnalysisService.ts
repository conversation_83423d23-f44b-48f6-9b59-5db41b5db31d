// =====================================================
// FULL SITE ANALYSIS SERVICE - BACKEND API CLIENT
// =====================================================

import { supabase } from '../../../../base/utils/supabaseClient';
import { API_URL } from '../../../../base/utils/constants';

// Simplified interfaces for API responses
interface PSEOWebsite {
  id: string;
  client_id: string;
  url: string;
  domain: string;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface FullSiteAnalysisOptions {
  analysisTypes: string[];
  userId: string;
  websiteId: string;
  priority?: number;
  useRealTrafficData?: boolean;
  includeCompetitorAnalysis?: boolean;
  generateContent?: boolean;
}

interface FullSiteAnalysisJob {
  id: string;
  websiteId: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentStep: string;
  startedAt: string;
  completedAt?: string;
  results?: Record<string, any>;
  error?: string;
}

interface FullSiteAnalysisResults {
  jobId: string;
  websiteId: string;
  analysisTypes: string[];
  results: {
    pageDiscovery?: any;
    keywordResearch?: any;
    contentGeneration?: any;
    backlinkAnalysis?: any;
  };
  summary: {
    totalPages: number;
    totalKeywords: number;
    contentOpportunities: number;
    backlinkProfile: any;
  };
  completedAt: string;
}

class FullSiteAnalysisService {
  private apiBaseUrl = `${API_URL}/api/pseo`;
  
  private logger = {
    info: (message: string, data?: any) => console.log(`[FullSiteAnalysis] ${message}`, data),
    error: (message: string, error?: Error) => console.error(`[FullSiteAnalysis] ${message}`, error),
    warn: (message: string, data?: any) => console.warn(`[FullSiteAnalysis] ${message}`, data)
  };

  /**
   * Start a full site analysis via backend API
   */
  async startFullSiteAnalysis(
    website: PSEOWebsite,
    options: FullSiteAnalysisOptions
  ): Promise<{ jobId: string; status: string }> {
    try {
      this.logger.info('🚀 Starting Full Site Analysis via Backend API', {
        website_id: website.id,
        domain: website.domain,
        analysis_types: options.analysisTypes,
        user_id: options.userId
      });

      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      // Call backend API to start analysis
      const response = await fetch(`${this.apiBaseUrl}/full-site-analysis/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          websiteId: website.id,
          analysisTypes: options.analysisTypes,
          options: {
            useRealTrafficData: options.useRealTrafficData || false,
            includeCompetitorAnalysis: options.includeCompetitorAnalysis || false,
            generateContent: options.generateContent || false,
            priority: options.priority || 1
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      this.logger.info('✅ Analysis started successfully', {
        job_id: result.jobId,
        status: result.status
      });

      return {
        jobId: result.jobId,
        status: result.status
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Failed to start full site analysis', error as Error);
      throw new Error(`Failed to start analysis: ${errorMessage}`);
    }
  }

  /**
   * Get job status via backend API
   */
  async getJobStatus(jobId: string): Promise<FullSiteAnalysisJob | null> {
    try {
      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${this.apiBaseUrl}/full-site-analysis/status/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const job = await response.json();
      return {
        id: job.jobId,
        websiteId: job.websiteId,
        status: job.status,
        progress: job.progress,
        currentStep: job.currentStep,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        results: job.results,
        error: job.error
      };

    } catch (error) {
      this.logger.error(`Failed to get job status for ${jobId}`, error as Error);
      throw error;
    }
  }

  /**
   * Cancel analysis job via backend API
   */
  async cancelAnalysis(jobId: string): Promise<void> {
    try {
      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${this.apiBaseUrl}/full-site-analysis/cancel/${jobId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      this.logger.info('✅ Analysis cancelled successfully', { job_id: jobId });

    } catch (error) {
      this.logger.error(`Failed to cancel analysis ${jobId}`, error as Error);
      throw error;
    }
  }

  /**
   * Get analysis results via backend API
   */
  async getAnalysisResults(jobId: string): Promise<FullSiteAnalysisResults | null> {
    try {
      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${this.apiBaseUrl}/full-site-analysis/results/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const results = await response.json();
      return results;

    } catch (error) {
      this.logger.error(`Failed to get analysis results for ${jobId}`, error as Error);
      throw error;
    }
  }

  /**
   * Get analysis history for a user via backend API
   */
  async getAnalysisHistory(userId: string): Promise<FullSiteAnalysisJob[]> {
    try {
      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${this.apiBaseUrl}/full-site-analysis/history/${userId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const history = await response.json();
      return history.map((job: any) => ({
        id: job.jobId,
        websiteId: job.websiteId,
        status: job.status,
        progress: job.progress,
        currentStep: job.currentStep,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        results: job.results,
        error: job.error
      }));

    } catch (error) {
      this.logger.error(`Failed to get analysis history for user ${userId}`, error as Error);
      throw error;
    }
  }

  /**
   * Utility method for backwards compatibility with existing UI code
   */
  calculateProgress(jobData: any): number {
    if (!jobData) return 0;
    return jobData.progress || 0;
  }

  /**
   * Utility method for backwards compatibility with existing UI code
   */
  getCurrentStep(jobData: any): string {
    if (!jobData) return 'Initializing...';
    return jobData.currentStep || jobData.status || 'Processing...';
  }
}

export const fullSiteAnalysisService = new FullSiteAnalysisService(); 