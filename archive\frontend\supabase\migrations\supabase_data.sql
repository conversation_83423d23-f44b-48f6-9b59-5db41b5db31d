-- Insert section definitions
INSERT INTO public.section_definitions (id, title, description, default_order, is_default)
VALUES
  ('section-1', 'Executive Summary', 'A brief overview of the project, its objectives, and expected outcomes.', 1, true),
  ('section-2', 'Project Background', 'Overview of the client, industry context, and reasons for initiating the project.', 2, true),
  ('section-3', 'Scope of Work', 'Detailed description of what is included and excluded from the project scope.', 3, true),
  ('section-4', 'Requirements & Deliverables', 'Specific requirements, features, and deliverables that will be produced.', 4, true),
  ('section-5', 'Timeline & Milestones', 'Project schedule, key dates, phases, and milestones.', 5, true),
  ('section-6', 'Budget & Resources', 'Cost breakdown, resource allocation, and financial considerations.', 6, true),
  ('section-7', 'Technical Requirements', 'Technical specifications, platforms, and architectural considerations.', 7, true),
  ('section-8', 'Assumptions & Constraints', 'Key assumptions made and constraints that may affect the project.', 8, true),
  ('section-9', 'Risks & Mitigations', 'Potential risks, challenges, and mitigation strategies.', 9, true),
  ('section-10', 'Success Criteria', 'Measurable criteria that define project success.', 10, true);

-- Insert default prompt templates
INSERT INTO public.prompt_templates (id, name, description, content, variables)
VALUES
  (
    gen_random_uuid(), 
    'General Project Scope', 
    'A comprehensive prompt for general project scoping documents',
    'Create a detailed scoping document for the following project: {{projectName}}. Include sections for executive summary, project background, scope of work, requirements, timeline, budget, technical specifications, assumptions, risks, and success criteria.',
    '["projectName"]'
  ),
  (
    gen_random_uuid(), 
    'Technical Project Scope', 
    'A technical-focused prompt for software development projects',
    'Create a technical scoping document for the software project named: {{projectName}}. This should be a comprehensive document with focus on technical requirements, architecture, integration points, and development considerations. Include detailed sections on system design, data models, API specifications, and technical dependencies.',
    '["projectName"]'
  ),
  (
    gen_random_uuid(), 
    'Marketing Campaign Scope', 
    'A prompt template for marketing campaign scoping',
    'Create a marketing campaign scoping document for {{campaignName}}. This document should outline the campaign strategy, target audience, messaging, channels, timeline, budget, KPIs, and expected outcomes.',
    '["campaignName", "targetAudience"]'
  ),
  (
    gen_random_uuid(), 
    'Business Analysis Scope', 
    'A business-focused scope document template',
    'Create a detailed business analysis document for {{projectName}} that focuses on business requirements, stakeholder analysis, process mapping, and organizational impact assessment. Include relevant business KPIs, ROI considerations, and market analysis.',
    '["projectName", "industry", "companySize"]'
  );

-- Insert default scope templates
INSERT INTO public.scope_templates (id, name, description, content)
VALUES
  (
    gen_random_uuid(),
    'Web Application',
    'Template for web application scoping',
    '{"projectType": "Web Application", "timeline": "8-12 weeks"}'
  ),
  (
    gen_random_uuid(),
    'Mobile App',
    'Template for mobile app development',
    '{"projectType": "Mobile App", "timeline": "12-16 weeks"}'
  ),
  (
    gen_random_uuid(),
    'E-commerce Platform',
    'Template for e-commerce platform development',
    '{"projectType": "E-commerce Platform", "timeline": "10-14 weeks"}'
  ),
  (
    gen_random_uuid(),
    'Enterprise Software',
    'Template for enterprise software development',
    '{"projectType": "Enterprise Software", "timeline": "16-24 weeks"}'
  ); 