# RapidAPI Gateway Setup for pSEO

## Overview

The RapidAPI Gateway provides unified access to multiple SEO APIs through a single interface, reducing complexity and providing fallback options for keyword research.

## Supported Providers

### Available through RapidAPI:
- **Ubersuggest** - Keyword suggestions and search volume data
- **Semrush** - Professional SEO metrics and competitor analysis  
- **Ahrefs** - Backlink analysis and keyword difficulty
- **Moz** - Domain authority and keyword analysis

## Environment Configuration

### Required Environment Variables

```bash
# RapidAPI Gateway Configuration
RAPIDAPI_KEY=your_rapidapi_key_here
RAPIDAPI_HOST=rapidapi.com

# SEO APIs via RapidAPI
RAPIDAPI_UBERSUGGEST_HOST=ubersuggest-keyword-research.p.rapidapi.com
RAPIDAPI_SEMRUSH_HOST=semrush-keyword-research.p.rapidapi.com
RAPIDAPI_AHREFS_HOST=ahrefs-keyword-research.p.rapidapi.com
RAPIDAPI_MOZ_HOST=moz-keyword-research.p.rapidapi.com

# Enable/Disable specific APIs
RAPIDAPI_UBERSUGGEST_ENABLED=true
RAPIDAPI_SEMRUSH_ENABLED=true
RAPIDAPI_AHREFS_ENABLED=false
RAPIDAPI_MOZ_ENABLED=false
```

### Getting Your RapidAPI Key

1. **Sign up at RapidAPI**: Visit [rapidapi.com](https://rapidapi.com) and create an account
2. **Subscribe to SEO APIs**: Search for and subscribe to the SEO APIs you want to use
3. **Get Your API Key**: Copy your RapidAPI key from the dashboard
4. **Update Environment**: Add the key to your `.env` file

## Usage in Workflows

### Automatic Integration

When `RAPIDAPI_KEY` is configured, the system automatically:

1. **Prioritizes RapidAPI**: Uses RapidAPI gateway as the primary data source
2. **Provides Fallbacks**: Falls back to direct API calls if RapidAPI fails
3. **Optimizes Costs**: Intelligently batches requests to minimize API costs
4. **Handles Errors**: Gracefully handles API failures with fallback data

### Data Source Priority

```
1. RapidAPI Gateway (if configured)
   ├── Ubersuggest via RapidAPI
   ├── Semrush via RapidAPI  
   ├── Ahrefs via RapidAPI
   └── Moz via RapidAPI
2. Direct API Calls (fallback)
   ├── Direct Semrush API
   └── Direct Ubersuggest API
3. AI-Generated Data (final fallback)
```

## API Endpoints and Costs

### Typical RapidAPI Pricing

| Provider | Cost per Request | Requests/Month | Monthly Cost |
|----------|------------------|----------------|--------------|
| Ubersuggest | $0.05 | 1,000 | $50 |
| Semrush | $0.10 | 1,000 | $100 |
| Ahrefs | $0.15 | 1,000 | $150 |
| Moz | $0.08 | 1,000 | $80 |

### Cost Optimization Features

- **Intelligent Batching**: Groups multiple keywords per request
- **Request Deduplication**: Avoids duplicate API calls
- **Provider Rotation**: Uses cheapest available provider first
- **Caching**: Caches results to reduce repeat requests

## Configuration Examples

### Basic Setup (Ubersuggest Only)
```bash
RAPIDAPI_KEY=your_key_here
RAPIDAPI_UBERSUGGEST_ENABLED=true
RAPIDAPI_SEMRUSH_ENABLED=false
RAPIDAPI_AHREFS_ENABLED=false
RAPIDAPI_MOZ_ENABLED=false
```

### Professional Setup (Multiple Providers)
```bash
RAPIDAPI_KEY=your_key_here
RAPIDAPI_UBERSUGGEST_ENABLED=true
RAPIDAPI_SEMRUSH_ENABLED=true
RAPIDAPI_AHREFS_ENABLED=true
RAPIDAPI_MOZ_ENABLED=false
```

### Development Setup (Fallback Only)
```bash
# Leave RAPIDAPI_KEY empty to use AI-generated fallback data
# RAPIDAPI_KEY=
```

## Testing Your Setup

### Health Check Endpoint

```bash
# Test RapidAPI connectivity
curl -X GET "http://localhost:3000/api/pseo/langgraph/health" \
  -H "x-user-id: your-user-id"
```

### Test Keyword Research

```bash
# Test keyword research with RapidAPI
curl -X POST "http://localhost:3000/api/pseo/langgraph/keyword-research" \
  -H "Content-Type: application/json" \
  -H "x-user-id: your-user-id" \
  -d '{
    "website_id": "test-website",
    "seed_keywords": ["digital marketing"],
    "research_method": "topic",
    "topic_input": "digital marketing automation",
    "max_keywords": 20,
    "data_sources": ["rapidapi"]
  }'
```

## Troubleshooting

### Common Issues

1. **"RapidAPI not configured" Error**
   - Check that `RAPIDAPI_KEY` is set in your `.env` file
   - Verify the API key is valid and active

2. **"No SEO providers enabled" Error**
   - Enable at least one provider: `RAPIDAPI_UBERSUGGEST_ENABLED=true`
   - Check provider host configurations

3. **API Rate Limiting**
   - Reduce request frequency in workflow configuration
   - Consider upgrading your RapidAPI subscription

4. **Poor Data Quality**
   - Enable multiple providers for better data coverage
   - Check that providers are returning valid responses

### Debug Mode

Enable debug logging to troubleshoot issues:

```bash
# Add to your .env file
DEBUG=rapidapi:*
LOG_LEVEL=debug
```

## Benefits of RapidAPI Gateway

### For Users
- **Better Data Quality**: Access to multiple professional SEO APIs
- **Cost Efficiency**: Competitive pricing through RapidAPI marketplace
- **Reliability**: Automatic fallbacks prevent workflow failures

### For Developers  
- **Simplified Integration**: Single API interface for multiple providers
- **Unified Data Format**: Consistent data structure across providers
- **Easy Scaling**: Add new providers without code changes

### For Business
- **Reduced Vendor Lock-in**: Multiple provider options
- **Flexible Pricing**: Pay-per-use model with various subscription tiers
- **Professional Results**: Enterprise-grade SEO data quality

## Next Steps

1. **Sign up for RapidAPI** and get your API key
2. **Subscribe to SEO APIs** you want to use
3. **Update your `.env` file** with the configuration
4. **Test the integration** using the provided endpoints
5. **Monitor usage and costs** through RapidAPI dashboard

For support, check the [RapidAPI documentation](https://docs.rapidapi.com/) or contact our development team.
