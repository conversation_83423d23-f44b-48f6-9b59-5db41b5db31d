// =============================================
// DOCUMENT SHARING TYPES
// =============================================

export type SharePermissionLevel =
  | "view" // Can only view the document
  | "comment" // Can view and add comments
  | "suggest" // Can view, comment, and suggest edits
  | "edit" // Can view, comment, suggest, and edit content
  | "manage" // Can do everything including sharing permissions
  | "admin"; // Full control including deletion

export type ShareEntityType = "user" | "team" | "organisation";

export const PERMISSION_LEVELS: Record<SharePermissionLevel, number> = {
  view: 1,
  comment: 2,
  suggest: 3,
  edit: 4,
  manage: 5,
  admin: 6,
};

export interface DocumentShareData {
  id: string;
  documentId: string;
  entityType: ShareEntityType;
  entityId?: string; // null for public shares
  permissionLevel: SharePermissionLevel;
  sharedBy: string;
  sharedAt: string;
  expiresAt?: string;
  accessToken?: string;
  isActive: boolean;
  customMessage?: string;
  notifyOnChanges: boolean;
  lastAccessedAt?: string;
  accessCount: number;
  // Entity details fetched separately since no foreign key constraints
  entityDetails?: {
    id: string;
    name?: string;
    full_name?: string;
    email?: string;
    description?: string;
  };
}

export interface ShareTarget {
  id: string;
  name: string;
  type: ShareEntityType;
  email?: string;
  description?: string;
  avatar?: string | null;
  memberCount?: number;
}

export interface ShareRequest {
  targets: ShareTarget[];
  permissionLevel: SharePermissionLevel;
  message?: string;
  expiresAt?: string;
  notifyTargets: boolean;
}

export interface ShareResponse {
  success: boolean;
  shares?: DocumentShareData[];
  message: string;
}

export interface ShareSearchResult {
  users: ShareTarget[];
  teams: ShareTarget[];
  organizations: ShareTarget[];
}

// =============================================
// COLLABORATION TYPES
// =============================================

export interface DocumentComment {
  id: string;
  documentId: string;
  userId: string;
  parentCommentId?: string;
  sectionId?: string;
  content: string;
  metadata: Record<string, any>;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;
  author?: UserProfile;
  replies?: DocumentComment[];
}

export interface DocumentSuggestion {
  id: string;
  documentId: string;
  userId: string;
  sectionId: string;
  originalContent: string;
  suggestedContent: string;
  suggestionType: "edit" | "insert" | "delete";
  reason?: string;
  status: "pending" | "accepted" | "rejected";
  reviewedBy?: string;
  reviewedAt?: string;
  reviewComment?: string;
  createdAt: string;
  author?: UserProfile;
  reviewer?: UserProfile;
}

export interface DocumentActivity {
  id: string;
  documentId: string;
  userId: string;
  action: string;
  metadata: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  user?: UserProfile;
}

export interface DocumentSession {
  id: string;
  documentId: string;
  userId: string;
  sessionToken: string;
  isActive: boolean;
  cursorPosition?: Record<string, any>;
  selectionRange?: Record<string, any>;
  lastActivity: string;
  createdAt: string;
  user?: UserProfile;
}

// =============================================
// USER AND DOCUMENT TYPES
// =============================================

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatarUrl?: string;
  role?: string;
  isOnline: boolean;
  lastSeen?: string;
}

export interface DocumentMetadata {
  tags: string[];
  category?: string;
  priority?: "low" | "medium" | "high";
  language?: string;
  industry?: string;
  confidentialityLevel?: "public" | "internal" | "confidential" | "restricted";
  retention?: {
    period: number;
    unit: "days" | "months" | "years";
    action: "archive" | "delete" | "review";
  };
  compliance?: {
    standards: string[];
    requirements: string[];
    lastAudit?: string;
    nextReview?: string;
  };
  workflow?: {
    stage: string;
    assignee?: string;
    dueDate?: string;
    priority: "low" | "medium" | "high" | "urgent";
  };
}

export type DocumentStatus =
  | "draft"
  | "in_review"
  | "approved"
  | "published"
  | "archived";

export interface DocumentPermissions {
  canView: boolean;
  canComment: boolean;
  canSuggest: boolean;
  canEdit: boolean;
  canManage: boolean;
  canAdmin: boolean;
  effectiveLevel: SharePermissionLevel;
}

export interface SharedDocument {
  id: string;
  title: string;
  content: any; // JSON content
  status: DocumentStatus;
  createdBy: string;
  clientName?: string;
  metadata: DocumentMetadata;
  filePath?: string;
  sizeBytes: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  lastAccessedAt: string;

  // Computed/joined fields
  author?: UserProfile;
  permissions?: DocumentPermissions;
  shareInfo?: DocumentShareData;
  isSharedWithMe?: boolean;
  isFavorited?: boolean;
  activeCollaborators?: UserProfile[];
  recentActivity?: DocumentActivity[];
  pendingComments?: number;
  pendingSuggestions?: number;
}

// =============================================
// UTILITY FUNCTIONS
// =============================================

export const PERMISSION_DESCRIPTIONS: Record<SharePermissionLevel, string> = {
  view: "Can view the document",
  comment: "Can view and add comments",
  suggest: "Can view, comment, and suggest edits",
  edit: "Can view and edit the document",
  manage: "Can do everything including sharing permissions",
  admin: "Full control including deletion",
};

export const hasPermission = (
  userLevel: SharePermissionLevel,
  requiredLevel: SharePermissionLevel
): boolean => {
  return PERMISSION_LEVELS[userLevel] >= PERMISSION_LEVELS[requiredLevel];
};

export const getPermissionIcon = (level: SharePermissionLevel): string => {
  switch (level) {
    case "view":
      return "👁️";
    case "comment":
      return "💬";
    case "suggest":
      return "✏️";
    case "edit":
      return "📝";
    case "manage":
      return "⚙️";
    case "admin":
      return "👑";
    default:
      return "❓";
  }
};

// =============================================
// UI STATE TYPES
// =============================================

export interface ShareModalState {
  isOpen: boolean;
  mode: "share" | "link" | "manage";
  documentId?: string;
  existingShares?: DocumentShareData[];
  existingLinks?: DocumentShareLink[];
}
