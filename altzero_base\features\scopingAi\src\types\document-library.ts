// Document Library specific types

export interface ProcessedDocument {
  title: string;
  fullContent: string;
  metadata: {
    author?: string;
    pageCount?: number;
    fileType?: string;
    createdDate?: string;
    modifiedDate?: string;
  };
  sections: ProcessedSection[];
}

export interface ProcessedSection {
  title: string;
  content: string;
  description?: string;
  order?: number;
}

export interface TemplateDocument {
  id: string;
  title: string;
  description: string;
  date: string;
  sections: string[];
  rawData: {
    content: string;
    sections: ProcessedSection[];
    structured_content?: {
      pages: Array<{
        page_number: number;
        content: Array<{
          type: string;
          content: string;
          position: { x: number; y: number; page: number };
        }>;
      }>;
    };
    metadata: {
      author?: string;
      createdDate?: string;
      modifiedDate?: string;
      pageCount?: number;
      fileType?: string;
    };
  };
}

export interface DocumentData {
  id?: number | string;
  title: string;
  content: string;
  metadata: {
    pageCount?: number;
    fileType?: string;
    author?: string;
  };
  sections: ProcessedSection[];
}

export interface LibraryDocument {
  id: number | string;
  title: string;
  client: string;
  date: string;
  type: string;
  sections: string[];
  rawData?: {
    content: string;
    sections: ProcessedSection[];
    metadata: any;
  };
  markdown_path?: string;
}

export interface UploadedFile {
  file: File;
  fileName: string;
  fileSize: number;
  fileType: string;
}

export interface MarkdownContent {
  content: string;
  sections: ProcessedSection[];
}

// Supabase table interfaces
export interface ProposalTemplate {
  id: string;
  name: string;
  description?: string;
  client_name?: string;
  author?: string;
  markdown_path?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  user_id: string;
} 