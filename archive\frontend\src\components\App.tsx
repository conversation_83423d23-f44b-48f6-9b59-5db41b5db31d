import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "../utils/supabaseClient";
import SectionSelector from "../ai_apps/scoping_ai/components/SectionSelector";
import TemplateSelector from "../ai_apps/scoping_ai/components/TemplateSelector";
import Preview from "../ai_apps/scoping_ai/components/Preview";
import Login from "./auth/Login";
import Signup from "./auth/Signup";
import Home from "../ai_apps/scoping_ai/components/Home";
import AIApps from "../pages/AIApps";
import ProposalGenerator from "../ai_apps/scoping_ai/components/ProposalGenerator";
import KnowledgeBase from "../ai_apps/scoping_ai/components/KnowledgeBase";
import ForgotPassword from "../pages/forgot-password";
import UpdatePassword from "../pages/update-password";
import Profile from "../pages/profile";
import { UserProvider } from "../../contexts/UserContext";
import RAGChatApp from "../ai_apps/scoping_ai/components/rag/RAGChatApp";
import AIScoping from "../ai_apps/scoping_ai/components/AIScoping";
import ProgressiveScopingApp from "../ai_apps/scoping_ai/components/scoping/progressive/ProgressiveScopingApp";
import ClientManager from "../ai_apps/scoping_ai/components/scoping/managers/ClientManager";
import TemplateManager from "../ai_apps/scoping_ai/components/scoping/managers/TemplateManager";
import ProgressiveScopeCreator from "../ai_apps/scoping_ai/components/scoping/progressive/ProgressiveScopeCreator";
import SectionManager from "../ai_apps/scoping_ai/components/scoping/managers/SectionManager";
import ScopeTemplateManager from "../ai_apps/scoping_ai/components/scoping/managers/ScopeTemplateManager";
import SectionTemplateManager from "../ai_apps/scoping_ai/components/scoping/managers/SectionTemplateManager";
import ProgressiveScopingResult from "../ai_apps/scoping_ai/components/scoping/progressive/ProgressiveScopingResult";

// Add environment variable check on app startup
if (!import.meta.env.VITE_API_URL) {
  console.warn(
    "VITE_API_URL is not set in environment variables. API calls may fail."
  );
}

if (!import.meta.env.VITE_API_KEY) {
  console.warn(
    "VITE_API_KEY is not set in environment variables. API authentication may fail."
  );
}

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check active sessions and sets the user
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for changes on auth state (login, sign out, etc.)
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <UserProvider>
      <BrowserRouter>
        {/* {user && <Navbar />} */}
        <Routes>
          {/* Public Routes */}
          <Route
            path="/login"
            element={user ? <Navigate to="/" /> : <Login />}
          />
          <Route
            path="/signup"
            element={user ? <Navigate to="/" /> : <Signup />}
          />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/update-password" element={<UpdatePassword />} />

          {/* Protected Routes */}
          {/* <Route
            path="/"
            element={user ? <Home /> : <Navigate to="/login" />}
          /> */}
          <Route
            path="/profile"
            element={user ? <Profile /> : <Navigate to="/login" />}
          />
          <Route
            path="/sections"
            element={user ? <SectionSelector /> : <Navigate to="/login" />}
          />
          <Route
            path="/templates"
            element={user ? <TemplateSelector /> : <Navigate to="/login" />}
          />
          <Route
            path="/preview"
            element={user ? <Preview /> : <Navigate to="/login" />}
          />
          <Route
            path="/"
            element={user ? <AIApps /> : <Navigate to="/login" />}
          />
          <Route
            path="/proposal-generator"
            element={user ? <ProposalGenerator /> : <Navigate to="/login" />}
          />
          <Route
            path="/knowledge-base"
            element={user ? <KnowledgeBase /> : <Navigate to="/login" />}
          />
          <Route
            path="/rag-chat"
            element={user ? <RAGChatApp /> : <Navigate to="/login" />}
          />
          <Route
            path="/ai-scoping"
            element={user ? <AIScoping /> : <Navigate to="/login" />}
          />

          {/* Progressive Scoping System Routes */}
          <Route
            path="/scoping"
            element={
              user ? <ProgressiveScopingApp /> : <Navigate to="/login" />
            }
          />
          <Route
            path="/scoping/progressive"
            element={
              user ? <ProgressiveScopingApp /> : <Navigate to="/login" />
            }
          />
          <Route
            path="/scoping/create"
            element={
              user ? <ProgressiveScopeCreator /> : <Navigate to="/login" />
            }
          />

          {/* Scoping Management Routes */}
          <Route
            path="/scoping/clients"
            element={user ? <ClientManager /> : <Navigate to="/login" />}
          />
          <Route
            path="/scoping/templates"
            element={user ? <TemplateManager /> : <Navigate to="/login" />}
          />
          <Route
            path="/scoping/scope-templates"
            element={user ? <ScopeTemplateManager /> : <Navigate to="/login" />}
          />
          <Route
            path="/scoping/section-templates"
            element={
              user ? <SectionTemplateManager /> : <Navigate to="/login" />
            }
          />
          <Route
            path="/scoping/sections"
            element={user ? <SectionManager /> : <Navigate to="/login" />}
          />
          <Route
            path="/scoping/document/:documentId"
            element={
              user ? <ProgressiveScopingResult /> : <Navigate to="/login" />
            }
          />
        </Routes>
      </BrowserRouter>
    </UserProvider>
  );
}

export default App;
