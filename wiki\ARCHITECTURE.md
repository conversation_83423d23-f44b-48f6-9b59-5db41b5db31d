# AltZero Platform Architecture

This document outlines the comprehensive architecture of the AltZero platform, describing the project's structure, key components, routes, technologies, and relationships between different parts of the application.

## Project Overview

AltZero is a full-stack TypeScript application built with React and Express.js that provides:

- **AI-Powered Knowledge Base**: Document upload, processing, and intelligent chat capabilities
- **Multi-tenant Architecture**: Organizations and teams with role-based access control
- **Subscription Management**: Tiered subscription plans with feature access control
- **Real-time Communication**: AI chat with streaming responses and CopilotKit integration
- **Modern UI/UX**: Built with Radix UI, Tailwind CSS, and Framer Motion

## Project Structure

```
altzero-base/
├── App.tsx                     # Main Application component with routing
├── main.tsx                    # Application entry point
├── index.html                  # HTML template
├── index.css                   # Global styles and theme system
├──
├── base/                       # Core application modules
│   ├── components/             # Reusable UI components
│   │   ├── ui/                 # Radix UI-based design system components
│   │   │   ├── accordion.tsx   # Collapsible content sections
│   │   │   ├── alert.tsx       # Alert/notification components
│   │   │   ├── avatar.tsx      # User avatar components
│   │   │   ├── badge.tsx       # Status and category badges
│   │   │   ├── button.tsx      # Button component with variants
│   │   │   ├── calendar.tsx    # Date picker calendar
│   │   │   ├── card.tsx        # Content cards with header/body/footer
│   │   │   ├── chart.tsx       # Data visualization charts
│   │   │   ├── checkbox.tsx    # Form checkbox inputs
│   │   │   ├── command.tsx     # Command palette/search interface
│   │   │   ├── dialog.tsx      # Modal dialogs
│   │   │   ├── dropdown-menu.tsx # Context menus and dropdowns
│   │   │   ├── form.tsx        # Form handling with validation
│   │   │   ├── input.tsx       # Text input fields
│   │   │   ├── label.tsx       # Form labels
│   │   │   ├── progress.tsx    # Progress bars
│   │   │   ├── select.tsx      # Dropdown selection
│   │   │   ├── separator.tsx   # Visual separators
│   │   │   ├── sheet.tsx       # Slide-out panels
│   │   │   ├── sidebar.tsx     # Navigation sidebar
│   │   │   ├── switch.tsx      # Toggle switches
│   │   │   ├── table.tsx       # Data tables
│   │   │   ├── tabs.tsx        # Tab navigation
│   │   │   ├── textarea.tsx    # Multi-line text input
│   │   │   ├── toast.tsx       # Notification toasts
│   │   │   └── tooltip.tsx     # Hover tooltips
│   │   ├── auth/               # Authentication components
│   │   │   ├── Login.tsx       # Login form with OAuth support
│   │   │   └── Signup.tsx      # Multi-step registration
│   │   ├── chat/               # AI chat components
│   │   ├── team/               # Team management components
│   │   ├── organization/       # Organization management
│   │   ├── invitation/         # User invitation system
│   │   ├── dashboard/          # Dashboard widgets
│   │   ├── subscription/       # Subscription management UI
│   │   ├── common/             # Shared utility components
│   │   ├── user-button.tsx     # User profile dropdown
│   │   ├── theme-provider.tsx  # Theme context provider
│   │   ├── theme-toggle.tsx    # Dark/light mode toggle
│   │   ├── stepper.tsx         # Multi-step form component
│   │   └── Layout.tsx          # Layout compatibility layer
│   │
│   ├── contextapi/             # React Context API implementations
│   │   ├── UserContext.tsx     # User authentication and profile state
│   │   ├── SubscriptionContext.tsx # Subscription management state
│   │   └── KnowledgeContext.tsx # Knowledge base and chat state
│   │
│   ├── pages/                  # Application pages
│   │   ├── Dashboard.tsx       # Main user dashboard
│   │   ├── KnowledgeBase.tsx   # Document upload and management
│   │   ├── Chat.tsx            # AI chat interface
│   │   ├── CopilotChat.tsx     # CopilotKit integration
│   │   ├── Settings.tsx        # User and system settings
│   │   ├── AIApps.tsx          # AI applications marketplace
│   │   ├── profile.tsx         # User profile management
│   │   ├── teams.tsx           # Team listing and management
│   │   ├── team-details.tsx    # Individual team management
│   │   ├── organizations.tsx   # Organization listing
│   │   ├── organization-details.tsx # Organization management
│   │   ├── subscription.tsx    # Subscription management
│   │   ├── entities.tsx        # Entity management
│   │   ├── forgot-password.tsx # Password recovery
│   │   └── update-password.tsx # Password update
│   │
│   ├── services/               # Business logic services
│   │   ├── api.ts              # General API service layer
│   │   ├── knowledgeService.ts # Knowledge base operations
│   │   ├── subscriptionService.ts # Subscription management
│   │   ├── teamService.ts      # Team operations
│   │   └── organizationService.ts # Organization operations
│   │
│   ├── types/                  # TypeScript type definitions
│   │   ├── user.ts             # User-related types
│   │   ├── knowledge.ts        # Knowledge base types
│   │   ├── subscription.ts     # Subscription and billing types
│   │   ├── team.ts             # Team management types
│   │   ├── organization.ts     # Organization types
│   │   └── scoping.ts          # Access control and scoping types
│   │
│   ├── utils/                  # Utility functions
│   │   ├── supabaseClient.ts   # Supabase client configuration
│   │   ├── constants.ts        # Application constants and enums
│   │   ├── formatters.ts       # Data formatting utilities
│   │   ├── api.ts              # API communication utilities
│   │   └── utils.ts            # General utility functions
│   │
│   ├── hooks/                  # Custom React hooks
│   ├── lib/                    # Third-party library configurations
│   ├── supabase/               # Supabase configurations
│   └── config/                 # Application configurations
│
├── header/                     # Header components
│   └── Header.tsx              # Main navigation header
│
├── layout/                     # Layout components
│   └── MainLayout.tsx          # Main application layout wrapper
│
├── pages/                      # Legacy page components (deprecated)
├── hooks/                      # Global custom hooks
├── uploads/                    # File upload directory
├── utils/                      # Legacy global utilities
│
├── server/                     # Backend Express.js server
│   ├── app.ts                  # Server entry point and configuration
│   ├── tsconfig.json           # TypeScript config for server
│   ├── nodemon.json            # Development server config
│   ├── uploads/                # Server-side file uploads
│   └── base/                   # Server modules
│       ├── services/           # Server-side business logic
│       └── common/             # Shared server utilities
│           ├── apps/           # External service integrations
│           │   ├── config/     # Server configuration
│           │   └── supabase.ts # Supabase server client
│           └── routes/         # API route definitions
│               ├── index.ts    # Route registration
│               ├── auth.ts     # Authentication middleware
│               ├── knowledge.ts # Knowledge base API endpoints
│               ├── copilotkit.ts # CopilotKit integration
│               └── example.ts  # Example protected routes
│
├── supabase/                   # Database schema and migrations
│   ├── orgTeamInviteSubscription.sql # Main database schema
│   └── initialProfile.sql     # User profile initialization
│
├── database/                   # Database migration files
│   └── migrations/             # Version-controlled schema changes
│
├── package.json                # Project dependencies and scripts
├── tsconfig.json               # TypeScript configuration
├── vite.config.ts              # Vite bundler configuration
├── tailwind.config.js          # Tailwind CSS configuration
├── postcss.config.js           # PostCSS configuration
├── env.template                # Environment variables template
├── README.md                   # Project documentation
├── KNOWLEDGE_BASE_README.md    # AI features documentation
└── .gitignore                  # Git ignore rules
```

## Core Technologies

### Frontend Stack

- **React 18**: Modern React with hooks and concurrent features
- **TypeScript**: Static type checking and enhanced developer experience
- **Vite**: Fast development server and build tool
- **React Router DOM**: Client-side routing and navigation
- **Tailwind CSS**: Utility-first CSS framework with custom theme system
- **Radix UI**: Accessible, unstyled UI primitives
- **Framer Motion**: Animation and gesture library
- **React Hook Form**: Form handling with validation
- **Zod**: Runtime type validation
- **Supabase Client**: Authentication and database operations

### Backend Stack

- **Node.js**: JavaScript runtime environment
- **Express.js**: Web application framework
- **TypeScript**: Server-side type safety
- **Supabase**: Backend-as-a-Service (authentication, database, storage)
- **Express Session**: Session management
- **CORS**: Cross-origin resource sharing
- **Multer**: File upload handling
- **Dotenv**: Environment variable management

### AI/ML Integration

- **CopilotKit**: AI copilot framework for React applications
- **LangChain**: LLM application framework
- **LlamaIndex**: Document indexing and retrieval framework
- **OpenAI API**: GPT models for chat and completion
- **Pinecone**: Vector database for semantic search
- **LlamaParser**: Advanced document parsing

### Development Tools

- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting
- **Nodemon**: Development server auto-restart
- **Concurrently**: Run multiple npm scripts simultaneously
- **ts-node**: TypeScript execution for Node.js

## Frontend Architecture

### Application Entry Point

The application starts from `main.tsx`, which renders the main `App` component:

```12:15:altzero_base/main.tsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
```

### Main App Component (`App.tsx`)

The App component provides the foundational structure with multiple context providers and routing:

#### Context Providers Hierarchy

1. **BrowserRouter**: Enables client-side routing
2. **UserProvider**: Manages user authentication state
3. **SubscriptionProvider**: Handles subscription and billing state
4. **KnowledgeProvider**: Manages knowledge base and chat functionality
5. **CopilotKit**: Provides AI copilot capabilities
6. **MainLayout**: Wraps all pages with consistent layout

#### Routing Structure

##### Public Routes (Accessible without authentication)

- `/login` - User login with email/password and OAuth
- `/signup` - Multi-step user registration
- `/forgot-password` - Password recovery via email
- `/update-password` - Password reset form

##### Protected Routes (Require authentication)

- `/dashboard` - Main user dashboard with overview
- `/profile` - User profile management and settings
- `/knowledge` - Knowledge base document management
- `/settings` - Application and user settings

##### Navigation Logic

- Root path `/` redirects based on authentication status
- Authenticated users → `/dashboard`
- Unauthenticated users → `/login`
- `*` (catch-all) → 404 Not Found page

### Context API Architecture

#### UserContext (`base/contextapi/UserContext.tsx`)

Manages global user authentication state and provides:

- User authentication status and profile data
- Loading states during authentication checks
- Login/logout functionality
- Session management with Supabase
- Profile updates and user preferences

#### SubscriptionContext (`base/contextapi/SubscriptionContext.tsx`)

Handles subscription and billing management:

- Current subscription plan and status
- Feature access control based on subscription tier
- Billing history and payment methods
- Subscription upgrade/downgrade workflows
- Usage tracking and limits

#### KnowledgeContext (`base/contextapi/KnowledgeContext.tsx`)

Manages knowledge base and AI chat functionality:

- Document upload and processing state
- Chat session management
- AI conversation history
- Document selection for chat context
- Real-time status updates for document processing

### Component Architecture

#### Design System Components (`base/components/ui/`)

A comprehensive design system built on Radix UI primitives:

- **Form Components**: Input, textarea, select, checkbox, radio groups with validation
- **Navigation**: Dropdown menus, navigation menus, command palette
- **Layout**: Cards, separators, sidebars, sheets (slide-out panels)
- **Feedback**: Alerts, toasts, progress bars, skeletons
- **Data Display**: Tables, charts, badges, avatars
- **Overlay**: Dialogs, popovers, tooltips, hover cards

#### Feature-Specific Components

##### Authentication (`base/components/auth/`)

- **Login.tsx**: Email/password login with Google OAuth integration
- **Signup.tsx**: Multi-step registration with email verification

##### Chat System (`base/components/chat/`)

- Real-time chat interface with streaming responses
- Message history and session management
- Document source citations
- Typing indicators and status updates

##### Team Management (`base/components/team/`)

- Team creation and settings
- Member invitation and role management
- Team-scoped permissions and resources

##### Organization Management (`base/components/organization/`)

- Multi-tenant organization structure
- Organization-wide settings and policies
- Billing and subscription management at org level

#### Theme System

The application implements a comprehensive theme system in `index.css`:

##### CSS Custom Properties

- Light and dark mode support with automatic detection
- Semantic color tokens (primary, secondary, accent, destructive)
- Typography scale with consistent font sizes and line heights
- Spacing scale based on rem units
- Border radius and shadow scales

##### Tailwind Integration

- Custom Tailwind configuration extends default theme
- Utility classes map to CSS custom properties
- Responsive design with mobile-first approach
- Consistent spacing and sizing throughout the app

### Page Components (`base/pages/`)

#### Dashboard (`Dashboard.tsx`)

Main landing page for authenticated users featuring:

- Quick access to key features
- Recent activity overview
- System notifications and updates
- Subscription status and usage metrics

#### Knowledge Base (`KnowledgeBase.tsx`)

Comprehensive document management interface:

- Drag-and-drop file upload with progress tracking
- Document grid with search and filtering
- Processing status indicators
- Bulk operations and document management
- Integration with AI chat for document selection

#### AI Chat (`Chat.tsx`)

Advanced conversational AI interface:

- Real-time streaming responses
- Document-based context for accurate answers
- Source citations with document references
- Chat session management and history
- Multi-document conversation support

#### Settings (`Settings.tsx`)

Centralized configuration management:

- User profile and preferences
- Theme and appearance settings
- Notification preferences
- Security settings and two-factor authentication
- API key management for integrations

#### Team and Organization Management

- **teams.tsx**: Team listing and creation
- **team-details.tsx**: Individual team management with member roles
- **organizations.tsx**: Organization overview and management
- **organization-details.tsx**: Detailed organization settings

### Services Architecture (`base/services/`)

#### API Service (`api.ts`)

General-purpose API communication layer:

- HTTP client configuration with error handling
- Request/response interceptors
- Authentication token management
- Retry logic for failed requests
- Base URL and endpoint configuration

#### Knowledge Service (`knowledgeService.ts`)

Specialized service for AI and document operations:

- Document upload with progress tracking
- File processing and status monitoring
- Chat API integration with streaming support
- Search and retrieval operations
- Document metadata management

#### Subscription Service (`subscriptionService.ts`)

Handles billing and subscription operations:

- Plan comparison and selection
- Payment processing integration
- Usage tracking and limit enforcement
- Billing history and invoice management
- Subscription lifecycle management

#### Team and Organization Services

- **teamService.ts**: Team CRUD operations and member management
- **organizationService.ts**: Organization management and settings

### Type System (`base/types/`)

#### Core Types

- **user.ts**: User profiles, roles, and permissions
- **subscription.ts**: Billing plans, usage limits, and payment data
- **knowledge.ts**: Document metadata, chat sessions, and AI responses
- **team.ts**: Team structure, roles, and permissions
- **organization.ts**: Multi-tenant organization structure
- **scoping.ts**: Access control and resource scoping

### Utility Functions (`base/utils/`)

#### Supabase Client (`supabaseClient.ts`)

Configured Supabase client with:

- Authentication persistence
- Automatic session refresh (every 23 hours)
- Environment-specific configuration
- Error handling and retry logic

#### Constants (`constants.ts`)

Application-wide constants including:

- API endpoints and URLs
- File upload limits and allowed types
- Document processing configuration
- Toast messages and user feedback
- Role definitions and permissions
- Entity types and labels

#### Formatters (`formatters.ts`)

Data formatting utilities:

- Date and time formatting
- File size formatting
- Currency and number formatting
- Text truncation and ellipsis

## Backend Architecture

### Server Entry Point (`server/app.ts`)

The Express.js server provides a robust foundation with:

#### Environment Configuration

- Multi-path environment variable loading
- Comprehensive environment variable validation
- Development and production configuration
- Debug logging for configuration status

#### Middleware Stack

1. **CORS**: Configured for cross-origin requests with credentials
2. **JSON Parsing**: Request body parsing for API endpoints
3. **Session Management**: Express session with secure cookies
4. **Request Logging**: Detailed request/response logging
5. **Error Handling**: Centralized error handling and reporting

#### Core Routes

- `GET /health`: Health check endpoint for monitoring
- `GET /api/protected`: Example protected route with API key validation
- `/api/*`: Comprehensive API route mounting

### API Routes (`server/base/common/routes/`)

#### Route Organization (`index.ts`)

Central route registration system that mounts all API modules:

- Modular route organization
- Consistent URL patterns
- Middleware application per route group

#### Authentication Middleware (`auth.ts`)

API key validation system:

- Header and query parameter API key support
- Environment-specific API key configuration
- Unauthorized access protection
- Request logging and security monitoring

#### Knowledge Base API (`knowledge.ts`)

Comprehensive document and chat API:

##### Document Management Endpoints

- `POST /api/knowledge/documents/upload`: Multi-file upload with processing
- `GET /api/knowledge/documents`: Document listing with filtering
- `DELETE /api/knowledge/documents/:id`: Document deletion
- `POST /api/knowledge/documents/:id/reprocess`: Reprocess failed documents
- `GET /api/knowledge/stats`: Knowledge base statistics

##### AI Chat Endpoints

- `POST /api/knowledge/chat`: Standard chat completion
- `POST /api/knowledge/chat/stream`: Streaming chat responses
- `GET /api/knowledge/chat/sessions`: Chat session management
- `POST /api/knowledge/search`: Semantic document search

#### CopilotKit Integration (`copilotkit.ts`)

AI copilot backend integration:

- OpenAI API integration
- Function calling capabilities
- Context management for AI interactions
- Streaming response handling

### External Service Integration (`server/base/common/apps/`)

#### Supabase Server Client

Server-side Supabase integration with:

- Service role key authentication
- Database operations with elevated permissions
- User authentication verification
- Real-time subscriptions and webhooks

#### Configuration Management

Environment-specific configuration for:

- Development, staging, and production environments
- API key management and rotation
- Feature flags and toggle configuration
- Performance optimization settings

## Database Architecture

### Supabase Integration

#### Schema Design (`supabase/orgTeamInviteSubscription.sql`)

Comprehensive database schema supporting:

##### Core Tables

- **profiles**: User profile information and preferences
- **organizations**: Multi-tenant organization structure
- **teams**: Team management within organizations
- **memberships**: User-organization-team relationships with roles
- **invitations**: User invitation system with role assignment

##### Subscription Management

- **subscriptions**: Subscription plans and billing information
- **usage_logs**: Feature usage tracking and limits
- **billing_history**: Payment and invoice tracking

##### Knowledge Base Tables

- **documents**: Document metadata and processing status
- **chat_sessions**: AI conversation management
- **document_chunks**: Text chunks for semantic search
- **embeddings**: Vector embeddings for similarity search

##### Security Features

- **Row Level Security (RLS)**: Fine-grained access control
- **Role-based permissions**: Organization and team-scoped access
- **API key management**: Secure API access control

#### Database Migrations (`database/migrations/`)

Version-controlled schema changes with:

- Sequential migration files
- Rollback capabilities
- Data migration scripts
- Index optimization

### Data Access Patterns

#### Frontend Data Access

- Supabase client for direct database operations
- Real-time subscriptions for live updates
- Optimistic updates for better UX
- Caching strategies for performance

#### Backend Data Access

- Service role for elevated permissions
- Bulk operations and complex queries
- Background job processing
- Data validation and sanitization

## AI/ML Architecture

### Knowledge Base Processing Pipeline

The AltZero platform implements a sophisticated multi-stage document processing and retrieval system that combines LlamaCloud parsing, Pinecone vector storage, and advanced RAG (Retrieval-Augmented Generation) capabilities.

#### Document Upload and Processing Flow

##### 1. Frontend Upload Process (`KnowledgeBase.tsx`)

The user interface provides a drag-and-drop upload zone with real-time progress tracking:

```typescript
const onDrop = useCallback(async (acceptedFiles: File[]) => {
  // File validation (type, size, format)
  const validFiles = acceptedFiles.filter(file => 
    knowledgeService.validateFile(file).isValid
  );
  
  // Upload with progress tracking
  const response = await knowledgeService.uploadDocuments(validFiles);
  
  // Update UI with results
  response.documents.forEach(doc => addDocument(doc));
}, []);
```

**Supported File Types:**
- PDF documents (`.pdf`)
- Microsoft Word (`.doc`, `.docx`)
- Plain text (`.txt`)
- Markdown (`.md`)
- Rich Text Format (`.rtf`)
- OpenDocument Text (`.odt`)

**File Validation:**
- Maximum file size: 50MB per file
- MIME type validation
- File integrity checks

##### 2. Service Layer Communication (`knowledgeService.ts`)

The knowledge service handles API communication with user authentication:

```typescript
async uploadDocuments(files: File[]): Promise<UploadResponse> {
  const userId = await this.getCurrentUserId();
  const formData = new FormData();
  
  files.forEach(file => formData.append("files", file));
  formData.append("metadata", JSON.stringify({
    uploadedAt: new Date().toISOString(),
    source: "web_upload",
    userId: userId,
  }));
  
  return this.makeRequest<UploadResponse>(ENDPOINTS.KNOWLEDGE_UPLOAD, {
    method: "POST",
    body: formData,
  });
}
```

**Key Features:**
- Automatic user ID injection from Supabase session
- Request/response type safety with TypeScript
- Error handling and retry logic
- Progress tracking capabilities

##### 3. Backend API Processing (`knowledge.ts`)

The Express.js route handles file upload and orchestrates the processing pipeline:

```typescript
router.post("/upload", upload.array("files", 10), async (req, res) => {
  const userId = getUserIdFromRequest(req);
  const uploadedFiles = req.files as Express.Multer.File[];
  
  const results = await Promise.allSettled(
    uploadedFiles.map(async (file) => {
      // 1. Parse document with LlamaCloud
      const parsedDoc = await llamaCloudService.parseDocument(
        file.path, 
        file.originalname
      );
      
      // 2. Store in Pinecone vector database
      const documentIds = await pineconeService.storeDocuments([{
        id: parsedDoc.id,
        content: parsedDoc.content,
        metadata: {
          ...parsedDoc.metadata,
          userId: userId,
        }
      }]);
      
      return { parsedDoc, documentIds };
    })
  );
});
```

##### 4. LlamaCloud Document Parsing (`llamaCloudService.ts`)

Advanced document parsing with intelligent text extraction:

```typescript
async parseDocument(filePath: string, originalName: string): Promise<ParsedDocument> {
  // 1. Extract raw text content
  const content = await this.extractTextContent(filePath, originalName);
  
  // 2. Create intelligent chunks with overlap
  const chunks = this.createChunks(content, originalName);
  
  // 3. Generate comprehensive metadata
  const metadata = {
    fileName: originalName,
    fileType: path.extname(originalName),
    fileSize: fs.statSync(filePath).size,
    pageCount: this.estimatePageCount(content),
    parsedAt: new Date().toISOString(),
  };
  
  return { id: documentId, content, metadata, chunks };
}
```

**Text Extraction Capabilities:**
- PDF text extraction with layout preservation
- Word document content parsing
- Markdown and plain text processing
- Metadata extraction (author, title, creation date)

**Intelligent Chunking:**
- Configurable chunk size (default: 1000 characters)
- Overlap between chunks (default: 200 characters)
- Sentence boundary preservation
- Page number tracking for citations

##### 5. Pinecone Vector Storage (`pineconeService.ts`)

Vector embedding generation and storage:

```typescript
async storeDocuments(documents: PineconeDocument[]): Promise<string[]> {
  await this.initializeVectorStore();
  
  const processedDocs = await Promise.all(
    documents.map(async (doc) => {
      // Generate embeddings for content
      const embedding = await this.embeddings.embedQuery(doc.content);
      
      return {
        id: doc.id,
        values: embedding,
        metadata: {
          content: this.sanitizeText(doc.content),
          ...doc.metadata,
          storedAt: new Date().toISOString(),
        }
      };
    })
  );
  
  // Batch upsert to Pinecone
  await this.vectorStore.addVectors(processedDocs);
  return processedDocs.map(doc => doc.id);
}
```

**Vector Database Features:**
- OpenAI embeddings (text-embedding-ada-002)
- 1536-dimensional vectors
- Metadata filtering by userId
- Batch operations for performance
- Automatic text sanitization

#### Document Retrieval and Search Architecture

##### 1. Semantic Search Flow

The system implements multiple search strategies depending on the use case:

```typescript
// In langGraphService.ts
async performRAG(request: RAGRequest): Promise<RAGResponse> {
  // 1. Retrieve relevant documents using hybrid approach
  const relevantDocs = await this.retrieveDocuments(
    request.query,
    request.selectedDocuments,
    { topK: 15, minScore: 0.3 },
    { userId: request.userFilter?.userId }
  );
  
  // 2. Format context from retrieved documents
  const context = this.formatContext(relevantDocs);
  
  // 3. Generate AI response with context
  const response = await this.llm.invoke([
    new SystemMessage(this.ragPrompt.format({ context })),
    new HumanMessage(request.query)
  ]);
  
  return {
    answer: response.content,
    sources: relevantDocs,
    metadata: {
      retrievedDocuments: relevantDocs.length,
      processingTime: Date.now() - startTime,
      model: "gpt-4",
      hasValidContext: relevantDocs.length > 0
    }
  };
}
```

##### 2. Hybrid Retrieval Strategy

The system uses different retrieval methods based on context:

```typescript
private async retrieveDocuments(
  query: string,
  selectedDocuments?: string[],
  options = { topK: 15, minScore: 0.3 },
  userFilter?: { userId: string }
): Promise<SearchResult[]> {
  
  if (selectedDocuments && selectedDocuments.length > 0) {
    // Specific document retrieval by ID
    return await this.getDocumentsByIds(selectedDocuments, userFilter);
  } else {
    // Semantic search across all user documents
    return await this.searchDocuments(query, {
      topK: options.topK,
      minScore: options.minScore,
      filter: userFilter ? { userId: userFilter.userId } : undefined,
    });
  }
}
```

**Retrieval Methods:**

1. **Document-Specific Retrieval**: When users select specific documents for chat
2. **Semantic Search**: When performing open-ended queries across all documents
3. **Filtered Search**: User-scoped searches with metadata filtering

##### 3. Vector Similarity Search

```typescript
async searchSimilar(query: string, options = {}): Promise<SearchResult[]> {
  // 1. Generate query embedding
  const queryEmbedding = await this.embeddings.embedQuery(query);
  
  // 2. Search Pinecone for similar vectors
  const results = await this.index.query({
    vector: queryEmbedding,
    topK: options.topK || 10,
    filter: options.filter, // userId filtering
    includeMetadata: true,
    includeValues: false
  });
  
  // 3. Format and filter results
  return results.matches
    .filter(match => match.score >= (options.minScore || 0.3))
    .map(match => ({
      id: match.id,
      text: match.metadata.content,
      score: match.score,
      metadata: match.metadata
    }));
}
```

#### AI Chat System Architecture

##### 1. Real-time Chat Interface (`KnowledgeBase.tsx`)

The chat interface provides real-time interaction with document-based AI:

```typescript
const handleSendMessage = async () => {
  const response = await knowledgeService.chatWithPineconeRAG({
    message: inputMessage,
    selectedDocuments: selectedDocuments,
    systemMessage: DEFAULT_SYSTEM_MESSAGE,
    temperature: 0.7,
    maxTokens: 2048,
  });
  
  // Display response with source citations
  setChatMessages(prev => [...prev, {
    role: "assistant",
    content: response.message,
    sources: response.sources,
    timestamp: new Date().toISOString()
  }]);
};
```

**Chat Features:**
- Real-time streaming responses
- Document source citations
- Chat session management
- Context-aware conversations
- Multi-document support

##### 2. RAG (Retrieval-Augmented Generation) Pipeline

```typescript
// In ragService.ts
async generateResponse(request: ChatRequest): Promise<ChatResponse> {
  const startTime = Date.now();
  
  // 1. Search for relevant documents
  const searchResults = await pineconeService.searchSimilar(
    request.message,
    {
      topK: 10,
      filter: request.userId ? { userId: request.userId } : undefined,
      minScore: 0.3
    }
  );
  
  // 2. Prepare context from search results
  const context = searchResults
    .map(result => `Source: ${result.metadata.fileName}\nContent: ${result.text}`)
    .join('\n\n');
  
  // 3. Generate response with context
  const response = await this.llm.invoke([
    new SystemMessage(`You are a helpful AI assistant. Use the following context to answer questions accurately. Always cite your sources.`),
    new HumanMessage(`Context:\n${context}\n\nQuestion: ${request.message}`)
  ]);
  
  return {
    message: response.content,
    sources: searchResults,
    metadata: {
      processingTime: Date.now() - startTime,
      model: "gpt-4",
      userId: request.userId
    }
  };
}
```

##### 3. Streaming Chat Implementation

```typescript
async streamChatMessage(
  request: ChatRequest,
  onChunk: (chunk: string) => void,
  onComplete: (response: ChatResponse) => void,
  onError: (error: Error) => void
): Promise<void> {
  
  const response = await fetch(`${this.baseUrl}${ENDPOINTS.CHAT_STREAM}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": this.apiKey,
      "x-user-id": userId,
    },
    body: JSON.stringify(request),
  });
  
  const reader = response.body?.getReader();
  const decoder = new TextDecoder();
  let buffer = "";
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split("\n");
    buffer = lines.pop() || "";
    
    for (const line of lines) {
      if (line.startsWith("data: ")) {
        const chunk = JSON.parse(line.slice(6));
        if (chunk.chunk) onChunk(chunk.chunk);
        if (chunk.complete) onComplete(chunk);
      }
    }
  }
}
```

#### Document Management System

##### 1. Document Status Tracking

Documents progress through multiple states during processing:

```typescript
type DocumentStatus = 
  | "uploading"    // File upload in progress
  | "parsing"      // LlamaCloud text extraction
  | "indexing"     // Vector embedding generation
  | "processing"   // General processing state
  | "success"      // Ready for use
  | "error";       // Processing failed
```

##### 2. Document Metadata Structure

```typescript
interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  status: DocumentStatus;
  uploadedAt: string;
  userId: string;
  llamaCloudId?: string;
  metadata?: {
    pageCount?: number;
    wordCount?: number;
    language?: string;
    author?: string;
    title?: string;
  };
  error?: string;
}
```

##### 3. Document Lifecycle Management

```typescript
// Document creation and status updates
storeDocumentMetadata(document: StoredDocument): void {
  this.documents.set(document.id, {
    ...document,
    status: "processing"
  });
}

updateDocumentStatus(
  documentId: string, 
  status: DocumentStatus, 
  error?: string
): void {
  const doc = this.documents.get(documentId);
  if (doc) {
    doc.status = status;
    if (error) doc.error = error;
    this.documents.set(documentId, doc);
  }
}
```

#### Performance Optimization

##### 1. Batch Processing

- Multiple file uploads processed concurrently
- Batch vector operations to Pinecone
- Chunked document processing for large files

##### 2. Caching Strategies

- Vector embedding caching
- Document metadata caching
- Search result caching for repeated queries

##### 3. Error Handling and Resilience

- Graceful degradation for failed uploads
- Retry logic for transient failures
- Comprehensive error logging and monitoring

#### Security and Access Control

##### 1. User-Scoped Data Access

All operations are scoped to the authenticated user:

```typescript
// Automatic user ID injection
const userId = await this.getCurrentUserId();

// Pinecone filtering by user
const filter = { userId: userId };

// API request authentication
headers: {
  "x-api-key": this.apiKey,
  "x-user-id": userId,
}
```

##### 2. Data Isolation

- Vector embeddings tagged with userId
- Search results filtered by user ownership
- Document access controlled at the API level

##### 3. Secure File Handling

- File type validation and sanitization
- Temporary file cleanup after processing
- Secure file storage with access controls

## Security Architecture

### Authentication and Authorization

#### Multi-layered Security

1. **Supabase Authentication**: Email/password and OAuth providers
2. **Session Management**: Secure session handling with refresh tokens
3. **API Key Authentication**: Backend API protection
4. **Row Level Security**: Database-level access control

#### Role-Based Access Control (RBAC)

- **Organization Roles**: Owner, Admin, Member with different permissions
- **Team Roles**: Team-specific role inheritance
- **Resource Scoping**: Access control at resource level
- **Permission Inheritance**: Hierarchical permission system

### Data Protection

#### Input Validation

- Client-side validation with Zod schemas
- Server-side validation and sanitization
- File upload validation and scanning
- SQL injection prevention

#### Data Encryption

- HTTPS/TLS for data in transit
- Database encryption at rest
- Secure API key storage
- Environment variable protection

## Performance Optimization

### Frontend Performance

#### Code Splitting

- Route-based code splitting with React.lazy
- Component-level lazy loading
- Dynamic imports for heavy libraries
- Preloading for critical resources

#### State Management Optimization

- Context provider optimization
- Memoization with useMemo and useCallback
- Reducer patterns for complex state
- Local state vs global state decisions

#### Asset Optimization

- Vite-based bundling and optimization
- Tree shaking for unused code elimination
- Image optimization and lazy loading
- CSS optimization with Tailwind purging

### Backend Performance

#### API Optimization

- Request/response compression
- Caching strategies for frequent queries
- Connection pooling for database access
- Rate limiting and throttling

#### Database Performance

- Proper indexing for query optimization
- Query optimization and analysis
- Connection pooling and management
- Read replica strategies for scaling

## Development Workflow

### Build System

#### Frontend Build Process

- **Development**: Vite dev server with hot module replacement
- **Production**: Optimized bundle with code splitting
- **Type Checking**: TypeScript compilation and validation
- **Linting**: ESLint with custom rules and configurations

#### Backend Build Process

- **Development**: Nodemon for auto-restart on changes
- **Production**: TypeScript compilation to JavaScript
- **Environment Management**: Different configs per environment

### Scripts and Automation

#### Package.json Scripts

- `dev`: Concurrent frontend and backend development
- `build`: Production build for both frontend and backend
- `start`: Production server startup
- `lint`: Code quality and style checking

#### Development Tools

- **Concurrently**: Run multiple development servers
- **Nodemon**: Backend auto-restart during development
- **TypeScript**: Static type checking across the stack

## Deployment Architecture

### Environment Configuration

#### Environment Variables

Comprehensive environment management via `.env` files:

##### Frontend Variables (VITE\_\*)

- `VITE_API_BASE_URL`: Backend API base URL
- `VITE_API_KEY`: Frontend API authentication
- `VITE_SUPABASE_URL`: Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Supabase public API key

##### Backend Variables

- `PORT`: Server port configuration
- `NODE_ENV`: Environment mode (development/production)
- `SESSION_SECRET`: Session encryption key
- `SUPABASE_SERVICE_ROLE_KEY`: Elevated Supabase access

##### AI Service Configuration

- `OPENAI_API_KEY`: OpenAI API access
- `LLAMA_CLOUD_API_KEY`: LlamaIndex cloud service
- `PINECONE_API_KEY`: Vector database access
- `LANGSMITH_API_KEY`: LLM observability platform

### Production Considerations

#### Scalability

- Horizontal scaling with load balancers
- Database connection pooling
- CDN integration for static assets
- Microservice architecture considerations

#### Monitoring and Observability

- Application performance monitoring
- Error tracking and alerting
- User analytics and behavior tracking
- Infrastructure monitoring and logging

#### Security Hardening

- Content Security Policy (CSP) headers
- Rate limiting and DDoS protection
- Regular security audits and updates
- Secrets management and rotation

## API Documentation

### RESTful API Design

#### Endpoint Patterns

- Resource-based URL structure
- HTTP methods for different operations (GET, POST, PUT, DELETE)
- Consistent response formats
- Error handling with appropriate status codes

#### Authentication

- API key authentication for backend routes
- Bearer token authentication for user-specific operations
- Session-based authentication for web interface

#### Request/Response Format

- JSON request and response bodies
- Consistent error response structure
- Pagination for list endpoints
- Filtering and sorting capabilities

### Knowledge Base API

#### Document Operations

```
POST   /api/knowledge/documents/upload     # Upload documents
GET    /api/knowledge/documents            # List documents
DELETE /api/knowledge/documents/:id        # Delete document
POST   /api/knowledge/documents/:id/reprocess # Reprocess document
GET    /api/knowledge/stats               # Get statistics
```

#### Chat Operations

```
POST   /api/knowledge/chat                # Send chat message
POST   /api/knowledge/chat/stream         # Streaming chat
GET    /api/knowledge/chat/sessions       # Get chat sessions
POST   /api/knowledge/search              # Search documents
```

### Additional API Endpoints

#### CopilotKit Integration

```
POST   /api/copilotkit/openai             # CopilotKit OpenAI integration
GET    /api/copilotkit/health             # CopilotKit health check
```

#### Authentication API

```
POST   /api/auth/login                    # User authentication
POST   /api/auth/logout                   # User logout
POST   /api/auth/refresh                  # Token refresh
GET    /api/auth/profile                  # User profile
```

## Testing Strategy

### Frontend Testing

#### Unit Testing

- Component testing with React Testing Library
- Custom hook testing with @testing-library/react-hooks
- Utility function testing with Jest
- Mock API responses for isolated testing

#### Integration Testing

- Context provider testing
- Route testing with React Router
- Form submission and validation testing
- API integration testing

#### End-to-End Testing

- User flow testing with Playwright or Cypress
- Authentication flow testing
- Document upload and chat functionality
- Cross-browser compatibility testing

### Backend Testing

#### Unit Testing

- API endpoint testing with Jest and Supertest
- Service layer unit testing
- Database operation testing with test database
- Middleware functionality testing

#### Integration Testing

- Full API flow testing
- Database integration testing
- External service integration testing
- File upload and processing testing

#### Performance Testing

- Load testing for API endpoints
- Database query performance testing
- File upload stress testing
- Memory leak detection

## Error Handling and Monitoring

### Frontend Error Handling

#### Error Boundaries

- React Error Boundaries for component error isolation
- Fallback UI components for graceful degradation
- Error reporting to monitoring services
- User-friendly error messages

#### Global Error Handling

- Unhandled promise rejection handling
- Network error handling and retry logic
- Form validation error display
- Toast notifications for user feedback

### Backend Error Handling

#### Express Error Middleware

- Centralized error handling middleware
- Error logging and reporting
- Appropriate HTTP status codes
- Security-conscious error responses (no sensitive data exposure)

#### Monitoring and Logging

- Structured logging with Winston or similar
- Application performance monitoring (APM)
- Error tracking with Sentry or similar
- Health checks and uptime monitoring

## Configuration Management

### Environment-Specific Configuration

#### Development Environment

- Local development server configuration
- Debug logging enabled
- Hot reload and development tools
- Mock external services for testing

#### Staging Environment

- Production-like configuration
- Limited debug logging
- Real external service integration
- Performance monitoring enabled

#### Production Environment

- Optimized performance settings
- Security hardening enabled
- Comprehensive monitoring
- Error reporting and alerting

### Feature Flags and Toggles

#### Implementation Strategy

- Environment-based feature toggles
- User-specific feature flags
- A/B testing capabilities
- Gradual feature rollout

#### Configuration Sources

- Environment variables for simple toggles
- Database-driven configuration for complex features
- External feature flag services integration
- Real-time configuration updates

## File and Asset Management

### Upload System Architecture

#### File Processing Pipeline

1. **Client-side validation**: File type, size, and format checking
2. **Secure upload**: Direct upload to cloud storage or server
3. **Virus scanning**: File security validation
4. **Metadata extraction**: File information and indexing
5. **Processing queue**: Background processing for large files
6. **Status tracking**: Real-time progress updates
7. **Cleanup**: Temporary file removal

#### Storage Strategy

- Cloud storage integration (AWS S3, Google Cloud Storage)
- Local storage for development
- CDN integration for asset delivery
- Backup and redundancy strategies

### Static Asset Management

#### Asset Optimization

- Image compression and format conversion
- CSS and JavaScript minification
- Bundle splitting and lazy loading
- Cache headers and versioning

#### CDN Integration

- Global content delivery network setup
- Asset versioning and cache invalidation
- Performance monitoring and optimization
- Fallback strategies for CDN failures

## Internationalization (i18n) Architecture

### Multi-language Support Preparation

#### Text Externalization

- All user-facing text moved to i18n files
- Consistent key naming conventions
- Pluralization and formatting support
- Context-aware translations

#### Locale Management

- Dynamic locale switching
- Browser locale detection
- User preference persistence
- Fallback language strategies

#### Right-to-Left (RTL) Support

- CSS logical properties usage
- Layout direction handling
- Icon and image mirroring
- Text alignment considerations

## Future Architecture Considerations

### Scalability Roadmap

- Microservice decomposition strategy
- Database sharding and partitioning
- Event-driven architecture implementation
- Real-time collaboration features

### Technology Evolution

- Modern React features adoption (Concurrent features, Suspense)
- Edge computing and CDN optimization
- WebAssembly integration for performance-critical operations
- Progressive Web App (PWA) capabilities

### AI/ML Enhancements

- Custom model fine-tuning
- Multi-modal AI capabilities (text, image, audio)
- Advanced RAG (Retrieval-Augmented Generation) techniques
- Real-time AI collaboration features

## Conclusion

The AltZero Platform represents a modern, scalable, and maintainable architecture that successfully integrates AI capabilities with a robust multi-tenant application structure. The architecture supports current requirements while providing flexibility for future enhancements and scaling needs.

### Key Architectural Strengths

1. **Separation of Concerns**: Clear boundaries between frontend, backend, and data layers
2. **Type Safety**: Comprehensive TypeScript usage across the entire stack
3. **Modern Technologies**: Leveraging current best practices and technologies
4. **Security First**: Multi-layered security approach with proper authentication and authorization
5. **Performance Optimized**: Built with performance considerations from the ground up
6. **Developer Experience**: Excellent development workflow with hot reload, linting, and type checking
7. **AI Integration**: Seamless integration of AI capabilities without compromising core functionality

### Maintenance and Evolution

This architecture document should be treated as a living document that evolves with the platform. Regular reviews and updates ensure that the documentation remains accurate and valuable for current and future development team members.

---

This comprehensive architecture document serves as the definitive guide to the AltZero Platform. As the application continues to evolve, this document should be updated to reflect architectural changes, new features, and technology adoption.
