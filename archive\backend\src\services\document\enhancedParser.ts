import { spawn } from "child_process";
import * as path from "path";
import * as fs from "fs";
import * as os from "os";
import { promisify } from "util";
import process from "process";
import { writeFileSync, unlinkSync } from "fs";
import { extname } from "path";
import { tmpdir } from "os";
import { existsSync } from "fs";

const execFile = promisify(require("child_process").execFile);

// Interface for document element with precise positioning
export interface DocumentElement {
  type: string; // "heading", "paragraph", "list-item", "image", "table", etc.
  content: string;
  level?: number; // For headings
  items?: string[]; // For lists
  isNumbered?: boolean; // Is this a numbered list
  order?: number;
  style?: {
    fontSize?: number;
    fontFamily?: string;
    isBold?: boolean;
    isItalic?: boolean;
    alignment?: "left" | "center" | "right" | "justified";
    color?: string;
    lineHeight?: number;
    marginTop?: number;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
  };
  position?: {
    x: number;
    y: number;
    page: number;
    width?: number;
    height?: number;
  };
  imageData?: {
    data: string; // Base64 encoded image
    width: number;
    height: number;
    position: {
      x: number;
      y: number;
      page: number;
    };
    alt?: string;
  };
}

// Enhanced interface for parsed document content with layout preservation
export interface ParsedDocumentContent {
  title: string;
  sections: {
    title: string;
    content: string;
    images?: string[];
    imageData?: Array<{
      data: string;
      width: number;
      height: number;
      position: {
        x: number;
        y: number;
        page: number;
      };
      alt?: string;
    }>;
    data?: Array<Array<string | number | boolean | null>>;
    order?: number;
    elements?: DocumentElement[]; // Structured elements with positioning
    pageStart?: number; // Starting page of this section
    pageEnd?: number; // Ending page of this section
  }[];
  metadata?: {
    author?: string;
    createdDate?: string;
    modifiedDate?: string;
    pageCount?: number;
    fileType?: string;
    sheetCount?: number;
  };
  fullContent?: string;
  pages?: {
    pageNumber: number;
    elements: DocumentElement[];
  }[];
}

interface DocumentResult {
  text?: string;
  images?: Array<{
    data: string;
    width?: number;
    height?: number;
  }>;
  title?: string;
  file_type?: string;
  error?: string;
}

/**
 * Enhanced document parser that extracts structured content from files,
 * including PDFs and Word documents with precise layout preservation.
 */
export class DocumentProcessor {
  private pythonScriptPath: string;

  constructor() {
    this.pythonScriptPath = path.join(
      __dirname,
      "..",
      "..",
      "..",
      "python_scripts",
      "document_processor.py"
    );
  }

  async processDocument(filePath: string): Promise<DocumentResult> {
    // Verify Python script exists
    if (!existsSync(this.pythonScriptPath)) {
      throw new Error(`Python script not found at: ${this.pythonScriptPath}`);
    }

    // Verify input file exists
    if (!existsSync(filePath)) {
      throw new Error(`Input file not found at: ${filePath}`);
    }

    return new Promise<DocumentResult>((resolve, reject) => {
      console.log(
        `Spawning Python process with script: ${this.pythonScriptPath}`
      );
      console.log(`Processing file: ${filePath}`);

      const pythonProcess = spawn("python", [this.pythonScriptPath, filePath]);
      let outputData = "";
      let errorData = "";

      pythonProcess.stdout.on("data", (data) => {
        outputData += data.toString();
      });

      pythonProcess.stderr.on("data", (data) => {
        errorData += data.toString();
        // Log stderr for debugging
        console.error("Python stderr:", data.toString());
      });

      pythonProcess.on("close", (code) => {
        if (code === 0 && outputData) {
          try {
            const result = JSON.parse(outputData);
            if (result.error) {
              reject(new Error(result.error));
            } else {
              resolve(result);
            }
          } catch (err) {
            reject(new Error("Failed to parse Python script output"));
          }
        } else {
          reject(new Error(errorData || "Document processing failed"));
        }
      });

      pythonProcess.on("error", (err) => {
        reject(err);
      });
    });
  }
}
