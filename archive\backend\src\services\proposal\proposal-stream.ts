import { Document, VectorStoreIndex } from "llamaindex";
import { OpenRouterClient } from "../llm/openrouter";
import { DocumentProcessor } from "../document/processor";
import { DocumentMetadata } from "../../types/llama";
import { environment } from "../../config/environment";
import fs from "fs";
import path from "path";
import { OpenAI } from "openai";
import { MockProposalService } from "./mock-proposal";

interface ProposalRequest {
  clientName: string;
  description: string;
  apiKey: string;
}

interface ProposalSection {
  title: string;
  content: string;
}

interface Proposal {
  id: string;
  clientName: string;
  description: string;
  sections: ProposalSection[];
  createdAt: Date;
  updatedAt: Date;
}

type ProposalEvent = {
  type: "started" | "research" | "summary" | "section" | "completed" | "error";
  proposalId: string;
  data?: any;
  error?: string;
};

export class ProposalStreamService {
  private llm: OpenRouterClient;
  private documentProcessor: DocumentProcessor;
  private index: VectorStoreIndex | null = null;
  private initialized = false;
  private mockService: MockProposalService;

  constructor() {
    this.llm = new OpenRouterClient();
    this.documentProcessor = new DocumentProcessor();
    this.mockService = new MockProposalService();
  }

  async initialize(): Promise<void> {
    try {
      console.log("Initializing LLM service...");

      this.llm = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1",
        timeout: 60000,
        maxRetries: 3,
      });

      console.log("Testing LLM connection...");

      try {
        const testResponse = await this.llm.chat.completions.create({
          messages: [{ role: "user", content: "Test" }],
          model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
          max_tokens: 10,
        });
        console.log("LLM connection test successful");
        this.initialized = true;
      } catch (testError) {
        // Sanitize error message
        const sanitizedError = testError.message?.replace(
          /sk-[a-zA-Z0-9_-]+/g,
          "[API_KEY]"
        );
        console.error("LLM test failed:", sanitizedError);
        throw testError;
      }
    } catch (error) {
      // Sanitize error message
      const sanitizedError = error.message?.replace(
        /sk-[a-zA-Z0-9_-]+/g,
        "[API_KEY]"
      );
      console.error("LLM initialization error:", sanitizedError);
      throw error;
    }
  }

  async *generateProposalStream(
    request: ProposalRequest
  ): AsyncGenerator<ProposalEvent> {
    const proposalId = `prop_${Date.now()}`;

    try {
      // Check for mock data first
      const mockData = await this.mockService.getMockedResponse(
        request.clientName
      );

      if (mockData) {
        console.log("Using mocked proposal data");

        // Send initial event
        yield {
          type: "started",
          proposalId,
          data: { message: "Starting proposal generation (mock)..." },
        };

        // Simulate research phase
        yield {
          type: "research",
          proposalId,
          data: { status: "completed", content: mockData.research.content },
        };

        // Simulate summary phase
        yield {
          type: "summary",
          proposalId,
          data: { status: "completed", content: mockData.summary.content },
        };

        // Simulate section generation
        for (const section of mockData.sections) {
          yield {
            type: "section",
            proposalId,
            data: { status: "completed", section },
          };
        }

        // Send completion event
        yield {
          type: "completed",
          proposalId,
          data: {
            proposal: {
              id: proposalId,
              clientName: request.clientName,
              description: request.description,
              sections: mockData.sections,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        };

        return;
      }

      // If no mock data, proceed with normal OpenAI flow
      if (!this.initialized) {
        console.log("Initializing LLM...");
        await this.initialize();
      }

      // Send initial event
      yield {
        type: "started",
        proposalId,
        data: { message: "Starting proposal generation..." },
      };

      const model = process.env.OPENAI_MODEL || "gpt-3.5-turbo";
      console.log(`Using model: ${model}`);
      console.log(
        `Starting proposal generation for client: ${request.clientName}`
      );

      const sections: ProposalSection[] = [];

      // Research phase
      yield {
        type: "research",
        proposalId,
        data: { status: "started" },
      };

      const researchPrompt = `Research the following client and project description to gather relevant information:
      Client: ${request.clientName}
      Description: ${request.description}`;

      let researchResponse;

      // Add progress events before each major operation
      yield {
        type: "started",
        proposalId,
        data: { message: "Processing..." },
      };
      try {
        // Make sure to specify a model that's available with your API key
        researchResponse = await this.llm.chat.completions.create({
          messages: [{ role: "user", content: researchPrompt }],
          model: model,
          max_tokens: 1000,
          temperature: 0.7,
        });
      } catch (apiError) {
        console.error("API call error:", apiError);

        // Yield error event that the frontend can handle
        yield {
          type: "error",
          proposalId,
          error: apiError.message || "API call failed",
          data: {
            details: JSON.stringify(apiError),
          },
        };
        return;
      }

      yield {
        type: "research",
        proposalId,
        data: {
          status: "completed",
          content: researchResponse.choices[0].message.content,
        },
      };

      // Executive summary
      yield {
        type: "summary",
        proposalId,
        data: { status: "started" },
      };

      const summaryPrompt = `Based on the following research, create an executive summary for a proposal:
      Research: ${researchResponse.choices[0].message.content}`;

      let summaryResponse;

      // Add progress events before each major operation
      yield {
        type: "started",
        proposalId,
        data: { message: "Processing..." },
      };
      try {
        // Make sure to specify a model that's available with your API key
        summaryResponse = await this.llm.chat.completions.create({
          messages: [{ role: "user", content: summaryPrompt }],
          model: model,
          max_tokens: 1000,
          temperature: 0.7,
        });
      } catch (apiError) {
        console.error("API call error:", apiError);

        // Yield error event that the frontend can handle
        yield {
          type: "error",
          proposalId,
          error: apiError.message || "API call failed",
          data: {
            details: JSON.stringify(apiError),
          },
        };
        return;
      }

      yield {
        type: "summary",
        proposalId,
        data: {
          status: "completed",
          content: summaryResponse.choices[0].message.content,
        },
      };

      // Generate sections
      const sectionTitles = [
        "Project Overview",
        "Our Approach",
        "Deliverables",
        "Timeline",
        "Investment",
      ];

      for (const title of sectionTitles) {
        yield {
          type: "started",
          proposalId,
          data: { message: "Processing..." },
        };
        yield {
          type: "section",
          proposalId,
          data: { status: "started", title },
        };

        const sectionPrompt = `Generate the ${title} section for a proposal based on:
        Client: ${request.clientName}
        Description: ${request.description}
        Research: ${researchResponse.choices[0].message.content}
        Executive Summary: ${summaryResponse.choices[0].message.content}`;

        let sectionResponse;

        try {
          // Make sure to specify a model that's available with your API key
          sectionResponse = await this.llm.chat.completions.create({
            messages: [{ role: "user", content: sectionPrompt }],
            model: model,
            max_tokens: 1000,
            temperature: 0.7,
          });
        } catch (apiError) {
          console.error("API call error:", apiError);

          // Yield error event that the frontend can handle
          yield {
            type: "error",
            proposalId,
            error: apiError.message || "API call failed",
            data: {
              details: JSON.stringify(apiError),
            },
          };
          return;
        }

        const section: ProposalSection = {
          title,
          content: sectionResponse.choices[0].message.content,
        };

        sections.push(section);

        yield {
          type: "section",
          proposalId,
          data: { status: "completed", section },
        };
      }

      // Create final proposal
      const proposal: Proposal = {
        id: proposalId,
        clientName: request.clientName,
        description: request.description,
        sections,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Completed event
      yield {
        type: "completed",
        proposalId,
        data: { proposal },
      };
    } catch (error) {
      const sanitizedError = error.message?.replace(
        /sk-[a-zA-Z0-9_-]+/g,
        "[API_KEY]"
      );
      console.error("Error in proposal generation:", sanitizedError);
      yield {
        type: "error",
        proposalId,
        error: "An error occurred during proposal generation",
      };
    }
  }

  async deleteAll(): Promise<void> {
    try {
      await this.documentProcessor.deleteAllDocuments();
      this.index = null;
      console.log("Successfully deleted all documents and reset index");
    } catch (error) {
      console.error("Error deleting documents:", error);
      throw error;
    }
  }
}
