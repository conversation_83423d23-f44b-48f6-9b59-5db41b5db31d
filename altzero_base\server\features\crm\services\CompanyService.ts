import { supabase } from '../../../base/common/apps/supabase';

export interface Company {
  id?: string;
  organisation_id: string;
  name: string;
  website?: string;
  email?: string;
  phone?: string;
  fax?: string;
  address?: any;
  employees?: number;
  revenues?: string;
  tax_number?: string;
  region_id?: string;
  owner_id?: string;
  external_id?: string;
  custom_fields?: any;
  created_at?: string;
  updated_at?: string;
}

export interface CompanyFilters {
  page: number;
  limit: number;
  search?: string;
  userId?: string;
}

export class CompanyService {
  
  /**
   * Get user's organization IDs from organisation_members table
   */
  private async getUserOrganisationIds(userId: string): Promise<string[]> {
    const { data, error } = await supabase
      .from('organisation_members')
      .select('organisation_id')
      .eq('user_id', userId);
    
    if (error) {
      console.error('Error fetching user organizations:', error);
      return [];
    }
    
    return data.map(row => row.organisation_id);
  }

  /**
   * Get companies with pagination and filtering
   */
  async getCompanies(filters: CompanyFilters) {
    try {
      if (!filters.userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(filters.userId);
      if (organisationIds.length === 0) {
        return { data: [], total: 0, page: filters.page, limit: filters.limit };
      }

      let query = supabase
        .from('crm_companies')
        .select('*', { count: 'exact' })
        .in('organisation_id', organisationIds);

      // Apply search filter
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,website.ilike.%${filters.search}%`);
      }

      // Apply pagination
      const offset = (filters.page - 1) * filters.limit;
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + filters.limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching companies:', error);
        throw error;
      }

      return {
        data: data || [],
        total: count || 0,
        page: filters.page,
        limit: filters.limit
      };
    } catch (error) {
      console.error('CompanyService.getCompanies error:', error);
      throw error;
    }
  }

  /**
   * Get a single company by ID
   */
  async getCompanyById(companyId: string, userId: string): Promise<Company | null> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return null;
      }

      const { data, error } = await supabase
        .from('crm_companies')
        .select('*')
        .eq('id', companyId)
        .in('organisation_id', organisationIds)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        console.error('Error fetching company:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('CompanyService.getCompanyById error:', error);
      throw error;
    }
  }

  /**
   * Create a new company
   */
  async createCompany(companyData: Omit<Company, 'id' | 'created_at' | 'updated_at'>, userId: string): Promise<Company> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        throw new Error('User is not a member of any organization');
      }

      // Use the first organization if not specified
      if (!companyData.organisation_id) {
        companyData.organisation_id = organisationIds[0];
      }

      // Verify user has access to the specified organization
      if (!organisationIds.includes(companyData.organisation_id)) {
        throw new Error('User does not have access to the specified organization');
      }

      // Set owner_id to current user if not specified
      if (!companyData.owner_id) {
        companyData.owner_id = userId;
      }

      const { data, error } = await supabase
        .from('crm_companies')
        .insert([companyData])
        .select()
        .single();

      if (error) {
        console.error('Error creating company:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('CompanyService.createCompany error:', error);
      throw error;
    }
  }

  /**
   * Update an existing company
   */
  async updateCompany(companyId: string, companyData: Partial<Company>, userId: string): Promise<Company | null> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return null;
      }

      // Remove fields that shouldn't be updated
      const { id, created_at, ...updateData } = companyData;
      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('crm_companies')
        .update(updateData)
        .eq('id', companyId)
        .in('organisation_id', organisationIds)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        console.error('Error updating company:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('CompanyService.updateCompany error:', error);
      throw error;
    }
  }

  /**
   * Delete a company
   */
  async deleteCompany(companyId: string, userId: string): Promise<boolean> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return false;
      }

      const { error } = await supabase
        .from('crm_companies')
        .delete()
        .eq('id', companyId)
        .in('organisation_id', organisationIds);

      if (error) {
        console.error('Error deleting company:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('CompanyService.deleteCompany error:', error);
      throw error;
    }
  }

  /**
   * Get companies with contact count
   */
  async getCompaniesWithContactCount(userId: string) {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return [];
      }

      const { data, error } = await supabase
        .from('crm_companies')
        .select(`
          *,
          crm_contacts!crm_contacts_company_id_fkey (count)
        `)
        .in('organisation_id', organisationIds)
        .order('name');

      if (error) {
        console.error('Error fetching companies with contact count:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('CompanyService.getCompaniesWithContactCount error:', error);
      throw error;
    }
  }
}
