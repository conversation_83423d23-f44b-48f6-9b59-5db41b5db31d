-- =====================================================
-- AGENTIC pSEO DATABASE MIGRATION
-- Adds new tables for full-website analysis capabilities
-- PRESERVES all existing tables and functionality
-- =====================================================

-- Website pages discovered during full-site analysis
CREATE TABLE pseo_website_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  url VARCHAR(1000) NOT NULL,
  title VARCHAR(500),
  meta_description TEXT,
  content_hash VARCHAR(64), -- SHA-256 hash for change detection
  page_type VARCHAR(50) DEFAULT 'page' CHECK (page_type IN ('page', 'blog', 'product', 'category', 'homepage')),
  status VARCHAR(50) DEFAULT 'discovered' CHECK (status IN ('discovered', 'crawled', 'analyzed', 'error')),
  discovered_via VARCHAR(50) DEFAULT 'sitemap' CHECK (discovered_via IN ('sitemap', 'crawl', 'manual')),
  last_crawled TIMESTAMP WITH TIME ZONE,
  crawl_error TEXT,
  word_count INTEGER,
  response_code INTEGER,
  load_time_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(website_id, url)
);

-- Domain-level keyword research data
CREATE TABLE pseo_keywords (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  keyword VARCHAR(255) NOT NULL,
  search_volume INTEGER,
  keyword_difficulty INTEGER CHECK (keyword_difficulty >= 0 AND keyword_difficulty <= 100),
  cpc DECIMAL(10,2),
  competition VARCHAR(20) CHECK (competition IN ('low', 'medium', 'high')),
  ranking_position INTEGER,
  ranking_url VARCHAR(1000),
  intent VARCHAR(50) CHECK (intent IN ('informational', 'navigational', 'commercial', 'transactional')),
  data_source VARCHAR(50) DEFAULT 'manual' CHECK (data_source IN ('google_planner', 'ubersuggest', 'dataforseo', 'manual')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Backlink analysis data
CREATE TABLE pseo_backlinks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  source_domain VARCHAR(255) NOT NULL,
  source_url VARCHAR(1000) NOT NULL,
  target_url VARCHAR(1000) NOT NULL,
  anchor_text VARCHAR(500),
  link_type VARCHAR(20) DEFAULT 'unknown' CHECK (link_type IN ('dofollow', 'nofollow', 'unknown')),
  domain_authority INTEGER CHECK (domain_authority >= 0 AND domain_authority <= 100),
  page_authority INTEGER CHECK (page_authority >= 0 AND page_authority <= 100),
  spam_score INTEGER CHECK (spam_score >= 0 AND spam_score <= 100),
  first_seen TIMESTAMP WITH TIME ZONE,
  last_seen TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'lost', 'unknown')),
  data_source VARCHAR(50) DEFAULT 'manual' CHECK (data_source IN ('moz', 'majestic', 'ahrefs', 'manual')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content opportunities and gap analysis
CREATE TABLE pseo_content_opportunities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  target_keyword VARCHAR(255) NOT NULL,
  content_type VARCHAR(50) DEFAULT 'blog' CHECK (content_type IN ('blog', 'landing', 'product', 'guide', 'faq')),
  title VARCHAR(500) NOT NULL,
  content_brief TEXT,
  target_word_count INTEGER,
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  difficulty_score INTEGER CHECK (difficulty_score >= 0 AND difficulty_score <= 100),
  estimated_traffic INTEGER,
  status VARCHAR(30) DEFAULT 'identified' CHECK (status IN ('identified', 'in_progress', 'completed', 'abandoned')),
  assigned_to VARCHAR(255),
  due_date TIMESTAMP WITH TIME ZONE,
  generated_content TEXT,
  ai_model_used VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI-generated content storage (MAIN CONTENT TABLE)
CREATE TABLE pseo_content_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  opportunity_id UUID REFERENCES pseo_content_opportunities(id) ON DELETE SET NULL,
  title VARCHAR(500) NOT NULL,
  slug VARCHAR(500) NOT NULL,
  content_markdown TEXT NOT NULL, -- 🎯 MAIN MARKDOWN STORAGE
  content_html TEXT, -- Converted HTML version
  meta_description VARCHAR(300),
  target_keywords TEXT[] DEFAULT '{}',
  content_type VARCHAR(50) DEFAULT 'blog_post' CHECK (content_type IN ('blog_post', 'landing_page', 'guide', 'faq', 'product_description')),
  word_count INTEGER DEFAULT 0,
  ai_generated BOOLEAN DEFAULT FALSE, -- 🤖 AI content flag
  generated_by_agent VARCHAR(100), -- Which AI agent created it
  ai_model_used VARCHAR(100),
  seo_score INTEGER CHECK (seo_score >= 0 AND seo_score <= 100),
  readability_score INTEGER CHECK (readability_score >= 0 AND readability_score <= 100),
  status VARCHAR(30) DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'approved', 'published', 'archived')),
  scheduled_publish_date TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  author VARCHAR(255),
  editor VARCHAR(255),
  internal_notes TEXT,
  external_url VARCHAR(1000), -- URL where content was published
  performance_data JSONB DEFAULT '{}', -- Views, clicks, conversions, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content workflow management
CREATE TABLE pseo_content_workflow (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content_id UUID REFERENCES pseo_content_items(id) ON DELETE CASCADE,
  workflow_step VARCHAR(50) NOT NULL CHECK (workflow_step IN ('generation', 'review', 'editing', 'seo_optimization', 'approval', 'scheduling', 'publishing')),
  status VARCHAR(30) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'rejected', 'skipped')),
  assigned_to VARCHAR(255),
  completed_by VARCHAR(255),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content calendar for publishing schedule
CREATE TABLE pseo_content_calendar (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  content_id UUID REFERENCES pseo_content_items(id) ON DELETE CASCADE,
  title VARCHAR(500) NOT NULL,
  scheduled_date DATE NOT NULL,
  scheduled_time TIME DEFAULT '09:00:00',
  target_keyword VARCHAR(255),
  content_type VARCHAR(50),
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  estimated_traffic INTEGER,
  status VARCHAR(30) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'published', 'cancelled', 'rescheduled')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agentic job tracking and orchestration
CREATE TABLE pseo_agent_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  job_type VARCHAR(50) NOT NULL CHECK (job_type IN ('page_discovery', 'keyword_research', 'backlink_analysis', 'content_generation', 'full_site_audit')),
  agent_name VARCHAR(100) NOT NULL,
  parent_job_id UUID REFERENCES pseo_agent_jobs(id),
  status VARCHAR(30) DEFAULT 'queued' CHECK (status IN ('queued', 'running', 'completed', 'failed', 'cancelled')),
  priority INTEGER DEFAULT 0,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  processing_time_seconds INTEGER,
  result_data JSONB DEFAULT '{}',
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Website analysis summaries (aggregated insights)
CREATE TABLE pseo_website_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE UNIQUE,
  total_pages INTEGER DEFAULT 0,
  crawled_pages INTEGER DEFAULT 0,
  total_keywords INTEGER DEFAULT 0,
  ranking_keywords INTEGER DEFAULT 0,
  total_backlinks INTEGER DEFAULT 0,
  referring_domains INTEGER DEFAULT 0,
  domain_authority INTEGER CHECK (domain_authority >= 0 AND domain_authority <= 100),
  organic_traffic_estimate INTEGER,
  content_gaps_identified INTEGER DEFAULT 0,
  technical_issues_count INTEGER DEFAULT 0,
  last_full_analysis TIMESTAMP WITH TIME ZONE,
  analysis_status VARCHAR(30) DEFAULT 'pending' CHECK (analysis_status IN ('pending', 'in_progress', 'completed', 'error')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Website pages indexes
CREATE INDEX idx_pseo_website_pages_website_id ON pseo_website_pages(website_id);
CREATE INDEX idx_pseo_website_pages_status ON pseo_website_pages(status);
CREATE INDEX idx_pseo_website_pages_discovered_via ON pseo_website_pages(discovered_via);
CREATE INDEX idx_pseo_website_pages_last_crawled ON pseo_website_pages(last_crawled DESC);
CREATE INDEX idx_pseo_website_pages_url_hash ON pseo_website_pages USING hash(url);

-- Keywords indexes
CREATE INDEX idx_pseo_keywords_website_id ON pseo_keywords(website_id);
CREATE INDEX idx_pseo_keywords_keyword ON pseo_keywords(keyword);
CREATE INDEX idx_pseo_keywords_search_volume ON pseo_keywords(search_volume DESC);
CREATE INDEX idx_pseo_keywords_difficulty ON pseo_keywords(keyword_difficulty);
CREATE INDEX idx_pseo_keywords_ranking_position ON pseo_keywords(ranking_position);

-- Backlinks indexes
CREATE INDEX idx_pseo_backlinks_website_id ON pseo_backlinks(website_id);
CREATE INDEX idx_pseo_backlinks_source_domain ON pseo_backlinks(source_domain);
CREATE INDEX idx_pseo_backlinks_domain_authority ON pseo_backlinks(domain_authority DESC);
CREATE INDEX idx_pseo_backlinks_status ON pseo_backlinks(status);
CREATE INDEX idx_pseo_backlinks_last_seen ON pseo_backlinks(last_seen DESC);

-- Content opportunities indexes
CREATE INDEX idx_pseo_content_opportunities_website_id ON pseo_content_opportunities(website_id);
CREATE INDEX idx_pseo_content_opportunities_priority ON pseo_content_opportunities(priority);
CREATE INDEX idx_pseo_content_opportunities_status ON pseo_content_opportunities(status);
CREATE INDEX idx_pseo_content_opportunities_target_keyword ON pseo_content_opportunities(target_keyword);

-- Agent jobs indexes
CREATE INDEX idx_pseo_agent_jobs_website_id ON pseo_agent_jobs(website_id);
CREATE INDEX idx_pseo_agent_jobs_job_type ON pseo_agent_jobs(job_type);
CREATE INDEX idx_pseo_agent_jobs_status ON pseo_agent_jobs(status);
CREATE INDEX idx_pseo_agent_jobs_parent_job_id ON pseo_agent_jobs(parent_job_id);
CREATE INDEX idx_pseo_agent_jobs_created_at ON pseo_agent_jobs(created_at DESC);
CREATE INDEX idx_pseo_agent_jobs_priority ON pseo_agent_jobs(priority DESC);

-- Website analysis indexes
CREATE INDEX idx_pseo_website_analysis_website_id ON pseo_website_analysis(website_id);
CREATE INDEX idx_pseo_website_analysis_status ON pseo_website_analysis(analysis_status);
CREATE INDEX idx_pseo_website_analysis_last_analysis ON pseo_website_analysis(last_full_analysis DESC);

-- Content items indexes
CREATE INDEX idx_pseo_content_items_website_id ON pseo_content_items(website_id);
CREATE INDEX idx_pseo_content_items_status ON pseo_content_items(status);
CREATE INDEX idx_pseo_content_items_ai_generated ON pseo_content_items(ai_generated);
CREATE INDEX idx_pseo_content_items_generated_by_agent ON pseo_content_items(generated_by_agent);
CREATE INDEX idx_pseo_content_items_content_type ON pseo_content_items(content_type);
CREATE INDEX idx_pseo_content_items_published_at ON pseo_content_items(published_at DESC);
CREATE INDEX idx_pseo_content_items_scheduled_publish_date ON pseo_content_items(scheduled_publish_date);
CREATE UNIQUE INDEX idx_pseo_content_items_website_slug ON pseo_content_items(website_id, slug);

-- Content workflow indexes
CREATE INDEX idx_pseo_content_workflow_content_id ON pseo_content_workflow(content_id);
CREATE INDEX idx_pseo_content_workflow_step_status ON pseo_content_workflow(workflow_step, status);
CREATE INDEX idx_pseo_content_workflow_assigned_to ON pseo_content_workflow(assigned_to);

-- Content calendar indexes
CREATE INDEX idx_pseo_content_calendar_website_id ON pseo_content_calendar(website_id);
CREATE INDEX idx_pseo_content_calendar_scheduled_date ON pseo_content_calendar(scheduled_date);
CREATE INDEX idx_pseo_content_calendar_status ON pseo_content_calendar(status);
CREATE INDEX idx_pseo_content_calendar_priority ON pseo_content_calendar(priority);

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE pseo_website_pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_backlinks ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_content_opportunities ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_content_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_content_workflow ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_content_calendar ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_agent_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_website_analysis ENABLE ROW LEVEL SECURITY;

-- RLS Policies (users can only access data for their websites)
CREATE POLICY "Users can manage website pages of their websites" ON pseo_website_pages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_website_pages.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage keywords of their websites" ON pseo_keywords
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_keywords.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage backlinks of their websites" ON pseo_backlinks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_backlinks.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage content opportunities of their websites" ON pseo_content_opportunities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_content_opportunities.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage agent jobs of their websites" ON pseo_agent_jobs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_agent_jobs.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage website analysis of their websites" ON pseo_website_analysis
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_website_analysis.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage content items of their websites" ON pseo_content_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_content_items.website_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage content workflow of their content" ON pseo_content_workflow
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_content_items ci
      JOIN pseo_websites w ON ci.website_id = w.id
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE ci.id = pseo_content_workflow.content_id 
      AND c.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage content calendar of their websites" ON pseo_content_calendar
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_content_calendar.website_id 
      AND c.user_id = auth.uid()
    )
  );

-- =====================================================
-- UPDATED_AT TRIGGERS
-- =====================================================

-- Apply updated_at triggers to new tables
CREATE TRIGGER update_pseo_website_pages_updated_at 
  BEFORE UPDATE ON pseo_website_pages 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_keywords_updated_at 
  BEFORE UPDATE ON pseo_keywords 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_content_opportunities_updated_at 
  BEFORE UPDATE ON pseo_content_opportunities 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_agent_jobs_updated_at 
  BEFORE UPDATE ON pseo_agent_jobs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_website_analysis_updated_at 
  BEFORE UPDATE ON pseo_website_analysis 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_content_items_updated_at 
  BEFORE UPDATE ON pseo_content_items 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_content_calendar_updated_at 
  BEFORE UPDATE ON pseo_content_calendar 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT ALL ON pseo_website_pages TO authenticated;
GRANT ALL ON pseo_keywords TO authenticated;
GRANT ALL ON pseo_backlinks TO authenticated;
GRANT ALL ON pseo_content_opportunities TO authenticated;
GRANT ALL ON pseo_content_items TO authenticated;
GRANT ALL ON pseo_content_workflow TO authenticated;
GRANT ALL ON pseo_content_calendar TO authenticated;
GRANT ALL ON pseo_agent_jobs TO authenticated;
GRANT ALL ON pseo_website_analysis TO authenticated;

-- =====================================================
-- HELPFUL VIEWS FOR AGENTIC FEATURES
-- =====================================================

-- Website discovery progress view
CREATE VIEW pseo_website_discovery_progress AS
SELECT 
  w.id as website_id,
  w.name as website_name,
  w.domain,
  COUNT(wp.id) as total_discovered_pages,
  COUNT(CASE WHEN wp.status = 'crawled' THEN 1 END) as crawled_pages,
  COUNT(CASE WHEN wp.status = 'analyzed' THEN 1 END) as analyzed_pages,
  COUNT(CASE WHEN wp.status = 'error' THEN 1 END) as error_pages,
  MAX(wp.last_crawled) as last_crawl_date,
  AVG(wp.load_time_ms) as avg_load_time
FROM pseo_websites w
LEFT JOIN pseo_website_pages wp ON w.id = wp.website_id
GROUP BY w.id, w.name, w.domain;

-- Keyword opportunities view
CREATE VIEW pseo_keyword_opportunities AS
SELECT 
  w.id as website_id,
  w.name as website_name,
  k.keyword,
  k.search_volume,
  k.keyword_difficulty,
  k.ranking_position,
  CASE 
    WHEN k.ranking_position IS NULL THEN 'not_ranking'
    WHEN k.ranking_position <= 3 THEN 'top_3'
    WHEN k.ranking_position <= 10 THEN 'page_1'
    WHEN k.ranking_position <= 20 THEN 'page_2'
    ELSE 'beyond_page_2'
  END as ranking_tier,
  k.intent,
  k.data_source
FROM pseo_websites w
JOIN pseo_keywords k ON w.id = k.website_id
WHERE k.search_volume > 0;

-- Content gap analysis view
CREATE VIEW pseo_content_gaps AS
SELECT 
  w.id as website_id,
  w.name as website_name,
  co.target_keyword,
  co.content_type,
  co.priority,
  co.difficulty_score,
  co.estimated_traffic,
  co.status,
  co.title,
  CASE 
    WHEN co.status = 'completed' THEN 'content_exists'
    WHEN co.priority = 'critical' AND co.difficulty_score < 50 THEN 'quick_win'
    WHEN co.estimated_traffic > 1000 THEN 'high_impact'
    ELSE 'standard_opportunity'
  END as opportunity_type
FROM pseo_websites w
JOIN pseo_content_opportunities co ON w.id = co.website_id;

-- Agent job status summary view
CREATE VIEW pseo_agent_job_summary AS
SELECT 
  w.id as website_id,
  w.name as website_name,
  aj.job_type,
  COUNT(*) as total_jobs,
  COUNT(CASE WHEN aj.status = 'completed' THEN 1 END) as completed_jobs,
  COUNT(CASE WHEN aj.status = 'running' THEN 1 END) as running_jobs,
  COUNT(CASE WHEN aj.status = 'failed' THEN 1 END) as failed_jobs,
  AVG(aj.processing_time_seconds) as avg_processing_time,
  MAX(aj.completed_at) as last_completed
FROM pseo_websites w
LEFT JOIN pseo_agent_jobs aj ON w.id = aj.website_id
GROUP BY w.id, w.name, aj.job_type;

-- Grant permissions on views
GRANT SELECT ON pseo_website_discovery_progress TO authenticated;
GRANT SELECT ON pseo_keyword_opportunities TO authenticated;
GRANT SELECT ON pseo_content_gaps TO authenticated;
GRANT SELECT ON pseo_agent_job_summary TO authenticated;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Verify new tables were created
SELECT 
  schemaname,
  tablename,
  tableowner,
  hasindexes,
  hasrules,
  hastriggers
FROM pg_tables 
WHERE tablename LIKE 'pseo_%'
  AND tablename NOT IN ('pseo_clients', 'pseo_websites', 'pseo_audits', 'pseo_audit_steps')
ORDER BY tablename; 