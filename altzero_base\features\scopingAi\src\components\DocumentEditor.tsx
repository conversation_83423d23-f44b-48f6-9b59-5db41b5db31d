import React, { useEffect, useState, useRef } from "react";
import { But<PERSON> } from "@base/components/ui/button";
import {
  Type,
  Heading,
  Image as ImageIcon,
  Quote,
  List,
  Table,
  Trash2,
  MoveUp,
  MoveDown,
  ChevronDown,
  Plus,
  PlusCircle,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Bold,
  Italic,
  Underline,
  Link,
  Heading1,
  Heading2,
  Heading3,
} from "lucide-react";
import { cn } from "../lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@base/components/ui/popover";
import { Textarea } from "@base/components/ui/textarea";
import { ScrollArea } from "@base/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@base/components/ui/select";

// Types for blocks
export type BlockType =
  | "header"
  | "text"
  | "image"
  | "quote"
  | "list"
  | "table";

export interface Block {
  id: string;
  type: BlockType;
  content: string;
  level?: number; // For headers
  imageUrl?: string; // For images
  alignment?: "left" | "center" | "right" | "justify";
  listType?: "bullet" | "numbered"; // For lists
}

interface DocumentEditorProps {
  content: string;
  onChange: (content: string) => void;
  images?: any[]; // Optional array of images that can be inserted
}

export function DocumentEditor({
  content,
  onChange,
  images = [],
}: DocumentEditorProps) {
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [showMenu, setShowMenu] = useState<string | null>(null);
  const [focusedBlockId, setFocusedBlockId] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Parse content into blocks
  useEffect(() => {
    try {
      // First, try to parse as JSON if it looks like our blocks format
      if (content.trim().startsWith("[") && content.trim().endsWith("]")) {
        try {
          const parsedBlocks = JSON.parse(content);
          if (
            Array.isArray(parsedBlocks) &&
            parsedBlocks.every(
              (block) =>
                typeof block === "object" &&
                "type" in block &&
                "content" in block
            )
          ) {
            setBlocks(parsedBlocks);
            return;
          }
        } catch (e) {
          console.log("Not valid JSON blocks, trying other formats");
        }
      }

      // If not our format, create a text block with the content
      setBlocks([
        {
          id: generateId(),
          type: "text",
          content: content || "",
        },
      ]);
    } catch (error) {
      console.error("Error parsing content:", error);
      // Create an empty text block as fallback
      setBlocks([
        {
          id: generateId(),
          type: "text",
          content: "",
        },
      ]);
    }
  }, [content]);

  // Update parent component when blocks change
  useEffect(() => {
    if (blocks.length > 0) {
      const serializedContent = JSON.stringify(blocks);
      onChange(serializedContent);
    }
  }, [blocks, onChange]);

  // Handle click outside block menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Generate unique ID for blocks
  const generateId = () => {
    return Math.random().toString(36).substring(2, 15);
  };

  // Add a new block
  const addBlock = (type: BlockType, index: number) => {
    const newBlock: Block = {
      id: generateId(),
      type,
      content: "",
    };

    // Set specific properties based on block type
    switch (type) {
      case "header":
        newBlock.level = 2;
        break;
      case "image":
        newBlock.imageUrl = "";
        break;
      case "list":
        newBlock.listType = "bullet";
        break;
      case "quote":
      case "table":
        break;
      default:
        break;
    }

    const newBlocks = [...blocks];
    newBlocks.splice(index + 1, 0, newBlock);
    setBlocks(newBlocks);

    // Focus the new block after it's added to the DOM
    setTimeout(() => {
      setFocusedBlockId(newBlock.id);
      setShowMenu(null);
    }, 10);
  };

  // Delete a block
  const deleteBlock = (index: number) => {
    if (blocks.length === 1) {
      // Don't delete the last block, just clear it
      setBlocks([
        {
          id: blocks[0].id,
          type: "text",
          content: "",
        },
      ]);
    } else {
      const newBlocks = [...blocks];
      newBlocks.splice(index, 1);
      setBlocks(newBlocks);
    }
  };

  // Move a block up or down
  const moveBlock = (index: number, direction: "up" | "down") => {
    if (
      (direction === "up" && index === 0) ||
      (direction === "down" && index === blocks.length - 1)
    ) {
      return; // Can't move further
    }

    const newBlocks = [...blocks];
    const blockToMove = newBlocks[index];
    newBlocks.splice(index, 1);
    newBlocks.splice(
      direction === "up" ? index - 1 : index + 1,
      0,
      blockToMove
    );
    setBlocks(newBlocks);
  };

  // Update block content
  const updateBlockContent = (index: number, content: string) => {
    const newBlocks = [...blocks];
    newBlocks[index] = { ...newBlocks[index], content };
    setBlocks(newBlocks);
  };

  // Update block properties
  const updateBlockProps = (index: number, props: Partial<Block>) => {
    const newBlocks = [...blocks];
    newBlocks[index] = { ...newBlocks[index], ...props };
    setBlocks(newBlocks);
  };

  // Change block type
  const changeBlockType = (index: number, type: BlockType) => {
    const newBlocks = [...blocks];
    const currentContent = newBlocks[index].content;

    const newBlock: Block = {
      id: newBlocks[index].id,
      type,
      content: currentContent,
    };

    // Add type-specific properties
    if (type === "header") {
      newBlock.level = 2;
    } else if (type === "image") {
      newBlock.imageUrl = "";
    } else if (type === "list") {
      newBlock.listType = "bullet";
    }

    newBlocks[index] = newBlock;
    setBlocks(newBlocks);
    setShowMenu(null);
  };

  // Toggle block menu
  const toggleBlockMenu = (blockId: string) => {
    setShowMenu(showMenu === blockId ? null : blockId);
  };

  // Set alignment for a block
  const setBlockAlignment = (
    index: number,
    alignment: "left" | "center" | "right" | "justify"
  ) => {
    const newBlocks = [...blocks];
    newBlocks[index] = { ...newBlocks[index], alignment };
    setBlocks(newBlocks);
  };

  // Set heading level
  const setHeaderLevel = (index: number, level: number) => {
    const newBlocks = [...blocks];
    newBlocks[index] = { ...newBlocks[index], level };
    setBlocks(newBlocks);
  };

  // Set list type (bullet or numbered)
  const setListType = (index: number, listType: "bullet" | "numbered") => {
    const newBlocks = [...blocks];
    newBlocks[index] = { ...newBlocks[index], listType };
    setBlocks(newBlocks);
  };

  // Render different block types
  const renderBlock = (block: Block, index: number) => {
    const isFocused = focusedBlockId === block.id;

    // Common props for content editable areas
    const contentEditableProps = {
      onFocus: () => setFocusedBlockId(block.id),
      onBlur: () => setFocusedBlockId(null),
      style: block.alignment ? { textAlign: block.alignment } : undefined,
    };

    let blockContent;

    switch (block.type) {
      case "header":
        blockContent = (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Select
                value={String(block.level || 2)}
                onValueChange={(value) => setHeaderLevel(index, Number(value))}
              >
                <SelectTrigger className="w-36">
                  <SelectValue placeholder="Heading Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">
                    <div className="flex items-center">
                      <Heading1 className="h-4 w-4 mr-2" />
                      <span>Heading 1</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="2">
                    <div className="flex items-center">
                      <Heading2 className="h-4 w-4 mr-2" />
                      <span>Heading 2</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="3">
                    <div className="flex items-center">
                      <Heading3 className="h-4 w-4 mr-2" />
                      <span>Heading 3</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div
              className={cn(
                "outline-none font-bold w-full",
                block.level === 1
                  ? "text-2xl"
                  : block.level === 2
                    ? "text-xl"
                    : block.level === 3
                      ? "text-lg"
                      : "text-base"
              )}
              contentEditable
              suppressContentEditableWarning
              onInput={(e) =>
                updateBlockContent(index, e.currentTarget.textContent || "")
              }
              dangerouslySetInnerHTML={{ __html: block.content }}
              {...contentEditableProps}
            />
          </div>
        );
        break;

      case "text":
        blockContent = (
          <p
            className="outline-none py-1 w-full min-h-[28px]"
            contentEditable
            suppressContentEditableWarning
            onInput={(e) =>
              updateBlockContent(index, e.currentTarget.textContent || "")
            }
            dangerouslySetInnerHTML={{ __html: block.content }}
            {...contentEditableProps}
          />
        );
        break;

      case "image":
        blockContent = (
          <div
            className={cn(
              "py-2",
              block.alignment === "center"
                ? "flex justify-center"
                : block.alignment === "right"
                  ? "flex justify-end"
                  : ""
            )}
          >
            {block.imageUrl ? (
              <div className="relative group">
                <img
                  src={block.imageUrl}
                  alt="Document image"
                  className="max-w-full max-h-96 object-contain rounded"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white"
                  onClick={() => updateBlockProps(index, { imageUrl: "" })}
                >
                  Change
                </Button>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                <imgIcon className="h-12 w-12 mx-auto text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">
                  {images && images.length > 0
                    ? "Select an image or enter URL"
                    : "Enter image URL"}
                </p>
                <input
                  type="text"
                  placeholder="Image URL"
                  className="mt-4 w-full p-2 border rounded"
                  value={block.imageUrl || ""}
                  onChange={(e) =>
                    updateBlockProps(index, { imageUrl: e.target.value })
                  }
                  onFocus={() => setFocusedBlockId(block.id)}
                  onBlur={() => setFocusedBlockId(null)}
                />
                {images && images.length > 0 && (
                  <div className="mt-4">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" size="sm">
                          Select Document Image
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80">
                        <ScrollArea className="h-72">
                          <div className="grid grid-cols-2 gap-2 p-2">
                            {images.map((image, imgIndex) => (
                              <div
                                key={imgIndex}
                                className="aspect-square relative cursor-pointer border rounded hover:border-blue-500 overflow-hidden"
                                onClick={() => {
                                  updateBlockProps(index, {
                                    imageUrl: image.data,
                                  });
                                }}
                              >
                                <img
                                  src={image.data}
                                  alt={`Image ${imgIndex}`}
                                  className="absolute inset-0 w-full h-full object-cover"
                                />
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </PopoverContent>
                    </Popover>
                  </div>
                )}
              </div>
            )}
          </div>
        );
        break;

      case "quote":
        blockContent = (
          <blockquote
            className="border-l-4 border-gray-300 pl-4 py-2 italic outline-none min-h-[40px]"
            contentEditable
            suppressContentEditableWarning
            onInput={(e) =>
              updateBlockContent(index, e.currentTarget.textContent || "")
            }
            dangerouslySetInnerHTML={{ __html: block.content }}
            {...contentEditableProps}
          />
        );
        break;

      case "list":
        blockContent = (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Select
                value={block.listType || "bullet"}
                onValueChange={(value) =>
                  setListType(index, value as "bullet" | "numbered")
                }
              >
                <SelectTrigger className="w-36">
                  <SelectValue placeholder="List Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bullet">
                    <div className="flex items-center">
                      <List className="h-4 w-4 mr-2" />
                      <span>Bullet List</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="numbered">
                    <div className="flex items-center">
                      <List className="h-4 w-4 mr-2" />
                      <span>Numbered List</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="pl-5">
              {/* Preview of the list */}
              {block.listType === "bullet" ? (
                <ul className="list-disc pl-2 mb-2">
                  {block.content.split("\n").map((item, i) => (
                    <li key={i} className="mb-1">
                      {item.trim() || (
                        <span className="opacity-50">List item {i + 1}</span>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <ol className="list-decimal pl-2 mb-2">
                  {block.content.split("\n").map((item, i) => (
                    <li key={i} className="mb-1">
                      {item.trim() || (
                        <span className="opacity-50">List item {i + 1}</span>
                      )}
                    </li>
                  ))}
                </ol>
              )}

              <Textarea
                value={block.content}
                onChange={(e) => updateBlockContent(index, e.target.value)}
                className="w-full text-sm"
                rows={Math.max(3, block.content.split("\n").length)}
                placeholder="Enter list items (one per line)"
                onFocus={() => setFocusedBlockId(block.id)}
                onBlur={() => setFocusedBlockId(null)}
              />
            </div>
          </div>
        );
        break;

      case "table":
        blockContent = (
          <div className="space-y-4">
            {/* Preview of the table */}
            <div className="overflow-x-auto border rounded">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {block.content
                      .split("\n")[0]
                      ?.split("\t")
                      .map((header, i) => (
                        <th
                          key={i}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {header.trim() || `Column ${i + 1}`}
                        </th>
                      ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {block.content
                    .split("\n")
                    .slice(1)
                    .map((row, rowIndex) => (
                      <tr
                        key={rowIndex}
                        className={
                          rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"
                        }
                      >
                        {row.split("\t").map((cell, cellIndex) => (
                          <td
                            key={cellIndex}
                            className="px-6 py-4 whitespace-nowrap text-sm"
                          >
                            {cell.trim() || (
                              <span className="opacity-50">
                                Cell {rowIndex + 1}-{cellIndex + 1}
                              </span>
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>

            <div>
              <div className="text-sm text-gray-500 mb-2">
                Format: Tab-separated columns, new line for rows
              </div>
              <Textarea
                value={block.content}
                onChange={(e) => updateBlockContent(index, e.target.value)}
                className="w-full text-sm font-mono"
                rows={Math.max(4, block.content.split("\n").length + 1)}
                placeholder="Header 1\tHeader 2\tHeader 3\nRow 1 Col 1\tRow 1 Col 2\tRow 1 Col 3\nRow 2 Col 1\tRow 2 Col 2\tRow 2 Col 3"
                onFocus={() => setFocusedBlockId(block.id)}
                onBlur={() => setFocusedBlockId(null)}
              />
            </div>
          </div>
        );
        break;

      default:
        blockContent = (
          <p
            className="outline-none py-1"
            contentEditable
            suppressContentEditableWarning
            onInput={(e) =>
              updateBlockContent(index, e.currentTarget.textContent || "")
            }
            dangerouslySetInnerHTML={{ __html: block.content }}
            {...contentEditableProps}
          />
        );
        break;
    }

    return (
      <div
        key={block.id}
        className={cn(
          "relative group border border-transparent hover:border-gray-200 rounded-md px-3 py-2",
          isFocused && "border-blue-200 bg-blue-50/30"
        )}
      >
        <div className="flex items-start">
          <div className="w-8 flex-shrink-0 flex justify-center mt-2">
            {block.type === "text" && (
              <Type className="h-4 w-4 text-gray-400" />
            )}
            {block.type === "header" && (
              <Heading className="h-4 w-4 text-gray-400" />
            )}
            {block.type === "image" && (
              <imgIcon className="h-4 w-4 text-gray-400" />
            )}
            {block.type === "quote" && (
              <Quote className="h-4 w-4 text-gray-400" />
            )}
            {block.type === "list" && (
              <List className="h-4 w-4 text-gray-400" />
            )}
            {block.type === "table" && (
              <Table className="h-4 w-4 text-gray-400" />
            )}
          </div>
          <div className="flex-1">{blockContent}</div>
          <div
            className={cn(
              "opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1 ml-2",
              isFocused && "opacity-100"
            )}
          >
            {(block.type === "text" ||
              block.type === "header" ||
              block.type === "quote") && (
              <div className="flex">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setBlockAlignment(index, "left")}
                >
                  <AlignLeft
                    className={cn(
                      "h-4 w-4",
                      block.alignment === "left" && "text-blue-600"
                    )}
                  />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setBlockAlignment(index, "center")}
                >
                  <AlignCenter
                    className={cn(
                      "h-4 w-4",
                      block.alignment === "center" && "text-blue-600"
                    )}
                  />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setBlockAlignment(index, "right")}
                >
                  <AlignRight
                    className={cn(
                      "h-4 w-4",
                      block.alignment === "right" && "text-blue-600"
                    )}
                  />
                </Button>
                {block.type === "text" && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => setBlockAlignment(index, "justify")}
                  >
                    <AlignJustify
                      className={cn(
                        "h-4 w-4",
                        block.alignment === "justify" && "text-blue-600"
                      )}
                    />
                  </Button>
                )}
              </div>
            )}
            {block.type === "image" && (
              <div className="flex">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setBlockAlignment(index, "left")}
                >
                  <AlignLeft
                    className={cn(
                      "h-4 w-4",
                      block.alignment === "left" && "text-blue-600"
                    )}
                  />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setBlockAlignment(index, "center")}
                >
                  <AlignCenter
                    className={cn(
                      "h-4 w-4",
                      block.alignment === "center" && "text-blue-600"
                    )}
                  />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setBlockAlignment(index, "right")}
                >
                  <AlignRight
                    className={cn(
                      "h-4 w-4",
                      block.alignment === "right" && "text-blue-600"
                    )}
                  />
                </Button>
              </div>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => moveBlock(index, "up")}
              disabled={index === 0}
            >
              <MoveUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => moveBlock(index, "down")}
              disabled={index === blocks.length - 1}
            >
              <MoveDown className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-red-500"
              onClick={() => deleteBlock(index)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Add button on the left */}
        <div
          className={cn(
            "absolute left-0 -ml-10 flex flex-col opacity-0 group-hover:opacity-100 transition-opacity",
            isFocused && "opacity-100"
          )}
        >
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => toggleBlockMenu(block.id)}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Block Menu */}
        {showMenu === block.id && (
          <div
            ref={menuRef}
            className="absolute z-10 left-0 -ml-32 mt-2 bg-white rounded-md shadow-lg border"
          >
            <div className="p-1">
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => changeBlockType(index, "text")}
              >
                <Type className="h-4 w-4 mr-2" />
                Text
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => changeBlockType(index, "header")}
              >
                <Heading className="h-4 w-4 mr-2" />
                Heading
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => changeBlockType(index, "image")}
              >
                <imgIcon className="h-4 w-4 mr-2" />
                Image
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => changeBlockType(index, "quote")}
              >
                <Quote className="h-4 w-4 mr-2" />
                Quote
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => changeBlockType(index, "list")}
              >
                <List className="h-4 w-4 mr-2" />
                List
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => changeBlockType(index, "table")}
              >
                <Table className="h-4 w-4 mr-2" />
                Table
              </Button>
            </div>
            <div className="border-t p-1">
              <Button
                variant="ghost"
                className="w-full justify-start text-sm px-2 py-1 h-8"
                onClick={() => addBlock("text", index)}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Block Below
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full">
      <div className="space-y-1">
        {blocks.map((block, index) => renderBlock(block, index))}
      </div>
      {/* Add block button at the end */}
      <div className="mt-4 flex justify-center">
        <Button
          variant="outline"
          size="sm"
          onClick={() => addBlock("text", blocks.length - 1)}
          className="flex items-center gap-1"
        >
          <Plus className="h-4 w-4" />
          Add Block
        </Button>
      </div>
    </div>
  );
}
