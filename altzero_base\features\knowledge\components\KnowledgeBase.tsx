import React, { useEffect, useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useDropzone } from "react-dropzone";
import {
  Upload,
  FileText,
  Trash2,
  RefreshCw,
  Search,
  Filter,
  MoreVertical,
  Download,
  Eye,
  CheckCircle,
  AlertCircle,
  Clock,
  Database,
  MessageSquare,
  Send,
  Bot,
  User,
  Sparkles,
  Brain,
} from "lucide-react";
import { useKnowledge } from "../context/KnowledgeContext";
import {
  useSuccessToast,
  useErrorToast,
  useInfoToast
} from "../../../base/components/ui/toast";
import { knowledgeService } from "../services/knowledgeService";
import { Document, DocumentStatus } from "../types/knowledge";
import { KNOWLEDGE_TOAST_MESSAGES, DEFAULT_SYSTEM_MESSAGE } from "../utils/constants";

interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  sources?: any[];
  metadata?: {
    uniqueDocuments?: number;
    documentsUsed?: string[];
    retrievedDocuments?: number;
    processingTime?: number;
    model?: string;
    hasValidContext?: boolean;
  };
}

const AIAssistant: React.FC = () => {
  const {
    state: { documents, uploadProgress, stats, isUploading, error },
    setDocuments,
    addDocument,
    setUploadProgress,
    clearUploadProgress,
    setStats,
    setUploading,
    setError,
    clearError,
    updateDocument,
    deleteDocument: removeDocument,
  } = useKnowledge();

  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<DocumentStatus | "all">(
    "all"
  );
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"upload" | "chat">("upload");

  // Chat state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [chatError, setChatError] = useState<string | null>(null);

  const showSuccess = useSuccessToast();
  const showError = useErrorToast();
  const showInfo = useInfoToast();

  // Load documents on mount
  useEffect(() => {
    loadDocuments();
  }, []);

  // Watch for document changes and update stats
  useEffect(() => {
    if (documents && documents.length >= 0) {
      loadStats();
    }
  }, [documents]);

  const loadDocuments = async (): Promise<void> => {
    try {
      setIsLoading(true);
      console.log("🔍 Loading documents...");

      const docs = await knowledgeService.getDocuments();
      console.log("📄 Received documents:", docs);

      // Ensure docs is always an array and has proper structure
      const validDocs = Array.isArray(docs)
        ? docs.filter(
            (doc) => doc && typeof doc === "object" && doc.id && doc.name
          )
        : [];

      console.log("✅ Valid documents:", validDocs.length);
      setDocuments(validDocs);
    } catch (error) {
      console.error("❌ Failed to load documents:", error);
      showError(
        "Failed to load documents",
        "Please refresh the page and try again."
      );
      // Set empty array on error
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async (): Promise<void> => {
    try {
      console.log("📊 Calculating stats from documents...");

      // Calculate stats from documents instead of API call
      const docs = documents || [];
      const statsData = {
        totalDocuments: docs.length,
        totalSize: docs.reduce((sum, doc) => sum + (doc.size || 0), 0),
        processingCount: docs.filter((doc) => doc.status === "processing")
          .length,
        errorCount: docs.filter((doc) => doc.status === "error").length,
        lastUpdated: new Date().toISOString(),
      };

      console.log("📈 Calculated stats:", statsData);
      setStats(statsData);
    } catch (error) {
      console.error("❌ Failed to calculate stats:", error);
      // Set default stats on error
      setStats({
        totalDocuments: 0,
        totalSize: 0,
        processingCount: 0,
        errorCount: 0,
        lastUpdated: new Date().toISOString(),
      });
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      setUploading(true);
      clearError();

      try {
        // Validate files
        const validFiles: File[] = [];
        const invalidFiles: string[] = [];

        acceptedFiles.forEach((file) => {
          const validation = knowledgeService.validateFile(file);
          if (validation.isValid) {
            validFiles.push(file);
          } else {
            invalidFiles.push(`${file.name}: ${validation.error}`);
          }
        });

        if (invalidFiles.length > 0) {
          showError("Some files were rejected", invalidFiles.join("\n"));
        }

        if (validFiles.length === 0) return;

        // Create progress tracking for each file
        validFiles.forEach((file) => {
          const documentId = `temp-${Date.now()}-${Math.random()}`;
          setUploadProgress({
            documentId,
            progress: 0,
            status: "uploading",
            message: `Uploading ${file.name}...`,
          });
        });

        showInfo(
          KNOWLEDGE_TOAST_MESSAGES.UPLOAD_PROCESSING,
          `Processing ${validFiles.length} file(s)...`
        );

        // Upload files
        const response = await knowledgeService.uploadDocuments(validFiles);

        // Handle successful uploads
        if (response.documents && Array.isArray(response.documents)) {
          console.log("📄 Processing uploaded documents:", response.documents);

          response.documents.forEach((doc) => {
            // Validate document object before adding
            if (doc && doc.id && doc.name && doc.type && doc.uploadedAt) {
              console.log("✅ Adding valid document:", doc.name);
              // Clear progress and add document
              clearUploadProgress(doc.id);
              addDocument(doc);
            } else {
              console.warn("⚠️ Invalid document object received:", doc);
            }
          });

          showSuccess(
            KNOWLEDGE_TOAST_MESSAGES.UPLOAD_SUCCESS,
            `Successfully uploaded ${response.documents.length} document(s)`
          );

          // Force reload documents to ensure consistency
          setTimeout(() => {
            console.log("🔄 Reloading documents after upload...");
            loadDocuments();
          }, 1000);
        } else {
          console.warn("⚠️ No documents in upload response:", response);
        }

        // Handle errors
        if (response.errors && response.errors.length > 0) {
          const errorMessages = response.errors.map(
            (err) => `${err.fileName}: ${err.error}`
          );
          showError("Some uploads failed", errorMessages.join("\n"));
        }

        // Recalculate stats
        setTimeout(() => loadStats(), 100);
      } catch (error) {
        console.error("Upload failed:", error);
        showError(
          KNOWLEDGE_TOAST_MESSAGES.UPLOAD_ERROR,
          error instanceof Error ? error.message : "Unknown error"
        );
      } finally {
        setUploading(false);
      }
    },
    [
      setUploading,
      clearError,
      setUploadProgress,
      clearUploadProgress,
      addDocument,
      setStats,
      showSuccess,
      showError,
      showInfo,
    ]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "text/plain": [".txt"],
      "text/markdown": [".md"],
      "application/rtf": [".rtf"],
      "application/vnd.oasis.opendocument.text": [".odt"],
    },
    multiple: true,
    disabled: isUploading,
  });

  const handleDeleteDocument = async (documentId: string): Promise<void> => {
    try {
      // Show confirmation dialog
      if (!window.confirm("Are you sure you want to delete this document? This action cannot be undone.")) {
        return;
      }

      console.log(`🗑️ Deleting document: ${documentId}`);
      
      // Call the backend to delete from Pinecone and other storage
      await knowledgeService.deleteDocument(documentId);
      
      // Remove from local state
      removeDocument(documentId);
      setSelectedDocuments((prev) => prev.filter((id) => id !== documentId));
      
      showSuccess("Document deleted successfully", "The document has been removed from your knowledge base.");
      
      // Reload documents to ensure consistency
      setTimeout(() => {
        loadDocuments();
        loadStats();
      }, 500);
    } catch (error) {
      console.error("Failed to delete document:", error);
      showError(
        "Failed to delete document",
        error instanceof Error ? error.message : "Unknown error occurred while deleting the document."
      );
    }
  };

  const handleReprocessDocument = async (documentId: string): Promise<void> => {
    try {
      // For now, just show a message since we don't have reprocess endpoint
      showInfo(
        "Reprocessing not available",
        "Documents are processed automatically when uploaded to LlamaCloud"
      );
    } catch (error) {
      console.error("Failed to reprocess document:", error);
      showError(
        "Failed to reprocess document",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  };

  const filteredDocuments = (documents || []).filter((doc) => {
    // Add safety checks
    if (!doc || typeof doc !== "object") return false;
    if (!doc.name || typeof doc.name !== "string") return false;

    const matchesSearch = doc.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || doc.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Debug logging
  useEffect(() => {
    console.log("🔍 Documents state:", {
      documentsLength: documents?.length || 0,
      filteredLength: filteredDocuments.length,
      searchQuery,
      statusFilter,
      sampleDoc: documents?.[0],
    });
  }, [documents, filteredDocuments.length, searchQuery, statusFilter]);

  const getStatusIcon = (status: DocumentStatus) => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "error":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case "processing":
      case "uploading":
      case "parsing":
      case "indexing":
        return <Clock className="w-4 h-4 text-yellow-500 animate-pulse" />;
      default:
        return <FileText className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusText = (status: DocumentStatus) => {
    switch (status) {
      case "uploading":
        return "Uploading...";
      case "parsing":
        return "Parsing...";
      case "indexing":
        return "Indexing...";
      case "processing":
        return "Processing...";
      case "success":
        return "Ready";
      case "error":
        return "Error";
      default:
        return status;
    }
  };

  // Chat functionality
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isSending) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: "user",
      content: inputMessage,
      timestamp: new Date().toISOString(),
    };

    setChatMessages((prev) => [...prev, userMessage]);
    setInputMessage("");
    setIsSending(true);
    setChatError(null);

    try {
      const response = await knowledgeService.chatWithPineconeRAG({
        message: inputMessage,
        selectedDocuments: selectedDocuments,
        systemMessage: DEFAULT_SYSTEM_MESSAGE,
        temperature: 0.7,
        maxTokens: 2048,
      });

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content:
          response.message || "I'm here to help you with your documents!",
        timestamp: new Date().toISOString(),
        sources: response.sources || [],
        metadata: response.metadata,
      };

      setChatMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Chat error:", error);
      setChatError("Failed to get response. Please try again.");

      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "Sorry, I encountered an error. Please try again.",
        timestamp: new Date().toISOString(),
      };
      setChatMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleDocumentSelection = (documentId: string) => {
    setSelectedDocuments((prev) =>
      prev.includes(documentId)
        ? prev.filter((id) => id !== documentId)
        : [...prev, documentId]
    );
  };

  const clearChat = () => {
    setChatMessages([]);
    setChatError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4 mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent-primary bg-clip-text text-transparent">
            AI Assistant
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Upload documents and chat with your knowledge base using advanced AI
          </p>
          <div className="flex justify-center gap-2">
            <button
              onClick={loadDocuments}
              disabled={isLoading}
              className="px-4 py-2 text-sm bg-muted hover:bg-muted/80 rounded-lg transition-colors flex items-center space-x-2"
            >
              <RefreshCw
                className={`w-4 h-4 ${isLoading ? "animate-spin" : ""}`}
              />
              <span>Refresh Data</span>
            </button>
            <div className="text-sm text-muted-foreground flex items-center space-x-2">
              <span>•</span>
              <span>{documents?.length || 0} documents</span>
              {stats && (
                <>
                  <span>•</span>
                  <span>
                    {knowledgeService.formatFileSize(stats.totalSize)}
                  </span>
                </>
              )}
            </div>
          </div>
        </motion.div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="dashboard-card p-1 flex space-x-1">
            <button
              onClick={() => setActiveTab("upload")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "upload"
                  ? "bg-primary text-primary-foreground shadow-md"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              }`}
            >
              <div className="flex items-center space-x-2">
                <Upload className="w-4 h-4" />
                <span>Upload</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab("chat")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "chat"
                  ? "bg-primary text-primary-foreground shadow-md"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              }`}
            >
              <div className="flex items-center space-x-2">
                <MessageSquare className="w-4 h-4" />
                <span>Chat with Data</span>
              </div>
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === "upload" && (
            <motion.div
              key="upload"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-8"
            >
              {/* Stats Cards */}
              {stats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="dashboard-card">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Database className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Total Documents
                        </p>
                        <p className="text-2xl font-bold">
                          {stats.totalDocuments}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="dashboard-card">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-500/10 rounded-lg">
                        <FileText className="w-6 h-6 text-blue-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Total Size
                        </p>
                        <p className="text-2xl font-bold">
                          {knowledgeService.formatFileSize(stats.totalSize)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="dashboard-card">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-yellow-500/10 rounded-lg">
                        <Clock className="w-6 h-6 text-yellow-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Processing
                        </p>
                        <p className="text-2xl font-bold">
                          {stats.processingCount}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="dashboard-card">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-red-500/10 rounded-lg">
                        <AlertCircle className="w-6 h-6 text-red-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Errors</p>
                        <p className="text-2xl font-bold">{stats.errorCount}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Upload Zone */}
              <div className="dashboard-card">
                <div
                  {...getRootProps()}
                  className={`knowledge-upload-zone ${
                    isDragActive ? "dragover" : ""
                  }`}
                >
                  <input {...getInputProps()} />
                  <div className="space-y-4">
                    <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                      <Upload className="w-8 h-8 text-primary" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold">
                        {isDragActive ? "Drop files here" : "Upload Documents"}
                      </h3>
                      <p className="text-muted-foreground">
                        Drag and drop files here, or click to browse. Supports
                        PDF, Word, Text, and Markdown files.
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Maximum file size: 50MB per file
                      </p>
                    </div>
                    {!isDragActive && (
                      <button
                        type="button"
                        className="px-6 py-3 gradient-primary text-primary-foreground rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                        disabled={isUploading}
                      >
                        {isUploading ? "Uploading..." : "Choose Files"}
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="dashboard-card">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Upload Progress</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Processing files...</span>
                        <span>Please wait</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div className="bg-primary h-2 rounded-full animate-pulse w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Documents List */}
              <div className="dashboard-card">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold flex items-center space-x-2">
                      <FileText className="w-5 h-5" />
                      <span>Your Documents</span>
                    </h3>
                    <div className="flex items-center space-x-2">
                      {/* Search */}
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <input
                          type="text"
                          placeholder="Search documents..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="w-64 pl-10 pr-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all text-sm"
                        />
                      </div>
                      {/* Filter */}
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value as DocumentStatus | "all")}
                        className="px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all text-sm"
                      >
                        <option value="all">All Status</option>
                        <option value="success">Ready</option>
                        <option value="processing">Processing</option>
                        <option value="error">Error</option>
                      </select>
                    </div>
                  </div>

                  {/* Documents Grid */}
                  <div className="space-y-3">
                    {isLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="loading-spinner w-5 h-5"></div>
                        <span className="ml-2 text-sm text-muted-foreground">
                          Loading documents...
                        </span>
                      </div>
                    ) : filteredDocuments.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No documents found</h3>
                        <p className="text-muted-foreground">
                          {searchQuery || statusFilter !== "all" 
                            ? "Try adjusting your search or filter criteria."
                            : "Upload your first document to get started."}
                        </p>
                      </div>
                    ) : (
                      filteredDocuments.map((doc) => (
                        <div
                          key={doc.id}
                          className="p-4 border border-border rounded-lg hover:shadow-sm transition-all"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3 flex-1 min-w-0">
                              <div className="flex-shrink-0">
                                {getStatusIcon(doc.status)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="text-sm font-medium truncate">
                                  {doc.name}
                                </h4>
                                <div className="flex items-center space-x-4 mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {getStatusText(doc.status)}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {knowledgeService.formatFileSize(doc.size || 0)}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(doc.uploadedAt || '').toLocaleDateString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                            
                            {/* Actions */}
                            <div className="flex items-center space-x-2 flex-shrink-0">
                              {doc.status === "success" && (
                                <button
                                  onClick={() => toggleDocumentSelection(doc.id)}
                                  className={`px-3 py-1 text-xs rounded-full transition-colors ${
                                    selectedDocuments.includes(doc.id)
                                      ? "bg-primary text-primary-foreground"
                                      : "bg-muted hover:bg-muted/80 text-muted-foreground"
                                  }`}
                                >
                                  {selectedDocuments.includes(doc.id) ? "Selected" : "Select"}
                                </button>
                              )}
                              
                              {doc.status === "error" && (
                                <button
                                  onClick={() => handleReprocessDocument(doc.id)}
                                  className="px-3 py-1 text-xs bg-yellow-500/10 text-yellow-600 rounded-full hover:bg-yellow-500/20 transition-colors"
                                >
                                  Retry
                                </button>
                              )}
                              
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteDocument(doc.id);
                                }}
                                className="p-2 text-red-500 hover:bg-red-500/10 rounded-lg transition-colors"
                                title="Delete document"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  {/* Bulk Actions */}
                  {filteredDocuments.length > 0 && (
                    <div className="flex items-center justify-between pt-4 border-t border-border">
                      <div className="text-sm text-muted-foreground">
                        Showing {filteredDocuments.length} of {documents?.length || 0} documents
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            const readyDocs = filteredDocuments.filter(doc => doc.status === "success");
                            setSelectedDocuments(readyDocs.map(doc => doc.id));
                          }}
                          className="px-3 py-1 text-xs bg-primary/10 text-primary rounded-lg hover:bg-primary/20 transition-colors"
                          disabled={filteredDocuments.filter(doc => doc.status === "success").length === 0}
                        >
                          Select All Ready
                        </button>
                        <button
                          onClick={() => setSelectedDocuments([])}
                          className="px-3 py-1 text-xs bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors"
                          disabled={selectedDocuments.length === 0}
                        >
                          Clear Selection
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === "chat" && (
            <motion.div
              key="chat"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {/* Chat Container with fixed height to prevent footer override */}
              <div className="h-[600px] w-full">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
                  {/* Document Selection Sidebar */}
                  <div className="lg:col-span-1">
                    <div className="dashboard-card p-4 h-full flex flex-col">
                      <h3 className="font-semibold mb-4 flex items-center space-x-2">
                        <FileText className="w-5 h-5" />
                        <span>Select Documents</span>
                      </h3>

                      {/* Search */}
                      <div className="relative mb-4">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <input
                          type="text"
                          placeholder="Search documents..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all text-sm"
                        />
                      </div>

                      {/* Documents List */}
                      <div className="flex-1 overflow-y-auto space-y-2 mb-4 max-h-[350px]">
                        {isLoading ? (
                          <div className="flex items-center justify-center py-8">
                            <div className="loading-spinner w-5 h-5"></div>
                            <span className="ml-2 text-sm text-muted-foreground">
                              Loading...
                            </span>
                          </div>
                        ) : filteredDocuments.length === 0 ? (
                          <div className="text-center py-8">
                            <FileText className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">
                              No documents found
                            </p>
                            <button
                              onClick={() => setActiveTab("upload")}
                              className="text-xs text-primary hover:underline mt-2"
                            >
                              Upload documents first
                            </button>
                          </div>
                        ) : (
                          filteredDocuments.map((doc) => (
                            <div
                              key={doc.id}
                              className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-sm ${
                                selectedDocuments.includes(doc.id)
                                  ? "border-primary bg-primary/5"
                                  : "border-border hover:border-primary/50"
                              }`}
                              onClick={() => toggleDocumentSelection(doc.id)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2 flex-1 min-w-0">
                                  <div className="flex-shrink-0">
                                    {getStatusIcon(doc.status)}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="text-sm font-medium truncate">
                                      {doc.name}
                                    </h4>
                                    <p className="text-xs text-muted-foreground">
                                      {getStatusText(doc.status)}
                                    </p>
                                  </div>
                                </div>
                                {selectedDocuments.includes(doc.id) && (
                                  <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>

                      {/* Selected Count */}
                      {selectedDocuments.length > 0 && (
                        <div className="p-2 bg-primary/10 rounded-lg mb-4">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-primary font-medium">
                              {selectedDocuments.length} selected
                            </span>
                            <button
                              onClick={() => setSelectedDocuments([])}
                              className="text-xs text-primary hover:underline"
                            >
                              Clear all
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Quick Actions */}
                      <div className="border-t border-border pt-4">
                        <h4 className="text-sm font-medium mb-2">
                          Quick Actions
                        </h4>
                        <div className="space-y-2">
                          <button
                            onClick={() =>
                              setSelectedDocuments(
                                filteredDocuments.map((doc) => doc.id)
                              )
                            }
                            className="w-full text-xs text-left px-2 py-1 text-primary hover:bg-primary/10 rounded"
                            disabled={filteredDocuments.length === 0}
                          >
                            Select All Documents
                          </button>
                          <button
                            onClick={() => {
                              const readyDocs = filteredDocuments.filter(
                                (doc) => doc.status === "success"
                              );
                              setSelectedDocuments(
                                readyDocs.map((doc) => doc.id)
                              );
                            }}
                            className="w-full text-xs text-left px-2 py-1 text-primary hover:bg-primary/10 rounded"
                          >
                            Select Ready Documents
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Chat Interface */}
                  <div className="lg:col-span-3">
                    <div className="dashboard-card h-full flex flex-col">
                      {/* Chat Header */}
                      <div className="flex items-center justify-between p-4 border-b border-border flex-shrink-0">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-primary/10 rounded-lg">
                            <Brain className="w-5 h-5 text-primary" />
                          </div>
                          <div>
                            <h3 className="font-semibold">AI Assistant</h3>
                            <p className="text-sm text-muted-foreground">
                              {selectedDocuments.length > 0
                                ? `Chatting with ${selectedDocuments.length} document(s)`
                                : "Select documents to start chatting"}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={clearChat}
                          className="text-sm text-muted-foreground hover:text-foreground px-3 py-1 rounded-lg hover:bg-muted/50"
                        >
                          Clear chat
                        </button>
                      </div>

                      {/* Chat Messages - Fixed height for footer space */}
                      <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-[400px]">
                        {chatMessages.length === 0 ? (
                          <div className="text-center py-8">
                            <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">
                              Start a conversation
                            </h3>
                            <p className="text-muted-foreground mb-4">
                              Select documents from the sidebar and ask
                              questions about them
                            </p>

                            {/* Suggested Questions */}
                            {selectedDocuments.length > 0 && (
                              <div className="mt-6 max-w-md mx-auto">
                                <p className="text-sm font-medium mb-3">
                                  Try asking:
                                </p>
                                <div className="space-y-2">
                                  {[
                                    "What are the key points in these documents?",
                                    "Can you summarize the main topics?",
                                    "What are the important dates mentioned?",
                                    "Are there any action items or recommendations?",
                                  ].map((question, index) => (
                                    <button
                                      key={index}
                                      onClick={() => setInputMessage(question)}
                                      className="block w-full text-left p-2 text-sm bg-muted hover:bg-muted/80 rounded-lg transition-colors"
                                    >
                                      "{question}"
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          chatMessages.map((message) => (
                            <div
                              key={message.id}
                              className={`flex ${
                                message.role === "user"
                                  ? "justify-end"
                                  : "justify-start"
                              }`}
                            >
                              <div
                                className={`max-w-[85%] rounded-lg p-3 ${
                                  message.role === "user"
                                    ? "bg-primary text-primary-foreground"
                                    : "bg-muted"
                                }`}
                              >
                                <div className="flex items-start space-x-2">
                                  {message.role === "assistant" && (
                                    <Bot className="w-5 h-5 mt-1 text-primary flex-shrink-0" />
                                  )}
                                  <div className="flex-1">
                                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                                      {message.content}
                                    </p>
                                    {message.sources &&
                                      message.sources.length > 0 && (
                                        <div className="mt-2 pt-2 border-t border-border/50">
                                          <p className="text-xs text-muted-foreground">
                                            📚 Sources: {message.metadata?.documentsUsed && message.metadata.documentsUsed.length > 0 
                                              ? message.metadata.documentsUsed.join(', ')
                                              : `${message.metadata?.uniqueDocuments || 1} document(s)`
                                            }
                                            <span className="ml-2 opacity-70">
                                              ({message.sources.length} chunks)
                                            </span>
                                          </p>
                                        </div>
                                      )}
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {new Date(
                                        message.timestamp
                                      ).toLocaleTimeString()}
                                    </p>
                                  </div>
                                  {message.role === "user" && (
                                    <User className="w-5 h-5 mt-1 flex-shrink-0" />
                                  )}
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                        {isSending && (
                          <div className="flex justify-start">
                            <div className="bg-muted rounded-lg p-3">
                              <div className="flex items-center space-x-2">
                                <Bot className="w-5 h-5 text-primary" />
                                <div className="flex space-x-1">
                                  <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                                  <div
                                    className="w-2 h-2 bg-primary rounded-full animate-bounce"
                                    style={{ animationDelay: "0.1s" }}
                                  ></div>
                                  <div
                                    className="w-2 h-2 bg-primary rounded-full animate-bounce"
                                    style={{ animationDelay: "0.2s" }}
                                  ></div>
                                </div>
                                <span className="text-sm text-muted-foreground">
                                  AI is thinking...
                                </span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Chat Input - Fixed at bottom */}
                      <div className="p-4 border-t border-border bg-background flex-shrink-0">
                        {chatError && (
                          <div className="mb-3 p-2 bg-destructive/10 text-destructive text-sm rounded-lg flex items-center justify-between">
                            <span>{chatError}</span>
                            <button
                              onClick={() => setChatError(null)}
                              className="text-destructive hover:text-destructive/80"
                            >
                              ✕
                            </button>
                          </div>
                        )}

                        <div className="flex space-x-3">
                          <div className="flex-1 relative">
                            <textarea
                              value={inputMessage}
                              onChange={(e) => setInputMessage(e.target.value)}
                              onKeyPress={handleKeyPress}
                              placeholder={
                                selectedDocuments.length > 0
                                  ? "Ask a question about your documents... (Shift+Enter for new line)"
                                  : "Select documents first to start chatting..."
                              }
                              className="w-full px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all resize-none text-sm"
                              disabled={
                                isSending || selectedDocuments.length === 0
                              }
                              rows={1}
                              style={{ minHeight: "40px", maxHeight: "80px" }}
                            />
                          </div>
                          <button
                            onClick={handleSendMessage}
                            disabled={
                              !inputMessage.trim() ||
                              isSending ||
                              selectedDocuments.length === 0
                            }
                            className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                          >
                            <Send className="w-4 h-4" />
                          </button>
                        </div>

                        {/* Quick action buttons */}
                        <div className="flex flex-wrap gap-2 mt-2">
                          {[
                            "Summarize",
                            "Key points",
                            "Action items",
                            "Important dates",
                          ].map((action) => (
                            <button
                              key={action}
                              onClick={() => setInputMessage(`${action}: `)}
                              className="px-2 py-1 text-xs bg-muted hover:bg-muted/80 rounded-full transition-colors"
                              disabled={selectedDocuments.length === 0}
                            >
                              {action}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AIAssistant;
