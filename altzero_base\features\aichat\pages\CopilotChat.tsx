import "@copilotkit/react-ui/styles.css";
import React from "react";
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotChat } from "@copilotkit/react-ui";
import {
  useCopilotAction,
  useCopilotReadable,
} from "@copilotkit/react-core";

/**
 * CopilotChat - Pure CopilotKit chat interface for AltZero
 * Standalone AI assistant with database querying and component interaction
 */
const AltzeroChat: React.FC = () => {
  // Make application state readable to the copilot
  useCopilotReadable({
    description: "The current application context and user information",
    value: {
      currentPage: window.location.pathname,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      platform: "AltZero",
    },
  });

  // Database query action
  useCopilotAction({
    name: "queryDatabase",
    description: "Query the AltZero database using natural language. Use @entity syntax like '@customers John' or '@orders today'",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "Natural language database query using @entity syntax",
        required: true,
      },
    ],
    handler: async ({ query }) => {
      try {
        console.log("Database query:", query);
        return {
          success: true,
          message: `Database query executed: ${query}`,
          results: [],
          note: "MCP database integration coming soon!"
        };
      } catch (error) {
        throw new Error(`Database query failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    },
  });

  // Navigation action
  useCopilotAction({
    name: "navigateToPage",
    description: "Navigate to different pages in the AltZero application",
    parameters: [
      {
        name: "path",
        type: "string",
        description: "The path to navigate to (e.g., '/dashboard', '/settings', '/knowledge')",
        required: true,
      },
    ],
    handler: async ({ path }) => {
      try {
        window.location.href = path;
        return {
          success: true,
          message: `Navigating to ${path}`,
        };
      } catch (error) {
        throw new Error(`Navigation failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    },
  });

  // Component interaction action
  useCopilotAction({
    name: "interactWithComponent",
    description: "Interact with AltZero application components",
    parameters: [
      {
        name: "component",
        type: "string",
        description: "The component to interact with (e.g., 'fileUpload', 'seoAudit', 'teamManagement')",
        required: true,
      },
      {
        name: "action",
        type: "string",
        description: "The action to perform",
        required: true,
      },
    ],
    handler: async ({ component, action }) => {
      try {
        console.log("Component interaction:", { component, action });
        return {
          success: true,
          message: `Executed ${action} on ${component}`,
          note: "Component interaction system coming soon!"
        };
      } catch (error) {
        throw new Error(`Component interaction failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    },
  });

  return (
    <CopilotKit runtimeUrl="/api/copilotkit/openai">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        {/* Back Button */}
        <button 
          onClick={() => window.history.back()}
          className="absolute top-6 left-6 flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
          <span className="text-sm font-medium">Back to Dashboard</span>
        </button>

        {/* Chat Panel */}
        <div className="w-full max-w-4xl h-[80vh] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Panel Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-lg font-bold">AltZero AI Chat</h1>
                  <p className="text-sm text-white/80">Your intelligent assistant</p>
                </div>
              </div>
              <div className="px-3 py-1 bg-green-500/20 border border-green-400/30 rounded-full">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium">Online</span>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Container */}
          <div className="h-[calc(100%-80px)]">
            <CopilotChat
              instructions="You are AltZero AI, an intelligent assistant for the AltZero platform. You help users with database queries (@customers, @orders, @users), navigation (go to dashboard, settings), and platform features (upload documents, run audits). Be helpful and concise."
              labels={{
                title: "",
                initial: "Hi! 👋 I'm your AltZero AI assistant. How can I help you today?",
              }}
              className="h-full"
            />
          </div>
        </div>
      </div>
    </CopilotKit>
  );
};

export default AltzeroChat;
