"use client";

import React, { useState, useRef } from "react";
import {
  Upload,
  X,
  Move,
  RotateCcw,
  Eye,
  Settings,
  Image,
  Palette,
  Crop,
  Save,
  Download,
} from "lucide-react";
import { <PERSON><PERSON> } from "../../../../../base/components/ui/button";
import { Input } from "../../../../../base/components/ui/input";
import { Label } from "../../../../../base/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../../../base/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import { Slider } from "../../../../../base/components/ui/slider";
import { Switch } from "../../../../../base/components/ui/switch";
import { Badge } from "../../../../../base/components/ui/badge";
import { useToast } from "../../../../../base/hooks/use-toast";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../../../base/components/ui/popover";

export interface LogoConfig {
  src: string;
  position:
    | "header-left"
    | "header-center"
    | "header-right"
    | "footer-left"
    | "footer-center"
    | "footer-right"
    | "watermark";
  size: "small" | "medium" | "large" | "custom";
  customWidth?: number;
  customHeight?: number;
  opacity: number;
  padding: number;
  showOnAllPages: boolean;
  backgroundColor?: string;
  borderRadius: number;
  rotation: number;
  filter?: "none" | "grayscale" | "sepia" | "blur" | "brightness";
  filterValue?: number;
}

interface LogoManagerProps {
  logo?: LogoConfig;
  onLogoChange: (logo: LogoConfig | null) => void;
  className?: string;
}

const DEFAULT_LOGO_CONFIG: Partial<LogoConfig> = {
  position: "header-left",
  size: "medium",
  opacity: 100,
  padding: 16,
  showOnAllPages: true,
  backgroundColor: "transparent",
  borderRadius: 0,
  rotation: 0,
  filter: "none",
  filterValue: 100,
};

export function LogoManager({
  logo,
  onLogoChange,
  className = "",
}: LogoManagerProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [draggedPosition, setDraggedPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleLogoUpload = async (file: File) => {
    setIsUploading(true);
    try {
      // Validate file
      if (!file.type.startsWith("image/")) {
        throw new Error("Please select a valid image file");
      }

      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        throw new Error("File size must be less than 5MB");
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        const src = event.target?.result as string;

        // Create a new logo config
        const newLogo: LogoConfig = {
          ...DEFAULT_LOGO_CONFIG,
          src,
        } as LogoConfig;

        onLogoChange(newLogo);

        toast({
          title: "Logo uploaded successfully",
          description: "Your logo has been added to the document.",
        });
      };

      reader.onerror = () => {
        throw new Error("Failed to read the file");
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Logo upload error:", error);
      toast({
        title: "Upload failed",
        description:
          error instanceof Error ? error.message : "Failed to upload logo",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleLogoUpload(file);
    }
  };

  const updateLogoConfig = (updates: Partial<LogoConfig>) => {
    if (logo) {
      onLogoChange({ ...logo, ...updates });
    }
  };

  const removeLogo = () => {
    onLogoChange(null);
    toast({
      title: "Logo removed",
      description: "The logo has been removed from your document.",
    });
  };

  const getSizePreset = (size: string) => {
    switch (size) {
      case "small":
        return { width: 80, height: 40 };
      case "medium":
        return { width: 120, height: 60 };
      case "large":
        return { width: 160, height: 80 };
      default:
        return {
          width: logo?.customWidth || 120,
          height: logo?.customHeight || 60,
        };
    }
  };

  const getPositionLabel = (position: string) => {
    const labels = {
      "header-left": "Header Left",
      "header-center": "Header Center",
      "header-right": "Header Right",
      "footer-left": "Footer Left",
      "footer-center": "Footer Center",
      "footer-right": "Footer Right",
      watermark: "Watermark",
    };
    return labels[position as keyof typeof labels] || position;
  };

  const renderLogoPreview = () => {
    if (!logo?.src) return null;

    const { width, height } = getSizePreset(logo.size);

    return (
      <div className="relative border rounded-lg p-4 bg-gradient-to-br from-gray-50 to-gray-100">
        {/* Position indicator */}
        <div className="absolute top-2 left-2">
          <Badge variant="secondary" className="text-xs">
            {getPositionLabel(logo.position)}
          </Badge>
        </div>

        {/* Logo preview */}
        <div
          className="relative mx-auto"
          style={{
            width: `${width}px`,
            height: `${height}px`,
            padding: `${logo.padding}px`,
          }}
        >
          <img
            src={logo.src}
            alt="Logo preview"
            className="w-full h-full object-contain"
            style={{
              opacity: logo.opacity / 100,
              backgroundColor:
                logo.backgroundColor === "transparent"
                  ? "transparent"
                  : logo.backgroundColor,
              borderRadius: `${logo.borderRadius}px`,
              transform: `rotate(${logo.rotation}deg)`,
              filter:
                logo.filter !== "none"
                  ? `${logo.filter}(${logo.filterValue || 100}%)`
                  : "none",
            }}
          />
        </div>

        {/* Preview controls */}
        <div className="absolute top-2 right-2 flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0"
            onClick={() => setShowPreview(!showPreview)}
          >
            <Eye className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Section */}
      {!logo ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              Logo Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer transition-colors"
              onClick={handleFileSelect}
            >
              <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">Upload Your Logo</h3>
              <p className="text-gray-500 mb-4">
                Click to upload or drag and drop your logo
              </p>
              <p className="text-xs text-gray-400">
                Supports: PNG, JPG, SVG • Max size: 5MB
              </p>
              {isUploading && (
                <div className="mt-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Logo Configuration */
        <div className="space-y-4">
          {/* Logo Preview */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Image className="h-4 w-4" />
                  Logo Preview
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleFileSelect}
                    className="gap-2"
                  >
                    <Upload className="h-3 w-3" />
                    Replace
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={removeLogo}
                    className="gap-2 text-red-600 hover:text-red-700"
                  >
                    <X className="h-3 w-3" />
                    Remove
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>{renderLogoPreview()}</CardContent>
          </Card>

          {/* Logo Settings */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Settings className="h-4 w-4" />
                Logo Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Position */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Position</Label>
                  <Select
                    value={logo.position}
                    onValueChange={(value) =>
                      updateLogoConfig({
                        position: value as LogoConfig["position"],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="header-left">Header Left</SelectItem>
                      <SelectItem value="header-center">
                        Header Center
                      </SelectItem>
                      <SelectItem value="header-right">Header Right</SelectItem>
                      <SelectItem value="footer-left">Footer Left</SelectItem>
                      <SelectItem value="footer-center">
                        Footer Center
                      </SelectItem>
                      <SelectItem value="footer-right">Footer Right</SelectItem>
                      <SelectItem value="watermark">Watermark</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium">Size</Label>
                  <Select
                    value={logo.size}
                    onValueChange={(value) =>
                      updateLogoConfig({
                        size: value as LogoConfig["size"],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">Small (80x40)</SelectItem>
                      <SelectItem value="medium">Medium (120x60)</SelectItem>
                      <SelectItem value="large">Large (160x80)</SelectItem>
                      <SelectItem value="custom">Custom Size</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Custom Size Controls */}
              {logo.size === "custom" && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Width (px)</Label>
                    <Input
                      type="number"
                      value={logo.customWidth || 120}
                      onChange={(e) =>
                        updateLogoConfig({
                          customWidth: parseInt(e.target.value) || 120,
                        })
                      }
                      min="20"
                      max="500"
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Height (px)</Label>
                    <Input
                      type="number"
                      value={logo.customHeight || 60}
                      onChange={(e) =>
                        updateLogoConfig({
                          customHeight: parseInt(e.target.value) || 60,
                        })
                      }
                      min="20"
                      max="300"
                    />
                  </div>
                </div>
              )}

              {/* Opacity & Padding */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">
                    Opacity ({logo.opacity}%)
                  </Label>
                  <Slider
                    value={[logo.opacity]}
                    onValueChange={([value]) =>
                      updateLogoConfig({ opacity: value })
                    }
                    max={100}
                    min={10}
                    step={5}
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">
                    Padding ({logo.padding}px)
                  </Label>
                  <Slider
                    value={[logo.padding]}
                    onValueChange={([value]) =>
                      updateLogoConfig({ padding: value })
                    }
                    max={50}
                    min={0}
                    step={2}
                    className="mt-2"
                  />
                </div>
              </div>

              {/* Advanced Styling */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Advanced Styling</Label>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs text-gray-600">
                      Background Color
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={
                          logo.backgroundColor === "transparent"
                            ? "#ffffff"
                            : logo.backgroundColor
                        }
                        onChange={(e) =>
                          updateLogoConfig({ backgroundColor: e.target.value })
                        }
                        className="w-12 h-8 p-1"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          updateLogoConfig({ backgroundColor: "transparent" })
                        }
                        className="text-xs"
                      >
                        Transparent
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-gray-600">
                      Border Radius ({logo.borderRadius}px)
                    </Label>
                    <Slider
                      value={[logo.borderRadius]}
                      onValueChange={([value]) =>
                        updateLogoConfig({ borderRadius: value })
                      }
                      max={20}
                      min={0}
                      step={1}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs text-gray-600">
                      Rotation ({logo.rotation}°)
                    </Label>
                    <div className="flex gap-2">
                      <Slider
                        value={[logo.rotation]}
                        onValueChange={([value]) =>
                          updateLogoConfig({ rotation: value })
                        }
                        max={360}
                        min={0}
                        step={5}
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateLogoConfig({ rotation: 0 })}
                        className="p-1"
                      >
                        <RotateCcw className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-gray-600">Filter</Label>
                    <Select
                      value={logo.filter || "none"}
                      onValueChange={(value) =>
                        updateLogoConfig({
                          filter: value as LogoConfig["filter"],
                        })
                      }
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="grayscale">Grayscale</SelectItem>
                        <SelectItem value="sepia">Sepia</SelectItem>
                        <SelectItem value="blur">Blur</SelectItem>
                        <SelectItem value="brightness">Brightness</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Show on all pages toggle */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <Label className="text-sm font-medium">
                    Show on all pages
                  </Label>
                  <p className="text-xs text-gray-600">
                    Display logo on every page of the document
                  </p>
                </div>
                <Switch
                  checked={logo.showOnAllPages}
                  onCheckedChange={(checked) =>
                    updateLogoConfig({ showOnAllPages: checked })
                  }
                />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
    </div>
  );
}

export default LogoManager;
