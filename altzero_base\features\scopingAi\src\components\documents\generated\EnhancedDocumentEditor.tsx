"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragOverEvent,
  rectIntersection,
  CollisionDetection,
  getFirstCollision,
  pointerWithin,
  closestCorners,
  DragMoveEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import { Button } from "../../../../../base/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
} from "../../../../../base/components/ui/card";
import { Badge } from "../../../../../base/components/ui/badge";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Tabs<PERSON>rigger,
} from "../../../../../base/components/ui/tabs";
import {
  Plus,
  Type,
  Heading1,
  List,
  Image as ImageIcon,
  Columns,
  Columns2,
  Columns3,
  Save,
  Eye,
  Edit3,
  Sparkles,
  Grid,
  FileText,
  RotateCcw,
  Download,
  Move,
  GripVertical,
  Zap,
} from "lucide-react";
import { useToast } from "../../../../../base/hooks/use-toast";
import { EnhancedSortableBlock, EnhancedBlock } from "./EnhancedDragDropEditor";
import { EnhancedColumnLayout } from "./EnhancedColumnLayout";

interface EnhancedDocumentEditorProps {
  sections: any[];
  onUpdate: (sections: any[]) => void;
  readonly?: boolean;
  className?: string;
}

// Professional Drag State Management
interface DragState {
  activeBlock: EnhancedBlock | null;
  sourceContainer: string | null;
  targetContainer: string | null;
  insertionIndex: number | null;
  isDragging: boolean;
  dragOffset: { x: number; y: number };
  previewPosition: { x: number; y: number } | null;
}

// Advanced Collision Detection with Smart Insertion
const professionalCollisionDetection: CollisionDetection = (args) => {
  const { active, droppableContainers, pointerCoordinates } = args;

  // Multi-phase collision detection for maximum accuracy

  // Phase 1: Precise pointer intersections for immediate targets
  const pointerIntersections = pointerWithin(args);

  // Phase 2: Rectangle intersections for broader detection
  const rectIntersections = rectIntersection(args);

  // Phase 3: Closest center as fallback
  const centerIntersections = closestCenter(args);

  // Smart prioritization based on drag context
  const allIntersections = [
    ...pointerIntersections,
    ...rectIntersections.filter(
      (r) => !pointerIntersections.some((p) => p.id === r.id)
    ),
    ...centerIntersections.filter(
      (c) =>
        !pointerIntersections.some((p) => p.id === c.id) &&
        !rectIntersections.some((r) => r.id === c.id)
    ),
  ];

  // Prioritize section and column drop zones
  const prioritized = allIntersections.sort((a, b) => {
    const aContainer = droppableContainers.find((c) => c.id === a.id);
    const bContainer = droppableContainers.find((c) => c.id === b.id);

    const aData = aContainer?.data.current;
    const bData = bContainer?.data.current;

    // Section zones get highest priority
    if (aData?.type === "section" && bData?.type !== "section") return -1;
    if (bData?.type === "section" && aData?.type !== "section") return 1;

    // Column zones get second priority
    if (aData?.type === "column" && bData?.type !== "column") return -1;
    if (bData?.type === "column" && aData?.type !== "column") return 1;

    return 0;
  });

  return prioritized;
};

// Professional Insertion Point Calculator
const calculateInsertionPoint = (
  pointerY: number,
  containerRect: DOMRect,
  blocks: EnhancedBlock[]
): number => {
  if (blocks.length === 0) return 0;

  const relativeY = pointerY - containerRect.top;
  const blockHeight = containerRect.height / (blocks.length || 1);

  return Math.min(
    Math.max(0, Math.round(relativeY / blockHeight)),
    blocks.length
  );
};

// Enhanced Drop Zone with Real-time Insertion Preview
function ProfessionalDropZone({
  sectionId,
  children,
  insertionIndex,
  isDragActive,
}: {
  sectionId: string;
  children: React.ReactNode;
  insertionIndex: number | null;
  isDragActive: boolean;
}) {
  const { setNodeRef, isOver, active } = useDroppable({
    id: `section-${sectionId}`,
    data: {
      type: "section",
      sectionId,
      acceptsBlocks: true,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`relative min-h-[300px] transition-all duration-300 ease-out ${
        isOver && isDragActive
          ? "bg-gradient-to-br from-blue-50 to-indigo-50 ring-2 ring-blue-300 ring-opacity-50 shadow-xl rounded-xl"
          : ""
      } ${isDragActive ? "transform scale-[1.02]" : ""}`}
      style={{
        background:
          isOver && isDragActive
            ? "linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%)"
            : undefined,
      }}
    >
      {children}

      {/* Professional Insertion Indicator */}
      {isOver && isDragActive && insertionIndex !== null && (
        <div
          className="absolute left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-500 to-indigo-500 rounded-full shadow-lg animate-pulse z-50"
          style={{
            top: `${(insertionIndex / Math.max(1, React.Children.count(children))) * 100}%`,
            boxShadow: "0 0 20px rgba(59, 130, 246, 0.6)",
          }}
        >
          <div className="absolute -left-2 -top-2 w-5 h-5 bg-blue-500 rounded-full shadow-lg animate-bounce">
            <div className="absolute inset-1 bg-white rounded-full"></div>
          </div>
          <div className="absolute -right-2 -top-2 w-5 h-5 bg-indigo-500 rounded-full shadow-lg animate-bounce delay-100">
            <div className="absolute inset-1 bg-white rounded-full"></div>
          </div>
        </div>
      )}

      {/* Professional Drop Indicator */}
      {isOver && isDragActive && (
        <div className="absolute inset-0 pointer-events-none z-40">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-transparent to-indigo-400/10 rounded-xl" />
          <div className="absolute inset-4 border-2 border-dashed border-blue-400 rounded-lg flex items-center justify-center">
            <div className="bg-white/95 backdrop-blur-sm px-6 py-3 rounded-xl shadow-lg border border-blue-200 flex items-center gap-3">
              <div className="relative">
                <Zap className="h-5 w-5 text-blue-600 animate-pulse" />
                <div className="absolute -inset-1 bg-blue-400 rounded-full opacity-20 animate-ping"></div>
              </div>
              <p className="text-blue-700 font-semibold text-sm">
                Drop block here for instant placement
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Professional Block Palette with Stunning UI
function ProfessionalBlockPalette({
  onAddBlock,
  readonly = false,
}: {
  onAddBlock: (type: EnhancedBlock["type"]) => void;
  readonly?: boolean;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredType, setHoveredType] = useState<string | null>(null);

  if (readonly) return null;

  const blockTypes = [
    {
      type: "text" as const,
      icon: Type,
      label: "Rich Text",
      description: "Professional paragraph with formatting",
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50",
      borderColor: "border-blue-200",
      textColor: "text-blue-700",
    },
    {
      type: "heading" as const,
      icon: Heading1,
      label: "Heading",
      description: "Eye-catching headlines (H1-H6)",
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-gradient-to-br from-purple-50 to-pink-50",
      borderColor: "border-purple-200",
      textColor: "text-purple-700",
    },
    {
      type: "list" as const,
      icon: List,
      label: "Smart List",
      description: "Bulleted or numbered lists",
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-gradient-to-br from-green-50 to-emerald-50",
      borderColor: "border-green-200",
      textColor: "text-green-700",
    },
    {
      type: "image" as const,
      icon: ImageIcon,
      label: "Media",
      description: "Images, photos, and graphics",
      color: "from-orange-500 to-red-500",
      bgColor: "bg-gradient-to-br from-orange-50 to-red-50",
      borderColor: "border-orange-200",
      textColor: "text-orange-700",
    },
    {
      type: "columns" as const,
      icon: Columns3,
      label: "Layout",
      description: "Advanced column layouts (1-3 cols)",
      color: "from-indigo-500 to-blue-500",
      bgColor: "bg-gradient-to-br from-indigo-50 to-blue-50",
      borderColor: "border-indigo-200",
      textColor: "text-indigo-700",
    },
  ];

  return (
    <div className="relative mb-8">
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-20 border-2 border-dashed border-gray-300 hover:border-gray-400 bg-gradient-to-br from-white to-gray-50 hover:from-gray-50 hover:to-gray-100 text-gray-700 transition-all duration-300 group relative overflow-hidden"
        variant="outline"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="relative z-10 flex items-center justify-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
            <Plus className="h-6 w-6" />
          </div>
          <div className="text-left">
            <div className="font-bold text-lg">Add New Block</div>
            <div className="text-sm opacity-70">
              Choose from professional block types
            </div>
          </div>
          <Sparkles className="h-5 w-5 text-purple-500 animate-pulse" />
        </div>
      </Button>

      {isOpen && (
        <>
          {/* Professional Backdrop */}
          <div
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
          />

          {/* Stunning Block Menu */}
          <div className="absolute top-full left-0 right-0 mt-4 z-50 overflow-hidden">
            <div className="bg-white/95 backdrop-blur-xl border border-gray-200/50 rounded-2xl shadow-2xl overflow-hidden">
              <div className="p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-lg">
                    <Grid className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800">
                      Professional Block Library
                    </h3>
                    <p className="text-sm text-gray-600">
                      Drag-ready components for your content
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-3">
                  {blockTypes.map((blockType) => (
                    <button
                      key={blockType.type}
                      onClick={() => {
                        onAddBlock(blockType.type);
                        setIsOpen(false);
                      }}
                      onMouseEnter={() => setHoveredType(blockType.type)}
                      onMouseLeave={() => setHoveredType(null)}
                      className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] group ${blockType.bgColor} ${blockType.borderColor} hover:border-opacity-50`}
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`p-3 bg-gradient-to-br ${blockType.color} text-white rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}
                        >
                          <blockType.icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div
                            className={`font-bold text-base ${blockType.textColor}`}
                          >
                            {blockType.label}
                          </div>
                          <div className="text-sm opacity-80 mt-1">
                            {blockType.description}
                          </div>
                        </div>
                        <div
                          className={`opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                        >
                          <Move className={`h-5 w-5 ${blockType.textColor}`} />
                        </div>
                      </div>

                      {hoveredType === blockType.type && (
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
                      )}
                    </button>
                  ))}
                </div>

                <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                  <div className="flex items-center gap-3">
                    <Zap className="h-5 w-5 text-blue-600" />
                    <p className="text-sm text-blue-800 font-medium">
                      Pro Tip: Drag blocks anywhere for instant rearrangement!
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// Block Palette Component
function BlockPalette({
  onAddBlock,
  readonly = false,
}: {
  onAddBlock: (type: EnhancedBlock["type"]) => void;
  readonly?: boolean;
}) {
  const [isOpen, setIsOpen] = useState(false);

  if (readonly) return null;

  const blockTypes = [
    {
      type: "text" as const,
      icon: Type,
      label: "Text",
      description: "Add paragraph text",
      color: "bg-blue-50 text-blue-600 border-blue-200",
    },
    {
      type: "heading" as const,
      icon: Heading1,
      label: "Heading",
      description: "Add a heading",
      color: "bg-purple-50 text-purple-600 border-purple-200",
    },
    {
      type: "list" as const,
      icon: List,
      label: "List",
      description: "Add a bulleted list",
      color: "bg-green-50 text-green-600 border-green-200",
    },
    {
      type: "image" as const,
      icon: ImageIcon,
      label: "Image",
      description: "Add an image",
      color: "bg-orange-50 text-orange-600 border-orange-200",
    },
    {
      type: "columns" as const,
      icon: Columns3,
      label: "Columns",
      description: "Create column layout (1-3 columns)",
      color: "bg-red-50 text-red-600 border-red-200",
    },
  ];

  return (
    <div className="relative mb-6">
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full border-2 border-dashed border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50 text-gray-600 h-16"
        variant="outline"
      >
        <Plus className="h-6 w-6 mr-3" />
        <div className="text-left">
          <div className="font-medium">Add New Block</div>
          <div className="text-sm opacity-70">Click to see all block types</div>
        </div>
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-30"
            onClick={() => setIsOpen(false)}
          />

          {/* Block Menu */}
          <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-xl z-40 overflow-hidden">
            <div className="p-2">
              <div className="text-sm font-medium text-gray-700 px-3 py-2 bg-gray-50 rounded-md mb-3">
                Choose a block type
              </div>

              <div className="grid grid-cols-1 gap-2">
                {blockTypes.map((blockType) => (
                  <button
                    key={blockType.type}
                    onClick={() => {
                      onAddBlock(blockType.type);
                      setIsOpen(false);
                    }}
                    className={`w-full text-left p-4 rounded-lg border transition-all hover:shadow-md ${blockType.color}`}
                  >
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-white rounded-md">
                        <blockType.icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm">
                          {blockType.label}
                        </div>
                        <div className="text-xs opacity-80">
                          {blockType.description}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// Professional Document Section with Advanced Features
function ProfessionalDocumentSection({
  section,
  sectionId,
  onUpdate,
  readonly = false,
  dragState,
}: {
  section: any;
  sectionId: string;
  onUpdate: (sectionId: string, updates: any) => void;
  readonly?: boolean;
  dragState: DragState;
}) {
  const [blocks, setBlocks] = useState<EnhancedBlock[]>([]);
  const [isDirty, setIsDirty] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<
    "idle" | "saving" | "saved"
  >("idle");
  const { toast } = useToast();
  const sectionRef = useRef<HTMLDivElement>(null);

  // Convert section content to blocks on mount
  useEffect(() => {
    if (section.content) {
      const convertedBlocks = convertContentToBlocks(section.content);
      setBlocks(convertedBlocks);
    } else if (section.blocks) {
      setBlocks(section.blocks);
    }
  }, [section.content, section.blocks]);

  // Professional auto-save with status indicators
  const handleSave = useCallback(async () => {
    setAutoSaveStatus("saving");

    try {
      const htmlContent = renderBlocksToHTML(blocks);
      await onUpdate(sectionId, {
        content: htmlContent,
        blocks: blocks,
        lastModified: new Date().toISOString(),
      });

      setAutoSaveStatus("saved");
      setTimeout(() => setAutoSaveStatus("idle"), 2000);

      toast({
        title: "✨ Auto-saved",
        description: "Your changes have been saved automatically.",
        duration: 1500,
      });
    } catch (error) {
      setAutoSaveStatus("idle");
      toast({
        title: "Save Error",
        description: "Failed to save changes. Please try again.",
        variant: "destructive",
      });
    }
  }, [blocks, sectionId, onUpdate, toast]);

  // Enhanced auto-save functionality
  const autoSaveRef = useRef<NodeJS.Timeout | undefined>(undefined);
  useEffect(() => {
    if (isDirty && !readonly) {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }

      autoSaveRef.current = setTimeout(() => {
        handleSave();
        setIsDirty(false);
      }, 1500); // Faster auto-save for better UX
    }

    return () => {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }
    };
  }, [isDirty, readonly, handleSave]);

  const addBlock = useCallback(
    (type: EnhancedBlock["type"]) => {
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substr(2, 9);
      const newBlock: EnhancedBlock = {
        id: `block-${timestamp}-${randomId}`,
        type,
        content: getDefaultContent(type),
        style: { textAlign: "left" },
        ...(type === "columns" && {
          layout: {
            columns: 2,
            children: {
              [`${timestamp}-col-1`]: [],
              [`${timestamp}-col-2`]: [],
            },
            columnWidths: ["1fr", "1fr"],
          },
        }),
      };

      setBlocks((prev) => [...prev, newBlock]);
      setIsDirty(true);

      toast({
        title: `🎉 ${type.charAt(0).toUpperCase() + type.slice(1)} Added`,
        description: `Professional ${type} block has been added to your document.`,
        duration: 1500,
      });
    },
    [toast]
  );

  const updateBlock = useCallback(
    (blockId: string, updates: Partial<EnhancedBlock>) => {
      setBlocks((prev) =>
        prev.map((block) =>
          block.id === blockId ? { ...block, ...updates } : block
        )
      );
      setIsDirty(true);
    },
    []
  );

  const deleteBlock = useCallback(
    (blockId: string) => {
      setBlocks((prev) => prev.filter((block) => block.id !== blockId));
      setIsDirty(true);

      toast({
        title: "🗑️ Block Removed",
        description: "Block has been removed successfully.",
        duration: 1500,
      });
    },
    [toast]
  );

  const duplicateBlock = useCallback(
    (blockId: string) => {
      const blockToDuplicate = blocks.find((block) => block.id === blockId);
      if (!blockToDuplicate) return;

      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substr(2, 9);
      const duplicatedBlock: EnhancedBlock = {
        ...blockToDuplicate,
        id: `block-${timestamp}-${randomId}`,
        ...(blockToDuplicate.layout && {
          layout: {
            ...blockToDuplicate.layout,
            children: Object.entries(
              blockToDuplicate.layout.children || {}
            ).reduce(
              (acc, [key, value]) => ({
                ...acc,
                [key.replace(blockToDuplicate.id, `${timestamp}`)]: value,
              }),
              {}
            ),
          },
        }),
      };

      const blockIndex = blocks.findIndex((block) => block.id === blockId);
      setBlocks((prev) => [
        ...prev.slice(0, blockIndex + 1),
        duplicatedBlock,
        ...prev.slice(blockIndex + 1),
      ]);
      setIsDirty(true);

      toast({
        title: "📋 Block Duplicated",
        description: "Block has been duplicated successfully.",
        duration: 1500,
      });
    },
    [blocks, toast]
  );

  // Calculate insertion index for real-time preview
  const insertionIndex =
    dragState.targetContainer === `section-${sectionId}`
      ? dragState.insertionIndex
      : null;

  return (
    <ProfessionalDropZone
      sectionId={sectionId}
      insertionIndex={insertionIndex}
      isDragActive={dragState.isDragging}
    >
      <div ref={sectionRef} className="space-y-6">
        {/* Professional Section Header */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200 shadow-sm">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-lg shadow-md">
              <FileText className="h-5 w-5" />
            </div>
            <div>
              <Badge
                variant="outline"
                className="font-bold text-base px-3 py-1"
              >
                {section.title || `Section ${sectionId}`}
              </Badge>
              <div className="flex items-center gap-2 mt-1">
                {autoSaveStatus === "saving" && (
                  <Badge variant="secondary" className="text-xs animate-pulse">
                    <div className="animate-spin h-3 w-3 border-2 border-orange-500 border-t-transparent rounded-full mr-2" />
                    Saving...
                  </Badge>
                )}
                {autoSaveStatus === "saved" && (
                  <Badge
                    variant="default"
                    className="text-xs bg-green-100 text-green-700 border-green-200"
                  >
                    <div className="h-2 w-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                    Saved!
                  </Badge>
                )}
                {isDirty && autoSaveStatus === "idle" && (
                  <Badge variant="secondary" className="text-xs">
                    <div className="animate-pulse h-2 w-2 bg-orange-500 rounded-full mr-2" />
                    Changes pending...
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {!readonly && (
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleSave}
                disabled={!isDirty}
                className="transition-all duration-200 hover:scale-105"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Now
              </Button>
            </div>
          )}
        </div>

        {/* Enhanced Blocks with Professional Styling */}
        <div className="space-y-4">
          <SortableContext
            items={blocks.map((b) => b.id)}
            strategy={verticalListSortingStrategy}
          >
            {blocks.map((block, index) => (
              <div
                key={block.id}
                className={`transition-all duration-300 ${
                  dragState.isDragging ? "hover:scale-[1.01]" : ""
                }`}
                style={{
                  // Add smooth transitions for reordering
                  transform:
                    dragState.isDragging && index >= (insertionIndex || 0)
                      ? "translateY(4px)"
                      : "translateY(0)",
                }}
              >
                {block.type === "columns" ? (
                  <EnhancedColumnLayout
                    block={block}
                    sectionId={sectionId}
                    onUpdate={updateBlock}
                    onDelete={deleteBlock}
                    onDuplicate={duplicateBlock}
                    readonly={readonly}
                  />
                ) : (
                  <EnhancedSortableBlock
                    block={block}
                    sectionId={sectionId}
                    onUpdate={updateBlock}
                    onDelete={deleteBlock}
                    onDuplicate={duplicateBlock}
                    readonly={readonly}
                  />
                )}
              </div>
            ))}
          </SortableContext>
        </div>

        {/* Professional Add Block Palette */}
        <ProfessionalBlockPalette onAddBlock={addBlock} readonly={readonly} />
      </div>
    </ProfessionalDropZone>
  );
}

// Main Professional Document Editor
export function EnhancedDocumentEditor({
  sections,
  onUpdate,
  readonly = false,
  className = "",
}: EnhancedDocumentEditorProps) {
  const [activeTab, setActiveTab] = useState("edit");
  const [sectionsState, setSectionsState] = useState(sections);
  const [dragState, setDragState] = useState<DragState>({
    activeBlock: null,
    sourceContainer: null,
    targetContainer: null,
    insertionIndex: null,
    isDragging: false,
    dragOffset: { x: 0, y: 0 },
    previewPosition: null,
  });
  const { toast } = useToast();

  // Update local state when props change
  useEffect(() => {
    setSectionsState(sections);
  }, [sections]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const draggedBlockData = active.data.current;

      if (draggedBlockData?.type === "block") {
        const section = sectionsState.find(
          (s) => s.id === draggedBlockData.sectionId
        );
        if (section?.blocks) {
          const block = findBlockInSection(section, draggedBlockData.blockId);

          setDragState({
            activeBlock: block,
            sourceContainer: draggedBlockData.columnId
              ? `column-${draggedBlockData.columnId}`
              : `section-${draggedBlockData.sectionId}`,
            targetContainer: null,
            insertionIndex: null,
            isDragging: true,
            dragOffset: { x: 0, y: 0 },
            previewPosition: null,
          });
        }
      }
    },
    [sectionsState]
  );

  const handleDragMove = useCallback((event: DragMoveEvent) => {
    const { delta } = event;

    setDragState((prev) => ({
      ...prev,
      dragOffset: { x: delta.x, y: delta.y },
      previewPosition: { x: delta.x, y: delta.y },
    }));
  }, []);

  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      const { over, delta } = event;

      if (!over) {
        setDragState((prev) => ({
          ...prev,
          targetContainer: null,
          insertionIndex: null,
        }));
        return;
      }

      const overData = over.data.current;
      if (!overData) return;

      // Calculate insertion point for real-time preview
      let insertionIndex: number | null = null;
      let targetContainer: string | null = null;

      if (overData.type === "section") {
        targetContainer = `section-${overData.sectionId}`;
        // Calculate insertion point based on mouse position
        const sectionElement = document.getElementById(
          `section-${overData.sectionId}`
        );
        if (sectionElement) {
          const rect = sectionElement.getBoundingClientRect();
          const mouseY = rect.top + delta.y;
          const section = sectionsState.find(
            (s) => s.id === overData.sectionId
          );
          if (section?.blocks) {
            insertionIndex = calculateInsertionPoint(
              mouseY,
              rect,
              section.blocks
            );
          }
        }
      } else if (overData.type === "column") {
        targetContainer = `column-${overData.columnId}`;
        // Similar calculation for columns
        insertionIndex = 0; // Simplified for now
      }

      setDragState((prev) => ({
        ...prev,
        targetContainer,
        insertionIndex,
      }));
    },
    [sectionsState]
  );

  // Helper functions - defined before handleDragEnd
  const findBlockInSection = useCallback(
    (section: any, blockId: string): EnhancedBlock | null => {
      if (!section.blocks) return null;

      const directBlock = section.blocks.find((b: any) => b.id === blockId);
      if (directBlock) return directBlock;

      for (const block of section.blocks) {
        if (block.type === "columns" && block.layout?.children) {
          for (const columnId of Object.keys(block.layout.children)) {
            const columnBlocks = block.layout.children[columnId] || [];
            const columnBlock = columnBlocks.find((b: any) => b.id === blockId);
            if (columnBlock) return columnBlock;
          }
        }
      }

      return null;
    },
    []
  );

  const removeBlockFromSection = useCallback(
    (section: any, blockId: string): any => {
      const newSection = { ...section };

      if (!newSection.blocks) {
        newSection.blocks = [];
        return newSection;
      }

      const topLevelIndex = newSection.blocks.findIndex(
        (b: any) => b.id === blockId
      );
      if (topLevelIndex !== -1) {
        newSection.blocks = newSection.blocks.filter(
          (b: any) => b.id !== blockId
        );
        return newSection;
      }

      newSection.blocks = newSection.blocks.map((block: any) => {
        if (block.type === "columns" && block.layout?.children) {
          const newLayout = { ...block.layout };
          const newChildren = { ...newLayout.children };

          for (const columnId of Object.keys(newChildren)) {
            newChildren[columnId] = newChildren[columnId].filter(
              (b: any) => b.id !== blockId
            );
          }

          newLayout.children = newChildren;
          return { ...block, layout: newLayout };
        }
        return block;
      });

      return newSection;
    },
    []
  );

  const addBlockToTarget = useCallback(
    (section: any, block: EnhancedBlock, targetData: any): any => {
      const newSection = { ...section };

      if (!newSection.blocks) {
        newSection.blocks = [];
      }

      if (targetData.type === "section") {
        newSection.blocks = [...newSection.blocks, block];
      } else if (targetData.type === "column") {
        newSection.blocks = newSection.blocks.map((sectionBlock: any) => {
          if (
            sectionBlock.type === "columns" &&
            sectionBlock.layout?.children
          ) {
            const newLayout = { ...sectionBlock.layout };
            const newChildren = { ...newLayout.children };

            if (newChildren[targetData.columnId]) {
              newChildren[targetData.columnId] = [
                ...newChildren[targetData.columnId],
                block,
              ];
            }

            newLayout.children = newChildren;
            return { ...sectionBlock, layout: newLayout };
          }
          return sectionBlock;
        });
      }

      return newSection;
    },
    []
  );

  // Helper function for smart insertion at specific index
  const addBlockToTargetAtIndex = useCallback(
    (
      section: any,
      block: EnhancedBlock,
      targetData: any,
      insertionIndex: number
    ): any => {
      const newSection = { ...section };

      if (!newSection.blocks) {
        newSection.blocks = [];
      }

      if (targetData.type === "section") {
        // Insert at specific index in section
        const newBlocks = [...newSection.blocks];
        newBlocks.splice(insertionIndex, 0, block);
        newSection.blocks = newBlocks;
      }

      return newSection;
    },
    []
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      // Reset drag state
      setDragState({
        activeBlock: null,
        sourceContainer: null,
        targetContainer: null,
        insertionIndex: null,
        isDragging: false,
        dragOffset: { x: 0, y: 0 },
        previewPosition: null,
      });

      if (!over) return;

      const activeData = active.data.current;
      const overData = over.data.current;

      if (!activeData || activeData.type !== "block" || !overData) return;

      const sourceBlockId = activeData.blockId;
      const sourceSectionId = activeData.sectionId;
      const sourceColumnId = activeData.columnId;

      // Find source section and block
      const sourceSection = sectionsState.find((s) => s.id === sourceSectionId);
      if (!sourceSection) return;

      const draggedBlock = findBlockInSection(sourceSection, sourceBlockId);
      if (!draggedBlock) return;

      let newSectionsState = [...sectionsState];

      // Professional cross-container movement handling
      if (overData.type === "section") {
        const targetSectionId = overData.sectionId;

        if (sourceSectionId === targetSectionId && !sourceColumnId) {
          // Same section reorder - let SortableContext handle it
          return;
        }

        // Remove from source
        const updatedSourceSection = removeBlockFromSection(
          sourceSection,
          sourceBlockId
        );
        newSectionsState = newSectionsState.map((s) =>
          s.id === sourceSectionId ? updatedSourceSection : s
        );

        // Add to target with smart insertion
        const targetSection = newSectionsState.find(
          (s) => s.id === targetSectionId
        );
        if (targetSection) {
          const insertionIndex =
            dragState.insertionIndex || targetSection.blocks?.length || 0;
          const updatedTargetSection = addBlockToTargetAtIndex(
            targetSection,
            draggedBlock,
            overData,
            insertionIndex
          );
          newSectionsState = newSectionsState.map((s) =>
            s.id === targetSectionId ? updatedTargetSection : s
          );
        }

        setSectionsState(newSectionsState);
        onUpdate(newSectionsState);

        toast({
          title: "🚀 Block Moved!",
          description: sourceColumnId
            ? "Block successfully moved from column to main section."
            : "Block moved to new section with perfect positioning.",
          duration: 2000,
        });
      } else if (overData.type === "column") {
        const targetColumnId = overData.columnId;
        const targetSectionId = overData.sectionId;

        if (
          sourceSectionId === targetSectionId &&
          sourceColumnId === targetColumnId
        ) {
          // Same column - let SortableContext handle it
          return;
        }

        // Professional column-to-column movement
        const updatedSourceSection = removeBlockFromSection(
          sourceSection,
          sourceBlockId
        );
        newSectionsState = newSectionsState.map((s) =>
          s.id === sourceSectionId ? updatedSourceSection : s
        );

        const targetSection = newSectionsState.find(
          (s) => s.id === targetSectionId
        );
        if (targetSection) {
          const updatedTargetSection = addBlockToTarget(
            targetSection,
            draggedBlock,
            overData
          );
          newSectionsState = newSectionsState.map((s) =>
            s.id === targetSectionId ? updatedTargetSection : s
          );
        }

        setSectionsState(newSectionsState);
        onUpdate(newSectionsState);

        toast({
          title: "✨ Perfect Drop!",
          description: sourceColumnId
            ? "Block moved between columns with professional precision."
            : "Block moved from section to column seamlessly.",
          duration: 2000,
        });
      } else if (overData.type === "block") {
        // Smart block-to-block positioning
        const targetSectionId = overData.sectionId;
        const targetColumnId = overData.columnId;

        if (
          sourceSectionId === targetSectionId &&
          sourceColumnId === targetColumnId
        ) {
          // Same container - let SortableContext handle automatic reordering
          return;
        }

        // Cross-container block positioning
        const updatedSourceSection = removeBlockFromSection(
          sourceSection,
          sourceBlockId
        );
        newSectionsState = newSectionsState.map((s) =>
          s.id === sourceSectionId ? updatedSourceSection : s
        );

        const targetSection = newSectionsState.find(
          (s) => s.id === targetSectionId
        );
        if (targetSection) {
          const targetData = targetColumnId
            ? {
                type: "column",
                columnId: targetColumnId,
                sectionId: targetSectionId,
              }
            : { type: "section", sectionId: targetSectionId };

          const updatedTargetSection = addBlockToTarget(
            targetSection,
            draggedBlock,
            targetData
          );
          newSectionsState = newSectionsState.map((s) =>
            s.id === targetSectionId ? updatedTargetSection : s
          );
        }

        setSectionsState(newSectionsState);
        onUpdate(newSectionsState);

        toast({
          title: "🎯 Precision Placement!",
          description: "Block positioned exactly where you wanted it.",
          duration: 2000,
        });
      }
    },
    [
      sectionsState,
      dragState.insertionIndex,
      onUpdate,
      toast,
      findBlockInSection,
      removeBlockFromSection,
      addBlockToTarget,
    ]
  );

  const handleSectionUpdate = useCallback(
    (sectionId: string, updates: any) => {
      const updatedSections = sectionsState.map((section) =>
        section.id === sectionId ? { ...section, ...updates } : section
      );
      setSectionsState(updatedSections);
      onUpdate(updatedSections);
    },
    [sectionsState, onUpdate]
  );

  const renderPreview = () => {
    // Professional proposal configuration with enhanced branding
    const proposalConfig = {
      company: {
        name: "AltZero Platform",
        logo: "🚀",
        brandIcon: "⚡",
        tagline: "Intelligent Scoping Solutions",
        website: "www.altzero.com",
        email: "<EMAIL>",
        colors: {
          primary: "#3B82F6", // Blue
          secondary: "#8B5CF6", // Purple
          accent: "#F59E0B", // Amber
          success: "#10B981", // Green
        },
      },
      document: {
        title: "Professional Proposal",
        date: new Date().toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
        version: "v1.0",
        confidential: true,
      },
    };

    // Enhanced data handling for both new and existing documents
    console.log("Preview Debug - Raw Sections:", sectionsState);
    console.log("Preview Debug - Sections Length:", sectionsState?.length);
    console.log("Preview Debug - First Section:", sectionsState?.[0]);

    // Handle different data structures for existing vs new documents
    const processedSections =
      sectionsState?.map((section, index) => {
        console.log(`Processing section ${index}:`, section);

        // Try to get content from multiple possible sources
        let content = null;
        let title = null;

        // Check various content sources - be more aggressive in finding content
        if (section.content && section.content.trim()) {
          content = section.content;
          console.log(
            `Found content in 'content' field:`,
            content.substring(0, 100)
          );
        } else if (
          section.blocks &&
          Array.isArray(section.blocks) &&
          section.blocks.length > 0
        ) {
          // Convert blocks to HTML content
          content = renderBlocksToHTML(section.blocks);
          console.log(
            `Converted blocks to content:`,
            content.substring(0, 100)
          );
        } else if (section.body && section.body.trim()) {
          content = section.body;
          console.log(
            `Found content in 'body' field:`,
            content.substring(0, 100)
          );
        } else if (section.text && section.text.trim()) {
          content = section.text;
          console.log(
            `Found content in 'text' field:`,
            content.substring(0, 100)
          );
        } else if (section.description && section.description.trim()) {
          content = section.description;
          console.log(
            `Found content in 'description' field:`,
            content.substring(0, 100)
          );
        } else if (section.html && section.html.trim()) {
          content = section.html;
          console.log(
            `Found content in 'html' field:`,
            content.substring(0, 100)
          );
        } else if (typeof section === "string") {
          // Sometimes the section itself might be a string
          content = section;
          console.log(`Section is a string:`, content.substring(0, 100));
        } else {
          // Try to find any field that looks like content
          const possibleContentFields = Object.keys(section || {}).filter(
            (key) =>
              section &&
              typeof section[key] === "string" &&
              section[key].length > 10 &&
              !["id", "title", "name", "type", "created", "updated"].includes(
                key.toLowerCase()
              )
          );

          if (possibleContentFields.length > 0 && section) {
            content = section[possibleContentFields[0]];
            console.log(
              `Found content in '${possibleContentFields[0]}' field:`,
              content?.substring(0, 100)
            );
          }
        }

        // Check various title sources
        if (section.title && section.title.trim()) {
          title = section.title;
        } else if (section.name && section.name.trim()) {
          title = section.name;
        } else if (section.heading && section.heading.trim()) {
          title = section.heading;
        } else if (section.label && section.label.trim()) {
          title = section.label;
        } else if (section.sectionTitle && section.sectionTitle.trim()) {
          title = section.sectionTitle;
        } else if (section.sectionName && section.sectionName.trim()) {
          title = section.sectionName;
        } else {
          title = `Section ${index + 1}`;
        }

        console.log(`Final processed section ${index}:`, {
          title,
          hasContent: !!(content && content.trim()),
          contentLength: content?.length || 0,
        });

        return {
          ...section,
          title,
          content,
          hasContent: !!(content && content.trim()),
          originalData: section, // Keep original for debugging
        };
      }) || [];

    console.log("Preview Debug - Processed Sections:", processedSections);
    console.log(
      "Preview Debug - Sections with content:",
      processedSections.filter((s) => s.hasContent)
    );

    // If no sections or all sections are empty, try to create some default content
    let finalSections = processedSections;
    if (
      !finalSections ||
      finalSections.length === 0 ||
      !finalSections.some((s) => s.hasContent)
    ) {
      console.log("No valid sections found, checking raw data...");

      // Try to extract data differently - maybe it's nested
      if (
        sectionsState &&
        typeof sectionsState === "object" &&
        !Array.isArray(sectionsState)
      ) {
        // Check if sectionsState has nested data
        const possibleSections: any[] = [];

        // Check various nested structures
        if ((sectionsState as any).sections) {
          const sectionsData = (sectionsState as any).sections;
          possibleSections.push(
            ...(Array.isArray(sectionsData) ? sectionsData : [sectionsData])
          );
        }
        if ((sectionsState as any).data) {
          const dataField = (sectionsState as any).data;
          possibleSections.push(
            ...(Array.isArray(dataField) ? dataField : [dataField])
          );
        }
        if ((sectionsState as any).content) {
          const contentField = (sectionsState as any).content;
          possibleSections.push(
            ...(Array.isArray(contentField) ? contentField : [contentField])
          );
        }
      }
    }

    return (
      <div className="proposal-document bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 min-h-screen">
        {/* Ultra Professional Brand Header */}
        <div className="proposal-header sticky top-0 z-20 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white border-b-4 border-amber-400 shadow-2xl">
          <div className="max-w-6xl mx-auto px-8 py-8">
            <div className="flex items-center justify-between">
              {/* Enhanced Company Branding */}
              <div className="flex items-center gap-6">
                <div className="relative">
                  <div className="text-7xl font-bold animate-pulse drop-shadow-2xl">
                    {proposalConfig.company.logo}
                  </div>
                  <div className="absolute -top-3 -right-3 text-3xl animate-bounce">
                    {proposalConfig.company.brandIcon}
                  </div>
                </div>
                <div>
                  <h1 className="text-5xl font-black text-white drop-shadow-2xl mb-2">
                    {proposalConfig.company.name}
                  </h1>
                  <p className="text-2xl text-amber-200 font-bold tracking-wide drop-shadow-lg">
                    {proposalConfig.company.tagline}
                  </p>
                  <div className="flex items-center gap-4 mt-3">
                    <span className="px-4 py-2 bg-amber-400 text-blue-900 rounded-full text-base font-black shadow-lg">
                      ⭐ PREMIUM
                    </span>
                    <span className="px-4 py-2 bg-green-400 text-green-900 rounded-full text-base font-black shadow-lg">
                      ✅ VERIFIED
                    </span>
                    <span className="px-4 py-2 bg-pink-400 text-pink-900 rounded-full text-base font-black shadow-lg">
                      🚀 PROFESSIONAL
                    </span>
                  </div>
                </div>
              </div>

              {/* Enhanced Document Info */}
              <div className="text-right">
                <div className="flex items-center gap-4 mb-3">
                  <span className="text-3xl font-black text-white drop-shadow-lg">
                    {proposalConfig.document.title}
                  </span>
                  <span className="px-6 py-3 text-xl font-bold bg-gradient-to-r from-amber-400 to-orange-500 text-white rounded-2xl shadow-2xl">
                    {proposalConfig.document.version}
                  </span>
                </div>
                <p className="text-xl text-blue-200 font-bold drop-shadow-lg">
                  📅 {proposalConfig.document.date}
                </p>
                {proposalConfig.document.confidential && (
                  <span className="inline-block mt-4 px-6 py-3 text-base font-black bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-2xl shadow-2xl animate-pulse">
                    🔒 CONFIDENTIAL
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Spectacular Hero Section */}
        <div className="hero-section bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-600 text-white p-16 mx-8 mt-8 rounded-3xl shadow-2xl relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-yellow-400/30 to-orange-500/30 rounded-full -translate-y-32 translate-x-32 animate-pulse"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-br from-cyan-400/30 to-blue-500/30 rounded-full translate-y-24 -translate-x-24 animate-pulse delay-1000"></div>

          <div className="relative z-10 text-center">
            <div className="text-8xl mb-6 animate-bounce">✨</div>
            <h1 className="text-6xl font-black mb-8 text-white drop-shadow-2xl">
              Professional Proposal Document
            </h1>
            <p className="text-3xl text-white/95 mb-10 leading-relaxed font-medium">
              🎯 Crafted with precision and attention to detail
            </p>
            <div className="flex items-center justify-center gap-12 text-xl text-white/90">
              <div className="flex items-center gap-4 bg-white/20 px-6 py-3 rounded-2xl backdrop-blur-sm">
                <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                <span className="font-bold">
                  📊 Generated {proposalConfig.document.date}
                </span>
              </div>
              <div className="flex items-center gap-4 bg-white/20 px-6 py-3 rounded-2xl backdrop-blur-sm">
                <div className="w-4 h-4 bg-amber-400 rounded-full animate-pulse delay-500"></div>
                <span className="font-bold">
                  🚀 Version {proposalConfig.document.version}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Document Sections */}
        <div className="proposal-content max-w-6xl mx-auto">
          <div className="px-8 py-12">
            {finalSections && finalSections.length > 0 ? (
              finalSections.map((section, sectionIndex) => (
                <div
                  key={section.id || sectionIndex}
                  className="proposal-section mb-20"
                >
                  {/* Ultra Colorful Section Header with Logo */}
                  <div className="section-header mb-12">
                    <div className="flex items-center gap-6 mb-8">
                      {/* Animated Number Badge */}
                      <div className="relative">
                        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 text-white rounded-2xl flex items-center justify-center font-black text-2xl shadow-2xl transform rotate-3 hover:rotate-6 transition-transform duration-300">
                          {sectionIndex + 1}
                        </div>
                        <div className="absolute -top-2 -right-2 text-2xl animate-bounce">
                          {proposalConfig.company.brandIcon}
                        </div>
                      </div>

                      {/* Section Title with Brand Header */}
                      <div className="flex-1">
                        {/* Brand Header with Company Name */}
                        <div className="flex items-center gap-4 mb-4">
                          <span className="text-4xl animate-pulse">
                            {proposalConfig.company.logo}
                          </span>
                          <div className="bg-gradient-to-r from-blue-600 to-purple-700 text-white px-6 py-2 rounded-xl shadow-lg">
                            <span className="text-xl font-black">
                              {proposalConfig.company.name}
                            </span>
                          </div>
                          <span className="text-4xl animate-bounce">
                            {proposalConfig.company.brandIcon}
                          </span>
                          <div className="bg-gradient-to-r from-amber-500 to-orange-600 text-white px-4 py-1 rounded-full">
                            <span className="text-sm font-bold">PREMIUM</span>
                          </div>
                        </div>

                        {/* Section Title */}
                        <div className="flex items-center gap-4 mb-3">
                          <h2 className="text-5xl font-black text-gray-800 drop-shadow-lg">
                            {section.title}
                          </h2>
                        </div>

                        {/* Decorative Elements */}
                        <div className="flex items-center gap-4">
                          <div className="h-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full w-48 shadow-lg"></div>
                          <span className="px-4 py-1 bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-full text-sm font-bold shadow-lg">
                            SECTION {sectionIndex + 1}
                          </span>
                          <div className="flex items-center gap-2">
                            <span className="text-2xl">
                              {proposalConfig.company.logo}
                            </span>
                            <span className="text-lg font-bold text-blue-600">
                              {proposalConfig.company.tagline}
                            </span>
                            <span className="text-2xl">
                              {proposalConfig.company.brandIcon}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Vibrant Content Box */}
                  <div className="section-content bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-3xl border-4 border-gradient-to-r from-blue-200 via-purple-200 to-pink-200 p-12 shadow-2xl hover:shadow-3xl transition-all duration-500 relative overflow-hidden">
                    {/* Decorative Background Elements */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-pink-200/30 to-orange-200/30 rounded-full translate-y-12 -translate-x-12"></div>

                    <div className="relative z-10">
                      {section.hasContent ? (
                        <div
                          className="prose prose-2xl max-w-none"
                          dangerouslySetInnerHTML={{ __html: section.content }}
                          style={{
                            lineHeight: "2.0",
                            color: "#1F2937",
                            fontSize: "20px",
                          }}
                        />
                      ) : (
                        <div className="text-center py-20">
                          <div className="text-8xl mb-8 animate-bounce">📝</div>
                          <h3 className="text-3xl font-bold text-gray-700 mb-4">
                            No content yet
                          </h3>
                          <p className="text-xl text-gray-500">
                            Add content in the Editor tab to see it here
                          </p>
                          <div className="mt-8 flex justify-center gap-4">
                            <span className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-bold">
                              📝 Add Text
                            </span>
                            <span className="px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-bold">
                              🖼️ Add Images
                            </span>
                            <span className="px-4 py-2 bg-pink-100 text-pink-700 rounded-full text-sm font-bold">
                              📋 Add Lists
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Spectacular Section Divider */}
                  {sectionIndex < finalSections.length - 1 && (
                    <div className="section-divider mt-20 flex items-center justify-center">
                      <div className="flex items-center gap-4 px-12 py-4 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white rounded-full shadow-2xl">
                        <span className="text-2xl">
                          {proposalConfig.company.logo}
                        </span>
                        <div className="flex gap-2">
                          <div className="w-4 h-4 bg-white rounded-full animate-pulse"></div>
                          <div className="w-4 h-4 bg-white rounded-full animate-pulse delay-100"></div>
                          <div className="w-4 h-4 bg-white rounded-full animate-pulse delay-200"></div>
                        </div>
                        <span className="text-2xl">
                          {proposalConfig.company.brandIcon}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))
            ) : (
              // Enhanced Fallback for Empty State
              <div className="text-center py-24">
                <div className="mb-16">
                  <div className="text-9xl font-bold mb-8 animate-bounce">
                    {proposalConfig.company.logo}
                  </div>
                  <h3 className="text-6xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
                    Welcome to Your Professional Document
                  </h3>
                  <p className="text-2xl text-gray-600 mb-12 max-w-4xl mx-auto">
                    🚀 Start adding content in the Editor tab to see your
                    stunning professional proposal here
                  </p>
                </div>

                <div className="bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-3xl border-4 border-gradient-to-r from-blue-200 to-purple-200 p-16 shadow-2xl max-w-2xl mx-auto">
                  <h4 className="text-3xl font-black text-gray-800 mb-10 flex items-center justify-center gap-4">
                    <span className="text-4xl">
                      {proposalConfig.company.brandIcon}
                    </span>
                    Quick Start Guide
                    <span className="text-4xl">
                      {proposalConfig.company.logo}
                    </span>
                  </h4>
                  <div className="space-y-6 text-xl text-gray-700">
                    <div className="flex items-center gap-6 p-4 bg-blue-100 rounded-2xl">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        1
                      </div>
                      <span className="font-semibold">
                        Click on the "Professional Editor" tab
                      </span>
                    </div>
                    <div className="flex items-center gap-6 p-4 bg-purple-100 rounded-2xl">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                        2
                      </div>
                      <span className="font-semibold">
                        Add blocks using the "Add New Block" button
                      </span>
                    </div>
                    <div className="flex items-center gap-6 p-4 bg-pink-100 rounded-2xl">
                      <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold">
                        3
                      </div>
                      <span className="font-semibold">
                        Your content will appear here instantly!
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Spectacular Footer Section */}
            <div className="proposal-footer mt-24 pt-16 border-t-8 border-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
              <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 text-white p-16 rounded-3xl shadow-2xl relative overflow-hidden">
                {/* Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-pink-600/20"></div>
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full -translate-y-20 translate-x-20"></div>

                <div className="relative z-10">
                  <div className="grid md:grid-cols-2 gap-16">
                    {/* Enhanced Contact Information */}
                    <div>
                      <h3 className="text-4xl font-black mb-8 flex items-center gap-4 text-white">
                        <span className="text-5xl animate-pulse">
                          {proposalConfig.company.logo}
                        </span>
                        Get In Touch
                        <span className="text-3xl animate-bounce">
                          {proposalConfig.company.brandIcon}
                        </span>
                      </h3>
                      <div className="space-y-6 text-2xl text-white/95">
                        <div className="flex items-center gap-6 p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                          <div className="w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-pulse"></div>
                          <span className="font-bold">
                            🌐 {proposalConfig.company.website}
                          </span>
                        </div>
                        <div className="flex items-center gap-6 p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                          <div className="w-6 h-6 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full animate-pulse delay-200"></div>
                          <span className="font-bold">
                            📧 {proposalConfig.company.email}
                          </span>
                        </div>
                        <div className="flex items-center gap-6 p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                          <div className="w-6 h-6 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full animate-pulse delay-400"></div>
                          <span className="font-bold">
                            🎯 Professional Solutions & Support
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Document Metadata */}
                    <div>
                      <h3 className="text-4xl font-black mb-8 text-white">
                        📊 Document Information
                      </h3>
                      <div className="space-y-6 text-2xl text-white/95">
                        <div className="flex justify-between p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                          <span className="font-semibold">📅 Generated:</span>
                          <span className="font-black text-amber-300">
                            {proposalConfig.document.date}
                          </span>
                        </div>
                        <div className="flex justify-between p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                          <span className="font-semibold">🚀 Version:</span>
                          <span className="font-black text-purple-300">
                            {proposalConfig.document.version}
                          </span>
                        </div>
                        <div className="flex justify-between p-4 bg-white/10 rounded-2xl backdrop-blur-sm">
                          <span className="font-semibold">⚡ Status:</span>
                          <span className="font-black text-green-300 animate-pulse">
                            ✅ Active
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Bottom Signature */}
                  <div className="mt-16 pt-12 border-t-2 border-white/30 text-center">
                    <div className="flex items-center justify-center gap-6 mb-6">
                      <span className="text-6xl animate-pulse">
                        {proposalConfig.company.logo}
                      </span>
                      <div className="text-3xl font-black text-white">
                        © 2024 {proposalConfig.company.name}
                      </div>
                      <span className="text-4xl animate-bounce">
                        {proposalConfig.company.brandIcon}
                      </span>
                    </div>
                    <p className="text-xl text-white/90 font-bold">
                      All rights reserved | {proposalConfig.company.tagline}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Ultra Professional Page Footer */}
        <div className="proposal-page-footer fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 border-t-4 border-amber-400 py-4 z-10 shadow-2xl">
          <div className="max-w-6xl mx-auto px-8 flex items-center justify-between">
            <div className="flex items-center gap-6 text-xl text-white">
              <div className="flex items-center gap-3">
                <span className="text-2xl animate-pulse">
                  {proposalConfig.company.logo}
                </span>
                <span className="font-black">
                  {proposalConfig.company.name}
                </span>
                <span className="text-amber-300">•</span>
                <span className="font-bold">
                  {proposalConfig.document.title}
                </span>
                <span className="text-2xl animate-bounce">
                  {proposalConfig.company.brandIcon}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-6">
              {proposalConfig.document.confidential && (
                <span className="text-lg font-black text-red-300 px-4 py-2 bg-red-900/50 rounded-xl border-2 border-red-400 animate-pulse">
                  🔒 CONFIDENTIAL
                </span>
              )}
              <div className="flex items-center gap-4 px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-600 text-white rounded-xl text-lg font-black shadow-lg">
                <span>📄 Page</span>
                <span className="bg-white/30 px-3 py-1 rounded-full">1</span>
                <span>of</span>
                <span className="bg-white/30 px-3 py-1 rounded-full">1</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`w-full ${className}`}>
      <DndContext
        sensors={sensors}
        collisionDetection={professionalCollisionDetection}
        onDragStart={handleDragStart}
        onDragMove={handleDragMove}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <Card className="min-h-[700px] shadow-2xl border-0 bg-gradient-to-br from-white to-gray-50">
          <CardHeader className="pb-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-200">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 bg-white/80 backdrop-blur-sm">
                <TabsTrigger
                  value="edit"
                  className="flex items-center gap-3 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white transition-all duration-300"
                >
                  <Edit3 className="h-5 w-5" />
                  Professional Editor
                </TabsTrigger>
                <TabsTrigger
                  value="preview"
                  className="flex items-center gap-3 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-300"
                >
                  <Eye className="h-5 w-5" />
                  Live Preview
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>

          <CardContent className="p-8">
            <Tabs value={activeTab} className="w-full">
              <TabsContent value="edit" className="mt-0">
                <div className="space-y-12">
                  {sectionsState.map((section) => (
                    <ProfessionalDocumentSection
                      key={section.id}
                      section={section}
                      sectionId={section.id}
                      onUpdate={handleSectionUpdate}
                      readonly={readonly}
                      dragState={dragState}
                    />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="preview" className="mt-0">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl overflow-hidden relative">
                  {/* PDF Viewer Header */}
                  <div className="bg-white/90 backdrop-blur-sm border-b border-gray-200 p-4 sticky top-0 z-30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full"></div>
                          <span className="text-sm font-semibold text-gray-700">
                            Professional Preview
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-500">
                          PDF-Quality Output
                        </span>
                      </div>

                      <div className="flex items-center gap-3">
                        <button className="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
                          Export PDF
                        </button>
                        <button className="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors">
                          Print
                        </button>
                        <div className="flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-full">
                          <span className="text-xs font-medium text-gray-600">
                            100%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* PDF Content Area */}
                  <div
                    className="pdf-viewer-content overflow-auto"
                    style={{ maxHeight: "calc(100vh - 300px)" }}
                  >
                    <div className="p-8">
                      {/* PDF Shadow Effect */}
                      <div className="pdf-document-shadow max-w-none mx-auto relative">
                        <div className="absolute inset-0 bg-black/10 rounded-lg transform translate-x-2 translate-y-2"></div>
                        <div className="relative bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden">
                          {renderPreview()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Professional Drag Overlay with Stunning Effects */}
        <DragOverlay>
          {dragState.activeBlock && (
            <div className="relative">
              {/* Glow Effect */}
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 rounded-2xl opacity-30 blur-lg animate-pulse" />

              {/* Main Overlay */}
              <div className="relative bg-white/95 backdrop-blur-xl border-2 border-blue-300 rounded-xl shadow-2xl p-6 transform rotate-3 hover:rotate-6 transition-transform duration-300">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-xl shadow-lg">
                    <GripVertical className="h-6 w-6" />
                  </div>
                  <div>
                    <Badge variant="outline" className="mb-2 font-bold">
                      {dragState.activeBlock.type.toUpperCase()}
                    </Badge>
                    <div className="text-sm text-gray-600 font-medium">
                      ✨ Dragging professional {dragState.activeBlock.type}{" "}
                      block
                    </div>
                  </div>
                </div>

                {/* Shimmer Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-shimmer" />
              </div>

              {/* Trail Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-xl blur-sm transform rotate-1 -z-10" />
            </div>
          )}
        </DragOverlay>
      </DndContext>
    </div>
  );
}

// Helper Functions
function convertContentToBlocks(htmlContent: string): EnhancedBlock[] {
  // Simple HTML to blocks conversion
  const blocks: EnhancedBlock[] = [];

  if (!htmlContent) return blocks;

  // Split by paragraphs and convert to blocks
  const paragraphs = htmlContent.split(/<\/p>|<br\s*\/?>/i).filter(Boolean);

  paragraphs.forEach((para, index) => {
    const cleanText = para.replace(/<[^>]*>/g, "").trim();
    if (cleanText) {
      blocks.push({
        id: `converted-block-${index}-${Date.now()}`,
        type: "text",
        content: cleanText,
        style: { textAlign: "left" },
      });
    }
  });

  return blocks.length > 0
    ? blocks
    : [
        {
          id: `default-block-${Date.now()}`,
          type: "text",
          content: "Start writing your content here...",
          style: { textAlign: "left" },
        },
      ];
}

function renderBlocksToHTML(blocks: EnhancedBlock[]): string {
  return blocks
    .map((block) => {
      switch (block.type) {
        case "heading":
          const level =
            block.style?.fontSize === "text-3xl"
              ? 1
              : block.style?.fontSize === "text-2xl"
                ? 2
                : 3;
          return `<h${level}>${block.content || "New Heading"}</h${level}>`;

        case "text":
          return `<p>${block.content || "Enter your text here..."}</p>`;

        case "list":
          const items = Array.isArray(block.content)
            ? block.content
            : typeof block.content === "string"
              ? block.content.split("\n").filter(Boolean)
              : ["New list item"];
          return `<ul>${items.map((item) => `<li>${item}</li>`).join("")}</ul>`;

        case "image":
          return block.content
            ? `<img src="${block.content}" alt="Content image" style="max-width: 100%;" />`
            : "";

        case "columns":
          // Render columns as div structure
          const columnData = block.layout?.children || {};
          const columnIds = Object.keys(columnData);
          const columnBlocks = columnIds
            .map((colId) => renderBlocksToHTML(columnData[colId] || []))
            .join('</div><div class="column">');

          return `<div class="columns-layout"><div class="column">${columnBlocks}</div></div>`;

        default:
          return `<p>${block.content || ""}</p>`;
      }
    })
    .join("");
}

function getDefaultContent(type: EnhancedBlock["type"]): any {
  switch (type) {
    case "heading":
      return "New Heading";
    case "text":
      return "Enter your text here...";
    case "list":
      return ["New list item"];
    case "image":
      return "";
    case "columns":
      return null;
    default:
      return "";
  }
}

export default EnhancedDocumentEditor;
