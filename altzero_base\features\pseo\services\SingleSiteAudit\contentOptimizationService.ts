class ContentOptimizationService {
  private readonly MAX_CONTENT_SIZE = 100000; // 100KB limit

  /**
   * Optimize content for AI analysis to avoid payload size issues
   */
  async optimizeForAnalysis(content: string): Promise<string> {
    try {
      if (content.length <= this.MAX_CONTENT_SIZE) {
        return content;
      }

      console.log(`Content too large (${content.length} chars), optimizing for analysis...`);

      // Step 1: Remove unnecessary content while preserving SEO-relevant parts
      let optimized = this.removeNonSEOContent(content);

      // Step 2: If still too large, prioritize important SEO content
      if (optimized.length > this.MAX_CONTENT_SIZE) {
        console.log(`Still too large (${optimized.length} chars), extracting key SEO elements...`);
        optimized = this.extractKeyContent(optimized);
      }

      // Step 3: Final truncation if still too large
      if (optimized.length > this.MAX_CONTENT_SIZE) {
        console.log(`Final truncation needed, cutting to ${this.MAX_CONTENT_SIZE} chars...`);
        optimized = this.performFinalTruncation(optimized);
      }

      console.log(`Content optimized: ${content.length} → ${optimized.length} chars`);
      return optimized;
      
    } catch (error) {
      console.error('Error optimizing content:', error);
      // Fallback: simple truncation
      const fallbackSize = 50000;
      return content.substring(0, fallbackSize) + '\n<!-- Content truncated due to optimization error -->';
    }
  }

  /**
   * Remove non-SEO relevant content
   */
  private removeNonSEOContent(content: string): string {
    return content
      // Remove script tags and their content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      // Remove style tags and their content
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      // Remove comments
      .replace(/<!--[\s\S]*?-->/g, '')
      // Remove SVG content (often large and not SEO relevant)
      .replace(/<svg[^>]*>[\s\S]*?<\/svg>/gi, '')
      // Remove base64 images from src attributes
      .replace(/src="data:image\/[^"]*"/gi, 'src="[base64-image]"')
      // Remove inline styles (keep the elements but remove style attributes)
      .replace(/\s+style="[^"]*"/gi, '')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Extract key SEO content areas
   */
  private extractKeyContent(content: string): string {
    // Extract key SEO elements
    const headMatch = content.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
    const head = headMatch ? headMatch[0] : '';
    
    // Extract main content areas (common patterns)
    const mainContentPatterns = [
      /<main[^>]*>([\s\S]*?)<\/main>/i,
      /<article[^>]*>([\s\S]*?)<\/article>/i,
      /<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/i,
      /<div[^>]*id="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/i,
    ];
    
    let mainContent = '';
    for (const pattern of mainContentPatterns) {
      const match = content.match(pattern);
      if (match) {
        mainContent = match[0];
        break;
      }
    }
    
    // If no main content found, take first part of body
    if (!mainContent) {
      const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      if (bodyMatch) {
        const bodyContent = bodyMatch[1];
        // Take first portion of body content
        const maxBodySize = this.MAX_CONTENT_SIZE - head.length - 1000; // Leave room for structure
        mainContent = bodyContent.substring(0, maxBodySize);
      }
    }
    
    // Reconstruct with essential elements
    return `
      <!DOCTYPE html>
      <html>
      ${head}
      <body>
      ${mainContent}
      </body>
      </html>
    `.trim();
  }

  /**
   * Perform final truncation with smart ending
   */
  private performFinalTruncation(content: string): string {
    let truncated = content.substring(0, this.MAX_CONTENT_SIZE);
    
    // Try to end at a complete tag to avoid broken HTML
    const lastTagEnd = truncated.lastIndexOf('>');
    if (lastTagEnd > this.MAX_CONTENT_SIZE - 1000) {
      truncated = truncated.substring(0, lastTagEnd + 1);
    }
    
    // Add a note about truncation
    return truncated + '\n<!-- Content truncated for analysis -->';
  }

  /**
   * Get optimization statistics
   */
  getOptimizationStats(originalSize: number, optimizedSize: number): {
    originalSize: number;
    optimizedSize: number;
    reductionPercentage: number;
    reductionBytes: number;
  } {
    const reductionBytes = originalSize - optimizedSize;
    const reductionPercentage = Math.round((reductionBytes / originalSize) * 100);

    return {
      originalSize,
      optimizedSize,
      reductionPercentage,
      reductionBytes,
    };
  }
}

export const contentOptimizationService = new ContentOptimizationService(); 