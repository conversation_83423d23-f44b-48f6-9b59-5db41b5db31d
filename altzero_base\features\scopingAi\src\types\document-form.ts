import { ReactNode } from "react";

// Template section interface
export interface TemplateSection {
  id: string;
  name: string;
  description: string;
  sections: string[];
}

// Client interface for the dropdown and data saving
export interface Client {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  company: string;
  createdAt: Date;
  updatedAt: Date;
}

// Client form data interface
export interface ClientFormData {
  id?: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  company: string;
}

// Prompt Template interface
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  variables: string[];
  created_at?: string;
  updated_at?: string;
}

// Reference Document interface
export interface ReferenceDocument {
  id: number;
  title: string;
  client: string;
  date: string;
  type: string;
  sections: string[];
  content: {
    overview: string;
    objectives: string[];
    fullContent: string;
    parsedSections: {
      title: string;
      content: string;
      description: string;
    }[];
  };
}

// Project Information interface
export interface ProjectInfo {
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  budget: string;
}

// AI Prompts interface
export interface AiPrompts {
  styleGuidance: string;
  contentFocus: string;
  additionalInstructions: string;
}

// Document generation data
export interface DocumentGenerationData {
  title: string;
  client: {
    id: string | null;
    name: string;
    contactPerson: string;
    email: string;
    phone: string;
    industry: string;
    company: string;
  };
  template: {
    id: string;
    name: string;
    sections: string[];
  };
  referenceDocument: ReferenceDocument | null;
  preserveOriginalContent: boolean;
  requirements: {
    method: string;
    projectRequirements: string;
    hasUploadedFiles: boolean;
    selectedKnowledgeDocuments?: string[];
    documentRequirements?: Record<string, string>;
  };
  aiPrompts: AiPrompts;
  project: ProjectInfo;
}

// Step component props
export interface StepProps {
  onNext: () => void;
  onBack: () => void;
}

// Context interface
export interface DocumentFormContextType {
  // State
  currentStep: number;
  isGenerating: boolean;
  isLoading: boolean;
  isSavingClient: boolean;
  isSavingPrompt: boolean;
  isLoadingClients: boolean;
  clientsError: string | null;
  selectedTemplate: string;
  referenceDocument: ReferenceDocument | null;
  aiPrompts: AiPrompts;
  requirementsOption: string;
  requirementsText: string;
  selectedKnowledgeDocuments: string[];
  documentRequirements: Record<string, string>;
  generationStatus: string;
  generationProgress: number;
  documentId: string | null;
  clients: Client[];
  clientSearchOpen: boolean;
  selectedClient: Client | null;
  clientData: ClientFormData;
  isNewClient: boolean;
  promptTemplates: PromptTemplate[];
  promptSearchOpen: boolean;
  selectedPrompt: PromptTemplate | null;
  promptData: PromptTemplate;
  isNewPrompt: boolean;
  isPromptDialogOpen: boolean;
  projectInfo: ProjectInfo;
  eventSourceRef: React.RefObject<EventSource | null>;

  // Methods
  setCurrentStep: (step: number) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsLoadingClients: (isLoading: boolean) => void;
  setClientsError: (error: string | null) => void;
  setSelectedTemplate: (template: string) => void;
  setReferenceDocument: (doc: ReferenceDocument | null) => void;
  handlePromptChange: (field: keyof AiPrompts, value: string) => void;
  setRequirementsOption: (option: string) => void;
  setRequirementsText: (text: string) => void;
  setSelectedKnowledgeDocuments: (docs: string[]) => void;
  setDocumentRequirements: (requirements: Record<string, string>) => void;
  setGenerationStatus: (status: string) => void;
  setGenerationProgress: (progress: number) => void;
  setDocumentId: (id: string | null) => void;
  setClients: (clients: Client[]) => void;
  setClientSearchOpen: (open: boolean) => void;
  setSelectedClient: (client: Client | null) => void;
  setClientData: (data: ClientFormData) => void;
  setIsNewClient: (isNew: boolean) => void;
  setIsSavingClient: (isSaving: boolean) => void;
  setPromptTemplates: (templates: PromptTemplate[]) => void;
  setPromptSearchOpen: (open: boolean) => void;
  setSelectedPrompt: (prompt: PromptTemplate | null) => void;
  setPromptData: (data: PromptTemplate) => void;
  setIsNewPrompt: (isNew: boolean) => void;
  setIsSavingPrompt: (isSaving: boolean) => void;
  setIsPromptDialogOpen: (isOpen: boolean) => void;
  setProjectInfo: (info: ProjectInfo) => void;
  handleClientInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  handleProjectInfoChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  handleBudgetChange: (value: string) => void;
  handleClientSelect: (clientId: string) => void;
  handlePromptSelect: (promptId: string) => void;
  handlePromptInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  saveClient: () => Promise<void>;
  savePromptTemplate: () => Promise<void>;
  loadClients: () => Promise<Client[]>;
  handleGenerate: (preserveOriginalContent: boolean) => void;
  handleNext: () => void;
  handleBack: () => void;
  handleRemoveReference: () => void;
  handleTemplateSelect: (templateId: string) => void;
  isCurrentStepValid: () => boolean;
}

// Provider props
export interface DocumentFormProviderProps {
  children: ReactNode;
}
