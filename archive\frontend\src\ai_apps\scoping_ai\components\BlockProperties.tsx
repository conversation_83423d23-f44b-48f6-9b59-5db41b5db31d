import React from 'react';
import { Block } from './ProposalEditor';

interface BlockPropertiesProps {
  block: Block;
  onChange: (updates: Partial<Block>) => void;
}

export const BlockProperties: React.FC<BlockPropertiesProps> = ({ block, onChange }) => {
  switch (block.type) {
    case 'header':
      return (
        <div className="space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Header Level
            </label>
            <div className="flex space-x-3">
              {[1, 2, 3].map((level) => (
                <button
                  key={level}
                  onClick={() => onChange({ level })}
                  className={`flex-1 py-2 px-3 rounded-md ${
                    block.level === level 
                      ? 'bg-indigo-100 border-indigo-300 text-indigo-700 font-medium' 
                      : 'bg-white border-gray-300 text-gray-700'
                  } border`}
                >
                  H{level}
                </button>
              ))}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Text
            </label>
            <input
              type="text"
              value={block.content}
              onChange={(e) => onChange({ content: e.target.value })}
              className="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>
      );
      
    case 'image':
      return (
        <div className="space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image URL
            </label>
            <input
              type="text"
              value={block.url || ''}
              onChange={(e) => onChange({ url: e.target.value })}
              className="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="https://example.com/image.jpg"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Alt Text
            </label>
            <input
              type="text"
              value={block.alt || ''}
              onChange={(e) => onChange({ alt: e.target.value })}
              className="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Description of the image"
            />
          </div>
          {block.url && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preview
              </label>
              <div className="border border-gray-200 rounded-lg p-2 bg-white">
                <img 
                  src={block.url} 
                  alt={block.alt || ''} 
                  className="max-w-full h-auto rounded"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x150?text=Invalid+Image+URL';
                  }}
                />
              </div>
            </div>
          )}
        </div>
      );
      
    case 'list':
      return (
        <div className="space-y-5">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            List Items
          </label>
          {block.items?.map((item, index) => (
            <div key={index} className="flex items-center">
              <input
                type="text"
                value={item}
                onChange={(e) => {
                  const newItems = [...(block.items || [])];
                  newItems[index] = e.target.value;
                  onChange({ items: newItems });
                }}
                className="flex-grow px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <button
                onClick={() => {
                  const newItems = [...(block.items || [])];
                  newItems.splice(index, 1);
                  onChange({ items: newItems });
                }}
                className="ml-2 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full"
                title="Remove item"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          ))}
          <button
            onClick={() => {
              onChange({ items: [...(block.items || []), ''] });
            }}
            className="w-full flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Item
          </button>
        </div>
      );
      
    default:
      return (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Content
          </label>
          <textarea
            value={block.content}
            onChange={(e) => onChange({ content: e.target.value })}
            className="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            rows={6}
          />
        </div>
      );
  }
}; 