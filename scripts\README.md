# AltZero Platform Scripts

This directory contains utility scripts for working with the AltZero platform.

## Base Components Copy Script

The `copy-base-components.sh` script allows you to copy base components from a GitHub repository to a target repository, enabling code reuse between different applications while maintaining a centralized source of common functionality.

### Usage

```bash
./copy-base-components.sh [<target_repo_path>] [--frontend-only|--backend-only]
```

### Arguments

- `target_repo_path`: (Optional) Path to the target repository where components will be copied

### Options

- `--frontend-only`: Copy only frontend components
- `--backend-only`: Copy only backend components

### Interactive Mode

When run, the script enters an interactive mode asking for:

1. **Target Repository Path**: Where to copy the components (default: current directory)
2. **GitHub Repository URL**: Source repository to clone from
3. **Source Branch**: Which branch to pull from (default: main)
4. **App Directory Name**: The base name for your app (default: base)
5. **Source Directory Structure**: Where components are located in the source repo
   - Source frontend directory (default: altzero_base/base)
   - Source backend directory (default: altzero_base/server/base)
6. **Target Directory Structure**: Where to place components in your target repo
   - Target frontend directory (default: base)
   - Target backend directory (default: server/base)
7. **Components to Copy**: (all/frontend/backend)

The script will show a detailed summary and directory structure preview before proceeding.

### Environment Variables for Customization

You can customize the script behavior with these environment variables:

```bash
# Example of customizing paths and components
FRONTEND_BASE_SUBDIRS="components utils" GITHUB_REPO_URL="https://github.com/your-org/repo.git" ./copy-base-components.sh
```

Available variables:

- `TARGET_REPO_PATH`: Path to the target repository (default: current directory)
- `GITHUB_REPO_URL`: URL of the GitHub repository to clone from
- `SOURCE_BRANCH`: Branch from the source repository to copy components from (default: main)
- `APP_NAME`: Name of the app directory (default: 'base')
- `FRONTEND_BASE_DIR`: Base frontend directory (default: 'base')
- `BACKEND_BASE_DIR`: Base backend directory (default: 'server/base')
- `SOURCE_FRONTEND_DIR`: Path in source repo where frontend components are located (default: 'altzero_base/base')
- `SOURCE_BACKEND_DIR`: Path in source repo where backend components are located (default: 'altzero_base/server/base')
- `REFERENCE_DIR_NAME`: Name of reference directory (default: 'reference/base-platform')
- `FRONTEND_BASE_SUBDIRS`: Space-separated list of frontend subdirectories to copy
- `BACKEND_BASE_SUBDIRS`: Space-separated list of backend subdirectories to copy

### Examples

Copy all base components interactively:

```bash
./copy-base-components.sh
```

Copy components to a specific repository with specific options:

```bash
./copy-base-components.sh /path/to/my-new-app --frontend-only
```

Use environment variables to specify GitHub repository and branch:

```bash
GITHUB_REPO_URL="https://github.com/Altzero-AU/altzero-base.git" SOURCE_BRANCH="reStructure" ./copy-base-components.sh
```

### What Gets Copied

#### Directly Copied Components
The following components are copied directly to matching locations in the target repository:

- `{FRONTEND_BASE_DIR}/{components,utils,lib,hooks,config,types,services,contextapi,supabase}`: Reusable frontend components
- `{BACKEND_BASE_DIR}/common`: Backend base modules

The default structure will create:
- `base/components`, `base/utils`, etc. for frontend
- `server/base/common` for backend

#### Reference Directory
Other specified files are copied to the reference directory (`reference/base-platform/` by default):

- **Layout & Header**:
  - `layout/`: Layout components
  - `header/`: Header components

- **Configuration Templates**:
  - `.cursorrules`: Development guidelines
  - `tsconfig.json`: TypeScript configuration
  - `vite.config.ts`: Vite configuration
  - `postcss.config.js`: CSS processing configuration
  - `.eslintrc.js`: ESLint configuration
  - `.prettierrc`: Prettier configuration

- **Environment Files**:
  - `.env.sample`
  - `.env.example`
  - `.env.local.example`

- **Documentation**:
  - `wiki/ARCHITECTURE.md`

- **Package Management**:
  - Creates comparison files between source and target package.json
  - Provides a guide for merging dependencies

### Smart Component Finding

If components aren't found at the expected paths in the source repository, the script will:
1. Search the entire repository for directories with matching names
2. Copy the first match found
3. Warn about any components it couldn't locate

### Directory Structure Flexibility

The script offers flexibility in directory structure:

- **Source Repository**: Can have components nested in directories like `altzero_base/base/components`
- **Target Repository**: Places components in a simpler structure like `base/components`

This allows copying from third-party repositories with different structures than your own.

### Package.json Handling

Instead of overwriting package.json files, the script creates comparison files:

1. Copies the base repository's package.json to `reference/base-platform/package.json.base`
2. Copies the target repository's package.json to `reference/base-platform/package.json.target`
3. Creates a guide explaining how to merge dependencies

This approach allows developers to:
- See what dependencies are required by the base platform
- Decide which dependencies to add or update in their application
- Maintain application-specific dependencies

### Notes

- After copying, you may need to adjust import paths in the new repository
- Remember to review the copied files to ensure they match your application's requirements
- Consider setting up a periodic sync process if you plan to regularly update base components
- Review the reference directory for layout, header, and configuration templates 