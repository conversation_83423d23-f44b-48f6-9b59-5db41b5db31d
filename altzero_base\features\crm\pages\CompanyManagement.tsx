import React, { useState, useEffect } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Building,
  Users,
  Globe,
  Mail,
  Phone,
  Filter,
  RefreshCw,
  MoreHorizontal,
  Eye,
  MapPin,
  Calendar,
  Tag,
  SortAsc,
  SortDesc,
  Download,
  Upload,
} from "lucide-react";
import { crmService } from "../services/crmService";
import { Company, CompanyFilters, PaginatedResponse } from "../types";
import CompanyForm from "../components/CompanyForm";
import Modal from "../components/Modal";
import CRMLayout from "../components/CRMLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../base/components/ui/card";
import { Button } from "../../../base/components/ui/button";
import { Badge } from "../../../base/components/ui/badge";
import { Input } from "../../../base/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../base/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../base/components/ui/select";
import { Avatar, AvatarFallback } from "../../../base/components/ui/avatar";

const CompanyManagement: React.FC = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string>("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Optimized for large datasets
  const pageSize = 50;

  useEffect(() => {
    loadCompanies();
  }, [currentPage, searchTerm]);

  const loadCompanies = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: CompanyFilters = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
      };

      const response: PaginatedResponse<Company> =
        await crmService.getCompanies(filters);
      setCompanies(response.data);
      setTotal(response.total);
    } catch (err) {
      console.error("Error loading companies:", err);
      setError(err instanceof Error ? err.message : "Failed to load companies");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCompany = async (companyData: any) => {
    try {
      await crmService.createCompany(companyData);
      setShowCreateModal(false);
      loadCompanies();
    } catch (err) {
      console.error("Error creating company:", err);
      throw err;
    }
  };

  const handleEditCompany = async (companyData: any) => {
    if (!selectedCompany?.id) return;

    try {
      await crmService.updateCompany(selectedCompany.id, companyData);
      setShowEditModal(false);
      setSelectedCompany(null);
      loadCompanies();
    } catch (err) {
      console.error("Error updating company:", err);
      throw err;
    }
  };

  const handleDeleteCompany = async () => {
    if (!selectedCompany?.id) return;

    try {
      await crmService.deleteCompany(selectedCompany.id);
      setShowDeleteModal(false);
      setSelectedCompany(null);
      loadCompanies();
    } catch (err) {
      console.error("Error deleting company:", err);
      setError(err instanceof Error ? err.message : "Failed to delete company");
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    loadCompanies();
  };

  const openEditModal = (company: Company) => {
    setSelectedCompany(company);
    setShowEditModal(true);
  };

  const openDeleteModal = (company: Company) => {
    setSelectedCompany(company);
    setShowDeleteModal(true);
  };

  const totalPages = Math.ceil(total / pageSize);

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Company Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage your business relationships and partnerships
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={loadCompanies}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <MoreHorizontal className="h-4 w-4" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem className="gap-2">
                    <Upload className="h-4 w-4" />
                    Import Companies
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Download className="h-4 w-4" />
                    Export Companies
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="gap-2">
                    <Filter className="h-4 w-4" />
                    Advanced Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Company
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col lg:flex-row gap-3">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      type="text"
                      placeholder="Search companies by name, email, website, or industry..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name A-Z</SelectItem>
                      <SelectItem value="created_at">Date Added</SelectItem>
                      <SelectItem value="employees">Size</SelectItem>
                      <SelectItem value="industry">Industry</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                    }
                    className="px-2"
                  >
                    {sortOrder === "asc" ? (
                      <SortAsc className="h-4 w-4" />
                    ) : (
                      <SortDesc className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    type="button"
                    onClick={handleSearch}
                    size="sm"
                    className="gap-2"
                  >
                    <Search className="h-4 w-4" />
                    Search
                  </Button>
                </div>
              </div>

              {/* Bulk Actions Bar */}
              {selectedCompanies.length > 0 && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-900">
                      {selectedCompanies.length} company
                      {selectedCompanies.length > 1 ? "s" : ""} selected
                    </span>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="gap-2">
                        <Download className="h-4 w-4" />
                        Export Selected
                      </Button>
                      <Button variant="outline" size="sm" className="gap-2">
                        <Tag className="h-4 w-4" />
                        Add Tags
                      </Button>
                      <Button variant="destructive" size="sm" className="gap-2">
                        <Trash2 className="h-4 w-4" />
                        Delete Selected
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedCompanies([])}
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Companies
                </CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Website
                </CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {companies.filter((c) => c.website).length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Email
                </CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {companies.filter((c) => c.email).length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg Employees
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {companies.length > 0
                    ? Math.round(
                        companies.reduce(
                          (sum, c) => sum + (c.employees || 0),
                          0
                        ) / companies.length
                      )
                    : 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Error Display */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                    <Building className="w-6 h-6 text-destructive" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-destructive">
                      Error Loading Companies
                    </h3>
                    <p className="text-muted-foreground mt-1">{error}</p>
                  </div>
                  <Button onClick={loadCompanies} variant="destructive">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Modern Company Table */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Companies ({total.toLocaleString()})
                </CardTitle>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    Page {currentPage} of {totalPages}
                  </span>
                  <span>•</span>
                  <span>{pageSize} per page</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6">
                  <div className="space-y-3">
                    {[...Array(8)].map((_, i) => (
                      <div
                        key={i}
                        className="flex items-center space-x-4 p-4 border rounded-lg"
                      >
                        <div className="w-10 h-10 bg-muted rounded-full animate-pulse"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-muted rounded w-1/4 animate-pulse"></div>
                          <div className="h-3 bg-muted rounded w-1/3 animate-pulse"></div>
                        </div>
                        <div className="flex gap-2">
                          <div className="h-8 w-8 bg-muted rounded animate-pulse"></div>
                          <div className="h-8 w-8 bg-muted rounded animate-pulse"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : companies.length === 0 ? (
                <div className="p-12 text-center">
                  <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No companies found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm
                      ? `No companies match "${searchTerm}". Try adjusting your search.`
                      : "Get started by creating your first company."}
                  </p>
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Company
                  </Button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="border-b bg-muted/20">
                      <tr className="text-left">
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          <input
                            type="checkbox"
                            checked={
                              selectedCompanies.length === companies.length &&
                              companies.length > 0
                            }
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedCompanies(
                                  companies
                                    .map((c) => c.id)
                                    .filter((id): id is string => Boolean(id))
                                );
                              } else {
                                setSelectedCompanies([]);
                              }
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Company
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Contact Info
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Industry
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Size
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Location
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Date Added
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {companies.map((company) => (
                        <tr
                          key={company.id}
                          className="hover:bg-muted/50 transition-colors group"
                        >
                          <td className="p-4">
                            <input
                              type="checkbox"
                              checked={selectedCompanies.includes(
                                company.id || ""
                              )}
                              onChange={(e) => {
                                if (e.target.checked && company.id) {
                                  setSelectedCompanies([
                                    ...selectedCompanies,
                                    company.id,
                                  ]);
                                } else if (company.id) {
                                  setSelectedCompanies(
                                    selectedCompanies.filter(
                                      (id) => id !== company.id
                                    )
                                  );
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                                  {company.name
                                    ?.split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase() || "?"}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-semibold text-sm text-foreground">
                                  {company.name}
                                </div>
                                {company.website && (
                                  <a
                                    href={company.website}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {company.website}
                                  </a>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="space-y-1">
                              {company.email && (
                                <div className="flex items-center gap-2">
                                  <Mail className="h-3 w-3 text-muted-foreground" />
                                  <a
                                    href={`mailto:${company.email}`}
                                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {company.email}
                                  </a>
                                </div>
                              )}
                              {company.phone && (
                                <div className="flex items-center gap-2">
                                  <Phone className="h-3 w-3 text-muted-foreground" />
                                  <a
                                    href={`tel:${company.phone}`}
                                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {company.phone}
                                  </a>
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            {(company as any).industry ? (
                              <Badge
                                variant="outline"
                                className="text-xs font-medium"
                              >
                                {(company as any).industry}
                              </Badge>
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                -
                              </span>
                            )}
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              <Users className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm font-medium">
                                {company.employees
                                  ? `${company.employees.toLocaleString()}`
                                  : "-"}
                              </span>
                            </div>
                          </td>
                          <td className="p-4">
                            {company.address ? (
                              <div className="flex items-center gap-2">
                                <MapPin className="h-3 w-3 text-muted-foreground" />
                                <span className="text-sm">
                                  {company.address}
                                </span>
                              </div>
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                -
                              </span>
                            )}
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">
                                {company.created_at
                                  ? new Date(
                                      company.created_at
                                    ).toLocaleDateString()
                                  : "-"}
                              </span>
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => openEditModal(company)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => openEditModal(company)}
                                    className="gap-2"
                                  >
                                    <Eye className="h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => openEditModal(company)}
                                    className="gap-2"
                                  >
                                    <Edit className="h-4 w-4" />
                                    Edit Company
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => openDeleteModal(company)}
                                    className="gap-2 text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    Delete Company
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="px-4 py-3 border-t bg-muted/20">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                  <div className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * pageSize + 1} to{" "}
                    {Math.min(currentPage * pageSize, total)} of{" "}
                    {total.toLocaleString()} companies
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="px-2"
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Badge variant="secondary" className="px-3 py-1">
                      {currentPage} of {totalPages}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="px-2"
                    >
                      Last
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Create Company Modal */}
          <Modal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            title="Create New Company"
          >
            <CompanyForm
              onSubmit={handleCreateCompany}
              onCancel={() => setShowCreateModal(false)}
            />
          </Modal>

          {/* Edit Company Modal */}
          <Modal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            title="Edit Company"
          >
            {selectedCompany && (
              <CompanyForm
                company={selectedCompany}
                onSubmit={handleEditCompany}
                onCancel={() => setShowEditModal(false)}
              />
            )}
          </Modal>

          {/* Delete Confirmation Modal */}
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Company"
          >
            <div className="p-6">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete{" "}
                <strong>{selectedCompany?.name}</strong>? This action cannot be
                undone and will affect all related contacts.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteCompany}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </CRMLayout>
  );
};

export default CompanyManagement;
