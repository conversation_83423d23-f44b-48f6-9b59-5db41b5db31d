import { useState, useEffect, useRef } from "react";
import Layout from "../../../components/Layout";
import { proposalAPI } from "../../../services/api";
import ProposalEditor, { Block } from "./ProposalEditor";
import { API_URL, ENDPOINTS } from "../../../utils/constants";

// Define types for streaming updates
interface ResearchData {
  client_info: string;
  industry_insights: string;
  key_points: string[];
}

interface StreamState {
  isStreaming: boolean;
  progress: number;
  stage: string;
  message: string;
  research: ResearchData | null;
  sections: Array<{ title: string; content: string }>;
  executiveSummary: string;
  title: string;
}

// New interface for the editor state
interface EditorState {
  isEditing: boolean;
  blocks: Block[];
}

interface StreamEvent extends MessageEvent {
  type:
    | "started"
    | "progress"
    | "research_complete"
    | "section_complete"
    | "summary_complete"
    | "complete";
}

interface ProposalStreamResponse {
  executiveSummary: string;
  sections: Array<{
    title: string;
    content: string;
  }>;
}

// Define these interfaces locally since they're not properly exported
interface ProposalRequest {
  proposerName: string;
  clientName: string;
  clientDescription: string;
  projectScope: string;
}

interface ProposalResponse {
  executiveSummary: string;
  sections: Array<{
    title: string;
    content: string;
  }>;
  title?: string;
  clientName?: string;
}

const ProposalGenerator = () => {
  // Form data state
  const [formData, setFormData] = useState<ProposalRequest>({
    proposerName: "",
    clientName: "",
    clientDescription: "",
    projectScope: "",
  });

  // States for API interaction
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [proposal, setProposal] = useState<ProposalResponse | null>(null);

  // State for streaming updates
  const [streamState, setStreamState] = useState<StreamState>({
    isStreaming: false,
    progress: 0,
    stage: "",
    message: "",
    research: null,
    sections: [],
    executiveSummary: "",
    title: "",
  });

  // Reference to the EventSource
  const eventSourceRef = useRef<EventSource | null>(null);

  // Add editor state
  const [editorState, setEditorState] = useState<EditorState>({
    isEditing: false,
    blocks: [],
  });

  // Add state to track sidebar status from the editor
  const [editorSidebarOpen, setEditorSidebarOpen] = useState(true);

  // Clean up event source on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setProposal(null);

    // Reset stream state
    setStreamState({
      isStreaming: true,
      progress: 0,
      stage: "starting",
      message: "Starting proposal generation...",
      research: null,
      sections: [],
      executiveSummary: "",
      title: `Proposal for ${formData.clientName}`,
    });

    try {
      // Check if we should use streaming or regular API
      if (proposalAPI.supportsStreaming) {
        // Set up streaming connection
        startStreamingProposal();
      } else {
        // Fallback to non-streaming API
        const data = await proposalAPI.generateProposal(formData);
        setProposal(data);
        setStreamState((prev) => ({ ...prev, isStreaming: false }));
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
      console.error("Error generating proposal:", err);
      setStreamState((prev) => ({ ...prev, isStreaming: false }));
    } finally {
      if (!proposalAPI.supportsStreaming) {
        setLoading(false);
      }
    }
  };

  const handleStarted = (event: StreamEvent) => {
    try {
      const data = JSON.parse(event.data);
      setStreamState((prev) => ({
        ...prev,
        progress: 5,
        message: data.message || "Starting...",
      }));
    } catch (error) {
      console.error("Error parsing started event:", error);
      setStreamState((prev) => ({
        ...prev,
        progress: 5,
        message: "Starting...",
      }));
    }
  };

  const handleProgress = (event: StreamEvent) => {
    const data = JSON.parse(event.data);
    setStreamState((prev) => ({
      ...prev,
      progress: data.progress,
      stage: data.stage,
      message: data.message,
    }));
  };

  const handleComplete = (event: StreamEvent) => {
    const data = JSON.parse(event.data);
    const proposal = data.proposal;

    setProposal({
      executiveSummary: proposal.executiveSummary,
      sections: proposal.sections,
    });

    // Convert proposal to blocks for editing
    const blocks = convertProposalToBlocks(proposal);
    setEditorState({
      isEditing: true,
      blocks,
    });

    setStreamState((prev) => ({
      ...prev,
      isStreaming: false,
      progress: 100,
      stage: "complete",
      message: "Proposal generation complete",
    }));

    setLoading(false);
  };

  const startStreamingProposal = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    const params = new URLSearchParams({
      clientName: formData.clientName,
      description: formData.clientDescription,
      apiKey: "scopingai",
    });

    const url = `${API_URL}${ENDPOINTS.PROPOSALS_STREAM}?${params.toString()}`;

    try {
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      // Track connection state
      let isConnected = false;

      eventSource.onopen = () => {
        console.log("Connection established");
        isConnected = true;
        setError(null);
      };

      // Handle different event types
      eventSource.addEventListener("started", (event: MessageEvent) => {
        console.log("Received started event:", event.data);
        try {
          const data = JSON.parse(event.data);
          setStreamState((prev) => ({
            ...prev,
            isStreaming: true,
            progress: prev.progress + 5,
            message: data.message || "Processing...",
          }));
        } catch (error) {
          console.error("Error parsing started event:", error);
        }
      });

      eventSource.addEventListener("research", (event: MessageEvent) => {
        console.log("Received research event:", event.data);
        try {
          const data = JSON.parse(event.data);
          setStreamState((prev) => ({
            ...prev,
            progress: 30,
            research: data.content
              ? {
                  client_info: data.content,
                  industry_insights: data.content,
                  key_points: data.content
                    .split("\n")
                    .filter((line: string) => line.trim().startsWith("-")),
                }
              : null,
          }));
        } catch (error) {
          console.error("Error parsing research event:", error);
        }
      });

      eventSource.addEventListener("summary", (event: MessageEvent) => {
        console.log("Received summary event:", event.data);
        try {
          const data = JSON.parse(event.data);
          setStreamState((prev) => ({
            ...prev,
            progress: 50,
            executiveSummary: data.content || "",
          }));
        } catch (error) {
          console.error("Error parsing summary event:", error);
        }
      });

      eventSource.addEventListener("section", (event: MessageEvent) => {
        console.log("Received section event:", event.data);
        try {
          const data = JSON.parse(event.data);
          if (data.section) {
            setStreamState((prev) => ({
              ...prev,
              progress: prev.progress + 10,
              sections: [...prev.sections, data.section],
            }));
          }
        } catch (error) {
          console.error("Error parsing section event:", error);
        }
      });

      eventSource.addEventListener("completed", (event: MessageEvent) => {
        console.log("Received completed event:", event.data);
        try {
          const data = JSON.parse(event.data);
          if (data.proposal) {
            setProposal(data.proposal);
            setStreamState((prev) => ({
              ...prev,
              isStreaming: false,
              progress: 100,
            }));
          }
          eventSource.close();
        } catch (error) {
          console.error("Error parsing completed event:", error);
        }
      });

      eventSource.addEventListener("error", (event: Event) => {
        console.error("Stream error:", event);
        if (isConnected) {
          setError("Connection lost. Please try again.");
          setStreamState((prev) => ({ ...prev, isStreaming: false }));
          setLoading(false);
          eventSource.close();
        }
      });

      return () => {
        if (eventSourceRef.current) {
          eventSourceRef.current.close();
          eventSourceRef.current = null;
        }
      };
    } catch (error) {
      console.error("Error creating EventSource:", error);
      setError("Failed to establish connection. Please try again.");
      setStreamState((prev) => ({ ...prev, isStreaming: false }));
      setLoading(false);
    }
  };

  const resetForm = () => {
    setProposal(null);
    setError(null);
    setLoading(false);
    setStreamState({
      isStreaming: false,
      progress: 0,
      stage: "",
      message: "",
      research: null,
      sections: [],
      executiveSummary: "",
      title: "",
    });
    setFormData({
      proposerName: "",
      clientName: "",
      clientDescription: "",
      projectScope: "",
    });

    // Close any existing connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
  };

  // Convert proposal sections to editor blocks
  const convertProposalToBlocks = (proposal: ProposalResponse): Block[] => {
    const blocks: Block[] = [];

    // Add executive summary
    blocks.push({
      id: `block-summary-${Date.now()}`,
      type: "header",
      content: "Executive Summary",
      level: 1,
    });

    blocks.push({
      id: `block-summary-content-${Date.now()}`,
      type: "text",
      content: proposal.executiveSummary,
    });

    // Add sections
    proposal.sections.forEach((section, index) => {
      blocks.push({
        id: `block-section-${index}-${Date.now()}`,
        type: "header",
        content: section.title,
        level: 2,
      });

      // Parse section content into paragraphs
      const paragraphs = section.content.split("\n\n");
      paragraphs.forEach((paragraph, pIndex) => {
        if (paragraph.trim()) {
          blocks.push({
            id: `block-section-${index}-para-${pIndex}-${Date.now()}`,
            type: "text",
            content: paragraph.trim(),
          });
        }
      });
    });

    return blocks;
  };

  // Update the handleSaveProposal function to receive sidebar state
  const handleSaveProposal = (
    title: string,
    blocks: Block[],
    sidebarOpen: boolean
  ) => {
    // Here you could send the updated proposal back to the backend
    // For now, we'll just update the local state
    setEditorState({
      isEditing: false,
      blocks: blocks,
    });
    setEditorSidebarOpen(sidebarOpen);

    // You could also reconstruct a proposal object from blocks
    // and update the proposal state
  };

  // Handle edit button click
  const handleEditProposal = () => {
    if (proposal) {
      setEditorState({
        isEditing: true,
        blocks: convertProposalToBlocks(proposal),
      });
    }
  };

  return (
    <Layout>
      <div className="p-4 md:p-8">
        {!editorState.isEditing && (
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Proposal Generator
            </h1>
            <p className="mt-2 text-gray-600">
              Create professional proposals with AI assistance
            </p>
          </div>
        )}

        <div className="max-w-7xl mx-auto">
          {" "}
          {/* Increased max width for full screen */}
          {/* Form */}
          {!proposal && !streamState.isStreaming && !editorState.isEditing && (
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Proposer - New Field */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Proposer
                </label>
                <input
                  type="text"
                  value={formData.proposerName}
                  onChange={(e) =>
                    setFormData({ ...formData, proposerName: e.target.value })
                  }
                  className="w-full px-4 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter proposer name"
                  required
                />
              </div>

              {/* Client - Renamed from Client Name */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Client
                </label>
                <input
                  type="text"
                  value={formData.clientName}
                  onChange={(e) =>
                    setFormData({ ...formData, clientName: e.target.value })
                  }
                  className="w-full px-4 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter client name"
                  required
                />
              </div>

              {/* Description of proposal - Renamed from Project Description */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Description of proposal
                </label>
                <textarea
                  value={formData.clientDescription}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      clientDescription: e.target.value,
                    })
                  }
                  rows={4}
                  className="w-full px-4 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Describe the proposal requirements and goals..."
                  required
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 p-4 rounded-lg border border-red-200 text-red-700">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <div className="flex justify-center">
                <button
                  type="submit"
                  className="px-8 py-3 bg-indigo-500 text-white font-medium rounded-lg hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? (
                    <span className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Generating...
                    </span>
                  ) : (
                    "Create Proposal"
                  )}
                </button>
              </div>
            </form>
          )}
          {/* Streaming Progress Display */}
          {streamState.isStreaming && (
            <div className="space-y-8">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {streamState.title || `Proposal for ${formData.clientName}`}
                </h2>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {streamState.stage
                        ? `${
                            streamState.stage.charAt(0).toUpperCase() +
                            streamState.stage.slice(1)
                          }`
                        : "Starting"}
                    </span>
                    <span className="text-sm font-medium text-gray-700">
                      {streamState.progress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-indigo-500 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                      style={{ width: `${streamState.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    {streamState.message}
                  </p>
                </div>

                {/* Research Data (if available) */}
                {streamState.research && (
                  <div className="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-100">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">
                      Research Findings
                    </h3>

                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-700 mb-1">
                        Client Information
                      </h4>
                      <p className="text-gray-600">
                        {streamState.research.client_info}
                      </p>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-md font-medium text-gray-700 mb-1">
                        Industry Insights
                      </h4>
                      <p className="text-gray-600">
                        {streamState.research.industry_insights}
                      </p>
                    </div>

                    {streamState.research.key_points.length > 0 && (
                      <div>
                        <h4 className="text-md font-medium text-gray-700 mb-1">
                          Key Points
                        </h4>
                        <ul className="list-disc pl-5 text-gray-600">
                          {streamState.research.key_points.map((point, idx) => (
                            <li key={idx}>{point}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {/* Executive Summary (if available) */}
                {streamState.executiveSummary && (
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      Executive Summary
                    </h3>
                    <p className="text-gray-700">
                      {streamState.executiveSummary}
                    </p>
                  </div>
                )}

                {/* Sections (as they become available) */}
                {streamState.sections.length > 0 && (
                  <div className="space-y-6">
                    {streamState.sections.map((section, index) => (
                      <div key={index} className="border-t pt-6 animate-fadeIn">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">
                          {section.title}
                        </h3>
                        <p className="text-gray-700 whitespace-pre-line">
                          {section.content}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Cancel Button */}
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    if (eventSourceRef.current) {
                      eventSourceRef.current.close();
                      eventSourceRef.current = null;
                    }
                    resetForm();
                  }}
                  className="px-8 py-3 bg-red-100 text-red-700 font-medium rounded-lg hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                >
                  Cancel Generation
                </button>
              </div>
            </div>
          )}
          {/* Proposal Editor - Full Screen */}
          {editorState.isEditing && (
            <div className="h-[calc(100vh-120px)]">
              {" "}
              {/* Adjusted height for better fit */}
              <ProposalEditor
                initialTitle={proposal?.title || `Proposal for ${formData.clientName}`}
                initialBlocks={editorState.blocks}
                onSave={handleSaveProposal}
                onSidebarToggle={setEditorSidebarOpen}
                initialSidebarOpen={editorSidebarOpen}
              />
            </div>
          )}
          {/* Display Final Generated Proposal */}
          {proposal && !streamState.isStreaming && !editorState.isEditing && (
            <div className="space-y-8">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {proposal.title || "Proposal"}
                </h2>
                <p className="text-sm text-gray-500 mb-6">
                  For: {proposal.clientName || formData.clientName}
                </p>

                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Executive Summary
                  </h3>
                  <p className="text-gray-700">{proposal.executiveSummary}</p>
                </div>

                <div className="space-y-6">
                  {proposal.sections.map((section, index) => (
                    <div key={index} className="border-t pt-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        {section.title}
                      </h3>
                      <p className="text-gray-700 whitespace-pre-line">
                        {section.content}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  onClick={handleEditProposal}
                  className="px-8 py-3 bg-indigo-500 text-white font-medium rounded-lg hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
                >
                  Edit Proposal
                </button>

                <button
                  onClick={resetForm}
                  className="px-8 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  Create Another Proposal
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default ProposalGenerator;
