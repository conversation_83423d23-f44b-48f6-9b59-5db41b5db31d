"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Plus,
  GripVertical,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  Image as ImageIcon,
  Columns,
  Trash2,
  Copy,
  Settings,
  Upload,
  Layout,
  AlignLeft,
  AlignCenter,
  AlignRight,
  ChevronDown,
  Move,
  Edit3,
  Save,
  X,
  Sparkles,
  Blocks,
} from "lucide-react";
import { Button } from "../../../../../base/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
} from "../../../../../base/components/ui/card";
import { Badge } from "../../../../../base/components/ui/badge";
import { Input } from "../../../../../base/components/ui/input";
import { Textarea } from "../../../../../base/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../../../base/components/ui/dropdown-menu";
import { useToast } from "../../../../../base/hooks/use-toast";
import { useDocumentContext } from "../../../contexts/DocumentContext";

export interface AdvancedBlock {
  id: string;
  type:
    | "text"
    | "heading"
    | "list"
    | "image"
    | "columns"
    | "spacer"
    | "divider";
  content: any;
  style?: {
    backgroundColor?: string;
    textColor?: string;
    padding?: string;
    margin?: string;
    textAlign?: "left" | "center" | "right";
    fontSize?: string;
    fontWeight?: string;
  };
  layout?: {
    columns?: number;
    children?: AdvancedBlock[];
  };
}

interface AdvancedBlockEditorProps {
  blocks: AdvancedBlock[];
  onBlocksChange: (blocks: AdvancedBlock[]) => void;
  sectionId: string;
  onAddImage?: (sectionId: string) => void;
  readonly?: boolean;
  existingContent?: string;
}

// Sortable Block Component
function SortableBlock({
  block,
  onUpdate,
  onDelete,
  onDuplicate,
  readonly = false,
}: {
  block: AdvancedBlock;
  onUpdate: (updates: Partial<AdvancedBlock>) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  readonly?: boolean;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: block.id });

  const [isEditing, setIsEditing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [localContent, setLocalContent] = useState(block.content);
  const { toast } = useToast();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Update local content when block content changes
  useEffect(() => {
    setLocalContent(block.content);
  }, [block.content]);

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height =
        Math.max(120, textareaRef.current.scrollHeight) + "px";
    }
  };

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      adjustTextareaHeight();
    }
  }, [isEditing]);

  // Handle content changes
  const handleContentChange = (newContent: any) => {
    setLocalContent(newContent);
    onUpdate({ content: newContent });
  };

  // Handle save and stop editing
  const handleSave = () => {
    onUpdate({ content: localContent });
    setIsEditing(false);
    toast({
      title: "Content Saved",
      description: "Your changes have been saved successfully.",
      duration: 2000,
    });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const newContent = {
          ...localContent,
          src: event.target?.result as string,
          alt: file.name,
        };
        handleContentChange(newContent);
        toast({
          title: "Image uploaded",
          description: "Your image has been added to the block.",
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const renderBlockContent = () => {
    switch (block.type) {
      case "text":
        return (
          <div className="min-h-[100px]">
            {isEditing && !readonly ? (
              <Textarea
                ref={textareaRef}
                value={localContent.text || ""}
                onChange={(e) => {
                  const newContent = { ...localContent, text: e.target.value };
                  handleContentChange(newContent);
                  adjustTextareaHeight();
                }}
                onBlur={handleSave}
                placeholder="Enter your text here..."
                className="min-h-[120px] resize-none border-0 shadow-none focus-visible:ring-0 bg-transparent text-base leading-relaxed"
                style={{
                  fontFamily: block.style?.fontSize || "Inter",
                  fontSize: block.style?.fontSize || "1rem",
                  fontWeight: block.style?.fontWeight || "400",
                  color: block.style?.textColor,
                  textAlign: block.style?.textAlign || "left",
                }}
              />
            ) : (
              <div
                className="prose prose-lg max-w-none cursor-text p-4 rounded hover:bg-gray-50 min-h-[100px] transition-colors leading-relaxed"
                onClick={() => !readonly && setIsEditing(true)}
                style={{
                  textAlign: block.style?.textAlign || "left",
                  color: block.style?.textColor,
                  fontSize: block.style?.fontSize || "1rem",
                  fontWeight: block.style?.fontWeight || "400",
                  backgroundColor: block.style?.backgroundColor,
                }}
              >
                {localContent.text || (
                  <span className="text-gray-400 italic">
                    Click to add text content...
                  </span>
                )}
              </div>
            )}
          </div>
        );

      case "heading":
        const level = localContent.level || 2;
        const headingStyles = {
          1: { fontSize: "2rem", fontWeight: "700" },
          2: { fontSize: "1.5rem", fontWeight: "600" },
          3: { fontSize: "1.25rem", fontWeight: "600" },
          4: { fontSize: "1.125rem", fontWeight: "600" },
          5: { fontSize: "1rem", fontWeight: "600" },
          6: { fontSize: "0.875rem", fontWeight: "600" },
        };

        return (
          <div>
            {isEditing && !readonly ? (
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    value={localContent.text || ""}
                    onChange={(e) => {
                      const newContent = {
                        ...localContent,
                        text: e.target.value,
                      };
                      handleContentChange(newContent);
                    }}
                    onBlur={handleSave}
                    placeholder="Enter heading..."
                    className="text-xl font-semibold border-0 shadow-none focus-visible:ring-0 bg-transparent"
                    style={{
                      fontSize:
                        headingStyles[level as keyof typeof headingStyles]
                          .fontSize,
                      fontWeight:
                        headingStyles[level as keyof typeof headingStyles]
                          .fontWeight,
                    }}
                  />
                  <Select
                    value={level.toString()}
                    onValueChange={(value) => {
                      const newContent = {
                        ...localContent,
                        level: parseInt(value),
                      };
                      handleContentChange(newContent);
                    }}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5, 6].map((level) => (
                        <SelectItem key={level} value={level.toString()}>
                          H{level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ) : (
              React.createElement(
                `h${level}`,
                {
                  className: `cursor-text hover:bg-gray-50 p-4 rounded transition-colors font-semibold`,
                  onClick: () => !readonly && setIsEditing(true),
                  style: {
                    textAlign: block.style?.textAlign || "left",
                    color: block.style?.textColor,
                    backgroundColor: block.style?.backgroundColor,
                    ...headingStyles[level as keyof typeof headingStyles],
                  },
                },
                localContent.text || (
                  <span className="text-gray-400 italic font-normal">
                    Click to add heading...
                  </span>
                )
              )
            )}
          </div>
        );

      case "list":
        return (
          <div className="space-y-2">
            {isEditing && !readonly ? (
              <div className="space-y-2">
                {(localContent.items || [""]).map(
                  (item: string, index: number) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={item}
                        onChange={(e) => {
                          const newItems = [...(localContent.items || [])];
                          newItems[index] = e.target.value;
                          const newContent = {
                            ...localContent,
                            items: newItems,
                          };
                          handleContentChange(newContent);
                        }}
                        placeholder={`List item ${index + 1}...`}
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const newItems = (localContent.items || []).filter(
                            (_: any, i: number) => i !== index
                          );
                          const newContent = {
                            ...localContent,
                            items: newItems,
                          };
                          handleContentChange(newContent);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const newItems = [...(localContent.items || []), ""];
                    const newContent = { ...localContent, items: newItems };
                    handleContentChange(newContent);
                  }}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Item
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleSave}
                  className="ml-2"
                >
                  Done
                </Button>
              </div>
            ) : (
              <ul
                className="list-disc list-inside space-y-1 cursor-text hover:bg-gray-50 p-4 rounded transition-colors"
                onClick={() => !readonly && setIsEditing(true)}
                style={{
                  color: block.style?.textColor,
                  backgroundColor: block.style?.backgroundColor,
                }}
              >
                {(localContent.items || []).length > 0 ? (
                  (localContent.items || []).map(
                    (item: string, index: number) => (
                      <li key={index} className="leading-relaxed">
                        {item || `List item ${index + 1}`}
                      </li>
                    )
                  )
                ) : (
                  <li className="text-gray-400 italic">
                    Click to add list items...
                  </li>
                )}
              </ul>
            )}
          </div>
        );

      case "image":
        return (
          <div className="space-y-3">
            {localContent.src ? (
              <div className="relative group">
                <img
                  src={localContent.src}
                  alt={localContent.alt || ""}
                  className="max-w-full h-auto rounded-lg shadow-sm border"
                  style={{
                    width: "100%",
                    maxHeight: "400px",
                    objectFit: "contain",
                  }}
                />
                {!readonly && (
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => setShowSettings(!showSettings)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-gray-400 cursor-pointer transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id={`upload-${block.id}`}
                />
                <label
                  htmlFor={`upload-${block.id}`}
                  className="cursor-pointer"
                >
                  <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">
                    Upload Image
                  </h3>
                  <p className="text-gray-500">
                    Click to select or drag and drop
                  </p>
                  <p className="text-xs text-gray-400 mt-2">
                    Supports: JPG, PNG, GIF, WebP
                  </p>
                </label>
              </div>
            )}

            {showSettings && !readonly && localContent.src && (
              <div className="space-y-2 p-3 bg-gray-50 rounded">
                <Input
                  placeholder="Alt text (for accessibility)"
                  value={localContent.alt || ""}
                  onChange={(e) => {
                    const newContent = { ...localContent, alt: e.target.value };
                    handleContentChange(newContent);
                  }}
                />
                <Input
                  placeholder="Caption (optional)"
                  value={localContent.caption || ""}
                  onChange={(e) => {
                    const newContent = {
                      ...localContent,
                      caption: e.target.value,
                    };
                    handleContentChange(newContent);
                  }}
                />
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      document.getElementById(`upload-${block.id}`)?.click();
                    }}
                  >
                    Replace Image
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const newContent = { src: "", alt: "", caption: "" };
                      handleContentChange(newContent);
                    }}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            )}
          </div>
        );

      case "columns":
        const columnCount = block.layout?.columns || 2;
        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Badge variant="outline" className="gap-1">
                <Columns className="h-3 w-3" />
                {columnCount} Columns
              </Badge>
              {!readonly && (
                <Select
                  value={columnCount.toString()}
                  onValueChange={(value) => {
                    const newColumnCount = parseInt(value);
                    const children = Array.from(
                      { length: newColumnCount },
                      (_, i) =>
                        block.layout?.children?.[i] || {
                          id: `col-${Date.now()}-${i}`,
                          type: "text" as const,
                          content: { text: `Column ${i + 1} content...` },
                        }
                    );
                    onUpdate({
                      layout: { columns: newColumnCount, children },
                    });
                  }}
                >
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                    <SelectItem value="4">4</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>

            <div
              className="grid gap-4"
              style={{
                gridTemplateColumns: `repeat(${columnCount}, 1fr)`,
              }}
            >
              {(block.layout?.children || []).map((childBlock, index) => (
                <div
                  key={childBlock.id}
                  className="border border-gray-200 rounded-lg p-4 bg-gray-50/50 min-h-[120px]"
                >
                  <div className="mb-2 flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500">
                      Column {index + 1}
                    </span>
                    {!readonly && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => {
                          const newChildren = (
                            block.layout?.children || []
                          ).filter((_, i) => i !== index);
                          if (newChildren.length > 0) {
                            onUpdate({
                              layout: {
                                ...block.layout,
                                children: newChildren,
                                columns: newChildren.length,
                              },
                            });
                          }
                        }}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  <SortableBlock
                    block={childBlock}
                    onUpdate={(updates) => {
                      const newChildren = [...(block.layout?.children || [])];
                      newChildren[index] = { ...childBlock, ...updates };
                      onUpdate({
                        layout: { ...block.layout, children: newChildren },
                      });
                    }}
                    onDelete={() => {}}
                    onDuplicate={() => {}}
                    readonly={readonly}
                  />
                </div>
              ))}
            </div>
          </div>
        );

      case "divider":
        return (
          <div className="py-4">
            <hr
              className="border-gray-300"
              style={{ borderColor: block.style?.textColor }}
            />
          </div>
        );

      case "spacer":
        return (
          <div
            className="bg-gray-50 border-2 border-dashed border-gray-300 rounded flex items-center justify-center text-gray-400"
            style={{ height: localContent.height || "60px" }}
          >
            <span className="text-sm">
              Spacer - {localContent.height || "60px"}
            </span>
          </div>
        );

      default:
        return (
          <div className="p-6 border border-gray-200 rounded bg-gray-50 text-center">
            <Blocks className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-500">{block.type} block content</p>
          </div>
        );
    }
  };

  return (
    <div ref={setNodeRef} style={style} className="group relative mb-4">
      <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-transparent hover:border-l-purple-500">
        <CardContent className="p-0">
          {/* Block Controls */}
          {!readonly && (
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity z-10 bg-white rounded-lg shadow-lg border p-1">
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <Settings className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0"
                  onClick={onDuplicate}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0 text-red-500 hover:text-red-700"
                  onClick={onDelete}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
                <div
                  {...attributes}
                  {...listeners}
                  className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
                >
                  <GripVertical className="h-3 w-3 text-gray-400" />
                </div>
              </div>
            </div>
          )}

          {/* Block Type Badge */}
          <div className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <Badge variant="secondary" className="text-xs">
              {block.type}
            </Badge>
          </div>

          {/* Block Content */}
          <div
            className="p-4 pt-8"
            style={{
              backgroundColor: block.style?.backgroundColor,
              padding: block.style?.padding || "1rem 1rem 1rem 1rem",
              margin: block.style?.margin,
            }}
          >
            {renderBlockContent()}
          </div>

          {/* Block Settings Panel */}
          {showSettings && !readonly && (
            <div className="mx-4 mb-4 p-4 bg-purple-50 rounded-lg border-t">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Block Settings
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block mb-1 text-sm font-medium">
                    Background
                  </label>
                  <Input
                    type="color"
                    value={block.style?.backgroundColor || "#ffffff"}
                    onChange={(e) =>
                      onUpdate({
                        style: {
                          ...block.style,
                          backgroundColor: e.target.value,
                        },
                      })
                    }
                    className="h-8 w-full"
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium">
                    Text Color
                  </label>
                  <Input
                    type="color"
                    value={block.style?.textColor || "#000000"}
                    onChange={(e) =>
                      onUpdate({
                        style: { ...block.style, textColor: e.target.value },
                      })
                    }
                    className="h-8 w-full"
                  />
                </div>
                <div className="col-span-2">
                  <label className="block mb-1 text-sm font-medium">
                    Text Alignment
                  </label>
                  <div className="flex gap-1">
                    {[
                      { value: "left", icon: AlignLeft },
                      { value: "center", icon: AlignCenter },
                      { value: "right", icon: AlignRight },
                    ].map(({ value, icon: Icon }) => (
                      <Button
                        key={value}
                        size="sm"
                        variant={
                          (block.style?.textAlign || "left") === value
                            ? "default"
                            : "outline"
                        }
                        onClick={() =>
                          onUpdate({
                            style: {
                              ...block.style,
                              textAlign: value as "left" | "center" | "right",
                            },
                          })
                        }
                        className="flex-1"
                      >
                        <Icon className="h-4 w-4" />
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Block Palette Component
function BlockPalette({
  onAddBlock,
}: {
  onAddBlock: (type: AdvancedBlock["type"]) => void;
}) {
  const blockTypes: {
    type: AdvancedBlock["type"];
    icon: React.ElementType;
    label: string;
    description: string;
  }[] = [
    {
      type: "text",
      icon: Type,
      label: "Text Block",
      description: "Rich text paragraph",
    },
    {
      type: "heading",
      icon: Heading2,
      label: "Heading",
      description: "Section title",
    },
    {
      type: "list",
      icon: List,
      label: "List",
      description: "Bullet or numbered list",
    },
    {
      type: "image",
      icon: ImageIcon,
      label: "Image",
      description: "Upload image",
    },
    {
      type: "columns",
      icon: Columns,
      label: "Columns",
      description: "Multi-column layout",
    },
    {
      type: "divider",
      icon: Layout,
      label: "Divider",
      description: "Section separator",
    },
    {
      type: "spacer",
      icon: Layout,
      label: "Spacer",
      description: "Empty space",
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-2">
      {blockTypes.map(({ type, icon: Icon, label, description }) => (
        <Button
          key={type}
          variant="outline"
          className="h-auto p-3 flex flex-col items-center gap-2 hover:bg-purple-50 hover:border-purple-300 transition-colors"
          onClick={() => onAddBlock(type)}
        >
          <Icon className="h-5 w-5 text-purple-600" />
          <div className="text-center">
            <div className="font-medium text-sm">{label}</div>
            <div className="text-xs text-gray-500">{description}</div>
          </div>
        </Button>
      ))}
    </div>
  );
}

// Main Advanced Block Editor Component
export function AdvancedBlockEditor({
  blocks: initialBlocks,
  onBlocksChange,
  sectionId,
  onAddImage,
  readonly = false,
  existingContent,
}: AdvancedBlockEditorProps) {
  const [blocks, setBlocks] = useState<AdvancedBlock[]>([]);
  const [activeBlockId, setActiveBlockId] = useState<string | null>(null);
  const [showPalette, setShowPalette] = useState(false);
  const { toast } = useToast();
  const { documentTheme } = useDocumentContext();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Initialize blocks from existing content
  useEffect(() => {
    if (existingContent && blocks.length === 0) {
      const contentBlocks = convertContentToBlocks(existingContent);
      setBlocks(contentBlocks);
    } else if (initialBlocks.length > 0) {
      setBlocks(initialBlocks);
    }
  }, [existingContent, initialBlocks]);

  // Auto-save functionality
  useEffect(() => {
    if (blocks.length > 0) {
      onBlocksChange(blocks);
    }
  }, [blocks, onBlocksChange]);

  // Convert content to blocks helper
  const convertContentToBlocks = (content: string): AdvancedBlock[] => {
    if (!content) return [];

    const lines = content.split("\n").filter((line) => line.trim());
    const blocks: AdvancedBlock[] = [];

    lines.forEach((line, index) => {
      if (line.startsWith("# ")) {
        blocks.push({
          id: `block-${Date.now()}-${index}`,
          type: "heading",
          content: { text: line.replace("# ", ""), level: 1 },
        });
      } else if (line.startsWith("## ")) {
        blocks.push({
          id: `block-${Date.now()}-${index}`,
          type: "heading",
          content: { text: line.replace("## ", ""), level: 2 },
        });
      } else if (line.startsWith("### ")) {
        blocks.push({
          id: `block-${Date.now()}-${index}`,
          type: "heading",
          content: { text: line.replace("### ", ""), level: 3 },
        });
      } else if (line.startsWith("- ") || line.startsWith("* ")) {
        // Check if we can combine with previous list block
        const lastBlock = blocks[blocks.length - 1];
        if (lastBlock && lastBlock.type === "list") {
          lastBlock.content.items.push(line.replace(/^[- *] /, ""));
        } else {
          blocks.push({
            id: `block-${Date.now()}-${index}`,
            type: "list",
            content: { items: [line.replace(/^[- *] /, "")] },
          });
        }
      } else if (line.trim()) {
        blocks.push({
          id: `block-${Date.now()}-${index}`,
          type: "text",
          content: { text: line },
        });
      }
    });

    return blocks;
  };

  const addBlock = (type: AdvancedBlock["type"]) => {
    const newBlock: AdvancedBlock = {
      id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
      type,
      content: getDefaultContent(type),
      style: getDefaultStyle(type),
      layout: type === "columns" ? { columns: 2, children: [] } : undefined,
    };

    const newBlocks = [...blocks, newBlock];
    setBlocks(newBlocks);
    setShowPalette(false);

    toast({
      title: "Block Added & Saved",
      description: `${type} block has been added and saved automatically.`,
    });
  };

  const updateBlock = (blockId: string, updates: Partial<AdvancedBlock>) => {
    const updatedBlocks = blocks.map((block) =>
      block.id === blockId ? { ...block, ...updates } : block
    );
    setBlocks(updatedBlocks);

    toast({
      title: "Changes Saved",
      description: "Your changes have been saved automatically.",
      duration: 1500,
    });
  };

  const deleteBlock = (blockId: string) => {
    const newBlocks = blocks.filter((block) => block.id !== blockId);
    setBlocks(newBlocks);

    toast({
      title: "Block Deleted",
      description: "The block has been removed and changes saved.",
    });
  };

  const duplicateBlock = (blockId: string) => {
    const blockToDuplicate = blocks.find((block) => block.id === blockId);

    if (blockToDuplicate) {
      const duplicatedBlock: AdvancedBlock = {
        ...blockToDuplicate,
        id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
      };

      const blockIndex = blocks.findIndex((block) => block.id === blockId);
      const newBlocks = [...blocks];
      newBlocks.splice(blockIndex + 1, 0, duplicatedBlock);

      setBlocks(newBlocks);

      toast({
        title: "Block Duplicated & Saved",
        description: "A copy of the block has been created and saved.",
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = blocks.findIndex((block) => block.id === active.id);
      const newIndex = blocks.findIndex((block) => block.id === over?.id);

      const newBlocks = arrayMove(blocks, oldIndex, newIndex);
      setBlocks(newBlocks);

      toast({
        title: "Blocks Reordered",
        description: "Block order has been updated and saved.",
        duration: 1500,
      });
    }

    setActiveBlockId(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white border rounded-lg shadow-sm">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Sparkles className="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Advanced Block Editor
            </h3>
            <p className="text-sm text-gray-600">
              Professional drag & drop editor • Auto-save enabled • Rich content
              blocks
            </p>
          </div>
        </div>

        {!readonly && (
          <DropdownMenu open={showPalette} onOpenChange={setShowPalette}>
            <DropdownMenuTrigger asChild>
              <Button className="gap-2 bg-purple-600 hover:bg-purple-700">
                <Plus className="h-4 w-4" />
                Add Block
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-80" align="end">
              <div className="p-3 space-y-3">
                <h4 className="font-semibold text-purple-900">
                  Add Content Block
                </h4>
                <BlockPalette onAddBlock={addBlock} />
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Content Blocks */}
      {blocks.length > 0 ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={(event) => setActiveBlockId(event.active.id as string)}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={blocks.map((b) => b.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {blocks.map((block) => (
                <SortableBlock
                  key={block.id}
                  block={block}
                  onUpdate={(updates) => updateBlock(block.id, updates)}
                  onDelete={() => deleteBlock(block.id)}
                  onDuplicate={() => duplicateBlock(block.id)}
                  readonly={readonly}
                />
              ))}
            </div>
          </SortableContext>

          <DragOverlay>
            {activeBlockId ? (
              <div className="opacity-75 transform rotate-2 shadow-2xl">
                <SortableBlock
                  block={blocks.find((b) => b.id === activeBlockId)!}
                  onUpdate={() => {}}
                  onDelete={() => {}}
                  onDuplicate={() => {}}
                  readonly={true}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      ) : (
        <Card className="border-2 border-dashed border-purple-300 bg-purple-50">
          <CardContent className="p-12 text-center">
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-purple-100 rounded-full">
                <Blocks className="h-12 w-12 text-purple-600" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-purple-900 mb-2">
                  Start Building Amazing Content
                </h3>
                <p className="text-purple-700 mb-4">
                  Add your first content block to create professional layouts
                  with advanced features.
                </p>
              </div>
              {!readonly && (
                <Button
                  onClick={() => setShowPalette(true)}
                  className="gap-2 bg-purple-600 hover:bg-purple-700 px-6 py-3 text-lg"
                >
                  <Plus className="h-5 w-5" />
                  Add Your First Block
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Helper functions
function getDefaultContent(type: AdvancedBlock["type"]) {
  switch (type) {
    case "text":
      return { text: "" };
    case "heading":
      return { text: "", level: 2 };
    case "list":
      return { items: [""] };
    case "image":
      return { src: "", alt: "", caption: "" };
    case "columns":
      return {};
    case "spacer":
      return { height: "60px" };
    default:
      return {};
  }
}

function getDefaultStyle(type: AdvancedBlock["type"]) {
  switch (type) {
    case "heading":
      return {
        textAlign: "left" as const,
        fontWeight: "600",
      };
    case "text":
      return {
        textAlign: "left" as const,
        fontSize: "1rem",
        fontWeight: "400",
      };
    default:
      return {};
  }
}

export default AdvancedBlockEditor;
