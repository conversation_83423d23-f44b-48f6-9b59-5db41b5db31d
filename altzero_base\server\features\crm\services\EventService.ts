import { supabase } from '../../../base/common/apps/supabase';

export interface Event {
  id?: string;
  organisation_id: string;
  title: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  geo?: any;
  note?: string;
  contact_id?: string;
  company_id?: string;
  owner_id?: string;
  related_project_id?: string;
  related_opportunity_id?: string;
  custom_fields?: any;
  recurrence_rule?: string;
  created_at?: string;
  updated_at?: string;
}

export interface EventFilters {
  page: number;
  limit: number;
  startDate?: string;
  endDate?: string;
  userId?: string;
}

export class EventService {
  
  /**
   * Get user's organization IDs from organisation_members table
   */
  private async getUserOrganisationIds(userId: string): Promise<string[]> {
    const { data, error } = await supabase
      .from('organisation_members')
      .select('organisation_id')
      .eq('user_id', userId);
    
    if (error) {
      console.error('Error fetching user organizations:', error);
      return [];
    }
    
    return data.map(row => row.organisation_id);
  }

  /**
   * Get events with pagination and filtering
   */
  async getEvents(filters: EventFilters) {
    try {
      if (!filters.userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(filters.userId);
      if (organisationIds.length === 0) {
        return { data: [], total: 0, page: filters.page, limit: filters.limit };
      }

      let query = supabase
        .from('crm_events')
        .select(`
          *,
          crm_contacts!crm_events_contact_id_fkey (
            id,
            full_name,
            email
          ),
          crm_companies!crm_events_company_id_fkey (
            id,
            name
          )
        `, { count: 'exact' })
        .in('organisation_id', organisationIds);

      // Apply date range filter
      if (filters.startDate) {
        query = query.gte('start_time', filters.startDate);
      }
      if (filters.endDate) {
        query = query.lte('start_time', filters.endDate);
      }

      // Apply pagination
      const offset = (filters.page - 1) * filters.limit;
      query = query
        .order('start_time', { ascending: true })
        .range(offset, offset + filters.limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching events:', error);
        throw error;
      }

      return {
        data: data || [],
        total: count || 0,
        page: filters.page,
        limit: filters.limit
      };
    } catch (error) {
      console.error('EventService.getEvents error:', error);
      throw error;
    }
  }

  /**
   * Get a single event by ID
   */
  async getEventById(eventId: string, userId: string): Promise<Event | null> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return null;
      }

      const { data, error } = await supabase
        .from('crm_events')
        .select(`
          *,
          crm_contacts!crm_events_contact_id_fkey (
            id,
            full_name,
            email,
            phone
          ),
          crm_companies!crm_events_company_id_fkey (
            id,
            name,
            email,
            phone
          )
        `)
        .eq('id', eventId)
        .in('organisation_id', organisationIds)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        console.error('Error fetching event:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('EventService.getEventById error:', error);
      throw error;
    }
  }

  /**
   * Create a new event
   */
  async createEvent(eventData: Omit<Event, 'id' | 'created_at' | 'updated_at'>, userId: string): Promise<Event> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        throw new Error('User is not a member of any organization');
      }

      // Use the first organization if not specified
      if (!eventData.organisation_id) {
        eventData.organisation_id = organisationIds[0];
      }

      // Verify user has access to the specified organization
      if (!organisationIds.includes(eventData.organisation_id)) {
        throw new Error('User does not have access to the specified organization');
      }

      // Set owner_id to current user if not specified
      if (!eventData.owner_id) {
        eventData.owner_id = userId;
      }

      const { data, error } = await supabase
        .from('crm_events')
        .insert([eventData])
        .select()
        .single();

      if (error) {
        console.error('Error creating event:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('EventService.createEvent error:', error);
      throw error;
    }
  }

  /**
   * Update an existing event
   */
  async updateEvent(eventId: string, eventData: Partial<Event>, userId: string): Promise<Event | null> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return null;
      }

      // Remove fields that shouldn't be updated
      const { id, created_at, ...updateData } = eventData;
      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('crm_events')
        .update(updateData)
        .eq('id', eventId)
        .in('organisation_id', organisationIds)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        console.error('Error updating event:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('EventService.updateEvent error:', error);
      throw error;
    }
  }

  /**
   * Delete an event
   */
  async deleteEvent(eventId: string, userId: string): Promise<boolean> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return false;
      }

      const { error } = await supabase
        .from('crm_events')
        .delete()
        .eq('id', eventId)
        .in('organisation_id', organisationIds);

      if (error) {
        console.error('Error deleting event:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('EventService.deleteEvent error:', error);
      throw error;
    }
  }

  /**
   * Get upcoming events
   */
  async getUpcomingEvents(userId: string, limit: number = 10): Promise<Event[]> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return [];
      }

      const now = new Date().toISOString();

      const { data, error } = await supabase
        .from('crm_events')
        .select(`
          *,
          crm_contacts!crm_events_contact_id_fkey (
            id,
            full_name
          ),
          crm_companies!crm_events_company_id_fkey (
            id,
            name
          )
        `)
        .in('organisation_id', organisationIds)
        .gte('start_time', now)
        .order('start_time', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching upcoming events:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('EventService.getUpcomingEvents error:', error);
      throw error;
    }
  }

  /**
   * Get events for calendar view
   */
  async getCalendarEvents(userId: string, startDate: string, endDate: string): Promise<Event[]> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return [];
      }

      const { data, error } = await supabase
        .from('crm_events')
        .select(`
          *,
          crm_contacts!crm_events_contact_id_fkey (
            id,
            full_name
          ),
          crm_companies!crm_events_company_id_fkey (
            id,
            name
          )
        `)
        .in('organisation_id', organisationIds)
        .gte('start_time', startDate)
        .lte('start_time', endDate)
        .order('start_time', { ascending: true });

      if (error) {
        console.error('Error fetching calendar events:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('EventService.getCalendarEvents error:', error);
      throw error;
    }
  }
}
