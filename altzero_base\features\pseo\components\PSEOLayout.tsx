import React, { ReactNode, useEffect, useState } from 'react';
import { useUser } from '../../../base/contextapi/UserContext';
import { supabase } from '../../../base/utils/supabaseClient';
import PSEONavigation from './PSEOSidePanel';

interface PSEOLayoutProps {
  children: ReactNode;
}

const PSEOLayout: React.FC<PSEOLayoutProps> = ({ children }) => {
  const { user } = useUser();

  // State for navigation data
  const [stats, setStats] = useState({
    totalClients: 0,
    totalWebsites: 0,
    totalAudits: 0,
    totalAnalysisJobs: 0,
    loading: true
  });

  const [fullSiteAnalysisResults, setFullSiteAnalysisResults] = useState<any[]>([]);
  const [contentOpportunities, setContentOpportunities] = useState<any[]>([]);
  const [keywordResearch, setKeywordResearch] = useState<any[]>([]);

  useEffect(() => {
    if (user?.id) {
      loadNavigationData();
    }
  }, [user?.id]);

  const loadNavigationData = async () => {
    if (!user?.id) return;

    try {
      // Load stats for navigation
      const [clientsResult, websitesResult, auditsResult, analysisResult] = await Promise.all([
        supabase.from('pseo_clients').select('id', { count: 'exact' }).eq('user_id', user.id),
        supabase.from('pseo_websites').select('id', { count: 'exact' }).eq('user_id', user.id),
        supabase.from('pseo_audits').select('id', { count: 'exact' }).eq('user_id', user.id),
        supabase.from('pseo_analysis_jobs').select('id', { count: 'exact' }).eq('user_id', user.id)
      ]);

      setStats({
        totalClients: clientsResult.count || 0,
        totalWebsites: websitesResult.count || 0,
        totalAudits: auditsResult.count || 0,
        totalAnalysisJobs: analysisResult.count || 0,
        loading: false
      });

      // Load other navigation data
      await Promise.all([
        loadFullSiteAnalysisResults(),
        loadContentOpportunities(),
        loadKeywordResearch()
      ]);

    } catch (error) {
      console.error('Error loading navigation data:', error);
      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  const loadFullSiteAnalysisResults = async () => {
    try {
      const { data, error } = await supabase
        .from('pseo_analysis_jobs')
        .select('*')
        .eq('user_id', user!.id)
        .eq('status', 'completed')
        .order('completed_at', { ascending: false })
        .limit(10);

      if (!error && data) {
        setFullSiteAnalysisResults(data);
      }
    } catch (error) {
      console.error('Error loading analysis results:', error);
    }
  };

  const loadContentOpportunities = async () => {
    try {
      const { data, error } = await supabase
        .from('pseo_content_opportunities')
        .select('*')
        .eq('user_id', user!.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!error && data) {
        setContentOpportunities(data);
      }
    } catch (error) {
      console.error('Error loading content opportunities:', error);
    }
  };

  const loadKeywordResearch = async () => {
    try {
      const { data, error } = await supabase
        .from('pseo_keyword_research')
        .select('*')
        .eq('user_id', user!.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!error && data) {
        setKeywordResearch(data);
      }
    } catch (error) {
      console.error('Error loading keyword research:', error);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg border-r border-gray-200 flex flex-col">
        <PSEONavigation
          stats={stats}
          fullSiteAnalysisResults={fullSiteAnalysisResults}
          contentOpportunities={contentOpportunities}
          keywordResearch={keywordResearch}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Content Area */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default PSEOLayout;
