# AltZero

Full-stack application with React frontend and Express backend.

## Setup

Install dependencies (both frontend and backend):

```bash
npm install
```

## Development

Run both frontend and backend in development mode:

```bash
npm run dev
```

Or run them separately:

```bash
# Frontend only
npm run dev:frontend

# Backend only
npm run dev:server
```

- Frontend runs on: http://localhost:5173
- Backend runs on: http://localhost:3000

## Building for Production

Build both frontend and backend:

```bash
npm run build
```

## Production Start

Start both frontend and backend in production mode:

```bash
npm start
```

## Available Scripts

- `npm run dev` - Run both frontend and backend in development mode
- `npm run dev:frontend` - Run frontend in development mode
- `npm run dev:server` - Run backend in development mode
- `npm run build` - Build both frontend and backend for production
- `npm run build:frontend` - Build frontend for production
- `npm run build:server` - Build backend for production
- `npm run preview` - Preview frontend production build
- `npm run start:frontend` - Start frontend in production mode
- `npm run start:server` - Start backend in production mode
- `npm run start` - Start both frontend and backend in production mode
- `npm run lint:frontend` - Run ESLint on frontend code
- `npm run lint:server` - Run ESLint on backend code 