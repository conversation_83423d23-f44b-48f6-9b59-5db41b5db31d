import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, Plus, Search, Loader2, Edit, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { Badge } from "../../../../base/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../../../base/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Al<PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "../../../../base/components/ui/alert-dialog";
import { Label } from "../../../../base/components/ui/label";
import { useUser } from "../../../../base/contextapi/UserContext";
import { useToast } from "../../../../base/hooks/use-toast";
import ScopingAILayout from "../components/ScopingAILayout";
import {
  fetchClients,
  createClient,
  updateClient,
  deleteClient,
  type Client,
  type ClientFormData,
} from "../services/clientService";

export default function KnowledgeBaseClients() {
  const { user } = useUser();
  const { toast } = useToast();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [clientData, setClientData] = useState<ClientFormData>({
    name: "",
    contactPerson: "",
    email: "",
    phone: "",
    industry: "",
  });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [confirmDeleteClientId, setConfirmDeleteClientId] = useState<
    string | null
  >(null);

  // Fetch clients on component mount
  useEffect(() => {
    if (user) {
      loadClients();
    }
  }, [user]);

  // Function to fetch clients from Supabase
  const loadClients = async () => {
    setLoading(true);
    setError(null);

    try {
      const clientsData = await fetchClients();
      setClients(clientsData);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error fetching clients:", err);
      toast({
        title: "Error",
        description: "Failed to load clients",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Reset client form
  const resetClientForm = () => {
    setClientData({
      name: "",
      contactPerson: "",
      email: "",
      phone: "",
      industry: "",
    });
    setEditingClient(null);
  };

  // Open dialog for editing
  const handleEdit = (client: Client) => {
    setEditingClient(client);
    setClientData({
      name: client.name,
      contactPerson: client.contactPerson,
      email: client.email,
      phone: client.phone,
      industry: client.industry,
    });
    setDialogOpen(true);
  };

  // Open dialog for creating new client
  const handleAddNew = () => {
    resetClientForm();
    setDialogOpen(true);
  };

  // Prompt for deleting a client
  const handleDeleteClick = (clientId: string) => {
    setConfirmDeleteClientId(clientId);
  };

  // Delete a client
  const handleDeleteConfirm = async () => {
    if (!confirmDeleteClientId) return;

    setLoading(true);
    setError(null);

    try {
      await deleteClient(confirmDeleteClientId);
      setClients((prev) =>
        prev.filter((client) => client.id !== confirmDeleteClientId)
      );
      toast({
        title: "Client deleted",
        description: "Client has been removed successfully",
      });
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete client";
      setError(errorMessage);
      console.error("Error deleting client:", err);
      toast({
        title: "Delete failed",
        description: "There was an error deleting the client",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setConfirmDeleteClientId(null);
    }
  };

  // Handle client form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setClientData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle client save (create or update)
  const handleClientSave = async () => {
    setLoading(true);
    setError(null);

    try {
      if (editingClient) {
        // Update existing client
        await updateClient(editingClient.id, clientData);
        toast({
          title: "Client updated",
          description: "Client information has been updated successfully",
        });
      } else {
        // Create new client
        await createClient(clientData);
        toast({
          title: "Client added",
          description: "New client has been added successfully",
        });
      }

      // Refresh the clients list
      await loadClients();

      // Reset form and close dialog
      resetClientForm();
      setDialogOpen(false);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to save client";
      setError(errorMessage);
      console.error("Error saving client:", err);
      toast({
        title: "Save failed",
        description: "There was an error saving the client",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter clients based on search query
  const filteredClients = clients.filter(
    (client) =>
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.industry.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <ScopingAILayout>
      <div className="container mx-auto px-6 py-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Link
                to="/scopingai/knowledge-base"
                className="flex items-center text-muted-foreground hover:text-foreground mb-4"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back to Knowledge Base
              </Link>
              <h1 className="text-3xl font-bold">Client Contacts</h1>
              <p className="text-muted-foreground mt-1">
                Manage client contact information for scoping projects
              </p>
            </div>
            <Button onClick={handleAddNew} className="gap-2">
              <Plus size={16} />
              Add Contact
            </Button>
          </div>

          {error && (
            <div className="p-4 bg-destructive/10 border border-destructive/30 rounded-md text-destructive">
              <p>{error}</p>
            </div>
          )}

          <div className="flex items-center mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search clients..."
                className="w-full bg-background pl-8 focus-visible:ring-primary"
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setSearchQuery(e.target.value)
                }
              />
            </div>
          </div>

          {loading && clients.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="mt-4 text-muted-foreground">Loading clients...</p>
            </div>
          ) : clients.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <p className="text-muted-foreground mb-4">
                  No clients found. Add your first client!
                </p>
                <Button
                  onClick={handleAddNew}
                  variant="outline"
                  className="gap-2"
                >
                  <Plus size={16} />
                  Add Client
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="rounded-md border">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="[&_tr]:border-b">
                    <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        Client Name
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium max-md:hidden">
                        Industry
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        Contact
                      </th>
                      <th className="h-12 px-4 text-left align-middle font-medium">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="[&_tr:last-child]:border-0">
                    {filteredClients.map((client) => (
                      <tr
                        key={client.id}
                        className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                      >
                        <td className="p-4 align-middle font-medium">
                          {client.name}
                        </td>
                        <td className="p-4 align-middle max-md:hidden">
                          {client.industry ? (
                            <Badge variant="outline">{client.industry}</Badge>
                          ) : (
                            "—"
                          )}
                        </td>
                        <td className="p-4 align-middle">
                          <div>
                            {client.contactPerson || "—"}
                            {client.email && (
                              <div className="text-xs text-muted-foreground">
                                {client.email}
                              </div>
                            )}
                            {client.phone && (
                              <div className="text-xs text-muted-foreground">
                                {client.phone}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="p-4 align-middle">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 px-2 lg:px-3"
                              onClick={() => handleEdit(client)}
                            >
                              Edit
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 px-2 lg:px-3 text-destructive border-destructive/30 hover:bg-destructive/10"
                                >
                                  Delete
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Delete Client
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete{" "}
                                    {client.name}? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteClick(client.id)}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Client Dialog Form */}
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingClient ? "Edit Client" : "Add New Client"}
                </DialogTitle>
                <DialogDescription>
                  {editingClient
                    ? "Update the client information below."
                    : "Enter the client details to add them to your database."}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Client Name*</Label>
                  <Input
                    id="name"
                    name="name"
                    value={clientData.name}
                    onChange={handleInputChange}
                    placeholder="Enter client name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPerson">Contact Person</Label>
                  <Input
                    id="contactPerson"
                    name="contactPerson"
                    value={clientData.contactPerson}
                    onChange={handleInputChange}
                    placeholder="Enter contact person name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={clientData.email}
                    onChange={handleInputChange}
                    placeholder="Enter contact email"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={clientData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter contact phone"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="industry">Industry</Label>
                  <Input
                    id="industry"
                    name="industry"
                    value={clientData.industry}
                    onChange={handleInputChange}
                    placeholder="Enter client industry"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleClientSave} disabled={!clientData.name}>
                  {editingClient ? "Update Client" : "Add Client"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation */}
          <AlertDialog
            open={!!confirmDeleteClientId}
            onOpenChange={(open: boolean) =>
              !open && setConfirmDeleteClientId(null)
            }
          >
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Client</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this client? This action
                  cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteConfirm}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
    </ScopingAILayout>
  );
}
