import { supabase } from "../lib/supabase";

// Enhanced client interface that supports both CRM contacts and ScopingAI clients
export interface EnhancedClient {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  company: string;
  industry: string;
  source: "crm" | "scopingai";
  organisationId: string;
  organisationName?: string; // Add organization name
  isLinked?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// API response interface
interface ApiResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    total: number;
    crmContacts: number;
    scopingaiClients: number;
  };
}

export class EnhancedClientService {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || "http://localhost:3001";
    this.apiKey = import.meta.env.VITE_API_KEY || "scopingai";
  }

  /**
   * Get current user ID from Supabase session
   */
  private async getCurrentUserId(): Promise<string> {
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting user:", userError);
      throw userError;
    }

    const userId = userData.user?.id;
    if (!userId) {
      throw new Error("User not authenticated");
    }

    return userId;
  }

  /**
   * Get user's organization ID
   */
  async getUserOrganization(): Promise<string | null> {
    try {
      const userId = await this.getCurrentUserId();

      const response = await fetch(
        `${this.baseUrl}/api/scopingai/user/organization`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "x-api-key": this.apiKey,
            "x-user-id": userId,
          },
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          return null; // User not in any organization
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data?.organisationId || null;
    } catch (error) {
      console.error("Error getting user organization:", error);
      return null;
    }
  }

  /**
   * Get ONLY CRM contacts from ALL user's organizations (NO ScopingAI clients)
   */
  async getOrganizationContacts(): Promise<EnhancedClient[]> {
    try {
      const userId = await this.getCurrentUserId();
      const organisationId = await this.getUserOrganization();

      if (!organisationId) {
        console.warn(
          "User is not a member of any organization, returning empty array"
        );
        return [];
      }

      console.log(
        `🔍 Fetching CRM contacts ONLY for all user's organizations - Primary org: ${organisationId}`
      );

      const response = await fetch(
        `${this.baseUrl}/api/scopingai/contacts/organization/${organisationId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "x-api-key": this.apiKey,
            "x-user-id": userId,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any[]> = await response.json();

      const allContacts = result.data.map(this.transformApiClient);

      // FILTER TO SHOW ONLY CRM CONTACTS (NO ScopingAI clients)
      const crmContactsOnly = allContacts.filter(
        (contact) => contact.source === "crm"
      );

      console.log(`✅ Loaded CRM contacts ONLY from all user organizations:`, {
        totalFromAPI: allContacts.length,
        crmContacts: crmContactsOnly.length,
        scopingaiClientsFiltered: allContacts.filter(
          (c) => c.source === "scopingai"
        ).length,
        finalContacts: crmContactsOnly.length,
      });

      return crmContactsOnly;
    } catch (error) {
      console.error("Error fetching organization contacts:", error);
      // Return empty array instead of fallback for organization contacts
      return [];
    }
  }

  /**
   * Get personal ScopingAI clients (fallback when no organization)
   */
  private async getPersonalScopingAiClients(): Promise<EnhancedClient[]> {
    try {
      const userId = await this.getCurrentUserId();

      const { data, error } = await supabase
        .from("scopingai_clients")
        .select("*")
        .eq("user_id", userId)
        .order("name");

      if (error) {
        console.error("Error fetching personal ScopingAI clients:", error);
        throw error;
      }

      return data.map((client: any) => ({
        id: client.id,
        name: client.name,
        contactPerson: client.contact_person || client.name,
        email: client.email || "",
        phone: client.phone || "",
        company: client.company || "",
        industry: client.industry || "",
        source: "scopingai" as const,
        organisationId: "", // No organization for personal clients
        isLinked: true,
        createdAt: new Date(client.created_at),
        updatedAt: new Date(client.updated_at),
      }));
    } catch (error) {
      console.error("Error fetching personal ScopingAI clients:", error);
      return [];
    }
  }

  // Note: CRM contacts are now directly available without linking

  /**
   * Convert CRM contact to dedicated ScopingAI client
   */
  async convertCrmContactToClient(crmContactId: string): Promise<string> {
    try {
      const userId = await this.getCurrentUserId();

      const response = await fetch(
        `${this.baseUrl}/api/scopingai/contacts/convert-crm/${crmContactId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-api-key": this.apiKey,
            "x-user-id": userId,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || "Failed to convert CRM contact");
      }

      const result = await response.json();
      console.log(
        `✅ Successfully converted CRM contact to ScopingAI client: ${result.data.newClientId}`
      );
      return result.data.newClientId;
    } catch (error) {
      console.error("Error converting CRM contact:", error);
      throw error;
    }
  }

  /**
   * Transform API client data to EnhancedClient interface
   */
  private transformApiClient(apiClient: any): EnhancedClient {
    return {
      id: apiClient.id,
      name: apiClient.name,
      contactPerson: apiClient.contactPerson,
      email: apiClient.email,
      phone: apiClient.phone,
      company: apiClient.company,
      industry: apiClient.industry,
      source: apiClient.source,
      organisationId: apiClient.organisationId,
      organisationName: apiClient.organisationName,
      isLinked: apiClient.isLinked,
      createdAt: new Date(apiClient.createdAt),
      updatedAt: new Date(apiClient.updatedAt),
    };
  }

  /**
   * Search contacts by name or company
   */
  searchContacts(contacts: EnhancedClient[], query: string): EnhancedClient[] {
    if (!query.trim()) {
      return contacts;
    }

    const searchTerm = query.toLowerCase();
    return contacts.filter(
      (contact) =>
        contact.name.toLowerCase().includes(searchTerm) ||
        contact.company.toLowerCase().includes(searchTerm) ||
        contact.contactPerson.toLowerCase().includes(searchTerm) ||
        contact.email.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Filter contacts by source
   */
  filterContactsBySource(
    contacts: EnhancedClient[],
    source?: "crm" | "scopingai"
  ): EnhancedClient[] {
    if (!source) {
      return contacts;
    }
    return contacts.filter((contact) => contact.source === source);
  }

  /**
   * Get contact statistics
   */
  getContactStats(contacts: EnhancedClient[]) {
    return {
      total: contacts.length,
      crmContacts: contacts.filter((c) => c.source === "crm").length,
      scopingaiClients: contacts.filter((c) => c.source === "scopingai").length,
    };
  }

  // Note: All CRM contacts are now automatically available

  /**
   * Get organization statistics
   */
  async getOrganizationStats(): Promise<any> {
    try {
      const userId = await this.getCurrentUserId();
      const organisationId = await this.getUserOrganization();

      if (!organisationId) {
        return null;
      }

      const response = await fetch(
        `${this.baseUrl}/api/scopingai/organization/${organisationId}/stats`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "x-api-key": this.apiKey,
            "x-user-id": userId,
          },
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("Error getting organization stats:", error);
      return null;
    }
  }

  /**
   * Sync organization data
   */
  async syncOrganizationData(): Promise<any> {
    try {
      const userId = await this.getCurrentUserId();
      const organisationId = await this.getUserOrganization();

      if (!organisationId) {
        throw new Error("User is not a member of any organization");
      }

      const response = await fetch(
        `${this.baseUrl}/api/scopingai/organization/${organisationId}/sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-api-key": this.apiKey,
            "x-user-id": userId,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.details || "Failed to sync organization data"
        );
      }

      const result = await response.json();
      console.log(`✅ Organization sync completed:`, result.data);
      return result.data;
    } catch (error) {
      console.error("Error syncing organization data:", error);
      throw error;
    }
  }
}

export const enhancedClientService = new EnhancedClientService();
