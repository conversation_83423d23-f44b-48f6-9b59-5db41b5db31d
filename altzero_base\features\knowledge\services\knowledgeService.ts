import {
  Document,
  ChatMessage,
  ChatRequest,
  ChatResponse,
  UploadRequest,
  UploadResponse,
  KnowledgeBaseStats,
  ChatSession,
  DocumentStatus,
} from "../types/knowledge";
import { API_URL, ENDPOINTS } from "../../../base/utils/constants";
import { KNOWLEDGE_ENDPOINTS, KNOWLEDGE_TOAST_MESSAGES } from "../utils/constants";
import { supabase } from "../../../base/utils/supabaseClient";

class KnowledgeService {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = API_URL;
    this.apiKey = import.meta.env.VITE_API_KEY || "";
  }

  // Get current user ID from Supabase session
  private async getCurrentUserId(): Promise<string | null> {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.warn("Failed to get Supabase session:", error);
        return null;
      }

      return session?.user?.id || null;
    } catch (error) {
      console.warn("Failed to get user ID from Supabase:", error);
      return null;
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const userId = await this.getCurrentUserId();

    const defaultHeaders: Record<string, string> = {
      "x-api-key": this.apiKey,
    };

    // Add user ID to headers if available
    if (userId) {
      defaultHeaders["x-user-id"] = userId;
    }

    // Only add Content-Type for non-FormData requests
    if (!(options.body instanceof FormData)) {
      defaultHeaders["Content-Type"] = "application/json";
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    // Add userId to request body for POST/PUT requests if not FormData
    if (
      userId &&
      (options.method === "POST" || options.method === "PUT") &&
      !(options.body instanceof FormData) &&
      options.body
    ) {
      try {
        const bodyData = JSON.parse(options.body as string);
        bodyData.userId = userId;
        config.body = JSON.stringify(bodyData);
      } catch (error) {
        // If body is not JSON, just continue
      }
    }

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Document Management with LlamaParser → Pinecone workflow
  async uploadDocuments(
    files: File[],
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required for document upload. Please log in."
      );
    }

    const formData = new FormData();

    files.forEach((file, index) => {
      formData.append("files", file);
    });

    // Add metadata including userId
    formData.append(
      "metadata",
      JSON.stringify({
        uploadedAt: new Date().toISOString(),
        source: "web_upload",
        userId: userId,
      })
    );

    // Add userId as a separate field
    formData.append("userId", userId);

    return this.makeRequest<UploadResponse>(KNOWLEDGE_ENDPOINTS.KNOWLEDGE_UPLOAD, {
      method: "POST",
      body: formData,
    });
  }

  // Search documents using Pinecone + LangGraph
  async searchDocuments(
    query: string,
    documentIds: string[] = [],
    options: {
      maxResults?: number;
      minRelevanceScore?: number;
    } = {}
  ): Promise<{
    results: Array<{
      id: string;
      text: string;
      score: number;
      metadata?: Record<string, any>;
    }>;
    sources: any[];
    totalResults: number;
  }> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required for document search. Please log in."
      );
    }

    return this.makeRequest<{
      results: Array<{
        id: string;
        text: string;
        score: number;
        metadata?: Record<string, any>;
      }>;
      sources: any[];
      totalResults: number;
    }>(`${KNOWLEDGE_ENDPOINTS.KNOWLEDGE_SEARCH}`, {
      method: "POST",
      body: JSON.stringify({
        query,
        documentIds,
        userId: userId,
        ...options,
      }),
    });
  }

  // Chat with Pinecone RAG
  async chatWithPineconeRAG(request: ChatRequest): Promise<ChatResponse> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error("User ID is required for chat. Please log in.");
    }

    console.log(`🔍 FRONTEND - chatWithPineconeRAG called with:`);
    console.log(`   Message: "${request.message}"`);
    console.log(`   SelectedDocuments:`, request.selectedDocuments);
    console.log(`   SelectedDocuments type:`, typeof request.selectedDocuments);
    console.log(`   SelectedDocuments length:`, request.selectedDocuments?.length);
    console.log(`   SelectedDocuments array:`, Array.isArray(request.selectedDocuments));
    if (request.selectedDocuments && request.selectedDocuments.length > 0) {
      console.log(`   SelectedDocuments values:`, request.selectedDocuments.map(id => `"${id}"`));
    }
    console.log(`   UserId: ${userId}`);

    return this.makeRequest<ChatResponse>(KNOWLEDGE_ENDPOINTS.CHAT, {
      method: "POST",
      body: JSON.stringify({
        ...request,
        userId: userId,
      }),
    });
  }

  // Document Management
  async getDocuments(): Promise<Document[]> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error("User ID is required to get documents. Please log in.");
    }

    return this.makeRequest<Document[]>(
      `${KNOWLEDGE_ENDPOINTS.KNOWLEDGE_DOCUMENTS}?userId=${userId}`
    );
  }

  // Delete document
  async deleteDocument(documentId: string): Promise<void> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error("User ID is required to delete document. Please log in.");
    }

    return this.makeRequest<void>(
      `${KNOWLEDGE_ENDPOINTS.KNOWLEDGE_DOCUMENTS}/${documentId}?userId=${userId}`,
      {
        method: "DELETE",
      }
    );
  }

  // Bulk delete documents
  async deleteDocuments(documentIds: string[]): Promise<{ deletedCount: number; deletedDocuments: Array<{ id: string; name: string }> }> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error("User ID is required to delete documents. Please log in.");
    }

    if (!documentIds || documentIds.length === 0) {
      throw new Error("At least one document ID is required for bulk deletion.");
    }

    return this.makeRequest<{ deletedCount: number; deletedDocuments: Array<{ id: string; name: string }> }>(
      `${KNOWLEDGE_ENDPOINTS.KNOWLEDGE_DOCUMENTS}?userId=${userId}`,
      {
        method: "DELETE",
        body: JSON.stringify({ documentIds }),
      }
    );
  }

  // Chat Management
  async sendChatMessage(request: ChatRequest): Promise<ChatResponse> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error("User ID is required for chat. Please log in.");
    }

    return this.makeRequest<ChatResponse>(KNOWLEDGE_ENDPOINTS.CHAT, {
      method: "POST",
      body: JSON.stringify({
        ...request,
        userId: userId,
      }),
    });
  }

  async streamChatMessage(
    request: ChatRequest,
    onChunk: (chunk: string) => void,
    onComplete: (response: ChatResponse) => void,
    onError: (error: Error) => void
  ): Promise<void> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      onError(
        new Error("User ID is required for chat streaming. Please log in.")
      );
      return;
    }

    try {
      const response = await fetch(`${this.baseUrl}${KNOWLEDGE_ENDPOINTS.CHAT_STREAM}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": this.apiKey,
          "x-user-id": userId,
        },
        body: JSON.stringify({
          ...request,
          userId: userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Response body is not readable");
      }

      const decoder = new TextDecoder();
      let buffer = "";
      let fullResponse = "";

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.slice(6);

            if (data === "[DONE]") {
              return;
            }

            try {
              const chunk = JSON.parse(data);

              if (chunk.error) {
                onError(new Error(chunk.error));
                return;
              }

              if (chunk.chunk) {
                fullResponse += chunk.chunk;
                onChunk(chunk.chunk);
              }

              if (chunk.complete) {
                onComplete({
                  message: chunk.message || fullResponse,
                  sources: chunk.sources || [],
                  sessionId: request.sessionId || "",
                  metadata: chunk.metadata || {},
                });
                return;
              }
            } catch (e) {
              // Ignore malformed chunks
            }
          }
        }
      }
    } catch (error) {
      onError(
        error instanceof Error ? error : new Error("Unknown streaming error")
      );
    }
  }

  async getChatSessions(): Promise<ChatSession[]> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required to get chat sessions. Please log in."
      );
    }

    return this.makeRequest<ChatSession[]>(
      `${KNOWLEDGE_ENDPOINTS.CHAT_HISTORY}?userId=${userId}`
    );
  }

  async createChatSession(
    title: string,
    selectedDocuments: string[]
  ): Promise<ChatSession> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required to create chat session. Please log in."
      );
    }

    return this.makeRequest<ChatSession>(KNOWLEDGE_ENDPOINTS.CHAT_HISTORY, {
      method: "POST",
      body: JSON.stringify({
        title,
        selectedDocuments,
        userId: userId,
      }),
    });
  }

  async updateChatSession(
    sessionId: string,
    updates: Partial<ChatSession>
  ): Promise<ChatSession> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required to update chat session. Please log in."
      );
    }

    return this.makeRequest<ChatSession>(
      `${KNOWLEDGE_ENDPOINTS.CHAT_HISTORY}/${sessionId}?userId=${userId}`,
      {
        method: "PATCH",
        body: JSON.stringify({
          ...updates,
          userId: userId,
        }),
      }
    );
  }

  async deleteChatSession(sessionId: string): Promise<void> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required to delete chat session. Please log in."
      );
    }

    return this.makeRequest<void>(
      `${KNOWLEDGE_ENDPOINTS.CHAT_HISTORY}/${sessionId}?userId=${userId}`,
      {
        method: "DELETE",
      }
    );
  }

  // Utility methods
  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  getFileIcon(fileType: string): string {
    const type = fileType.toLowerCase();

    if (type.includes("pdf")) return "📄";
    if (type.includes("word") || type.includes("doc")) return "📝";
    if (type.includes("text") || type.includes("txt")) return "📃";
    if (type.includes("markdown") || type.includes("md")) return "📋";

    return "📄";
  }

  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/plain",
      "text/markdown",
      "application/rtf",
      "application/vnd.oasis.opendocument.text",
    ];

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size exceeds 50MB limit. Current size: ${this.formatFileSize(
          file.size
        )}`,
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type not supported: ${file.type}`,
      };
    }

    return { isValid: true };
  }

  generateSessionTitle(firstMessage: string): string {
    // Generate a title from the first message (max 50 chars)
    const title = firstMessage.trim().slice(0, 50);
    return title.length < firstMessage.trim().length ? `${title}...` : title;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.makeRequest<{ status: string; timestamp: string }>(
      ENDPOINTS.HEALTH
    );
  }

  // Get document summary using LangGraph analysis
  async getDocumentSummary(documentId: string): Promise<{
    content: string;
    metadata: any;
  }> {
    const userId = await this.getCurrentUserId();
    if (!userId) {
      throw new Error(
        "User ID is required to get document summary. Please log in."
      );
    }

    return this.makeRequest<{
      content: string;
      metadata: any;
    }>(`/api/knowledge/summary/${documentId}?userId=${userId}`, {
      method: "GET",
    });
  }
}

export const knowledgeService = new KnowledgeService();
