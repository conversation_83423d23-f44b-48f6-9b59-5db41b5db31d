-- pSEO SEO Analysis Features Database Migration
-- Run this script to add SEO analysis support to your existing pSEO database

-- ============================================
-- 1. Add new columns to PSEOAudit table
-- ============================================

-- Add SEO analysis result fields
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS html_analysis_result TEXT;
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS seo_score INTEGER;
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS seo_grade VARCHAR(2);
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS seo_scoring_result TEXT;
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Add update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_pseo_audits_updated_at 
    BEFORE UPDATE ON pseo_audits 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- 2. Add new columns to PSEOAuditStep table
-- ============================================

-- Add step type and result fields
ALTER TABLE pseo_audit_steps ADD COLUMN IF NOT EXISTS step_type VARCHAR(50);
ALTER TABLE pseo_audit_steps ADD COLUMN IF NOT EXISTS result TEXT;
ALTER TABLE pseo_audit_steps ADD COLUMN IF NOT EXISTS metadata TEXT;

-- ============================================
-- 3. Create indexes for performance
-- ============================================

-- Index for SEO score queries
CREATE INDEX IF NOT EXISTS idx_pseo_audits_seo_score ON pseo_audits(seo_score);
CREATE INDEX IF NOT EXISTS idx_pseo_audits_seo_grade ON pseo_audits(seo_grade);

-- Index for step type queries
CREATE INDEX IF NOT EXISTS idx_pseo_audit_steps_step_type ON pseo_audit_steps(step_type);

-- Index for updated_at queries
CREATE INDEX IF NOT EXISTS idx_pseo_audits_updated_at ON pseo_audits(updated_at);

-- ============================================
-- 4. Add constraints
-- ============================================

-- Ensure SEO score is between 0 and 100
ALTER TABLE pseo_audits ADD CONSTRAINT IF NOT EXISTS chk_seo_score_range 
    CHECK (seo_score IS NULL OR (seo_score >= 0 AND seo_score <= 100));

-- Ensure SEO grade is valid
ALTER TABLE pseo_audits ADD CONSTRAINT IF NOT EXISTS chk_seo_grade_valid 
    CHECK (seo_grade IS NULL OR seo_grade IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D', 'F'));

-- ============================================
-- 5. Insert reference data
-- ============================================

-- Add new audit step types if using a reference table
-- INSERT INTO audit_step_types (name, description) VALUES 
-- ('html_analysis', 'HTML structure and SEO analysis'),
-- ('seo_scoring', 'SEO scoring with provider integration'),
-- ('comprehensive_analysis', 'Full SEO analysis with exact locations')
-- ON CONFLICT (name) DO NOTHING;

-- ============================================
-- 6. Data migration (if needed)
-- ============================================

-- Backfill existing audits with default values if needed
-- UPDATE pseo_audits 
-- SET updated_at = created_at 
-- WHERE updated_at IS NULL;

-- ============================================
-- 7. Verification queries
-- ============================================

-- Verify the migration was successful
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'pseo_audits' 
    AND column_name IN ('html_analysis_result', 'seo_score', 'seo_grade', 'seo_scoring_result', 'updated_at');

SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'pseo_audit_steps' 
    AND column_name IN ('step_type', 'result', 'metadata');

-- Check indexes
SELECT indexname, tablename FROM pg_indexes 
WHERE tablename IN ('pseo_audits', 'pseo_audit_steps') 
    AND indexname LIKE 'idx_%seo%';

-- Check constraints
SELECT constraint_name, table_name 
FROM information_schema.table_constraints 
WHERE table_name = 'pseo_audits' 
    AND constraint_name LIKE '%seo%';

COMMENT ON COLUMN pseo_audits.html_analysis_result IS 'JSON string containing detailed HTML analysis with exact issue locations';
COMMENT ON COLUMN pseo_audits.seo_score IS 'Overall SEO score from 0-100 calculated by provider-based scoring';
COMMENT ON COLUMN pseo_audits.seo_grade IS 'Letter grade (A+ to F) based on SEO score';
COMMENT ON COLUMN pseo_audits.seo_scoring_result IS 'JSON string containing complete SEO scoring breakdown';
COMMENT ON COLUMN pseo_audits.updated_at IS 'Timestamp of last update to audit record';

COMMENT ON COLUMN pseo_audit_steps.step_type IS 'Type of audit step (html_analysis, seo_scoring, etc.)';
COMMENT ON COLUMN pseo_audit_steps.result IS 'JSON string containing step execution results';
COMMENT ON COLUMN pseo_audit_steps.metadata IS 'JSON string containing additional step metadata';

-- Migration completed successfully
SELECT 'SEO Analysis Database Migration Completed Successfully!' as status; 

ALTER TABLE pseo_website_pages ADD CONSTRAINT pseo_website_pages_website_id_url_unique UNIQUE (website_id, url);