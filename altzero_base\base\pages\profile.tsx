import React, { useState, useEffect } from "react";
import { useUser } from "../contextapi/UserContext";
import { supabase } from "../utils/supabaseClient";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Separator } from "../components/ui/separator";
import { User, KeyRound, Loader2 } from "lucide-react";
import { AUTH_ROUTES } from "../config/constants";
import { Link } from "react-router-dom";

const Profile = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState({
    name: "",
    email: user?.email || "",
    avatar_url: ""
  });
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user]);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      
      if (!user?.id) return;

      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error) {
        console.warn("No profile found. Creating one.");
      } else if (data) {
        setUserData({
          name: data.name || "",
          email: user.email || "",
          avatar_url: data.avatar || ""
        });
      }
    } catch (error) {
      console.error("Error fetching profile: ", error);
      setError("Failed to load profile data");
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setMessage("");
      setError("");

      const updates = {
        id: user.id,
        name: userData.name,
        avatar: userData.avatar_url,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from("profiles")
        .upsert(updates);

      if (error) throw error;
      
      setMessage("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile: ", error);
      setError("Failed to update profile");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="flex items-center mb-6">
          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
            <User className="h-5 w-5 text-primary" />
          </div>
          <h1 className="text-2xl font-semibold">Profile Settings</h1>
        </div>
        
        {message && (
          <div className="p-4 rounded-md bg-success/10 border border-success/30 text-success text-sm">
            {message}
          </div>
        )}
        
        {error && (
          <div className="p-4 rounded-md bg-destructive/10 border border-destructive/30 text-destructive text-sm">
            {error}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>
              Manage your personal details and contact information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={updateProfile} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="text"
                  value={userData.email}
                  disabled
                  className="bg-muted/50 text-muted-foreground"
                />
                <p className="text-xs text-muted-foreground">
                  Your email cannot be changed
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  type="text"
                  value={userData.name}
                  onChange={(e) => setUserData({ ...userData, name: e.target.value })}
                />
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  type="submit"
                  disabled={loading}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {loading ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-primary">
          <CardHeader>
            <div className="flex items-center">
              <KeyRound className="h-5 w-5 mr-2 text-primary" />
              <CardTitle>Password Settings</CardTitle>
            </div>
            <CardDescription>
              Manage your account's security and password
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Need to update your password? You can request a password reset.
            </p>
            <Button variant="outline" asChild>
              <Link to={AUTH_ROUTES.UPDATE_PASSWORD}>
                Update Password
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Profile; 