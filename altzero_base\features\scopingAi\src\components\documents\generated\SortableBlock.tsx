import React, { useRef, useState, useEffect } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { GripVertical } from "lucide-react";
import { Block } from "../../../types/documentTypes";
import { debounce } from "../../../utils/documentUtils";

interface SortableBlockProps {
  block: Block;
  sectionId: string;
  onUpdate: (updates: Partial<Block>) => void;
  onDelete: () => void;
  isRegenerating?: boolean;
}

export const SortableBlock: React.FC<SortableBlockProps> = ({
  block,
  sectionId,
  onUpdate,
  onDelete,
  isRegenerating = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: block.id,
    disabled: isRegenerating || false,
  });

  // References to maintain focus
  const textInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);

  // Local state - ONLY for controlled component values
  const [localContent, setLocalContent] = useState(block.content);
  const [localItems, setLocalItems] = useState(block.items || []);
  const [localCaption, setLocalCaption] = useState(block.caption || "");

  // Debounced update function - only update parent after user stops typing
  const debouncedUpdate = debounce((updates: Partial<Block>) => {
    onUpdate(updates);
  }, 300);

  // Synchronize local state with props - but preserve focus
  useEffect(() => {
    // Only update local state from props if the content actually changed
    // and if the input doesn't have focus (to prevent cursor jumping)
    if (localContent !== block.content) {
      const activeElement = document.activeElement;
      const textInputHasFocus =
        textInputRef.current && textInputRef.current === activeElement;
      const textareaHasFocus =
        textareaRef.current && textareaRef.current === activeElement;

      if (!textInputHasFocus && !textareaHasFocus) {
        setLocalContent(block.content);
      }
    }

    setLocalItems(block.items || []);
    setLocalCaption(block.caption || "");
  }, [block.content, block.items, block.caption, localContent]);

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : "auto",
  };

  // Handle content update - only update local state immediately
  const handleContentChange = (value: string) => {
    setLocalContent(value);
    // Debounce the parent update to avoid re-renders while typing
    debouncedUpdate({ content: value });
  };

  // Handle list item update
  const handleItemChange = (index: number, value: string) => {
    const newItems = [...localItems];
    newItems[index] = value;
    setLocalItems(newItems);
    // Debounce the parent update
    debouncedUpdate({ items: newItems });
  };

  // Handle caption update
  const handleCaptionChange = (value: string) => {
    setLocalCaption(value);
    // Debounce the parent update
    debouncedUpdate({ caption: value });
  };

  // Prevent drag from starting on input elements
  const stopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Render block based on type
  const renderBlockEditor = () => {
    switch (block.type) {
      case "header":
        return (
          <div className="flex flex-col space-y-2">
            <div className="flex items-center justify-between">
              <select
                value={block.level}
                onChange={(e) => {
                  // Level changes are discrete, so no need to debounce
                  onUpdate({ level: parseInt(e.target.value) });
                }}
                className="p-2 border rounded bg-transparent"
                onMouseDown={stopPropagation}
                disabled={isRegenerating}
              >
                <option value={1}>Heading 1</option>
                <option value={2}>Heading 2</option>
                <option value={3}>Heading 3</option>
              </select>
              <button
                onClick={onDelete}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
                onMouseDown={stopPropagation}
                disabled={isRegenerating}
              >
                Delete
              </button>
            </div>
            <input
              ref={textInputRef}
              type="text"
              value={localContent}
              onChange={(e) => handleContentChange(e.target.value)}
              className="w-full p-2 border rounded"
              placeholder="Heading text"
              onMouseDown={stopPropagation}
              disabled={isRegenerating}
            />
          </div>
        );

      case "list":
        return (
          <div className="flex flex-col space-y-2">
            <div className="flex justify-between items-center">
              <span className="font-medium">List</span>
              <button
                onClick={onDelete}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
                onMouseDown={stopPropagation}
                disabled={isRegenerating}
              >
                Delete
              </button>
            </div>
            {localItems.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span>•</span>
                <input
                  type="text"
                  value={item}
                  onChange={(e) => handleItemChange(index, e.target.value)}
                  className="flex-grow p-2 border rounded"
                  placeholder="List item"
                  onMouseDown={stopPropagation}
                  disabled={isRegenerating}
                />
                <button
                  onClick={() => {
                    const newItems = [...localItems];
                    newItems.splice(index, 1);
                    setLocalItems(newItems);
                    onUpdate({ items: newItems });
                  }}
                  className="p-1 text-red-500 hover:bg-red-50 rounded"
                  onMouseDown={stopPropagation}
                  disabled={isRegenerating}
                >
                  ×
                </button>
              </div>
            ))}
            <button
              onClick={() => {
                const newItems = [...localItems, ""];
                setLocalItems(newItems);
                onUpdate({ items: newItems });
              }}
              className="p-2 border border-dashed rounded text-gray-500 hover:bg-gray-50"
              onMouseDown={stopPropagation}
              disabled={isRegenerating}
            >
              + Add list item
            </button>
          </div>
        );

      case "image":
        return (
          <div className="flex flex-col space-y-2">
            <div className="flex justify-between items-center">
              <span className="font-medium">Image</span>
              <button
                onClick={onDelete}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
                onMouseDown={stopPropagation}
                disabled={isRegenerating}
              >
                Delete
              </button>
            </div>
            {block.content.startsWith("data:image") ||
            block.content.startsWith("http") ? (
              <div className="relative">
                <img
                  src={block.content}
                  alt="Uploaded image"
                  className="max-w-full h-auto rounded border"
                />
              </div>
            ) : (
              <div className="p-4 border rounded text-center text-gray-500">
                Image URL not valid: {block.content.substring(0, 30)}...
              </div>
            )}
            <input
              ref={textInputRef}
              type="text"
              placeholder="Add image caption (optional)"
              className="w-full p-2 border rounded"
              value={localCaption}
              onChange={(e) => handleCaptionChange(e.target.value)}
              onMouseDown={stopPropagation}
              disabled={isRegenerating}
            />
          </div>
        );

      default: // text blocks
        return (
          <div className="flex flex-col space-y-2">
            <div className="flex justify-between items-center">
              <span className="font-medium">Paragraph</span>
              <button
                onClick={onDelete}
                className="p-1 text-red-500 hover:bg-red-50 rounded"
                onMouseDown={stopPropagation}
                disabled={isRegenerating}
              >
                Delete
              </button>
            </div>
            <textarea
              ref={textareaRef}
              value={localContent}
              onChange={(e) => handleContentChange(e.target.value)}
              className="w-full p-2 border rounded min-h-[100px] focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter text content"
              onMouseDown={stopPropagation}
              disabled={isRegenerating}
            />
          </div>
        );
    }
  };

  // Apply visual effect when regenerating
  const blockClasses = `border p-4 rounded-md bg-white shadow-sm mb-4 hover:shadow-md transition-shadow
    ${isRegenerating ? "opacity-60" : ""}
    ${isDragging ? "shadow-lg" : ""}`;

  return (
    <div ref={setNodeRef} style={style} className={blockClasses}>
      <div className="flex items-start mb-2">
        <div
          ref={dragHandleRef}
          {...attributes}
          {...listeners}
          className={`p-1 mr-2 mt-1 ${
            isRegenerating
              ? "cursor-not-allowed opacity-50"
              : "cursor-grab hover:bg-gray-100 active:cursor-grabbing"
          } rounded text-gray-400 hover:text-gray-700`}
          title="Drag to reorder"
        >
          <GripVertical size={16} />
        </div>
        <div className="flex-grow">{renderBlockEditor()}</div>
      </div>
    </div>
  );
}; 
