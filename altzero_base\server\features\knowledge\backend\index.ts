import express from 'express';
import { BackendPlugin } from '../../../plugins/loader';
import knowledgeRoutes from '../routes/knowledge';

// Create the backend plugin for knowledge
const knowledgeBackendPlugin: BackendPlugin = {
  router: knowledgeRoutes,
  config: {
    name: 'Knowledge Base API',
    version: '1.0.0',
    apiPrefix: '/api/knowledge'
  },
  initialize: async () => {
    console.log('🧠 Knowledge Base backend plugin initialized');
  },
  cleanup: async () => {
    console.log('🧠 Knowledge Base backend plugin cleaned up');
  },
  healthCheck: async () => {
    // Basic health check - you can enhance this
    try {
      return true;
    } catch (error) {
      console.error('Knowledge backend health check failed:', error);
      return false;
    }
  }
};

export default knowledgeBackendPlugin; 