import React from 'react';
import { RouteObject } from 'react-router-dom';

// Re-export the main config interface
export type { PluginConfig } from './registry';

// Navigation item structure
export interface NavigationItem {
  name: string;
  route: string;
  icon: string;
  order?: number;
  children?: NavigationItem[];
  permissions?: string[];
  badge?: string;
  external?: boolean;
}

// Frontend plugin module structure
export interface PluginModule {
  // React Router routes for this plugin
  routes: RouteObject[];
  
  // Navigation items for header/sidebar
  navigation: NavigationItem[];
  
  // Context providers (optional)
  providers?: React.ComponentType<{ children: React.ReactNode }>[];
  
  // Plugin metadata
  config: {
    name: string;
    version: string;
    description?: string;
  };
  
  // Initialization function (optional)
  initialize?: () => Promise<void> | void;
  
  // Cleanup function (optional)
  cleanup?: () => Promise<void> | void;
}

// Backend plugin structure
export interface BackendPlugin {
  // Express router for this plugin's API routes
  router: any; // Express.Router type
  
  // Plugin metadata
  config: {
    name: string;
    version: string;
    apiPrefix: string; // e.g., '/api/knowledge'
  };
  
  // Middleware specific to this plugin (optional)
  middleware?: any[];
  
  // Initialization function (optional)
  initialize?: () => Promise<void> | void;
  
  // Health check function (optional)
  healthCheck?: () => Promise<boolean>;
}

// Plugin loading state
export interface PluginLoadingState {
  pluginName: string;
  status: 'loading' | 'loaded' | 'error' | 'disabled';
  error?: string;
  module?: PluginModule;
}

// Plugin context type
export interface PluginContextType {
  loadedPlugins: Record<string, PluginModule>;
  loadingStates: Record<string, PluginLoadingState>;
  enabledPlugins: string[];
  isPluginLoaded: (pluginName: string) => boolean;
  getPluginModule: (pluginName: string) => PluginModule | undefined;
}

// Hook types
export interface UsePluginNavigationResult {
  navigation: NavigationItem[];
  isLoading: boolean;
  error?: string;
}

export interface UsePluginRoutesResult {
  routes: RouteObject[];
  isLoading: boolean;
  error?: string;
}

// Plugin events
export type PluginEventType = 'loaded' | 'unloaded' | 'error' | 'enabled' | 'disabled';

export interface PluginEvent {
  type: PluginEventType;
  pluginName: string;
  timestamp: Date;
  data?: any;
} 