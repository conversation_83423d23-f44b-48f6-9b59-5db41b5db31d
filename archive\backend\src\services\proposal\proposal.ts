import { Document, VectorStoreIndex } from 'llamaindex';
import { OpenRouterClient } from '../llm/openrouter';
import { DocumentProcessor } from '../document/processor';
import { DocumentMetadata } from '../../types/llama';

interface ProposalRequest {
  clientName: string;
  description: string;
  apiKey: string;
}

interface ProposalSection {
  title: string;
  content: string;
}

interface Proposal {
  id: string;
  clientName: string;
  description: string;
  sections: ProposalSection[];
  createdAt: Date;
  updatedAt: Date;
}

export class ProposalService {
  private llm: OpenRouterClient;
  private documentProcessor: DocumentProcessor;
  private index: VectorStoreIndex | null = null;

  constructor() {
    this.llm = new OpenRouterClient();
    this.documentProcessor = new DocumentProcessor();
  }

  async initialize(): Promise<void> {
    // Load documents and create index
    const documents = await this.documentProcessor.loadDocumentsFromDirectory('./documents');
    this.index = await this.documentProcessor.createIndex(documents);
  }

  async generateProposal(request: ProposalRequest): Promise<Proposal> {
    if (!this.index) {
      throw new Error('Index not initialized. Call initialize() first.');
    }

    // Generate research phase
    const researchPrompt = `Research the following client and project description to gather relevant information:
    Client: ${request.clientName}
    Description: ${request.description}`;

    const researchResponse = await this.llm.complete({
      prompt: researchPrompt,
      stream: false,
    });

    // Generate executive summary
    const summaryPrompt = `Based on the following research, create an executive summary for a proposal:
    Research: ${researchResponse.text}`;

    const summaryResponse = await this.llm.complete({
      prompt: summaryPrompt,
      stream: false,
    });

    // Generate sections
    const sections: ProposalSection[] = [];
    const sectionTitles = [
      'Project Overview',
      'Our Approach',
      'Deliverables',
      'Timeline',
      'Investment',
    ];

    for (const title of sectionTitles) {
      const sectionPrompt = `Generate the ${title} section for a proposal based on:
      Client: ${request.clientName}
      Description: ${request.description}
      Research: ${researchResponse.text}
      Executive Summary: ${summaryResponse.text}`;

      const sectionResponse = await this.llm.complete({
        prompt: sectionPrompt,
        stream: false,
      });

      sections.push({
        title,
        content: sectionResponse.text,
      });
    }

    // Create proposal
    const proposal: Proposal = {
      id: `prop_${Date.now()}`,
      clientName: request.clientName,
      description: request.description,
      sections,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return proposal;
  }

  async deleteAll(): Promise<void> {
    await this.documentProcessor.deleteAllDocuments();
    this.index = null;
  }
} 