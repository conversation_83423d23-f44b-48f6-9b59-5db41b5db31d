/**
 * This file contains prompt templates for the scoping document generation.
 * Centralizing prompts makes them easier to manage and improve.
 */

export const PROMPTS = {
  /**
   * System prompt template for scoping document generation
   */
  SYSTEM_SCOPING: `You are an expert AI consultant tasked with creating professional scoping documents.
Your job is to analyze project requirements and create comprehensive, well-structured scope documents.
Focus on being clear, thorough, and professional in your language.
Break down complex concepts into manageable sections.
Provide specific, actionable details rather than generic statements.
Use industry-standard terminology appropriate to the client's field.`,

  /**
   * Template for research phase prompt
   */
  RESEARCH: `
CLIENT INFORMATION:
{{clientInfo}}

PROJECT INFORMATION:
{{projectInfo}}

REQUIRED SECTIONS:
{{sections}}

{{documentContent}}

Based on the above information, please conduct research and provide a comprehensive summary.`,

  /**
   * Template for summary/introduction generation
   */
  SUMMARY: `Based on the following research about {{clientName}}'s project {{projectName}}:
  
{{researchContent}}

Please provide a concise executive summary of the project scope.`,

  /**
   * Template for section generation
   */
  SECTION: `Based on the following research and project information:
  
Research:
{{researchContent}}

Client Information:
{{clientInfo}}

Project Information:
{{projectInfo}}

Please write the "{{sectionTitle}}" section for the project scoping document. 
Description: {{sectionDescription}}`,

  /**
   * Template for user prompt with full project information
   */
  USER_FULL_PROMPT: `# Project Information

## Client Information
Client Name: {{clientName}}
Industry: {{industry}}
Company: {{company}}
Contact Person: {{contactPerson}}
Email: {{email}}

## Project Information
Project Name: {{projectName}}
Project Description: {{projectDescription}}
Timeline: {{timeline}}
Budget: {{budget}}

## Project Goals
{{goals}}

## Document Sections
I need you to create a detailed scoping document with the following sections:

{{sections}}

{{documentContent}}

Please create a comprehensive and professional scoping document based on the information provided.`
};

/**
 * Helper function to fill template with values
 */
export function fillTemplate(template: string, values: Record<string, string>): string {
  let result = template;
  for (const [key, value] of Object.entries(values)) {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
  }
  return result;
} 