import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { EntityManagement } from "../components/common/EntityManagement";
import { EntityType } from "../utils/constants";

export default function EntitiesPage() {
  const location = useLocation();
  const navigate = useNavigate();

  // Determine initial entity type based on the URL path
  const initialEntityType = location.pathname.includes("/teams")
    ? EntityType.TEAM
    : EntityType.ORGANIZATION;

  const [activeEntity, setActiveEntity] =
    useState<EntityType>(initialEntityType);

  const handleChangeEntity = (entity: EntityType) => {
    setActiveEntity(entity);

    // Update URL to match the selected entity type without page refresh
    const newPath =
      entity === EntityType.ORGANIZATION ? "/organizations" : "/teams";
    if (location.pathname !== newPath) {
      navigate(newPath, { replace: true });
    }
  };

  return (
    <EntityManagement
      activeEntity={activeEntity}
      onChangeEntity={handleChangeEntity}
    />
  );
}
