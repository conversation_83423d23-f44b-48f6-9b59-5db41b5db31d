import { createClient } from '@supabase/supabase-js';

// Replace these with your actual Supabase URL and public key
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'YOUR_SUPABASE_URL';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export const setupSessionRefresh = () => {
  // Setup a refresh interval (e.g., every 23 hours to avoid token expiration)
  const REFRESH_INTERVAL = 23 * 60 * 60 * 1000; // 23 hours in milliseconds
  
  const refreshSession = async () => {
    const { data, error } = await supabase.auth.refreshSession();
    if (error) {
      console.error('Error refreshing session:', error);
    } else {
      console.log('Session refreshed successfully');
    }
  };

  // Set up interval
  const interval = setInterval(refreshSession, REFRESH_INTERVAL);
  
  // Clear interval on unmount
  return () => clearInterval(interval);
};
