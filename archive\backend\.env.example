


OPENROUTER_API_KEY=""
PINECONE_API_KEY=""
COHERE_API_KEY=""

# Server Configuration
PORT=""
NODE_ENV=""

# Vector Store Configuration
PINECONE_ENVIRONMENT=""
PINECONE_INDEX_NAME=""
PINECONE_NAMESPACE=""

# OpenRouter Configuration
OPENROUTER_BASE_URL=""
OPENROUTER_MODEL=""

# Document Processing
CHUNK_SIZE=512
CHUNK_OVERLAP=20
MAX_TOKENS=4096

# Cache Configuration
CACHE_DIR=./cache
DOCUMENTS_DIR=./documents 
OPENAI_MODEL=
OPENAI_BASE_URL=
OPENAI_API_KEY=

# CORS settings
ALLOWED_ORIGINS=http://localhost:5173,https://yourdomain.com