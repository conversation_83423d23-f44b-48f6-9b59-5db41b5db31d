-- =============================================
-- SCOPINGAI DATABASE SCHEMA - ESSENTIAL TABLES ONLY
-- =============================================
-- This file contains the essential database schema for the ScopingAI feature
-- All tables are prefixed with 'scopingAi_' for portability across projects
-- Schema matches the exact structure from scopingai-dev project
-- Only includes the 5 essential tables you actually need

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- CUSTOM TYPES (ENUMS) - Only for Document Sharing
-- =============================================

-- Entity types for sharing
CREATE TYPE share_entity_type AS ENUM ('user', 'team', 'organisation', 'link');

-- Permission levels for sharing
CREATE TYPE share_permission_level AS ENUM ('view', 'comment', 'suggest', 'edit', 'manage', 'admin');

-- =============================================
-- MAIN TABLES
-- =============================================

-- 1. Main Documents Table (matches scopingai-dev exactly)
CREATE TABLE scopingai_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL,
    title TEXT NOT NULL,
    content JSONB,
    client_id UUID,
    template_id UUID,
    sections JSONB,
    status TEXT DEFAULT 'progress',
    project_data JSONB,
    selected_client_id TEXT,
    selected_prompt_template_id TEXT,
    selected_scope_template_id TEXT,
    selected_section_template_id TEXT,
    uploaded_document_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "proposalFilePath" TEXT,
    metadata JSONB
);

-- 2. Clients Table (matches scopingai-dev exactly)
CREATE TABLE scopingai_clients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL,
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    industry TEXT,
    company TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Prompt Templates Table (matches scopingai-dev exactly)
CREATE TABLE scopingai_prompttemplates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    variables JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Proposal Templates Table (matches scopingai-dev exactly)
CREATE TABLE scopingai_proposaltemplates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    user_id UUID NOT NULL,
    markdown_path TEXT,
    bucket_path TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 5. Document Shares Table (matches scopingai-dev exactly)
CREATE TABLE scopingai_documentshares (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID NOT NULL,
    entity_type share_entity_type NOT NULL,
    entity_id UUID,
    permission_level share_permission_level NOT NULL,
    shared_by UUID NOT NULL,
    shared_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('utc'::text, now()),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    custom_message TEXT,
    notify_on_changes BOOLEAN DEFAULT false,
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    token TEXT,
    password_hash TEXT,
    max_access_count INTEGER,
    allowed_domains TEXT[] DEFAULT '{}'::text[]
);

-- =============================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================

-- Documents table foreign keys
ALTER TABLE scopingai_documents
ADD CONSTRAINT fk_scopingAi_documents_client_id
FOREIGN KEY (client_id) REFERENCES scopingai_clients(id) ON DELETE SET NULL;

-- Document shares foreign keys
ALTER TABLE scopingai_documentshares
ADD CONSTRAINT fk_scopingAi_documentShares_document_id
FOREIGN KEY (document_id) REFERENCES scopingai_documents(id) ON DELETE CASCADE;

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Documents table indexes
CREATE INDEX IF NOT EXISTS idx_scopingAi_documents_user_id ON scopingai_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_scopingAi_documents_client_id ON scopingai_documents(client_id);
CREATE INDEX IF NOT EXISTS idx_scopingAi_documents_status ON scopingai_documents(status);
CREATE INDEX IF NOT EXISTS idx_scopingAi_documents_created_at ON scopingai_documents(created_at);

-- Clients table indexes
CREATE INDEX IF NOT EXISTS idx_scopingAi_clients_user_id ON scopingai_clients(user_id);
CREATE INDEX IF NOT EXISTS idx_scopingAi_clients_name ON scopingai_clients(name);

-- Prompt templates indexes
CREATE INDEX IF NOT EXISTS idx_scopingAi_promptTemplates_user_id ON scopingai_prompttemplates(user_id);

-- Proposal templates indexes
CREATE INDEX IF NOT EXISTS idx_scopingAi_proposalTemplates_user_id ON scopingai_proposaltemplates(user_id);

-- Document shares indexes
CREATE INDEX IF NOT EXISTS idx_scopingAi_documentShares_document_id ON scopingai_documentshares(document_id);
CREATE INDEX IF NOT EXISTS idx_scopingAi_documentShares_entity_type_id ON scopingai_documentshares(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_scopingAi_documentShares_shared_by ON scopingai_documentshares(shared_by);
