import {
  Block,
  Section,
  GeneratedDocumentData,
  PageData,
} from "../types/documentTypes";

// Debounce function
export const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Enhanced function to convert content strings to blocks
export function convertContentToBlocks(content: string): Block[] {
  if (!content) return [];

  // Split content into meaningful blocks
  const lines = content.split("\n");
  const blocks: Block[] = [];
  let currentBlock: any = null;
  let blockId = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip empty lines
    if (line === "") {
      if (currentBlock) {
        blocks.push(currentBlock);
        currentBlock = null;
      }
      continue;
    }

    // Check for headers (# Header)
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (headerMatch) {
      if (currentBlock) {
        blocks.push(currentBlock);
      }

      currentBlock = {
        id: `block-${blockId++}-${Date.now()}`,
        type: "header",
        content: headerMatch[2],
        level: headerMatch[1].length,
      };

      blocks.push(currentBlock);
      currentBlock = null;
      continue;
    }

    // Check for list items
    if (
      line.startsWith("- ") ||
      line.startsWith("* ") ||
      line.match(/^\d+\.\s+/)
    ) {
      const listContent = line.replace(/^[- *]\s+|^\d+\.\s+/, "");

      // If we're not already building a list, start a new one
      if (!currentBlock || currentBlock.type !== "list") {
        if (currentBlock) {
          blocks.push(currentBlock);
        }

        currentBlock = {
          id: `block-${blockId++}-${Date.now()}`,
          type: "list",
          content: line,
          items: [listContent],
        };
      } else {
        // Add to existing list
        currentBlock.items = currentBlock.items || [];
        currentBlock.items.push(listContent);
        currentBlock.content += `\n${line}`;
      }
      continue;
    }

    // Check for image markdown ![alt](url)
    const imageMatch = line.match(/!\[(.*?)\]\((.*?)\)/);
    if (imageMatch) {
      if (currentBlock) {
        blocks.push(currentBlock);
      }

      currentBlock = {
        id: `block-${blockId++}-${Date.now()}`,
        type: "image",
        content: imageMatch[2], // The URL or base64 data
        caption: imageMatch[1] || "", // The alt text as caption
      };

      blocks.push(currentBlock);
      currentBlock = null;
      continue;
    }

    // Regular paragraph text
    if (!currentBlock || currentBlock.type !== "text") {
      if (currentBlock) {
        blocks.push(currentBlock);
      }

      currentBlock = {
        id: `block-${blockId++}-${Date.now()}`,
        type: "text",
        content: line,
      };
    } else {
      // Append to existing paragraph
      currentBlock.content += `\n${line}`;
    }
  }

  // Add the last block if it exists
  if (currentBlock) {
    blocks.push(currentBlock);
  }

  return blocks;
}

// Helper function to convert blocks back to content
export function convertBlocksToContent(blocks: Block[]): string {
  return blocks
    .map((block) => {
      switch (block.type) {
        case "header":
          const prefix = "#".repeat(block.level || 1);
          return `${prefix} ${block.content}`;
        case "list":
          if (block.items && block.items.length > 0) {
            return block.items.map((item) => `- ${item}`).join("\n");
          }
          return block.content;
        case "image":
          // For images, preserve the full content including base64 data
          if (block.content) {
            // If it's already in markdown format, return as is
            if (
              block.content.startsWith("![") &&
              block.content.includes("](")
            ) {
              return block.content;
            }
            // If it's base64 data or URL, wrap it in markdown format
            const caption = block.caption || "Image";
            return `![${caption}](${block.content})`;
          } else {
            // Fallback for empty images
            const caption = block.caption || "Image placeholder";
            return `![${caption}](placeholder)`;
          }
        default:
          return block.content;
      }
    })
    .join("\n\n");
}

// Enhanced function to render blocks as HTML for preview
export const renderBlocksToHTML = (blocks: any[]): string => {
  return blocks
    .map((block) => {
      switch (block.type) {
        case "header":
        case "heading":
          const level = Math.min(Math.max(block.level || 2, 1), 6); // Ensure level is between 1-6
          const tag = `h${level}`;
          const fontSize =
            level === 1
              ? "2rem"
              : level === 2
                ? "1.5rem"
                : level === 3
                  ? "1.25rem"
                  : "1.125rem";
          return `<${tag} style="margin: 0.75rem 0; font-weight: 600; font-size: ${fontSize}; line-height: 1.2;">${block.content || ""}</${tag}>`;

        case "text":
          // Convert newlines to HTML line breaks for proper formatting
          const formattedText = (block.content || "").replace(/\n/g, "<br>");
          return `<p style="margin: 0.5rem 0; line-height: 1.6; color: inherit;">${formattedText}</p>`;

        case "list":
          const items =
            block.items ||
            (block.content
              ? block.content
                  .split("\n")
                  .filter((line: string) => line.trim().startsWith("-"))
                  .map((line: string) => line.replace(/^-\s*/, ""))
              : []);
          if (items.length === 0) return "";
          const listItems = items
            .map((item: string) => {
              // Handle line breaks in list items too
              const formattedItem = item.replace(/\n/g, "<br>");
              return `<li style="margin: 0.25rem 0;">${formattedItem}</li>`;
            })
            .join("");
          return `<ul style="margin: 0.5rem 0; padding-left: 1.5rem; line-height: 1.5;">${listItems}</ul>`;

        case "image":
          const src = block.content || "";
          const alt = block.caption || block.alt || "Image";
          if (src) {
            return `
              <div style="margin: 1rem 0; text-align: center;">
                <img src="${src}" alt="${alt}" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" />
                ${block.caption ? `<p style="margin-top: 0.5rem; font-size: 0.875rem; color: #666; font-style: italic;">${block.caption}</p>` : ""}
              </div>
            `;
          }
          return `<div style="padding: 2rem; border: 2px dashed #ccc; text-align: center; color: #666; margin: 0.5rem 0; border-radius: 8px; background-color: #f9f9f9;">
            <p style="margin: 0; font-style: italic;">Image placeholder: ${alt}</p>
          </div>`;

        case "columns":
          // Render column layouts properly in preview
          if (block.columns && block.columns.length > 0) {
            const columnWidth = `${100 / block.columns.length}%`;
            const columnsHTML = block.columns
              .map((column: any) => {
                const columnBlocks = column.blocks || [];
                const columnContent = renderBlocksToHTML(columnBlocks);
                return `
                <div style="flex: 1; min-width: 0; padding: 0 0.5rem; box-sizing: border-box;">
                  ${columnContent || '<p style="color: #999; font-style: italic; text-align: center; padding: 2rem;">Empty column</p>'}
                </div>
              `;
              })
              .join("");

            return `
              <div style="display: flex; gap: 1rem; margin: 1rem 0; min-height: 100px; border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; background-color: #fafafa;">
                ${columnsHTML}
              </div>
            `;
          }
          return `<div style="padding: 1rem; border: 2px dashed #ccc; text-align: center; color: #666; margin: 0.5rem 0; border-radius: 8px; background-color: #f9f9f9;">
            <p style="margin: 0; font-style: italic;">Empty column layout</p>
          </div>`;

        default:
          return `<div style="margin: 0.5rem 0; padding: 0.5rem; background-color: #f5f5f5; border-radius: 4px; border-left: 3px solid #ddd;">${block.content || ""}</div>`;
      }
    })
    .join("");
};

// Function to convert content to markdown
export function convertToMarkdown(documentData: GeneratedDocumentData): string {
  let markdown = `# ${documentData.title}\n\n`;

  if (documentData.client) {
    markdown += `**Client:** ${documentData.client}\n\n`;
  }

  // Add sections
  if (documentData.sections && documentData.sections.length > 0) {
    documentData.sections.forEach((section) => {
      markdown += `## ${section.title}\n\n`;

      // Process blocks in each section
      section.blocks.forEach((block) => {
        switch (block.type) {
          case "header":
            const headerLevel = block.level || 2;
            const prefix = "#".repeat(headerLevel + 1); // +1 because we already have a level-2 section header
            markdown += `${prefix} ${block.content}\n\n`;
            break;

          case "list":
            if (block.items && block.items.length > 0) {
              block.items.forEach((item) => {
                markdown += `- ${item}\n`;
              });
              markdown += "\n";
            }
            break;

          case "image":
            const caption = block.caption ? ` "${block.caption}"` : "";
            // Include the full base64 image data in the markdown
            if (block.content.startsWith("data:image")) {
              markdown += `![Image${caption}](${block.content})\n\n`;
            } else {
              // Fallback for non-data URLs
              markdown += `![Image${caption}](${block.caption || "image"})\n\n`;
            }
            break;

          default: // text
            markdown += `${block.content}\n\n`;
        }
      });
    });
  }

  return markdown;
}

// Function to split sections into pages
export function splitIntoPages(sections: Section[]): PageData[] {
  return sections.map((section, index) => ({
    id: `page-${index + 1}`,
    content: [section],
  }));
}

// Helper function to convert file to base64
export const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

// Helper function to convert advanced blocks to regular blocks
export function convertAdvancedBlocksToBlocks(advancedBlocks: any[]): Block[] {
  return advancedBlocks.map((block, index) => ({
    id: block.id || `converted-${index}-${Date.now()}`,
    type: block.type === "heading" ? "header" : block.type,
    content:
      block.type === "text"
        ? block.content?.text
        : block.type === "heading"
          ? block.content?.text
          : block.type === "image"
            ? block.content?.src
            : block.content,
    level: block.type === "heading" ? block.content?.level : block.level,
    items: block.type === "list" ? block.content?.items : block.items,
    caption:
      block.type === "image"
        ? block.content?.caption || block.content?.alt
        : undefined,
  }));
}

// Helper function to extract plain text from blocks for search/preview
export function extractTextFromBlocks(blocks: Block[]): string {
  return blocks
    .map((block) => {
      switch (block.type) {
        case "header":
          return block.content || "";
        case "text":
          return block.content || "";
        case "list":
          return block.items ? block.items.join(" ") : block.content || "";
        case "image":
          return block.caption || "Image";
        default:
          return block.content || "";
      }
    })
    .join(" ");
}
