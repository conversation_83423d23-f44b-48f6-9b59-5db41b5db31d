import React, { useState } from "react";
import { Upload, X } from "lucide-react";
import { Button } from "@base/components/ui/button";
import { Input } from "@base/components/ui/input";
import { Label } from "@base/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@base/components/ui/dialog";
import { ProcessedDocument } from "../../../types/document-library";

interface UploadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDocumentProcessed: (document: ProcessedDocument) => void;
}

export function UploadDialog({
  isOpen,
  onClose,
  onDocumentProcessed,
}: UploadDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [title, setTitle] = useState("");

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setTitle(file.name.replace(/\.[^/.]+$/, "")); // Remove file extension
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !title) return;

    setIsUploading(true);
    try {
      // Simulate file processing
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;

        // Create processed document
        const processedDocument: ProcessedDocument = {
          title: title,
          fullContent: content,
          metadata: {
            author: "Unknown Author",
            pageCount: 1,
            fileType: selectedFile.type,
            createdDate: new Date().toISOString(),
            modifiedDate: new Date().toISOString(),
          },
          sections: [
            {
              title: "Content",
              content: content,
              description: "Document content",
              order: 0,
            },
          ],
        };

        onDocumentProcessed(processedDocument);
        handleClose();
      };

      reader.readAsText(selectedFile);
    } catch (error) {
      console.error("Error processing file:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setTitle("");
    setIsUploading(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
          <DialogDescription>
            Upload a document to add to your library
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Document Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter document title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="file">File</Label>
            <div className="flex items-center gap-2">
              <Input
                id="file"
                type="file"
                accept=".txt,.md,.pdf,.doc,.docx"
                onChange={handleFileSelect}
                className="flex-1"
              />
              {selectedFile && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedFile(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            {selectedFile && (
              <p className="text-sm text-muted-foreground">
                Selected: {selectedFile.name} (
                {(selectedFile.size / 1024).toFixed(1)} KB)
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || !title || isUploading}
          >
            {isUploading ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
