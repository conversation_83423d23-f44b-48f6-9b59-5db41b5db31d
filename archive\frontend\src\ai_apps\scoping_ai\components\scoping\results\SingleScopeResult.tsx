import React, { useRef, useState, useEffect } from 'react';
import { Scoping, Block } from '../../../../../types/scoping';

interface SingleScopeResultProps {
  onReset: () => void;
  result: Scoping | null;
  onSave?: (updatedScoping: Scoping) => void;
}

interface EditableSection {
  isEditing: boolean;
  content: string;
  blocks?: Block[];
}

const SingleScopeResult: React.FC<SingleScopeResultProps> = ({
  onReset,
  result,
  onSave
}) => {
  const documentRef = useRef<HTMLDivElement>(null);
  
  // State for tracking editable sections
  const [editableDescription, setEditableDescription] = useState<EditableSection>({
    isEditing: false,
    content: result?.description || '',
    blocks: convertContentToBlocks(result?.description || '')
  });
  
  const [editableSections, setEditableSections] = useState<EditableSection[]>(
    result?.sections.map(section => ({
      isEditing: false,
      content: section.content,
      blocks: convertContentToBlocks(section.content)
    })) || []
  );

  const [documentName, setDocumentName] = React.useState('');
  
  // Function to convert plain text content to blocks
  function convertContentToBlocks(content: string): Block[] {
    // Split content by double line breaks to separate paragraphs
    const paragraphs = content.split(/\n\n+/);
    return paragraphs.map((para, index) => {
      // Detect block type based on content
      if (para.startsWith('# ')) {
        return {
          id: `block-${index}`,
          type: 'header',
          content: para.substring(2),
          level: 1
        };
      } else if (para.startsWith('## ')) {
        return {
          id: `block-${index}`,
          type: 'header',
          content: para.substring(3),
          level: 2
        };
      } else if (para.startsWith('### ')) {
        return {
          id: `block-${index}`,
          type: 'header',
          content: para.substring(4),
          level: 3
        };
      } else if (para.startsWith('> ')) {
        return {
          id: `block-${index}`,
          type: 'quote',
          content: para.substring(2)
        };
      } else if (para.startsWith('```')) {
        const codeMatch = para.match(/```(\w*)\n([\s\S]*?)```/);
        if (codeMatch) {
          return {
            id: `block-${index}`,
            type: 'code',
            content: codeMatch[2],
            language: codeMatch[1] || 'plaintext'
          };
        }
      } else if (para.includes('| --- |') || para.match(/\|.*\|.*\|/)) {
        // Simple table detection
        const rows = para.split('\n').map(row => 
          row.split('|').map(cell => cell.trim()).filter(cell => cell)
        );
        return {
          id: `block-${index}`,
          type: 'table',
          content: para,
          rows: rows
        };
      } else if (para.startsWith('- ') || para.match(/^\d+\. /)) {
        // List detection
        const items = para.split('\n').map(item => 
          item.replace(/^- /, '').replace(/^\d+\. /, '')
        );
        return {
          id: `block-${index}`,
          type: 'list',
          content: para,
          items: items
        };
      }
      
      // Default to text block
      return {
        id: `block-${index}`,
        type: 'text',
        content: para
      };
    });
  }

  // Function to convert blocks back to content
  function convertBlocksToContent(blocks: Block[]): string {
    return blocks.map(block => {
      switch (block.type) {
        case 'header':
          const prefix = '#'.repeat(block.level || 1);
          return `${prefix} ${block.content}`;
        case 'quote':
          return `> ${block.content}`;
        case 'code':
          return `\`\`\`${block.language || ''}\n${block.content}\n\`\`\``;
        case 'list':
          return (block.items || []).map(item => `- ${item}`).join('\n');
        case 'table':
          if (block.rows) {
            return block.rows.map(row => `| ${row.join(' | ')} |`).join('\n');
          }
          return block.content;
        default:
          return block.content;
      }
    }).join('\n\n');
  }

  // Update state when result changes
  useEffect(() => {
    if (result) {
      setEditableDescription({
        isEditing: false,
        content: result.description,
        blocks: convertContentToBlocks(result.description)
      });
      
      setEditableSections(
        result.sections.map(section => ({
          isEditing: false,
          content: section.content,
          blocks: convertContentToBlocks(section.content)
        }))
      );
    }
  }, [result]);

  // Toggle editing for description
  const toggleDescriptionEdit = () => {
    setEditableDescription({
      ...editableDescription,
      isEditing: !editableDescription.isEditing
    });
  };

  // Toggle editing for a specific section
  const toggleSectionEdit = (index: number) => {
    const newSections = [...editableSections];
    newSections[index] = {
      ...newSections[index],
      isEditing: !newSections[index].isEditing
    };
    setEditableSections(newSections);
  };

  // Handle description content change
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    setEditableDescription({
      ...editableDescription,
      content,
      blocks: convertContentToBlocks(content)
    });
  };

  // Handle section content change
  const handleSectionChange = (index: number, e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    const newSections = [...editableSections];
    newSections[index] = {
      ...newSections[index],
      content,
      blocks: convertContentToBlocks(content)
    };
    setEditableSections(newSections);
  };

  // Update a block within a section
  const updateSectionBlock = (sectionIndex: number, blockIndex: number, updates: Partial<Block>) => {
    if (!editableSections[sectionIndex]?.blocks) return;
    
    const newSections = [...editableSections];
    const blocks = [...(newSections[sectionIndex].blocks || [])];
    blocks[blockIndex] = { ...blocks[blockIndex], ...updates };
    
    newSections[sectionIndex] = {
      ...newSections[sectionIndex],
      blocks,
      content: convertBlocksToContent(blocks)
    };
    
    setEditableSections(newSections);
  };

  // Update a block within the description
  const updateDescriptionBlock = (blockIndex: number, updates: Partial<Block>) => {
    if (!editableDescription.blocks) return;
    
    const blocks = [...(editableDescription.blocks || [])];
    blocks[blockIndex] = { ...blocks[blockIndex], ...updates };
    
    setEditableDescription({
      ...editableDescription,
      blocks,
      content: convertBlocksToContent(blocks)
    });
  };

  // Save all changes
  const handleSave = () => {
    if (documentName.trim() && onSave && result) {
      onSave({
        ...result!,
        projectName: documentName,
        updatedAt: new Date()
      });
    }
  };

  // Render a specific type of block
  const renderBlock = (block: Block, isEditing: boolean, onChange?: (updates: Partial<Block>) => void) => {
    if (isEditing) {
      // When editing, we render the block editor UI
      return renderBlockEditor(block, onChange);
    }
    
    // When not editing, we render the block content
    switch (block.type) {
      case 'header':
        const HeaderTag = `h${block.level || 2}` as keyof JSX.IntrinsicElements;
        return (
          <HeaderTag className={`font-bold mb-4 ${
            block.level === 1 ? 'text-2xl' : 
            block.level === 2 ? 'text-xl' : 'text-lg'
          }`}>
            {block.content}
          </HeaderTag>
        );
        
      case 'text':
        return <p className="text-gray-700 mb-4">{block.content}</p>;
        
      case 'quote':
        return (
          <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-4">
            {block.content}
          </blockquote>
        );
        
      case 'list':
        return (
          <ul className="list-disc pl-5 mb-4">
            {(block.items || [block.content]).map((item, i) => (
              <li key={i} className="mb-1">{item}</li>
            ))}
          </ul>
        );
        
      case 'code':
        return (
          <pre className="bg-gray-50 p-4 rounded overflow-x-auto mb-4">
            <code>{block.content}</code>
          </pre>
        );
        
      case 'table':
        if (block.rows && block.rows.length > 0) {
          return (
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full divide-y divide-gray-200">
                <tbody className="bg-white divide-y divide-gray-200">
                  {block.rows.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.map((cell, cellIndex) => (
                        <td 
                          key={cellIndex} 
                          className={`px-3 py-2 ${rowIndex === 0 ? 'font-medium bg-gray-50' : ''}`}
                        >
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          );
        }
        return <p className="text-gray-700 mb-4">{block.content}</p>;
        
      default:
        return <p className="text-gray-700 mb-4">{block.content}</p>;
    }
  };
  
  // Render editor UI for a block
  const renderBlockEditor = (block: Block, onChange?: (updates: Partial<Block>) => void) => {
    if (!onChange) return null;
    
    const updateContent = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      onChange({ content: e.target.value });
    };
    
    switch (block.type) {
      case 'header':
        return (
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <label className="block text-sm font-medium text-gray-700 mr-2">Level:</label>
              <select 
                value={block.level || 2}
                onChange={(e) => onChange({ level: parseInt(e.target.value) })}
                className="mr-2 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm"
              >
                <option value={1}>H1</option>
                <option value={2}>H2</option>
                <option value={3}>H3</option>
              </select>
            </div>
            <input
              type="text"
              value={block.content}
              onChange={updateContent}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Heading text"
            />
          </div>
        );
        
      case 'list':
        return (
          <div className="mb-4">
            <div className="mb-2 flex items-center">
              <span className="text-sm font-medium text-gray-700">List Items</span>
              <button
                type="button"
                onClick={() => onChange({ 
                  items: [...(block.items || []), ''] 
                })}
                className="ml-2 text-sm text-indigo-600 hover:text-indigo-900"
              >
                + Add Item
              </button>
            </div>
            {(block.items || []).map((item, i) => (
              <div key={i} className="flex mb-2">
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newItems = [...(block.items || [])];
                    newItems[i] = e.target.value;
                    onChange({ items: newItems });
                  }}
                  className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder={`List item ${i + 1}`}
                />
                <button
                  type="button"
                  onClick={() => {
                    const newItems = [...(block.items || [])];
                    newItems.splice(i, 1);
                    onChange({ items: newItems });
                  }}
                  className="ml-2 text-red-500 hover:text-red-700"
                >
                  &times;
                </button>
              </div>
            ))}
          </div>
        );
        
      case 'quote':
      case 'text':
      default:
        return (
          <textarea
            value={block.content}
            onChange={updateContent}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 h-32 mb-4"
            placeholder={`${block.type.charAt(0).toUpperCase() + block.type.slice(1)} content`}
          />
        );
    }
  };
  
  // Handle print/export functionality
  const handleExport = () => {
    if (!documentRef.current || !result) return;

    const printContents = documentRef.current.innerHTML;
    const originalContents = document.body.innerHTML;
    
    // Create a styled print version
    document.body.innerHTML = `
      <html>
        <head>
          <title>${result.projectName || 'Scoping Document'}</title>
          <style>
            @page { size: A4; margin: 2cm; }
            body { 
              font-family: 'Arial', 'Helvetica', sans-serif; 
              line-height: 1.5;
              color: #333;
              margin: 0;
              padding: 0;
            }
            .document-container {
              max-width: 21cm;
              margin: 0 auto;
              padding: 1cm;
            }
            .header {
              text-align: center;
              margin-bottom: 2cm;
              padding-bottom: 0.5cm;
              border-bottom: 1px solid #ddd;
            }
            h1 { font-size: 24pt; margin-bottom: 6pt; color: #222; }
            h2 { font-size: 18pt; margin-top: 16pt; margin-bottom: 6pt; color: #222; page-break-after: avoid; }
            h3 { font-size: 14pt; margin-top: 13pt; margin-bottom: 6pt; color: #222; }
            p { font-size: 11pt; margin-bottom: 10pt; }
            .section {
              margin-top: 1cm;
              page-break-inside: avoid;
            }
            .footer {
              margin-top: 2cm;
              padding-top: 0.5cm;
              border-top: 1px solid #ddd;
              text-align: center;
              font-size: 9pt;
              color: #666;
            }
            @media print {
              body { background-color: white; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="document-container">
            ${printContents}
            <div class="footer">
              <p>Generated on ${new Date().toLocaleDateString()} | ${result.clientName}</p>
            </div>
          </div>
        </body>
      </html>
    `;
    
    window.print();
    
    // Restore original content
    document.body.innerHTML = originalContents;
  };

  if (!result) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No scoping document has been generated yet.</p>
        <button
          onClick={onReset}
          className="mt-4 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Return to Form
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Scoping Document Result</h1>
        <div className="flex space-x-3">
          <input
            type="text"
            value={documentName}
            onChange={(e) => setDocumentName(e.target.value)}
            placeholder="Document name"
            className="border border-gray-300 rounded px-3 py-2 text-sm"
          />
          <button
            onClick={handleSave}
            disabled={!documentName.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
          >
            Save
          </button>
          <button
            onClick={onReset}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Create New
          </button>
        </div>
      </div>
      
      <div className="bg-white shadow-sm rounded-lg p-6 mb-6">
        <div className="text-center mb-8 pb-4 border-b">
          <h2 className="text-2xl font-bold">{result.projectName || "Untitled Project"}</h2>
          <p className="text-gray-600">Scope Document</p>
          {result.clientInfo && (
            <p className="text-sm text-gray-500 mt-2">
              Prepared for {result.clientInfo.name} ({result.clientInfo.company})
            </p>
          )}
        </div>
        
        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-3">Project Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Client</p>
              <p>{result.clientInfo?.name || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Company</p>
              <p>{result.clientInfo?.company || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Industry</p>
              <p>{result.clientInfo?.industry || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Contact</p>
              <p>{result.clientInfo?.contactPerson || "N/A"}</p>
            </div>
          </div>
        </div>
        
        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-3">Scope Information</h3>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Project Type</p>
              <p>{result.scopingInfo?.projectType || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Timeline</p>
              <p>{result.scopingInfo?.timeline || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Budget</p>
              <p>{result.scopingInfo?.budget || "N/A"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Objectives</p>
              <p>{result.scopingInfo?.objectives || "N/A"}</p>
            </div>
          </div>
        </div>
        
        {result.sections && result.sections.length > 0 && (
          <div>
            <h3 className="text-xl font-semibold mb-3">Document Sections</h3>
            <div className="space-y-6">
              {result.sections.map((section, index) => (
                <div key={index} className="border-t pt-4">
                  <h4 className="text-lg font-medium mb-2">{section.title}</h4>
                  <div className="prose prose-sm max-w-none">
                    {section.content ? (
                      <div dangerouslySetInnerHTML={{ __html: section.content }} />
                    ) : (
                      <p className="text-gray-500 italic">No content generated</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SingleScopeResult; 