import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/pseo/databaseService';
import { fullSiteAnalysisService } from '../services/pseo/fullSiteAnalysisService';
import { useUser } from '../../../base/contextapi/UserContext';
import type { PSEOClient, PSEOWebsite } from '../types';
import PSEOLayout from '../components/PSEOLayout';

// Temporary simple components while we fix the imports
const AnalysisTypeSelector: React.FC<any> = ({ analysisTypes, selectedTypes, onTypesChange, onOptionsChange }) => (
  <div className="space-y-4">
    {analysisTypes.map((type: any) => (
      <div key={type.id} className="flex items-center gap-3 p-3 border rounded">
        <input
          type="checkbox"
          checked={selectedTypes.includes(type.id)}
          onChange={() => {
            const newTypes = selectedTypes.includes(type.id)
              ? selectedTypes.filter((id: string) => id !== type.id)
              : [...selectedTypes, type.id];
            onTypesChange(newTypes);
          }}
        />
        <div>
          <span className="text-lg">{type.icon}</span>
          <span className="ml-2 font-medium">{type.name}</span>
          <p className="text-sm text-gray-600">{type.description}</p>
        </div>
      </div>
    ))}
  </div>
);

const AnalysisProgress: React.FC<any> = ({ job, onCancel }) => (
  <div className="p-4">
    <h4 className="font-medium mb-2">{job.currentStep}</h4>
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className="bg-blue-600 h-2 rounded-full transition-all" 
        style={{ width: `${job.progress}%` }}
      />
    </div>
    <p className="text-sm text-gray-600 mt-2">{job.progress}% complete</p>
    {job.status === 'running' && (
      <button onClick={onCancel} className="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm">
        Cancel
      </button>
    )}
  </div>
);

const AnalysisResults: React.FC<any> = ({ website, results, analysisTypes, jobId }) => (
  <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
    <h3 className="font-semibold text-green-800 mb-4">🎉 Analysis Complete!</h3>
    <p className="text-green-700 mb-4">Analysis completed for {website?.name}</p>
    <p className="text-sm text-green-600 mb-6">
      Types: {analysisTypes.join(', ')}
    </p>
    
    {/* Primary Navigation to Unified Results */}
    <div className="space-y-4">
      <div className="text-center">
        <a
          href={`/full-site-analysis-results/${jobId}`}
          className="inline-flex items-center gap-3 px-6 py-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-lg font-medium shadow-md"
        >
          <span>📊</span>
          <div>
            <div>View Complete Analysis Results</div>
            <div className="text-sm font-normal opacity-90">Overview • Pages • Keywords • Content • Backlinks</div>
          </div>
          <span>→</span>
        </a>
      </div>
      
      {/* Quick Access Links */}
      <div className="border-t border-green-200 pt-4">
        <h4 className="font-medium text-green-800 mb-3 text-center">Quick Access to Sections:</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {analysisTypes.includes('page_discovery') && (
            <a
              href={`/full-site-analysis-results/${jobId}#pages`}
              className="inline-flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200 transition-colors text-sm"
            >
              <span>🔍</span>
              <span>Pages</span>
            </a>
          )}
          
          {analysisTypes.includes('keyword_research') && (
            <a
              href={`/full-site-analysis-results/${jobId}#keywords`}
              className="inline-flex items-center gap-2 px-3 py-2 bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200 transition-colors text-sm"
            >
              <span>🎯</span>
              <span>Keywords</span>
            </a>
          )}
          
          {analysisTypes.includes('content_generation') && (
            <a
              href={`/full-site-analysis-results/${jobId}#content`}
              className="inline-flex items-center gap-2 px-3 py-2 bg-green-100 text-green-800 rounded-md hover:bg-green-200 transition-colors text-sm"
            >
              <span>📝</span>
              <span>Content</span>
            </a>
          )}
          
          {analysisTypes.includes('backlink_analysis') && (
            <a
              href={`/full-site-analysis-results/${jobId}#backlinks`}
              className="inline-flex items-center gap-2 px-3 py-2 bg-orange-100 text-orange-800 rounded-md hover:bg-orange-200 transition-colors text-sm"
            >
              <span>🔗</span>
              <span>Backlinks</span>
            </a>
          )}
        </div>
      </div>
      
      {/* Secondary Actions */}
      <div className="border-t border-green-200 pt-4">
        <div className="flex justify-center gap-3">
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-green-100 text-green-800 border border-green-300 rounded-md hover:bg-green-200 transition-colors text-sm"
          >
            🔄 Run New Analysis
          </button>
        </div>
      </div>
    </div>
  </div>
);

const WebsiteSelector: React.FC<any> = ({ clients, selectedWebsite, onWebsiteSelect, loading }) => {
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [websites, setWebsites] = useState<PSEOWebsite[]>([]);

  useEffect(() => {
    if (selectedClientId) {
      const client = clients.find((c: PSEOClient) => c.id === selectedClientId);
      if (client) {
        // Load websites for selected client
        databaseService.getWebsitesByClientId(selectedClientId).then(setWebsites);
      }
    }
  }, [selectedClientId, clients]);

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">Select Client:</label>
        <select
          value={selectedClientId}
          onChange={(e) => setSelectedClientId(e.target.value)}
          className="w-full p-2 border rounded"
        >
          <option value="">Choose a client...</option>
          {clients.map((client: PSEOClient) => (
            <option key={client.id} value={client.id}>{client.name}</option>
          ))}
        </select>
      </div>
      
      {websites.length > 0 && (
        <div>
          <label className="block text-sm font-medium mb-2">Select Website:</label>
          <select
            value={selectedWebsite?.id || ''}
            onChange={(e) => {
              const website = websites.find(w => w.id === e.target.value);
              onWebsiteSelect(website || null);
            }}
            className="w-full p-2 border rounded"
          >
            <option value="">Choose a website...</option>
            {websites.map((website: PSEOWebsite) => (
              <option key={website.id} value={website.id}>{website.name}</option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
};

interface AnalysisJob {
  id: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentStep: string;
  results?: Record<string, any>;
  error?: string;
  startedAt?: string;
  completedAt?: string;
}

const FullSiteAnalysis: React.FC = () => {
  const { user } = useUser();
  
  // State for website selection
  const [clients, setClients] = useState<PSEOClient[]>([]);
  const [selectedWebsite, setSelectedWebsite] = useState<PSEOWebsite | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for analysis configuration
  const [selectedAnalysisTypes, setSelectedAnalysisTypes] = useState<string[]>([]);
  const [analysisOptions, setAnalysisOptions] = useState<Record<string, any>>({});

  // State for analysis execution
  const [currentJob, setCurrentJob] = useState<AnalysisJob | null>(null);
  const [analysisHistory, setAnalysisHistory] = useState<any[]>([]);

  useEffect(() => {
    if (user?.id) {
      loadClients();
      loadAnalysisHistory();
    }
  }, [user?.id]);

  useEffect(() => {
    if (selectedWebsite) {
      loadWebsiteAnalysisData();
    }
  }, [selectedWebsite]);

  // Polling for job updates when analysis is running
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (currentJob && ['queued', 'running'].includes(currentJob.status)) {
      interval = setInterval(async () => {
        await updateJobStatus();
      }, 2000); // Poll every 2 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentJob]);

  const loadClients = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const clientsData = await databaseService.getClientsByUserId(user.id);
      setClients(clientsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const loadAnalysisHistory = async () => {
    if (!user?.id) return;

    try {
      // Get recent full site analysis jobs
      const recentAudits = await databaseService.getAuditsByUserId(user.id, 10);
      setAnalysisHistory(recentAudits);
    } catch (err) {
      console.error('Failed to load analysis history:', err);
    }
  };

  const loadWebsiteAnalysisData = async () => {
    if (!selectedWebsite) return;

    try {
      setLoading(true);
      
      // Get existing analysis data
      const analysisData = await databaseService.getWebsiteAnalysis(selectedWebsite.id);
      
      // Get any running jobs
      const runningJobs = await databaseService.getRunningAgentJobs(selectedWebsite.id);
      
      if (runningJobs.length > 0) {
        const latestJob = runningJobs[0];
        setCurrentJob({
          id: latestJob.id,
          status: latestJob.status,
          progress: 50, // Will be updated by polling
          currentStep: latestJob.agent_name || 'Processing...',
          startedAt: latestJob.started_at
        });
      }

    } catch (err) {
      console.error('Failed to load website analysis data:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateJobStatus = async () => {
    if (!currentJob) return;

    try {
      const jobStatus = await fullSiteAnalysisService.getJobStatus(currentJob.id);
      
      if (jobStatus) {
        setCurrentJob(prev => prev ? {
          ...prev,
          status: jobStatus.status,
          progress: jobStatus.progress,
          currentStep: jobStatus.currentStep,
          results: jobStatus.results,
          error: jobStatus.error,
          completedAt: jobStatus.completedAt
        } : null);

        // If job completed, load updated analysis data
        if (['completed', 'failed', 'cancelled'].includes(jobStatus.status)) {
          await loadWebsiteAnalysisData();
          await loadAnalysisHistory();
        }
      }
    } catch (err) {
      console.error('Failed to update job status:', err);
    }
  };

  const calculateProgress = (jobData: any): number => {
    if (jobData.status === 'completed') return 100;
    if (jobData.status === 'failed' || jobData.status === 'cancelled') return 0;
    if (jobData.status === 'running') return 50; // Simplified - would be more sophisticated in production
    return 0;
  };

  const getCurrentStep = (jobData: any): string => {
    if (jobData.status === 'completed') return 'Analysis completed';
    if (jobData.status === 'failed') return 'Analysis failed';
    if (jobData.status === 'cancelled') return 'Analysis cancelled';
    return jobData.agent_name || 'Processing...';
  };

  const startAnalysis = async () => {
    if (!selectedWebsite || selectedAnalysisTypes.length === 0) {
      setError('Please select a website and at least one analysis type');
      return;
    }

    if (!user?.id) {
      setError('Please log in to perform analysis');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🚀 Starting full site analysis:', {
        website: selectedWebsite.domain,
        analysisTypes: selectedAnalysisTypes,
        options: analysisOptions
      });

      // **FIXED: Get the actual job ID first, then set currentJob**
      const result = await fullSiteAnalysisService.startFullSiteAnalysis(
        selectedWebsite,
        {
          analysisTypes: selectedAnalysisTypes,
          userId: user.id,
          websiteId: selectedWebsite.id,
          priority: 1
        }
      );
      
      if (result?.jobId) {
        // Only set currentJob once we have a real job ID
        setCurrentJob({
          id: result.jobId,
          status: 'queued',
          progress: 0,
          currentStep: 'Multi-agent analysis queued...',
          startedAt: new Date().toISOString()
        });
        console.log('✅ Started multi-agent analysis, job ID:', result.jobId);
      } else {
        throw new Error('No job ID returned from backend');
      }

      // The analysis is running in the background, polling will track progress
      console.log('🎉 Multi-agent analysis started successfully');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);
      console.error('❌ Full site analysis failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const cancelAnalysis = async () => {
    if (!currentJob) return;

    try {
      await databaseService.cancelAgentJob(currentJob.id);
      setCurrentJob(prev => prev ? { ...prev, status: 'cancelled' } : null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel analysis');
    }
  };

  const analysisTypeOptions = [
    {
      id: 'page_discovery',
      name: 'Page Discovery',
      description: 'Crawl and analyze all pages on the website',
      icon: '🔍',
      estimatedTime: '5-15 minutes'
    },
    {
      id: 'keyword_research',
      name: 'Keyword Research',
      description: 'Comprehensive keyword analysis and opportunities',
      icon: '🎯',
      estimatedTime: '10-20 minutes'
    },
    {
      id: 'content_generation',
      name: 'Content Analysis',
      description: 'Content gaps and generation opportunities',
      icon: '📝',
      estimatedTime: '15-30 minutes'
    },
    {
      id: 'backlink_analysis',
      name: 'Backlink Analysis',
      description: 'Analyze backlink profile and opportunities',
      icon: '🔗',
      estimatedTime: '10-25 minutes',
      comingSoon: true
    }
  ];

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Full Site Analysis
          </h1>
          <p className="text-muted-foreground">
            Comprehensive multi-agent analysis of your entire website using AI-powered agents
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Configuration */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Website Selection */}
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold text-foreground mb-4">
                1. Select Website
              </h2>
              <WebsiteSelector
                clients={clients}
                selectedWebsite={selectedWebsite}
                onWebsiteSelect={setSelectedWebsite}
                loading={loading}
              />
            </div>

            {/* Analysis Type Selection */}
            {selectedWebsite && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  2. Choose Analysis Types
                </h2>
                <AnalysisTypeSelector
                  analysisTypes={analysisTypeOptions}
                  selectedTypes={selectedAnalysisTypes}
                  onTypesChange={setSelectedAnalysisTypes}
                  onOptionsChange={setAnalysisOptions}
                />
              </div>
            )}

            {/* Start Analysis Button */}
            {selectedWebsite && selectedAnalysisTypes.length > 0 && (
              <div className="bg-card rounded-lg border p-6">
                <h2 className="text-xl font-semibold text-foreground mb-4">
                  3. Start Analysis
                </h2>
                
                {!currentJob && (
                  <div className="space-y-4">
                    <div className="text-muted-foreground">
                      <p>Ready to analyze <strong>{selectedWebsite.name}</strong></p>
                      <p>Selected analysis types: {selectedAnalysisTypes.length}</p>
                      <p>Estimated time: 15-45 minutes</p>
                    </div>
                    
                    <button
                      onClick={startAnalysis}
                      disabled={loading}
                      className="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed py-3 px-6 rounded-lg font-medium transition-colors"
                    >
                      {loading ? 'Starting Analysis...' : '🚀 Start Full Site Analysis'}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Column - Progress & Results */}
          <div className="space-y-6">
            
            {/* Current Analysis Progress */}
            {currentJob && (
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">
                  Analysis Progress
                </h3>
                <AnalysisProgress
                  job={currentJob}
                  onCancel={cancelAnalysis}
                />
              </div>
            )}

            {/* Quick Stats */}
            {selectedWebsite && (
              <div className="bg-card rounded-lg border p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">
                  Website Overview
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Domain:</span>
                    <span className="text-foreground font-medium">{selectedWebsite.domain}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <span className="text-green-600 font-medium">{selectedWebsite.status}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Added:</span>
                    <span className="text-foreground">{new Date(selectedWebsite.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Recent Analysis History */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                Recent Analysis
              </h3>
              {analysisHistory.length > 0 ? (
                <div className="space-y-3">
                  {analysisHistory.slice(0, 5).map((analysis) => (
                    <div key={analysis.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div>
                        <p className="text-sm font-medium text-foreground">{analysis.website_name}</p>
                        <p className="text-xs text-muted-foreground">{new Date(analysis.created_at).toLocaleDateString()}</p>
                      </div>
                      <div className={`px-2 py-1 rounded text-xs font-medium ${
                        analysis.status === 'completed' ? 'bg-green-100 text-green-700' :
                        analysis.status === 'failed' ? 'bg-red-100 text-red-700' :
                        'bg-blue-100 text-blue-700'
                      }`}>
                        {analysis.status}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">No recent analysis found</p>
              )}
            </div>
          </div>
        </div>

        {/* Results Section */}
        {currentJob?.status === 'completed' && currentJob.results && (
          <div className="mt-8">
            <AnalysisResults
              website={selectedWebsite!}
              results={currentJob.results}
              analysisTypes={selectedAnalysisTypes}
              jobId={currentJob.id}
            />
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default FullSiteAnalysis; 