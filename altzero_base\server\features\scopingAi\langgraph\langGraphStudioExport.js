// =====================================================
// LANGGRAPH STUDIO EXPORT FOR SCOPINGAI PROPOSAL WORKFLOW
// =====================================================

const { StateGraph, START, END, Annotation } = require('@langchain/langgraph');

// Define the state schema using LangGraph Annotation
const ScopingAiState = Annotation.Root({
  // Core workflow identifiers
  workflow_id: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),
  user_id: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),
  client_id: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),

  // Input data
  client_data: Annotation({ reducer: (x, y) => ({ ...x, ...y }), default: () => ({}) }),
  template_id: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),
  requirements: Annotation({ reducer: (x, y) => y ?? x, default: () => [] }),
  knowledge_documents: Annotation({ reducer: (x, y) => y ?? x, default: () => [] }),
  ai_prompts: Annotation({ reducer: (x, y) => y ?? x, default: () => [] }),

  // Workflow state
  current_step: Annotation({ reducer: (x, y) => y ?? x, default: () => 'initializing' }),
  progress: Annotation({ reducer: (x, y) => y ?? x, default: () => 0 }),
  status: Annotation({ reducer: (x, y) => y ?? x, default: () => 'pending' }),
  errors: Annotation({ reducer: (x, y) => [...(x || []), ...(y || [])], default: () => [] }),

  // Generated content
  executive_summary: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),
  proposal_sections: Annotation({ reducer: (x, y) => y ?? x, default: () => [] }),
  final_proposal: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),

  // Analysis data
  client_analysis: Annotation({ reducer: (x, y) => ({ ...x, ...y }), default: () => ({}) }),
  research_data: Annotation({ reducer: (x, y) => ({ ...x, ...y }), default: () => ({}) }),
  knowledge_context: Annotation({ reducer: (x, y) => y ?? x, default: () => [] }),

  // Metadata
  processing_time: Annotation({ reducer: (x, y) => y ?? x, default: () => 0 }),
  api_calls_made: Annotation({ reducer: (x, y) => [...(x || []), ...(y || [])], default: () => [] }),
  total_cost: Annotation({ reducer: (x, y) => (x || 0) + (y || 0), default: () => 0 }),
  started_at: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),
  completed_at: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),
  last_updated: Annotation({ reducer: (x, y) => y ?? x, default: () => null }),

  // Node execution data
  node_data: Annotation({ reducer: (x, y) => ({ ...x, ...y }), default: () => ({}) }),

  // Configuration
  config: Annotation({ reducer: (x, y) => ({ ...x, ...y }), default: () => ({}) })
});

// Node functions for LangGraph Studio
async function validationNode(state) {
  console.log('🔍 Validation Node: Starting input validation');

  // Simulate validation logic
  const errors = [];

  if (!state.client_data || Object.keys(state.client_data).length === 0) {
    errors.push('Client data is required');
  }

  if (!state.template_id) {
    errors.push('Template ID is required');
  }

  if (!state.requirements || state.requirements.length === 0) {
    errors.push('Requirements are required');
  }

  const hasErrors = errors.length > 0;

  return {
    current_step: 'validation',
    progress: 10,
    status: hasErrors ? 'failed' : 'running',
    errors: hasErrors ? errors.map(msg => ({
      node_name: 'validation',
      error_message: msg,
      error_code: 'VALIDATION_ERROR',
      timestamp: new Date().toISOString(),
      recoverable: false
    })) : [],
    last_updated: new Date().toISOString(),
    node_data: {
      ...state.node_data,
      validation: {
        passed: !hasErrors,
        errors: errors,
        validated_at: new Date().toISOString()
      }
    }
  };
}

async function knowledgeRetrievalNode(state) {
  console.log('📚 Knowledge Retrieval Node: Retrieving relevant knowledge');

  // Simulate knowledge retrieval
  const mockKnowledgeContext = [
    {
      document_id: 'doc_1',
      title: 'Industry Best Practices',
      content: 'Sample industry knowledge...',
      relevance_score: 0.85
    },
    {
      document_id: 'doc_2',
      title: 'Technical Standards',
      content: 'Sample technical standards...',
      relevance_score: 0.78
    }
  ];

  return {
    current_step: 'knowledge_retrieval',
    progress: 25,
    status: 'running',
    knowledge_context: mockKnowledgeContext,
    last_updated: new Date().toISOString(),
    node_data: {
      ...state.node_data,
      knowledge_retrieval: {
        documents_retrieved: mockKnowledgeContext.length,
        average_relevance: 0.815,
        retrieved_at: new Date().toISOString()
      }
    }
  };
}

async function clientAnalysisNode(state) {
  console.log('🏢 Client Analysis Node: Analyzing client requirements');
  
  // Simulate client analysis
  const mockClientAnalysis = {
    industry: state.client_data.industry || 'Technology',
    company_size: state.client_data.company_size || 'Medium',
    key_challenges: ['Digital transformation', 'Process optimization'],
    budget_range: state.client_data.budget_range || 'Medium',
    timeline: state.client_data.timeline || '3-6 months',
    decision_makers: state.client_data.decision_makers || ['CTO', 'CEO']
  };
  
  return {
    current_step: 'client_analysis',
    progress: 40,
    status: 'running',
    client_analysis: mockClientAnalysis,
    last_updated: new Date().toISOString(),
    node_data: {
      client_analysis: {
        analysis_completed: true,
        insights_generated: Object.keys(mockClientAnalysis).length,
        analyzed_at: new Date().toISOString()
      }
    }
  };
}

async function researchAnalysisNode(state) {
  console.log('🔬 Research Analysis Node: Conducting market research');
  
  // Simulate research analysis
  const mockResearchData = {
    market_trends: ['AI adoption increasing', 'Remote work solutions in demand'],
    competitive_landscape: ['Competitor A', 'Competitor B'],
    industry_insights: ['Digital transformation priority', 'Cost optimization focus'],
    technology_trends: ['Cloud migration', 'AI/ML integration']
  };
  
  return {
    current_step: 'research_analysis',
    progress: 55,
    status: 'running',
    research_data: mockResearchData,
    last_updated: new Date().toISOString(),
    node_data: {
      research_analysis: {
        research_completed: true,
        data_points_collected: 12,
        researched_at: new Date().toISOString()
      }
    }
  };
}

async function executiveSummaryNode(state) {
  console.log('📋 Executive Summary Node: Generating executive summary');
  
  // Simulate executive summary generation
  const mockExecutiveSummary = {
    title: 'Digital Transformation Proposal',
    overview: 'This proposal outlines a comprehensive digital transformation strategy...',
    key_benefits: ['Increased efficiency', 'Cost reduction', 'Improved customer experience'],
    investment_required: '$500,000 - $750,000',
    timeline: '6-9 months',
    roi_projection: '200% within 18 months'
  };
  
  return {
    current_step: 'executive_summary',
    progress: 70,
    status: 'running',
    executive_summary: mockExecutiveSummary,
    last_updated: new Date().toISOString(),
    node_data: {
      executive_summary: {
        summary_generated: true,
        word_count: 250,
        generated_at: new Date().toISOString()
      }
    }
  };
}

async function sectionGenerationNode(state) {
  console.log('📝 Section Generation Node: Generating proposal sections');
  
  // Simulate section generation
  const mockProposalSections = [
    {
      section_id: 'technical_approach',
      title: 'Technical Approach',
      content: 'Our technical approach leverages modern cloud technologies...',
      order: 1
    },
    {
      section_id: 'implementation_plan',
      title: 'Implementation Plan',
      content: 'The implementation will be conducted in three phases...',
      order: 2
    },
    {
      section_id: 'team_structure',
      title: 'Team Structure',
      content: 'Our dedicated team consists of experienced professionals...',
      order: 3
    }
  ];
  
  return {
    current_step: 'section_generation',
    progress: 85,
    status: 'running',
    proposal_sections: mockProposalSections,
    last_updated: new Date().toISOString(),
    node_data: {
      section_generation: {
        sections_generated: mockProposalSections.length,
        total_word_count: 1500,
        generated_at: new Date().toISOString()
      }
    }
  };
}

async function qualityReviewNode(state) {
  console.log('✅ Quality Review Node: Reviewing proposal quality');
  
  // Simulate quality review
  const qualityScore = 0.87;
  const qualityThreshold = state.config.quality_threshold || 0.7;
  const passed = qualityScore >= qualityThreshold;
  
  return {
    current_step: 'quality_review',
    progress: 95,
    status: 'running',
    last_updated: new Date().toISOString(),
    node_data: {
      quality_review: {
        quality_score: qualityScore,
        passed_quality_threshold: passed,
        review_criteria: ['Completeness', 'Accuracy', 'Clarity', 'Relevance'],
        reviewed_at: new Date().toISOString(),
        retry_count: 0
      }
    }
  };
}

async function finalizationNode(state) {
  console.log('🎯 Finalization Node: Finalizing proposal');

  // Simulate final proposal assembly
  const mockFinalProposal = {
    proposal_id: state.workflow_id,
    title: state.executive_summary?.title || 'Business Proposal',
    executive_summary: state.executive_summary,
    sections: state.proposal_sections,
    appendices: [],
    metadata: {
      generated_at: new Date().toISOString(),
      version: '1.0',
      total_pages: 25,
      word_count: 5000
    }
  };

  return {
    current_step: 'finalization',
    progress: 100,
    status: 'completed',
    final_proposal: mockFinalProposal,
    completed_at: new Date().toISOString(),
    last_updated: new Date().toISOString(),
    processing_time: Date.now() - new Date(state.started_at).getTime(),
    node_data: {
      ...state.node_data,
      finalization: {
        proposal_finalized: true,
        final_word_count: 5000,
        finalized_at: new Date().toISOString()
      }
    }
  };
}

// Conditional routing functions
function shouldContinueAfterValidation(state) {
  return state.status === 'failed' ? 'end' : 'retrieve_knowledge';
}

function shouldContinueAfterKnowledge(state) {
  return state.status === 'failed' ? 'end' : 'analyze_client';
}

function shouldContinueAfterClientAnalysis(state) {
  return state.status === 'failed' ? 'end' : 'conduct_research';
}

function shouldContinueAfterResearch(state) {
  return state.status === 'failed' ? 'end' : 'create_summary';
}

function shouldContinueAfterExecutiveSummary(state) {
  return state.status === 'failed' ? 'end' : 'generate_sections';
}

function shouldContinueAfterSectionGeneration(state) {
  if (state.status === 'failed') return 'end';

  // Check if quality review is enabled
  const qualityThreshold = state.config?.quality_threshold || 0;
  return qualityThreshold > 0 ? 'review_quality' : 'finalize_proposal';
}

function shouldContinueAfterQualityReview(state) {
  if (state.status === 'failed') return 'end';

  // Check if quality review passed or if we should retry
  const qualityData = state.node_data?.quality_review;
  if (qualityData && !qualityData.passed_quality_threshold && qualityData.retry_count < 2) {
    return 'generate_sections'; // Retry section generation
  }
  return 'finalize_proposal';
}

// Create and export the workflow graph
function createProposalWorkflow() {
  const workflow = new StateGraph(ScopingAiState)
    // Add nodes (using different names to avoid conflicts with state attributes)
    .addNode('validate_input', validationNode)
    .addNode('retrieve_knowledge', knowledgeRetrievalNode)
    .addNode('analyze_client', clientAnalysisNode)
    .addNode('conduct_research', researchAnalysisNode)
    .addNode('create_summary', executiveSummaryNode)
    .addNode('generate_sections', sectionGenerationNode)
    .addNode('review_quality', qualityReviewNode)
    .addNode('finalize_proposal', finalizationNode)
    
    // Add edges
    .addEdge(START, 'validate_input')
    .addConditionalEdges('validate_input', shouldContinueAfterValidation, {
      'retrieve_knowledge': 'retrieve_knowledge',
      'end': END
    })
    .addConditionalEdges('retrieve_knowledge', shouldContinueAfterKnowledge, {
      'analyze_client': 'analyze_client',
      'end': END
    })
    .addConditionalEdges('analyze_client', shouldContinueAfterClientAnalysis, {
      'conduct_research': 'conduct_research',
      'end': END
    })
    .addConditionalEdges('conduct_research', shouldContinueAfterResearch, {
      'create_summary': 'create_summary',
      'end': END
    })
    .addConditionalEdges('create_summary', shouldContinueAfterExecutiveSummary, {
      'generate_sections': 'generate_sections',
      'end': END
    })
    .addConditionalEdges('generate_sections', shouldContinueAfterSectionGeneration, {
      'review_quality': 'review_quality',
      'finalize_proposal': 'finalize_proposal',
      'end': END
    })
    .addConditionalEdges('review_quality', shouldContinueAfterQualityReview, {
      'generate_sections': 'generate_sections',
      'finalize_proposal': 'finalize_proposal',
      'end': END
    })
    .addEdge('finalize_proposal', END);

  return workflow.compile();
}

// Export the workflow for LangGraph Studio
const proposalWorkflow = createProposalWorkflow();

module.exports = {
  proposalWorkflow,
  ScopingAiState,
  createProposalWorkflow
};
