import axios from "axios";
import {
  GeneratedDocumentData,
  DocumentTheme,
  Section,
} from "../types/documentTypes";
import {
  DocumentGenerationData,
  Client,
  PromptTemplate,
  ReferenceDocument,
} from "../types/document-form";
import { config, apiEndpoints, defaults } from "../config/environment";
import { supabase } from "../lib/supabase";
import { convertContentToBlocks } from "../utils/documentUtils";

// Create axios instance with default config
const api = axios.create({
  baseURL: config.apiUrl,
  timeout: defaults.apiTimeout,
  headers: {
    "Content-Type": "application/json",
  },
});

// API Health Check
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await api.get(apiEndpoints.health);
    return response.status === 200;
  } catch (error) {
    console.warn("API health check failed:", error);
    return false;
  }
};

// Add a function to get user ID
const getUserId = async (): Promise<string | null> => {
  try {
    // Use the already imported supabase client
    const {
      data: { user },
    } = await supabase.auth.getUser();
    return user?.id || null;
  } catch (error) {
    console.warn("Unable to get user ID:", error);
    return null;
  }
};

// Document Generation Service
export interface GenerationCallbacks {
  onStarted?: (data: any) => void;
  onProgress?: (data: any) => void; // Add the missing progress callback
  onResearch?: (data: any) => void;
  onSummary?: (data: any) => void;
  onSection?: (data: any) => void;
  onCompleted?: (data: any) => void;
  onEnd?: (data: any) => void;
  onError?: (error: any) => void;
}

export const generateDocument = async (
  documentData: DocumentGenerationData,
  preserveOriginalContent: boolean,
  callbacks: GenerationCallbacks,
  eventSourceRef: React.MutableRefObject<EventSource | null>
): Promise<EventSource> => {
  const documentId = `doc_${Date.now()}_${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  console.log("🚀 Starting document generation...");
  console.log("Document ID:", documentId);
  console.log("Input data:", documentData);
  console.log("Preserve original content:", preserveOriginalContent);

  // Check for required data
  if (!documentData.title) {
    throw new Error("Document title is required");
  }

  if (!documentData.client?.name) {
    throw new Error("Client name is required");
  }

  // Close any existing EventSource
  if (eventSourceRef.current) {
    eventSourceRef.current.close();
    eventSourceRef.current = null;
  }

  // Generate a consistent document ID that can be used for storage and retrieval
  console.log("Generated document ID:", documentId);

  // Get user ID for Pinecone filtering
  const userId = await getUserId();

  // Prepare the request data for the API
  const apiRequestData = {
    client: documentData.client,
    template: documentData.template,
    requirements: documentData.requirements,
    resources: [], // Add any resources if needed
    aiPrompts: documentData.aiPrompts,
    project: documentData.project,
    referenceDocument: documentData.referenceDocument,
    preserveOriginalContent: preserveOriginalContent,
    selectedKnowledgeDocuments:
      documentData.requirements?.selectedKnowledgeDocuments || [],
    documentRequirements: documentData.requirements?.documentRequirements || {},
    userId: userId, // Add userId for Pinecone filtering
  };

  console.log("📤 Request data prepared:", apiRequestData);
  console.log(
    "🔍 Knowledge base documents:",
    apiRequestData.selectedKnowledgeDocuments
  );
  console.log("📝 Document requirements:", apiRequestData.documentRequirements);

  // Continue with the rest of the function...

  try {
    // Check if we have knowledge base documents to include
    if (
      documentData.requirements?.selectedKnowledgeDocuments &&
      documentData.requirements.selectedKnowledgeDocuments.length > 0
    ) {
      console.log(
        `📚 Including ${documentData.requirements.selectedKnowledgeDocuments.length} knowledge base documents`
      );

      // Import the knowledge service to get document details
      const { knowledgeService } = await import(
        "../../../knowledge/services/knowledgeService"
      );

      try {
        // Get full document data from knowledge base
        const allDocs = await knowledgeService.getDocuments();
        const selectedDocs = allDocs.filter((doc) =>
          documentData.requirements!.selectedKnowledgeDocuments!.includes(
            doc.id
          )
        );

        console.log(
          `✅ Found ${selectedDocs.length} knowledge base documents:`,
          selectedDocs.map((d) => d.name)
        );

        // Note: Document content will be retrieved by the backend from Pinecone
        // We just need to pass the document IDs
      } catch (error) {
        console.warn("⚠️  Failed to fetch knowledge base documents:", error);
        // Continue with generation but log the warning
      }
    }

    // Create the EventSource connection directly to the streaming endpoint
    const streamUrl = `${config.apiUrl}/api/scopingai/proposals/stream`;
    console.log("🌊 Creating direct EventSource connection to:", streamUrl);

    // Since the server expects a POST but EventSource only does GET,
    // we need to use a different approach. Let's create a custom EventSource-like implementation
    // using fetch with a ReadableStream

    // Create the POST request for streaming
    const response = await fetch(streamUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": "scopingai",
      },
      body: JSON.stringify(apiRequestData),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log("📝 Connected to streaming endpoint");

    // Initialize document storage immediately with real data
    const initialDocumentData = {
      documentData: {
        title: apiRequestData.project.title,
        client: apiRequestData.client.name,
        type: "Scoping Document",
        sections: [],
      },
      sections: [],
      overview: "",
      status: "generating",
      documentId: documentId,
      clientId: apiRequestData.client.id,
      startedAt: new Date().toISOString(),
      preserveOriginalContent: preserveOriginalContent,
      knowledgeBaseUsed:
        (apiRequestData.selectedKnowledgeDocuments?.length || 0) > 0,
      knowledgeDocumentCount:
        apiRequestData.selectedKnowledgeDocuments?.length || 0,
    };

    // Store initial data
    sessionStorage.setItem(
      `document_${documentId}`,
      JSON.stringify(initialDocumentData)
    );
    console.log("💾 Stored initial document data with ID:", documentId);

    // Process the streaming response
    if (!response.body) {
      throw new Error("No response body for streaming");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    // Create a mock EventSource object to maintain compatibility
    const mockEventSource = new EventTarget() as EventSource & EventTarget;
    eventSourceRef.current = mockEventSource as EventSource;

    // Process the stream
    const processStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("event: ")) {
              const eventType = line.substring(7).trim();
              continue;
            }

            if (line.startsWith("data: ")) {
              const data = line.substring(6).trim();
              if (data) {
                try {
                  const parsedData = JSON.parse(data);

                  // Determine event type based on data structure

                  // PRIORITY: Handle progress events first (real-time updates)
                  if (
                    parsedData.progress !== undefined ||
                    parsedData.current_step ||
                    (parsedData.message && parsedData.status === "running")
                  ) {
                    console.log("🔥 PROGRESS EVENT DETECTED:", parsedData);
                    console.log(
                      "🔥 Progress:",
                      parsedData.progress,
                      "Step:",
                      parsedData.current_step,
                      "Message:",
                      parsedData.message
                    );
                    if (callbacks.onProgress) {
                      callbacks.onProgress(parsedData);
                    }
                  } else if (
                    parsedData.message === "Starting document generation..."
                  ) {
                    console.log("🚀 Started event:", parsedData);
                    const callbackData = {
                      ...parsedData,
                      documentId: documentId,
                    };
                    if (callbacks.onStarted) callbacks.onStarted(callbackData);
                  } else if (
                    parsedData.status === "started" &&
                    parsedData.message?.includes("Researching")
                  ) {
                    console.log("🔍 Research started event:", parsedData);
                    if (callbacks.onResearch) callbacks.onResearch(parsedData);
                  } else if (
                    parsedData.status === "completed" &&
                    parsedData.content
                  ) {
                    if (
                      parsedData.content.includes("summary") ||
                      parsedData.content.includes("executive")
                    ) {
                      console.log("📋 Summary event:", parsedData);

                      // Update stored data with summary
                      const storedData = JSON.parse(
                        sessionStorage.getItem(`document_${documentId}`) || "{}"
                      );
                      storedData.overview = parsedData.content;
                      storedData.status = "summarizing";
                      sessionStorage.setItem(
                        `document_${documentId}`,
                        JSON.stringify(storedData)
                      );

                      if (callbacks.onSummary) callbacks.onSummary(parsedData);
                    } else {
                      console.log("🔍 Research completed event:", parsedData);

                      // Update stored data with research
                      const storedData = JSON.parse(
                        sessionStorage.getItem(`document_${documentId}`) || "{}"
                      );
                      storedData.research = parsedData.content;
                      storedData.status = "researching";
                      sessionStorage.setItem(
                        `document_${documentId}`,
                        JSON.stringify(storedData)
                      );

                      if (callbacks.onResearch)
                        callbacks.onResearch(parsedData);
                    }
                  } else if (parsedData.section) {
                    console.log("📄 Section event:", parsedData);

                    // Update stored data with new section
                    const storedData = JSON.parse(
                      sessionStorage.getItem(`document_${documentId}`) || "{}"
                    );
                    if (!storedData.sections) storedData.sections = [];

                    // Convert section to the expected format
                    const newSection = {
                      id:
                        parsedData.section.id ||
                        `section-${storedData.sections.length + 1}`,
                      title:
                        parsedData.section.title ||
                        `Section ${storedData.sections.length + 1}`,
                      content: parsedData.section.content || "",
                      blocks: convertContentToBlocks(
                        parsedData.section.content || ""
                      ),
                      status: "completed",
                      order: storedData.sections.length + 1,
                    };

                    storedData.sections.push(newSection);

                    // Also update the main document sections
                    if (!storedData.documentData.sections)
                      storedData.documentData.sections = [];
                    storedData.documentData.sections.push({
                      id: newSection.id,
                      title: newSection.title,
                      content: newSection.content,
                    });

                    storedData.status = "generating_sections";
                    sessionStorage.setItem(
                      `document_${documentId}`,
                      JSON.stringify(storedData)
                    );

                    if (callbacks.onSection) callbacks.onSection(parsedData);
                  } else if (parsedData.proposal) {
                    console.log("✅ Completed event:", parsedData);

                    // Update stored data with completion status
                    const storedData = JSON.parse(
                      sessionStorage.getItem(`document_${documentId}`) || "{}"
                    );
                    storedData.status = "completed";
                    storedData.completedAt = new Date().toISOString();
                    sessionStorage.setItem(
                      `document_${documentId}`,
                      JSON.stringify(storedData)
                    );

                    // Update callbacks with our document ID
                    const callbackData = {
                      ...parsedData,
                      documentId: documentId,
                    };
                    if (callbacks.onCompleted)
                      callbacks.onCompleted(callbackData);
                  } else if (parsedData.message === "Stream complete") {
                    console.log("🏁 End event:", parsedData);

                    // Update callbacks with our document ID
                    const callbackData = {
                      ...parsedData,
                      documentId: documentId,
                    };
                    if (callbacks.onEnd) callbacks.onEnd(callbackData);
                  } else if (parsedData.error) {
                    console.error("❌ Error event:", parsedData);
                    if (callbacks.onError) {
                      callbacks.onError({
                        message: parsedData.error,
                        details: parsedData.details,
                        type: "generation_error",
                      });
                    }
                  }
                } catch (parseError) {
                  console.warn("⚠️  Failed to parse streaming data:", data);
                }
              }
            }
          }
        }
      } catch (streamError) {
        console.error("❌ Stream processing error:", streamError);
        if (callbacks.onError) {
          callbacks.onError({
            message:
              streamError instanceof Error
                ? streamError.message
                : "Stream processing failed",
            type: "stream_error",
          });
        }
      }
    };

    // Start processing the stream
    processStream();

    return mockEventSource as EventSource;
  } catch (error) {
    console.error("❌ Failed to start document generation:", error);

    if (callbacks.onError) {
      callbacks.onError({
        message:
          error instanceof Error ? error.message : "Failed to start generation",
        type: "initialization_error",
      });
    }

    throw error;
  }
};

// Document Management Services
export async function fetchDocument(documentId: string) {
  try {
    console.log(
      "fetchDocument called with ID:",
      documentId,
      "type:",
      typeof documentId
    );

    // Fetch document metadata from database
    const { data: dbData, error: dbError } = await supabase
      .from("scopingai_documents")
      .select("*")
      .eq("id", documentId)
      .single();

    console.log("Database query result:", { dbData, dbError });

    if (dbError) {
      console.error("Database error in fetchDocument:", dbError);
      throw dbError;
    }

    if (!dbData) {
      console.error("No document found with ID:", documentId);
      throw new Error(`Document with ID ${documentId} not found`);
    }

    console.log("Found document in database:", dbData);

    // Initialize the document data structure
    let documentData: any = {
      id: dbData.id,
      title: dbData.title,
      client: "Unknown Client", // Will be updated if we have client data
      type: "Scoping Document",
      sections: [],
      theme: null,
    };

    // If there's a proposal file path, fetch the markdown content
    if (dbData.proposalFilePath) {
      console.log("Document has proposalFilePath:", dbData.proposalFilePath);
      try {
        const markdownContent = await fetchDocumentFile(
          dbData.proposalFilePath
        );

        console.log(
          "Fetched markdown content length:",
          markdownContent?.length || 0
        );

        // Parse the markdown to extract theme and sections
        const { theme, sections } = parseMarkdownDocument(
          markdownContent,
          dbData.title
        );

        console.log("Parsed sections:", sections);
        console.log("Parsed theme:", theme);

        documentData.sections = sections;
        documentData.theme = theme;
      } catch (storageError) {
        console.warn(
          "Could not fetch document content from storage, using empty document:",
          storageError
        );
        // Create a default section if we can't load content
        documentData.sections = [
          {
            id: "section-1",
            title: "Document Content",
            content:
              "Content could not be loaded from file. Please edit this section.",
            status: "completed",
            order: 1,
          },
        ];
      }
    } else {
      console.log("Document has no proposalFilePath, creating default content");
      // No file path, create a default section with more helpful content
      documentData.sections = [
        {
          id: "section-1",
          title: "Introduction",
          content: `# ${dbData.title}

This document exists in your database but doesn't have saved content yet. 

**Document Details:**
- Title: ${dbData.title}
- Created: ${new Date(dbData.created_at).toLocaleDateString()}
- Status: ${dbData.status || "Draft"}

You can start editing this content right away. Use the Content Editor or Advanced Editor tabs to add sections, format text, and customize your document.

## Getting Started
1. Click on the "Content Editor" tab to add and edit sections
2. Use the "Advanced Editor" for block-based editing with drag & drop
3. Customize themes and branding in the "Theme & Branding" tab  
4. Save your changes when ready

Your edits will be automatically saved to this document.`,
          status: "completed",
          order: 1,
        },
      ];
    }

    // Try to get client name if client_id exists
    if (dbData.client_id) {
      try {
        const { data: clientData } = await supabase
          .from("scopingai_clients")
          .select("name")
          .eq("id", dbData.client_id)
          .single();

        if (clientData) {
          documentData.client = clientData.name;
        }
      } catch (clientError) {
        console.warn("Could not fetch client data:", clientError);
      }
    }

    return { document: documentData };
  } catch (error) {
    console.error("Error fetching document:", error);
    throw error;
  }
}

export async function fetchDocumentFile(filePath: string) {
  try {
    const { data, error } = await supabase.storage
      .from("scoping-proposals")
      .download(filePath);

    if (error) throw error;
    return await data.text();
  } catch (error) {
    console.error("Error fetching document file:", error);
    throw error;
  }
}

export async function saveDocument(
  documentData: GeneratedDocumentData,
  editableSections: Section[],
  documentTheme: DocumentTheme,
  documentDbId: string | null,
  clientId: string | null,
  promptTemplateId: string | null,
  scopeTemplateId: string | null,
  filePath: string | null
) {
  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData.user) {
      throw new Error("Not authenticated");
    }

    const user = userData.user;

    // Create a metadata object to store theme settings
    const themeMetadata = {
      colors: {
        heading: documentTheme.headingColor,
        subheading: documentTheme.subheadingColor,
        text: documentTheme.textColor,
        background: documentTheme.backgroundColor,
        accent: documentTheme.accentColor,
      },
      typography: {
        fontFamily: documentTheme.fontFamily,
        headingSize: documentTheme.headingSize,
        textSize: documentTheme.textSize,
      },
      layout: {
        hasHeader: documentTheme.header?.enabled || false,
        hasFooter: documentTheme.footer?.enabled || false,
        headerType: documentTheme.header?.type || "simple",
        footerType: documentTheme.footer?.type || "simple",
        showCoverPage: documentTheme.showCoverPage !== false,
      },
      hasLogo: !!documentTheme.logo,
      logoData: documentTheme.logo, // Store the full base64 data string
    };

    // 1. Convert document to markdown with theme metadata
    // Add a special metadata section at the top for theme data
    let markdown = `---
theme:
  headingColor: ${documentTheme.headingColor}
  subheadingColor: ${documentTheme.subheadingColor}
  textColor: ${documentTheme.textColor}
  backgroundColor: ${documentTheme.backgroundColor}
  accentColor: ${documentTheme.accentColor}
  fontFamily: ${documentTheme.fontFamily}
  headingSize: ${documentTheme.headingSize}
  textSize: ${documentTheme.textSize}
  header:
    enabled: ${documentTheme.header?.enabled || false}
    type: ${documentTheme.header?.type || "simple"}
    text: ${documentTheme.header?.text || ""}
    includePageNumbers: ${documentTheme.header?.includePageNumbers || false}
  footer:
    enabled: ${documentTheme.footer?.enabled || false}
    type: ${documentTheme.footer?.type || "simple"}
    text: ${documentTheme.footer?.text || ""}
    includePageNumbers: ${documentTheme.footer?.includePageNumbers || false}
  showCoverPage: ${documentTheme.showCoverPage !== false}
---
`;

    // Add the regular markdown content
    markdown += convertToMarkdown({
      ...documentData,
      sections: editableSections,
    });

    // 2. Determine file path - use existing path if available
    let saveFilePath = filePath;
    if (!saveFilePath) {
      // If no existing file path, create a new one
      const fileName = `${documentData.title
        .replace(/[^a-z0-9]/gi, "_")
        .toLowerCase()}.md`;
      saveFilePath = `${user.id}/${fileName}`;
    }

    // 3. Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from("scoping-proposals")
      .upload(saveFilePath, markdown, {
        contentType: "text/markdown",
        upsert: true,
      });

    if (uploadError) {
      throw uploadError;
    }

    // 4. Get or create scoping_document record
    let docId = documentDbId;
    if (docId) {
      // Update existing document
      const { error: updateError } = await supabase
        .from("scopingai_documents")
        .update({
          title: documentData.title,
          proposalFilePath: saveFilePath,
          updated_at: new Date().toISOString(),
        })
        .eq("id", docId);

      if (updateError) throw updateError;
    } else {
      // Create new document
      const { data: newDoc, error: insertError } = await supabase
        .from("scopingai_documents")
        .insert({
          title: documentData.title,
          proposalFilePath: saveFilePath,
          user_id: user.id,
          client_id: clientId,
          status: "created",
        })
        .select()
        .single();

      if (insertError) throw insertError;
      docId = newDoc.id;
    }

    return { documentId: docId, filePath: saveFilePath };
  } catch (error) {
    console.error("Error saving document:", error);
    throw error;
  }
}

export async function regenerateSectionWithAI(
  sectionContent: string,
  sectionTitle: string,
  enhancementType: string,
  projectContext: string
) {
  try {
    const response = await axios.post("/api/regenerate-section", {
      sectionContent,
      sectionTitle,
      enhancementType,
      projectContext,
    });

    return response.data.regeneratedContent;
  } catch (error) {
    console.error("Error regenerating section:", error);
    throw error;
  }
}

function parseMarkdownDocument(markdownContent: string, fallbackTitle: string) {
  const lines = markdownContent.split("\n");
  let themeData = null;
  let contentStart = 0;

  // Check if document starts with frontmatter
  if (lines[0] === "---") {
    let frontmatterEnd = -1;
    for (let i = 1; i < lines.length; i++) {
      if (lines[i] === "---") {
        frontmatterEnd = i;
        break;
      }
    }

    if (frontmatterEnd > 0) {
      const frontmatter = lines.slice(1, frontmatterEnd).join("\n");
      try {
        // Simple YAML parser for theme data
        const themeMatch = frontmatter.match(/theme:\s*([\s\S]*?)(?=\n\w|$)/);
        if (themeMatch && themeMatch[1]) {
          themeData = parseSimpleTheme(themeMatch[1]);
        }
      } catch (error) {
        console.warn("Could not parse theme frontmatter:", error);
      }
      contentStart = frontmatterEnd + 1;
    }
  }

  // Parse the rest as markdown content
  const contentLines = lines.slice(contentStart);
  const sections: Section[] = [];
  let currentSection: Section | null = null;
  let currentContent: string[] = [];
  let sectionCounter = 1;

  for (const line of contentLines) {
    if (line.startsWith("# ")) {
      // This is a main title, skip it
      continue;
    } else if (line.startsWith("## ")) {
      // Save previous section
      if (currentSection) {
        currentSection.content = currentContent.join("\n").trim();
        sections.push(currentSection);
      }

      // Start new section
      const sectionContent = "";
      currentSection = {
        id: `section-${sectionCounter}`,
        title: line.replace("## ", "").trim(),
        content: sectionContent,
        blocks: [], // Will be populated after content is set
        status: "completed" as const,
        order: sectionCounter,
      };
      currentContent = [];
      sectionCounter++;
    } else if (currentSection) {
      currentContent.push(line);
    }
  }

  // Save the last section
  if (currentSection) {
    currentSection.content = currentContent.join("\n").trim();
    sections.push(currentSection);
  }

  // Convert content to blocks for all sections
  const sectionsWithBlocks = sections.map((section) => ({
    ...section,
    blocks: convertContentToBlocks(section.content || ""),
  }));

  // If no sections found, create a default one
  if (sectionsWithBlocks.length === 0) {
    const defaultContent =
      contentLines.join("\n").trim() || "No content available.";
    sectionsWithBlocks.push({
      id: "section-1",
      title: "Document Content",
      content: defaultContent,
      blocks: convertContentToBlocks(defaultContent),
      status: "completed",
      order: 1,
    });
  }

  return { theme: themeData, sections: sectionsWithBlocks };
}

function parseSimpleTheme(themeData: string): DocumentTheme {
  // Simple parser for theme YAML data
  const theme: Record<string, any> = {};

  const lines = themeData.split("\n");
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.includes(":")) {
      const [key, value] = trimmed.split(":").map((s) => s.trim());
      if (key && value && value !== "") {
        // Remove quotes if present
        const cleanValue = value.replace(/['"]/g, "");
        theme[key] =
          cleanValue === "true"
            ? true
            : cleanValue === "false"
            ? false
            : cleanValue;
      }
    }
  }

  return theme as DocumentTheme;
}

// Simple markdown converter
function convertToMarkdown(document: GeneratedDocumentData): string {
  let markdown = `# ${document.title}\n\n`;

  if (document.client) {
    markdown += `**Client:** ${document.client}\n\n`;
  }

  if (document.type) {
    markdown += `**Document Type:** ${document.type}\n\n`;
  }

  for (const section of document.sections) {
    markdown += `## ${section.title}\n\n`;
    markdown += `${section.content}\n\n`;
  }

  return markdown;
}

// Export Document Services
export const exportDocumentToPDF = async (
  documentId: string
): Promise<Blob> => {
  try {
    const response = await api.get(apiEndpoints.exportPDF(documentId), {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    console.error("Error exporting document to PDF:", error);
    throw error;
  }
};

export const exportDocumentToWord = async (
  documentId: string
): Promise<Blob> => {
  try {
    const response = await api.get(apiEndpoints.exportWord(documentId), {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    console.error("Error exporting document to Word:", error);
    throw error;
  }
};

// Client Management Services
export const fetchClients = async (): Promise<Client[]> => {
  try {
    const response = await api.get(apiEndpoints.clients);
    return response.data;
  } catch (error) {
    console.error("Error fetching clients:", error);
    throw error;
  }
};

export const saveClient = async (
  clientData: Omit<Client, "id">
): Promise<Client> => {
  try {
    const response = await api.post(apiEndpoints.clients, clientData);
    return response.data;
  } catch (error) {
    console.error("Error saving client:", error);
    throw error;
  }
};

// Prompt Template Services
export const fetchPromptTemplates = async (): Promise<PromptTemplate[]> => {
  try {
    const response = await api.get(apiEndpoints.promptTemplates);
    return response.data;
  } catch (error) {
    console.error("Error fetching prompt templates:", error);
    throw error;
  }
};

export const savePromptTemplate = async (
  promptData: Omit<PromptTemplate, "id" | "created_at" | "updated_at">
): Promise<PromptTemplate> => {
  try {
    const response = await api.post(apiEndpoints.promptTemplates, promptData);
    return response.data;
  } catch (error) {
    console.error("Error saving prompt template:", error);
    throw error;
  }
};

// Reference Document Services
export const fetchReferenceDocuments = async (): Promise<
  ReferenceDocument[]
> => {
  try {
    const response = await api.get(apiEndpoints.referenceDocuments);
    return response.data;
  } catch (error) {
    console.error("Error fetching reference documents:", error);
    throw error;
  }
};

export const fetchReferenceDocument = async (
  docId: string
): Promise<ReferenceDocument | null> => {
  try {
    const response = await api.get(apiEndpoints.referenceDocumentById(docId));
    return response.data;
  } catch (error) {
    console.error("Error fetching reference document:", error);
    return null;
  }
};

// Document Library Services
export const fetchDocumentLibrary = async (): Promise<any[]> => {
  try {
    const response = await api.get(apiEndpoints.documentLibrary);
    return response.data;
  } catch (error) {
    console.error("Error fetching document library:", error);
    throw error;
  }
};

export const deleteDocument = async (documentId: string): Promise<void> => {
  try {
    await api.delete(apiEndpoints.documentById(documentId));
  } catch (error) {
    console.error("Error deleting document:", error);
    throw error;
  }
};

// Utility function to prepare document data for generation
export const prepareDocumentData = (
  projectInfo: any,
  clientData: any,
  selectedClient: any,
  selectedTemplate: string,
  referenceDocument: any,
  requirementsOption: string,
  requirementsText: string,
  selectedKnowledgeDocuments: string[],
  documentRequirements: Record<string, string>,
  aiPrompts: any,
  templates: any[]
): DocumentGenerationData => {
  // Check if we have enhanced AI prompts from the DocumentOverviewStep
  const enhancedAiPrompts = (window as any).enhancedAiPrompts;
  const finalAiPrompts = enhancedAiPrompts || aiPrompts;

  // Log the prompt information for debugging
  if (enhancedAiPrompts) {
    console.log(
      "🎯 Using enhanced AI prompts for generation:",
      enhancedAiPrompts
    );
  } else {
    console.log("📝 Using default AI prompts for generation:", aiPrompts);
  }

  return {
    title: projectInfo.title,
    client: {
      id: clientData.id || selectedClient?.id || null,
      name: clientData.name,
      contactPerson: clientData.contactPerson,
      email: clientData.email,
      phone: clientData.phone,
      industry: clientData.industry,
      company: clientData.company,
    },
    template: {
      id: selectedTemplate,
      name: templates.find((t) => t.id === selectedTemplate)?.name || "",
      sections:
        templates.find((t) => t.id === selectedTemplate)?.sections || [],
    },
    referenceDocument: referenceDocument,
    preserveOriginalContent: false,
    requirements: {
      method: requirementsOption,
      projectRequirements:
        requirementsOption === "requirements" ? requirementsText : "",
      hasUploadedFiles: requirementsOption === "upload",
      selectedKnowledgeDocuments:
        requirementsOption === "knowledge"
          ? selectedKnowledgeDocuments
          : undefined,
      documentRequirements:
        requirementsOption === "knowledge" ? documentRequirements : undefined,
    },
    aiPrompts: finalAiPrompts, // Use enhanced prompts if available
    project: {
      title: projectInfo.title,
      startDate: projectInfo.startDate,
      endDate: projectInfo.endDate,
      description: projectInfo.description,
      budget: projectInfo.budget,
    },
  };
};
