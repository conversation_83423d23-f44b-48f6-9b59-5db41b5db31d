import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUser } from "@base/contexts/UserContext";

export default function HomePage() {
  const navigate = useNavigate();
  const { user, isLoading } = useUser();

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (!isLoading && user) {
      navigate("/dashboard");
    }
  }, [user, isLoading, navigate]);

  // Redirect to landing page (this component only serves as a router)
  if (!isLoading && !user) {
    navigate("/landingpage");
  }

  // Show loading state while determining where to redirect
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );
}
