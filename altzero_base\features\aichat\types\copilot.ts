/**
 * CopilotKit Type Definitions for AltZero Platform
 * Enhanced types for CopilotKit integration with MCP and component interaction
 */

// Local type definition to avoid React dependency in types
type ReactNode = any;

// Core CopilotKit Action Types
export interface CopilotAction {
  name: string;
  description: string;
  parameters: CopilotActionParameter[];
  handler: (args: Record<string, any>) => Promise<any>;
  render?: (props: CopilotActionRenderProps) => ReactNode;
}

export interface CopilotActionParameter {
  name: string;
  type: "string" | "number" | "boolean" | "object" | "array";
  description: string;
  required?: boolean;
  enum?: any[];
  default?: any;
}

export interface CopilotActionRenderProps {
  status: "idle" | "inProgress" | "complete" | "failed";
  args: Record<string, any>;
  result?: any;
  error?: Error;
}

// Readable Context Types
export interface CopilotReadable {
  description: string;
  value: any;
  convert?: (value: any) => string;
}

export interface CopilotReadableState {
  user: CopilotUserReadable;
  application: CopilotApplicationReadable;
  database: CopilotDatabaseReadable;
  components: CopilotComponentReadable;
}

export interface CopilotUserReadable {
  id: string;
  email: string;
  name?: string;
  role: string;
  subscription: {
    plan: string;
    status: string;
  };
  preferences: Record<string, any>;
}

export interface CopilotApplicationReadable {
  currentPage: string;
  navigationHistory: string[];
  selectedDocuments: string[];
  chatSessions: CopilotChatSession[];
  notifications: CopilotNotification[];
}

export interface CopilotDatabaseReadable {
  schema: {
    tables: string[];
    views: string[];
    functions: string[];
  };
  recentQueries: CopilotQueryHistory[];
  connectionStatus: "connected" | "disconnected" | "error";
}

export interface CopilotComponentReadable {
  availableActions: CopilotComponentAction[];
  activeComponents: string[];
  componentStates: Record<string, any>;
}

// Chat and Session Types
export interface CopilotChatSession {
  id: string;
  title: string;
  messages: CopilotMessage[];
  createdAt: string;
  updatedAt: string;
  context?: CopilotSessionContext;
}

export interface CopilotMessage {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: string;
  type?: "text" | "action_result" | "query_result" | "component_interaction";
  metadata?: CopilotMessageMetadata;
}

export interface CopilotMessageMetadata {
  actionName?: string;
  actionResult?: any;
  queryExecuted?: string;
  queryResult?: any;
  componentInteraction?: {
    component: string;
    action: string;
    result: any;
  };
  sources?: CopilotSource[];
  processingTime?: number;
}

export interface CopilotSource {
  type: "database" | "document" | "component" | "api";
  name: string;
  query?: string;
  relevanceScore?: number;
  excerpt?: string;
}

export interface CopilotSessionContext {
  selectedDocuments: string[];
  databaseContext: {
    preferredTables: string[];
    recentQueries: string[];
  };
  componentContext: {
    activeComponents: string[];
    componentStates: Record<string, any>;
  };
}

// Action Result Types
export interface CopilotActionResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    executionTime: number;
    resourcesUsed: string[];
    cacheHit?: boolean;
  };
}

export interface CopilotQueryActionResult extends CopilotActionResult {
  query: string;
  results: Record<string, any>[];
  totalCount: number;
  executionTime: number;
  tableUsed: string;
}

export interface CopilotNavigationActionResult extends CopilotActionResult {
  fromPage: string;
  toPage: string;
  timestamp: string;
}

export interface CopilotComponentActionResult extends CopilotActionResult {
  component: string;
  action: string;
  previousState?: any;
  newState?: any;
}

// Component Interaction Types
export interface CopilotComponentAction {
  component: string;
  name: string;
  description: string;
  parameters: CopilotActionParameter[];
  category: "navigation" | "data" | "ui" | "workflow";
}

export interface CopilotComponentRegistry {
  [componentName: string]: {
    description: string;
    actions: CopilotComponentAction[];
    state?: any;
    getState?: () => any;
    setState?: (state: any) => void;
  };
}

// Notification Types
export interface CopilotNotification {
  id: string;
  type: "info" | "success" | "warning" | "error";
  title: string;
  message: string;
  timestamp: string;
  action?: {
    label: string;
    handler: () => void;
  };
  autoClose?: boolean;
  duration?: number;
}

// Query History Types
export interface CopilotQueryHistory {
  id: string;
  query: string;
  naturalLanguage: string;
  results: any[];
  executionTime: number;
  timestamp: string;
  success: boolean;
  error?: string;
}

// Configuration Types
export interface CopilotConfig {
  apiUrl: string;
  model: string;
  temperature: number;
  maxTokens: number;
  enableMCP: boolean;
  enableComponentInteraction: boolean;
  features: {
    databaseQuerying: boolean;
    documentChat: boolean;
    componentControl: boolean;
    workflowAutomation: boolean;
  };
  ui: {
    theme: "light" | "dark" | "auto";
    position: "sidebar" | "popup" | "inline";
    defaultOpen: boolean;
    shortcuts: Record<string, string>;
  };
}

// Provider Props Types
export interface CopilotProviderProps {
  children: ReactNode;
  config: CopilotConfig;
  apiKey?: string;
  userId?: string;
  onError?: (error: Error) => void;
  onActionComplete?: (action: string, result: CopilotActionResult) => void;
}

// Hook Return Types
export interface UseCopilotActionsReturn {
  actions: CopilotAction[];
  executeAction: (name: string, args: Record<string, any>) => Promise<CopilotActionResult>;
  isExecuting: boolean;
  lastResult?: CopilotActionResult;
  error?: Error;
}

export interface UseMCPClientReturn {
  isConnected: boolean;
  connectionStatus: "disconnected" | "connecting" | "connected" | "error";
  executeQuery: (query: string) => Promise<any>;
  introspectSchema: () => Promise<any>;
  error?: Error;
  lastQuery?: string;
  lastResult?: any;
}

export interface UseComponentInteractionReturn {
  availableComponents: string[];
  executeComponentAction: (
    component: string, 
    action: string, 
    params?: any
  ) => Promise<CopilotComponentActionResult>;
  getComponentState: (component: string) => any;
  setComponentState: (component: string, state: any) => void;
  isExecuting: boolean;
}

// UI Component Props
export interface CopilotChatProps {
  title?: string;
  placeholder?: string;
  showHistory?: boolean;
  enableMCP?: boolean;
  enableComponentInteraction?: boolean;
  onMessageSent?: (message: string) => void;
  onActionExecuted?: (action: string, result: any) => void;
  className?: string;
}

export interface CopilotSidebarProps {
  title?: string;
  position?: "left" | "right";
  defaultOpen?: boolean;
  width?: number;
  instructions?: string;
  features?: {
    databaseQuery: boolean;
    componentInteraction: boolean;
    documentChat: boolean;
  };
  onToggle?: (isOpen: boolean) => void;
}

export interface MCPIndicatorProps {
  status: "disconnected" | "connecting" | "connected" | "error";
  serverInfo?: {
    name: string;
    version: string;
    capabilities: string[];
  };
  onClick?: () => void;
  showDetails?: boolean;
}

export interface QueryResultViewProps {
  results: Record<string, any>[];
  query: string;
  totalCount?: number;
  executionTime: number;
  onExport?: (format: "csv" | "json" | "excel") => void;
  onRefresh?: () => void;
  pagination?: {
    page: number;
    pageSize: number;
    onPageChange: (page: number) => void;
  };
}

// Event Types
export interface CopilotEvent {
  type: string;
  timestamp: string;
  data: any;
  userId?: string;
  sessionId?: string;
}

export interface CopilotActionEvent extends CopilotEvent {
  type: "action_executed" | "action_failed" | "action_started";
  actionName: string;
  parameters: Record<string, any>;
  result?: CopilotActionResult;
  error?: Error;
}

export interface CopilotQueryEvent extends CopilotEvent {
  type: "query_executed" | "query_failed" | "query_started";
  naturalLanguage: string;
  sqlQuery: string;
  results?: any[];
  error?: Error;
  executionTime?: number;
}

export interface CopilotNavigationEvent extends CopilotEvent {
  type: "navigation" | "component_interaction";
  fromPage?: string;
  toPage?: string;
  component?: string;
  action?: string;
}

// Error Types
export interface CopilotError extends Error {
  code: string;
  context?: {
    action?: string;
    component?: string;
    query?: string;
    userId?: string;
  };
  retryable: boolean;
}

// State Management Types
export interface CopilotState {
  isConnected: boolean;
  currentSession?: CopilotChatSession;
  sessions: CopilotChatSession[];
  notifications: CopilotNotification[];
  queryHistory: CopilotQueryHistory[];
  componentRegistry: CopilotComponentRegistry;
  config: CopilotConfig;
  user?: CopilotUserReadable;
}

export interface CopilotActions {
  // Session Management
  createSession: (title?: string) => void;
  selectSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  
  // Message Management
  sendMessage: (content: string) => Promise<void>;
  addMessage: (message: CopilotMessage) => void;
  
  // Action Execution
  executeAction: (name: string, args: Record<string, any>) => Promise<CopilotActionResult>;
  
  // Query Execution
  executeQuery: (query: string) => Promise<any>;
  
  // Component Interaction
  interactWithComponent: (
    component: string, 
    action: string, 
    params?: any
  ) => Promise<CopilotComponentActionResult>;
  
  // Notifications
  addNotification: (notification: Omit<CopilotNotification, "id" | "timestamp">) => void;
  removeNotification: (notificationId: string) => void;
  
  // Configuration
  updateConfig: (config: Partial<CopilotConfig>) => void;
} 