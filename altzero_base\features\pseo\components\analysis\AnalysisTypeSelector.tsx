import React, { useState } from 'react';

interface AnalysisType {
  id: string;
  name: string;
  description: string;
  icon: string;
  estimatedTime: string;
  comingSoon?: boolean;
}

interface AnalysisTypeSelectorProps {
  analysisTypes: AnalysisType[];
  selectedTypes: string[];
  onTypesChange: (types: string[]) => void;
  onOptionsChange: (options: Record<string, any>) => void;
}

export const AnalysisTypeSelector: React.FC<AnalysisTypeSelectorProps> = ({
  analysisTypes,
  selectedTypes,
  onTypesChange,
  onOptionsChange
}) => {
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [options, setOptions] = useState<Record<string, any>>({
    maxPages: 100,
    maxKeywords: 500,
    crawlDepth: 3,
    includeImages: true,
    contentWordCountMin: 300
  });

  const handleTypeToggle = (typeId: string) => {
    const newTypes = selectedTypes.includes(typeId)
      ? selectedTypes.filter(id => id !== typeId)
      : [...selectedTypes, typeId];
    
    onTypesChange(newTypes);
  };

  const handleOptionChange = (key: string, value: any) => {
    const newOptions = { ...options, [key]: value };
    setOptions(newOptions);
    onOptionsChange(newOptions);
  };

  const isTypeSelected = (typeId: string) => selectedTypes.includes(typeId);

  const getEstimatedTotalTime = () => {
    if (selectedTypes.length === 0) return '0 minutes';
    
    const selectedAnalysisTypes = analysisTypes.filter(type => selectedTypes.includes(type.id));
    const minTime = selectedAnalysisTypes.reduce((sum, type) => {
      const time = parseInt(type.estimatedTime.split('-')[0]);
      return sum + time;
    }, 0);
    
    const maxTime = selectedAnalysisTypes.reduce((sum, type) => {
      const timeRange = type.estimatedTime.match(/(\d+)-(\d+)/);
      if (timeRange) {
        return sum + parseInt(timeRange[2]);
      }
      return sum + parseInt(type.estimatedTime.split(' ')[0]);
    }, 0);

    return `${minTime}-${maxTime} minutes`;
  };

  return (
    <div className="space-y-6">
      {/* Analysis Type Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {analysisTypes.map((type) => (
          <div
            key={type.id}
            className={`relative border rounded-lg p-4 transition-all cursor-pointer ${
              type.comingSoon
                ? 'bg-muted/50 border-muted cursor-not-allowed opacity-60'
                : isTypeSelected(type.id)
                ? 'bg-primary/5 border-primary shadow-sm'
                : 'bg-card border-border hover:border-primary/50'
            }`}
            onClick={() => !type.comingSoon && handleTypeToggle(type.id)}
          >
            {/* Coming Soon Badge */}
            {type.comingSoon && (
              <div className="absolute top-2 right-2 bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded">
                Coming Soon
              </div>
            )}

            {/* Checkbox */}
            <div className="flex items-start gap-3">
              <div className="mt-1">
                <input
                  type="checkbox"
                  checked={isTypeSelected(type.id)}
                  onChange={() => !type.comingSoon && handleTypeToggle(type.id)}
                  disabled={type.comingSoon}
                  className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
                />
              </div>
              
              <div className="flex-1">
                {/* Icon and Name */}
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xl">{type.icon}</span>
                  <h3 className="font-semibold text-foreground">{type.name}</h3>
                </div>
                
                {/* Description */}
                <p className="text-sm text-muted-foreground mb-3">
                  {type.description}
                </p>
                
                {/* Estimated Time */}
                <div className="flex items-center gap-2 text-xs">
                  <span className="text-muted-foreground">⏱️ {type.estimatedTime}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Selected Types Summary */}
      {selectedTypes.length > 0 && (
        <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <h4 className="font-medium text-foreground mb-2">Selected Analysis Types</h4>
          <div className="flex flex-wrap gap-2 mb-3">
            {selectedTypes.map((typeId) => {
              const type = analysisTypes.find(t => t.id === typeId);
              return type ? (
                <span
                  key={typeId}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-primary text-primary-foreground rounded text-sm"
                >
                  <span>{type.icon}</span>
                  <span>{type.name}</span>
                </span>
              ) : null;
            })}
          </div>
          <p className="text-sm text-muted-foreground">
            Estimated total time: <strong>{getEstimatedTotalTime()}</strong>
          </p>
        </div>
      )}

      {/* Advanced Options */}
      {selectedTypes.length > 0 && (
        <div className="border border-border rounded-lg">
          <button
            onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
          >
            <span className="font-medium text-foreground">Advanced Options</span>
            <span className={`transform transition-transform ${showAdvancedOptions ? 'rotate-180' : ''}`}>
              ⌄
            </span>
          </button>
          
          {showAdvancedOptions && (
            <div className="border-t border-border p-4 space-y-4">
              
              {/* Page Discovery Options */}
              {selectedTypes.includes('page_discovery') && (
                <div className="space-y-3">
                  <h5 className="font-medium text-foreground">Page Discovery Settings</h5>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-1">
                        Max Pages to Crawl
                      </label>
                      <input
                        type="number"
                        value={options.maxPages}
                        onChange={(e) => handleOptionChange('maxPages', parseInt(e.target.value))}
                        className="w-full p-2 border border-border rounded bg-background text-foreground"
                        min="10"
                        max="1000"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-1">
                        Crawl Depth
                      </label>
                      <select
                        value={options.crawlDepth}
                        onChange={(e) => handleOptionChange('crawlDepth', parseInt(e.target.value))}
                        className="w-full p-2 border border-border rounded bg-background text-foreground"
                      >
                        <option value={1}>1 level</option>
                        <option value={2}>2 levels</option>
                        <option value={3}>3 levels</option>
                        <option value={4}>4 levels</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Keyword Research Options */}
              {selectedTypes.includes('keyword_research') && (
                <div className="space-y-3">
                  <h5 className="font-medium text-foreground">Keyword Research Settings</h5>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-1">
                        Max Keywords
                      </label>
                      <input
                        type="number"
                        value={options.maxKeywords}
                        onChange={(e) => handleOptionChange('maxKeywords', parseInt(e.target.value))}
                        className="w-full p-2 border border-border rounded bg-background text-foreground"
                        min="100"
                        max="2000"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-1">
                        Data Sources
                      </label>
                      <select
                        value={options.dataSources || 'google_planner'}
                        onChange={(e) => handleOptionChange('dataSources', e.target.value)}
                        className="w-full p-2 border border-border rounded bg-background text-foreground"
                      >
                        <option value="google_planner">Google Keyword Planner</option>
                        <option value="ubersuggest">Ubersuggest</option>
                        <option value="both">Both Sources</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Content Analysis Options */}
              {selectedTypes.includes('content_generation') && (
                <div className="space-y-3">
                  <h5 className="font-medium text-foreground">Content Analysis Settings</h5>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-1">
                        Min Word Count for Analysis
                      </label>
                      <input
                        type="number"
                        value={options.contentWordCountMin}
                        onChange={(e) => handleOptionChange('contentWordCountMin', parseInt(e.target.value))}
                        className="w-full p-2 border border-border rounded bg-background text-foreground"
                        min="100"
                        max="1000"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-1">
                        AI Model
                      </label>
                      <select
                        value={options.aiModel || 'gpt-4o'}
                        onChange={(e) => handleOptionChange('aiModel', e.target.value)}
                        className="w-full p-2 border border-border rounded bg-background text-foreground"
                      >
                        <option value="gpt-4o">GPT-4 Optimized</option>
                        <option value="gpt-4o-mini">GPT-4 Mini (Faster)</option>
                        <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Global Options */}
              <div className="space-y-3 border-t pt-4">
                <h5 className="font-medium text-foreground">Global Settings</h5>
                
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="include-images"
                    checked={options.includeImages}
                    onChange={(e) => handleOptionChange('includeImages', e.target.checked)}
                    className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
                  />
                  <label htmlFor="include-images" className="text-sm text-foreground">
                    Include image analysis and optimization suggestions
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Quick Presets */}
      {selectedTypes.length === 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-foreground">Quick Presets</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <button
              onClick={() => onTypesChange(['page_discovery'])}
              className="p-3 border border-border rounded-lg text-left hover:bg-muted/50 transition-colors"
            >
              <div className="font-medium text-foreground">🔍 Basic Crawl</div>
              <div className="text-xs text-muted-foreground">Page discovery only</div>
            </button>
            
            <button
              onClick={() => onTypesChange(['page_discovery', 'keyword_research'])}
              className="p-3 border border-border rounded-lg text-left hover:bg-muted/50 transition-colors"
            >
              <div className="font-medium text-foreground">🎯 SEO Analysis</div>
              <div className="text-xs text-muted-foreground">Pages + Keywords</div>
            </button>
            
            <button
              onClick={() => onTypesChange(['page_discovery', 'keyword_research', 'content_generation'])}
              className="p-3 border border-border rounded-lg text-left hover:bg-muted/50 transition-colors"
            >
              <div className="font-medium text-foreground">🚀 Complete Audit</div>
              <div className="text-xs text-muted-foreground">All available features</div>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}; 