import {
  Contact,
  Company,
  Opportunity,
  Activity,
  Event,
  ContactFilters,
  CompanyFilters,
  OpportunityFilters,
  ActivityFilters,
  EventFilters,
  ContactFormData,
  CompanyFormData,
  OpportunityFormData,
  ActivityFormData,
  EventFormData,
  PaginatedResponse,
  ApiResponse,
} from "../types";

const API_BASE = "/api/crm";
const API_KEY = import.meta.env.VITE_API_KEY;

class CRMService {
  /**
   * Get current user ID from the user context
   */
  private async getCurrentUserId(): Promise<string | null> {
    try {
      // Get user from Supabase auth
      const { supabase } = await import(
        "../../../base/supabase/supabaseClient"
      );
      const {
        data: { user },
      } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error("Error getting current user:", error);
      return null;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE}${endpoint}`;
    const userId = await this.getCurrentUserId();

    const defaultHeaders: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Add API key if available
    if (API_KEY) {
      defaultHeaders["x-api-key"] = API_KEY;
    }

    // Add user ID if available
    if (userId) {
      defaultHeaders["x-user-id"] = userId;
    }

    const defaultOptions: RequestInit = {
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`
        );
      }

      // Handle 204 No Content responses
      if (response.status === 204) {
        return {} as T;
      }

      return await response.json();
    } catch (error) {
      console.error(`CRM API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // ===== CONTACT METHODS =====
  async getContacts(
    filters: ContactFilters = {}
  ): Promise<PaginatedResponse<Contact>> {
    // Use direct Supabase query (skip API call since server isn't running)
    console.log("🔄 Loading contacts via direct Supabase query...");
    try {
      const { supabase } = await import(
        "../../../base/supabase/supabaseClient"
      );
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Get user's organizations
      const { data: orgMembers, error: orgError } = await supabase
        .from("organisation_members")
        .select("organisation_id")
        .eq("user_id", user.id);

      if (orgError) {
        throw orgError;
      }

      const userOrgIds = orgMembers.map((row) => row.organisation_id);
      if (userOrgIds.length === 0) {
        return {
          data: [],
          total: 0,
          page: filters.page || 1,
          limit: filters.limit || 20,
        };
      }

      // Build query
      let query = supabase
        .from("crm_contacts")
        .select("*", { count: "exact" })
        .in("organisation_id", userOrgIds);

      // Apply search filter
      if (filters.search) {
        query = query.or(
          `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`
        );
      }

      // Apply tags filter
      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps("tags", filters.tags);
      }

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;
      query = query
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      console.log(`✅ Loaded ${data?.length || 0} contacts via Supabase query`);
      return {
        data: data || [],
        total: count || 0,
        page,
        limit,
      };
    } catch (supabaseError) {
      console.error("Supabase query failed:", supabaseError);
      throw supabaseError;
    }
  }

  async getContact(id: string): Promise<Contact> {
    return this.request<Contact>(`/contacts/${id}`);
  }

  async createContact(data: ContactFormData): Promise<Contact> {
    // Use direct Supabase query (skip API call since server isn't running)
    console.log("🔄 Creating contact via direct Supabase query...");
    console.log("📋 Form data received:", data);
    try {
      const { supabase } = await import(
        "../../../base/supabase/supabaseClient"
      );
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Validate that organization_id is provided and not empty
      if (!data.organisation_id || data.organisation_id.trim() === "") {
        throw new Error("Organization ID is required");
      }

      // Verify user has access to the specified organization
      const { data: orgMembers, error: orgError } = await supabase
        .from("organisation_members")
        .select("organisation_id")
        .eq("user_id", user.id);

      if (orgError) {
        throw orgError;
      }

      const userOrgIds = orgMembers.map((row) => row.organisation_id);
      if (!userOrgIds.includes(data.organisation_id)) {
        throw new Error(
          "User does not have access to the specified organization"
        );
      }

      // Prepare contact data - remove empty string fields that should be null
      const contactData: any = {
        // Required fields
        full_name: data.full_name.trim(),
        organisation_id: data.organisation_id, // Already validated above
        owner_id: user.id,

        // System fields
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add optional UUID fields only if they have valid values
      if (data.company_id && data.company_id.trim() !== "") {
        contactData.company_id = data.company_id.trim();
      }

      // Add optional text fields only if they have values
      if (data.email && data.email.trim() !== "") {
        contactData.email = data.email.trim();
      }
      if (data.phone && data.phone.trim() !== "") {
        contactData.phone = data.phone.trim();
      }
      if (data.phone2 && data.phone2.trim() !== "") {
        contactData.phone2 = data.phone2.trim();
      }
      if (data.mobile && data.mobile.trim() !== "") {
        contactData.mobile = data.mobile.trim();
      }
      if (data.fax && data.fax.trim() !== "") {
        contactData.fax = data.fax.trim();
      }
      if (data.job_title && data.job_title.trim() !== "") {
        contactData.job_title = data.job_title.trim();
      }
      if (data.salutation && data.salutation.trim() !== "") {
        contactData.salutation = data.salutation.trim();
      }
      if (data.skypename && data.skypename.trim() !== "") {
        contactData.skypename = data.skypename.trim();
      }
      if (data.webpage && data.webpage.trim() !== "") {
        contactData.webpage = data.webpage.trim();
      }
      if (data.note && data.note.trim() !== "") {
        contactData.note = data.note.trim();
      }
      if (data.tags && Array.isArray(data.tags) && data.tags.length > 0) {
        contactData.tags = data.tags;
      }
      if (data.custom_fields && Object.keys(data.custom_fields).length > 0) {
        contactData.custom_fields = data.custom_fields;
      }

      console.log("🧹 Cleaned contact data:", contactData);

      // Create contact in Supabase
      const { data: newContact, error: createError } = await supabase
        .from("crm_contacts")
        .insert([contactData])
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      console.log("✅ Contact created successfully via Supabase query");
      return newContact;
    } catch (supabaseError) {
      console.error("Supabase query failed:", supabaseError);
      throw supabaseError;
    }
  }

  async updateContact(
    id: string,
    data: Partial<ContactFormData>
  ): Promise<Contact> {
    return this.request<Contact>(`/contacts/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteContact(id: string): Promise<void> {
    return this.request<void>(`/contacts/${id}`, {
      method: "DELETE",
    });
  }

  // ===== COMPANY METHODS =====
  async getCompanies(
    filters: CompanyFilters = {}
  ): Promise<PaginatedResponse<Company>> {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
    if (filters.search) params.append("search", filters.search);

    return this.request<PaginatedResponse<Company>>(`/companies?${params}`);
  }

  async getCompany(id: string): Promise<Company> {
    return this.request<Company>(`/companies/${id}`);
  }

  async createCompany(data: CompanyFormData): Promise<Company> {
    return this.request<Company>("/companies", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateCompany(
    id: string,
    data: Partial<CompanyFormData>
  ): Promise<Company> {
    return this.request<Company>(`/companies/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteCompany(id: string): Promise<void> {
    return this.request<void>(`/companies/${id}`, {
      method: "DELETE",
    });
  }

  // ===== OPPORTUNITY METHODS =====
  async getOpportunities(
    filters: OpportunityFilters = {}
  ): Promise<PaginatedResponse<Opportunity>> {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
    if (filters.stage) params.append("stage", filters.stage);
    if (filters.assigned_to) params.append("assigned_to", filters.assigned_to);

    return this.request<PaginatedResponse<Opportunity>>(
      `/opportunities?${params}`
    );
  }

  async getOpportunity(id: string): Promise<Opportunity> {
    return this.request<Opportunity>(`/opportunities/${id}`);
  }

  async createOpportunity(data: OpportunityFormData): Promise<Opportunity> {
    return this.request<Opportunity>("/opportunities", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateOpportunity(
    id: string,
    data: Partial<OpportunityFormData>
  ): Promise<Opportunity> {
    return this.request<Opportunity>(`/opportunities/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteOpportunity(id: string): Promise<void> {
    return this.request<void>(`/opportunities/${id}`, {
      method: "DELETE",
    });
  }

  // ===== ACTIVITY METHODS =====
  async getActivities(
    filters: ActivityFilters = {}
  ): Promise<PaginatedResponse<Activity>> {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
    if (filters.type) params.append("type", filters.type);
    if (filters.contact_id) params.append("contact_id", filters.contact_id);
    if (filters.opportunity_id)
      params.append("opportunity_id", filters.opportunity_id);

    return this.request<PaginatedResponse<Activity>>(`/activities?${params}`);
  }

  async createActivity(data: ActivityFormData): Promise<Activity> {
    return this.request<Activity>("/activities", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // ===== EVENT METHODS =====
  async getEvents(
    filters: EventFilters = {}
  ): Promise<PaginatedResponse<Event>> {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
    if (filters.start_date) params.append("start_date", filters.start_date);
    if (filters.end_date) params.append("end_date", filters.end_date);

    return this.request<PaginatedResponse<Event>>(`/events?${params}`);
  }

  async getEvent(id: string): Promise<Event> {
    return this.request<Event>(`/events/${id}`);
  }

  async createEvent(data: EventFormData): Promise<Event> {
    return this.request<Event>("/events", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateEvent(id: string, data: Partial<EventFormData>): Promise<Event> {
    return this.request<Event>(`/events/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteEvent(id: string): Promise<void> {
    return this.request<void>(`/events/${id}`, {
      method: "DELETE",
    });
  }

  // ===== UTILITY METHODS =====
  async healthCheck(): Promise<{
    status: string;
    service: string;
    timestamp: string;
  }> {
    return this.request<{ status: string; service: string; timestamp: string }>(
      "/health"
    );
  }

  // ===== ORGANIZATION METHODS =====
  async getUserOrganizations(): Promise<
    PaginatedResponse<{ id: string; name: string; role?: string }>
  > {
    // Use direct Supabase query (skip API call since server isn't running)
    console.log("🔄 Loading organizations via direct Supabase query...");
    try {
      const { supabase } = await import(
        "../../../base/supabase/supabaseClient"
      );
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("organisation_members")
        .select(
          `
            role,
            organisations (
              id,
              name
            )
          `
        )
        .eq("user_id", user.id);

      if (error) {
        throw error;
      }

      const organizations = data.map((row) => ({
        id: row.organisations.id,
        name: row.organisations.name,
        role: row.role,
      }));

      return {
        data: organizations,
        total: organizations.length,
        page: 1,
        limit: organizations.length,
      };
    } catch (supabaseError) {
      console.error("Supabase query failed:", supabaseError);
      throw supabaseError;
    }
  }
}

// Export singleton instance
export const crmService = new CRMService();
export default crmService;
