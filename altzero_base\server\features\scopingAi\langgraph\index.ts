// =====================================================
// SCOPINGAI LANGGRAPH MAIN EXPORTS
// =====================================================

// Export main workflow
export { ProposalWorkflow } from './workflows/ProposalWorkflow';

// Export types
export * from './types/WorkflowState';
export * from './types/NodeTypes';

// Export core components
export { BaseWorkflow } from './core/BaseWorkflow';
export { StateManager } from './core/StateManager';
export { ToolRegistry } from './core/ToolRegistry';

// Export individual nodes
export { ValidationNode } from './nodes/ValidationNode';
export { KnowledgeRetrievalNode } from './nodes/KnowledgeRetrievalNode';
export { ClientAnalysisNode } from './nodes/ClientAnalysisNode';
export { ResearchAnalysisNode } from './nodes/ResearchAnalysisNode';
export { ExecutiveSummaryNode } from './nodes/ExecutiveSummaryNode';
export { SectionGenerationNode } from './nodes/SectionGenerationNode';
export { QualityReviewNode } from './nodes/QualityReviewNode';
export { FinalizationNode } from './nodes/FinalizationNode';

// Export routes
export { default as langgraphRoutes } from './routes/langgraph';

console.log('🚀 ScopingAI LangGraph module loaded successfully');
