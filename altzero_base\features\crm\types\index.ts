// CRM Type Definitions

export interface Contact {
  id?: string;
  organisation_id: string;
  full_name: string;
  email?: string;
  phone?: string;
  phone2?: string;
  mobile?: string;
  fax?: string;
  address?: any;
  job_title?: string;
  company_id?: string;
  external_id?: string;
  owner_id?: string;
  salutation?: string;
  skypename?: string;
  webpage?: string;
  note?: string;
  tags?: string[];
  custom_fields?: any;
  created_at?: string;
  updated_at?: string;
  // Related data
  crm_companies?: Company;
}

export interface Company {
  id?: string;
  organisation_id: string;
  name: string;
  website?: string;
  email?: string;
  phone?: string;
  fax?: string;
  address?: any;
  employees?: number;
  revenues?: string;
  tax_number?: string;
  region_id?: string;
  owner_id?: string;
  external_id?: string;
  custom_fields?: any;
  created_at?: string;
  updated_at?: string;
  // Related data
  crm_regions?: Region;
  contact_count?: number;
}

export interface Opportunity {
  id?: string;
  organisation_id: string;
  contact_id?: string;
  title: string;
  value?: number;
  currency?: string;
  stage?: string;
  close_date?: string;
  assigned_to?: string;
  created_at?: string;
  updated_at?: string;
  // Related data
  crm_contacts?: Contact;
}

export interface Activity {
  id?: string;
  organisation_id: string;
  contact_id?: string;
  opportunity_id?: string;
  type: "call" | "email" | "meeting" | "note";
  content?: string;
  scheduled_at?: string;
  created_by?: string;
  created_at?: string;
  // Related data
  crm_contacts?: Contact;
  crm_opportunities?: Opportunity;
}

export interface Event {
  id?: string;
  organisation_id: string;
  title: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  geo?: any;
  note?: string;
  contact_id?: string;
  company_id?: string;
  owner_id?: string;
  related_project_id?: string;
  related_opportunity_id?: string;
  custom_fields?: any;
  recurrence_rule?: string;
  created_at?: string;
  updated_at?: string;
  // Related data
  crm_contacts?: Contact;
  crm_companies?: Company;
}

export interface Region {
  id?: string;
  organisation_id: string;
  name: string;
  is_favorite?: boolean;
  created_at?: string;
}

export interface Pipeline {
  id?: string;
  organisation_id: string;
  name: string;
  created_at?: string;
}

export interface PipelineStage {
  id?: string;
  organisation_id: string;
  pipeline_id?: string;
  name: string;
  position: number;
  created_at?: string;
}

// API Response Types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Filter Types
export interface ContactFilters {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string[];
}

export interface CompanyFilters {
  page?: number;
  limit?: number;
  search?: string;
}

export interface OpportunityFilters {
  page?: number;
  limit?: number;
  stage?: string;
  assigned_to?: string;
}

export interface ActivityFilters {
  page?: number;
  limit?: number;
  type?: string;
  contact_id?: string;
  opportunity_id?: string;
}

export interface EventFilters {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
}

// Form Types
export interface ContactFormData {
  full_name: string;
  organisation_id?: string; // Add organization selection field
  email?: string;
  phone?: string;
  phone2?: string;
  mobile?: string;
  fax?: string;
  address?: any;
  job_title?: string;
  company_id?: string;
  salutation?: string;
  skypename?: string;
  webpage?: string;
  note?: string;
  tags?: string[];
  custom_fields?: any;
}

export interface CompanyFormData {
  name: string;
  website?: string;
  email?: string;
  phone?: string;
  fax?: string;
  address?: any;
  employees?: number;
  revenues?: string;
  tax_number?: string;
  region_id?: string;
  custom_fields?: any;
}

export interface OpportunityFormData {
  contact_id?: string;
  title: string;
  value?: number;
  currency?: string;
  stage?: string;
  close_date?: string;
  assigned_to?: string;
}

export interface ActivityFormData {
  contact_id?: string;
  opportunity_id?: string;
  type: "call" | "email" | "meeting" | "note";
  content?: string;
  scheduled_at?: string;
}

export interface EventFormData {
  title: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  note?: string;
  contact_id?: string;
  company_id?: string;
  related_opportunity_id?: string;
  recurrence_rule?: string;
}

// Dashboard Stats Types
export interface DashboardStats {
  contacts: {
    total: number;
    thisMonth: number;
    growth: number;
  };
  companies: {
    total: number;
    thisMonth: number;
    growth: number;
  };
  opportunities: {
    total: number;
    totalValue: number;
    avgValue: number;
    byStage: Record<string, { count: number; value: number }>;
  };
  activities: {
    total: number;
    thisWeek: number;
    thisMonth: number;
    byType: Record<string, number>;
  };
}

// UI State Types
export interface TableState {
  loading: boolean;
  data: any[];
  total: number;
  page: number;
  limit: number;
  filters: any;
}

export interface FormState {
  loading: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

// Constants
export const ACTIVITY_TYPES = [
  { value: "call", label: "Call" },
  { value: "email", label: "Email" },
  { value: "meeting", label: "Meeting" },
  { value: "note", label: "Note" },
] as const;

export const OPPORTUNITY_STAGES = [
  { value: "lead", label: "Lead" },
  { value: "qualified", label: "Qualified" },
  { value: "proposal", label: "Proposal" },
  { value: "negotiation", label: "Negotiation" },
  { value: "closed_won", label: "Closed Won" },
  { value: "closed_lost", label: "Closed Lost" },
] as const;

export const CURRENCIES = [
  { value: "USD", label: "USD ($)" },
  { value: "EUR", label: "EUR (€)" },
  { value: "GBP", label: "GBP (£)" },
  { value: "AUD", label: "AUD (A$)" },
  { value: "CAD", label: "CAD (C$)" },
] as const;
