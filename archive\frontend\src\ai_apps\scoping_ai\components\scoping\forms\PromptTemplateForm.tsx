import React, { useState, useEffect } from "react";
import { PromptTemplate } from "../../../../../types/scoping";
import { supabase } from "../../../../../utils/supabaseClient";

interface PromptTemplateFormProps {
  onNext: () => void;
  onBack: () => void;
  state: {
    promptTemplate?: PromptTemplate;
  };
  setPromptTemplate: (template: PromptTemplate) => void;
}

// Fetch prompt templates from Supabase
const fetchPromptTemplates = async (): Promise<PromptTemplate[]> => {
  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting user:", userError);
      return [];
    }

    const userId = userData.user?.id;
    if (!userId) {
      console.error("No user ID found, user may not be authenticated");
      return [];
    }

    const { data, error } = await supabase
      .from("prompt_templates")
      .select("*")
      .eq("user_id", userId)
      .order("name");

    if (error) {
      console.error("Error fetching prompt templates:", error);
      return [];
    }

    // Transform the data to match our PromptTemplate type
    return data.map((template) => ({
      id: template.id,
      name: template.name,
      description: template.description || "",
      content: template.content,
      variables: template.variables || [],
      createdAt: new Date(template.created_at),
      updatedAt: new Date(template.updated_at),
    }));
  } catch (error) {
    console.error("Error fetching prompt templates:", error);
    return [];
  }
};

const PromptTemplateForm: React.FC<PromptTemplateFormProps> = ({
  onNext,
  onBack,
  state,
  setPromptTemplate,
}) => {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(
    null
  );
  const [isCustomTemplate, setIsCustomTemplate] = useState<boolean>(false);
  const [customTemplate, setCustomTemplate] = useState<PromptTemplate>({
    id: "custom-template",
    name: "Custom Template",
    content: "",
  });

  // Load templates from Supabase
  useEffect(() => {
    const loadTemplates = async () => {
      setLoading(true);
      try {
        const data = await fetchPromptTemplates();
        setTemplates(data);
      } catch (error) {
        console.error("Error loading prompt templates:", error);
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, []);

  // Initialize from existing state if available
  useEffect(() => {
    if (state.promptTemplate) {
      // Check if it's one of our templates
      const existingTemplate = templates.find(
        (t) => t.id === state.promptTemplate?.id
      );
      if (existingTemplate) {
        setSelectedTemplateId(existingTemplate.id);
        setIsCustomTemplate(false);
      } else {
        // It's a custom template
        setCustomTemplate(state.promptTemplate);
        setIsCustomTemplate(true);
      }
    }
  }, [state.promptTemplate, templates]);

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplateId(templateId);
    setIsCustomTemplate(false);
  };

  const handleCustomTemplateSelect = () => {
    setSelectedTemplateId(null);
    setIsCustomTemplate(true);
  };

  const handleCustomNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomTemplate((prev: PromptTemplate) => ({
      ...prev,
      name: e.target.value,
    }));
  };

  const handleCustomContentChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setCustomTemplate((prev: PromptTemplate) => ({
      ...prev,
      content: e.target.value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (isCustomTemplate) {
      // Save custom template
      setPromptTemplate(customTemplate);
    } else if (selectedTemplateId) {
      // Save selected template
      const selectedTemplate = templates.find(
        (t) => t.id === selectedTemplateId
      );
      if (selectedTemplate) {
        setPromptTemplate(selectedTemplate);
      }
    }

    onNext();
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Prompt Template</h2>
      <p className="text-gray-600 mb-6">
        Choose a template or create your own to guide the AI in generating your
        scoping document
      </p>

      {loading ? (
        <div className="flex justify-center items-center h-32">
          <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {templates.map((template) => (
                <div
                  key={template.id}
                  onClick={() => handleTemplateSelect(template.id)}
                  className={`cursor-pointer rounded-lg border p-4 hover:border-indigo-300 ${
                    selectedTemplateId === template.id
                      ? "border-indigo-500 ring-2 ring-indigo-500 bg-indigo-50"
                      : "border-gray-300"
                  }`}
                >
                  <h3 className="font-medium text-gray-900">{template.name}</h3>
                  <p className="mt-1 text-sm text-gray-500 line-clamp-3">
                    {template.content.substring(0, 150)}...
                  </p>
                </div>
              ))}

              <div
                onClick={handleCustomTemplateSelect}
                className={`cursor-pointer rounded-lg border p-4 hover:border-indigo-300 ${
                  isCustomTemplate
                    ? "border-indigo-500 ring-2 ring-indigo-500 bg-indigo-50"
                    : "border-gray-300"
                }`}
              >
                <h3 className="font-medium text-gray-900">Custom Template</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Create your own custom prompt template
                </p>
              </div>
            </div>

            {/* Custom Template Form */}
            {isCustomTemplate && (
              <div className="mt-6 space-y-4 bg-gray-50 p-4 rounded-lg">
                <div>
                  <label
                    htmlFor="templateName"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Template Name
                  </label>
                  <input
                    type="text"
                    id="templateName"
                    value={customTemplate.name}
                    onChange={handleCustomNameChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    required
                  />
                </div>

                <div>
                  <label
                    htmlFor="templateContent"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Template Content
                  </label>
                  <textarea
                    id="templateContent"
                    rows={10}
                    value={customTemplate.content}
                    onChange={handleCustomContentChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Provide instructions for the AI on how to generate your scoping document..."
                    required
                  />
                  <p className="mt-2 text-xs text-gray-500">
                    Write detailed instructions for the AI to follow when
                    creating your scoping document. Include any specific tone,
                    format, or sections you want the AI to focus on.
                  </p>
                </div>
              </div>
            )}

            {/* Template Preview */}
            {selectedTemplateId && !isCustomTemplate && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Template Preview
                </h3>
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                    {
                      templates.find((t) => t.id === selectedTemplateId)
                        ?.content
                    }
                  </pre>
                </div>
              </div>
            )}

            <div className="flex justify-between pt-4">
              <button
                type="button"
                onClick={onBack}
                className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Back
              </button>
              <button
                type="submit"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Next
              </button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
};

export default PromptTemplateForm;
