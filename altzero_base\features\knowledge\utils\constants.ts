// File upload configuration
export const ALLOWED_FILE_TYPES = [
  ".pdf",
  ".docx",
  ".txt",
  ".md",
  ".doc",
  ".rtf",
  ".odt",
];
export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
export const MAX_FILES_PER_UPLOAD = 10;

// Document status types
export const DOCUMENT_STATUS = {
  SUCCESS: "success",
  PROCESSING: "processing",
  ERROR: "error",
  UPLOADING: "uploading",
  PARSING: "parsing",
  INDEXING: "indexing",
} as const;

// Chat configuration
export const DEFAULT_SYSTEM_MESSAGE =
  "I am an advanced AI assistant that helps you analyze and understand your documents. I can answer questions, summarize content, and provide insights based on your uploaded knowledge base.";
export const MIN_RELEVANCE_SCORE = 0.7;
export const MAX_CHAT_HISTORY = 50;
export const CHAT_RESPONSE_TIMEOUT = 30000; // 30 seconds

// Knowledge base configuration
export const KNOWLEDGE_BASE = {
  MAX_DOCUMENTS: 1000,
  CHUNK_SIZE: 1000,
  CHUNK_OVERLAP: 200,
  SIMILARITY_THRESHOLD: 0.8,
  MAX_RETRIEVAL_RESULTS: 10,
} as const;

// Knowledge-specific API endpoints
export const KNOWLEDGE_ENDPOINTS = {
  // Knowledge base endpoints
  KNOWLEDGE_UPLOAD: "/api/knowledge/documents/upload",
  KNOWLEDGE_DOCUMENTS: "/api/knowledge/documents",
  KNOWLEDGE_DELETE: "/api/knowledge/documents/{id}",
  KNOWLEDGE_REPROCESS: "/api/knowledge/documents/{id}/reprocess",
  KNOWLEDGE_STATS: "/api/knowledge/stats",
  KNOWLEDGE_SEARCH: "/api/knowledge/search",
  KNOWLEDGE_SUMMARY: "/api/knowledge/summary",

  // Chat endpoints
  CHAT: "/api/knowledge/chat",
  CHAT_STREAM: "/api/knowledge/chat/stream",
  CHAT_HISTORY: "/api/knowledge/chat/sessions",
} as const;

// Knowledge-specific toast messages
export const KNOWLEDGE_TOAST_MESSAGES = {
  UPLOAD_SUCCESS: "Document uploaded successfully!",
  UPLOAD_ERROR: "Failed to upload document. Please try again.",
  UPLOAD_PROCESSING: "Processing document...",
  DELETE_SUCCESS: "Document deleted successfully!",
  DELETE_ERROR: "Failed to delete document.",
  CHAT_ERROR: "Failed to send message. Please try again.",
  CONNECTION_ERROR: "Connection error. Please check your internet connection.",
} as const; 