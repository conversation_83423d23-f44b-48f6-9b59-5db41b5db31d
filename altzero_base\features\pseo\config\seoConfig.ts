import { ProviderConfig } from '../services/pseo/providers/base/SEOProvider';

export type SEOProviderType = 
  | 'lighthouse'      // Google PageSpeed Insights
  | 'gtmetrix'        // GTmetrix
  | 'webpagetest'     // WebPageTest
  | 'semrush'         // Semrush (Premium)
  | 'ahrefs'          // Ahrefs (Premium)
  | 'screaming_frog'; // Screaming Frog (Premium)

export type SEOFunctionType = 
  | 'generator'       // Main SEO report generation
  | 'pagespeed'       // Page speed and performance analysis
  | 'backlink'        // Backlink analysis
  | 'keyword';        // Keyword analysis and research

export interface SEOEnvironmentConfig {
  // Function-based provider configuration
  SEO_GENERATOR_PROVIDER?: SEOProviderType;
  SEO_GENERATOR_API_KEY?: string;
  
  SEO_PAGESPEED_PROVIDER?: SEOProviderType;
  SEO_PAGESPEED_API_KEY?: string;
  
  SEO_BACKLINK_PROVIDER?: SEOProviderType;
  SEO_BACKLINK_API_KEY?: string;
  
  SEO_KEYWORD_PROVIDER?: SEOProviderType;
  SEO_KEYWORD_API_KEY?: string;
  
  // Global configuration overrides
  SEO_RATE_LIMIT?: string;
  SEO_TIMEOUT?: string;
  SEO_RETRIES?: string;
}

interface ProviderInfo {
  name: string;
  tier: 'free' | 'premium';
  description: string;
  defaultRateLimit: number;
  defaultTimeout: number;
  setupUrl: string;
  monthlyLimit?: number;
  costPerRequest?: number;
  capabilities: SEOFunctionType[];
}

interface FunctionProviderConfig {
  provider: SEOProviderType;
  config: ProviderConfig;
}

class SEOConfigService {
  private envConfig: SEOEnvironmentConfig = {};
  
  private readonly PROVIDER_INFO: Record<SEOProviderType, ProviderInfo> = {
    lighthouse: {
      name: 'Google Lighthouse',
      tier: 'free',
      description: 'Google PageSpeed Insights API with Lighthouse analysis',
      defaultRateLimit: 60,
      defaultTimeout: 30000,
      setupUrl: 'https://console.cloud.google.com/apis/credentials',
      monthlyLimit: 25000,
      costPerRequest: 0,
      capabilities: ['generator', 'pagespeed']
    },
    gtmetrix: {
      name: 'GTmetrix',
      tier: 'free',
      description: 'GTmetrix performance and SEO analysis',
      defaultRateLimit: 1,
      defaultTimeout: 120000,
      setupUrl: 'https://gtmetrix.com/api/',
      monthlyLimit: 20,
      costPerRequest: 0,
      capabilities: ['generator', 'pagespeed']
    },
    webpagetest: {
      name: 'WebPageTest',
      tier: 'free',
      description: 'WebPageTest performance analysis',
      defaultRateLimit: 3,
      defaultTimeout: 180000,
      setupUrl: 'https://www.webpagetest.org/api',
      monthlyLimit: 200,
      costPerRequest: 0,
      capabilities: ['pagespeed']
    },
    semrush: {
      name: 'Semrush',
      tier: 'premium',
      description: 'Comprehensive SEO analysis with keyword and backlink data',
      defaultRateLimit: 600,
      defaultTimeout: 30000,
      setupUrl: 'https://www.semrush.com/api-documentation/',
      costPerRequest: 0.005,
      capabilities: ['generator', 'backlink', 'keyword']
    },
    ahrefs: {
      name: 'Ahrefs',
      tier: 'premium',
      description: 'Advanced backlink and keyword analysis',
      defaultRateLimit: 300,
      defaultTimeout: 30000,
      setupUrl: 'https://ahrefs.com/api',
      costPerRequest: 0.008,
      capabilities: ['backlink', 'keyword']
    },
    screaming_frog: {
      name: 'Screaming Frog',
      tier: 'premium',
      description: 'Technical SEO crawler and analysis',
      defaultRateLimit: 60,
      defaultTimeout: 60000,
      setupUrl: 'https://api.screamingfrog.co.uk/',
      costPerRequest: 0.003,
      capabilities: ['generator']
    }
  };

  constructor() {
    this.loadEnvironmentConfig();
  }

  /**
   * Load configuration from environment variables
   */
  private loadEnvironmentConfig(): void {
    // Check if we're in a browser environment
    const isBrowser = typeof window !== 'undefined' && typeof process === 'undefined';
    
    if (isBrowser) {
      // In browser, use empty config - environment variables should be set server-side
      // or through build-time environment variable injection (like Vite/Next.js)
      this.envConfig = {
        SEO_GENERATOR_PROVIDER: (window as any).ENV?.SEO_GENERATOR_PROVIDER as SEOProviderType,
        SEO_GENERATOR_API_KEY: (window as any).ENV?.SEO_GENERATOR_API_KEY,
        
        SEO_PAGESPEED_PROVIDER: (window as any).ENV?.SEO_PAGESPEED_PROVIDER as SEOProviderType,
        SEO_PAGESPEED_API_KEY: (window as any).ENV?.SEO_PAGESPEED_API_KEY,
        
        SEO_BACKLINK_PROVIDER: (window as any).ENV?.SEO_BACKLINK_PROVIDER as SEOProviderType,
        SEO_BACKLINK_API_KEY: (window as any).ENV?.SEO_BACKLINK_API_KEY,
        
        SEO_KEYWORD_PROVIDER: (window as any).ENV?.SEO_KEYWORD_PROVIDER as SEOProviderType,
        SEO_KEYWORD_API_KEY: (window as any).ENV?.SEO_KEYWORD_API_KEY,
        
        SEO_RATE_LIMIT: (window as any).ENV?.SEO_RATE_LIMIT,
        SEO_TIMEOUT: (window as any).ENV?.SEO_TIMEOUT,
        SEO_RETRIES: (window as any).ENV?.SEO_RETRIES
      };
    } else {
      // In Node.js environment
      this.envConfig = {
        SEO_GENERATOR_PROVIDER: process.env.SEO_GENERATOR_PROVIDER as SEOProviderType,
        SEO_GENERATOR_API_KEY: process.env.SEO_GENERATOR_API_KEY,
        
        SEO_PAGESPEED_PROVIDER: process.env.SEO_PAGESPEED_PROVIDER as SEOProviderType,
        SEO_PAGESPEED_API_KEY: process.env.SEO_PAGESPEED_API_KEY,
        
        SEO_BACKLINK_PROVIDER: process.env.SEO_BACKLINK_PROVIDER as SEOProviderType,
        SEO_BACKLINK_API_KEY: process.env.SEO_BACKLINK_API_KEY,
        
        SEO_KEYWORD_PROVIDER: process.env.SEO_KEYWORD_PROVIDER as SEOProviderType,
        SEO_KEYWORD_API_KEY: process.env.SEO_KEYWORD_API_KEY,
        
        SEO_RATE_LIMIT: process.env.SEO_RATE_LIMIT,
        SEO_TIMEOUT: process.env.SEO_TIMEOUT,
        SEO_RETRIES: process.env.SEO_RETRIES
      };
    }
    
    console.log('🔧 SEO Config loaded:', {
      environment: isBrowser ? 'browser' : 'node',
      configuredProviders: Object.entries(this.envConfig)
        .filter(([key, value]) => key.includes('_PROVIDER') && value)
        .map(([key]) => key)
    });
  }

  /**
   * Get provider configuration for a specific function
   */
  getProviderForFunction(func: SEOFunctionType): FunctionProviderConfig | null {
    const envKey = `SEO_${func.toUpperCase()}_PROVIDER` as keyof SEOEnvironmentConfig;
    const keyEnvKey = `SEO_${func.toUpperCase()}_API_KEY` as keyof SEOEnvironmentConfig;
    
    const provider = this.envConfig[envKey] as SEOProviderType;
    const apiKey = this.envConfig[keyEnvKey] as string;
    
    if (!provider || !apiKey) {
      return null;
    }
    
    const providerInfo = this.PROVIDER_INFO[provider];
    if (!providerInfo.capabilities.includes(func)) {
      console.warn(`Provider ${provider} does not support ${func} function`);
      return null;
    }
    
    return {
      provider,
      config: {
        apiKey,
        enabled: !!apiKey,
        rateLimit: this.getNumberConfig('SEO_RATE_LIMIT', providerInfo.defaultRateLimit),
        timeout: this.getNumberConfig('SEO_TIMEOUT', providerInfo.defaultTimeout),
        retries: this.getNumberConfig('SEO_RETRIES', 3)
      }
    };
  }

  /**
   * Get all configured functions and their providers
   */
  getAllConfiguredFunctions(): Record<SEOFunctionType, FunctionProviderConfig | null> {
    return {
      generator: this.getProviderForFunction('generator'),
      pagespeed: this.getProviderForFunction('pagespeed'),
      backlink: this.getProviderForFunction('backlink'),
      keyword: this.getProviderForFunction('keyword')
    };
  }

  /**
   * Get available providers for a specific function
   */
  getAvailableProvidersForFunction(func: SEOFunctionType): ProviderInfo[] {
    return Object.entries(this.PROVIDER_INFO)
      .filter(([_, info]) => info.capabilities.includes(func))
      .map(([_, info]) => info);
  }

  /**
   * Get provider information
   */
  getProviderInfo(provider: SEOProviderType): ProviderInfo {
    return this.PROVIDER_INFO[provider];
  }

  /**
   * Get all available providers
   */
  getAvailableProviders(): Record<SEOProviderType, ProviderInfo> {
    return this.PROVIDER_INFO;
  }

  /**
   * Validate current configuration
   */
  validateConfiguration(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check if at least one function is configured
    const configuredFunctions = this.getAllConfiguredFunctions();
    const hasAnyFunction = Object.values(configuredFunctions).some(config => config !== null);
    
    if (!hasAnyFunction) {
      errors.push('No SEO functions are configured. Configure at least one function (generator, pagespeed, backlink, or keyword)');
    }

    // Validate each configured function
    Object.entries(configuredFunctions).forEach(([func, config]) => {
      if (config) {
        const providerInfo = this.getProviderInfo(config.provider);
        if (!providerInfo.capabilities.includes(func as SEOFunctionType)) {
          errors.push(`Provider ${config.provider} does not support ${func} function`);
        }
      }
    });

    // Check for incomplete configurations
    const functions: SEOFunctionType[] = ['generator', 'pagespeed', 'backlink', 'keyword'];
    functions.forEach(func => {
      const providerKey = `SEO_${func.toUpperCase()}_PROVIDER` as keyof SEOEnvironmentConfig;
      const keyKey = `SEO_${func.toUpperCase()}_API_KEY` as keyof SEOEnvironmentConfig;
      
      const hasProvider = !!this.envConfig[providerKey];
      const hasKey = !!this.envConfig[keyKey];
      
      if (hasProvider && !hasKey) {
        warnings.push(`${providerKey} is set but ${keyKey} is missing`);
      }
      
      if (hasKey && !hasProvider) {
        warnings.push(`${keyKey} is set but ${providerKey} is missing`);
      }
    });

    // Validate numeric configs
    if (this.envConfig.SEO_RATE_LIMIT && isNaN(parseInt(this.envConfig.SEO_RATE_LIMIT))) {
      errors.push('SEO_RATE_LIMIT must be a valid number');
    }

    if (this.envConfig.SEO_TIMEOUT && isNaN(parseInt(this.envConfig.SEO_TIMEOUT))) {
      errors.push('SEO_TIMEOUT must be a valid number');
    }

    if (this.envConfig.SEO_RETRIES && isNaN(parseInt(this.envConfig.SEO_RETRIES))) {
      errors.push('SEO_RETRIES must be a valid number');
    }

    // Suggestions for optimal configuration
    if (!configuredFunctions.generator) {
      suggestions.push('Configure SEO_GENERATOR_PROVIDER for basic SEO report generation (lighthouse recommended for free tier)');
    }

    if (!configuredFunctions.pagespeed) {
      suggestions.push('Configure SEO_PAGESPEED_PROVIDER for performance analysis (lighthouse or gtmetrix recommended)');
    }

    if (!configuredFunctions.backlink && !configuredFunctions.keyword) {
      suggestions.push('Consider configuring premium providers (semrush/ahrefs) for backlink and keyword analysis');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Get configuration summary
   */
  getConfigurationSummary(): {
    configuredFunctions: string[];
    providers: string[];
    estimatedMonthlyCost: number;
    recommendations: string[];
  } {
    const configuredFunctions = this.getAllConfiguredFunctions();
    const activeConfigs = Object.entries(configuredFunctions)
      .filter(([_, config]) => config !== null)
      .map(([func, config]) => ({ func, config: config! }));
    
    const providers = [...new Set(activeConfigs.map(({ config }) => config.provider))];
    const providerNames = providers.map(p => this.getProviderInfo(p).name);
    
    const estimatedMonthlyCost = providers.reduce((cost, provider) => {
      const info = this.getProviderInfo(provider);
      return cost + (info.costPerRequest || 0) * 100; // Assume 100 requests/month per provider
    }, 0);

    const recommendations: string[] = [];
    
    // Check for missing functions
    if (!configuredFunctions.generator) recommendations.push('Add generator provider for basic SEO reports');
    if (!configuredFunctions.pagespeed) recommendations.push('Add pagespeed provider for performance insights');
    if (!configuredFunctions.backlink) recommendations.push('Consider premium provider for backlink analysis');
    if (!configuredFunctions.keyword) recommendations.push('Consider premium provider for keyword research');

    return {
      configuredFunctions: activeConfigs.map(({ func }) => func),
      providers: providerNames,
      estimatedMonthlyCost,
      recommendations
    };
  }

  /**
   * Generate .env template
   */
  generateEnvTemplate(): string {
    const functionDescriptions = {
      generator: 'Main SEO report generation and technical analysis',
      pagespeed: 'Page speed and performance analysis',
      backlink: 'Backlink analysis and domain authority',
      keyword: 'Keyword research and ranking analysis'
    };

    return `
# ======================================
# Function-Based SEO Provider Configuration
# ======================================

# Configure providers based on specific SEO functions
# Use the best provider for each function based on your needs

# GENERATOR - ${functionDescriptions.generator}
# Recommended: lighthouse (free), semrush (premium), screaming_frog (premium)
SEO_GENERATOR_PROVIDER=lighthouse
SEO_GENERATOR_API_KEY=your-google-api-key

# PAGESPEED - ${functionDescriptions.pagespeed}  
# Recommended: lighthouse (free), gtmetrix (free), webpagetest (free)
SEO_PAGESPEED_PROVIDER=lighthouse
SEO_PAGESPEED_API_KEY=your-google-api-key

# BACKLINK - ${functionDescriptions.backlink}
# Recommended: semrush (premium), ahrefs (premium)
# SEO_BACKLINK_PROVIDER=semrush
# SEO_BACKLINK_API_KEY=your-semrush-api-key

# KEYWORD - ${functionDescriptions.keyword}
# Recommended: semrush (premium), ahrefs (premium)
# SEO_KEYWORD_PROVIDER=semrush  
# SEO_KEYWORD_API_KEY=your-semrush-api-key

# ======================================
# Optional Global Configuration Overrides
# ======================================

# Rate limit (requests per minute)
# SEO_RATE_LIMIT=60

# Timeout (milliseconds)
# SEO_TIMEOUT=30000

# Retry count
# SEO_RETRIES=3

# ======================================
# Provider Capabilities & Setup URLs:
# ======================================
${Object.entries(this.PROVIDER_INFO)
  .map(([key, info]) => `# ${key}: ${info.capabilities.join(', ')} - ${info.setupUrl}`)
  .join('\n')}
`.trim();
  }

  /**
   * Set provider for a specific function
   */
  setProviderForFunction(func: SEOFunctionType, provider: SEOProviderType, apiKey: string): void {
    const providerKey = `SEO_${func.toUpperCase()}_PROVIDER` as keyof SEOEnvironmentConfig;
    const keyKey = `SEO_${func.toUpperCase()}_API_KEY` as keyof SEOEnvironmentConfig;
    
    (this.envConfig as any)[providerKey] = provider;
    (this.envConfig as any)[keyKey] = apiKey;
  }

  /**
   * Helper to get numeric configuration with fallback
   */
  private getNumberConfig(key: keyof SEOEnvironmentConfig, defaultValue: number): number {
    const value = this.envConfig[key];
    return value ? parseInt(value) : defaultValue;
  }
}

export const seoConfig = new SEOConfigService(); 