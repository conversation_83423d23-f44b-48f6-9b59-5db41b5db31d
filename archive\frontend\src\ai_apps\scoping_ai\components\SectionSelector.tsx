import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { SortableItem } from './SortableItem';
import { Plus, RefreshCw, X } from 'lucide-react';
import type { Section } from '../../../types';
import Loading from '../../../components/common/Loading';

const sectionOptions = [
  {
    id: 'option1',
    sections: [
      { id: '1', title: 'Introduction and Overview' },
      { id: '2', title: 'Proposed Solutions' },
      { id: '3', title: 'Implementation Strategy' },
      { id: '4', title: 'Pricing Options' },
      { id: '5', title: 'ROI Analysis' },
      { id: '6', title: 'Conclusion and Next Steps' },
    ],
  },
  {
    id: 'option2',
    sections: [
      { id: '7', title: 'Executive Summary' },
      { id: '8', title: 'Product Overview' },
      { id: '9', title: 'Implementation Strategy' },
      { id: '10', title: 'Pricing Options' },
      { id: '11', title: 'ROI' },
      { id: '12', title: 'Client Success Stories' },
    ],
  },
];

export default function SectionSelector() {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentOption, setCurrentOption] = useState(0);
  const [sections, setSections] = useState<Section[]>(sectionOptions[0].sections);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newSectionTitle, setNewSectionTitle] = useState('');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setSections((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const handleShowAnother = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate loading
    const nextOption = (currentOption + 1) % sectionOptions.length;
    setCurrentOption(nextOption);
    setSections(sectionOptions[nextOption].sections);
    setLoading(false);
  };

  const handleLooksGood = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate loading
    navigate('/templates', {
      state: {
        ...location.state,
        sections,
      },
    });
  };

  const handleAddSection = () => {
    if (newSectionTitle.trim()) {
      setSections([
        ...sections,
        { id: `custom-${Date.now()}`, title: newSectionTitle.trim() }
      ]);
      setNewSectionTitle('');
      setShowAddModal(false);
    }
  };

  const handleRemoveSection = (id: string) => {
    setSections(sections.filter(section => section.id !== id));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-8">
      {loading && <Loading />}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold text-indigo-900 mb-8 text-center">
          Choose and Arrange Your Sections
        </h2>

        <div className="bg-white rounded-xl shadow-xl p-8 mb-8">
          <div className="flex justify-between items-center mb-6">
            <p className="text-gray-600">Drag to reorder sections</p>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center gap-2 px-4 py-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
            >
              <Plus className="w-5 h-5" />
              Add Section
            </button>
          </div>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sections} strategy={verticalListSortingStrategy}>
              <div className="space-y-3">
                {sections.map((section) => (
                  <SortableItem key={section.id} id={section.id}>
                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 flex items-center justify-between group hover:border-indigo-200 transition-colors">
                      <span className="text-gray-800">{section.title}</span>
                      <button
                        onClick={() => handleRemoveSection(section.id)}
                        className="text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </SortableItem>
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>

        <div className="flex justify-center space-x-4">
          <button
            onClick={handleShowAnother}
            className="px-6 py-3 bg-white text-indigo-600 rounded-lg shadow-sm border border-indigo-600 hover:bg-indigo-50 flex items-center gap-2"
          >
            <RefreshCw className="w-5 h-5" />
            Show me another
          </button>
          <button
            onClick={handleLooksGood}
            className="px-6 py-3 bg-indigo-600 text-white rounded-lg shadow-sm hover:bg-indigo-700"
          >
            Continue to Templates
          </button>
        </div>
      </div>

      {/* Add Section Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-96 shadow-2xl">
            <h3 className="text-xl font-semibold mb-4">Add New Section</h3>
            <input
              type="text"
              value={newSectionTitle}
              onChange={(e) => setNewSectionTitle(e.target.value)}
              placeholder="Enter section title"
              className="w-full px-4 py-2 border rounded-lg mb-4 focus:ring-2 focus:ring-indigo-200 focus:border-indigo-500"
              autoFocus
            />
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setNewSectionTitle('');
                }}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleAddSection}
                disabled={!newSectionTitle.trim()}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-indigo-300"
              >
                Add Section
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}