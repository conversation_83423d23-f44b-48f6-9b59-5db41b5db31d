#!/bin/bash

# Script to copy base components from GitHub repository to a target repository
# Usage: ./copy-base-components.sh [<target_repo_path>] [--frontend-only|--backend-only]

set -e

# Default configurations - can be overridden with environment variables
TARGET_REPO_PATH="${TARGET_REPO_PATH:-}"
SOURCE_BRANCH="${SOURCE_BRANCH:-main}"
GITHUB_REPO_URL="${GITHUB_REPO_URL:-}"
APP_NAME="${APP_NAME:-base}"

# Directory paths - can be overridden with environment variables
FRONTEND_BASE_DIR="${FRONTEND_BASE_DIR:-base}"
BACKEND_BASE_DIR="${BACKEND_BASE_DIR:-server/base}"
REFERENCE_DIR_NAME="${REFERENCE_DIR_NAME:-reference/base-platform}"

# Source repository structure - this is the expected structure in the GitHub repo
SOURCE_FRONTEND_DIR="${SOURCE_FRONTEND_DIR:-altzero_base/base}"
SOURCE_BACKEND_DIR="${SOURCE_BACKEND_DIR:-altzero_base/server/base}"

# Components to copy directly - can be overridden with environment variables
FRONTEND_BASE_SUBDIRS="${FRONTEND_BASE_SUBDIRS:-components utils lib hooks config types services contextapi supabase pages}"
BACKEND_BASE_SUBDIRS="${BACKEND_BASE_SUBDIRS:-common}"

# Convert space-separated string to array
read -r -a FRONTEND_BASE_COMPONENTS <<< "$FRONTEND_BASE_SUBDIRS"
read -r -a BACKEND_BASE_COMPONENTS <<< "$BACKEND_BASE_SUBDIRS"

# Build full paths for frontend components
FRONTEND_BASE_DIRS=()
for component in "${FRONTEND_BASE_COMPONENTS[@]}"; do
  FRONTEND_BASE_DIRS+=("$FRONTEND_BASE_DIR/$component")
done

# Build full paths for backend components
BACKEND_BASE_DIRS=()
for component in "${BACKEND_BASE_COMPONENTS[@]}"; do
  BACKEND_BASE_DIRS+=("$BACKEND_BASE_DIR/$component")
done

# Everything else goes to reference directory
REFERENCE_DIRS=("layout" "header")
REFERENCE_FILES=(
  ".env.sample" 
  ".env.example"
  ".env.local.example"
  "wiki/ARCHITECTURE.md"
  "postcss.config.js"
  ".eslintrc.js"
  ".prettierrc"
  ".cursorrules" 
  "tsconfig.json" 
  "vite.config.ts"
)

# Display help message
show_help() {
  echo "Usage: $0 [<target_repo_path>] [--frontend-only|--backend-only]"
  echo ""
  echo "Arguments:"
  echo "  target_repo_path     Path to the target repository where components will be copied"
  echo ""
  echo "Options:"
  echo "  --frontend-only      Copy only frontend components"
  echo "  --backend-only       Copy only backend components"
  echo ""
  echo "Environment Variables (for customization):"
  echo "  TARGET_REPO_PATH     Path to the target repository (default: current directory)"
  echo "  GITHUB_REPO_URL      URL of the GitHub repository to clone from"
  echo "  SOURCE_BRANCH        Branch from the source repository to copy components from (default: main)"
  echo "  APP_NAME             Name of the app directory (default: 'base')"
  echo "  FRONTEND_BASE_DIR    Base frontend directory (default: 'base')"
  echo "  BACKEND_BASE_DIR     Base backend directory (default: 'server/base')" 
  echo "  REFERENCE_DIR_NAME   Name of reference directory (default: 'reference/base-platform')"
  echo "  SOURCE_FRONTEND_DIR  Path in source repo where frontend components are located (default: 'altzero_base/base')"
  echo "  SOURCE_BACKEND_DIR   Path in source repo where backend components are located (default: 'altzero_base/server/base')"
  echo "  FRONTEND_BASE_SUBDIRS Space-separated list of frontend subdirectories to copy"
  echo "  BACKEND_BASE_SUBDIRS  Space-separated list of backend subdirectories to copy"
  echo ""
  echo "Example:"
  echo "  $0 /path/to/target-repo --frontend-only"
  echo "  APP_NAME=\"myapp\" $0 /path/to/target-repo"
}

# Validate arguments
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
  show_help
  exit 0
fi

# Parse arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --frontend-only)
      COPY_OPTION="frontend"
      shift
      ;;
    --backend-only)
      COPY_OPTION="backend"
      shift
      ;;
    *)
      if [ -z "$TARGET_REPO_PATH" ]; then
        TARGET_REPO_PATH="$1"
      fi
      shift
      ;;
  esac
done

# Reset variables to force interactive input
unset SOURCE_BRANCH
unset TARGET_REPO_PATH
unset GITHUB_REPO_URL
unset APP_NAME
unset FRONTEND_BASE_DIR
unset BACKEND_BASE_DIR
unset SOURCE_FRONTEND_DIR
unset SOURCE_BACKEND_DIR

# Always ask for target repository path
read -p "Enter target repository path (default: current directory): " input_target
TARGET_REPO_PATH="${input_target:-$(pwd)}"

# Always ask for GitHub repository URL
read -p "Enter GitHub repository URL (e.g., https://github.com/username/repo.git): " input_repo
GITHUB_REPO_URL="${input_repo}"

# Validate GitHub URL
if [[ ! "$GITHUB_REPO_URL" =~ ^https://github\.com/ ]] && [[ ! "$GITHUB_REPO_URL" =~ ^git@github\.com: ]]; then
  echo "Error: Invalid GitHub repository URL. Please provide a valid GitHub URL."
  exit 1
fi

# Always ask for source branch
read -p "Enter source branch (default: main): " input_branch
SOURCE_BRANCH="${input_branch:-main}"

# Always ask for app directory name
read -p "Enter app directory name (default: base): " input_app_name
APP_NAME="${input_app_name:-base}"

# Source repository directories
read -p "Enter source frontend directory (default: altzero_base/base): " input_source_frontend
SOURCE_FRONTEND_DIR="${input_source_frontend:-altzero_base/base}"

read -p "Enter source backend directory (default: altzero_base/server/base): " input_source_backend
SOURCE_BACKEND_DIR="${input_source_backend:-altzero_base/server/base}"

# Always ask for frontend base directory - using plain 'base' as default
read -p "Enter target frontend directory (default: base): " input_frontend_dir
FRONTEND_BASE_DIR="${input_frontend_dir:-base}"

# Always ask for backend base directory - using 'server/base' as default
read -p "Enter target backend directory (default: server/base): " input_backend_dir
BACKEND_BASE_DIR="${input_backend_dir:-server/base}"

# Rebuild component paths based on new values
FRONTEND_BASE_DIRS=()
for component in "${FRONTEND_BASE_COMPONENTS[@]}"; do
  FRONTEND_BASE_DIRS+=("$FRONTEND_BASE_DIR/$component")
done

BACKEND_BASE_DIRS=()
for component in "${BACKEND_BASE_COMPONENTS[@]}"; do
  BACKEND_BASE_DIRS+=("$BACKEND_BASE_DIR/$component")
done

if [ -z "$COPY_OPTION" ]; then
  read -p "What to copy? (all/frontend/backend) [default: all]: " input_option
  COPY_OPTION="${input_option:-all}"
fi

# Validate inputs
echo ""
echo "Configuration Summary:"
echo "---------------------"
echo "Target Repository: $TARGET_REPO_PATH"
echo "Source GitHub URL: $GITHUB_REPO_URL"
echo "Source Branch: $SOURCE_BRANCH"
echo "App Name: $APP_NAME"
echo "Source Frontend Dir: $SOURCE_FRONTEND_DIR"
echo "Source Backend Dir: $SOURCE_BACKEND_DIR"
echo "Target Frontend Dir: $FRONTEND_BASE_DIR"
echo "Target Backend Dir: $BACKEND_BASE_DIR"
echo "Copy Option: $COPY_OPTION"
echo ""

# Show a more detailed explanation of directory structure that will be created
echo "Directory Structure to be Created:"
echo "--------------------------------"
for component in "${FRONTEND_BASE_COMPONENTS[@]}"; do
  echo "- $TARGET_REPO_PATH/$FRONTEND_BASE_DIR/$component"
done
for component in "${BACKEND_BASE_COMPONENTS[@]}"; do
  echo "- $TARGET_REPO_PATH/$BACKEND_BASE_DIR/$component"
done
echo ""

read -p "Is this configuration correct? (y/n): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
  echo "Operation cancelled by user."
  exit 0
fi

# Check if target directory exists
if [ ! -d "$TARGET_REPO_PATH" ]; then
  echo "Error: Target repository directory does not exist: $TARGET_REPO_PATH"
  exit 1
fi

# Create a temporary directory to clone the source repository
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Clone the source repository to the temporary directory with the specified branch
echo "Cloning GitHub repository branch '$SOURCE_BRANCH' to temporary directory..."
if ! git clone --branch "$SOURCE_BRANCH" --single-branch "$GITHUB_REPO_URL" "$TEMP_DIR"; then
  echo "Error: Failed to clone repository from $GITHUB_REPO_URL with branch $SOURCE_BRANCH"
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Create the reference directory
REFERENCE_DIR="$TARGET_REPO_PATH/$REFERENCE_DIR_NAME"
mkdir -p "$REFERENCE_DIR"

# Function to copy components from source to target
copy_components() {
  local source_base_dir="$1"
  local target_base_dir="$2"
  local components=("${@:3}")
  
  # Check if source directory exists
  if [ ! -d "$TEMP_DIR/$source_base_dir" ]; then
    echo "Warning: Source directory $source_base_dir not found in the cloned repository"
    echo "Looking for components individually..."
    
    # Try to find components anyway
    for component in "${components[@]}"; do
      # Find potential matches
      potential_dirs=$(find "$TEMP_DIR" -type d -name "$component" | grep -v "node_modules")
      
      if [ -n "$potential_dirs" ]; then
        first_match=$(echo "$potential_dirs" | head -n 1)
        target_dir="$TARGET_REPO_PATH/$target_base_dir/$component"
        
        echo "Found component '$component' at $first_match"
        echo "Copying to $target_dir..."
        
        mkdir -p "$target_dir"
        cp -R "$first_match"/* "$target_dir" || true
        
        echo "Successfully copied $component"
      else
        echo "Warning: Component '$component' not found in source repository"
      fi
    done
    
    return
  fi
  
  # Process each component
  for component in "${components[@]}"; do
    source_dir="$TEMP_DIR/$source_base_dir/$component"
    target_dir="$TARGET_REPO_PATH/$target_base_dir/$component"
    
    if [ -d "$source_dir" ]; then
      echo "Copying $component from $source_base_dir to $target_base_dir..."
      
      # Create target directory if it doesn't exist
      mkdir -p "$target_dir"
      
      # Copy files
      cp -R "$source_dir"/* "$target_dir" || true
      
      echo "Successfully copied $component"
    else
      echo "Warning: Component $component not found in $source_base_dir"
    fi
  done
}

# Function to copy directories to reference
copy_to_reference() {
  local dirs=("$@")
  local source_dir=""
  local ref_dir=""
  
  for dir in "${dirs[@]}"; do
    # Find the directory in the source repo
    potential_dirs=$(find "$TEMP_DIR" -type d -name "$(basename "$dir")" | grep -v "node_modules")
    
    if [ -n "$potential_dirs" ]; then
      first_match=$(echo "$potential_dirs" | head -n 1)
      ref_dir="$REFERENCE_DIR/$dir"
      
      echo "Copying $dir to reference directory..."
      
      # Create reference directory if it doesn't exist
      mkdir -p "$ref_dir"
      
      # Copy files
      cp -R "$first_match"/* "$ref_dir" || true
      
      echo "Successfully copied $dir to reference"
    else
      echo "Warning: Directory $dir not found in source repository"
    fi
  done
}

# Copy frontend components if requested
if [ "$COPY_OPTION" == "all" ] || [ "$COPY_OPTION" == "frontend" ]; then
  echo "Copying frontend base components..."
  copy_components "$SOURCE_FRONTEND_DIR" "$FRONTEND_BASE_DIR" "${FRONTEND_BASE_COMPONENTS[@]}"
  
  echo "Copying layout and header to reference directory..."
  copy_to_reference "${REFERENCE_DIRS[@]}"
fi

# Copy backend components if requested
if [ "$COPY_OPTION" == "all" ] || [ "$COPY_OPTION" == "backend" ]; then
  echo "Copying backend base components..."
  copy_components "$SOURCE_BACKEND_DIR" "$BACKEND_BASE_DIR" "${BACKEND_BASE_COMPONENTS[@]}"
fi

# Copy files to reference directory
echo "Copying reference files..."
for file in "${REFERENCE_FILES[@]}"; do
  potential_files=$(find "$TEMP_DIR" -name "$(basename "$file")" | grep -v "node_modules")
  
  if [ -n "$potential_files" ]; then
    # Use the first match
    first_match=$(echo "$potential_files" | head -n 1)
    
    # Create necessary directory structure in reference dir
    dir_path=$(dirname "$file")
    if [ "$dir_path" != "." ]; then
      mkdir -p "$REFERENCE_DIR/$dir_path"
    fi
    
    echo "Copying $(basename "$file") to reference directory..."
    cp "$first_match" "$REFERENCE_DIR/$file" || true
  fi
done

# Handle package.json - create a comparison file instead of overwriting
package_json_files=$(find "$TEMP_DIR" -name "package.json" -not -path "*/node_modules/*")
if [ -n "$package_json_files" ]; then
  echo "Copying package.json to reference directory..."
  # Use the first match
  first_match=$(echo "$package_json_files" | head -n 1)
  cp "$first_match" "$REFERENCE_DIR/package.json" || true
fi

# Handle server package.json if it exists
server_package_json_files=$(find "$TEMP_DIR" -name "package.json" -path "*/server/*" -not -path "*/node_modules/*")
if [ -n "$server_package_json_files" ]; then
  echo "Copying server package.json to reference directory..."
  mkdir -p "$REFERENCE_DIR/server"
  # Use the first match
  first_match=$(echo "$server_package_json_files" | head -n 1)
  cp "$first_match" "$REFERENCE_DIR/server/package.json" || true
fi

# Create reference README
cat > "$REFERENCE_DIR/README.md" << EOF
# AltZero Base Platform Reference

This directory contains reference files from the AltZero base platform.
These files are provided for reference only and should not be directly used in your application.

## Contents

- Layout components
- Header components
- Configuration templates
- Environment variable examples
- Package.json comparison
- Architecture documentation

## Usage

Use these files as a reference when updating your application to match the base platform requirements.
Especially pay attention to required dependencies and environment variables.
EOF

# Clean up
echo "Cleaning up temporary directory..."
rm -rf "$TEMP_DIR"

echo "Base components successfully copied from branch '$SOURCE_BRANCH' to target repository"
echo "Layout, header and configuration files have been placed in $REFERENCE_DIR"
echo "Remember to review the copied files and adjust imports/paths as needed" 