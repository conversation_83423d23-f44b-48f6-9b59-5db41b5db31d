import React, { useRef } from "react";
import { Button } from "@base/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@base/components/ui/dropdown-menu";
import {
  ChevronDown,
  Edit,
  FileText,
  ImageIcon,
  Loader2,
  Wand2,
} from "lucide-react";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { AIRegenerationOption, Block, Section } from "../types/documentTypes";
import { SortableBlock } from "./SortableBlock";

interface SectionEditorProps {
  section: Section;
  onTitleChange: (newTitle: string) => void;
  onAddBlock: (type: Block["type"]) => void;
  onUpdateBlock: (blockId: string, updates: Partial<Block>) => void;
  onDeleteBlock: (blockId: string) => void;
  onRegenerateWithAI?: (enhancementType: string) => void;
  isRegenerating?: boolean;
  enhancementOptions?: AIRegenerationOption[];
}

export const SectionEditor: React.FC<SectionEditorProps> = ({
  section,
  onTitleChange,
  onAddBlock,
  onUpdateBlock,
  onDeleteBlock,
  onRegenerateWithAI,
  isRegenerating = false,
  enhancementOptions = [],
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="mb-8">
      <div className="mb-4 flex flex-col gap-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Section Title</h3>
          {onRegenerateWithAI && (
            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2"
                    disabled={isRegenerating}
                  >
                    <Wand2 className="h-4 w-4" />
                    Regenerate with AI
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>AI Enhancement</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {enhancementOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.id}
                      onClick={() => onRegenerateWithAI(option.id)}
                      disabled={isRegenerating}
                      className="gap-2 cursor-pointer"
                    >
                      {option.icon}
                      <div className="flex flex-col">
                        <span>{option.label}</span>
                        <span className="text-xs text-muted-foreground">
                          {option.description}
                        </span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
        <input
          type="text"
          value={section.title}
          onChange={(e) => onTitleChange(e.target.value)}
          className="w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <h3 className="text-lg font-semibold mb-2">Content Blocks</h3>

      {isRegenerating && (
        <div className="flex items-center justify-center p-6 my-4 bg-blue-50 rounded-md">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500 mr-2" />
          <span>Enhancing content with AI...</span>
        </div>
      )}

      <SortableContext
        items={section.blocks.map((block) => block.id)}
        strategy={verticalListSortingStrategy}
      >
        {section.blocks.map((block) => (
          <SortableBlock
            key={block.id}
            block={block}
            sectionId={section.id}
            onUpdate={(updates) => onUpdateBlock(block.id, updates)}
            onDelete={() => onDeleteBlock(block.id)}
            isRegenerating={isRegenerating}
          />
        ))}
      </SortableContext>

      <div className="mt-4 flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onAddBlock("text")}
          className="gap-2"
          disabled={isRegenerating}
        >
          <Edit className="h-4 w-4" />
          Paragraph
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onAddBlock("header")}
          className="gap-2"
          disabled={isRegenerating}
        >
          <FileText className="h-4 w-4" />
          Heading
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onAddBlock("list")}
          className="gap-2"
          disabled={isRegenerating}
        >
          <FileText className="h-4 w-4" />
          List
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleImageUpload}
          className="gap-2"
          disabled={isRegenerating}
        >
          <ImageIcon className="h-4 w-4" />
          Image
        </Button>
      </div>
    </div>
  );
};
