import { getApiUrl } from '../config/backend';
import { ENDPOINTS } from '../utils/constants';
import { Message } from '../hooks/useChat';

interface ChatRequest {
  query: string;
  chatHistory?: Array<{ role: string; content: string }>;
}

interface ChatResponse {
  answer: string;
  sources: Array<{
    content: string;
    metadata: Record<string, any>;
  }>;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  createdAt: string;
}

interface ProposalRequest {
  clientName: string;
  clientDescription: string;
  projectScope: string;
}

export interface ProposalResponse {
  executiveSummary: string;
  sections: Array<{
    title: string;
    content: string;
  }>;
}

interface HealthCheckResponse {
  status: 'ok' | 'error';
  backend: 'python' | 'typescript';
  version?: string;
}

const API_KEY = import.meta.env.VITE_API_KEY;

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${API_KEY}`,
};

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

// Helper function to handle API errors
const handleError = (error: any) => {
  console.error('API Error:', error);
  throw error;
};

const api = {
  async chat(request: ChatRequest): Promise<ChatResponse> {
    const response = await fetch(getApiUrl('chat'), {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
    });
    if (!response.ok) throw new Error('Chat request failed');
    return response.json();
  },

  async listDocuments(): Promise<Document[]> {
    const response = await fetch(getApiUrl('documents'), {
      headers,
    });
    if (!response.ok) throw new Error('Failed to fetch documents');
    return response.json();
  },

  async uploadDocument(file: File): Promise<Document> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(getApiUrl('documents'), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
      },
      body: formData,
    });
    if (!response.ok) throw new Error('Document upload failed');
    return response.json();
  },

  async deleteDocument(id: string): Promise<void> {
    const response = await fetch(`${getApiUrl('documents')}/${id}`, {
      method: 'DELETE',
      headers,
    });
    if (!response.ok) throw new Error('Document deletion failed');
  },

  async generateProposal(request: ProposalRequest): Promise<ProposalResponse> {
    const response = await fetch(getApiUrl('proposals'), {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
    });
    if (!response.ok) throw new Error('Proposal generation failed');
    return response.json();
  },

  async healthCheck(): Promise<HealthCheckResponse> {
    const response = await fetch(`${getApiUrl('chat')}/health`, {
      headers,
    });
    if (!response.ok) throw new Error('Health check failed');
    return response.json();
  },

  async generateProposalStream(
    request: ProposalRequest,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const response = await fetch(`${getApiUrl('proposals')}/stream`, {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
    });

    if (!response.ok) throw new Error('Proposal streaming failed');
    if (!response.body) throw new Error('No response body');

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      onChunk(chunk);
    }
  },
};

export const proposalAPI = {
  baseUrl: getApiUrl('proposals').replace('/proposals', ''),
  supportsStreaming: true,
  generateProposal: api.generateProposal,
  generateProposalStream: api.generateProposalStream
};

export const chatAPI = {
  async streamChat(message: string, history: Message[], documentIds?: string[]): Promise<ReadableStream> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.CHAT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: message,
          history,
          documentIds
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body received');
      }

      return response.body;
    } catch (error) {
      return handleError(error);
    }
  },

  async chat(message: string, history: Message[], documentIds?: string[]): Promise<string> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.CHAT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: message,
          history,
          documentIds
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      return handleError(error);
    }
  }
};

export type { ChatRequest, ChatResponse, Document };
export { api as default }; 