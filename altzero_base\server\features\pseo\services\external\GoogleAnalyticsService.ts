// =====================================================
// GOOGLE ANALYTICS INTEGRATION SERVICE
// Phase 5: External API Integration
// =====================================================

import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

export interface AnalyticsMetrics {
  sessions: number;
  pageviews: number;
  users: number;
  bounceRate: number;
  avgSessionDuration: number;
  conversions?: number;
  conversionRate?: number;
  goalCompletions?: number;
}

export interface AnalyticsPageData {
  pagePath: string;
  pageTitle: string;
  sessions: number;
  pageviews: number;
  uniquePageviews: number;
  avgTimeOnPage: number;
  bounceRate: number;
  exitRate: number;
  entrances: number;
}

export interface AnalyticsTrafficSource {
  source: string;
  medium: string;
  campaign?: string;
  sessions: number;
  users: number;
  newUsers: number;
  bounceRate: number;
  avgSessionDuration: number;
}

export interface AnalyticsDeviceData {
  deviceCategory: 'desktop' | 'mobile' | 'tablet';
  sessions: number;
  users: number;
  pageviews: number;
  bounceRate: number;
  avgSessionDuration: number;
}

export interface AnalyticsGoalData {
  goalName: string;
  goalCompletions: number;
  goalConversionRate: number;
  goalValue: number;
}

export interface AnalyticsSiteOverview {
  totalSessions: number;
  totalUsers: number;
  totalPageviews: number;
  averageBounceRate: number;
  averageSessionDuration: number;
  totalConversions: number;
  conversionRate: number;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface AnalyticsIntegrationConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  refreshToken?: string;
  propertyId: string;
}

export class GoogleAnalyticsService {
  private oauth2Client: OAuth2Client;
  private analytics: any;
  private analyticsData: any;
  private propertyId: string;
  private isAuthenticated: boolean = false;

  constructor(config: AnalyticsIntegrationConfig) {
    // Initialize OAuth2 client
    this.oauth2Client = new google.auth.OAuth2(
      config.clientId,
      config.clientSecret,
      config.redirectUri
    );

    this.propertyId = config.propertyId;

    // Set refresh token if available
    if (config.refreshToken) {
      this.oauth2Client.setCredentials({
        refresh_token: config.refreshToken
      });
      this.isAuthenticated = true;
    }

    // Initialize Analytics APIs
    this.analytics = google.analytics({
      version: 'v3',
      auth: this.oauth2Client
    });

    this.analyticsData = google.analyticsdata({
      version: 'v1beta',
      auth: this.oauth2Client
    });
  }

  /**
   * Generate OAuth2 authorization URL
   */
  generateAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/analytics.readonly',
      'https://www.googleapis.com/auth/analytics'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(authCode: string): Promise<void> {
    try {
      const { tokens } = await this.oauth2Client.getToken(authCode);
      this.oauth2Client.setCredentials(tokens);
      this.isAuthenticated = true;
      
      console.log('✅ Google Analytics authentication successful');
    } catch (error) {
      console.error('❌ Failed to exchange code for tokens:', error);
      throw new Error('Analytics authentication failed');
    }
  }

  /**
   * Get site overview metrics
   */
  async getSiteOverview(
    startDate: string, 
    endDate: string
  ): Promise<AnalyticsSiteOverview> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Analytics');
    }

    try {
      const response = await this.analyticsData.properties.runReport({
        property: `properties/${this.propertyId}`,
        requestBody: {
          dateRanges: [{ startDate, endDate }],
          metrics: [
            { name: 'sessions' },
            { name: 'totalUsers' },
            { name: 'screenPageViews' },
            { name: 'bounceRate' },
            { name: 'averageSessionDuration' },
            { name: 'conversions' }
          ]
        }
      });

      const row = response.data.rows?.[0];
      const metrics = row?.metricValues || [];

      const conversionRate = parseFloat(metrics[0]?.value || '0') > 0 
        ? (parseFloat(metrics[5]?.value || '0') / parseFloat(metrics[0]?.value || '1')) * 100 
        : 0;

      return {
        totalSessions: parseInt(metrics[0]?.value || '0'),
        totalUsers: parseInt(metrics[1]?.value || '0'),
        totalPageviews: parseInt(metrics[2]?.value || '0'),
        averageBounceRate: parseFloat(metrics[3]?.value || '0'),
        averageSessionDuration: parseFloat(metrics[4]?.value || '0'),
        totalConversions: parseInt(metrics[5]?.value || '0'),
        conversionRate,
        dateRange: { start: startDate, end: endDate }
      };
    } catch (error) {
      console.error('Failed to fetch Analytics site overview:', error);
      throw error;
    }
  }

  /**
   * Get top performing pages
   */
  async getTopPages(
    startDate: string,
    endDate: string,
    limit: number = 100
  ): Promise<AnalyticsPageData[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Analytics');
    }

    try {
      const response = await this.analyticsData.properties.runReport({
        property: `properties/${this.propertyId}`,
        requestBody: {
          dateRanges: [{ startDate, endDate }],
          dimensions: [
            { name: 'pagePath' },
            { name: 'pageTitle' }
          ],
          metrics: [
            { name: 'sessions' },
            { name: 'screenPageViews' },
            { name: 'bounceRate' },
            { name: 'averageSessionDuration' }
          ],
          orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
          limit
        }
      });

      return response.data.rows?.map((row: any) => {
        const dimensions = row.dimensionValues || [];
        const metrics = row.metricValues || [];

        return {
          pagePath: dimensions[0]?.value || '',
          pageTitle: dimensions[1]?.value || '',
          sessions: parseInt(metrics[0]?.value || '0'),
          pageviews: parseInt(metrics[1]?.value || '0'),
          uniquePageviews: parseInt(metrics[1]?.value || '0'), // Approximation
          avgTimeOnPage: parseFloat(metrics[3]?.value || '0'),
          bounceRate: parseFloat(metrics[2]?.value || '0'),
          exitRate: parseFloat(metrics[2]?.value || '0'), // Approximation
          entrances: parseInt(metrics[0]?.value || '0') // Approximation
        };
      }) || [];
    } catch (error) {
      console.error('Failed to fetch top pages:', error);
      throw error;
    }
  }

  /**
   * Get traffic sources breakdown
   */
  async getTrafficSources(
    startDate: string,
    endDate: string,
    limit: number = 50
  ): Promise<AnalyticsTrafficSource[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Analytics');
    }

    try {
      const response = await this.analyticsData.properties.runReport({
        property: `properties/${this.propertyId}`,
        requestBody: {
          dateRanges: [{ startDate, endDate }],
          dimensions: [
            { name: 'sessionSource' },
            { name: 'sessionMedium' },
            { name: 'sessionCampaignName' }
          ],
          metrics: [
            { name: 'sessions' },
            { name: 'totalUsers' },
            { name: 'newUsers' },
            { name: 'bounceRate' },
            { name: 'averageSessionDuration' }
          ],
          orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
          limit
        }
      });

      return response.data.rows?.map((row: any) => {
        const dimensions = row.dimensionValues || [];
        const metrics = row.metricValues || [];

        return {
          source: dimensions[0]?.value || '',
          medium: dimensions[1]?.value || '',
          campaign: dimensions[2]?.value || undefined,
          sessions: parseInt(metrics[0]?.value || '0'),
          users: parseInt(metrics[1]?.value || '0'),
          newUsers: parseInt(metrics[2]?.value || '0'),
          bounceRate: parseFloat(metrics[3]?.value || '0'),
          avgSessionDuration: parseFloat(metrics[4]?.value || '0')
        };
      }) || [];
    } catch (error) {
      console.error('Failed to fetch traffic sources:', error);
      throw error;
    }
  }

  /**
   * Get device breakdown
   */
  async getDeviceData(
    startDate: string,
    endDate: string
  ): Promise<AnalyticsDeviceData[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Analytics');
    }

    try {
      const response = await this.analyticsData.properties.runReport({
        property: `properties/${this.propertyId}`,
        requestBody: {
          dateRanges: [{ startDate, endDate }],
          dimensions: [{ name: 'deviceCategory' }],
          metrics: [
            { name: 'sessions' },
            { name: 'totalUsers' },
            { name: 'screenPageViews' },
            { name: 'bounceRate' },
            { name: 'averageSessionDuration' }
          ]
        }
      });

      return response.data.rows?.map((row: any) => {
        const dimensions = row.dimensionValues || [];
        const metrics = row.metricValues || [];

        return {
          deviceCategory: dimensions[0]?.value as 'desktop' | 'mobile' | 'tablet',
          sessions: parseInt(metrics[0]?.value || '0'),
          users: parseInt(metrics[1]?.value || '0'),
          pageviews: parseInt(metrics[2]?.value || '0'),
          bounceRate: parseFloat(metrics[3]?.value || '0'),
          avgSessionDuration: parseFloat(metrics[4]?.value || '0')
        };
      }) || [];
    } catch (error) {
      console.error('Failed to fetch device data:', error);
      throw error;
    }
  }

  /**
   * Get goal completions data
   */
  async getGoalData(
    startDate: string,
    endDate: string
  ): Promise<AnalyticsGoalData[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Analytics');
    }

    try {
      const response = await this.analyticsData.properties.runReport({
        property: `properties/${this.propertyId}`,
        requestBody: {
          dateRanges: [{ startDate, endDate }],
          dimensions: [{ name: 'eventName' }],
          metrics: [
            { name: 'conversions' },
            { name: 'totalRevenue' }
          ],
          dimensionFilter: {
            filter: {
              fieldName: 'eventName',
              stringFilter: {
                matchType: 'CONTAINS',
                value: 'goal'
              }
            }
          }
        }
      });

      return response.data.rows?.map((row: any) => {
        const dimensions = row.dimensionValues || [];
        const metrics = row.metricValues || [];

        const conversions = parseInt(metrics[0]?.value || '0');
        const totalSessions = 1000; // Would need to fetch this separately

        return {
          goalName: dimensions[0]?.value || '',
          goalCompletions: conversions,
          goalConversionRate: totalSessions > 0 ? (conversions / totalSessions) * 100 : 0,
          goalValue: parseFloat(metrics[1]?.value || '0')
        };
      }) || [];
    } catch (error) {
      console.error('Failed to fetch goal data:', error);
      throw error;
    }
  }

  /**
   * Get page-specific analytics data
   */
  async getPageAnalytics(
    pagePath: string,
    startDate: string,
    endDate: string
  ): Promise<AnalyticsPageData> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Analytics');
    }

    try {
      const response = await this.analyticsData.properties.runReport({
        property: `properties/${this.propertyId}`,
        requestBody: {
          dateRanges: [{ startDate, endDate }],
          dimensions: [
            { name: 'pagePath' },
            { name: 'pageTitle' }
          ],
          metrics: [
            { name: 'sessions' },
            { name: 'screenPageViews' },
            { name: 'bounceRate' },
            { name: 'averageSessionDuration' }
          ],
          dimensionFilter: {
            filter: {
              fieldName: 'pagePath',
              stringFilter: {
                matchType: 'EXACT',
                value: pagePath
              }
            }
          }
        }
      });

      const row = response.data.rows?.[0];
      if (!row) {
        throw new Error(`No analytics data found for page: ${pagePath}`);
      }

      const dimensions = row.dimensionValues || [];
      const metrics = row.metricValues || [];

      return {
        pagePath: dimensions[0]?.value || pagePath,
        pageTitle: dimensions[1]?.value || '',
        sessions: parseInt(metrics[0]?.value || '0'),
        pageviews: parseInt(metrics[1]?.value || '0'),
        uniquePageviews: parseInt(metrics[1]?.value || '0'),
        avgTimeOnPage: parseFloat(metrics[3]?.value || '0'),
        bounceRate: parseFloat(metrics[2]?.value || '0'),
        exitRate: parseFloat(metrics[2]?.value || '0'),
        entrances: parseInt(metrics[0]?.value || '0')
      };
    } catch (error) {
      console.error('Failed to fetch page analytics:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive analytics data for pSEO analysis
   */
  async getDataForPSEOAnalysis(
    targetPages: string[]
  ): Promise<{
    siteOverview: AnalyticsSiteOverview;
    topPages: AnalyticsPageData[];
    trafficSources: AnalyticsTrafficSource[];
    deviceBreakdown: AnalyticsDeviceData[];
    goalData: AnalyticsGoalData[];
    pageAnalytics: AnalyticsPageData[];
  }> {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    try {
      // Get all analytics data in parallel
      const [
        siteOverview,
        topPages,
        trafficSources,
        deviceBreakdown,
        goalData
      ] = await Promise.all([
        this.getSiteOverview(startDate, endDate),
        this.getTopPages(startDate, endDate, 50),
        this.getTrafficSources(startDate, endDate, 20),
        this.getDeviceData(startDate, endDate),
        this.getGoalData(startDate, endDate)
      ]);

      // Get analytics for specific target pages
      const pageAnalytics: AnalyticsPageData[] = [];
      const limitedPages = targetPages.slice(0, 10); // Limit for performance

      for (const pagePath of limitedPages) {
        try {
          const pageData = await this.getPageAnalytics(pagePath, startDate, endDate);
          pageAnalytics.push(pageData);
        } catch (error) {
          console.warn(`Failed to get analytics for page ${pagePath}:`, error);
        }
      }

      return {
        siteOverview,
        topPages,
        trafficSources,
        deviceBreakdown,
        goalData,
        pageAnalytics
      };
    } catch (error) {
      console.error('Failed to get Analytics data for pSEO analysis:', error);
      throw error;
    }
  }

  /**
   * Check if service is authenticated
   */
  isAuthenticatedStatus(): boolean {
    return this.isAuthenticated;
  }

  /**
   * Get refresh token (for storage)
   */
  getRefreshToken(): string | null {
    return this.oauth2Client.credentials.refresh_token || null;
  }
} 