import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  // Removed insecure environment variable exposure - now using server-side API calls
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./"),
      "@base": path.resolve(__dirname, "./base"),
      "@layout": path.resolve(__dirname, "./layout"),
      "@header": path.resolve(__dirname, "./header"),
    },
  },
  server: {
    port: 5173,
    open: false,
    proxy: {
      "/api": {
        target: "http://localhost:3001",
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    sourcemap: true,
  },
});
