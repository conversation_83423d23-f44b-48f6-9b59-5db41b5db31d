import express from 'express';
import { BackendPlugin } from '../../../plugins/loader';
import scopingAiRoutes from '../routes/scopingAi';

// Create the backend plugin for ScopingAI
const scopingAiBackendPlugin: BackendPlugin = {
  router: scopingAiRoutes,
  config: {
    name: 'ScopingAI API',
    version: '1.0.0',
    apiPrefix: '/api/scopingai'
  },
  initialize: async () => {
    console.log('🎯 ScopingAI backend plugin initialized');
  },
  cleanup: async () => {
    console.log('🎯 ScopingAI backend plugin cleaned up');
  },
  healthCheck: async () => {
    try {
      // Basic health check - could be expanded to check database connectivity
      return true;
    } catch (error) {
      console.error('ScopingAI backend health check failed:', error);
      return false;
    }
  }
};

export default scopingAiBackendPlugin;
