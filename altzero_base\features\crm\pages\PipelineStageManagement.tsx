import React, { useState, useEffect } from 'react';
import { Layers, Search, Plus, Edit, Trash2, ArrowU<PERSON>, ArrowDown } from 'lucide-react';
import CRMLayout from '../components/CRMLayout';

interface PipelineStage {
  id: string;
  pipeline_id: string;
  name: string;
  description?: string;
  order_index: number;
  probability: number;
  is_closed: boolean;
  created_at: string;
  updated_at: string;
}

interface Pipeline {
  id: string;
  name: string;
}

const PipelineStageManagement: React.FC = () => {
  const [stages, setStages] = useState<PipelineStage[]>([]);
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [selectedPipeline, setSelectedPipeline] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadPipelines();
  }, []);

  useEffect(() => {
    if (selectedPipeline) {
      loadStages(selectedPipeline);
    }
  }, [selectedPipeline]);

  const loadPipelines = async () => {
    try {
      // TODO: Implement API call to fetch pipelines
      // const response = await crmService.getPipelines();
      // setPipelines(response.data);
      
      // Mock data for now
      const mockPipelines = [
        { id: '1', name: 'Sales Pipeline' },
        { id: '2', name: 'Enterprise Pipeline' },
        { id: '3', name: 'Partner Pipeline' }
      ];
      setPipelines(mockPipelines);
      if (mockPipelines.length > 0) {
        setSelectedPipeline(mockPipelines[0].id);
      }
    } catch (error) {
      console.error('Error loading pipelines:', error);
    }
  };

  const loadStages = async (pipelineId: string) => {
    try {
      setLoading(true);
      // TODO: Implement API call to fetch pipeline stages
      // const response = await crmService.getPipelineStages(pipelineId);
      // setStages(response.data);
      
      // Mock data for now
      setStages([
        {
          id: '1',
          pipeline_id: pipelineId,
          name: 'Lead',
          description: 'Initial contact made',
          order_index: 1,
          probability: 10,
          is_closed: false,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          pipeline_id: pipelineId,
          name: 'Qualified',
          description: 'Lead has been qualified',
          order_index: 2,
          probability: 25,
          is_closed: false,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '3',
          pipeline_id: pipelineId,
          name: 'Proposal',
          description: 'Proposal sent to prospect',
          order_index: 3,
          probability: 50,
          is_closed: false,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '4',
          pipeline_id: pipelineId,
          name: 'Negotiation',
          description: 'In negotiation phase',
          order_index: 4,
          probability: 75,
          is_closed: false,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '5',
          pipeline_id: pipelineId,
          name: 'Closed Won',
          description: 'Deal successfully closed',
          order_index: 5,
          probability: 100,
          is_closed: true,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        }
      ]);
    } catch (error) {
      console.error('Error loading stages:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredStages = stages.filter(stage =>
    stage.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    stage.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ).sort((a, b) => a.order_index - b.order_index);

  const selectedPipelineName = pipelines.find(p => p.id === selectedPipeline)?.name || '';

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Layers className="w-8 h-8 text-blue-600 mr-3" />
              Pipeline Stages
            </h1>
            <p className="text-gray-600 mt-1">Configure stages for your sales pipelines</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            New Stage
          </button>
        </div>

        {/* Pipeline Selection and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="w-64">
              <label className="block text-sm font-medium text-gray-700 mb-1">Pipeline</label>
              <select
                value={selectedPipeline}
                onChange={(e) => setSelectedPipeline(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {pipelines.map((pipeline) => (
                  <option key={pipeline.id} value={pipeline.id}>
                    {pipeline.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search stages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Stages Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              {selectedPipelineName} - Stages
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Order</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Stage Name</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Description</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Probability</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-gray-500">
                      Loading stages...
                    </td>
                  </tr>
                ) : filteredStages.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-gray-500">
                      {searchTerm ? 'No stages found matching your search.' : 'No stages found. Create your first stage to get started.'}
                    </td>
                  </tr>
                ) : (
                  filteredStages.map((stage) => (
                    <tr key={stage.id} className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-1">
                          <span className="font-medium text-gray-900">{stage.order_index}</span>
                          <div className="flex flex-col space-y-1">
                            <button className="p-0.5 text-gray-400 hover:text-blue-600 transition-colors">
                              <ArrowUp className="w-3 h-3" />
                            </button>
                            <button className="p-0.5 text-gray-400 hover:text-blue-600 transition-colors">
                              <ArrowDown className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{stage.name}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600">{stage.description || '-'}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-900">{stage.probability}%</div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          stage.is_closed 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {stage.is_closed ? 'Closed' : 'Open'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-red-600 transition-colors">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-800">
              <strong>{filteredStages.length}</strong> stages in {selectedPipelineName}
            </div>
            <div className="text-xs text-blue-600">
              💡 Tip: Use probability percentages to forecast revenue and track conversion rates
            </div>
          </div>
        </div>
      </div>
    </CRMLayout>
  );
};

export default PipelineStageManagement;
