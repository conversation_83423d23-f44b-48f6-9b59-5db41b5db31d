import React, { useEffect } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { CopilotKit } from "@copilotkit/react-core";
import { UserProvider, useUser } from "./base/contextapi/UserContext";
import { SubscriptionProvider } from "./base/contextapi/SubscriptionContext";
import { PluginProvider, PluginRoutes, PluginProviders } from "./plugins/loader";
import { Toaster } from "./base/components/ui/toaster";
import { supabase, setupSessionRefresh } from "./base/utils/supabaseClient";
import MainLayout from "./layout/MainLayout";
import Login from "./base/components/auth/Login";
import Signup from "./base/components/auth/Signup";
import Dashboard from "./base/pages/Dashboard";
import Profile from "./base/pages/profile";
import ForgotPassword from "./base/pages/forgot-password";
import UpdatePassword from "./base/pages/update-password";
import Settings from "./base/pages/Settings";

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useUser();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const AppRoutes = () => {
  const { user, isLoading } = useUser();

  useEffect(() => {
    setupSessionRefresh();

    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      console.log("Current session:", data.session ? "Active" : "None");
    };

    checkSession();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route
        path="/login"
        element={user ? <Navigate to="/dashboard" replace /> : <Login />}
      />
      <Route
        path="/signup"
        element={user ? <Navigate to="/dashboard" replace /> : <Signup />}
      />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/update-password" element={<UpdatePassword />} />

      {/* Protected routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <Profile />
          </ProtectedRoute>
        }
      />

      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <Settings />
          </ProtectedRoute>
        }
      />

      {/* Redirect to dashboard if logged in, otherwise to login */}
      <Route
        path="/"
        element={
          user ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <Navigate to="/login" replace />
          )
        }
      />

      {/* 🆕 Plugin Routes - All plugins handled dynamically (includes 404 fallback) */}
      <Route
        path="/*"
        element={
          <ProtectedRoute>
            <PluginRoutes />
          </ProtectedRoute>
        }
      />
    </Routes>
  );
};

const App = () => {
  return (
    <BrowserRouter>
      <UserProvider>
        <SubscriptionProvider>
          <PluginProvider>
            <PluginProviders>
              <CopilotKit runtimeUrl="/api/copilotkit/openai">
                <MainLayout>
                  <AppRoutes />
                </MainLayout>
                <Toaster />
              </CopilotKit>
            </PluginProviders>
          </PluginProvider>
        </SubscriptionProvider>
      </UserProvider>
    </BrowserRouter>
  );
};

export default App;
