import { useState, useCallback } from "react";
import { API_URL, API_KEY, ENDPOINTS } from "../utils/constants";

export interface Source {
  text: string;
  score: number;
  source?: string;
  document?: {
    id: string;
    title: string;
    filename: string;
    [key: string]: any;
  };
}

export interface Message {
  role: "user" | "assistant" | "system";
  content: string;
  sources?: Source[];
}

// ✅ Utility to clean up text formatting
const formatText = (text: string): string => {
  try {
    return text
      .replace(/\\n/g, "\n")
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'")
      .replace(/\n{3,}/g, "\n\n") // prevent excessive newlines
      .trim();
  } catch (err) {
    return text;
  }
};

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(
    async (selectedDocIds?: string[]) => {
      if (!inputValue.trim() || isLoading) return;

      try {
        setIsLoading(true);
        setError(null);

        const userMessage: Message = {
          role: "user",
          content: inputValue,
        };

        setMessages((prev) => [...prev, userMessage]);
        setInputValue("");

        const chatHistory = messages
          .filter((m) => m.role !== "system")
          .map((m) => ({
            role: m.role,
            content: m.content,
          }));

        const response = await fetch(`${API_URL}${ENDPOINTS.CHAT}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: API_KEY,
          },
          body: JSON.stringify({
            query: userMessage.content,
            history: chatHistory,
            documentIds: selectedDocIds?.length ? selectedDocIds : undefined,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to get response");
        }

        const data = await response.json();

        // 🔍 Handle structured or fallback response
        const cleanedAnswer =
          data.answer || data.response || formatText(JSON.stringify(data));

        const assistantMessage: Message = {
          role: "assistant",
          content: formatText(cleanedAnswer),
          sources: (data.sources || []).map((source: any) => ({
            ...source,
            text: formatText(source.text),
          })),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      } catch (err) {
        console.error("Error sending message:", err);
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
        setMessages((prev) => [
          ...prev,
          {
            role: "system",
            content: `Error: ${
              err instanceof Error ? err.message : "An unknown error occurred"
            }`,
          },
        ]);
      } finally {
        setIsLoading(false);
      }
    },
    [inputValue, messages, isLoading]
  );

  const resetChat = useCallback(() => {
    setMessages([]);
    setInputValue("");
    setError(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    inputValue,
    setInputValue,
    sendMessage,
    resetChat,
    setMessages,
  };
};
