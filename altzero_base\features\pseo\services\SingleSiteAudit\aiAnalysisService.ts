import { PSEO_CONSTANTS, API_ENDPOINTS } from '../../utilities/pseo/constants';
import type { 
  PSEOAnalysisRequest,
  PSEOAnalysisResponse,
  PSEOReportGenerationRequest,
  PSEOReportGenerationResponse,
  PSEOBatchAnalysisRequest,
  PSEOBatchAnalysisResponse,
  PSEOServiceHealthResponse,
  PSEOOpenAIStatusResponse,
  PSEOError,
  PSEOTechnicalAnalysis,
  PSEOContentAnalysis
} from '../../types';
import { htmlAnalysisService } from './htmlAnalysisService';
import { seoScoringService } from './seoScoringService';
import { databaseService } from '../pseo/databaseService';
import { supabase } from '../../../../base/utils/supabaseClient';

/**
 * AI Analysis Service Constants
 */
const AI_CONFIG = {
  MAX_TOKEN_LIMIT: 45000,     // Conservative token limit (Claude 3 Haiku: 50k, GPT-4o-mini: 64k)
  TEMPERATURE: 0.3,           // Lower temperature for more consistent analysis
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,          // 1 second
  REQUEST_TIMEOUT: 60000,     // 60 seconds
} as const;

class AIAnalysisService {
  private baseUrl: string;
  private apiKey: string | null = null;

  constructor() {
    // Default to localhost for development
    // In production, this should be configured via setBaseUrl() method
    this.baseUrl = 'http://localhost:3001';
    // Set default API key for development (matches server default)
    this.apiKey = 'altzero_base';
  }

  /**
   * Set the base URL for API calls
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  /**
   * Set API key for authentication
   */
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Get request headers with authentication
   */
  private getHeaders(userId?: string): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.apiKey) {
      headers['x-api-key'] = this.apiKey;
    }

    if (userId) {
      headers['x-user-id'] = userId;
    }

    return headers;
  }

  /**
   * Handle API response and errors
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    try {
      const data = await response.json();
      return data;
    } catch (error) {
      // Get response text for debugging
      const responseText = await response.text().catch(() => 'Unable to read response text');
      console.error('JSON parsing failed. Response text:', responseText.substring(0, 500));
      throw new Error(`Failed to parse technical SEO analysis response: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
    }
  }

  /**
   * Check service health
   */
  async checkHealth(): Promise<PSEOServiceHealthResponse> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.HEALTH}`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      return this.handleResponse<PSEOServiceHealthResponse>(response);
    } catch (error) {
      console.error('Health check failed:', error);
      throw new Error(`Service health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get OpenAI service status
   */
  async getOpenAIStatus(): Promise<PSEOOpenAIStatusResponse> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.OPENAI_STATUS}`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      return this.handleResponse<PSEOOpenAIStatusResponse>(response);
    } catch (error) {
      console.error('OpenAI status check failed:', error);
      throw new Error(`OpenAI status check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Test OpenAI connection
   */
  async testOpenAI(
    prompt: string, 
    options?: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      userId?: string;
    }
  ): Promise<{ success: boolean; response: string; metadata: any }> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.OPENAI_TEST}`, {
        method: 'POST',
        headers: this.getHeaders(options?.userId),
        body: JSON.stringify({
          prompt,
          model: options?.model,
          temperature: options?.temperature,
          maxTokens: options?.maxTokens,
        }),
      });

      return this.handleResponse(response);
    } catch (error) {
      console.error('OpenAI test failed:', error);
      throw new Error(`OpenAI test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Perform pSEO analysis on scraped content with enhanced HTML and SEO scoring
   */
  async performAnalysis(request: PSEOAnalysisRequest): Promise<PSEOAnalysisResponse> {
    try {
      console.log(`Starting enhanced pSEO analysis for URL: ${request.url}`);
      const startTime = Date.now();

      // Step 1: Run current OpenAI analysis (keep existing functionality)
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.ANALYZE}`, {
        method: 'POST',
        headers: this.getHeaders(request.userId),
        body: JSON.stringify({
          scrapedContent: request.scrapedContent,
          url: request.url,
        }),
      });

      const openaiResult = await this.handleResponse<{
        success: boolean;
        analysis: {
          technical: PSEOTechnicalAnalysis;
          content: PSEOContentAnalysis;
          processingTime: number;
          tokensUsed: number;
        };
        metadata: any;
      }>(response);

      console.log('✅ OpenAI analysis completed');

      // Step 2: Enhanced HTML analysis
      console.log('🔍 Starting HTML analysis...');
      const htmlAnalysis = await htmlAnalysisService.analyzeHTML(request.scrapedContent);
      console.log(`✅ HTML analysis completed - Found ${htmlAnalysis.totalIssues} issues (${htmlAnalysis.issuesByType.critical} critical, ${htmlAnalysis.issuesByType.warning} warnings, ${htmlAnalysis.issuesByType.info} info)`);

      // Step 3: Run SEO scoring analysis
      console.log('📊 Starting SEO scoring analysis...');
      const seoScoring = await seoScoringService.performSEOScoring(request.scrapedContent, request.url);
      console.log(`✅ SEO scoring completed - Overall score: ${seoScoring.overallScore}/100 (${seoScoring.grade}) | Technical: ${seoScoring.metrics.technicalScore} | Content: ${seoScoring.metrics.contentScore} | Performance: ${seoScoring.metrics.performanceScore}`);

      // Step 4: Store enhanced results in database if we have access to audit info
      // Note: We'll need the audit ID to store these results
      if (request.auditId) {
        try {
          // Store HTML analysis results
          await databaseService.updateAudit(request.auditId, {
            html_analysis: htmlAnalysis as any,
            html_issues: htmlAnalysis.issues as any,
            total_issues: htmlAnalysis.totalIssues,
            issues_by_type: htmlAnalysis.issuesByType as any,
            seo_score: htmlAnalysis.seoScore,
            performance_metrics: htmlAnalysis.performanceMetrics as any,
          } as any);

          // Store SEO scoring results
          await databaseService.updateAudit(request.auditId, {
            overall_score: seoScoring.overallScore,
            grade: seoScoring.grade,
            seo_metrics: seoScoring.metrics as any,
            recommendations: seoScoring.recommendations as any,
            provider_breakdown: seoScoring.providerBreakdown as any,
          } as any);

          console.log('✅ Enhanced analysis results stored in database');
        } catch (dbError) {
          console.error('Failed to store enhanced analysis results:', dbError);
          // Don't fail the entire analysis if storage fails
        }
      }

      const totalProcessingTime = Date.now() - startTime;

      // Step 5: Return combined results (maintain existing structure + enhancements)
      return {
        technical: {
          ...openaiResult.analysis.technical,
          // Add HTML analysis results to technical analysis
          htmlIssues: htmlAnalysis.issues as any,
          htmlScore: htmlAnalysis.seoScore,
          issuesByType: htmlAnalysis.issuesByType as any,
        },
        content: {
          ...openaiResult.analysis.content,
          // Add SEO scoring results to content analysis - convert to expected format
          seoMetrics: seoScoring.metrics as any,
          overallScore: seoScoring.overallScore as any,
          grade: seoScoring.grade as any,
          recommendations: seoScoring.recommendations.map(rec => ({
            category: rec.category,
            suggestion: rec.title,
            priority: rec.priority as 'high' | 'medium' | 'low',
            implementation: rec.action,
          })),
        },
        processingTime: totalProcessingTime,
        tokensUsed: openaiResult.analysis.tokensUsed,
        url: request.url,
        timestamp: new Date().toISOString(),
        // Add enhanced analysis data
        enhancedAnalysis: {
          htmlAnalysis,
          seoScoring,
          processingTime: totalProcessingTime,
        },
      };

    } catch (error) {
      console.error('Enhanced pSEO analysis failed:', error);
      throw new Error(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create summarized analysis data for report generation to avoid OpenAI token limits
   */
  private createSummarizedAnalysisData(analysis: PSEOAnalysisResponse): {
    technicalAnalysis: any;
    contentAnalysis: any;
  } {
    // Summarize technical analysis - keep core data but reduce details
    const technicalSummary = {
      // Combine critical issues from both original analysis and HTML analysis
      criticalIssues: [
        // Add original critical issues (if any)
        ...(analysis.technical.criticalIssues?.slice(0, 5) || []),
        // Add critical HTML issues
        ...(analysis.technical.htmlIssues ? 
          analysis.technical.htmlIssues
            .filter((issue: any) => issue.severity === 'critical')
            .slice(0, 5)
            .map((issue: any) => ({
              issue: issue.issue,
              impact: issue.impact,
              recommendation: issue.recommendation,
              priority: issue.priority || 'high' as 'high' | 'medium' | 'low'
            })) : [])
      ],
      
      quickWins: analysis.technical.quickWins?.slice(0, 5) || [],
      opportunities: analysis.technical.opportunities?.slice(0, 5) || [],
      
      // HTML summary stats
      htmlAnalysisSummary: {
        totalIssues: analysis.technical.htmlIssues?.length || 0,
        criticalCount: analysis.technical.issuesByType?.critical || 0,
        warningCount: analysis.technical.issuesByType?.warning || 0,
        seoScore: analysis.technical.htmlScore || 0
      }
    };

    // Summarize content analysis - keep core data but reduce details  
    const contentSummary = {
      // Core content metrics from original analysis (match PSEOContentAnalysis interface)
      analysis: analysis.content.analysis || {
        contentQuality: 'Not analyzed',
        keywordOptimization: 'Not analyzed', 
        structureAssessment: 'Not analyzed',
        userExperience: 'Not analyzed'
      },
      
      // Summarized SEO metrics
      seoSummary: {
        overallScore: analysis.content.overallScore || 0,
        grade: analysis.content.grade || 'F',
        technicalScore: analysis.content.seoMetrics?.technical?.score || 0,
        contentScore: analysis.content.seoMetrics?.content?.score || 0,
        performanceScore: analysis.content.seoMetrics?.performance?.score || 0
      },
      
      // Top 5 recommendations only
      recommendations: analysis.content.recommendations ? 
        analysis.content.recommendations
          .filter((rec: any) => rec.priority === 'high')
          .slice(0, 5)
          .map((rec: any) => ({
            category: rec.category,
            suggestion: rec.suggestion,
            priority: rec.priority as 'high' | 'medium' | 'low',
            implementation: rec.implementation
          })) : []
    };

    return {
      technicalAnalysis: technicalSummary,
      contentAnalysis: contentSummary
    };
  }

  /**
   * Generate markdown report from analysis results
   */
  async generateReport(request: PSEOReportGenerationRequest): Promise<PSEOReportGenerationResponse> {
    try {
      console.log(`Generating report for URL: ${request.url}`);

      // Create summarized analysis data to reduce payload size
      const summarizedData = this.createSummarizedAnalysisData({
        technical: request.technicalAnalysis,
        content: request.contentAnalysis,
        processingTime: 0,
        tokensUsed: 0,
        url: request.url,
        timestamp: new Date().toISOString()
      });

      // Log token reduction
      const originalSize = JSON.stringify({
        technicalAnalysis: request.technicalAnalysis,
        contentAnalysis: request.contentAnalysis
      }).length;
      const summarizedSize = JSON.stringify(summarizedData).length;
      const reduction = Math.round(((originalSize - summarizedSize) / originalSize) * 100);
      
      console.log(`📊 Report data optimized: ${originalSize} → ${summarizedSize} chars (${reduction}% reduction)`);

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.REPORT_GENERATE}`, {
        method: 'POST',
        headers: this.getHeaders(request.userId),
        body: JSON.stringify({
          technicalAnalysis: summarizedData.technicalAnalysis,
          contentAnalysis: summarizedData.contentAnalysis,
          url: request.url,
          domain: request.domain,
        }),
      });

      const result = await this.handleResponse<{
        success: boolean;
        report: string;
        metadata: any;
      }>(response);

      return {
        markdownReport: result.report,
        url: request.url,
        domain: request.domain,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Report generation failed:', error);
      throw new Error(`Report generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert markdown to HTML
   */
  async convertMarkdownToHTML(markdown: string): Promise<{ html: string; timestamp: string }> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.REPORT_CONVERT}`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify({ markdown }),
      });

      const result = await this.handleResponse<{
        success: boolean;
        html: string;
        metadata: { timestamp: string };
      }>(response);

      return {
        html: result.html,
        timestamp: result.metadata.timestamp,
      };
    } catch (error) {
      console.error('Markdown conversion failed:', error);
      throw new Error(`Markdown conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Perform batch analysis on multiple URLs
   */
  async performBatchAnalysis(request: PSEOBatchAnalysisRequest): Promise<PSEOBatchAnalysisResponse> {
    try {
      if (request.urls.length !== request.scrapedContents.length) {
        throw new Error('URLs and scraped contents arrays must have the same length');
      }

      if (request.urls.length > 10) {
        throw new Error('Maximum 10 URLs can be analyzed in a single batch');
      }

      console.log(`Starting batch analysis for ${request.urls.length} URLs`);

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.ANALYZE_BATCH}`, {
        method: 'POST',
        headers: this.getHeaders(request.userId),
        body: JSON.stringify({
          urls: request.urls,
          scrapedContents: request.scrapedContents,
        }),
      });

      return this.handleResponse<PSEOBatchAnalysisResponse>(response);
    } catch (error) {
      console.error('Batch analysis failed:', error);
      throw new Error(`Batch analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get analysis statistics
   */
  async getStats(userId?: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.STATS}`, {
        method: 'GET',
        headers: this.getHeaders(userId),
      });

      return this.handleResponse(response);
    } catch (error) {
      console.error('Stats retrieval failed:', error);
      throw new Error(`Stats retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate service configuration
   */
  async validateConfiguration(): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Check service health
      const health = await this.checkHealth();
      if (health.status !== 'ok') {
        issues.push('Service health check failed');
      }

      // Check OpenAI status
      const openaiStatus = await this.getOpenAIStatus();
      if (!openaiStatus.enabled) {
        issues.push('OpenAI service is not enabled');
        recommendations.push('Configure OPENAI_API_KEY environment variable');
      }

      // Check API key
      if (!this.apiKey) {
        issues.push('API key not configured');
        recommendations.push('Set API key using setApiKey() method');
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      issues.push(`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        isValid: false,
        issues,
        recommendations,
      };
    }
  }
}

export const aiAnalysisService = new AIAnalysisService();