import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Block, BlockType, Section, ScopingDocument } from '../../../../../types/scoping';
import SectionSidePanel from '../shared/SectionSidePanel';
import SectionEditor from '../shared/SectionEditor';
import { useProgressiveScoping } from '../../../hooks/useProgressiveScoping';
import { Al<PERSON>, Button } from 'react-bootstrap';

// Keep the prop interface for backward compatibility, but make all props optional
interface ProgressiveScopingResultProps {
  document?: ScopingDocument | null;
  onGenerateSection?: (sectionId: string) => Promise<void>;
  onGenerateAll?: () => Promise<void>;
  onUpdateDocument?: (documentId: string, updates: Partial<ScopingDocument>) => Promise<void>;
  onUpdateSection?: (documentId: string, sectionId: string, updates: Partial<Section>) => Promise<void>;
  onReorderSections?: (documentId: string, sections: Section[]) => Promise<void>;
  onReset?: () => void;
  onExport?: (documentId: string) => void;
}

// Helper function to convert content to blocks
function convertContentToBlocks(content: string): Block[] {
  // Split content by double line breaks to separate paragraphs
  const paragraphs = content.split(/\n\n+/);
  return paragraphs.map((para, index) => {
    // Detect block type based on content
    if (para.startsWith('# ')) {
      return {
        id: `block-${index}`,
        type: 'header' as BlockType,
        content: para.substring(2),
        level: 1
      };
    } else if (para.startsWith('## ')) {
      return {
        id: `block-${index}`,
        type: 'header' as BlockType,
        content: para.substring(3),
        level: 2
      };
    } else if (para.startsWith('### ')) {
      return {
        id: `block-${index}`,
        type: 'header' as BlockType,
        content: para.substring(4),
        level: 3
      };
    } else if (para.startsWith('> ')) {
      return {
        id: `block-${index}`,
        type: 'quote' as BlockType,
        content: para.substring(2)
      };
    } else if (para.startsWith('```')) {
      const codeMatch = para.match(/```(\w*)\n([\s\S]*?)```/);
      if (codeMatch) {
        return {
          id: `block-${index}`,
          type: 'code' as BlockType,
          content: codeMatch[2],
          language: codeMatch[1] || 'plaintext'
        };
      }
    } else if (para.includes('| --- |') || para.match(/\|.*\|.*\|/)) {
      // Simple table detection
      const rows = para.split('\n').map(row => 
        row.split('|').map(cell => cell.trim()).filter(cell => cell)
      );
      return {
        id: `block-${index}`,
        type: 'table' as BlockType,
        content: para,
        rows: rows
      };
    } else if (para.startsWith('- ') || para.match(/^\d+\. /)) {
      // List detection
      const items = para.split('\n').map(item => 
        item.replace(/^- /, '').replace(/^\d+\. /, '')
      );
      return {
        id: `block-${index}`,
        type: 'list' as BlockType,
        content: para,
        items: items
      };
    }
    
    // Default to text block
    return {
      id: `block-${index}`,
      type: 'text' as BlockType,
      content: para
    };
  });
}

// Helper function to convert blocks back to content
function convertBlocksToContent(blocks: Block[]): string {
  return blocks.map(block => {
    switch (block.type) {
      case 'header':
        const prefix = '#'.repeat(block.level || 1);
        return `${prefix} ${block.content}`;
      case 'quote':
        return `> ${block.content}`;
      case 'code':
        return `\`\`\`${block.language || ''}\n${block.content}\n\`\`\``;
      case 'list':
        return (block.items || []).map(item => `- ${item}`).join('\n');
      case 'table':
        if (block.rows) {
          return block.rows.map(row => `| ${row.join(' | ')} |`).join('\n');
        }
        return block.content;
      default:
        return block.content;
    }
  }).join('\n\n');
}

interface EditableContent {
  isEditing: boolean;
  content: string;
  blocks: Block[];
}

// Add this near the top of the file
interface ContentDebugInfo {
  rawContent: string;
  processedContent: string;
  eventType: string;
  timestamp: string;
}

// Enhance the DocumentHeader component for better template info display
const DocumentHeader: React.FC<{ document: any }> = ({ document }) => {
  if (!document) return null;
  
  const templateInfo = document.templateInfo || {};
  const sectionTemplate = templateInfo.sectionTemplate || {};
  const sectionCount = document.sections?.length || 0;
  
  // Debug function to test API connection
  const testLLMConnection = async () => {
    try {
      const sectionId = document.sections[0]?.id;
      if (!sectionId) {
        console.error("No section found to test");
        return;
      }
      
      console.log("Testing LLM connection with section:", sectionId);
      
      // Create direct API payload with all required fields
      const payload = {
        clientInfo: document.clientInfo || {
          name: document.clientName || 'Test Client',
          industry: 'Technology',
          company: document.clientName || 'Test Company',
          contactPerson: 'Project Manager',
          email: '<EMAIL>'
        },
        scopingInfo: document.scopingInfo || {
          projectName: document.projectName || 'Test Project',
          projectDescription: document.baseContent?.description || 'Test description',
          timeline: '2-3 months',
          budget: '$10,000 - $20,000',
          goals: ['Complete project successfully', 'Meet all requirements']
        },
        promptTemplate: document.templateInfo?.promptTemplate || {
          id: 'default-prompt',
          name: 'Default Test Prompt',
          content: 'You are an expert AI consultant tasked with creating professional scoping documents.'
        },
        sections: [{
          id: sectionId,
          title: document.sections[0].title,
          description: document.sections[0].description || `Generate content for ${document.sections[0].title} section`
        }]
      };
      
      console.log("Sending test payload:", JSON.stringify(payload, null, 2));
      
      // Make direct fetch call to backend
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
      const API_KEY = import.meta.env.VITE_API_KEY || '';
      
      const response = await fetch(`${API_URL}/scoping/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`API call failed with status: ${response.status}`);
      }
      
      console.log("API connection successful, processing stream...");
      
      // Handle server-sent events
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Stream reader not available');
      }
      
      const decoder = new TextDecoder();
      let buffer = '';
      
      // Process stream
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log("Stream complete");
          break;
        }
        
        buffer += decoder.decode(value, { stream: true });
        
        // Process events in buffer
        const lines = buffer.split('\n\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (!line.trim()) continue;
          
          const eventMatch = line.match(/^event: (.+)$/m);
          const dataMatch = line.match(/^data: (.+)$/m);
          
          if (eventMatch && dataMatch) {
            const eventType = eventMatch[1];
            let eventData;
            try {
              eventData = JSON.parse(dataMatch[1]);
              console.log(`Event: ${eventType}`, eventData);
            } catch (e) {
              console.error("Failed to parse event data:", dataMatch[1]);
            }
          }
        }
      }
      
      console.log("Test complete");
      
    } catch (error) {
      console.error("Error testing LLM connection:", error);
    }
  };
  
  return (
    <div className="bg-white shadow-sm border-b border-gray-200 mb-4">
      <div className="px-4 py-4 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-1">{document.projectName}</h1>
        <p className="text-gray-600">
          Client: <span className="font-medium">{document.clientName}</span>
        </p>
        
        <div className="mt-2 flex items-center text-sm text-gray-700">
          <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <span>{sectionCount} {sectionCount === 1 ? 'section' : 'sections'} available</span>
        </div>
        
        {sectionTemplate.name && (
          <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-100">
            <div className="flex items-center text-blue-700">
              <svg className="h-5 w-5 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="font-medium">Section template:</span>
              <span className="font-bold ml-1.5">{sectionTemplate.name}</span>
            </div>
            {sectionTemplate.description && (
              <p className="mt-1 text-sm text-blue-600 ml-6.5">
                {sectionTemplate.description}
              </p>
            )}
            <p className="mt-2 text-xs text-blue-500 ml-6.5">
              This document uses sections from the template. You can generate content for each section individually or all at once.
            </p>
          </div>
        )}
        
        {/* Debug button - only visible in development environment */}
        {import.meta.env.DEV && (
          <div className="mt-3">
            <button
              onClick={testLLMConnection}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded border border-blue-700 hover:bg-blue-700"
            >
              Test LLM Connection
            </button>
            <p className="mt-1 text-xs text-gray-500">
              This button will test the connection to the LLM API by sending a request to generate content for the first section.
              Check the browser console (F12) to see the API communication logs.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// Add this after the DocumentHeader component
const ApiDebugInfo: React.FC = () => {
  useEffect(() => {
    // Log API configuration on mount
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const API_KEY = import.meta.env.VITE_API_KEY || '';
    
    console.log('Current API Configuration:', {
      API_URL,
      hasApiKey: !!API_KEY,
      isDev: import.meta.env.DEV
    });
  }, []);
  
  if (!import.meta.env.DEV) return null;
  
  return (
    <div className="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200 text-xs">
      <div className="font-medium mb-1">API Configuration:</div>
      <div>API URL: {import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}</div>
      <div>API Key Set: {import.meta.env.VITE_API_KEY ? 'Yes' : 'No'}</div>
    </div>
  );
};

const ProgressiveScopingResult: React.FC<ProgressiveScopingResultProps> = (props) => {
  const navigate = useNavigate();
  // Use either props.document or load via documentId param
  const { documentId } = useParams<{ documentId: string }>();
  const [activeLoading, setActiveLoading] = useState(false);
  const [fullPageView, setFullPageView] = useState(true); // Default to full page view
  
  // If document is provided via props, use that instead of loading from documentId
  const usePropsDocument = !!props.document;
  
  const {
    document: hookDocument,
    sections: hookSections,
    activeSectionId: hookActiveSectionId,
    setActiveSectionId: hookSetActiveSectionId,
    generateSection: hookGenerateSection,
    generateAllSections: hookGenerateAllSections,
    updateSectionContent: hookUpdateSectionContent,
    reorderSections: hookReorderSections,
    exportDocument: hookExportDocument,
    loading: hookLoading,
    error: hookError
  } = useProgressiveScoping(usePropsDocument ? undefined : documentId);

  // Use either props values or hook values
  const document = usePropsDocument ? props.document : hookDocument;
  const sections = usePropsDocument && document 
    ? (document as ScopingDocument).sections as Section[] 
    : hookSections as Section[];
  const loading = usePropsDocument ? false : hookLoading;
  const error = usePropsDocument ? null : hookError;
  
  // For state management
  const [localActiveSectionId, setLocalActiveSectionId] = useState<string | null>(null);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const activeSectionId = usePropsDocument ? localActiveSectionId : hookActiveSectionId;
  
  const setActiveSectionId = (sectionId: string | null) => {
    if (usePropsDocument) {
      setLocalActiveSectionId(sectionId);
    } else {
      hookSetActiveSectionId(sectionId);
    }
  };

  // Handle loading and init states
  useEffect(() => {
    // Log successful document loading
    if (document && !loading) {
      console.log('Document loaded successfully:', document);
    }
  }, [document, loading]);

  // Set first section as active when sections are loaded
  useEffect(() => {
    if (sections && sections.length > 0 && !activeSectionId) {
      const firstCompletedSection = (sections as Section[]).find(s => s.status === 'completed');
      const firstSection = sections[0];
      
      setActiveSectionId(firstCompletedSection?.id || firstSection.id);
    }
  }, [sections, activeSectionId]);

  // Add this to the component's state
  const [debugMode, setDebugMode] = useState(false);
  const [contentDebugInfo, setContentDebugInfo] = useState<Record<string, ContentDebugInfo>>({});

  // Update the section content display logic
  const renderSectionContent = (section: Section) => {
    // Add debug information
    const debugInfo = {
      id: section.id,
      title: section.title,
      status: section.status,
      contentLength: section.content?.length || 0,
      content: section.content?.substring(0, 100) + (section.content?.length > 100 ? '...' : ''),
      error: section.error
    };

    console.log('Section data:', debugInfo);

    if (section.status === 'failed') {
      return (
        <div>
          <Alert variant="danger">
            <Alert.Heading>Generation Failed</Alert.Heading>
            <p>There was an error generating content for this section. Please try again.</p>
            {section.error && (
              <div>
                <hr />
                <p><strong>Error Details:</strong></p>
                <pre style={{ whiteSpace: 'pre-wrap' }}>
                  {JSON.stringify(section.error, null, 2)}
                </pre>
              </div>
            )}
          </Alert>
          <Button 
            variant="outline-danger" 
            onClick={() => handleGenerateSection(section.id)}
            className="mt-3"
          >
            Retry Generation
          </Button>
        </div>
      );
    }

    if (section.status === 'generating') {
      return (
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      );
    }

    if (section.status === 'completed') {
      const debugInfo = contentDebugInfo[section.id];
      
      if (debugMode && debugInfo) {
        return (
          <div className="space-y-4">
            <div className="bg-gray-100 p-4 rounded-md">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Debug Information</h4>
              <div className="space-y-2 text-xs">
                <p><span className="font-medium">Event Type:</span> {debugInfo.eventType}</p>
                <p><span className="font-medium">Timestamp:</span> {debugInfo.timestamp}</p>
                <div className="mt-2">
                  <p className="font-medium mb-1">Raw Content:</p>
                  <pre className="bg-gray-50 p-2 rounded overflow-auto max-h-40">
                    {debugInfo.rawContent}
                  </pre>
                </div>
                <div className="mt-2">
                  <p className="font-medium mb-1">Processed Content:</p>
                  <pre className="bg-gray-50 p-2 rounded overflow-auto max-h-40">
                    {debugInfo.processedContent}
                  </pre>
                </div>
              </div>
            </div>
            <div className="prose prose-indigo max-w-none">
              <div dangerouslySetInnerHTML={{ 
                __html: debugInfo.processedContent
                  .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
                  .replace(/^## (.*$)/gm, '<h2 class="text-xl font-bold mb-3">$1</h2>')
                  .replace(/^### (.*$)/gm, '<h3 class="text-lg font-bold mb-2">$1</h3>')
                  .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                  .replace(/\*(.*?)\*/g, '<em>$1</em>')
                  .replace(/\n\n/g, '</p><p class="mb-4">')
              }} />
            </div>
          </div>
        );
      }

      return (
        <div className="prose prose-indigo max-w-none">
          <div dangerouslySetInnerHTML={{ 
            __html: section.content
              .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
              .replace(/^## (.*$)/gm, '<h2 class="text-xl font-bold mb-3">$1</h2>')
              .replace(/^### (.*$)/gm, '<h3 class="text-lg font-bold mb-2">$1</h3>')
              .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              .replace(/\*(.*?)\*/g, '<em>$1</em>')
              .replace(/\n\n/g, '</p><p class="mb-4">')
          }} />
        </div>
      );
    }

    return (
      <p className="text-gray-500 italic">Content will appear here after generation</p>
    );
  };

  // Update the handleGenerateSection function with the working implementation
  const handleGenerateSection = async (sectionId: string) => {
    if (!document) return;
    
    setActiveLoading(true);
    try {
      if (usePropsDocument && props.onGenerateSection) {
        await props.onGenerateSection(sectionId);
      } else {
        await hookGenerateSection(sectionId);
      }
      setActiveSectionId(sectionId);
    } finally {
      setActiveLoading(false);
    }
  };

  // Handle all sections generation with loading state
  const handleGenerateAll = async () => {
    if (!document) return;
    
    setActiveLoading(true);
    try {
      if (usePropsDocument && props.onGenerateAll) {
        await props.onGenerateAll();
      } else {
        await hookGenerateAllSections();
      }
    } finally {
      setActiveLoading(false);
    }
  };
  
  // Handle updating section content
  const handleUpdateSectionContent = async (sectionId: string, content: string) => {
    if (!document) return;
    
    if (usePropsDocument && props.onUpdateSection) {
      await props.onUpdateSection((document as ScopingDocument).id, sectionId, { content });
    } else {
      await hookUpdateSectionContent(sectionId, content);
    }
    
    // Exit editing mode after saving
    setEditingSectionId(null);
  };
  
  // Handle reordering sections
  const handleReorderSections = async (reorderedSections: Section[]) => {
    if (!document) return;
    
    if (usePropsDocument && props.onReorderSections) {
      await props.onReorderSections((document as ScopingDocument).id, reorderedSections);
    } else {
      await hookReorderSections(reorderedSections);
    }
  };
  
  // Export document to PDF
  const handleExportToPDF = async () => {
    if (!document) return;
    
    // Show loading state
    const exportButton = window.document.querySelector('#exportButton') as HTMLButtonElement;
    if (exportButton) {
      exportButton.disabled = true;
      exportButton.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Exporting...';
    }
    
    try {
      if (usePropsDocument && props.onExport) {
        props.onExport((document as ScopingDocument).id);
      } else {
        try {
          console.log('Starting export...');
          const downloadUrl = await hookExportDocument();
          
          // Create a temporary link element to download the file
          const downloadLink = window.document.createElement('a');
          downloadLink.href = downloadUrl;
          downloadLink.download = `${(document as ScopingDocument).projectName || 'Scope'}-Document.html`; 
          window.document.body.appendChild(downloadLink);
          downloadLink.click();
          window.document.body.removeChild(downloadLink);
          
          console.log('Export successful, download started');
        } catch (error) {
          console.error('Error exporting document:', error);
          alert('Failed to export the document. Please try again later.');
        }
      }
    } finally {
      // Reset button state
      if (exportButton) {
        exportButton.disabled = false;
        exportButton.innerHTML = '<svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg> Export as PDF';
      }
    }
  };
  
  // Reset document
  const handleReset = () => {
    if (usePropsDocument && props.onReset) {
      props.onReset();
    } else {
      navigate('/scoping/progressive');
    }
  };

  // Render loading state
  if (loading) {
        return (
      <div className="h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
          <p className="mt-4 text-xl font-medium text-gray-700">Loading document...</p>
            </div>
          </div>
        );
  }
        
  // Render error state
  if (error) {
        return (
      <div className="h-full flex items-center justify-center bg-white">
        <div className="text-center max-w-md mx-auto p-6 bg-red-50 rounded-lg border border-red-200">
          <svg className="h-12 w-12 text-red-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h3 className="text-lg font-medium text-red-800">Error Loading Document</h3>
          <p className="mt-2 text-red-700">{error}</p>
              <button
            onClick={handleReset}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Go Back
                </button>
              </div>
          </div>
        );
  }
  
  // If no document is loaded
  if (!document) {
    return (
      <div className="h-full flex items-center justify-center bg-white">
        <div className="text-center max-w-md mx-auto p-6 bg-yellow-50 rounded-lg border border-yellow-200">
          <svg className="h-12 w-12 text-yellow-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h3 className="text-lg font-medium text-yellow-800">No Document Found</h3>
          <p className="mt-2 text-yellow-700">Could not find the requested document.</p>
        <button
            onClick={handleReset}
            className="mt-4 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
        >
            Go Back
        </button>
        </div>
      </div>
    );
  }
  
  // Sort sections by order
  const sortedSections = [...(sections as Section[])].sort((a, b) => a.order - b.order);
  
  // Find active section
  const activeSection = (sections as Section[]).find(section => section.id === activeSectionId);
  
  // Render the document in full page view
  return (
    <div className="h-full flex flex-col overflow-hidden bg-white">
      <DocumentHeader document={document} />
      {import.meta.env.DEV && <div className="px-4 py-2"><ApiDebugInfo /></div>}
      
      {/* Top toolbar with actions */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-5xl mx-auto flex justify-between items-center">
          <div className="flex gap-2">
            <button
              onClick={() => setFullPageView(!fullPageView)}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              {fullPageView ? (
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                  Section View
                </span>
              ) : (
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                  </svg>
                  Full Page View
                </span>
              )}
            </button>
            
            <button
              onClick={handleGenerateAll}
              disabled={activeLoading || !(sections as Section[]).some(s => s.status === 'pending')}
              className={`px-3 py-1.5 text-sm rounded-md text-white ${
                activeLoading || !(sections as Section[]).some(s => s.status === 'pending')
                  ? 'bg-indigo-300 cursor-not-allowed'
                  : 'bg-indigo-600 hover:bg-indigo-700'
              }`}
            >
              <span className="flex items-center">
                {activeLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Generate All Sections
                  </>
                )}
              </span>
            </button>
            
            {/* Add debug mode toggle */}
            {import.meta.env.DEV && (
              <button
                onClick={() => setDebugMode(!debugMode)}
                className={`px-3 py-1.5 text-sm border rounded-md ${
                  debugMode 
                    ? 'border-indigo-500 text-indigo-700 bg-indigo-50'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                  Debug Mode
                </span>
              </button>
            )}
          </div>
          
          <div className="flex gap-2">
          <button
              onClick={handleReset}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
              Back
          </button>
            
            <div className="flex flex-row">
              {/* Export button */}
              <Button 
                className="mr-2 flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-semibold rounded"
                onClick={handleExportToPDF}
                id="exportButton"
              >
                <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export as PDF
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Side panel with sections list (only visible in section view) */}
        {!fullPageView && (
        <SectionSidePanel 
            sections={sections}
            activeSectionId={activeSectionId || undefined}
            setActiveSection={setActiveSectionId}
          onGenerateSection={handleGenerateSection}
          onGenerateAll={handleGenerateAll}
            onSectionReorder={handleReorderSections}
          />
        )}
        
        {/* Main content area */}
        <div className="flex-1 overflow-auto">
          {fullPageView ? (
            // Full page view showing all sections
            <div className="max-w-4xl mx-auto py-6">
              {sortedSections.map((section) => (
                <div 
                  key={section.id} 
                  className="mb-10 p-6 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
                    <div className="flex items-center gap-2">
                      {section.status === 'pending' && (
                      <button 
                          onClick={() => handleGenerateSection(section.id)}
                          disabled={activeLoading}
                          className={`px-3 py-1 text-sm rounded-md text-white ${
                            activeLoading ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'
                          }`}
                        >
                          <span className="flex items-center">
                            {activeLoading ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Generating...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                Generate
                              </>
                            )}
                          </span>
                      </button>
                      )}
                      
                      {section.status === 'completed' && (
                      <button 
                          onClick={() => setEditingSectionId(editingSectionId === section.id ? null : section.id)}
                          className="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                          {editingSectionId === section.id ? 'Cancel' : 'Edit'}
                      </button>
                      )}
                    </div>
                  </div>
                  
                  {/* Section status indicator */}
                  {section.status !== 'completed' && (
                    <div className={`mb-4 p-3 rounded-md ${
                      section.status === 'generating' 
                        ? 'bg-blue-50 border border-blue-100' 
                        : 'bg-gray-50 border border-gray-200'
                    }`}>
                      {section.status === 'generating' ? (
                        <div className="flex items-center">
                          <svg className="animate-spin h-5 w-5 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span className="text-blue-700 font-medium">Generating content...</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <svg className="h-5 w-5 mr-2 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-gray-700">Pending generation</span>
                      </div>
                      )}
                    </div>
                  )}
                  
                  {/* Section content */}
                  {section.status === 'completed' && editingSectionId === section.id ? (
                    <div className="mt-4">
                  <textarea
                        value={section.content}
                        onChange={(e) => {
                          const updatedSections = (sections as Section[]).map(s => 
                            s.id === section.id ? { ...s, content: e.target.value } : s
                          );
                          if (usePropsDocument && props.onUpdateSection) {
                            props.onUpdateSection((document as ScopingDocument).id, section.id, { content: e.target.value });
                          }
                        }}
                        className="w-full h-80 p-3 border border-gray-300 rounded-md font-mono text-sm focus:ring-indigo-500 focus:border-indigo-500"
                      />
                      <div className="mt-2 flex justify-end">
                    <button
                          onClick={() => handleUpdateSectionContent(section.id, section.content)}
                          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                          Save Changes
                    </button>
                  </div>
                    </div>
                  ) : (
                    renderSectionContent(section)
                  )}
                          </div>
                        ))}
                      </div>
                    ) : (
            // Single section view
            activeSection ? (
              <SectionEditor
                section={activeSection}
                onUpdateContent={handleUpdateSectionContent}
                isLoading={activeLoading}
                onGenerateSection={handleGenerateSection}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-6">
                  <svg className="h-12 w-12 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900">No Section Selected</h3>
                  <p className="text-gray-500 mt-2">Please select a section from the sidebar to view or edit its content.</p>
                      </div>
              </div>
            )
            )}
        </div>
      </div>
    </div>
  );
};

export default ProgressiveScopingResult; 