import React, { useState, useEffect } from 'react';
import { UserPlus, Search, Plus, Edit, Trash2 } from 'lucide-react';
import CRMLayout from '../components/CRMLayout';

interface ContactGroup {
  id: string;
  organisation_id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

const ContactGroupManagement: React.FC = () => {
  const [contactGroups, setContactGroups] = useState<ContactGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadContactGroups();
  }, []);

  const loadContactGroups = async () => {
    try {
      setLoading(true);
      // TODO: Implement API call to fetch contact groups
      // const response = await crmService.getContactGroups();
      // setContactGroups(response.data);
      
      // Mock data for now
      setContactGroups([
        {
          id: '1',
          organisation_id: 'org1',
          name: 'VIP Customers',
          description: 'High-value customers requiring special attention',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          organisation_id: 'org1',
          name: 'Prospects',
          description: 'Potential customers in the sales pipeline',
          created_at: '2024-01-16T10:00:00Z',
          updated_at: '2024-01-16T10:00:00Z'
        }
      ]);
    } catch (error) {
      console.error('Error loading contact groups:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredGroups = contactGroups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <UserPlus className="w-8 h-8 text-blue-600 mr-3" />
              Contact Groups
            </h1>
            <p className="text-gray-600 mt-1">Organize and categorize your contacts</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            New Group
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search contact groups..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Contact Groups Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Group Name</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Description</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={4} className="text-center py-8 text-gray-500">
                      Loading contact groups...
                    </td>
                  </tr>
                ) : filteredGroups.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="text-center py-8 text-gray-500">
                      {searchTerm ? 'No contact groups found matching your search.' : 'No contact groups found. Create your first group to get started.'}
                    </td>
                  </tr>
                ) : (
                  filteredGroups.map((group) => (
                    <tr key={group.id} className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{group.name}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600">{group.description || '-'}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600">
                          {new Date(group.created_at).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-red-600 transition-colors">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-800">
              <strong>{filteredGroups.length}</strong> contact groups found
            </div>
            <div className="text-xs text-blue-600">
              💡 Tip: Use contact groups to organize customers by type, region, or importance level
            </div>
          </div>
        </div>
      </div>
    </CRMLayout>
  );
};

export default ContactGroupManagement;
