// =====================================================
// LANGGRAPH ROUTES FOR SCOPINGAI
// =====================================================

import { Router, Request, Response } from 'express';
import { ProposalWorkflow } from '../workflows/ProposalWorkflow';
import { ProposalGenerationInput, WorkflowConfig } from '../types/WorkflowState';

const router = Router();

// Middleware to validate API key
const validateApiKey = (req: Request, res: Response, next: any) => {
  const apiKey = req.headers['x-api-key'];
  if (apiKey !== 'scopingai') {
    return res.status(401).json({ error: 'Invalid API key' });
  }
  next();
};

// =====================================================
// WORKFLOW EXECUTION ENDPOINTS
// =====================================================

// Execute proposal workflow
router.post('/workflow/execute', validateApiKey, async (req: Request, res: Response) => {
  try {
    console.log('🚀 Starting LangGraph proposal workflow execution');

    const input: ProposalGenerationInput = {
      user_id: req.body.user_id || req.headers['x-user-id'] as string,
      client: req.body.client,
      project: req.body.project,
      template: req.body.template,
      requirements: req.body.requirements || {},
      selected_knowledge_documents: req.body.selected_knowledge_documents || [],
      document_requirements: req.body.document_requirements || {},
      ai_prompts: req.body.ai_prompts,
      config: req.body.config
    };

    // Validate required fields
    if (!input.user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    if (!input.client || !input.client.name) {
      return res.status(400).json({ error: 'Client information is required' });
    }

    if (!input.project || !input.project.title) {
      return res.status(400).json({ error: 'Project information is required' });
    }

    console.log('📋 Workflow input validated:', {
      user_id: input.user_id,
      client_name: input.client.name,
      project_title: input.project.title,
      knowledge_docs: input.selected_knowledge_documents.length
    });

    // Create and execute workflow
    const workflow = await ProposalWorkflow.create(input.config);
    const result = await workflow.executeProposal(input);

    console.log('✅ Workflow execution completed:', {
      workflow_id: result.workflow_id,
      status: result.status,
      processing_time: result.processing_time,
      sections_generated: result.proposal_sections?.length || 0
    });

    res.json({
      success: true,
      workflow_id: result.workflow_id,
      status: result.status,
      proposal: result.final_proposal,
      metadata: {
        processing_time_ms: result.processing_time,
        total_cost: result.total_cost,
        quality_score: result.final_proposal?.quality_metrics?.overall_score,
        sections_count: result.proposal_sections?.length || 0,
        word_count: result.final_proposal?.metadata?.total_word_count || 0
      },
      errors: result.errors,
      warnings: result.warnings
    });

  } catch (error: any) {
    console.error('❌ Workflow execution failed:', error);
    res.status(500).json({
      success: false,
      error: 'Workflow execution failed',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Execute workflow with streaming (real-time updates)
router.post('/workflow/stream', validateApiKey, async (req: Request, res: Response) => {
  try {
    console.log('🌊 Starting streaming LangGraph workflow execution');

    // Set up Server-Sent Events
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    const input: ProposalGenerationInput = {
      user_id: req.body.user_id || req.headers['x-user-id'] as string,
      client: req.body.client,
      project: req.body.project,
      template: req.body.template,
      requirements: req.body.requirements || {},
      selected_knowledge_documents: req.body.selected_knowledge_documents || [],
      document_requirements: req.body.document_requirements || {},
      ai_prompts: req.body.ai_prompts,
      config: req.body.config
    };

    // Send initial event
    res.write(`event: started\n`);
    res.write(`data: ${JSON.stringify({
      message: 'LangGraph workflow started',
      workflow_type: 'advanced_proposal_generation',
      client_name: input.client?.name
    })}\n\n`);

    // Create workflow with streaming callbacks
    const workflow = await ProposalWorkflow.create(input.config);
    
    // Execute workflow (in a real implementation, this would have streaming callbacks)
    const result = await workflow.executeProposal(input);

    // Send progress events (simplified - in real implementation, these would come from workflow nodes)
    const progressEvents = [
      { step: 'validation', progress: 10, message: 'Input validation completed' },
      { step: 'knowledge_retrieval', progress: 20, message: 'Knowledge base content retrieved' },
      { step: 'client_analysis', progress: 35, message: 'Client and market analysis completed' },
      { step: 'research_analysis', progress: 50, message: 'Research analysis completed' },
      { step: 'executive_summary', progress: 65, message: 'Executive summary generated' },
      { step: 'section_generation', progress: 85, message: 'Proposal sections generated' },
      { step: 'quality_review', progress: 95, message: 'Quality review completed' },
      { step: 'finalization', progress: 100, message: 'Proposal finalized' }
    ];

    for (const event of progressEvents) {
      res.write(`event: progress\n`);
      res.write(`data: ${JSON.stringify(event)}\n\n`);
      
      // Small delay to simulate real-time processing
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Send completion event
    res.write(`event: completed\n`);
    res.write(`data: ${JSON.stringify({
      workflow_id: result.workflow_id,
      status: result.status,
      proposal: result.final_proposal,
      metadata: {
        processing_time_ms: result.processing_time,
        total_cost: result.total_cost,
        quality_score: result.final_proposal?.quality_metrics?.overall_score,
        sections_count: result.proposal_sections?.length || 0,
        word_count: result.final_proposal?.metadata?.total_word_count || 0
      }
    })}\n\n`);

    res.end();

  } catch (error: any) {
    console.error('❌ Streaming workflow execution failed:', error);
    
    res.write(`event: error\n`);
    res.write(`data: ${JSON.stringify({
      error: 'Workflow execution failed',
      details: error.message
    })}\n\n`);
    
    res.end();
  }
});

// =====================================================
// WORKFLOW MANAGEMENT ENDPOINTS
// =====================================================

// Get workflow status
router.get('/workflow/:workflowId/status', validateApiKey, async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;
    
    // In a real implementation, this would query the StateManager
    console.log(`📊 Getting workflow status: ${workflowId}`);
    
    res.json({
      workflow_id: workflowId,
      status: 'completed', // Mock status
      progress: 100,
      current_step: 'finalization_completed',
      started_at: new Date().toISOString(),
      completed_at: new Date().toISOString(),
      processing_time: 45000,
      errors: [],
      warnings: []
    });

  } catch (error: any) {
    console.error('❌ Failed to get workflow status:', error);
    res.status(500).json({
      error: 'Failed to get workflow status',
      details: error.message
    });
  }
});

// Cancel workflow execution
router.post('/workflow/:workflowId/cancel', validateApiKey, async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;
    
    console.log(`🚫 Cancelling workflow: ${workflowId}`);
    
    // In a real implementation, this would cancel the running workflow
    res.json({
      success: true,
      workflow_id: workflowId,
      status: 'cancelled',
      message: 'Workflow cancellation requested'
    });

  } catch (error: any) {
    console.error('❌ Failed to cancel workflow:', error);
    res.status(500).json({
      error: 'Failed to cancel workflow',
      details: error.message
    });
  }
});

// =====================================================
// CONFIGURATION AND UTILITY ENDPOINTS
// =====================================================

// Get default workflow configuration
router.get('/config/default', validateApiKey, async (req: Request, res: Response) => {
  try {
    const defaultConfig = ProposalWorkflow.getDefaultConfig();
    
    res.json({
      success: true,
      config: {
        ...defaultConfig,
        // Don't expose API keys
        openai_api_key: defaultConfig.openai_api_key ? '***' : null,
        pinecone_api_key: defaultConfig.pinecone_api_key ? '***' : null,
        serp_api_key: defaultConfig.serp_api_key ? '***' : null
      }
    });

  } catch (error: any) {
    console.error('❌ Failed to get default config:', error);
    res.status(500).json({
      error: 'Failed to get default configuration',
      details: error.message
    });
  }
});

// Validate workflow configuration
router.post('/config/validate', validateApiKey, async (req: Request, res: Response) => {
  try {
    const config: WorkflowConfig = req.body;
    const validation = ProposalWorkflow.validateConfig(config);
    
    res.json({
      success: true,
      validation: validation
    });

  } catch (error: any) {
    console.error('❌ Failed to validate config:', error);
    res.status(500).json({
      error: 'Failed to validate configuration',
      details: error.message
    });
  }
});

// Get workflow statistics
router.get('/stats', validateApiKey, async (req: Request, res: Response) => {
  try {
    // In a real implementation, this would query the StateManager for statistics
    const stats = {
      total_workflows: 0,
      active_workflows: 0,
      completed_workflows: 0,
      failed_workflows: 0,
      cancelled_workflows: 0,
      success_rate: 0,
      average_processing_time: 0,
      total_cost: 0
    };

    res.json({
      success: true,
      stats: stats
    });

  } catch (error: any) {
    console.error('❌ Failed to get workflow stats:', error);
    res.status(500).json({
      error: 'Failed to get workflow statistics',
      details: error.message
    });
  }
});

// Health check endpoint
router.get('/health', async (req: Request, res: Response) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        langgraph: 'operational',
        openai: process.env.OPENAI_API_KEY ? 'configured' : 'not_configured',
        pinecone: process.env.PINECONE_API_KEY ? 'configured' : 'not_configured'
      }
    };

    res.json(health);

  } catch (error: any) {
    console.error('❌ Health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

export default router;
