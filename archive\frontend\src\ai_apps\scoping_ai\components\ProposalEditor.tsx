import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  useDroppable,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { SortableItem } from "./SortableItem";
import { ContentBlock } from "./ContentBlock";
import { BlockToolbar } from "./BlockToolbar";
import { BlockProperties } from "./BlockProperties";

// Define the types of content blocks available
export type BlockType =
  | "header"
  | "text"
  | "image"
  | "quote"
  | "section"
  | "diagram"
  | "list"
  | "bullet-list"
  | "numbered-list";

// Define the structure of a content block
export interface Block {
  id: string;
  type: BlockType;
  content: string;
  level?: number; // For headers (h1, h2, h3)
  url?: string; // For images
  alt?: string; // For images
  caption?: string; // For images (caption text)
  items?: string[]; // For lists
  rowId?: string; // ID of the row this block belongs to
  width?: string; // CSS width value (e.g., "50%", "33.33%", etc.)
  imagePosition?: {
    // Position info for images from PDF
    x: number;
    y: number;
    page?: number;
  };
}

// Define structure for a row that contains multiple blocks
interface Row {
  id: string;
  blockIds: string[]; // IDs of blocks in this row
}

interface ProposalEditorProps {
  initialTitle: string;
  initialBlocks: Block[];
  onSave: (title: string, blocks: Block[], sidebarOpen: boolean) => void;
  onSidebarToggle?: (isOpen: boolean) => void;
  initialSidebarOpen?: boolean;
}

const ProposalEditor: React.FC<ProposalEditorProps> = ({
  initialTitle,
  initialBlocks,
  onSave,
  onSidebarToggle,
  initialSidebarOpen = true,
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [blocks, setBlocks] = useState<Block[]>(initialBlocks);
  const [rows, setRows] = useState<Row[]>([]);
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(initialSidebarOpen);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedBlockType, setDraggedBlockType] = useState<BlockType | null>(
    null
  );
  const [dragOverRowId, setDragOverRowId] = useState<string | null>(null);
  const initialized = useRef(false);

  // --- Initialization ---
  // Function to initialize rows from blocks (can be simplified or removed if useEffect handles it)
  const initializeRows = useCallback((initialData: Block[]) => {
    const rowMap = new Map<string, string[]>();
    const initialRows: Row[] = [];
    const processedRowIds = new Set<string>();

    initialData.forEach((block) => {
      if (block.rowId) {
        if (!rowMap.has(block.rowId)) {
          rowMap.set(block.rowId, []);
        }
        rowMap.get(block.rowId)!.push(block.id);
      }
    });

    initialData.forEach((block) => {
      if (
        block.rowId &&
        rowMap.has(block.rowId) &&
        !processedRowIds.has(block.rowId)
      ) {
        initialRows.push({
          id: block.rowId,
          blockIds: rowMap.get(block.rowId)!,
        });
        processedRowIds.add(block.rowId);
      } else if (!block.rowId && !processedRowIds.has(`implicit-${block.id}`)) {
        // Treat standalone blocks initially as implicit single-block rows
        initialRows.push({ id: `implicit-${block.id}`, blockIds: [block.id] });
        processedRowIds.add(`implicit-${block.id}`);
      }
    });
    setRows(initialRows);
    updateBlockWidths(initialRows, initialData); // Pass initialData to ensure consistency
  }, []); // Removed dependencies, should be called manually

  // Run initialization only once on mount
  useEffect(() => {
    if (!initialized.current && initialBlocks.length > 0) {
      // Ensure initial blocks have widths consistent with their row state
      const blocksWithInitialWidths = initialBlocks.map((block) => {
        const isInMultiBlockRow = initialBlocks.some(
          (other) =>
            other.id !== block.id && other.rowId === block.rowId && block.rowId
        );
        return { ...block, width: isInMultiBlockRow ? undefined : "100%" }; // Set 100% if standalone initially
      });
      setBlocks(blocksWithInitialWidths);
      initializeRows(blocksWithInitialWidths);
      initialized.current = true;
    }
  }, [initialBlocks, initializeRows]); // Depend on initialBlocks and the initializer function

  // --- Width Calculation ---
  // Recalculate widths based on rows, update blocks state
  const updateBlockWidths = useCallback(
    (currentRows: Row[], currentBlocks: Block[]) => {
      setBlocks((prevBlocks) => {
        // Use currentBlocks passed from the effect or initialization for consistency
        const blocksToUpdate = currentBlocks || prevBlocks;

        const blocksWithWidths = blocksToUpdate.map((block) => {
          const containingRow = currentRows.find((r) =>
            r.blockIds.includes(block.id)
          );

          // Apply column width only if it's an EXPLICIT row (starts with 'row-') AND has multiple blocks
          if (
            containingRow &&
            containingRow.id.startsWith("row-") &&
            containingRow.blockIds.length > 1
          ) {
            const blockCount = containingRow.blockIds.length;
            // Ensure rowId is correctly set for blocks in multi-block rows
            return {
              ...block,
              width: `${100 / blockCount}%`,
              rowId: containingRow.id,
            };
          } else {
            // Standalone block or single block in an explicit row gets full width
            // Reset rowId if it was previously in a multi-block row but is now alone
            const isNowAlone =
              containingRow && containingRow.blockIds.length === 1;
            return {
              ...block,
              width: "100%",
              rowId:
                isNowAlone && containingRow?.id.startsWith("row-")
                  ? containingRow.id
                  : undefined,
            };
          }
        });

        // Avoid state update if no change in widths/relevant rowIds
        if (JSON.stringify(prevBlocks) === JSON.stringify(blocksWithWidths)) {
          return prevBlocks;
        }
        return blocksWithWidths;
      });
    },
    [setBlocks]
  );

  // --- Row Reconstruction Effect ---
  // Recalculate rows whenever the blocks array structure changes
  useEffect(() => {
    // Don't run during initial setup if initializeRows handles it
    if (!initialized.current) return;

    const rowMap = new Map<string, string[]>();

    // Group blocks by their assigned rowId
    blocks.forEach((block) => {
      if (block.rowId && block.rowId.startsWith("row-")) {
        // Only consider explicit rows
        if (!rowMap.has(block.rowId)) {
          rowMap.set(block.rowId, []);
        }
        rowMap.get(block.rowId)!.push(block.id);
      }
      // Standalone blocks (no rowId or implicit rowId) are handled below
    });

    // Create the new rows array, preserving the order based on the blocks array
    const newRows: Row[] = [];
    const processedIds = new Set<string>(); // Tracks both block IDs and row IDs

    blocks.forEach((block) => {
      if (
        block.rowId &&
        rowMap.has(block.rowId) &&
        !processedIds.has(block.rowId)
      ) {
        // Add the explicit row when its first block is encountered
        newRows.push({
          id: block.rowId,
          blockIds: rowMap.get(block.rowId)!,
        });
        processedIds.add(block.rowId); // Mark row as processed
        // Mark all blocks in this row as processed to avoid adding them individually
        rowMap.get(block.rowId)!.forEach((id) => processedIds.add(id));
      } else if (!block.rowId && !processedIds.has(block.id)) {
        // Add standalone blocks as implicit single-item rows for rendering order
        newRows.push({
          id: `implicit-${block.id}`, // Use implicit ID for rendering key
          blockIds: [block.id],
        });
        processedIds.add(block.id); // Mark block as processed
      }
    });

    // Update the rows state *only if it has actually changed*
    setRows((currentRows) => {
      // Filter out implicit rows before comparison if they are not needed in final state
      const finalExplicitRows = newRows.filter((r) => r.id.startsWith("row-"));
      const currentExplicitRows = currentRows.filter((r) =>
        r.id.startsWith("row-")
      );

      if (
        JSON.stringify(currentExplicitRows) !==
        JSON.stringify(finalExplicitRows)
      ) {
        // Update widths based on the new rows derived from blocks
        updateBlockWidths(newRows, blocks); // Pass newRows and current blocks
        return newRows; // Return the full newRows array (incl. implicit for rendering)
      }
      // If only implicit rows changed, maybe don't update state unnecessarily
      // Check if implicit rows changed significantly if needed, otherwise return currentRows
      if (JSON.stringify(currentRows) !== JSON.stringify(newRows)) {
        updateBlockWidths(newRows, blocks); // Still update widths if implicit rows changed order
        return newRows;
      }

      return currentRows; // No change needed
    });
  }, [blocks, updateBlockWidths]); // Triggered by changes in blocks array instance

  // --- Drag and Drop Handlers ---
  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 5 } }),
    useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
    if (
      typeof event.active.id === "string" &&
      event.active.id.startsWith("new-")
    ) {
      setDraggedBlockType(event.active.id.replace("new-", "") as BlockType);
    }
  }, []);

  // Placeholder - needed for the DndContext setup
  const handleDragOver = useCallback((event: DragEndEvent) => {
    const { over } = event;
    // Maybe set dragOverRowId here for visual feedback?
    // setDragOverRowId(over ? (over.data.current?.rowId || null) : null);
  }, []);

  // Function to check proximity (simplified)
  const shouldAddToRow = useCallback(() => {
    // Implement actual proximity check if needed, for now assume true if dropping on a block
    return true;
  }, []);

  // --- Core Drag End Logic ---
  // Focuses on updating the blocks array state (order and rowId assignments)
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      setActiveId(null); // Reset active element

      if (!over || !active) {
        setDraggedBlockType(null); // Reset if dropped outside or no active element
        return;
      }

      // --- Handle adding NEW blocks ---
      const isNewBlock =
        typeof active.id === "string" &&
        active.id.startsWith("new-") &&
        draggedBlockType;
      if (isNewBlock) {
        const newBlockBase: Omit<Block, "id" | "rowId" | "width"> = {
          // Base structure without ID/rowId/width
          type: draggedBlockType,
          content: "",
          ...(draggedBlockType === "header" && { level: 2 }),
          ...(draggedBlockType === "image" && { url: "", alt: "" }),
          ...(draggedBlockType === "list" && { items: [""] }),
        };

        setBlocks((prevBlocks) => {
          let newBlocks = [...prevBlocks];
          const newBlockId = `block-${Date.now()}`;
          let finalNewBlock: Block | null = null; // Will hold the complete new block object

          // Case A: Dropping into a Drop Zone (between existing rows/blocks)
          if (typeof over.id === "string" && over.id.startsWith("drop-")) {
            const dropIndex = parseInt(over.id.replace("drop-", ""));
            // New block initially gets its own rowId and full width
            const rowId = `row-${newBlockId}`;
            finalNewBlock = {
              ...newBlockBase,
              id: newBlockId,
              rowId: rowId,
              width: "100%",
            };
            newBlocks.splice(dropIndex, 0, finalNewBlock);
          }
          // Case B: Dropping next to an Existing Block
          else if (
            typeof over.id === "string" &&
            over.id.startsWith("block-")
          ) {
            const targetBlockIndex = newBlocks.findIndex(
              (b) => b.id === over.id
            );
            if (targetBlockIndex === -1) return prevBlocks; // Target not found

            const targetBlock = newBlocks[targetBlockIndex];
            const isProximityDrop = shouldAddToRow(); // Check if drop implies column creation

            if (targetBlock.rowId && isProximityDrop) {
              // Subcase B1: Add to target's existing row
              finalNewBlock = {
                ...newBlockBase,
                id: newBlockId,
                rowId: targetBlock.rowId,
              }; // Inherit rowId
              newBlocks.splice(targetBlockIndex + 1, 0, finalNewBlock); // Insert after target
              // Width will be recalculated by the useEffect hook
            } else if (isProximityDrop) {
              // Subcase B2: Create a new row containing both target and new block
              const newRowId = `row-${Date.now()}`;
              finalNewBlock = {
                ...newBlockBase,
                id: newBlockId,
                rowId: newRowId,
              }; // Assign new rowId
              // Update target block's rowId in the array
              newBlocks[targetBlockIndex] = { ...targetBlock, rowId: newRowId };
              // Insert new block after target
              newBlocks.splice(targetBlockIndex + 1, 0, finalNewBlock);
              // Widths will be recalculated by the useEffect hook
            } else {
              // Subcase B3: Add as a new separate block (implicitly a new row)
              const newRowId = `row-${newBlockId}`; // Give it its own explicit row ID
              finalNewBlock = {
                ...newBlockBase,
                id: newBlockId,
                rowId: newRowId,
                width: "100%",
              };
              newBlocks.splice(targetBlockIndex + 1, 0, finalNewBlock); // Insert after target
            }
          } else {
            // Fallback: Add to the very end if dropped somewhere unexpected
            const rowId = `row-${newBlockId}`;
            finalNewBlock = {
              ...newBlockBase,
              id: newBlockId,
              rowId: rowId,
              width: "100%",
            };
            newBlocks.push(finalNewBlock);
          }

          if (finalNewBlock) {
            setSelectedBlockId(finalNewBlock.id); // Select the newly added block
          }
          return newBlocks; // Return the updated blocks array
        });

        setDraggedBlockType(null); // Reset dragged type state
        return; // Exit after handling new block addition
      }

      // --- Handle moving EXISTING blocks ---
      const isMovingExisting =
        active.id !== over.id &&
        typeof active.id === "string" &&
        !active.id.startsWith("new-");
      if (isMovingExisting) {
        setBlocks((prevBlocks) => {
          const oldIndex = prevBlocks.findIndex((b) => b.id === active.id);
          if (oldIndex === -1) return prevBlocks; // Block being dragged not found

          const blockToMove = { ...prevBlocks[oldIndex] }; // Get a copy of the block

          // Create array without the dragged block temporarily
          let tempBlocksArray = prevBlocks.filter((b) => b.id !== active.id);

          // Determine the target position and context
          let targetIndex = -1;
          let targetBlock: Block | null = null;
          let isDropZone = false;

          if (typeof over.id === "string" && over.id.startsWith("drop-")) {
            targetIndex = parseInt(over.id.replace("drop-", ""));
            isDropZone = true;
          } else if (
            typeof over.id === "string" &&
            over.id.startsWith("block-")
          ) {
            const overIndex = tempBlocksArray.findIndex(
              (b) => b.id === over.id
            );
            if (overIndex !== -1) {
              targetIndex = overIndex + 1; // Default to inserting *after* the target block
              targetBlock = tempBlocksArray[overIndex];
            } else return prevBlocks; // Target block not found in temp array
          } else {
            return prevBlocks; // Invalid drop target
          }

          // --- Determine the new rowId for the moved block ---
          let newRowId: string | undefined = undefined;
          const isProximityDrop = targetBlock && shouldAddToRow(); // Check proximity

          if (isDropZone) {
            // Dropped between rows/blocks, becomes its own row
            newRowId = `row-${blockToMove.id}`;
            blockToMove.width = "100%"; // Reset width
          } else if (targetBlock?.rowId && isProximityDrop) {
            // Dropped onto a block in a row -> Join that row
            newRowId = targetBlock.rowId;
            // Width will be recalculated
          } else if (targetBlock && isProximityDrop) {
            // Dropped onto a standalone block -> Create new row with both
            newRowId = `row-${Date.now()}`;
            // Update the target block's rowId in the temp array
            const targetIdxInTemp = tempBlocksArray.findIndex(
              (b) => b.id === targetBlock!.id
            );
            if (targetIdxInTemp !== -1) {
              tempBlocksArray[targetIdxInTemp] = {
                ...tempBlocksArray[targetIdxInTemp],
                rowId: newRowId,
              };
            }
          } else {
            // Dropped onto a block but not close enough, or other cases
            newRowId = `row-${blockToMove.id}`; // Becomes its own row
            blockToMove.width = "100%"; // Reset width
          }

          blockToMove.rowId = newRowId; // Assign the determined rowId to the moved block

          // Insert the moved block at the calculated target index
          tempBlocksArray.splice(targetIndex, 0, blockToMove);

          return tempBlocksArray; // Return the final, reordered array
        });
      }
    },
    [
      blocks,
      draggedBlockType,
      shouldAddToRow,
      setBlocks,
      setSelectedBlockId,
      setDraggedBlockType,
      setActiveId,
    ]
  ); // Dependencies

  // --- Other Callbacks (addBlock, updateBlock, deleteBlock, handleSave, toggleSidebar) ---
  // Add a new block programmatically (e.g., from toolbar button click, not drag)
  const addBlock = useCallback(
    (type: BlockType) => {
      const newBlockId = `block-${Date.now()}`;
      const newBlock: Block = {
        id: newBlockId,
        type,
        content: "",
        rowId: `row-${newBlockId}`, // Give it its own row initially
        width: "100%", // Default to full width
      };

      if (type === "header") newBlock.level = 2;
      if (type === "image") {
        newBlock.url = "";
        newBlock.alt = "";
      }
      if (type === "list") newBlock.items = [""];

      // Add to the end of the blocks array
      setBlocks((prevBlocks) => [...prevBlocks, newBlock]);
      setSelectedBlockId(newBlock.id); // Select the new block
    },
    [setBlocks, setSelectedBlockId]
  );

  // Update a block's content or properties
  const updateBlock = useCallback(
    (id: string, updates: Partial<Block>) => {
      setBlocks((prevBlocks) =>
        prevBlocks.map((block) =>
          block.id === id ? { ...block, ...updates } : block
        )
      );
      // Note: If rowId changes here, the useEffect will handle row reconstruction
    },
    [setBlocks]
  );

  // Delete a block
  const deleteBlock = useCallback(
    (id: string) => {
      setBlocks((prevBlocks) => prevBlocks.filter((block) => block.id !== id));
      // The useEffect hook watching `blocks` will automatically handle row updates
      if (selectedBlockId === id) {
        setSelectedBlockId(null); // Deselect if the deleted block was selected
      }
    },
    [setBlocks, selectedBlockId, setSelectedBlockId]
  );

  // Handle save action
  const handleSave = useCallback(() => {
    // Pass the current blocks state, which should be the source of truth
    onSave(title, blocks, sidebarOpen);
  }, [title, blocks, sidebarOpen, onSave]);

  // Toggle sidebar visibility
  const toggleSidebar = useCallback(
    (isOpen: boolean) => {
      setSidebarOpen(isOpen);
      if (onSidebarToggle) {
        onSidebarToggle(isOpen);
      }
    },
    [onSidebarToggle]
  );

  // Find the active block data for the drag overlay
  const activeBlockForOverlay =
    activeId && !activeId.startsWith("new-")
      ? blocks.find((block) => block.id === activeId)
      : null;

  // --- Rendering Logic ---
  const DropZone: React.FC<{ id: string }> = ({ id }) => {
    const { setNodeRef, isOver } = useDroppable({ id });
    return (
      <div
        ref={setNodeRef}
        className={`h-2 my-1 rounded transition-all ${
          isOver ? "bg-indigo-200" : "bg-transparent hover:bg-gray-100"
        }`} // Make non-hover transparent
      />
    );
  };

  // Renders a row (either explicit multi-block or implicit single-block)
  const RowRenderer: React.FC<{ row: Row; rowIndex: number }> = ({
    row,
    rowIndex,
  }) => {
    // Find the actual block data for the IDs in this row
    // Maintain the order specified in row.blockIds
    const rowBlocksData = row.blockIds
      .map((id) => blocks.find((b) => b.id === id))
      .filter((b): b is Block => !!b); // Filter out undefined if block somehow missing

    const isMultiBlockExplicitRow =
      row.id.startsWith("row-") && rowBlocksData.length > 1;

    return (
      <div key={row.id} className="row-container">
        {/* Add DropZone before the first row */}
        {rowIndex === 0 && <DropZone id="drop-0" />}

        <div
          className={`flex flex-wrap ${
            isMultiBlockExplicitRow
              ? "border border-dashed border-transparent hover:border-gray-300 rounded p-1 -m-1"
              : ""
          }`}
        >
          {rowBlocksData.map((block) => (
            <div
              key={block.id}
              style={{ width: block.width || "100%" }} // Use width from block state
              className="px-1 relative group" // Add padding if needed
            >
              {/* Removed overlay divs for simplicity for now */}
              <SortableItem id={block.id}>
                <ContentBlock
                  block={block}
                  isSelected={selectedBlockId === block.id}
                  onClick={() => setSelectedBlockId(block.id)}
                  onChange={(updates) => updateBlock(block.id, updates)}
                  onDelete={() => deleteBlock(block.id)}
                />
              </SortableItem>
            </div>
          ))}
        </div>

        {/* Add DropZone after every row/block */}
        <DropZone id={`drop-${rowIndex + 1}`} />
      </div>
    );
  };

  // --- Main Return JSX ---
  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter} // Keep simple collision detection
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      // onDragOver={handleDragOver} // Can add back if needed for row highlighting
    >
      <div className="flex h-full">
        {/* Sidebar Panel (unchanged) */}
        <div
          className={`${
            sidebarOpen ? "w-[800px] opacity-100" : "w-0 opacity-0"
          } h-full bg-gray-100 border-r border-gray-200 transition-all duration-300 ease-in-out overflow-hidden`}
        >
          <div className="p-4 h-full overflow-y-auto">
            {/* Sidebar header */}
            <div className="flex justify-between items-center mb-4 sticky top-0 bg-gray-100 pt-1 pb-2 z-10">
              <h3 className="text-lg font-semibold">Content Blocks</h3>
              <button
                onClick={() => toggleSidebar(false)}
                className="p-1.5 rounded-md text-gray-500 hover:bg-gray-200 focus:outline-none"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">
                Content Blocks
              </h4>
              <BlockToolbar onAddBlock={addBlock} />
            </div>

            {/* Block properties panel */}
            {selectedBlockId && (
              <div className="mt-6 border-t border-gray-200 pt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3 sticky top-12 bg-gray-100 pb-2 z-10">
                  Block Properties
                </h4>
                {/* Properties editor for the selected block */}
                {blocks.find((block) => block.id === selectedBlockId) && (
                  <BlockProperties
                    block={
                      blocks.find((block) => block.id === selectedBlockId)!
                    }
                    onChange={(updates) =>
                      updateBlock(selectedBlockId, updates)
                    }
                  />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-grow overflow-hidden">
          <div className="h-full flex flex-col overflow-hidden">
            {/* Title Input (unchanged) */}
            <div className="flex items-center p-6 pb-4">
              <button
                onClick={() => toggleSidebar(!sidebarOpen)}
                className="mr-4 p-2 rounded-md text-gray-500 hover:bg-gray-100 focus:outline-none"
                aria-label="Toggle sidebar"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  {sidebarOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 19l-7-7 7-7"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5l7 7-7 7"
                    />
                  )}
                </svg>
              </button>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full text-3xl font-bold p-2 border-b-2 border-transparent focus:border-indigo-500 focus:outline-none"
                placeholder="Proposal Title"
              />
            </div>

            {/* Content Blocks Area */}
            <div className="flex-grow p-6 pt-2 overflow-y-auto">
              <SortableContext
                // Provide IDs of all draggable items (blocks)
                items={blocks.map((block) => block.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="content-area space-y-1">
                  {" "}
                  {/* Reduced space */}
                  {blocks.length === 0 ? (
                    <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <h3 className="mt-2 text-sm font-medium text-gray-900">
                        No content blocks
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Get started by adding a content block from the sidebar.
                      </p>
                      {!sidebarOpen && (
                        <div className="mt-6">
                          <button
                            type="button"
                            onClick={() => toggleSidebar(true)}
                            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            <svg
                              className="-ml-1 mr-2 h-5 w-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                              />
                            </svg>
                            Open Content Blocks
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    // Render rows based on the 'rows' state array
                    rows.map((row, index) => (
                      <RowRenderer key={row.id} row={row} rowIndex={index} />
                    ))
                  )}
                </div>
              </SortableContext>
            </div>

            {/* Save Button (unchanged) */}
            <div className="p-6 border-t border-gray-200 bg-white">
              <div className="flex justify-end">
                <button
                  onClick={handleSave}
                  className="px-6 py-2 bg-indigo-500 text-white font-medium rounded-lg hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
                >
                  Save Proposal
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Floating Add Button (unchanged) */}
        {!sidebarOpen && (
          <div className="fixed bottom-6 left-6">
            <div className="relative group">
              <button
                onClick={() => toggleSidebar(true)}
                className="w-14 h-14 bg-indigo-600 rounded-full flex items-center justify-center text-white shadow-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </button>
              <div className="absolute bottom-full left-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                  Add Content Block
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Drag Overlay */}
        <DragOverlay>
          {/* Overlay for NEW blocks */}
          {activeId && activeId.startsWith("new-") && draggedBlockType && (
            <div className="p-3 rounded-lg border border-indigo-500 bg-indigo-50 shadow-md">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-indigo-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <span className="text-sm font-medium text-indigo-700">
                  {draggedBlockType}
                </span>
              </div>
            </div>
          )}
          {/* Overlay for EXISTING blocks */}
          {activeId && activeBlockForOverlay && (
            <div className="p-4 rounded-lg border border-indigo-500 bg-indigo-50 shadow-lg">
              <div className="flex items-center mb-2 text-xs text-indigo-500 uppercase tracking-wide">
                <span className="mr-2">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </span>
                {activeBlockForOverlay.type}
              </div>
              <div className="text-gray-700">
                {activeBlockForOverlay.content ||
                  `${activeBlockForOverlay.type} block`}
              </div>
            </div>
          )}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default ProposalEditor;
