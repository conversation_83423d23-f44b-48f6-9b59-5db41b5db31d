import React, { useState, useEffect } from 'react';
import { Section } from '../../../types/scoping';

interface SectionSidePanelProps {
  sections: Section[];
  activeSectionId?: string;
  setActiveSection: (sectionId: string) => void;
  onGenerateSection: (sectionId: string) => Promise<void>;
  onGenerateAll: () => Promise<void>;
  onSectionReorder?: (sections: Section[]) => Promise<void>;
}

const SectionSidePanel: React.FC<SectionSidePanelProps> = ({
  sections,
  activeSectionId,
  setActiveSection,
  onGenerateSection,
  onGenerateAll,
  onSectionReorder
}) => {
  const pendingSections = sections.filter(section => section.status === 'pending');
  const completedSections = sections.filter(section => section.status === 'completed');
  const generatingSections = sections.filter(section => section.status === 'generating');
  
  // Calculate progress percentage
  const progress = sections.length > 0 
    ? Math.round((completedSections.length / sections.length) * 100) 
    : 0;
  
  // Track generation state
  const [isGeneratingAll, setIsGeneratingAll] = useState(false);
  const [generationInProgress, setGenerationInProgress] = useState(false);

  // Update generation state based on sections
  useEffect(() => {
    setGenerationInProgress(generatingSections.length > 0);
  }, [generatingSections.length]);
  
  // Simple drag and drop reordering
  const [draggedSectionId, setDraggedSectionId] = useState<string | null>(null);

  const handleDragStart = (e: React.DragEvent, sectionId: string) => {
    setDraggedSectionId(sectionId);
    e.dataTransfer.effectAllowed = 'move';
    // Add a transparent image to prevent the default drag ghost
    const img = new Image();
    img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
    e.dataTransfer.setDragImage(img, 0, 0);
  };

  const handleDragOver = (e: React.DragEvent, overSectionId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, dropSectionId: string) => {
    e.preventDefault();
    
    if (!draggedSectionId || draggedSectionId === dropSectionId) return;
    
    const draggedIndex = sections.findIndex(section => section.id === draggedSectionId);
    const dropIndex = sections.findIndex(section => section.id === dropSectionId);
    
    if (draggedIndex === -1 || dropIndex === -1) return;
    
    // Update local order
    const reorderedSections = [...sections];
    const [removedSection] = reorderedSections.splice(draggedIndex, 1);
    reorderedSections.splice(dropIndex, 0, removedSection);
    
    // Update order properties
    const updatedSections = reorderedSections.map((section, index) => ({
      ...section,
      order: index
    }));
    
    // Notify parent of reordering
    if (onSectionReorder) {
      await onSectionReorder(updatedSections);
    }
    
    setDraggedSectionId(null);
  };

  const handleDragEnd = () => {
    setDraggedSectionId(null);
  };

  // Handle generate all with loading state
  const handleGenerateAll = async () => {
    if (generationInProgress || isGeneratingAll) return;
    
    setIsGeneratingAll(true);
    try {
      await onGenerateAll();
    } finally {
      setIsGeneratingAll(false);
    }
  };

  // Handle generate single section with loading state
  const handleGenerateSection = async (sectionId: string) => {
    if (generationInProgress) return;
    
    try {
      await onGenerateSection(sectionId);
    } catch (error) {
      console.error("Error generating section:", error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'generating':
        return (
          <svg className="h-4 w-4 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      case 'completed':
        return (
          <svg className="h-4 w-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'failed':
        return (
          <svg className="h-4 w-4 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-80 flex-shrink-0 border-r border-gray-200 bg-white overflow-y-auto">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Sections</h2>
        <p className="mt-1 text-sm text-gray-500">
          Template sections to generate with AI
        </p>
        
        {/* Generation Progress Overview */}
        <div className="mt-3 bg-gray-50 rounded-md p-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">
              {sections.filter(s => s.status === 'completed').length} / {sections.length}
            </span>
          </div>
          <div className="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-green-500 transition-all duration-500"
              style={{ 
                width: `${(sections.filter(s => s.status === 'completed').length / sections.length) * 100}%` 
              }}
            />
          </div>
        </div>
        
        {/* Generate All Button */}
        {sections.some(s => s.status === 'pending') && (
          <button
            onClick={handleGenerateAll}
            disabled={generationInProgress || isGeneratingAll}
            className={`mt-3 w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              generationInProgress || isGeneratingAll 
                ? 'bg-indigo-300 cursor-not-allowed' 
                : 'bg-indigo-600 hover:bg-indigo-700'
            }`}
          >
            <svg className="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            {isGeneratingAll ? 'Generating...' : 'Generate All Sections'}
          </button>
        )}
      </div>

      <div className="px-4 py-2">
        {sections.map((section) => (
          <div
            key={section.id}
            className={`mb-2 p-3 rounded-md cursor-pointer transition-all duration-200 ${
              section.id === activeSectionId
                ? 'bg-indigo-50 border-indigo-500'
                : 'hover:bg-gray-50 border-transparent'
            } border`}
            onClick={() => setActiveSection(section.id)}
          >
            <div className="flex items-center justify-between">
              <span className="font-medium text-gray-900">{section.title}</span>
              {section.status === 'pending' && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleGenerateSection(section.id);
                  }}
                  disabled={generationInProgress}
                  className={`ml-2 p-1 ${
                    generationInProgress
                      ? 'text-indigo-300 cursor-not-allowed'
                      : 'text-indigo-600 hover:bg-indigo-50'
                  } rounded`}
                >
                  <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </button>
              )}
            </div>
            
            {/* Status Indicator */}
            <div className="mt-2 flex items-center">
              {section.status === 'generating' ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-indigo-500 border-t-transparent" />
                  <span className="ml-2 text-sm text-indigo-600">Generating...</span>
                </>
              ) : section.status === 'completed' ? (
                <>
                  <svg className="w-4 h-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="ml-2 text-sm text-green-600">Complete</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="ml-2 text-sm text-gray-500">Pending</span>
                </>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SectionSidePanel; 