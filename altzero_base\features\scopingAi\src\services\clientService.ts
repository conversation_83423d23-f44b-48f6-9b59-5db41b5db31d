import { supabase } from "../lib/supabase";

// Database client interface matching Supabase schema
export interface DbClient {
  id: string;
  name: string;
  contact_person: string | null;
  email: string | null;
  phone: string | null;
  industry: string | null;
  company: string | null;
  created_at: string;
  updated_at: string;
  user_id: string;
}

// Application client interface
export interface Client {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  company: string;
  createdAt: Date;
  updatedAt: Date;
}

// Client form data interface
export interface ClientFormData {
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
}

// Transform database client to application client
const transformDbClient = (dbClient: DbClient): Client => ({
  id: dbClient.id,
  name: dbClient.name,
  contactPerson: dbClient.contact_person || "",
  email: dbClient.email || "",
  phone: dbClient.phone || "",
  industry: dbClient.industry || "",
  company: dbClient.company || "",
  createdAt: new Date(dbClient.created_at),
  updatedAt: new Date(dbClient.updated_at),
});

// Fetch all clients for the current user
export const fetchClients = async (): Promise<Client[]> => {
  // Get current user
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError) {
    console.error("Error getting user:", userError);
    throw userError;
  }

  const userId = userData.user?.id;
  if (!userId) {
    console.error("No user ID found, user may not be authenticated");
    throw new Error("User not authenticated");
  }

  // Fetch clients for this user
  const { data, error } = await supabase
    .from("scopingai_clients")
    .select("*")
    .eq("user_id", userId)
    .order("name");

  if (error) {
    console.error("Error fetching clients:", error);
    throw error;
  }

  return data.map(transformDbClient);
};

// Create a new client
export const createClient = async (
  clientData: ClientFormData
): Promise<void> => {
  // Get current user
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError) {
    console.error("Error getting user:", userError);
    throw userError;
  }

  const userId = userData.user?.id;
  if (!userId) {
    throw new Error("User not authenticated");
  }

  // Create new client
  const { error } = await supabase.from("scopingai_clients").insert([
    {
      user_id: userId,
      name: clientData.name,
      contact_person: clientData.contactPerson,
      email: clientData.email,
      phone: clientData.phone || "",
      industry: clientData.industry,
      company: "",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ]);

  if (error) {
    console.error("Error creating client:", error);
    throw error;
  }
};

// Update an existing client
export const updateClient = async (
  clientId: string,
  clientData: ClientFormData
): Promise<void> => {
  const { error } = await supabase
    .from("scopingai_clients")
    .update({
      name: clientData.name,
      contact_person: clientData.contactPerson,
      email: clientData.email,
      phone: clientData.phone || "",
      industry: clientData.industry,
      updated_at: new Date().toISOString(),
    })
    .eq("id", clientId);

  if (error) {
    console.error("Error updating client:", error);
    throw error;
  }
};

// Delete a client
export const deleteClient = async (clientId: string): Promise<void> => {
  const { error } = await supabase
    .from("scopingai_clients")
    .delete()
    .eq("id", clientId);

  if (error) {
    console.error("Error deleting client:", error);
    throw error;
  }
};

// Check if client name already exists for the current user
export const checkClientNameExists = async (
  name: string,
  excludeId?: string
): Promise<boolean> => {
  // Get current user
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData.user?.id) {
    return false;
  }

  let query = supabase
    .from("scopingai_clients")
    .select("id")
    .eq("user_id", userData.user.id)
    .ilike("name", name);

  if (excludeId) {
    query = query.neq("id", excludeId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error checking client name:", error);
    return false;
  }

  return data.length > 0;
};
