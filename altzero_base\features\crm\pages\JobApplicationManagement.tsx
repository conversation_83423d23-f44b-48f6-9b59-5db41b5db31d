import React, { useState, useEffect } from 'react';
import { FileText, Search, Eye, Download, MessageSquare, CheckCircle, XCircle } from 'lucide-react';
import CRMLayout from '../components/CRMLayout';

interface JobApplication {
  id: string;
  job_id: string;
  job_title: string;
  applicant_name: string;
  applicant_email: string;
  applicant_phone?: string;
  resume_url?: string;
  cover_letter?: string;
  status: 'applied' | 'screening' | 'interview' | 'offer' | 'hired' | 'rejected';
  applied_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

const JobApplicationManagement: React.FC = () => {
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      setLoading(true);
      // TODO: Implement API call to fetch job applications
      // const response = await crmService.getJobApplications();
      // setApplications(response.data);
      
      // Mock data for now
      setApplications([
        {
          id: '1',
          job_id: '1',
          job_title: 'Senior Software Engineer',
          applicant_name: 'John Smith',
          applicant_email: '<EMAIL>',
          applicant_phone: '******-0123',
          resume_url: '/resumes/john-smith.pdf',
          cover_letter: 'I am excited to apply for this position...',
          status: 'interview',
          applied_date: '2024-01-20T10:00:00Z',
          notes: 'Strong technical background, good communication skills',
          created_at: '2024-01-20T10:00:00Z',
          updated_at: '2024-01-22T10:00:00Z'
        },
        {
          id: '2',
          job_id: '1',
          job_title: 'Senior Software Engineer',
          applicant_name: 'Sarah Johnson',
          applicant_email: '<EMAIL>',
          applicant_phone: '******-0124',
          resume_url: '/resumes/sarah-johnson.pdf',
          status: 'screening',
          applied_date: '2024-01-21T10:00:00Z',
          created_at: '2024-01-21T10:00:00Z',
          updated_at: '2024-01-21T10:00:00Z'
        },
        {
          id: '3',
          job_id: '2',
          job_title: 'Product Manager',
          applicant_name: 'Mike Chen',
          applicant_email: '<EMAIL>',
          status: 'applied',
          applied_date: '2024-01-22T10:00:00Z',
          created_at: '2024-01-22T10:00:00Z',
          updated_at: '2024-01-22T10:00:00Z'
        }
      ]);
    } catch (error) {
      console.error('Error loading applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.applicant_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.applicant_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.job_title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'applied': return 'bg-blue-100 text-blue-800';
      case 'screening': return 'bg-yellow-100 text-yellow-800';
      case 'interview': return 'bg-purple-100 text-purple-800';
      case 'offer': return 'bg-orange-100 text-orange-800';
      case 'hired': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="w-8 h-8 text-blue-600 mr-3" />
              Job Applications
            </h1>
            <p className="text-gray-600 mt-1">Track and manage candidate applications</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search applications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="applied">Applied</option>
                <option value="screening">Screening</option>
                <option value="interview">Interview</option>
                <option value="offer">Offer</option>
                <option value="hired">Hired</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>

        {/* Applications Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Applicant</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Job Title</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Applied Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Resume</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-gray-500">
                      Loading applications...
                    </td>
                  </tr>
                ) : filteredApplications.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-gray-500">
                      {searchTerm || statusFilter !== 'all' ? 'No applications found matching your criteria.' : 'No applications found.'}
                    </td>
                  </tr>
                ) : (
                  filteredApplications.map((application) => (
                    <tr key={application.id} className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{application.applicant_name}</div>
                        <div className="text-sm text-gray-500">{application.applicant_email}</div>
                        {application.applicant_phone && (
                          <div className="text-sm text-gray-500">{application.applicant_phone}</div>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-900">{application.job_title}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600">
                          {new Date(application.applied_date).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${getStatusColor(application.status)}`}>
                          {application.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {application.resume_url ? (
                          <button className="text-blue-600 hover:text-blue-700 text-sm flex items-center">
                            <Download className="w-4 h-4 mr-1" />
                            Download
                          </button>
                        ) : (
                          <span className="text-gray-400 text-sm">No resume</span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                            <MessageSquare className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-green-600 transition-colors">
                            <CheckCircle className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-red-600 transition-colors">
                            <XCircle className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-sm text-blue-800 font-medium">Total Applications</div>
            <div className="text-2xl font-bold text-blue-900">{filteredApplications.length}</div>
          </div>
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="text-sm text-yellow-800 font-medium">In Review</div>
            <div className="text-2xl font-bold text-yellow-900">
              {filteredApplications.filter(app => ['screening', 'interview'].includes(app.status)).length}
            </div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-sm text-green-800 font-medium">Hired</div>
            <div className="text-2xl font-bold text-green-900">
              {filteredApplications.filter(app => app.status === 'hired').length}
            </div>
          </div>
          <div className="bg-red-50 rounded-lg p-4">
            <div className="text-sm text-red-800 font-medium">Rejected</div>
            <div className="text-2xl font-bold text-red-900">
              {filteredApplications.filter(app => app.status === 'rejected').length}
            </div>
          </div>
        </div>
      </div>
    </CRMLayout>
  );
};

export default JobApplicationManagement;
