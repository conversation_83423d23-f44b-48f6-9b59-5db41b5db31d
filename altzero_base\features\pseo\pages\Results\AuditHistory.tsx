import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import { useUser } from '../../../../base/contextapi/UserContext';
import type { PSEOAudit } from '../../types';
import PSEOLayout from '../../components/PSEOLayout';

interface AuditWithDetails extends PSEOAudit {
  website_name: string;
  website_url: string;
  client_name: string;
}

const AuditHistory: React.FC = () => {
  const { user } = useUser();
  const [audits, setAudits] = useState<AuditWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'created_at' | 'completed_at'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const navigate = useNavigate();

  useEffect(() => {
    if (user?.id) {
      loadAudits();
    }
  }, [user?.id]);

  const loadAudits = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      // Get all audits for the user (no limit for history page)
      const auditsData = await databaseService.getAuditsByUserId(user.id, 100);
      setAudits(auditsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load audit history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'running':
      case 'scraping':
      case 'analyzing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'running':
      case 'scraping':
      case 'analyzing':
        return '⏳';
      case 'pending':
        return '⭕';
      default:
        return '❓';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const filteredAndSortedAudits = audits
    .filter(audit => filterStatus === 'all' || audit.status === filterStatus)
    .sort((a, b) => {
      const aDate = sortBy === 'created_at' ? a.created_at : (a.completed_at || a.created_at);
      const bDate = sortBy === 'created_at' ? b.created_at : (b.completed_at || b.created_at);
      
      const comparison = new Date(aDate).getTime() - new Date(bDate).getTime();
      return sortOrder === 'desc' ? -comparison : comparison;
    });

  const handleViewResults = (audit: AuditWithDetails) => {
    if (audit.status === 'completed') {
      navigate(`/audit-results/${audit.id}`);
    }
  };

  const handleRetryAudit = (audit: AuditWithDetails) => {
    navigate(`/audit-runner?retry=${audit.website_url}`);
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Audit History
          </h1>
          <p className="text-muted-foreground">
            View and manage all your SEO audit results
          </p>
        </div>

        {/* Filters and Controls */}
        <div className="bg-card rounded-lg border p-6 mb-6">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex flex-wrap gap-4">
              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="all">All Statuses</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="running">Running</option>
                  <option value="pending">Pending</option>
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium mb-1">Sort By</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'created_at' | 'completed_at')}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="created_at">Created Date</option>
                  <option value="completed_at">Completed Date</option>
                </select>
              </div>

              {/* Sort Order */}
              <div>
                <label className="block text-sm font-medium mb-1">Order</label>
                <select
                  value={sortOrder}
                  onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="desc">Newest First</option>
                  <option value="asc">Oldest First</option>
                </select>
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              {filteredAndSortedAudits.length} of {audits.length} audits
            </div>
          </div>
        </div>

        {/* Results */}
        {error ? (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6">
            <h3 className="font-medium text-destructive mb-1">Error Loading History</h3>
            <p className="text-sm text-destructive">{error}</p>
            <button
              onClick={loadAudits}
              className="mt-4 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        ) : filteredAndSortedAudits.length === 0 ? (
          <div className="bg-card rounded-lg border p-12 text-center">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium mb-2">No Audits Found</h3>
            <p className="text-muted-foreground mb-6">
              {audits.length === 0 
                ? "You haven't run any SEO audits yet." 
                : "No audits match your current filters."
              }
            </p>
            <button
              onClick={() => navigate('/audit-runner')}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
            >
              Run Your First Audit
            </button>
          </div>
        ) : (
          <div className="bg-card rounded-lg border overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="text-left p-4 font-medium">Website</th>
                    <th className="text-left p-4 font-medium">Client</th>
                    <th className="text-left p-4 font-medium">Status</th>
                    <th className="text-left p-4 font-medium">Created</th>
                    <th className="text-left p-4 font-medium">Duration</th>
                    <th className="text-left p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAndSortedAudits.map((audit, index) => (
                    <tr key={audit.id} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/20'}>
                      <td className="p-4">
                        <div>
                          <div className="font-medium text-foreground">{audit.website_name}</div>
                          <div className="text-sm text-muted-foreground">{audit.website_url}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-foreground">{audit.client_name}</div>
                      </td>
                      <td className="p-4">
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium border ${getStatusColor(audit.status)}`}>
                          {getStatusIcon(audit.status)}
                          {audit.status.charAt(0).toUpperCase() + audit.status.slice(1)}
                        </span>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-foreground">{formatDate(audit.created_at)}</div>
                        {audit.completed_at && (
                          <div className="text-xs text-muted-foreground">
                            Completed: {formatDate(audit.completed_at)}
                          </div>
                        )}
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-foreground">
                          {formatDuration(audit.processing_time_seconds)}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          {audit.status === 'completed' ? (
                            <button
                              onClick={() => handleViewResults(audit)}
                              className="bg-primary text-primary-foreground px-3 py-1 rounded text-xs hover:bg-primary/90"
                            >
                              View Results
                            </button>
                          ) : audit.status === 'failed' ? (
                            <button
                              onClick={() => handleRetryAudit(audit)}
                              className="bg-secondary text-secondary-foreground px-3 py-1 rounded text-xs hover:bg-secondary/90"
                            >
                              Retry
                            </button>
                          ) : (
                            <span className="text-xs text-muted-foreground">In Progress</span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Summary Stats */}
        {audits.length > 0 && (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-card rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-foreground">
                {audits.filter(a => a.status === 'completed').length}
              </div>
              <div className="text-sm text-muted-foreground">Completed</div>
            </div>
            <div className="bg-card rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-foreground">
                {audits.filter(a => a.status === 'failed').length}
              </div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
            <div className="bg-card rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-foreground">
                {audits.filter(a => ['running', 'scraping', 'analyzing'].includes(a.status)).length}
              </div>
              <div className="text-sm text-muted-foreground">In Progress</div>
            </div>
            <div className="bg-card rounded-lg border p-4 text-center">
              <div className="text-2xl font-bold text-foreground">
                {Math.round(audits.filter(a => a.processing_time_seconds).reduce((acc, a) => acc + (a.processing_time_seconds || 0), 0) / audits.filter(a => a.processing_time_seconds).length) || 0}s
              </div>
              <div className="text-sm text-muted-foreground">Avg Duration</div>
            </div>
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default AuditHistory; 