export interface ClientInfo {
  name: string;
  industry: string;
  company: string;
  contactPerson: string;
  email: string;
}

export interface ScopingInfo {
  projectName: string;
  projectDescription: string;
  timeline: string;
  budget: string;
  goals: string[];
}

export interface PromptTemplate {
  id: string;
  name: string;
  content: string;
}

export interface SectionDefinition {
  id: string;
  title: string;
  description: string;
}

export interface Scoping {
  id: string;
  clientName: string;
  projectName: string;
  description: string;
  sections: {
    title: string;
    content: string;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

export interface StoredDocument {
  id: string;
  filename: string;
  content: string;
  size: number;
} 