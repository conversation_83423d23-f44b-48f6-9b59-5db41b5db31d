import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useUser } from "./UserContext";
import {
  BillingCycle,
  PlanTier,
  ResourceAddonPack,
  ResourceType,
  SubscriptionRecord,
  SubscriptionSummary,
} from "../types/subscription";
import {
  getAvailablePlans,
  getResourceTypes,
  getUserSubscription,
  getOrganizationSubscription,
  getSubscriptionSummary,
  getAvailableAddonPacks,
  changeSubscriptionPlan,
  changeBillingCycle,
  cancelSubscription,
} from "../services/subscriptionService";

interface SubscriptionContextValue {
  userSubscription: SubscriptionRecord | null;
  organizationSubscription: SubscriptionRecord | null;
  availablePlans: PlanTier[];
  resourceTypes: ResourceType[];
  availableAddons: ResourceAddonPack[];
  subscriptionSummary: SubscriptionSummary | null;
  isLoading: boolean;
  isChangingPlan: boolean;
  isChangingBillingCycle: boolean;
  isCanceling: boolean;
  loadUserSubscription: (userId: string) => Promise<void>;
  loadOrganizationSubscription: (orgId: string) => Promise<void>;
  loadSubscriptionSummary: (subscriptionId: string) => Promise<void>;
  changePlan: (subscriptionId: string, planId: string) => Promise<void>;
  changeCycle: (subscriptionId: string, cycle: BillingCycle) => Promise<void>;
  cancelSub: (subscriptionId: string, atPeriodEnd?: boolean) => Promise<void>;
  hasActiveSubscription: () => boolean;
  getFormattedNextBillingDate: () => string | null;
  getFormattedSubscriptionStatus: () => string;
}

const SubscriptionContext = createContext<SubscriptionContextValue | undefined>(
  undefined
);

export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error(
      "useSubscription must be used within a SubscriptionProvider"
    );
  }
  return context;
}

interface SubscriptionProviderProps {
  children: ReactNode;
}

export function SubscriptionProvider({ children }: SubscriptionProviderProps) {
  const { user } = useUser();
  const [userSubscription, setUserSubscription] =
    useState<SubscriptionRecord | null>(null);
  const [organizationSubscription, setOrganizationSubscription] =
    useState<SubscriptionRecord | null>(null);
  const [availablePlans, setAvailablePlans] = useState<PlanTier[]>([]);
  const [resourceTypes, setResourceTypes] = useState<ResourceType[]>([]);
  const [availableAddons, setAvailableAddons] = useState<ResourceAddonPack[]>(
    []
  );
  const [subscriptionSummary, setSubscriptionSummary] =
    useState<SubscriptionSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isChangingPlan, setIsChangingPlan] = useState(false);
  const [isChangingBillingCycle, setIsChangingBillingCycle] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);

  // Load initial data
  useEffect(() => {
    async function loadInitialData() {
      try {
        const [plans, types, addons] = await Promise.all([
          getAvailablePlans(),
          getResourceTypes(),
          getAvailableAddonPacks(),
        ]);

        setAvailablePlans(plans);
        setResourceTypes(types);
        setAvailableAddons(addons);
      } catch (error) {
        console.error("Error loading initial subscription data:", error);
      } finally {
        setIsLoading(false);
      }
    }

    loadInitialData();
  }, []);

  // Load user subscription when user changes
  useEffect(() => {
    if (user?.id) {
      loadUserSubscription(user.id);
    }
  }, [user?.id]);

  // Load user subscription
  const loadUserSubscription = async (userId: string) => {
    try {
      setIsLoading(true);
      const subscription = await getUserSubscription(userId);
      setUserSubscription(subscription);

      // Load subscription summary if subscription exists
      if (subscription) {
        await loadSubscriptionSummary(subscription.id);
      }
    } catch (error) {
      console.error("Error loading user subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load organization subscription
  const loadOrganizationSubscription = async (orgId: string) => {
    try {
      setIsLoading(true);
      const subscription = await getOrganizationSubscription(orgId);
      setOrganizationSubscription(subscription);

      // Load subscription summary if subscription exists
      if (subscription) {
        await loadSubscriptionSummary(subscription.id);
      }
    } catch (error) {
      console.error("Error loading organization subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load subscription summary
  const loadSubscriptionSummary = async (subscriptionId: string) => {
    try {
      const summary = await getSubscriptionSummary(subscriptionId);
      setSubscriptionSummary(summary);
    } catch (error) {
      console.error("Error loading subscription summary:", error);
    }
  };

  // Change subscription plan
  const changePlan = async (subscriptionId: string, planId: string) => {
    try {
      setIsChangingPlan(true);
      const updatedSubscription = await changeSubscriptionPlan(
        subscriptionId,
        planId
      );

      // Update the appropriate subscription based on owner type
      if (updatedSubscription.owner_type === "user") {
        setUserSubscription(updatedSubscription);
      } else {
        setOrganizationSubscription(updatedSubscription);
      }

      // Reload subscription summary
      await loadSubscriptionSummary(subscriptionId);
    } catch (error) {
      console.error("Error changing subscription plan:", error);
      throw error;
    } finally {
      setIsChangingPlan(false);
    }
  };

  // Change billing cycle
  const changeCycle = async (subscriptionId: string, cycle: BillingCycle) => {
    try {
      setIsChangingBillingCycle(true);
      const updatedSubscription = await changeBillingCycle(
        subscriptionId,
        cycle
      );

      // Update the appropriate subscription based on owner type
      if (updatedSubscription.owner_type === "user") {
        setUserSubscription(updatedSubscription);
      } else {
        setOrganizationSubscription(updatedSubscription);
      }
    } catch (error) {
      console.error("Error changing billing cycle:", error);
      throw error;
    } finally {
      setIsChangingBillingCycle(false);
    }
  };

  // Cancel subscription
  const cancelSub = async (
    subscriptionId: string,
    atPeriodEnd: boolean = true
  ) => {
    try {
      setIsCanceling(true);
      const updatedSubscription = await cancelSubscription(
        subscriptionId,
        atPeriodEnd
      );

      // Update the appropriate subscription based on owner type
      if (updatedSubscription.owner_type === "user") {
        setUserSubscription(updatedSubscription);
      } else {
        setOrganizationSubscription(updatedSubscription);
      }
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw error;
    } finally {
      setIsCanceling(false);
    }
  };

  // Check if user has an active subscription
  const hasActiveSubscription = () => {
    const sub = userSubscription || organizationSubscription;
    if (!sub) return false;

    return sub.status === "active" || sub.status === "trialing";
  };

  // Get formatted next billing date
  const getFormattedNextBillingDate = () => {
    const sub = userSubscription || organizationSubscription;
    if (!sub || !sub.current_period_end) return null;

    return new Date(sub.current_period_end).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Get formatted subscription status
  const getFormattedSubscriptionStatus = () => {
    const sub = userSubscription || organizationSubscription;
    if (!sub) return "No Subscription";

    switch (sub.status) {
      case "active":
        return "Active";
      case "trialing":
        return "Trial";
      case "past_due":
        return "Past Due";
      case "canceled":
        return "Canceled";
      case "incomplete":
        return "Incomplete";
      case "incomplete_expired":
        return "Expired";
      case "unpaid":
        return "Unpaid";
      default:
        return "Unknown";
    }
  };

  const value = {
    userSubscription,
    organizationSubscription,
    availablePlans,
    resourceTypes,
    availableAddons,
    subscriptionSummary,
    isLoading,
    isChangingPlan,
    isChangingBillingCycle,
    isCanceling,
    loadUserSubscription,
    loadOrganizationSubscription,
    loadSubscriptionSummary,
    changePlan,
    changeCycle,
    cancelSub,
    hasActiveSubscription,
    getFormattedNextBillingDate,
    getFormattedSubscriptionStatus,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
}
