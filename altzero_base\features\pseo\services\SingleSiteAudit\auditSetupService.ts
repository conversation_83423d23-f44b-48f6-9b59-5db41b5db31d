import { databaseService } from '../pseo/databaseService';
import { PSEO_CONSTANTS } from '../../utilities/pseo/constants';
import type { PSEOAudit, PSEOClient, PSEOWebsite } from '../../types';

export interface AuditSetupResult {
  client: PSEOClient;
  website: PSEOWebsite;
  audit: PSEOAudit;
}

class AuditSetupService {
  /**
   * Setup complete audit infrastructure (client, website, audit)
   */
  async setupAuditInfrastructure(
    websiteUrl: string,
    websiteName: string,
    userId: string,
    aiModel?: string
  ): Promise<AuditSetupResult> {
    try {
      console.log(`🔧 Setting up audit infrastructure for: ${websiteUrl}`);

      // Step 1: Create temporary client
      console.log('Creating temporary client...');
      const client = await databaseService.createClient({
        user_id: userId,
        name: 'Temporary Client',
        email: '<EMAIL>',
        company: 'Temporary Company'
      });
      console.log(`✅ Client created: ${client.id}`);

      // Step 2: Create website record
      console.log(`Creating website record for client: ${client.id}`);
      const website = await databaseService.createWebsite({
        client_id: client.id,
        url: websiteUrl,
        name: websiteName
      });
      console.log(`✅ Website created: ${website.id}`);

      // Step 3: Create audit record
      console.log(`Creating audit record for website: ${website.id}`);
      const audit = await databaseService.createAudit({
        website_id: website.id,
        status: PSEO_CONSTANTS.AUDIT_STATUS.PENDING,
        scrape_metadata: {},
        technical_analysis: {},
        content_analysis: {},
        ai_model_used: aiModel || PSEO_CONSTANTS.AI_MODELS.DEFAULT,
      });
      console.log(`✅ Audit created: ${audit.id}`);

      return {
        client,
        website,
        audit,
      };

    } catch (error) {
      console.error('Failed to setup audit infrastructure:', error);
      throw new Error(`Audit setup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create client for audit
   */
  async createAuditClient(
    userId: string,
    clientData?: {
      name?: string;
      email?: string;
      company?: string;
    }
  ): Promise<PSEOClient> {
    try {
      const client = await databaseService.createClient({
        user_id: userId,
        name: clientData?.name || 'Temporary Client',
        email: clientData?.email || '<EMAIL>',
        company: clientData?.company || 'Temporary Company'
      });

      console.log(`Client created for audit: ${client.id}`);
      return client;
    } catch (error) {
      console.error('Failed to create audit client:', error);
      throw new Error(`Client creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create website for audit
   */
  async createAuditWebsite(
    clientId: string,
    websiteUrl: string,
    websiteName: string
  ): Promise<PSEOWebsite> {
    try {
      const website = await databaseService.createWebsite({
        client_id: clientId,
        url: websiteUrl,
        name: websiteName
      });

      console.log(`Website created for audit: ${website.id}`);
      return website;
    } catch (error) {
      console.error('Failed to create audit website:', error);
      throw new Error(`Website creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create audit record
   */
  async createAuditRecord(
    websiteId: string,
    aiModel?: string
  ): Promise<PSEOAudit> {
    try {
      const audit = await databaseService.createAudit({
        website_id: websiteId,
        status: PSEO_CONSTANTS.AUDIT_STATUS.PENDING,
        scrape_metadata: {},
        technical_analysis: {},
        content_analysis: {},
        ai_model_used: aiModel || PSEO_CONSTANTS.AI_MODELS.DEFAULT,
      });

      console.log(`Audit record created: ${audit.id}`);
      return audit;
    } catch (error) {
      console.error('Failed to create audit record:', error);
      throw new Error(`Audit creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Cleanup audit infrastructure on failure
   */
  async cleanupAuditInfrastructure(
    auditId?: string,
    websiteId?: string,
    clientId?: string
  ): Promise<void> {
    try {
      console.log('🧹 Cleaning up audit infrastructure...');

      // Note: Cleanup would happen automatically via database cascading deletes
      // or could be implemented with specific cleanup methods if needed
      if (auditId) {
        console.log(`Would cleanup audit ${auditId}`);
      }

      if (websiteId) {
        try {
          await databaseService.deleteWebsite(websiteId);
          console.log(`Website ${websiteId} deleted`);
        } catch (error) {
          console.error(`Failed to delete website ${websiteId}:`, error);
        }
      }

      if (clientId) {
        try {
          await databaseService.deleteClient(clientId);
          console.log(`Client ${clientId} deleted`);
        } catch (error) {
          console.error(`Failed to delete client ${clientId}:`, error);
        }
      }

      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }

  /**
   * Validate setup requirements
   */
  validateSetupRequirements(
    websiteUrl: string,
    websiteName: string,
    userId: string
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!websiteUrl || typeof websiteUrl !== 'string') {
      errors.push('Website URL is required');
    } else {
      try {
        new URL(websiteUrl);
      } catch {
        errors.push('Website URL must be a valid URL');
      }
    }

    if (!websiteName || typeof websiteName !== 'string' || websiteName.trim().length === 0) {
      errors.push('Website name is required');
    }

    if (!userId || typeof userId !== 'string') {
      errors.push('User ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

export const auditSetupService = new AuditSetupService(); 