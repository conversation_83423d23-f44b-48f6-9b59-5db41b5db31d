import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
  Users,
  FileText,
  Sparkles,
  ArrowRight,
  Database,
  MessageSquare,
  Brain,
  Upload,
  Plus,
  Loader2,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { But<PERSON> } from "../../../../base/components/ui/button";
import { Badge } from "../../../../base/components/ui/badge";
import { useToast } from "../../../../base/hooks/use-toast";

// Import the AI Assistant component from base
import AIAssistant from "../../../knowledge/components/KnowledgeBase";
import ScopingAILayout from "../components/ScopingAILayout";
import { useUser } from "../../../../base/contextapi/UserContext";

interface KnowledgeStats {
  totalDocuments: number;
  activeClients: number;
  aiInteractions: number;
  vectorDocs: number;
  loading: boolean;
}

interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  status: string;
  uploadedAt: string;
}

export default function KnowledgeBasePage() {
  const { user } = useUser();
  const [activeSection, setActiveSection] = useState<string>("overview");
  const [stats, setStats] = useState<KnowledgeStats>({
    totalDocuments: 0,
    activeClients: 0,
    aiInteractions: 0,
    vectorDocs: 0,
    loading: true,
  });
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Load real data from backend
  useEffect(() => {
    if (user?.id) {
      loadKnowledgeBaseData();
    }
  }, [user?.id]);

  const loadKnowledgeBaseData = async () => {
    try {
      setIsLoading(true);
      setStats((prev) => ({ ...prev, loading: true }));

      // Load documents from scopingAi vector database
      const response = await fetch(
        "/api/scopingai/knowledge/documents?apiKey=scopingai",
        {
          headers: {
            "x-api-key": "scopingai",
            "x-user-id": user?.id || "anonymous",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.status}`);
      }

      const docs = await response.json();
      setDocuments(docs);

      // Calculate real stats from loaded data
      const vectorDocsCount = docs.filter(
        (doc: any) => doc.status === "success"
      ).length;
      const processingCount = docs.filter(
        (doc: any) => doc.status === "processing"
      ).length;

      // For now, we'll calculate some stats from available data
      // Later you can add endpoints for clients and AI interactions
      const calculatedStats = {
        totalDocuments: docs.length,
        activeClients: 0, // Will need API endpoint for this
        aiInteractions: 0, // Will need API endpoint for this
        vectorDocs: vectorDocsCount,
        loading: false,
      };

      setStats(calculatedStats);

      console.log(`✅ Loaded ${docs.length} documents from knowledge base`);
    } catch (error) {
      console.error("❌ Failed to load knowledge base data:", error);
      // Removed toast error to prevent UI spam
      console.error("Failed to load knowledge base data:", error);
      setStats((prev) => ({ ...prev, loading: false }));
    } finally {
      setIsLoading(false);
    }
  };

  const knowledgeBaseSections = [
    {
      id: "documents",
      title: "Documents Library",
      description: "Manage project documents and files",
      icon: <FileText className="h-6 w-6" />,
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-950",
      count: isLoading ? "..." : stats.totalDocuments.toString(),
      href: "/scopingai/knowledge-base/documents",
    },
    {
      id: "clients",
      title: "Client Management",
      description: "Store and organize client information",
      icon: <Users className="h-6 w-6" />,
      color: "text-green-600",
      bgColor: "bg-green-50 dark:bg-green-950",
      count: isLoading ? "..." : stats.activeClients.toString(),
      href: "/scopingai/knowledge-base/clients",
    },
    {
      id: "prompts",
      title: "AI Prompts",
      description: "Create and manage AI prompt templates",
      icon: <MessageSquare className="h-6 w-6" />,
      color: "text-purple-600",
      bgColor: "bg-purple-50 dark:bg-purple-950",
      count: "8", // This could be loaded from DB later
      href: "/scopingai/knowledge-base/prompts",
    },
    // {
    //   id: "vector-documents",
    //   title: "Vector Documents",
    //   description: "AI-powered document processing and chat",
    //   icon: <Brain className="h-6 w-6" />,
    //   color: "text-orange-600",
    //   bgColor: "bg-orange-50 dark:bg-orange-950",
    //   count: isLoading ? "..." : stats.vectorDocs.toString(),
    //   action: () => setActiveSection("vector-documents"),
    // },
  ];

  const quickActions = [
    {
      title: "Upload Documents",
      description: "Add new documents to knowledge base",
      icon: <Upload className="h-5 w-5" />,
      color: "text-blue-600",
      action: () => setActiveSection("vector-documents"),
    },
    {
      title: "Create Client",
      description: "Add a new client profile",
      icon: <Plus className="h-5 w-5" />,
      color: "text-green-600",
      action: () => navigate("/scopingai/knowledge-base/clients"),
    },
    {
      title: "New Prompt Template",
      description: "Create custom AI prompt",
      icon: <Sparkles className="h-5 w-5" />,
      color: "text-purple-600",
      action: () => navigate("/scopingai/knowledge-base/prompts"),
    },
    {
      title: "AI Chat",
      description: "Chat with your documents",
      icon: <Brain className="h-5 w-5" />,
      color: "text-orange-600",
      action: () => setActiveSection("vector-documents"),
    },
  ];

  const statsCards = [
    {
      title: "Total Documents",
      value: stats.loading ? "..." : stats.totalDocuments.toString(),
      change: "+12%",
      icon: <FileText className="h-5 w-5 text-blue-500" />,
      trend: "up",
      loading: stats.loading,
    },
    {
      title: "Active Clients",
      value: stats.loading ? "..." : stats.activeClients.toString(),
      change: "+8%",
      icon: <Users className="h-5 w-5 text-green-500" />,
      trend: "up",
      loading: stats.loading,
    },
    {
      title: "AI Interactions",
      value: stats.loading ? "..." : stats.aiInteractions.toString(),
      change: "+24%",
      icon: <Brain className="h-5 w-5 text-purple-500" />,
      trend: "up",
      loading: stats.loading,
    },
    {
      title: "Vector Docs",
      value: stats.loading ? "..." : stats.vectorDocs.toString(),
      change: "+18%",
      icon: <Database className="h-5 w-5 text-orange-500" />,
      trend: "up",
      loading: stats.loading,
    },
  ];

  // Generate recent activity from real document data
  const getRecentActivity = () => {
    if (documents.length === 0) return [];

    // Get the most recent documents (up to 4)
    const recentDocs = documents
      .sort(
        (a, b) =>
          new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
      )
      .slice(0, 4);

    return recentDocs.map((doc) => ({
      action: "Document uploaded",
      item: doc.name,
      time:
        new Date(doc.uploadedAt).toLocaleDateString() ===
        new Date().toLocaleDateString()
          ? "Today"
          : new Date(doc.uploadedAt).toLocaleDateString(),
      icon: <Upload className="h-4 w-4 text-blue-500" />,
    }));
  };

  if (activeSection === "vector-documents") {
    return (
      <ScopingAILayout>
        <div className="min-h-screen bg-background">
          {/* Header with back button */}
          <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  onClick={() => setActiveSection("overview")}
                  className="gap-2"
                >
                  <ArrowRight className="h-4 w-4 rotate-180" />
                  Back to Knowledge Base
                </Button>
                <div className="flex items-center gap-2">
                  <Brain className="h-6 w-6 text-orange-600" />
                  <h1 className="text-2xl font-bold">
                    Vector Documents & AI Assistant
                  </h1>
                </div>
                <Button
                  variant="outline"
                  onClick={loadKnowledgeBaseData}
                  disabled={isLoading}
                  className="ml-auto gap-2"
                >
                  <Database className="h-4 w-4" />
                  {isLoading ? "Loading..." : "Refresh Data"}
                </Button>
              </div>
            </div>
          </div>

          {/* AI Assistant Component */}
          <AIAssistant />
        </div>
      </ScopingAILayout>
    );
  }

  return (
    <ScopingAILayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
                Knowledge Base
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl">
                Centralized hub for managing your project resources, documents,
                clients, and AI-powered tools.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={loadKnowledgeBaseData}
              disabled={isLoading}
              className="gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Database className="h-4 w-4" />
              )}
              {isLoading ? "Loading..." : "Refresh Data"}
            </Button>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-8"
            >
              {/* Stats Cards */}
              {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {statsCards.map((stat, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {stat.title}
                        </p>
                        <p className="text-2xl font-bold flex items-center gap-2">
                          {stat.loading ? (
                            <Loader2 className="h-6 w-6 animate-spin" />
                          ) : (
                            stat.value
                          )}
                        </p>
                        <p
                          className={`text-xs ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}
                        >
                          {stat.change} from last month
                        </p>
                      </div>
                      <div className="p-2 bg-muted rounded-lg">{stat.icon}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div> */}

              {/* Main Knowledge Base Sections */}
              <div>
                <h2 className="text-xl font-semibold mb-6">
                  Knowledge Base Sections
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {knowledgeBaseSections.map((section) => (
                    <Card
                      key={section.id}
                      className="hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-primary/20"
                      onClick={
                        section.action ||
                        (() => section.href && navigate(section.href))
                      }
                    >
                      <CardHeader className="pb-4">
                        <div
                          className={`w-12 h-12 ${section.bgColor} rounded-lg flex items-center justify-center mb-4`}
                        >
                          <div className={section.color}>{section.icon}</div>
                        </div>
                        <CardTitle className="text-lg font-semibold">
                          {section.title}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {section.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <Badge variant="secondary">
                            {section.count}{" "}
                            {section.count === "1" ? "item" : "items"}
                          </Badge>
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </ScopingAILayout>
  );
}
