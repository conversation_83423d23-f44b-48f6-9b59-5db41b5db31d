import { Request, Response, NextFunction } from 'express';
import { environment } from '../config/environment';

/**
 * Middleware to validate API key in requests
 */
export const validateApiKey = (req: Request, res: Response, next: NextFunction): void | Response => {
  const apiKey = req.query.apiKey || req.headers['x-api-key'];
  
  if (!apiKey) {
    return res.status(401).json({ error: 'API key is required' });
  }
  
  // Check against a fixed API key instead of OpenRouter API key
  // This matches the frontend VITE_API_KEY
  if (apiKey !== 'scopingai') {
    return res.status(403).json({ error: 'Invalid API key' });
  }
  
  next();
}; 