import express from "express";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "./auth";
import { ragService } from "../../../features/knowledge/services/ragService";
import { langGraphService } from "../../../features/knowledge/services/langGraphService";
import { pineconeService } from "../../../features/knowledge/services/pineconeService";

// Helper function to extract user ID from request
function getUserIdFromRequest(req: express.Request): string {
  const userId =
    (req.headers["x-user-id"] as string) ||
    req.body.userId ||
    (req.query.userId as string) ||
    "anonymous";
  return userId;
}

const router = express.Router();

// Simple OpenAI-compatible chat endpoint
router.post("/openai", validateApiKey, async (req, res) => {
  try {
    const { messages, model = "gpt-4" } = req.body;
    const userId = getUserIdFromRequest(req);

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: "Messages array is required" });
    }

    // Get the latest user message
    const userMessage = messages[messages.length - 1];
    if (!userMessage || userMessage.role !== "user") {
      return res.status(400).json({ error: "Last message must be from user" });
    }

    // Use ragService to generate response with user ID
    const response = await ragService.generateResponse({
      message: userMessage.content,
      selectedDocuments: [],
      userId: userId,
    });

    // Return response in OpenAI-compatible format
    res.json({
      id: `chatcmpl-${Date.now()}`,
      object: "chat.completion",
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [
        {
          index: 0,
          message: {
            role: "assistant",
            content: response.message,
          },
          finish_reason: "stop",
        },
      ],
      usage: {
        prompt_tokens: userMessage.content.length,
        completion_tokens: response.message.length,
        total_tokens: userMessage.content.length + response.message.length,
      },
    });
  } catch (error) {
    console.error("CopilotKit error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Streaming chat endpoint
router.post("/stream", validateApiKey, async (req, res) => {
  try {
    const { message, selectedDocuments = [] } = req.body;
    const userId = getUserIdFromRequest(req);

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Set headers for streaming
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("Access-Control-Allow-Origin", "*");

    try {
      // Use LangGraph for RAG processing
      const ragResponse = await langGraphService.performRAG({
        query: message,
        selectedDocuments,
        systemMessage:
          "You are a helpful AI assistant with access to a comprehensive knowledge base.",
        temperature: 0.7,
        maxTokens: 2048,
      });

      // Stream the response
      const chunks = ragResponse.answer.split(" ");
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i] + " ";
        res.write(`data: ${JSON.stringify({ chunk })}\n\n`);
        await new Promise((resolve) => setTimeout(resolve, 50));
      }

      // Send completion
      res.write(
        `data: ${JSON.stringify({
          complete: true,
          message: ragResponse.answer,
          sources: ragResponse.sources,
          metadata: ragResponse.metadata,
        })}\n\n`
      );
    } catch (ragError) {
      console.error("RAG error:", ragError);
      res.write(
        `data: ${JSON.stringify({
          error: "Failed to process message with RAG system",
        })}\n\n`
      );
    }

    res.end();
  } catch (error) {
    console.error("Streaming error:", error);
    res.status(500).json({ error: "Failed to generate response" });
  }
});

// Knowledge base search endpoint
router.post("/search", async (req, res) => {
  try {
    const { query, maxResults = 10 }: { query: string; maxResults?: number } =
      req.body;

    if (!query) {
      return res.status(400).json({ error: "Query is required" });
    }

    const results = await langGraphService.searchDocuments(query, {
      topK: maxResults,
      minScore: 0.5,
    });

    res.json({
      results: results.map((result) => ({
        id: result.id,
        content: result.text,
        score: result.score,
        source: result.metadata?.filename || "Unknown Document",
      })),
      totalFound: results.length,
      provider: "LangGraph + Pinecone",
    });
  } catch (error) {
    console.error("Search error:", error);
    res.status(500).json({
      error: "Search failed",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// RAG chat endpoint
router.post("/rag", async (req, res) => {
  try {
    const {
      message,
      selectedDocuments = [],
    }: {
      message: string;
      selectedDocuments?: string[];
    } = req.body;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    const ragResponse = await langGraphService.performRAG({
      query: message,
      selectedDocuments,
      systemMessage:
        "You are a helpful AI assistant with access to a comprehensive knowledge base.",
      temperature: 0.7,
      maxTokens: 2048,
    });

    res.json({
      response: ragResponse.answer,
      sources: ragResponse.sources,
      metadata: ragResponse.metadata,
      provider: "LangGraph + Pinecone RAG",
    });
  } catch (error) {
    console.error("RAG chat error:", error);
    res.status(500).json({
      error: "RAG chat failed",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Health check for CopilotKit services
router.get("/health", async (req, res) => {
  try {
    const langGraphHealthy = await langGraphService.healthCheck();
    const pineconeHealthy = await pineconeService.healthCheck();

    const status = {
      status: "healthy",
      services: {
        langGraph: langGraphHealthy ? "healthy" : "unhealthy",
        pinecone: pineconeHealthy ? "healthy" : "unhealthy",
      },
      timestamp: new Date().toISOString(),
    };

    const overallHealthy = langGraphHealthy && pineconeHealthy;
    res.status(overallHealthy ? 200 : 503).json(status);
  } catch (error) {
    console.error("Health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
