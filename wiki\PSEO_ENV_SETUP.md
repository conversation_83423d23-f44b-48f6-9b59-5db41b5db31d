# pSEO Environment Configuration Guide

## Required Environment Variables

### 1. SEO Provider Configuration

Add these to your `.env` file to configure which providers to use for each SEO function:

```bash
# Provider Selection (choose one for each function)
SEO_GENERATOR_PROVIDER=lighthouse    # Options: lighthouse, screaming_frog
SEO_PAGESPEED_PROVIDER=gtmetrix     # Options: gtmetrix, webpagetest, lighthouse
SEO_BACKLINK_PROVIDER=semrush      # Options: semrush, ahrefs
SEO_KEYWORD_PROVIDER=ahrefs        # Options: ahrefs, semrush
```

### 2. API Keys for SEO Providers

#### Free Providers (No API Key Needed)
- **Lighthouse**: Built-in, no configuration needed

#### Premium Providers
```bash
# GTMetrix (Page Speed Analysis)
SEO_GTMETRIX_API_KEY=your-gtmetrix-api-key
SEO_GTMETRIX_USERNAME=your-gtmetrix-username

# WebPageTest (Alternative Page Speed Analysis)
SEO_WEBPAGETEST_API_KEY=your-webpagetest-api-key

# Semrush (Backlink & Keyword Analysis)
SEO_SEMRUSH_API_KEY=your-semrush-api-key

# Ahrefs (Backlink & Keyword Analysis)
SEO_AHREFS_API_KEY=your-ahrefs-api-key

# Screaming Frog (Technical SEO Analysis)
SEO_SCREAMING_FROG_API_KEY=your-screaming-frog-api-key
```

### 3. Optional Configuration

```bash
# Provider Confidence Scores (0.0 to 1.0)
SEO_PROVIDER_CONFIDENCE_LIGHTHOUSE=0.9
SEO_PROVIDER_CONFIDENCE_GTMETRIX=0.95
SEO_PROVIDER_CONFIDENCE_SEMRUSH=0.95
SEO_PROVIDER_CONFIDENCE_AHREFS=0.98
SEO_PROVIDER_CONFIDENCE_WEBPAGETEST=0.85
SEO_PROVIDER_CONFIDENCE_SCREAMING_FROG=0.92

# SEO Analysis Configuration
SEO_DEFAULT_ANALYSIS_DEPTH=comprehensive  # Options: basic, standard, comprehensive
SEO_ENABLE_KEYWORD_ANALYSIS=true
SEO_ENABLE_BACKLINK_ANALYSIS=true
SEO_ENABLE_COMPETITOR_ANALYSIS=false

# Performance Thresholds
SEO_MIN_SCORE_THRESHOLD=70
SEO_CRITICAL_ISSUE_THRESHOLD=5
SEO_MAX_PROCESSING_TIME=30000  # milliseconds
```

## Provider Information & Setup

### GTMetrix
1. Sign up at https://gtmetrix.com/
2. Go to Account Settings → API
3. Generate API key and get username
4. Add to `.env`: `SEO_GTMETRIX_API_KEY` and `SEO_GTMETRIX_USERNAME`

### Semrush
1. Sign up at https://www.semrush.com/
2. Subscribe to API access
3. Get API key from account settings
4. Add to `.env`: `SEO_SEMRUSH_API_KEY`

### Ahrefs
1. Sign up at https://ahrefs.com/
2. Subscribe to API access
3. Get API key from account settings
4. Add to `.env`: `SEO_AHREFS_API_KEY`

### WebPageTest
1. Sign up at https://www.webpagetest.org/
2. Request API key
3. Add to `.env`: `SEO_WEBPAGETEST_API_KEY`

## Database Schema Updates

The following fields have been added to support SEO analysis:

### PSEOAudit Table
```sql
-- Add these columns to your PSEOAudit table
ALTER TABLE pseo_audits ADD COLUMN html_analysis_result TEXT;
ALTER TABLE pseo_audits ADD COLUMN seo_score INTEGER;
ALTER TABLE pseo_audits ADD COLUMN seo_grade VARCHAR(2);
ALTER TABLE pseo_audits ADD COLUMN seo_scoring_result TEXT;
ALTER TABLE pseo_audits ADD COLUMN updated_at TIMESTAMP;
```

### PSEOAuditStep Table
```sql
-- Add these columns to your PSEOAuditStep table
ALTER TABLE pseo_audit_steps ADD COLUMN step_type VARCHAR(50);
ALTER TABLE pseo_audit_steps ADD COLUMN result TEXT;
ALTER TABLE pseo_audit_steps ADD COLUMN metadata TEXT;
```

## Configuration Examples

### Basic Setup (Free Providers Only)
```bash
SEO_GENERATOR_PROVIDER=lighthouse
SEO_PAGESPEED_PROVIDER=lighthouse
# No backlink/keyword analysis with free setup
```

### Premium Setup (Full Features)
```bash
SEO_GENERATOR_PROVIDER=lighthouse
SEO_PAGESPEED_PROVIDER=gtmetrix
SEO_BACKLINK_PROVIDER=ahrefs
SEO_KEYWORD_PROVIDER=ahrefs

SEO_GTMETRIX_API_KEY=your-gtmetrix-key
SEO_GTMETRIX_USERNAME=your-username
SEO_AHREFS_API_KEY=your-ahrefs-key
```

### Mixed Setup (Balanced Cost/Features)
```bash
SEO_GENERATOR_PROVIDER=lighthouse
SEO_PAGESPEED_PROVIDER=gtmetrix
SEO_BACKLINK_PROVIDER=semrush
SEO_KEYWORD_PROVIDER=semrush

SEO_GTMETRIX_API_KEY=your-gtmetrix-key
SEO_GTMETRIX_USERNAME=your-username
SEO_SEMRUSH_API_KEY=your-semrush-key
```

## Usage Instructions

### 1. Basic HTML Analysis (Find Exact Locations)
```typescript
import { seoIntegrationService } from './services/pseo/seoIntegrationService';

const locations = await seoIntegrationService.getExactIssueLocations(
  'https://example.com',
  ['meta', 'heading', 'image'] // Optional: filter by issue types
);

console.log(locations.issues); // Array with exact line numbers, CSS selectors, XPath
```

### 2. SEO Scoring with Providers
```typescript
const scoreBreakdown = await seoIntegrationService.getProviderScoreBreakdown(
  'https://example.com'
);

console.log(`Overall Score: ${scoreBreakdown.overallScore}/100`);
console.log(`Grade: ${scoreBreakdown.grade}`);
console.log('Provider Breakdown:', scoreBreakdown.providerBreakdown);
```

### 3. Comprehensive Analysis
```typescript
const analysis = await seoIntegrationService.performComprehensiveAnalysis({
  url: 'https://example.com',
  auditDepth: 'comprehensive',
  includeKeywordAnalysis: true,
  includeBacklinkAnalysis: true
});

console.log('HTML Issues with Locations:', analysis.htmlAnalysis.issues);
console.log('SEO Score:', analysis.seoScoring.overallScore);
console.log('Action Plan:', analysis.actionPlan);
```

### 4. Integration with Existing pSEO Workflow
```typescript
// Add SEO analysis to existing audit
const integration = await seoIntegrationService.integrateWithPSEOAudit(auditId);

console.log(`Added ${integration.exactLocationsAdded} issue locations to audit`);
```

## Troubleshooting

### Common Issues

1. **Missing API Keys**: Ensure all required API keys are set in `.env`
2. **Provider Errors**: Check API key validity and account limits
3. **Database Errors**: Run schema updates for new columns
4. **TypeScript Errors**: Ensure all interfaces are properly imported

### Validation
```typescript
import { seoConfig } from './config/seoConfig';

const validation = seoConfig.validateConfiguration();
if (!validation.isValid) {
  console.error('Configuration errors:', validation.errors);
}
```

## Cost Considerations

### Free Tier Limitations
- **Lighthouse**: Unlimited, but basic metrics only
- **GTMetrix**: 5 tests/month free
- **Semrush**: Limited API calls
- **Ahrefs**: No free tier for API

### Recommended Provider Combinations by Budget

#### Budget: $0/month
```bash
SEO_GENERATOR_PROVIDER=lighthouse
SEO_PAGESPEED_PROVIDER=lighthouse
# No backlink/keyword analysis
```

#### Budget: $50-100/month
```bash
SEO_GENERATOR_PROVIDER=lighthouse
SEO_PAGESPEED_PROVIDER=gtmetrix
SEO_BACKLINK_PROVIDER=semrush
SEO_KEYWORD_PROVIDER=semrush
```

#### Budget: $200+/month
```bash
SEO_GENERATOR_PROVIDER=screaming_frog
SEO_PAGESPEED_PROVIDER=gtmetrix
SEO_BACKLINK_PROVIDER=ahrefs
SEO_KEYWORD_PROVIDER=ahrefs
``` 