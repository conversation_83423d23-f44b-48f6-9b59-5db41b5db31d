import React, { useState, useEffect } from "react";
import Layout from "../../../components/Layout";
import { useDocuments } from "../../../hooks/useDocuments";
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from "../../../utils/constants";

const KnowledgeBase = () => {
  const {
    documents,
    isLoading,
    uploadDocument,
    deleteDocument,
    refreshDocuments,
    reprocessDocument,
  } = useDocuments();

  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState("");
  const [tags, setTags] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  useEffect(() => {
    refreshDocuments();
  }, [refreshDocuments]);

  // Drag and drop functionality
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      handleFileSelected(droppedFile);
    }
  };

  const handleFileSelected = (selectedFile: File) => {
    // Validate file type
    const fileExt = selectedFile.name.split(".").pop()?.toLowerCase() || "";
    if (!ALLOWED_FILE_TYPES.includes(`.${fileExt}`)) {
      setError(
        `Invalid file type. Allowed types are: ${ALLOWED_FILE_TYPES.join(", ")}`
      );
      return;
    }

    // Validate file size
    if (selectedFile.size > MAX_FILE_SIZE) {
      setError(
        `File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      );
      return;
    }

    setFile(selectedFile);

    // Set default title to filename without extension
    const fileName = selectedFile.name;
    const titleFromFileName =
      fileName.substring(0, fileName.lastIndexOf(".")) || fileName;
    setTitle(titleFromFileName);

    setError("");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelected(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setError("Please select a file");
      return;
    }

    try {
      setIsUploading(true);
      setError("");

      // Process tag string to array
      const tagArray = tags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag !== "");

      // Set a longer timeout for the fetch to complete
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      // Upload with metadata - remove any doc_id from metadata
      await uploadDocument(file, {
        title: title || file.name,
        tags: tagArray,
        created_at: new Date().toISOString(), // Add timestamp
      });

      // Clear the timeout
      clearTimeout(timeoutId);

      // Reset form
      setFile(null);
      setTitle("");
      setTags("");

      // Refresh document list
      await refreshDocuments();
    } catch (err) {
      console.error("Upload error:", err);

      // Check for specific error types
      if (err instanceof DOMException && err.name === "AbortError") {
        setError(
          "Upload timed out. The file might be too large or the server is busy."
        );
      } else if (
        err instanceof Error &&
        err.message.includes("message channel closed")
      ) {
        setError("Upload was interrupted. Please try again.");
      } else {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleReprocess = async (docId: string) => {
    try {
      await reprocessDocument(docId);
      await refreshDocuments();
    } catch (err) {
      console.error("Reprocess error:", err);
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    }
  };

  const handleDelete = async (id: string, title: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete "${title}"? This action cannot be undone.`
      )
    ) {
      try {
        await deleteDocument(id);
      } catch (err) {
        console.error("Delete error:", err);
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
      }
    }
  };

  // Filter documents based on search and tab
  const filteredDocuments = documents.filter((doc) => {
    // Filter by search query
    const matchesSearch =
      !searchQuery ||
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.tags &&
        doc.tags.some((tag) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        ));

    // Filter by tab
    if (activeTab === "all") return matchesSearch;
    if (activeTab === "success")
      return matchesSearch && doc.status === "success";
    if (activeTab === "processing")
      return matchesSearch && doc.status === "processing";
    if (activeTab === "error") return matchesSearch && doc.status === "error";

    return matchesSearch;
  });

  return (
    <Layout>
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Knowledge Base</h1>
            <p className="mt-1 text-gray-600">
              Upload and manage documents for your AI assistant
            </p>
          </div>
          <button
            onClick={refreshDocuments}
            className="px-4 py-2 bg-indigo-50 text-indigo-700 rounded-md hover:bg-indigo-100 transition-colors"
          >
            Refresh Documents
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Add to Knowledge Base
              </h2>

              {/* Drag & Drop Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center text-center mb-4 hover:border-indigo-300 transition-colors"
                onDragOver={handleDrag}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDrop={handleDrop}
              >
                <div className="w-16 h-16 rounded-full bg-indigo-50 flex items-center justify-center mb-4">
                  <svg
                    className="w-8 h-8 text-indigo-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                </div>
                {file ? (
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                    <button
                      onClick={() => setFile(null)}
                      className="text-xs text-red-600 mt-2"
                    >
                      Remove
                    </button>
                  </div>
                ) : (
                  <>
                    <p className="text-sm font-medium text-gray-900">
                      Drop your file here, or
                    </p>
                    <label className="mt-2 cursor-pointer">
                      <span className="px-3 py-1.5 text-sm bg-indigo-50 text-indigo-700 rounded-md hover:bg-indigo-100 transition-colors">
                        Browse files
                      </span>
                      <input
                        type="file"
                        className="hidden"
                        onChange={handleFileChange}
                        accept={ALLOWED_FILE_TYPES.join(",")}
                      />
                    </label>
                    <p className="text-xs text-gray-500 mt-2">
                      Supports {ALLOWED_FILE_TYPES.join(", ")} up to{" "}
                      {MAX_FILE_SIZE / (1024 * 1024)}MB
                    </p>
                  </>
                )}
              </div>

              {/* Form Fields */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Document Title
                  </label>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter a title for this document"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    disabled={isUploading}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tags (comma separated)
                  </label>
                  <input
                    type="text"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    placeholder="e.g. marketing, strategy, 2023"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    disabled={isUploading}
                  />
                </div>

                {error && (
                  <div className="text-sm text-red-600 p-2 bg-red-50 rounded-md">
                    {error}
                  </div>
                )}

                <button
                  onClick={handleUpload}
                  disabled={!file || isUploading}
                  className={`w-full py-2 px-4 rounded-md text-sm font-medium ${
                    !file || isUploading
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-indigo-600 text-white hover:bg-indigo-700"
                  } transition-colors`}
                >
                  {isUploading ? (
                    <div className="flex items-center justify-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Uploading...
                    </div>
                  ) : (
                    "Add to Knowledge Base"
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Document List Panel */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">
                  Documents ({documents.length})
                </h2>

                {/* Search */}
                <div className="w-full sm:w-64 mt-2 sm:mt-0">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search documents..."
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Tabs */}
              <div className="flex border-b border-gray-200 mb-4">
                <button
                  className={`px-4 py-2 text-sm font-medium ${
                    activeTab === "all"
                      ? "text-indigo-600 border-b-2 border-indigo-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                  onClick={() => setActiveTab("all")}
                >
                  All
                </button>
                <button
                  className={`px-4 py-2 text-sm font-medium ${
                    activeTab === "success"
                      ? "text-indigo-600 border-b-2 border-indigo-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                  onClick={() => setActiveTab("success")}
                >
                  Processed
                </button>
                <button
                  className={`px-4 py-2 text-sm font-medium ${
                    activeTab === "processing"
                      ? "text-indigo-600 border-b-2 border-indigo-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                  onClick={() => setActiveTab("processing")}
                >
                  Processing
                </button>
                <button
                  className={`px-4 py-2 text-sm font-medium ${
                    activeTab === "error"
                      ? "text-indigo-600 border-b-2 border-indigo-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                  onClick={() => setActiveTab("error")}
                >
                  Failed
                </button>
              </div>

              {/* Document List */}
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600"></div>
                  <p className="mt-2 text-sm text-gray-500">
                    Loading documents...
                  </p>
                </div>
              ) : filteredDocuments.length === 0 ? (
                <div className="text-center py-12 border-2 border-dashed border-gray-200 rounded-lg">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No documents found
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchQuery
                      ? "Try adjusting your search terms."
                      : "Upload your first document to get started."}
                  </p>
                </div>
              ) : (
                <ul className="divide-y divide-gray-200">
                  {filteredDocuments.map((doc) => (
                    <li
                      key={
                        doc.id ||
                        `doc-${Math.random().toString(36).substr(2, 9)}`
                      }
                      className="py-4"
                    >
                      <div className="flex flex-col sm:flex-row justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center">
                            <h3 className="text-sm font-medium text-gray-900 truncate">
                              {doc.title}
                            </h3>

                            {/* Status badge */}
                            <span
                              className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                doc.status === "success"
                                  ? "bg-green-100 text-green-800"
                                  : doc.status === "processing"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {doc.status === "success"
                                ? "Processed"
                                : doc.status === "processing"
                                ? "Processing"
                                : "Failed"}
                            </span>
                          </div>

                          {/* File info */}
                          <div className="mt-1 flex items-center">
                            <svg
                              className="w-3 h-3 text-gray-400 mr-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                              />
                            </svg>
                            <span className="text-xs text-gray-500 truncate">
                              {doc.type || "Unknown file"}
                            </span>
                          </div>

                          {/* Chunks info */}
                          {doc.chunk_count !== undefined &&
                            doc.status === "success" && (
                              <div className="mt-1 flex items-center">
                                <svg
                                  className="w-3 h-3 text-gray-400 mr-1"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
                                  />
                                </svg>
                                <span className="text-xs text-gray-500">
                                  {doc.chunk_count} chunks indexed
                                </span>
                              </div>
                            )}

                          {/* Created date */}
                          {doc.created_at && (
                            <div className="mt-1 flex items-center">
                              <svg
                                className="w-3 h-3 text-gray-400 mr-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                              </svg>
                              <span className="text-xs text-gray-500">
                                {new Date(doc.created_at).toLocaleDateString()}
                              </span>
                            </div>
                          )}

                          {/* Tags */}
                          {doc.tags && doc.tags.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-1.5">
                              {doc.tags.map((tag, index) => (
                                <span
                                  key={`${doc.id}-tag-${index}`}
                                  className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}

                          {/* Error message */}
                          {doc.status === "error" && doc.error && (
                            <div className="mt-2 text-xs text-red-600">
                              Error: {doc.error}
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="mt-2 sm:mt-0 flex flex-shrink-0 space-x-2">
                          {doc.status === "error" && (
                            <button
                              onClick={() => handleReprocess(doc.id)}
                              className="px-2 py-1 text-xs text-indigo-700 bg-indigo-50 hover:bg-indigo-100 rounded"
                              title="Try processing again"
                            >
                              Retry Processing
                            </button>
                          )}

                          {doc.status === "success" && (
                            <button
                              onClick={() => handleReprocess(doc.id)}
                              className="px-2 py-1 text-xs text-indigo-700 bg-indigo-50 hover:bg-indigo-100 rounded"
                              title="Process document again"
                            >
                              Reprocess
                            </button>
                          )}

                          <button
                            onClick={() => handleDelete(doc.id, doc.title)}
                            className="px-2 py-1 text-xs text-red-700 bg-red-50 hover:bg-red-100 rounded"
                            title="Delete document"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default KnowledgeBase;
