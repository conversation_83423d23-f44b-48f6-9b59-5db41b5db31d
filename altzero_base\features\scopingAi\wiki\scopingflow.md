# 📋 ScopingAI Document Generation Flow - Technical Wiki

## 🎯 Overview

The ScopingAI system is a sophisticated AI-powered document generation platform that creates comprehensive scoping documents and proposals. It integrates with a knowledge base (Pinecone), uses OpenAI for content generation, and provides real-time streaming updates during the generation process.

## 🏗️ Architecture Overview

```
Frontend (React/TypeScript) → Backend API (Node.js/Express) → AI Services (OpenAI + Pinecone)
                            ↓
                    Supabase Database + File Storage
```

### Key Components:
- **Frontend**: Multi-step form with real-time generation feedback
- **Backend**: Streaming API with AI orchestration
- **Knowledge Base**: Pinecone vector database for document retrieval
- **AI Engine**: OpenAI GPT for content generation
- **Storage**: Supabase for persistence, session storage for real-time state

## 📁 File Flow & Code Execution Path

### 🎯 **Complete File Execution Flow**

```
USER CLICKS "Generate Document"
         ↓
📱 FRONTEND FLOW:
         ↓
1. DocumentOverviewStep.tsx
   └── handleGenerateWithPrompt()
   └── calls handleGenerate()
         ↓
2. DocumentFormContext.tsx
   └── handleGenerate() function
   └── calls generateDocument()
         ↓
3. utils/documentGeneration.ts
   └── Re-exports from services
         ↓
4. services/documentService.ts
   └── generateDocument() function
   └── Prepares API request
   └── Creates streaming connection
         ↓
🌐 API CALL:
   POST /api/scopingai/proposals/stream
         ↓
🖥️ BACKEND FLOW (ALL IN ONE FILE):
         ↓
5-8. server/features/scopingAi/routes/scopingAi.ts
   ├── POST "/proposals/stream" endpoint (lines 689-691)
   ├── validateApiKey middleware
   ├── SSE headers setup (lines 707-711)
   ├── Knowledge Base Integration (lines 732-822):
   │   └── pineconeService.getDocumentsByIds()
   │   └── Reference document structuring
   ├── AI Processing (lines 825-1047):
   │   ├── Research generation (OpenAI call)
   │   ├── Summary creation (OpenAI call)
   │   └── Section-by-section generation (OpenAI calls)
   └── Streaming Response (throughout):
       └── Server-Sent Events (SSE) to frontend
         ↓
📱 FRONTEND RESPONSE HANDLING:
         ↓
9. services/documentService.ts
   └── processStream() function
   └── Handles streaming events
   └── Updates session storage
         ↓
10. DocumentFormContext.tsx
    └── Generation callbacks
    └── UI state updates
         ↓
11. DocumentOverviewStep.tsx
    └── Progress indicators
    └── Completion handling
```

### 📂 **Detailed File-by-File Breakdown**

#### **Frontend Files (User Interaction → API Call)**

**1. Entry Point & Navigation**
```
features/scopingAi/src/pages/Scoping.tsx
└── Landing page with "Create Document" link
└── Routes to: /scoping/create
```

**2. Main Document Creation Flow**
```
features/scopingAi/src/pages/DocumentsNew.tsx
├── DocumentFormProvider wrapper
├── 4-step stepper interface
├── Step routing logic
└── Generation progress modal
```

**3. Step Components (Data Collection)**
```
features/scopingAi/src/components/
├── ClientInfoStep.tsx          → Step 1: Client & project info
├── TemplateSelectionStep.tsx   → Step 2: Document template
├── RequirementsStep.tsx        → Step 3: Requirements & knowledge docs
└── DocumentOverviewStep.tsx    → Step 4: Review & generate
```

**4. Form State Management**
```
features/scopingAi/src/contexts/DocumentFormContext.tsx
├── Multi-step form state
├── Client data management
├── Template selection logic
├── handleGenerate() - Main generation trigger
└── Generation callbacks & progress tracking
```

**5. Document Generation Service**
```
features/scopingAi/src/utils/documentGeneration.ts
└── Re-exports from services layer

features/scopingAi/src/services/documentService.ts
├── generateDocument() - Main generation function
├── API request preparation
├── Streaming connection setup
├── Event processing logic
└── Session storage management
```

#### **Backend Files (API Processing → AI Generation)**

**6. Single Backend File - Complete Processing**
```
🎯 server/features/scopingAi/routes/scopingAi.ts
│
├── 📡 API Endpoint Setup (lines 689-719)
│   ├── POST /proposals/stream endpoint
│   ├── validateApiKey middleware
│   ├── Request data extraction
│   └── SSE headers setup
│
├── 📚 Knowledge Base Integration (lines 732-822)
│   ├── pineconeService.getDocumentsByIds()
│   ├── Reference document structuring
│   ├── Knowledge base content formatting
│   └── Resource summary generation
│
├── 🤖 AI Processing Pipeline (lines 825-1047)
│   ├── Research Phase (lines 825-887)
│   │   ├── Research prompt construction
│   │   ├── OpenAI API call
│   │   └── Research content generation
│   ├── Summary Phase (lines 889-944)
│   │   ├── Executive summary prompt
│   │   ├── OpenAI API call
│   │   └── Summary content generation
│   └── Section Generation (lines 946-1047)
│       ├── Template section iteration
│       ├── Section-specific prompts
│       ├── OpenAI API calls per section
│       └── Section content generation
│
└── 📡 Response Streaming (throughout 714-1105)
    ├── Server-Sent Events setup
    ├── Event streaming per phase:
    │   ├── "started" event (line 714)
    │   ├── "research" events (lines 722, 880)
    │   ├── "summary" events (lines 890, 938)
    │   ├── "section" events (lines 956, 1038)
    │   ├── "completed" event (line 1052)
    │   └── "end" event (line 1099)
    └── Error handling & stream cleanup
```

**⚠️ Important Note**: The entire backend processing (steps 5-8 from the flow diagram) happens within this **single 1,119-line file**. This includes:
- API endpoint handling
- Knowledge base retrieval
- All AI processing (research, summary, sections)
- Real-time streaming responses
- Error handling

#### **Response Processing (Backend → Frontend)**

**10. Frontend Stream Processing**
```
features/scopingAi/src/services/documentService.ts (lines 215-420)
├── ReadableStream processing
├── Event parsing & routing
├── Session storage updates
├── Callback execution
└── UI state synchronization
```

**11. UI Updates & Progress**
```
features/scopingAi/src/contexts/DocumentFormContext.tsx (callbacks)
├── onStarted() - Initialize progress
├── onResearch() - Research phase updates
├── onSummary() - Summary phase updates
├── onSection() - Section completion updates
├── onCompleted() - Final document ready
└── onEnd() - Generation complete

features/scopingAi/src/pages/DocumentsNew.tsx (lines 129-167)
├── Generation progress modal
├── Progress bar updates
├── Status message display
└── Cancel functionality
```

### 🔄 **Data Flow Through Files**

**Input Data Journey:**
```
1. ClientInfoStep.tsx → clientData, projectData
2. TemplateSelectionStep.tsx → selectedTemplate
3. RequirementsStep.tsx → requirements, selectedKnowledgeDocuments
4. DocumentOverviewStep.tsx → aiPrompts, final review
5. DocumentFormContext.tsx → Aggregates all data
6. documentService.ts → Structures API request
7. scopingAi.ts (backend) → Processes & validates
8. OpenAI API → Generates content
9. Response streams back through same path
```

**State Management Flow:**
```
DocumentFormContext.tsx (React Context)
         ↓
Session Storage (Real-time persistence)
         ↓
Supabase Database (Final persistence)
         ↓
File Storage (Document content)
```

### 🎯 **Key Integration Points**

**Knowledge Base Integration:**
```
RequirementsStep.tsx → selects documents
         ↓
documentService.ts → includes document IDs
         ↓
scopingAi.ts → pineconeService.getDocumentsByIds()
         ↓
OpenAI prompts → enhanced with reference content
```

**Real-time Communication:**
```
scopingAi.ts → Server-Sent Events
         ↓
documentService.ts → Stream processing
         ↓
DocumentFormContext.tsx → State updates
         ↓
UI Components → Progress display
```

## 📱 User Journey & Interface Flow

### Entry Points
- **Main Dashboard**: `/scoping` - Landing page with tool selection
- **Document Creation**: `/scoping/create` - Primary generation flow

### 4-Step Generation Process

#### Step 1: Client Information (`ClientInfoStep.tsx`)
**Purpose**: Collect client and project details
**Data Collected**:
```typescript
{
  client: {
    id: string,
    name: string,
    industry: string,
    contactPerson: string,
    email: string,
    phone: string
  },
  project: {
    title: string,
    description: string
  }
}
```

#### Step 2: Template Selection (`TemplateSelectionStep.tsx`)
**Purpose**: Choose document structure and format
**Options**:
- Predefined templates with section structures
- Custom template based on reference documents
- Template sections array (e.g., ["Introduction", "Scope", "Timeline", "Budget"])

#### Step 3: Requirements & Resources (`RequirementsStep.tsx`)
**Purpose**: Define project requirements and select knowledge base documents
**Data Collected**:
```typescript
{
  requirements: {
    description: string,
    selectedKnowledgeDocuments: string[] // Knowledge base document IDs
  },
  resources: any[] // Additional files/resources
}
```

#### Step 4: Document Overview (`DocumentOverviewStep.tsx`)
**Purpose**: Review all information and trigger generation
**Actions**:
- Review collected data
- Configure AI prompts and style preferences
- Initiate document generation process

## 🔄 Data Flow Architecture

### Frontend Processing Chain

#### 1. Form Context Management
**File**: `contexts/DocumentFormContext.tsx`
**Responsibilities**:
- Manages multi-step form state
- Handles client data persistence
- Coordinates template and prompt selection
- Orchestrates generation process

#### 2. Document Generation Service
**File**: `services/documentService.ts`
**Key Function**: `generateDocument()`

**Process Flow**:
```typescript
1. Validation → 2. ID Generation → 3. API Preparation → 4. Streaming Connection → 5. Event Processing
```

**Detailed Steps**:
1. **Validation**: Check required fields (title, client name)
2. **Document ID Generation**: `doc_${timestamp}_${random}`
3. **Knowledge Base Integration**: Fetch selected documents
4. **API Request**: POST to `/api/scopingai/proposals/stream`
5. **Stream Processing**: Handle real-time events and UI updates

### Backend API Architecture

#### Main Endpoint
**File**: `server/features/scopingAi/routes/scopingAi.ts`
**Endpoint**: `POST /api/scopingai/proposals/stream`
**Authentication**: `x-api-key: scopingai`

#### Request Processing Pipeline

**Phase 1: Knowledge Base Retrieval**
```typescript
if (selectedKnowledgeDocuments && selectedKnowledgeDocuments.length > 0) {
  const retrievedDocs = await pineconeService.getDocumentsByIds(
    selectedKnowledgeDocuments,
    userId
  );
  // Structure reference materials for AI context
}
```

**Phase 2: Research Generation**
- **AI Model**: OpenAI GPT (configurable, default: gpt-3.5-turbo)
- **Context**: Client info + project requirements + knowledge base content
- **Output**: Comprehensive research analysis and industry insights

**Phase 3: Executive Summary Creation**
- **Input**: Research findings + client objectives
- **Processing**: AI generates executive-level summary
- **Focus**: Value proposition and business impact

**Phase 4: Section-by-Section Generation**
- **Iteration**: Through template sections array
- **Context**: Research + summary + knowledge base + section-specific requirements
- **Output**: Detailed content for each section

## 📡 Real-Time Communication

### Server-Sent Events (SSE) Protocol

**Event Types & Sequence**:
```typescript
1. "started"    → Generation begins
2. "research"   → Research phase (started/completed)
3. "summary"    → Executive summary (started/completed)
4. "section"    → Each section (started/completed)
5. "completed"  → Final document ready
6. "end"        → Stream complete
```

### Event Data Structures

**Research Event**:
```typescript
{
  status: "started" | "completed",
  message: string,
  content?: string,
  referenceDocumentsUsed?: number
}
```

**Section Event**:
```typescript
{
  status: "started" | "completed",
  title: string,
  section?: {
    title: string,
    content: string
  }
}
```

**Completion Event**:
```typescript
{
  proposal: {
    id: string,
    type: "client_proposal",
    clientName: string,
    sections: Array<{title: string, content: string}>,
    summary: string,
    research: string,
    metadata: {
      generatedBy: string,
      totalSections: number,
      referenceDocumentsUsed: number,
      hasKnowledgeBase: boolean,
      proposalQuality: string,
      aiModel: string,
      generationTimestamp: string
    }
  }
}
```

## 🧠 AI Integration & Processing

### OpenAI Configuration
- **Model**: `gpt-3.5-turbo` (environment configurable)
- **Temperature**: 0.7 (balanced creativity/consistency)
- **Max Tokens**: 
  - Research: 1500-3000 (increased with knowledge base)
  - Summary: 1500
  - Sections: 1500-2000 (increased with knowledge base)

### Knowledge Base Integration (Pinecone)

**Service**: `pineconeService.getDocumentsByIds()`
**Purpose**: Retrieve relevant documents for AI context
**Filtering**: By userId for organization-level access
**Processing**: 
```typescript
// Structure reference materials
const referenceDocuments = retrievedDocs.map(doc => ({
  id: doc.id,
  title: doc.metadata?.fileName || `Reference Document ${index + 1}`,
  type: doc.metadata?.fileType || "document",
  content: doc.content,
  size: doc.metadata?.fileSize
}));
```

### AI Prompt Engineering & Sequential Execution

**🔄 Sequential Prompt Execution (NOT Combined)**:
The system executes **3 separate AI calls** in sequence, with each building on the previous:

```
1. RESEARCH PROMPT → OpenAI → researchContent
                              ↓
2. SUMMARY PROMPT (+ researchContent) → OpenAI → summaryContent
                              ↓
3. SECTION PROMPTS (+ researchContent + summaryContent) → OpenAI → sectionContent
```

**📍 Prompt Locations & Input Sources**:

**1. Research Prompt (Lines 825-868)**:
```typescript
// Inputs: client, project, requirements, knowledgeBaseContent, aiPrompts.additionalInstructions ONLY
const researchPrompt = `You are a senior business analyst...
CLIENT & PROJECT OVERVIEW:
- Client Name: ${client?.name}
- Industry: ${client?.industry}
- Project Description: ${project?.description}
- Project Requirements: ${JSON.stringify(requirements)}

${knowledgeBaseContent ? `REFERENCE MATERIALS: ${knowledgeBaseContent}` : ""}

Additional Context: ${aiPrompts?.additionalInstructions || ""}`;  // ← ONLY this from aiPrompts
```

**2. Summary Prompt (Lines 897-928)**:
```typescript
// Inputs: client, project, researchContent, knowledgeBaseContent (NO aiPrompts used)
const summaryPrompt = `Create a compelling executive summary...
CLIENT INFORMATION: ${client?.name}, ${client?.industry}, ${project?.description}
RESEARCH FOUNDATION: ${researchContent}  // ← Previous AI response
${knowledgeBaseContent ? `REFERENCE MATERIALS: ${resourceSummary}` : ""}`;
// ❌ NO aiPrompts used in summary generation
```

**3. Section Prompts (Lines 963-1021) - Loop for each section**:
```typescript
// Inputs: sectionTitle, client, project, researchContent, summaryContent, knowledgeBaseContent, ALL aiPrompts
const sectionPrompt = `You are a senior proposal writer creating "${sectionTitle}"...
CLIENT CONTEXT: ${client?.name}, ${client?.industry}, ${project?.description}
RESEARCH FOUNDATION: ${researchContent}     // ← From step 1
EXECUTIVE SUMMARY: ${summaryContent}        // ← From step 2
${knowledgeBaseContent ? `REFERENCE MATERIALS: ${knowledgeBaseContent}` : ""}

STYLE PREFERENCES: ${aiPrompts?.styleGuidance || "Professional business proposal style"}
CONTENT FOCUS: ${aiPrompts?.contentFocus || "Client value and business impact"}
ADDITIONAL INSTRUCTIONS: ${aiPrompts?.additionalInstructions || ""}`;
// ✅ ALL aiPrompts fields used in section generation
```

**🎯 Content Accumulation Pattern**:
- **Research**: Base client/project data + knowledge base + `aiPrompts.additionalInstructions` only
- **Summary**: Research results + client/project data + knowledge base (NO aiPrompts)
- **Sections**: Research + Summary + client/project data + knowledge base + ALL aiPrompts (styleGuidance, contentFocus, additionalInstructions)

**📋 aiPrompts Usage Summary**:
```
Research Prompt:  ✅ additionalInstructions  ❌ styleGuidance  ❌ contentFocus
Summary Prompt:   ❌ additionalInstructions  ❌ styleGuidance  ❌ contentFocus
Section Prompts:  ✅ additionalInstructions  ✅ styleGuidance  ✅ contentFocus
```

**📊 Input Data Sources**:
```typescript
// From Frontend (req.body):
client: {name, industry, contactPerson, email, phone}           // Step 1
project: {title, description}                                   // Step 1
template: {sections: string[]}                                  // Step 2 ← DRIVES SECTION GENERATION
requirements: {description, selectedKnowledgeDocuments}        // Step 3
aiPrompts: {styleGuidance, contentFocus, additionalInstructions} // Step 4

// From Backend Processing:
knowledgeBaseContent: string    // Fetched from Pinecone via selectedKnowledgeDocuments
researchContent: string         // Result from research prompt
summaryContent: string          // Result from summary prompt
```

## 🎯 **How Selected Template is Used**

### **Template Structure & Selection (Frontend)**

**Template Definition** (`TemplateSelectionStep.tsx`):
```typescript
const templates: TemplateSection[] = [
  {
    id: "standard",
    name: "Standard Scope",
    sections: ["Project Overview", "Objectives", "Deliverables", "Timeline", "Budget", "Methodology"]
  },
  {
    id: "agile",
    name: "Agile Project",
    sections: ["Project Vision", "User Stories", "Sprint Planning", "Acceptance Criteria", "Team Structure", "Budget"]
  },
  {
    id: "minimal",
    name: "Minimal Scope",
    sections: ["Overview", "Deliverables", "Timeline", "Budget"]
  },
  {
    id: "custom",
    name: "Custom Template",
    sections: ["Custom sections"] // OR sections from reference document
  }
];
```

**Template Usage Flow**:
```
1. User selects template in Step 2 → template.sections array
2. Backend receives template.sections in req.body
3. Backend extracts: const templateSections = template?.sections || ["Introduction", "Scope", "Timeline", "Budget"]
4. Backend loops through each section to generate content
```

### **Backend Template Processing** (`scopingAi.ts` lines 946-955)

**Section Extraction**:
```typescript
// Lines 947-952: Extract sections from selected template
const templateSections = template?.sections || [
  "Introduction",    // Default fallback sections
  "Scope",
  "Timeline",
  "Budget",
];
const generatedSections: any[] = [];
```

**Section Loop Processing** (lines 955-1047):
```typescript
// Loop through each section from the template
for (const sectionTitle of templateSections) {
  // 1. Stream "section started" event
  res.write(`event: section\ndata: ${JSON.stringify({
    status: "started",
    title: sectionTitle
  })}\n\n`);

  // 2. Build section-specific prompt
  const sectionPrompt = `You are creating "${sectionTitle}" section...
    CLIENT CONTEXT: ${client}
    RESEARCH: ${researchContent}
    SUMMARY: ${summaryContent}
    SECTION REQUIREMENTS: ${getSectionRequirements(sectionTitle, hasKnowledgeBase)}
    STYLE: ${aiPrompts?.styleGuidance}`;

  // 3. Generate content with OpenAI
  const sectionResponse = await openai.chat.completions.create({
    messages: [{ role: "user", content: sectionPrompt }],
    model: "gpt-3.5-turbo"
  });

  // 4. Store generated section
  generatedSections.push({
    title: sectionTitle,
    content: sectionResponse.choices[0].message.content
  });

  // 5. Stream "section completed" event
  res.write(`event: section\ndata: ${JSON.stringify({
    status: "completed",
    section: { title: sectionTitle, content: sectionContent }
  })}\n\n`);
}
```

### **Section-Specific Requirements** (`getSectionRequirements()` function)

**Predefined Section Logic** (lines 90-192):
```typescript
function getSectionRequirements(sectionTitle: string, hasReferenceData: boolean): string {
  const requirements: Record<string, string> = {
    Introduction: `
      - Company background and credentials
      - Understanding of client's business and challenges
      - Project overview and objectives
      - Value proposition and unique differentiators
      ${hasReferenceData ? "- Reference to relevant case studies" : ""}`,

    Scope: `
      - Detailed project scope and deliverables
      - Work breakdown structure
      - Inclusions and exclusions
      - Success criteria and acceptance criteria
      ${hasReferenceData ? "- Reference to similar project scopes" : ""}`,

    Timeline: `
      - Project phases and milestones
      - Detailed timeline with key dates
      - Critical path analysis
      - Risk mitigation for timeline delays
      ${hasReferenceData ? "- Benchmarking against similar timelines" : ""}`,

    Budget: `
      - Comprehensive cost breakdown
      - Resource costs and allocation
      - Payment terms and schedule
      - Cost justification and ROI analysis
      ${hasReferenceData ? "- Cost validation using reference data" : ""}`,

    // ... more predefined sections
  };

  // Fallback for custom sections not in predefined list
  return requirements[sectionTitle] || `
    - Section-specific content relevant to "${sectionTitle}"
    - Professional presentation with clear structure
    - Client-focused value proposition
    - Actionable recommendations and next steps
    ${hasReferenceData ? "- Integration of relevant reference materials" : ""}`;
}
```

### **Template Impact on Final Document**

**Final Document Structure**:
```typescript
// Lines 1055-1068: Final document assembly
const proposal = {
  id: documentId,
  type: "client_proposal",
  clientName: client?.name,
  sections: generatedSections,  // ← Sections generated from template
  summary: summaryContent,
  research: researchContent,
  metadata: {
    totalSections: generatedSections.length,  // ← Count from template
    // ...
  }
};
```

**Key Points**:
- **Template drives structure**: `template.sections` array determines which sections are generated
- **Section order matters**: Sections are generated in the order specified in the template
- **Custom sections supported**: Any section name can be used, with fallback requirements
- **Knowledge base enhancement**: Each section gets enhanced prompts when knowledge base is available
- **Flexible templates**: Users can select predefined templates or create custom ones

## 📋 **How Requirements Entry is Used**

### **Requirements Collection (Frontend - Step 3)**

**Requirements Options** (`RequirementsStep.tsx`):
```typescript
// Three ways to provide requirements:
const requirementOptions = [
  {
    id: "requirements",     // Manual text entry
    title: "Manual Entry",
    description: "Type your requirements directly"
  },
  {
    id: "upload",          // File upload (not fully implemented)
    title: "Upload Documents",
    description: "Upload requirement files"
  },
  {
    id: "knowledge",       // Knowledge base selection
    title: "Knowledge Base",
    description: "Select from vector documents"
  }
];
```

**Requirements Data Structure**:
```typescript
// From DocumentFormContext.tsx
const [requirementsOption, setRequirementsOption] = useState("requirements");
const [requirementsText, setRequirementsText] = useState("");
const [selectedKnowledgeDocuments, setSelectedKnowledgeDocuments] = useState<string[]>([]);

// Sent to backend as:
requirements: {
  description: requirementsText,           // Manual text input
  selectedKnowledgeDocuments: string[]    // Knowledge base document IDs
}
```

## � **How Requirements Entry is Used**

### **Requirements Collection (Frontend - Step 3)**

**Requirements Options** (`RequirementsStep.tsx`):
```typescript
// Three ways to provide requirements:
const requirementOptions = [
  {
    id: "requirements",     // Manual text entry
    title: "Manual Entry",
    description: "Type your requirements directly"
  },
  {
    id: "upload",          // File upload (not fully implemented)
    title: "Upload Documents",
    description: "Upload requirement files"
  },
  {
    id: "knowledge",       // Knowledge base selection
    title: "Knowledge Base",
    description: "Select from vector documents"
  }
];
```

**Requirements Data Structure**:
```typescript
// From DocumentFormContext.tsx
const [requirementsOption, setRequirementsOption] = useState("requirements");
const [requirementsText, setRequirementsText] = useState("");
const [selectedKnowledgeDocuments, setSelectedKnowledgeDocuments] = useState<string[]>([]);

// Sent to backend as:
requirements: {
  description: requirementsText,           // Manual text input
  selectedKnowledgeDocuments: string[]    // Knowledge base document IDs
}
```

### **Requirements Processing (Backend)**

**Data Extraction** (`scopingAi.ts` line 698):
```typescript
const { requirements } = req.body;
// requirements = {
//   description: "User's manual requirements text",
//   selectedKnowledgeDocuments: ["doc1", "doc2", "doc3"]
// }
```

**Requirements Usage in AI Prompts**:

**1. Research Prompt (Line 831)**:
```typescript
const researchPrompt = `...
CLIENT & PROJECT OVERVIEW:
- Client Name: ${client?.name || "Unknown"}
- Industry: ${client?.industry || "Unknown"}
- Project Description: ${project?.description || ""}
- Project Requirements: ${JSON.stringify(requirements || {})}  // ← DIRECT INCLUSION
...`;
```

**2. Knowledge Base Integration** (Lines 732-822):
```typescript
// If selectedKnowledgeDocuments provided in requirements
if (selectedKnowledgeDocuments && selectedKnowledgeDocuments.length > 0) {
  const retrievedDocs = await pineconeService.getDocumentsByIds(
    selectedKnowledgeDocuments,  // ← From requirements.selectedKnowledgeDocuments
    userId
  );
  // Process retrieved documents into knowledgeBaseContent
}
```

**3. Enhanced Prompts with Knowledge Base**:
```typescript
// When knowledge base documents are selected, prompts get enhanced:
const researchPrompt = `...
REFERENCE MATERIALS & RESOURCES:
${resourceSummary}

DETAILED REFERENCE CONTENT:
${knowledgeBaseContent}  // ← Content from requirements.selectedKnowledgeDocuments

INSTRUCTIONS FOR REFERENCE MATERIAL USAGE:
- Analyze the reference documents for relevant methodologies
- Extract case studies and proven approaches
- Identify industry-specific insights and compliance requirements
- Note relevant cost models, timelines, or resource requirements
...`;
```

### **Requirements Validation & Flow**

**Frontend Validation** (`DocumentFormContext.tsx`):
```typescript
// Validation before generation
if (requirementsOption === "requirements" && !requirementsText.trim()) {
  throw new Error("Requirements text is required when manual entry is selected");
}

if (requirementsOption === "knowledge" &&
    (!selectedKnowledgeDocuments || selectedKnowledgeDocuments.length === 0)) {
  throw new Error("Please select at least one knowledge base document");
}
```

**Requirements Impact on Generation**:
```typescript
// Progress messages change based on requirements type
setGenerationStatus(
  selectedKnowledgeDocuments.length > 0
    ? "Analyzing knowledge base documents..."
    : "Researching your requirements..."
);
```

### **Requirements Usage Summary**

**Manual Requirements (`requirementsOption: "requirements"`)**:
- **Input**: User types requirements in textarea (up to unlimited characters)
- **Usage**: `requirements.description` included directly in research prompt as JSON
- **Purpose**: Provides specific project needs, constraints, and expectations
- **AI Processing**: Research phase analyzes requirements for industry insights and technical needs

**Knowledge Base Requirements (`requirementsOption: "knowledge"`)**:
- **Input**: User selects documents from Pinecone vector database
- **Usage**: `requirements.selectedKnowledgeDocuments` used to fetch full document content
- **Purpose**: Leverages existing knowledge base for proven methodologies and best practices
- **AI Processing**: All prompts enhanced with reference materials and specific integration guidelines

**Upload Requirements (`requirementsOption: "upload"`)**:
- **Status**: UI exists but backend processing not fully implemented
- **Purpose**: Would allow file uploads for requirements specification
- **Current Behavior**: Clears manual requirements text when selected

### **Requirements Enhancement of AI Output**

**Without Knowledge Base**:
```
Research → Basic industry analysis + manual requirements
Summary → Standard executive summary
Sections → Generic section content + manual requirements
```

**With Knowledge Base**:
```
Research → Enhanced analysis + manual requirements + reference materials
Summary → Data-driven summary with reference validation
Sections → Enriched content + proven methodologies + case studies + benchmarks
```

**Key Impact**:
- **Requirements drive content focus**: Manual text guides AI research direction
- **Knowledge base amplifies quality**: Reference documents provide proven approaches
- **Validation ensures completeness**: Users must provide either manual text or knowledge docs
- **Progressive enhancement**: More knowledge base docs = richer, more detailed output

## �💾 Data Persistence & Storage

### Session Storage (Real-time State)
**Key**: `document_${documentId}`
**Purpose**: Immediate state persistence during generation
**Structure**:
```typescript
{
  documentData: {title, client, type, sections},
  sections: Array<{id, title, content, blocks, status, order}>,
  overview: string,
  status: "generating" | "completed",
  documentId: string,
  startedAt: string,
  preserveOriginalContent: boolean,
  knowledgeBaseUsed: boolean
}
```

### Supabase Database
**Tables**:
- `scopingai_documents`: Document metadata
- `scopingai_clients`: Client information
- File storage for document content

### Document File Management
**Storage**: Supabase Storage
**Format**: Markdown files
**Path Structure**: Organized by document ID and type

## ⚡ Performance Optimizations

### Streaming Architecture Benefits
- **Real-time Updates**: Users see progress immediately
- **Reduced Perceived Latency**: Incremental content delivery
- **Better UX**: Progress indicators and status updates
- **Error Recovery**: Graceful handling of partial failures

### Caching Strategies
- **Knowledge Base**: Efficient document retrieval with Pinecone
- **Session Storage**: Immediate state persistence
- **Client Data**: Cached for reuse across sessions

### Resource Management
- **Token Optimization**: Adjusted limits based on content complexity
- **Connection Management**: Proper EventSource cleanup
- **Memory Efficiency**: Streaming prevents large payload accumulation

## 🛡️ Error Handling & Resilience

### Frontend Error Handling
```typescript
// Validation errors
if (!documentData.title) throw new Error("Document title is required");

// Network errors during streaming
catch (streamError) {
  if (callbacks.onError) callbacks.onError({
    message: "Stream processing failed",
    type: "stream_error"
  });
}

// Parse errors for malformed responses
catch (parseError) {
  console.warn("Failed to parse streaming data:", data);
}
```

### Backend Error Handling
```typescript
// Knowledge base retrieval failures (continues with warning)
catch (error) {
  console.error("Error retrieving reference documents:", error);
  // Continue with generation but log the error
}

// OpenAI API errors
catch (error) {
  res.write(`event: error\ndata: ${JSON.stringify({
    error: "Error generating content",
    details: error.message
  })}\n\n`);
}
```

## 🔧 Configuration & Environment

### Required Environment Variables
```bash
OPENAI_API_KEY=your_openai_key
OPENAI_MODEL=gpt-3.5-turbo  # Optional, defaults to gpt-3.5-turbo
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE=your_service_role_key
PINECONE_API_KEY=your_pinecone_key
```

### API Configuration
```typescript
// Frontend API base
const streamUrl = `${config.apiUrl}/api/scopingai/proposals/stream`;

// Headers
{
  "Content-Type": "application/json",
  "x-api-key": "scopingai"
}
```

## 🧪 Testing & Quality Assurance

### Testing Strategy
- **Unit Tests**: Individual component and service testing
- **Integration Tests**: API endpoint and database interaction testing
- **E2E Tests**: Complete user flow testing
- **Performance Tests**: Streaming and AI response time testing

### Quality Metrics
- **Generation Success Rate**: Percentage of successful document generations
- **Response Time**: Average time for complete document generation
- **Knowledge Base Utilization**: Effectiveness of reference material integration
- **User Satisfaction**: Document quality and relevance metrics

## 🚀 Deployment & Scaling

### Deployment Architecture
- **Frontend**: Static hosting with CDN
- **Backend**: Node.js server with load balancing
- **Database**: Supabase managed PostgreSQL
- **AI Services**: OpenAI API with rate limiting
- **Vector Database**: Pinecone managed service

### Scaling Considerations
- **Concurrent Generations**: Queue management for multiple users
- **AI Rate Limits**: OpenAI API quota management
- **Database Connections**: Connection pooling for Supabase
- **Storage Optimization**: Efficient file storage and retrieval

## 📊 Monitoring & Analytics

### Key Metrics
- **Generation Volume**: Documents created per time period
- **Success Rate**: Successful vs failed generations
- **Performance**: Average generation time and resource usage
- **Knowledge Base Usage**: Reference document utilization rates
- **User Engagement**: Feature adoption and user flow completion

### Logging Strategy
- **Frontend**: User actions and error tracking
- **Backend**: API requests, AI interactions, and performance metrics
- **Database**: Query performance and data integrity monitoring

---

## 🔗 Related Documentation
- [API Documentation](api-docs.md)
- [Database Schema](database-schema.md)
- [Deployment Guide](deployment.md)
- [User Guide](user-guide.md)
