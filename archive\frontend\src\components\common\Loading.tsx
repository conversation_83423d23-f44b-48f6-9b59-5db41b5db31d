const Loading = () => {
  return (
    <div className="fixed inset-0 bg-white backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center">
        <div className="relative w-32 h-32 mx-auto">
          {/* Neural Network Animation */}
          <div className="absolute inset-0">
            <svg className="animate-pulse" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="50" r="45" stroke="#4F46E5" strokeWidth="2" />
              <circle cx="30" cy="35" r="8" fill="#4F46E5" className="animate-ping" />
              <circle cx="70" cy="35" r="8" fill="#4F46E5" className="animate-ping [animation-delay:0.2s]" />
              <circle cx="50" cy="65" r="8" fill="#4F46E5" className="animate-ping [animation-delay:0.4s]" />
              <line x1="30" y1="35" x2="70" y2="35" stroke="#4F46E5" strokeWidth="2" />
              <line x1="30" y1="35" x2="50" y2="65" stroke="#4F46E5" strokeWidth="2" />
              <line x1="70" y1="35" x2="50" y2="65" stroke="#4F46E5" strokeWidth="2" />
            </svg>
          </div>
          
          {/* Circular Loading */}
          <div className="absolute inset-0">
            <svg className="animate-spin" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="50" cy="50" r="45" stroke="#E5E7EB" strokeWidth="8" />
              <path
                d="M50 5 A 45 45 0 0 1 95 50"
                stroke="#4F46E5"
                strokeWidth="8"
                strokeLinecap="round"
              />
            </svg>
          </div>
        </div>
        <p className="mt-4 text-lg font-medium text-gray-900">Processing...</p>
        <p className="mt-2 text-sm text-gray-500">AI is analyzing your input</p>
      </div>
    </div>
  );
};

export default Loading; 