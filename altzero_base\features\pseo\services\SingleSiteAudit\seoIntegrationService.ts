import { htmlAnalysisService, HTMLAnalysisResult } from './htmlAnalysisService';
import { seoScoringService, SEOScoringResult } from './seoScoringService';
import { scraperService } from './scraperService';
import { databaseService } from '../pseo/databaseService';

export interface ComprehensiveSEOAnalysis {
  // Basic Info
  url: string;
  analyzedAt: Date;
  processingTime: number;
  
  // HTML Analysis Results
  htmlAnalysis: HTMLAnalysisResult;
  
  // SEO Scoring Results
  seoScoring: SEOScoringResult;
  
  // Combined Summary
  summary: {
    overallHealth: 'excellent' | 'good' | 'average' | 'poor' | 'critical';
    primaryIssues: string[];
    quickWins: string[];
    technicalDebt: string[];
    estimatedFixTime: string;
  };
  
  // Provider Information
  providersUsed: {
    [functionType: string]: {
      provider: string;
      confidence: number;
    };
  };
  
  // Action Plan
  actionPlan: {
    immediate: ActionItem[];
    shortTerm: ActionItem[];
    longTerm: ActionItem[];
  };
}

export interface ActionItem {
  title: string;
  description: string;
  category: 'technical' | 'content' | 'performance' | 'backlink' | 'keyword';
  priority: 'critical' | 'high' | 'medium' | 'low';
  estimatedHours: number;
  expectedImpact: string;
  htmlLocations?: {
    lineNumber: number;
    cssSelector: string;
    xpath: string;
  }[];
}

export interface SEOAuditConfiguration {
  url: string;
  includeKeywordAnalysis?: boolean;
  includeBacklinkAnalysis?: boolean;
  includeCompetitorAnalysis?: boolean;
  targetKeywords?: string[];
  auditDepth: 'basic' | 'standard' | 'comprehensive';
}

class SEOIntegrationService {
  /**
   * Perform complete SEO analysis with exact HTML locations and scoring
   */
  async performComprehensiveAnalysis(config: SEOAuditConfiguration): Promise<ComprehensiveSEOAnalysis> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 Starting comprehensive SEO analysis for: ${config.url}`);
      
      // Step 1: Validate configuration
      await this.validateConfiguration();
      
      // Step 2: Scrape website content
      console.log('📄 Scraping website content...');
      const scrapingResult = await scraperService.scrapeWebsite(config.url);
      
      if (!scrapingResult.html) {
        throw new Error(`Failed to scrape website: No HTML content received`);
      }
      
      // Step 3: Perform HTML analysis with exact locations
      console.log('🔍 Analyzing HTML structure and finding exact issue locations...');
      const htmlAnalysis = await htmlAnalysisService.analyzeHTML(scrapingResult.html);
      
      // Step 4: Perform SEO scoring with provider integration
      console.log('📊 Calculating SEO scores using configured providers...');
      const seoScoring = await seoScoringService.performSEOScoring(scrapingResult.html, config.url);
      
      // Step 5: Generate combined summary
      const summary = this.generateSummary(htmlAnalysis, seoScoring);
      
      // Step 6: Create action plan with HTML locations
      const actionPlan = this.createActionPlan(htmlAnalysis, seoScoring);
      
      // Step 7: Get provider information
      const providersUsed = this.getProvidersUsed();
      
      const processingTime = Date.now() - startTime;
      
      const result: ComprehensiveSEOAnalysis = {
        url: config.url,
        analyzedAt: new Date(),
        processingTime,
        htmlAnalysis,
        seoScoring,
        summary,
        actionPlan,
        providersUsed
      };
      
      console.log(`✅ Analysis complete in ${processingTime}ms. Overall score: ${seoScoring.overallScore}/100 (${seoScoring.grade})`);
      
      return result;
      
    } catch (error) {
      console.error('❌ Comprehensive SEO analysis failed:', error);
      throw new Error(`SEO analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get exact HTML locations for specific SEO issues
   */
  async getExactIssueLocations(url: string, issueTypes?: string[]): Promise<{
    url: string;
    issues: Array<{
      issue: string;
      severity: 'critical' | 'warning' | 'info';
      htmlLocation: {
        lineNumber: number;
        columnNumber: number;
        cssSelector: string;
        xpath: string;
        context: {
          before: string;
          current: string;
          after: string;
        };
      };
      recommendation: string;
      impact: string;
    }>;
  }> {
    try {
      // Scrape content
      const scrapingResult = await scraperService.scrapeWebsite(url);
      if (!scrapingResult.html) {
        throw new Error(`Failed to scrape website: No HTML content received`);
      }
      
      // Analyze HTML
      const htmlAnalysis = await htmlAnalysisService.analyzeHTML(scrapingResult.html);
      
      // Filter issues if specific types requested
      let filteredIssues = htmlAnalysis.issues;
      if (issueTypes && issueTypes.length > 0) {
        filteredIssues = htmlAnalysis.issues.filter(issue => 
          issueTypes.some(type => issue.issue.toLowerCase().includes(type.toLowerCase()))
        );
      }
      
      return {
        url,
        issues: filteredIssues.map(issue => ({
          issue: issue.issue,
          severity: issue.severity,
          htmlLocation: {
            lineNumber: issue.lineNumber,
            columnNumber: issue.columnNumber,
            cssSelector: issue.cssSelector,
            xpath: issue.xpath,
            context: issue.context
          },
          recommendation: issue.recommendation,
          impact: issue.impact
        }))
      };
      
    } catch (error) {
      throw new Error(`Failed to get exact issue locations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get SEO score breakdown by provider
   */
  async getProviderScoreBreakdown(url: string): Promise<{
    url: string;
    overallScore: number;
    grade: string;
    providerBreakdown: {
      [functionType: string]: {
        provider: string;
        score: number;
        confidence: number;
        metrics: any;
      };
    };
  }> {
    try {
      // Scrape content
      const scrapingResult = await scraperService.scrapeWebsite(url);
      if (!scrapingResult.html) {
        throw new Error(`Failed to scrape website: No HTML content received`);
      }
      
      // Get SEO scoring
      const seoScoring = await seoScoringService.performSEOScoring(scrapingResult.html, url);
      
      return {
        url,
        overallScore: seoScoring.overallScore,
        grade: seoScoring.grade,
        providerBreakdown: Object.fromEntries(
          Object.entries(seoScoring.providerBreakdown).map(([functionType, info]) => [
            functionType,
            {
              provider: info?.provider || 'none',
              score: info?.score || 0,
              confidence: info?.confidence || 0,
              metrics: this.getMetricsForFunction(functionType, seoScoring)
            }
          ])
        )
      };
      
    } catch (error) {
      throw new Error(`Failed to get provider score breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Integrate with existing pSEO audit workflow
   */
  async integrateWithPSEOAudit(auditId: string): Promise<{
    auditId: string;
    htmlAnalysisAdded: boolean;
    seoScoreAdded: boolean;
    exactLocationsAdded: number;
  }> {
    try {
      // Get existing audit
      const audit = await databaseService.getAuditById(auditId);
      if (!audit) {
        throw new Error(`Audit not found: ${auditId}`);
      }

      // Get website info
      const website = await databaseService.getWebsiteById(audit.website_id);
      if (!website) {
        throw new Error(`Website not found for audit: ${auditId}`);
      }

      // Perform comprehensive analysis
      const analysis = await this.performComprehensiveAnalysis({
        url: website.url,
        auditDepth: 'comprehensive'
      });

      // Update audit with HTML analysis results
      const htmlAnalysisUpdate = await databaseService.updateAudit(auditId, {
        html_analysis_result: JSON.stringify(analysis.htmlAnalysis),
        updated_at: new Date().toISOString()
      });

      // Update audit with SEO scoring results
      const seoScoreUpdate = await databaseService.updateAudit(auditId, {
        seo_score: analysis.seoScoring.overallScore,
        seo_grade: analysis.seoScoring.grade,
        seo_scoring_result: JSON.stringify(analysis.seoScoring),
        updated_at: new Date().toISOString()
      });

      // Add exact issue locations as audit steps
      let exactLocationsAdded = 0;
      for (const issue of analysis.htmlAnalysis.issues) {
        await databaseService.createAuditStep({
          audit_id: auditId,
          step_name: issue.issue,
          step_type: 'html_analysis',
          status: 'completed',
          step_data: {},
          result: JSON.stringify({
            severity: issue.severity,
            lineNumber: issue.lineNumber,
            columnNumber: issue.columnNumber,
            cssSelector: issue.cssSelector,
            xpath: issue.xpath,
            context: issue.context,
            recommendation: issue.recommendation,
            impact: issue.impact
          }),
          metadata: JSON.stringify({
            category: 'technical',
            priority: issue.severity,
            htmlLocation: true
          })
        });
        exactLocationsAdded++;
      }

      return {
        auditId,
        htmlAnalysisAdded: !!htmlAnalysisUpdate,
        seoScoreAdded: !!seoScoreUpdate,
        exactLocationsAdded
      };

    } catch (error) {
      throw new Error(`Failed to integrate with pSEO audit: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate comprehensive summary
   */
  private generateSummary(htmlAnalysis: HTMLAnalysisResult, seoScoring: SEOScoringResult): ComprehensiveSEOAnalysis['summary'] {
    // Determine overall health
    let overallHealth: ComprehensiveSEOAnalysis['summary']['overallHealth'];
    if (seoScoring.overallScore >= 90) overallHealth = 'excellent';
    else if (seoScoring.overallScore >= 80) overallHealth = 'good';
    else if (seoScoring.overallScore >= 70) overallHealth = 'average';
    else if (seoScoring.overallScore >= 60) overallHealth = 'poor';
    else overallHealth = 'critical';

    // Identify primary issues (critical and high priority)
    const criticalIssues = htmlAnalysis.issues.filter(issue => issue.severity === 'critical');
    const primaryIssues = criticalIssues.map(issue => issue.issue);

    // Identify quick wins (info and warning level issues)
    const quickWinIssues = htmlAnalysis.issues.filter(issue => issue.severity === 'info' || issue.severity === 'warning');
    const quickWins = quickWinIssues.slice(0, 5).map(issue => issue.issue);

    // Identify technical debt
    const technicalDebt = [
      ...htmlAnalysis.issues.filter(issue => issue.issue.toLowerCase().includes('structure')).map(i => i.issue),
      ...seoScoring.recommendations.filter(rec => rec.category === 'technical' && rec.priority === 'medium').map(r => r.title)
    ].slice(0, 3);

    // Estimate fix time
    const totalIssues = htmlAnalysis.issues.length;
    let estimatedHours = 0;
    htmlAnalysis.issues.forEach(issue => {
      if (issue.severity === 'critical') estimatedHours += 4;
      else if (issue.severity === 'warning') estimatedHours += 2;
      else estimatedHours += 1;
    });

    const estimatedFixTime = estimatedHours < 8 ? '1 day' : 
                           estimatedHours < 24 ? '1-3 days' :
                           estimatedHours < 40 ? '1 week' : '2+ weeks';

    return {
      overallHealth,
      primaryIssues,
      quickWins,
      technicalDebt,
      estimatedFixTime
    };
  }

  /**
   * Create detailed action plan with HTML locations
   */
  private createActionPlan(htmlAnalysis: HTMLAnalysisResult, seoScoring: SEOScoringResult): ComprehensiveSEOAnalysis['actionPlan'] {
    const immediate: ActionItem[] = [];
    const shortTerm: ActionItem[] = [];
    const longTerm: ActionItem[] = [];

    // Process HTML analysis issues
    htmlAnalysis.issues.forEach(issue => {
      const actionItem: ActionItem = {
        title: issue.issue,
        description: issue.impact,
        category: 'technical',
        priority: issue.severity === 'critical' ? 'critical' : issue.severity === 'warning' ? 'high' : 'medium',
        estimatedHours: issue.severity === 'critical' ? 4 : issue.severity === 'warning' ? 2 : 1,
        expectedImpact: issue.severity === 'critical' ? 'High SEO impact' : 
                       issue.severity === 'warning' ? 'Medium SEO impact' : 'Low SEO impact',
        htmlLocations: [{
          lineNumber: issue.lineNumber,
          cssSelector: issue.cssSelector,
          xpath: issue.xpath
        }]
      };

      if (issue.severity === 'critical') {
        immediate.push(actionItem);
      } else if (issue.severity === 'warning') {
        shortTerm.push(actionItem);
      } else {
        longTerm.push(actionItem);
      }
    });

    // Process SEO scoring recommendations
    seoScoring.recommendations.forEach(rec => {
      const actionItem: ActionItem = {
        title: rec.title,
        description: rec.description,
        category: rec.category,
        priority: rec.priority,
        estimatedHours: rec.estimatedEffort === 'high' ? 8 : rec.estimatedEffort === 'medium' ? 4 : 2,
        expectedImpact: rec.expectedImpact
      };

      if (rec.priority === 'critical') {
        immediate.push(actionItem);
      } else if (rec.priority === 'high') {
        shortTerm.push(actionItem);
      } else {
        longTerm.push(actionItem);
      }
    });

    // Remove duplicates and sort by priority
    const deduplicateAndSort = (items: ActionItem[]) => {
      const unique = items.filter((item, index, arr) => 
        arr.findIndex(i => i.title === item.title) === index
      );
      return unique.sort((a, b) => {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });
    };

    return {
      immediate: deduplicateAndSort(immediate),
      shortTerm: deduplicateAndSort(shortTerm),
      longTerm: deduplicateAndSort(longTerm)
    };
  }

  /**
   * Get information about providers being used
   */
  private getProvidersUsed(): ComprehensiveSEOAnalysis['providersUsed'] {
    // TODO: Update this method to work with backend ProviderConfigService
    // const configuredFunctions = seoConfig.getAllConfiguredFunctions();
    const providersUsed: ComprehensiveSEOAnalysis['providersUsed'] = {};

    // Temporarily return empty until backend integration is complete
    // Object.entries(configuredFunctions).forEach(([functionType, config]) => {
    //   if (config) {
    //     const providerInfo = seoConfig.getProviderInfo(config.provider);
    //     providersUsed[functionType] = {
    //       provider: providerInfo.name,
    //       confidence: functionType === 'backlink' ? 0.95 : 
    //                  functionType === 'keyword' ? 0.85 : 
    //                  functionType === 'generator' ? 0.90 : 0.80
    //     };
    //   }
    // });

    return providersUsed;
  }

  /**
   * Get metrics for specific function type
   */
  private getMetricsForFunction(functionType: string, seoScoring: SEOScoringResult): any {
    switch (functionType) {
      case 'generator':
        return {
          technicalScore: seoScoring.metrics.technicalScore,
          technicalIssues: seoScoring.metrics.technicalIssues
        };
      case 'pagespeed':
        return {
          performanceScore: seoScoring.metrics.performanceScore,
          performanceMetrics: seoScoring.metrics.performanceMetrics
        };
      case 'backlink':
        return seoScoring.metrics.backlinkMetrics;
      case 'keyword':
        return seoScoring.metrics.keywordMetrics;
      default:
        return {};
    }
  }

  /**
   * Validate SEO configuration
   */
  private async validateConfiguration(): Promise<void> {
    // TODO: Update this method to work with backend ProviderConfigService
    // const validation = seoConfig.validateConfiguration();
    
    // if (!validation.isValid) {
    //   const errorMessage = `SEO configuration is invalid:\n${validation.errors.join('\n')}`;
    //   throw new Error(errorMessage);
    // }

    // if (validation.warnings.length > 0) {
    //   console.warn('⚠️ SEO configuration warnings:', validation.warnings);
    // }

    // if (validation.suggestions.length > 0) {
    //   console.info('💡 SEO configuration suggestions:', validation.suggestions);
    // }
    
    // Temporarily skip validation until backend integration is complete
    console.log('⚠️ SEO configuration validation skipped - awaiting backend integration');
  }
}

export const seoIntegrationService = new SEOIntegrationService(); 