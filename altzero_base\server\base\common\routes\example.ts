import express from "express";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "../base/common/route/auth";
import { supabase } from "../base/common/apps/supabase";

const router = express.Router();

// Get example data
router.get("/", validateApi<PERSON><PERSON>, async (req, res) => {
  try {
    // Example of using supabase client
    const { data, error } = await supabase
      .from("examples")
      .select("*")
      .limit(10);

    if (error) throw error;

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Export router
export default router;
