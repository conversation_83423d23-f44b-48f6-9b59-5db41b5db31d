"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  Search,
  Upload,
  Trash2,
  RefreshCw,
  Eye,
  Download,
  CheckCircle,
  AlertCircle,
  Clock,
  Calendar,
  MoreVertical,
  Grid3X3,
  List,
  Loader2,
  Plus,
  Filter,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { Button } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import { Badge } from "../../../../base/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../../base/components/ui/dropdown-menu";
import { useToast } from "../../../../base/hooks/use-toast";
import {
  scopingDocumentService,
  ScopingDocument,
} from "../services/scopingDocumentService";
import ScopingAILayout from "../components/ScopingAILayout";

export default function KnowledgeBaseDocuments() {
  const [documents, setDocuments] = useState<ScopingDocument[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const docs = await scopingDocumentService.getDocuments();
      setDocuments(docs);
      console.log(`✅ Loaded ${docs.length} scoping documents`);
    } catch (error) {
      console.error("❌ Failed to load documents:", error);
      toast({
        title: "Error",
        description: "Failed to load scoping documents",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await scopingDocumentService.deleteDocument(documentId);
      setDocuments((prev) => prev.filter((doc) => doc.id !== documentId));
      toast({
        title: "Success",
        description: "Document deleted successfully",
      });
    } catch (error) {
      console.error("Failed to delete document:", error);
      toast({
        title: "Error",
        description: "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.status &&
        doc.status.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesStatus = statusFilter === "all" || doc.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    const statusInfo = scopingDocumentService.getStatusInfo(status);
    switch (statusInfo.icon) {
      case "CheckCircle":
        return <CheckCircle className={`w-4 h-4 ${statusInfo.color}`} />;
      case "Clock":
        return (
          <Clock className={`w-4 h-4 ${statusInfo.color} animate-pulse`} />
        );
      case "Loader2":
        return (
          <Clock className={`w-4 h-4 ${statusInfo.color} animate-pulse`} />
        );
      case "AlertCircle":
        return <AlertCircle className={`w-4 h-4 ${statusInfo.color}`} />;
      default:
        return <FileText className={`w-4 h-4 ${statusInfo.color}`} />;
    }
  };

  return (
    <ScopingAILayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
                Document Library
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400">
                Manage your uploaded documents and their processing status
              </p>
            </div>
            <Button
              variant="outline"
              onClick={loadDocuments}
              disabled={isLoading}
              className="gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              {isLoading ? "Loading..." : "Refresh"}
            </Button>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search documents..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-80"
                  />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value="all">All Status</option>
                  <option value="created">Completed</option>
                  <option value="draft">Draft</option>
                  <option value="processing">Processing</option>
                  <option value="error">Error</option>
                </select>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Total Documents
                      </p>
                      <p className="text-2xl font-bold">{documents.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">Completed</p>
                      <p className="text-2xl font-bold">
                        {documents.filter((d) => d.status === "created").length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-yellow-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">Draft</p>
                      <p className="text-2xl font-bold">
                        {documents.filter((d) => d.status === "draft").length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">Errors</p>
                      <p className="text-2xl font-bold">
                        {documents.filter((d) => d.status === "error").length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Documents Display */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading documents...</span>
              </div>
            ) : filteredDocuments.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No documents found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {searchQuery
                      ? "No documents match your search"
                      : "Upload documents to get started"}
                  </p>
                  <Button
                    onClick={() =>
                      window.open("/scopingai/knowledge-base", "_blank")
                    }
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Go to Knowledge Base
                  </Button>
                </CardContent>
              </Card>
            ) : viewMode === "grid" ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredDocuments.map((doc) => (
                  <Card
                    key={doc.id}
                    className="hover:shadow-md transition-shadow"
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {getStatusIcon(doc.status)}
                          <CardTitle className="text-sm truncate">
                            {doc.title}
                          </CardTitle>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteDocument(doc.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {
                            scopingDocumentService.getStatusInfo(doc.status)
                              .label
                          }
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {scopingDocumentService.formatDate(doc.created_at)}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {scopingDocumentService.formatDate(doc.updated_at)}
                      </div>
                      {doc.metadata && (
                        <p className="text-xs text-muted-foreground mt-2">
                          {JSON.stringify(doc.metadata).substring(0, 100)}...
                        </p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-4">Name</th>
                        <th className="text-left p-4">Status</th>
                        <th className="text-left p-4">Created</th>
                        <th className="text-left p-4">Status</th>
                        <th className="text-left p-4">Updated</th>
                        <th className="text-right p-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredDocuments.map((doc) => (
                        <tr key={doc.id} className="border-b hover:bg-muted/50">
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(doc.status)}
                              <span className="font-medium">{doc.title}</span>
                            </div>
                          </td>
                          <td className="p-4">
                            <Badge variant="secondary">
                              {
                                scopingDocumentService.getStatusInfo(doc.status)
                                  .label
                              }
                            </Badge>
                          </td>
                          <td className="p-4 text-muted-foreground">
                            {scopingDocumentService.formatDate(doc.created_at)}
                          </td>
                          <td className="p-4">
                            <span
                              className={`capitalize ${
                                doc.status === "created"
                                  ? "text-green-600"
                                  : doc.status === "draft"
                                  ? "text-yellow-600"
                                  : doc.status === "error"
                                  ? "text-red-600"
                                  : ""
                              }`}
                            >
                              {
                                scopingDocumentService.getStatusInfo(doc.status)
                                  .label
                              }
                            </span>
                          </td>
                          <td className="p-4 text-muted-foreground">
                            {scopingDocumentService.formatDate(doc.updated_at)}
                          </td>
                          <td className="p-4 text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Download className="mr-2 h-4 w-4" />
                                  Download
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteDocument(doc.id)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    </ScopingAILayout>
  );
}
