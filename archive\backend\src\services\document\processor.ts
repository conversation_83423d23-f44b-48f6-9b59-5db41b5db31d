import {
  Document,
  VectorStoreIndex,
  SimpleNodeParser,
  ServiceContext,
  VectorStore,
  BaseNode,
  MetadataMode,
} from "llamaindex";
import { OpenAIEmbedding } from "@llamaindex/openai";
import { PineconeVectorStore } from "../vectorstore/pinecone";
import { DocumentMetadata } from "../../types/llama";
import { environment } from "../../config/environment";
import fs from "fs/promises";
import path from "path";

// Helper: safely trim string by byte size
function trimToBytes(str: string, maxBytes: number): string {
  const encoder = new TextEncoder();
  let total = 0;
  let result = "";

  for (const char of str) {
    const bytes = encoder.encode(char);
    if (total + bytes.length > maxBytes) break;
    total += bytes.length;
    result += char;
  }

  return result;
}

// Pinecone Adapter
class PineconeVectorStoreAdapter implements VectorStore {
  private pineconeStore: PineconeVectorStore;

  constructor(pineconeStore: PineconeVectorStore) {
    this.pineconeStore = pineconeStore;
  }

  storesText = true;

  client = () => this.pineconeStore.index;

  async add(nodes: BaseNode[]): Promise<string[]> {
    const embedder = new OpenAIEmbedding({
      apiKey: process.env.OPENAI_API_KEY!,
      model: "text-embedding-3-small",
      dimensions: 1024,
    });

    const vectors = await Promise.all(
      nodes.map(async (node) => {
        const fullText = node.getContent(MetadataMode.NONE);
        const trimmedText = trimToBytes(fullText, 39000);

        const embedding = await embedder.getTextEmbedding(fullText);

        return {
          id: node.id_,
          values: embedding,
          metadata: {
            title: node.metadata?.title || "",
            type: node.metadata?.type || "",
            createdAt: node.metadata?.createdAt || "",
            preview: trimmedText,
          },
        };
      })
    );

    const firstSize = JSON.stringify(vectors[0]?.metadata || {}).length;
    console.log(`🧠 First metadata size: ${firstSize} bytes`);

    await this.pineconeStore.index.upsert(vectors);
    console.log(`✅ Upserted ${vectors.length} vectors to Pinecone`);

    return vectors.map((v) => v.id);
  }

  async delete(refDocId: string): Promise<void> {}

  query: any;

  async deleteAll(): Promise<void> {
    if (typeof this.pineconeStore.deleteAll === "function") {
      await this.pineconeStore.deleteAll();
    } else {
      console.warn("deleteAll method not available on PineconeVectorStore");
    }
  }
}

// Processor Class
export class DocumentProcessor {
  private pineconeStore: PineconeVectorStore;
  private vectorStore: PineconeVectorStoreAdapter;
  private nodeParser: SimpleNodeParser;
  private serviceContext: ServiceContext;

  constructor() {
    this.pineconeStore = new PineconeVectorStore();

    const adapter = new PineconeVectorStoreAdapter(this.pineconeStore);
    adapter.query = this.pineconeStore.query.bind(this.pineconeStore);
    this.vectorStore = adapter;

    this.nodeParser = new SimpleNodeParser({
      chunkSize: 200,
      chunkOverlap: 20,
    });
  }

  reconfigureForLargeDocuments(): void {
    this.nodeParser = new SimpleNodeParser({
      chunkSize: 1024,
      chunkOverlap: 100,
    });
  }

  async loadDocumentsFromDirectory(
    directoryPath: string
  ): Promise<Document<DocumentMetadata>[]> {
    const files = await fs.readdir(directoryPath);
    const documents: Document<any>[] = [];

    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      const stats = await fs.stat(filePath);
      const content = await fs.readFile(filePath, "utf-8");

      documents.push(
        new Document({
          text: content,
          id_: filePath,
          metadata: {
            title: file,
            type: path.extname(file).slice(1),
            size: stats.size,
            createdAt: stats.birthtime.toISOString(),
            filepath: filePath,
            // text: content,
          },
        })
      );
    }

    return documents;
  }

  async createIndex(
    documents: Document<DocumentMetadata>[]
  ): Promise<VectorStoreIndex> {
    if (!documents || documents.length === 0) {
      console.log("No documents to index");

      return await VectorStoreIndex.fromDocuments([], this.serviceContext); // ✅ Correct format
    }

    try {
      console.time("Processing Time");

      // Parse docs into nodes
      const nodes = await this.nodeParser.getNodesFromDocuments(documents);

      // Push vectors to Pinecone manually
      await this.vectorStore.add(nodes);

      console.timeEnd("Processing Time");

      // Create index using service context (which already includes vectorStore)
      const index = await VectorStoreIndex.fromDocuments(
        documents,
        this.serviceContext
      );

      console.log("✅ Index created successfully");
      return index;
    } catch (error) {
      console.error("❌ Error creating index:", error);

      return await VectorStoreIndex.fromDocuments([], this.serviceContext);
    }
  }

  async deleteAllDocuments(): Promise<void> {
    await this.vectorStore.deleteAll();
  }
}
