import { supabase } from "../../../base/common/apps/supabase";

export interface Contact {
  id?: string;
  organisation_id: string;
  full_name: string;
  email?: string;
  phone?: string;
  phone2?: string;
  mobile?: string;
  fax?: string;
  address?: any;
  job_title?: string;
  company_id?: string;
  external_id?: string;
  owner_id?: string;
  salutation?: string;
  skypename?: string;
  webpage?: string;
  note?: string;
  tags?: string[];
  custom_fields?: any;
  created_at?: string;
  updated_at?: string;
}

export interface ContactFilters {
  page: number;
  limit: number;
  search?: string;
  tags?: string[];
  userId?: string;
}

export class ContactService {
  /**
   * Get user's organization IDs from organisation_members table
   */
  private async getUserOrganisationIds(userId: string): Promise<string[]> {
    const { data, error } = await supabase
      .from("organisation_members")
      .select("organisation_id")
      .eq("user_id", userId);

    if (error) {
      console.error("Error fetching user organizations:", error);
      return [];
    }

    return data.map((row) => row.organisation_id);
  }

  /**
   * Get user's organizations with details for selection
   */
  async getUserOrganizations(
    userId: string
  ): Promise<Array<{ id: string; name: string; role?: string }>> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const { data, error } = await supabase
        .from("organisation_members")
        .select(
          `
          role,
          organisations (
            id,
            name
          )
        `
        )
        .eq("user_id", userId);

      if (error) {
        console.error("Error fetching user organizations:", error);
        throw error;
      }

      return data.map((row: any) => ({
        id: row.organisations.id,
        name: row.organisations.name,
        role: row.role,
      }));
    } catch (error) {
      console.error("ContactService.getUserOrganizations error:", error);
      throw error;
    }
  }

  /**
   * Get contacts with pagination and filtering
   */
  async getContacts(filters: ContactFilters) {
    try {
      if (!filters.userId) {
        throw new Error("User ID is required");
      }

      const organisationIds = await this.getUserOrganisationIds(filters.userId);
      if (organisationIds.length === 0) {
        return { data: [], total: 0, page: filters.page, limit: filters.limit };
      }

      let query = supabase
        .from("crm_contacts")
        .select("*", { count: "exact" })
        .in("organisation_id", organisationIds);

      // Apply search filter
      if (filters.search) {
        query = query.or(
          `full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`
        );
      }

      // Apply tags filter
      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps("tags", filters.tags);
      }

      // Apply pagination
      const offset = (filters.page - 1) * filters.limit;
      query = query
        .order("created_at", { ascending: false })
        .range(offset, offset + filters.limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error("Error fetching contacts:", error);
        throw error;
      }

      return {
        data: data || [],
        total: count || 0,
        page: filters.page,
        limit: filters.limit,
      };
    } catch (error) {
      console.error("ContactService.getContacts error:", error);
      throw error;
    }
  }

  /**
   * Get a single contact by ID
   */
  async getContactById(
    contactId: string,
    userId: string
  ): Promise<Contact | null> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return null;
      }

      const { data, error } = await supabase
        .from("crm_contacts")
        .select("*")
        .eq("id", contactId)
        .in("organisation_id", organisationIds)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Not found
        }
        console.error("Error fetching contact:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("ContactService.getContactById error:", error);
      throw error;
    }
  }

  /**
   * Create a new contact
   */
  async createContact(
    contactData: Omit<Contact, "id" | "created_at" | "updated_at">,
    userId: string
  ): Promise<Contact> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        throw new Error("User is not a member of any organization");
      }

      // Validate that organization_id is provided
      if (!contactData.organisation_id) {
        throw new Error("Organization ID is required");
      }

      // Verify user has access to the specified organization
      if (!organisationIds.includes(contactData.organisation_id)) {
        throw new Error(
          "User does not have access to the specified organization"
        );
      }

      // Set owner_id to current user if not specified
      if (!contactData.owner_id) {
        contactData.owner_id = userId;
      }

      const { data, error } = await supabase
        .from("crm_contacts")
        .insert([contactData])
        .select()
        .single();

      if (error) {
        console.error("Error creating contact:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("ContactService.createContact error:", error);
      throw error;
    }
  }

  /**
   * Update an existing contact
   */
  async updateContact(
    contactId: string,
    contactData: Partial<Contact>,
    userId: string
  ): Promise<Contact | null> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return null;
      }

      // Remove fields that shouldn't be updated
      const { id, created_at, ...updateData } = contactData;
      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from("crm_contacts")
        .update(updateData)
        .eq("id", contactId)
        .in("organisation_id", organisationIds)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Not found
        }
        console.error("Error updating contact:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("ContactService.updateContact error:", error);
      throw error;
    }
  }

  /**
   * Delete a contact
   */
  async deleteContact(contactId: string, userId: string): Promise<boolean> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return false;
      }

      const { error } = await supabase
        .from("crm_contacts")
        .delete()
        .eq("id", contactId)
        .in("organisation_id", organisationIds);

      if (error) {
        console.error("Error deleting contact:", error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error("ContactService.deleteContact error:", error);
      throw error;
    }
  }

  /**
   * Get contacts by company
   */
  async getContactsByCompany(
    companyId: string,
    userId: string
  ): Promise<Contact[]> {
    try {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const organisationIds = await this.getUserOrganisationIds(userId);
      if (organisationIds.length === 0) {
        return [];
      }

      const { data, error } = await supabase
        .from("crm_contacts")
        .select("*")
        .eq("company_id", companyId)
        .in("organisation_id", organisationIds)
        .order("full_name");

      if (error) {
        console.error("Error fetching contacts by company:", error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error("ContactService.getContactsByCompany error:", error);
      throw error;
    }
  }
}
