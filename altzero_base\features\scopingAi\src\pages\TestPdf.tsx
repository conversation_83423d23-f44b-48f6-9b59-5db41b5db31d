import { useState } from "react";
// import PDFPreview from "../documents/preview";

// Sample data
const samplePages = [
  {
    id: "page-1",
    content: [
      {
        id: "section-1",
        title: "Introduction",
        blocks: [
          {
            id: "block-1",
            type: "text",
            content: "This is a test document to verify our PDF preview component works correctly.",
          },
          {
            id: "block-2",
            type: "header",
            content: "Key Points",
            level: 2,
          },
          {
            id: "block-3",
            type: "list",
            content: "",
            items: [
              "PDF-like preview with pages",
              "Thumbnails on the left side",
              "Navigation between pages"
            ]
          }
        ]
      }
    ]
  },
  {
    id: "page-2",
    content: [
      {
        id: "section-2",
        title: "Second Page",
        blocks: [
          {
            id: "block-4",
            type: "header",
            content: "This is page 2",
            level: 2,
          },
          {
            id: "block-5",
            type: "text",
            content: "Testing multi-page navigation."
          }
        ]
      }
    ]
  }
];

// Sample document
const sampleDocument = {
  title: "Test Document",
  client: "Test Client",
};

export default function TestPDFPage() {
  const [currentPage, setCurrentPage] = useState(0);
  
  return (
    <div className="container mx-auto py-6 max-w-[1200px]">
      <h1 className="text-2xl font-bold mb-6">PDF Preview Test</h1>
      
      <div className="border rounded-lg p-4">
        <div className="text-center text-muted-foreground">
          PDF Preview component temporarily disabled during migration
        </div>
        {/* <PDFPreview 
          document={sampleDocument} 
          pages={samplePages}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        /> */}
      </div>
    </div>
  );
} 
