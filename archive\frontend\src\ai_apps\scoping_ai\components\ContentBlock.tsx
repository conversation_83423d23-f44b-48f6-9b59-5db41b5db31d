import React from "react";
import { useDraggable } from "@dnd-kit/core";
import { Block } from "./ProposalEditor";

interface ContentBlockProps {
  block: Block;
  isSelected: boolean;
  onClick: () => void;
  onChange: (updates: Partial<Block>) => void;
  onDelete: () => void;
}

export const ContentBlock: React.FC<ContentBlockProps> = ({
  block,
  isSelected,
  onClick,
  onChange,
  onDelete,
}) => {
  // Add draggable functionality
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: block.id,
    data: {
      type: block.type,
    },
  });

  const renderBlockContent = () => {
    switch (block.type) {
      case "header":
        const HeaderTag = `h${block.level || 2}` as keyof JSX.IntrinsicElements;
        return (
          <HeaderTag
            className={`font-bold ${
              block.level === 1
                ? "text-2xl"
                : block.level === 2
                ? "text-xl"
                : "text-lg"
            }`}
          >
            {block.content || "Heading"}
          </HeaderTag>
        );

      case "text":
        return <p className="text-gray-700">{block.content || "Text block"}</p>;

      case "image":
        return (
          <div className="image-container">
            {block.url ? (
              <>
                <img
                  src={block.url}
                  alt={block.alt || ""}
                  className="max-w-full h-auto rounded-lg"
                />
                {block.caption && (
                  <div className="text-center text-sm text-gray-500 mt-2 italic">
                    {block.caption}
                  </div>
                )}
              </>
            ) : (
              <div className="bg-gray-200 p-8 text-center text-gray-500 rounded-lg">
                Image Placeholder
              </div>
            )}
          </div>
        );

      case "quote":
        return (
          <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600">
            {block.content || "Quote"}
          </blockquote>
        );

      case "section":
        return (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <h3 className="font-semibold text-lg mb-2">
              {block.content || "Section Title"}
            </h3>
          </div>
        );

      case "diagram":
        return (
          <div className="border border-gray-300 p-4 rounded-lg bg-gray-50">
            <div className="text-center text-gray-500">
              {block.content || "Diagram Placeholder"}
            </div>
          </div>
        );

      case "bullet-list":
        return (
          <ul className="list-disc pl-5">
            {block.items?.map((item, index) => <li key={index}>{item}</li>) || (
              <li>Bullet list item</li>
            )}
          </ul>
        );

      case "numbered-list":
        return (
          <ol className="list-decimal pl-5">
            {block.items?.map((item, index) => <li key={index}>{item}</li>) || (
              <li>Numbered list item</li>
            )}
          </ol>
        );

      case "list":
        return (
          <ul className="list-disc pl-5">
            {block.items?.map((item, index) => <li key={index}>{item}</li>) || (
              <li>List item</li>
            )}
          </ul>
        );

      default:
        return <p>{block.content || "Content block"}</p>;
    }
  };

  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      className={`p-4 rounded-lg border ${
        isSelected
          ? "border-indigo-500 bg-indigo-50 ring-2 ring-indigo-200"
          : "border-gray-200 hover:border-gray-300"
      } ${
        isDragging ? "opacity-50" : ""
      } cursor-grab active:cursor-grabbing relative group transition-all`}
      onClick={onClick}
    >
      <div className="flex items-center mb-2 text-xs text-gray-500 uppercase tracking-wide">
        <span className="mr-2">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </span>
        {block.type}
      </div>

      {renderBlockContent()}

      <div
        className={`absolute top-2 right-2 ${
          isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"
        } transition-opacity`}
      >
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full"
          title="Delete block"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};
