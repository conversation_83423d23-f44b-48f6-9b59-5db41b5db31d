import React, { useEffect, useState } from 'react';
import Layout from '../../../../components/Layout';
import ChatWindow from './ChatWindow';
import { useChat, Message } from '../../hooks/useChat';
import { useDocuments } from '../../hooks/useDocuments';

const RAGChatApp: React.FC = () => {
  const [selectedDocIds, setSelectedDocIds] = useState<string[]>([]);
  const [showDocumentSelector, setShowDocumentSelector] = useState(false);
  
  const { 
    messages, 
    isLoading, 
    error,
    inputValue, 
    setInputValue, 
    sendMessage,
    resetChat,
    setMessages
  } = useChat();

  const {
    documents,
    isLoading: isLoadingDocs,
    refreshDocuments
  } = useDocuments();

  // Load documents on initial render
  useEffect(() => {
    refreshDocuments();
    
    // Add welcome message
    const welcomeMessage: Message = {
      role: 'system',
      content: 'Welcome to the RAG Chatbot! Ask me any question about your uploaded documents, and I\'ll search for relevant information.'
    };
    setMessages([welcomeMessage]);
  }, [refreshDocuments, setMessages]);

  // Handle selecting/deselecting documents
  const handleDocumentSelection = (docId: string) => {
    setSelectedDocIds(prev => {
      if (prev.includes(docId)) {
        return prev.filter(id => id !== docId);
      } else {
        return [...prev, docId];
      }
    });
  };

  // Select all documents
  const handleSelectAll = () => {
    const allIds = documents.map(doc => doc.id);
    setSelectedDocIds(allIds);
  };

  // Deselect all documents
  const handleDeselectAll = () => {
    setSelectedDocIds([]);
  };

  // Send message with document filter
  const handleSendMessage = () => {
    // Pass selected document IDs as context to the chat
    sendMessage(selectedDocIds.length > 0 ? selectedDocIds : undefined);
  };

  return (
    <Layout>
      <div className="flex h-screen bg-gray-100">
        {/* Document Selector Sidebar */}
        <div className={`${showDocumentSelector ? 'w-64' : 'w-0'} transition-all duration-300 ease-in-out overflow-hidden bg-white border-r border-gray-200`}>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Documents</h2>
              <div className="space-x-2">
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  All
                </button>
                <button
                  onClick={handleDeselectAll}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  None
                </button>
              </div>
            </div>
            
            {isLoadingDocs ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="space-y-2">
                {documents.map(doc => (
                  <div
                    key={doc.id}
                    className="flex items-center space-x-2"
                  >
                    <input
                      type="checkbox"
                      checked={selectedDocIds.includes(doc.id)}
                      onChange={() => handleDocumentSelection(doc.id)}
                      className="rounded text-blue-500 focus:ring-blue-500"
                    />
                    <span className="text-sm truncate" title={doc.title}>
                      {doc.title}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Chat Window */}
        <div className="flex-1 flex flex-col">
          <div className="p-4 bg-white border-b border-gray-200 flex justify-between items-center">
            <h1 className="text-xl font-bold">RAG Chat</h1>
            <button
              onClick={() => setShowDocumentSelector(!showDocumentSelector)}
              className="text-gray-600 hover:text-gray-800"
            >
              {showDocumentSelector ? 'Hide Documents' : 'Show Documents'}
            </button>
          </div>

          <ChatWindow
            messages={messages}
            inputValue={inputValue}
            setInputValue={setInputValue}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            error={error}
          />
        </div>
      </div>
    </Layout>
  );
};

export default RAGChatApp; 