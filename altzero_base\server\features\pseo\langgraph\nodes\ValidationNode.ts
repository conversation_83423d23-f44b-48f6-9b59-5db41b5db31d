// =====================================================
// VALIDATION NODE - INPUT VALIDATION AND SANITIZATION
// =====================================================

import { BaseNode } from '../types/NodeTypes';
import { PSEOWorkflowState, WorkflowContext, WorkflowError } from '../types/WorkflowState';

export class ValidationNode implements BaseNode {
  name = 'validation';
  description = 'Validates and sanitizes workflow input parameters';

  async execute(context: WorkflowContext): Promise<Partial<PSEOWorkflowState>> {
    const { state, logger } = context;
    
    logger.info('Starting input validation', {
      workflow_id: state.workflow_id,
      research_method: state.research_method
    });

    try {
      const validationResults = await this.validateInputs(state, logger);
      
      if (!validationResults.isValid) {
        const errors: WorkflowError[] = validationResults.errors.map(error => ({
          node_name: this.name,
          error_message: error,
          error_code: 'VALIDATION_FAILED',
          timestamp: new Date().toISOString(),
          recoverable: false
        }));

        logger.error('Input validation failed', { errors: validationResults.errors });
        
        return {
          status: 'failed',
          errors: [...(state.errors || []), ...errors],
          current_step: 'validation_failed',
          last_updated: new Date().toISOString()
        };
      }

      // Sanitize and normalize inputs
      const sanitizedState = await this.sanitizeInputs(state, logger);
      
      logger.info('Input validation completed successfully', {
        warnings: validationResults.warnings.length
      });

      return {
        ...sanitizedState,
        current_step: 'validation_completed',
        progress: 10,
        last_updated: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Validation node execution failed', error);
      throw error;
    }
  }

  // Validate all input parameters
  private async validateInputs(
    state: PSEOWorkflowState,
    logger: any
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required field validation
    if (!state.workflow_id) {
      errors.push('Workflow ID is required');
    }

    if (!state.user_id) {
      errors.push('User ID is required');
    }

    if (!state.website_id) {
      errors.push('Website ID is required');
    }

    if (!state.research_method) {
      errors.push('Research method is required');
    } else if (!['website', 'topic'].includes(state.research_method)) {
      errors.push('Research method must be either "website" or "topic"');
    }

    // Method-specific validation
    if (state.research_method === 'website') {
      if (!state.domain) {
        errors.push('Domain is required for website research method');
      } else if (!this.isValidDomain(state.domain)) {
        errors.push('Invalid domain format');
      }
    }

    if (state.research_method === 'topic') {
      if (!state.topic_input || state.topic_input.trim().length === 0) {
        errors.push('Topic input is required for topic research method');
      } else if (state.topic_input.trim().length < 3) {
        errors.push('Topic input must be at least 3 characters long');
      } else if (state.topic_input.trim().length > 200) {
        warnings.push('Topic input is very long, consider shortening for better results');
      }
    }

    // Data sources validation
    if (!state.data_sources || state.data_sources.length === 0) {
      errors.push('At least one data source must be specified');
    } else {
      const validSources = ['rapidapi', 'semrush', 'ubersuggest', 'ahrefs', 'ai_generated'];
      const invalidSources = state.data_sources.filter(source => !validSources.includes(source));
      if (invalidSources.length > 0) {
        errors.push(`Invalid data sources: ${invalidSources.join(', ')}`);
      }
    }

    // Seed keywords validation
    if (state.seed_keywords && state.seed_keywords.length > 0) {
      const invalidKeywords = state.seed_keywords.filter(keyword => 
        !keyword || keyword.trim().length === 0 || keyword.trim().length > 100
      );
      if (invalidKeywords.length > 0) {
        warnings.push(`${invalidKeywords.length} invalid seed keywords will be filtered out`);
      }

      if (state.seed_keywords.length > 50) {
        warnings.push('Large number of seed keywords may slow down processing');
      }
    }

    // Competitor domains validation
    if (state.competitor_domains && state.competitor_domains.length > 0) {
      const invalidDomains = state.competitor_domains.filter(domain => !this.isValidDomain(domain));
      if (invalidDomains.length > 0) {
        warnings.push(`${invalidDomains.length} invalid competitor domains will be filtered out`);
      }

      if (state.competitor_domains.length > 10) {
        warnings.push('Large number of competitor domains may increase processing time and costs');
      }
    }

    // Max keywords validation
    if (state.max_keywords !== undefined) {
      if (state.max_keywords < 10) {
        warnings.push('Very low max_keywords limit may result in insufficient data');
      } else if (state.max_keywords > 1000) {
        warnings.push('Very high max_keywords limit may increase processing time and costs');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Sanitize and normalize input data
  private async sanitizeInputs(
    state: PSEOWorkflowState,
    logger: any
  ): Promise<Partial<PSEOWorkflowState>> {
    const sanitized: Partial<PSEOWorkflowState> = {};

    // Sanitize domain
    if (state.domain) {
      sanitized.domain = this.sanitizeDomain(state.domain);
    }

    // Sanitize topic input
    if (state.topic_input) {
      sanitized.topic_input = state.topic_input.trim().substring(0, 200);
    }

    // Sanitize seed keywords
    if (state.seed_keywords) {
      sanitized.seed_keywords = state.seed_keywords
        .filter(keyword => keyword && keyword.trim().length > 0 && keyword.trim().length <= 100)
        .map(keyword => keyword.trim().toLowerCase())
        .filter((keyword, index, array) => array.indexOf(keyword) === index) // Remove duplicates
        .slice(0, 50); // Limit to 50 keywords
    }

    // Sanitize competitor domains
    if (state.competitor_domains) {
      sanitized.competitor_domains = state.competitor_domains
        .filter(domain => this.isValidDomain(domain))
        .map(domain => this.sanitizeDomain(domain))
        .filter((domain, index, array) => array.indexOf(domain) === index) // Remove duplicates
        .slice(0, 10); // Limit to 10 competitors
    }

    // Set default max_keywords if not provided
    if (!state.max_keywords) {
      sanitized.max_keywords = 100;
    } else {
      sanitized.max_keywords = Math.min(Math.max(state.max_keywords, 10), 1000);
    }

    // Ensure data_sources is valid
    if (state.data_sources) {
      const validSources = ['rapidapi', 'semrush', 'ubersuggest', 'ahrefs', 'ai_generated'];
      sanitized.data_sources = state.data_sources.filter(source => validSources.includes(source));
      
      // Add AI as fallback if no valid sources
      if (sanitized.data_sources.length === 0) {
        sanitized.data_sources = ['ai_generated'];
      }
    }

    logger.debug('Input sanitization completed', {
      original_seed_keywords: state.seed_keywords?.length || 0,
      sanitized_seed_keywords: sanitized.seed_keywords?.length || 0,
      original_competitor_domains: state.competitor_domains?.length || 0,
      sanitized_competitor_domains: sanitized.competitor_domains?.length || 0
    });

    return sanitized;
  }

  // Validate domain format
  private isValidDomain(domain: string): boolean {
    if (!domain || typeof domain !== 'string') {
      return false;
    }

    // Remove protocol if present
    const cleanDomain = domain.replace(/^https?:\/\//, '').replace(/^www\./, '');
    
    // Basic domain validation regex
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.([a-zA-Z]{2,}|[a-zA-Z]{2,}\.[a-zA-Z]{2,})$/;
    
    return domainRegex.test(cleanDomain) && cleanDomain.length <= 253;
  }

  // Sanitize domain format
  private sanitizeDomain(domain: string): string {
    if (!domain) return '';
    
    // Remove protocol and www
    let cleanDomain = domain.toLowerCase()
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .replace(/\/$/, ''); // Remove trailing slash
    
    // Remove any path, query, or fragment
    cleanDomain = cleanDomain.split('/')[0].split('?')[0].split('#')[0];
    
    return cleanDomain;
  }

  // Validate configuration
  private validateConfiguration(config: any): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (config.timeout_seconds && (config.timeout_seconds < 30 || config.timeout_seconds > 3600)) {
      issues.push('Timeout should be between 30 and 3600 seconds');
    }

    if (config.retry_attempts && (config.retry_attempts < 0 || config.retry_attempts > 5)) {
      issues.push('Retry attempts should be between 0 and 5');
    }

    if (config.quality_threshold && (config.quality_threshold < 0 || config.quality_threshold > 1)) {
      issues.push('Quality threshold should be between 0 and 1');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  // Node validation method
  async validate(state: PSEOWorkflowState): Promise<boolean> {
    // This node is always valid to run as it performs the validation itself
    return true;
  }

  // Rollback method (not applicable for validation)
  async rollback(context: WorkflowContext): Promise<void> {
    // Validation node doesn't need rollback as it doesn't modify external state
    context.logger.debug('Validation node rollback called (no action needed)');
  }
}
