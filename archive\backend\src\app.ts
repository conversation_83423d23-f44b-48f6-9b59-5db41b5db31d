import express, { Router, Request, Response } from "express";
import cors from "cors";
import fileUpload, { UploadedFile } from "express-fileupload";
import session from "express-session";
import { validate<PERSON>pi<PERSON>ey } from "./middleware/auth";
import { environment } from "./config/environment";
import { ScopingStreamService } from "./services/scoping/scoping-stream";
import { SimpleDocumentParser } from "./services/document/simpleParser";
import { DocumentProcessor } from "./services/document/enhancedParser";
import { ChatService } from "./services/chat/chat";
import { ProposalStreamService } from "./services/proposal/proposal-stream";
import { Pinecone } from "@pinecone-database/pinecone";
import { PineconeVectorStore } from "./services/vectorstore/pinecone";
import os from "os";
import path from "path";
import { supabase } from "./config/supabase";
import { createClient } from "@supabase/supabase-js";
import fs from "fs";
// import { convertTemplateToMarkdown } from "./utils/templateUtils";

// Declare session types
declare module "express-session" {
  interface SessionData {
    documentStore?: {
      [key: string]: {
        id: string;
        filename: string;
        content: string;
        size: number;
      };
    };
  }
}

// Initialize the app
const app = express();
const router = Router();

// Configure middleware
app.use(
  cors({
    // Use environment variable for origin or allow multiple origins
    origin: process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(",")
      : "http://localhost:5173",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "x-api-key"],
    credentials: true,
    exposedHeaders: ["Content-Disposition"], // Allow frontend to access Content-Disposition header for filenames
  })
);

// Add specific CORS handling for SSE and document processing endpoints
app.use(
  ["/api/proposals/stream", "/api/documents/process"],
  (req, res, next) => {
    res.setHeader("Access-Control-Allow-Origin", "*");

    if (req.path === "/api/proposals/stream") {
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");
    }

    next();
  }
);

// Configure basic JSON parsing for all routes
app.use(express.json());

// Configure file upload middleware with proper settings
const fileUploadMiddleware = fileUpload({
  useTempFiles: true,
  tempFileDir: path.join(os.tmpdir(), "document-processor"),
  createParentPath: true,
  debug: true,
  safeFileNames: true,
  preserveExtension: 4, // Preserve up to 4 characters of extension (e.g., .docx)
  abortOnLimit: true,
  responseOnLimit: "File size limit has been reached",
  uploadTimeout: 60000, // 60 seconds timeout
  parseNested: true,
  limitHandler: function (req, res, next) {
    return res.status(413).json({
      success: false,
      error: "File too large",
      message: "File size exceeds limit",
    });
  },
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1,
  },
});

// Session configuration
app.use(
  session({
    secret: process.env.SESSION_SECRET || "scopingai-secret-key",
    resave: false,
    saveUninitialized: true,
    cookie: { secure: environment.nodeEnv === "production" },
  })
);

// Logging middleware
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Initialize Pinecone
async function testPineconeConnection() {
  console.log("🔍 Testing Pinecone connection...");

  try {
    const pc = new Pinecone({
      apiKey: environment.pineconeApiKey as string,
    });

    // Get the index
    const index = pc.Index(environment.pineconeIndexName as string);

    // Get index details
    console.log("Getting index info...");
    const indexStats = await index.describeIndexStats();

    console.log(
      `\nTotal vectors in index: ${indexStats.totalRecordCount || 0}`
    );
    console.log(`Namespaces: ${Object.keys(indexStats.namespaces || {})}`);

    // Test a simple query
    console.log("\nTesting a simple query...");
    const randomVector = Array.from(
      { length: 1024 },
      () => Math.random() * 0.01
    );

    const results = await index.query({
      vector: randomVector,
      topK: 5,
      includeMetadata: true,
    });

    console.log(`Query returned ${results.matches?.length || 0} matches`);

    return true;
  } catch (error) {
    console.error("❌ Pinecone test failed:", error);
    return false;
  }
}

// Create service instances
const scopingService = new ScopingStreamService();
const documentParser = new SimpleDocumentParser();
const enhancedDocumentParser = new DocumentProcessor();
const chatService = new ChatService();
const proposalStreamService = new ProposalStreamService();
const pineconeStore = new PineconeVectorStore();

// Initialize Pinecone when the server starts
(async () => {
  try {
    const pineconeConnected = await testPineconeConnection();
    if (pineconeConnected) {
      console.log("✅ Pinecone connection successful");
    } else {
      console.error("⚠️ Pinecone connection failed");
    }
  } catch (error) {
    console.error("❌ Error initializing Pinecone:", error);
  }
})();

// Health check route
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok" });
});

// Document upload endpoint
app.post(
  "/documents",
  validateApiKey,
  fileUploadMiddleware,
  async (req, res) => {
    try {
      // Check if files were uploaded
      if (!req.files || Object.keys(req.files).length === 0) {
        return res.status(400).json({ error: "No files were uploaded" });
      }

      const uploadedFile = req.files.file;

      // Process the document
      const document = await documentParser.processDocument(uploadedFile);

      // Store document in session for later use
      if (!req.session.documentStore) {
        req.session.documentStore = {};
      }
      req.session.documentStore[document.id] = document;

      // Return success with document ID
      return res.status(200).json({
        id: document.id,
        filename: document.filename,
        size: document.size,
      });
    } catch (error: any) {
      console.error("Error uploading document:", error);
      return res
        .status(500)
        .json({ error: error.message || "Failed to process document" });
    }
  }
);

// Document processing endpoint for Word/PDF files
app.post("/api/documents/process", fileUploadMiddleware, async (req, res) => {
  let tempFilePath: string | null = null;
  let newTempPath: string | null = null;

  try {
    console.log("📄 Document processing request received");

    // Check if files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      throw new Error("No files were uploaded");
    }

    // Handle the uploaded file
    const uploadedFile = req.files.file as UploadedFile;
    if (Array.isArray(uploadedFile)) {
      throw new Error("Multiple files not supported");
    }

    // Get original file extension and validate
    const originalExt = path.extname(uploadedFile.name).toLowerCase();
    const supportedExtensions = [".pdf", ".doc", ".docx", ".txt"];
    if (!supportedExtensions.includes(originalExt)) {
      throw new Error(
        `File type ${originalExt} is not supported. Supported types: ${supportedExtensions.join(", ")}`
      );
    }

    // Log file details
    console.log(
      `📂 Processing file: ${uploadedFile.name}, size: ${uploadedFile.size} bytes, type: ${uploadedFile.mimetype}`
    );

    // Verify the temp file exists and has content
    if (
      !uploadedFile.tempFilePath ||
      !fs.existsSync(uploadedFile.tempFilePath)
    ) {
      throw new Error("Temporary file not created properly");
    }

    tempFilePath = uploadedFile.tempFilePath;

    // Create a new temp file with proper extension
    newTempPath = `${tempFilePath}${originalExt}`;
    fs.renameSync(tempFilePath, newTempPath);
    console.log(`Renamed temp file to: ${newTempPath}`);

    const stats = fs.statSync(newTempPath);
    console.log(`Temporary file size: ${stats.size} bytes at ${newTempPath}`);

    if (stats.size === 0) {
      throw new Error("Uploaded file is empty");
    }

    if (stats.size !== uploadedFile.size) {
      throw new Error(
        `File size mismatch - expected ${uploadedFile.size} bytes but got ${stats.size} bytes`
      );
    }

    // Process the document
    console.log("Starting document processing...");
    const parsedDocument =
      await enhancedDocumentParser.processDocument(newTempPath);

    if (!parsedDocument) {
      throw new Error("Document parser returned no result");
    }

    if (parsedDocument.error) {
      throw new Error(parsedDocument.error);
    }

    // Success response
    return res.status(200).json({
      success: true,
      data: parsedDocument,
    });
  } catch (error: any) {
    console.error("❌ Error processing document:", error);
    return res.status(error.status || 500).json({
      success: false,
      error: "Failed to process document",
      message:
        error.message ||
        "An unexpected error occurred while processing the document",
      details: process.env.NODE_ENV === "development" ? error.stack : undefined,
    });
  } finally {
    // Clean up any temp files
    try {
      const tempFiles = [tempFilePath, newTempPath].filter(Boolean);
      for (const file of tempFiles) {
        if (file && fs.existsSync(file)) {
          fs.unlinkSync(file);
          console.log(`Cleaned up temporary file: ${file}`);
        }
      }
    } catch (cleanupError) {
      console.error("Error cleaning up temp files:", cleanupError);
    }
  }
});

// Scoping document generation endpoint
app.post("/scoping/stream", validateApiKey, (req, res) => {
  scopingService.streamResponse(req, res);
});

// Chat API Endpoint
router.post("/chat", validateApiKey, async (req, res) => {
  try {
    const { query, history = [], documentIds } = req.body;

    if (!query) {
      return res.status(400).json({ error: "Query is required" });
    }

    const result = await chatService.chat(query, history, documentIds);
    return res.json(result);
  } catch (error: any) {
    console.error("Error in chat endpoint:", error);
    return res.status(500).json({ error: error.message });
  }
});

// Proposal Generation Endpoint
router.get(
  "/proposals/stream",
  validateApiKey,
  async (req: express.Request, res: express.Response) => {
    try {
      console.log("🔥 Proposal stream request received");

      // Don't log the API key
      console.log("Query params:", {
        client_name: req.query.client_name,
        description: req.query.description,
      });

      const { client_name, description, api_key } = req.query;

      // Set headers first
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");
      res.setHeader("Access-Control-Allow-Origin", "*");

      // Send initial event
      console.log("Sending initial event...");
      res.write(
        `event: started\ndata: ${JSON.stringify({
          message: "Starting proposal generation...",
        })}\n\n`
      );

      // Ensure data is sent immediately
      // Some Express Response objects have flush() method from compression middleware
      const flushableRes = res as express.Response & { flush?: () => void };
      if (typeof flushableRes.flush === "function") {
        flushableRes.flush();
      }

      const request = {
        clientName: client_name as string,
        description: description as string,
        apiKey:
          (api_key as string) || (req.headers["x-api-key"] as string) || "",
      };

      try {
        const proposalStream =
          await proposalStreamService.generateProposalStream(request);

        for await (const event of proposalStream) {
          console.log("Streaming event:", event.type);

          // Format the event data properly
          const eventString = `event: ${event.type}\ndata: ${JSON.stringify(event.data || {})}\n\n`;
          res.write(eventString);

          // Flush after each event
          if (typeof flushableRes.flush === "function") {
            flushableRes.flush();
          }
        }
      } catch (streamError) {
        console.error("Stream error:", streamError);
        res.write(
          `event: error\ndata: ${JSON.stringify({
            error: "Error generating content",
          })}\n\n`
        );
      }

      // Always send end event
      res.write(
        `event: end\ndata: ${JSON.stringify({
          message: "Stream complete",
        })}\n\n`
      );

      res.end();
    } catch (error) {
      console.error("Error in proposal streaming", error);
      res.write(
        `event: error\ndata: ${JSON.stringify({
          error: "An error occurred",
        })}\n\n`
      );
      res.write(
        `event: end\ndata: ${JSON.stringify({
          message: "Stream ended with error",
        })}\n\n`
      );
      res.end();
    }
  }
);

// Use router for /api routes
app.use("/api", router);

/**
 * Converts a template to markdown format
 */
const convertTemplateToMarkdown = (template: any, sections: any) => {
  let markdown = `# ${template.name}\n\n`;

  if (template.description) {
    markdown += `${template.description}\n\n`;
  }

  sections.forEach((section: any) => {
    if (section.sections && section.sections.length > 0) {
      section.sections.forEach((s: any) => {
        markdown += `## ${s.title}\n\n`;
        if (s.description) markdown += `${s.description}\n\n`;
        if (s.content) markdown += `${s.content}\n\n`;
      });
    }
  });

  return markdown;
};

/**
 * Converts a template to markdown and saves it to the proposaltemplates bucket
 * @param {ExtendedSectionTemplate} template - The template to convert and save
 * @returns {Promise<string>} - The path to the saved file in the bucket
 */
const saveProposalTemplate = async (template: any) => {
  try {
    // 1. Convert template to markdown
    const markdown = convertTemplateToMarkdown(
      template,
      template.sections?.map((section: any) => ({
        id: section.id || "",
        sections: [
          {
            title: section.title,
            description: section.description || "",
            content: section.content || "",
            order: section.order || 0,
          },
        ],
      })) || []
    );

    // 2. Get the authenticated user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) throw new Error("User not authenticated");

    // 3. Generate a unique filename
    const fileName = `${template.name.replace(/\s+/g, "_")}_${Date.now()}.md`;
    const filePath = `${user.id}/${fileName}`;

    // 4. Upload the markdown to the proposaltemplates bucket
    const { error: uploadError } = await supabase.storage
      .from("proposaltemplates")
      .upload(filePath, new Blob([markdown], { type: "text/markdown" }), {
        upsert: true,
        contentType: "text/markdown",
      });

    if (uploadError) throw uploadError;

    // 5. Get the public URL
    const { data: publicUrlData } = supabase.storage
      .from("proposaltemplates")
      .getPublicUrl(filePath);

    // 6. Save only the reference in the database (not the content)
    const { data: dbData, error: dbError } = await supabase
      .from("proposal_templates")
      .insert({
        name: template.name,
        description: template.description || "",
        user_id: user.id,
        markdown_path: filePath,
        bucket_path: filePath,
        is_public: template.is_public || false,
      })
      .select()
      .single();

    if (dbError) throw dbError;

    return {
      templateId: dbData.id,
      filePath: filePath,
      publicUrl: publicUrlData.publicUrl,
    };
  } catch (error) {
    console.error("Error saving proposal template:", error);
    throw error;
  }
};

// Add this to your router section
router.post("/templates/save", validateApiKey, async (req, res) => {
  try {
    const template = req.body;

    if (!template || !template.name) {
      return res.status(400).json({ error: "Invalid template data" });
    }

    const result = await saveProposalTemplate(template);
    return res.status(200).json(result);
  } catch (error) {
    console.error("Error saving template:", error);
    return res.status(500).json({
      error: "Failed to save template",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

export default app;
