/**
 * Logger utility for consistent and detailed logging
 */

/**
 * Log LLM request in a formatted way
 */
export function logLLMRequest(model: string, service: string, prompt: string, additional?: Record<string, any>) {
  console.log('\n' + '-'.repeat(100));
  console.log(`🔍 LLM REQUEST [${service}] - Model: ${model}`);
  console.log('-'.repeat(100));
  console.log(prompt);
  
  if (additional) {
    console.log('\nAdditional parameters:');
    console.log(JSON.stringify(additional, null, 2));
  }
  
  console.log('-'.repeat(100) + '\n');
}

/**
 * Log LLM response in a formatted way
 */
export function logLLMResponse(service: string, response: string, timeTaken?: number) {
  console.log('\n' + '='.repeat(100));
  console.log(`✅ LLM RESPONSE [${service}]${timeTaken ? ` - Time: ${timeTaken}ms` : ''}`);
  console.log('='.repeat(100));
  console.log(response.substring(0, 1000) + (response.length > 1000 ? '...(truncated)' : ''));
  console.log('='.repeat(100) + '\n');
}

/**
 * Log an error in a formatted way
 */
export function logError(service: string, error: any) {
  console.error('\n' + '*'.repeat(100));
  console.error(`❌ ERROR [${service}]`);
  console.error('*'.repeat(100));
  console.error(error);
  console.error('*'.repeat(100) + '\n');
}

/**
 * Sanitize API keys from error messages
 */
export function sanitizeErrorMessage(message: string): string {
  return message.replace(/sk-[a-zA-Z0-9_-]+/g, '[API_KEY]');
} 