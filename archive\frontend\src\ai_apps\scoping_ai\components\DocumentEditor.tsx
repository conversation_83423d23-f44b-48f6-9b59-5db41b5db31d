import React, { useState, useEffect, useRef } from "react";
import { useEditor, EditorContent, BubbleMenu } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import Superscript from "@tiptap/extension-superscript";
import Subscript from "@tiptap/extension-subscript";
import TextStyle from "@tiptap/extension-text-style";
import FontFamily from "@tiptap/extension-font-family";
import Color from "@tiptap/extension-color";

// Structural components for toolbar
const ToolbarButton = ({ icon, title, onClick, isActive = false }) => (
  <button
    onClick={onClick}
    className={`p-1.5 rounded hover:bg-gray-200 ${
      isActive ? "bg-gray-200 text-blue-600" : "text-gray-700"
    }`}
    title={title}
  >
    <span className="w-5 h-5 flex items-center justify-center">{icon}</span>
  </button>
);

// Divider component
const Divider = () => <div className="w-px h-6 bg-gray-300 mx-1.5"></div>;

// Interface for document element with positioning
interface DocumentElement {
  type: string;
  content: string;
  level?: number;
  items?: string[];
  isNumbered?: boolean;
  order?: number;
  style?: {
    fontSize?: number;
    fontFamily?: string;
    isBold?: boolean;
    isItalic?: boolean;
    alignment?: "left" | "center" | "right" | "justified";
    color?: string;
  };
  position?: {
    x: number;
    y: number;
    page: number;
    width?: number;
    height?: number;
  };
  imageData?: {
    data: string;
    width: number;
    height: number;
    position: {
      x: number;
      y: number;
      page: number;
    };
    alt?: string;
  };
}

// Interface for section data
interface Section {
  id?: string;
  title: string;
  content?: string;
  description?: string;
  order?: number;
  images?: string[];
  imageData?: Array<{
    data: string;
    width?: number;
    height?: number;
    position?: {
      x: number;
      y: number;
      page?: number;
    };
    alt?: string;
  }>;
  elements?: DocumentElement[];
  data?: Array<Array<string | number | boolean | null>>;
  pageStart?: number;
  pageEnd?: number;
}

interface DocumentEditorProps {
  section?: Section;
  onSave: (updatedSection: Section) => void;
}

const DocumentEditor: React.FC<DocumentEditorProps> = ({ section, onSave }) => {
  const [editorContent, setEditorContent] = useState("");
  const [viewMode, setViewMode] = useState<"edit" | "preview">("edit");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [scale, setScale] = useState(1);
  const [isDirty, setIsDirty] = useState(false);

  // Initialize editor with extensions
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        inline: true,
        allowBase64: true,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Underline,
      Superscript,
      Subscript,
      TextStyle,
      FontFamily,
      Color,
    ],
    editorProps: {
      attributes: {
        class:
          "prose prose-sm sm:prose lg:prose-lg xl:prose-xl focus:outline-none",
      },
    },
    onUpdate: ({ editor }) => {
      setEditorContent(editor.getHTML());
      setIsDirty(true);
    },
  });

  // Process section data on initial load
  useEffect(() => {
    if (!section || !editor) return;

    let contentToSet = "";

    // Format regular content
    if (section.content) {
      contentToSet += `<h1>${section.title}</h1>${section.content}`;
    }

    // Process structural elements if available
    if (section.elements && section.elements.length > 0) {
      contentToSet = processElementsToHTML(section.elements);
    }

    // Process images if they exist
    if (section.imageData && section.imageData.length > 0) {
      // Append images with their positioning information
      section.imageData.forEach((img) => {
        contentToSet += `
          <div class="image-container" style="position: relative; margin: 1rem 0;">
            <img src="${img.data}" 
                 alt="${img.alt || "Document image"}" 
                 style="max-width: 100%; height: auto;" 
                 data-x="${img.position?.x || 0}" 
                 data-y="${img.position?.y || 0}" 
                 data-page="${img.position?.page || 1}" />
          </div>
        `;
      });
    } else if (section.images && section.images.length > 0) {
      // Handle regular image URLs
      section.images.forEach((imgUrl) => {
        contentToSet += `
          <div class="image-container" style="margin: 1rem 0;">
            <img src="${imgUrl}" alt="Document image" style="max-width: 100%; height: auto;" />
          </div>
        `;
      });
    }

    // Set total pages
    setTotalPages(
      Math.max(
        1,
        section.pageEnd ? section.pageEnd - (section.pageStart || 0) + 1 : 1
      )
    );

    // Set content to editor
    editor.commands.setContent(contentToSet);
    setEditorContent(contentToSet);
    setIsDirty(false);
  }, [section, editor]);

  // Process structured elements to HTML
  const processElementsToHTML = (elements: DocumentElement[]): string => {
    let html = "";

    elements.forEach((element) => {
      switch (element.type) {
        case "heading":
          const level = element.level || 1;
          html += `<h${level}>${element.content}</h${level}>`;
          break;

        case "paragraph":
          let style = "";
          if (element.style) {
            if (element.style.alignment)
              style += `text-align: ${element.style.alignment};`;
            if (element.style.fontFamily)
              style += `font-family: ${element.style.fontFamily};`;
            if (element.style.fontSize)
              style += `font-size: ${element.style.fontSize}px;`;
            if (element.style.color) style += `color: ${element.style.color};`;
            if (element.style.isBold) style += "font-weight: bold;";
            if (element.style.isItalic) style += "font-style: italic;";
          }
          html += `<p style="${style}">${element.content}</p>`;
          break;

        case "list":
          const listTag = element.isNumbered ? "ol" : "ul";
          html += `<${listTag}>`;
          if (element.items && element.items.length > 0) {
            element.items.forEach((item) => {
              html += `<li>${item}</li>`;
            });
          } else {
            html += `<li>${element.content}</li>`;
          }
          html += `</${listTag}>`;
          break;

        case "image":
          if (element.imageData) {
            const posAttr = element.position
              ? `data-x="${element.position.x}" data-y="${element.position.y}" data-page="${element.position.page}"`
              : "";

            html += `
              <div class="image-container" style="position: relative; margin: 1rem 0;">
                <img src="${element.imageData.data}" 
                     alt="${
                       element.imageData.alt ||
                       element.content ||
                       "Document image"
                     }" 
                     style="max-width: 100%; height: auto;" 
                     ${posAttr} />
                <div class="image-caption" style="text-align: center; font-size: 0.875rem; color: #6b7280; margin-top: 0.5rem;">
                  ${element.content || ""}
                </div>
              </div>
            `;
          }
          break;

        default:
          html += `<div>${element.content}</div>`;
      }
    });

    return html;
  };

  // Handle saving the edited content
  const handleSave = () => {
    if (!section || !editor) return;

    const updatedSection: Section = {
      ...section,
      content: editor.getHTML(),
    };

    onSave(updatedSection);
    setIsDirty(false);
  };

  // Toolbar actions
  const formatText = (command: string) => {
    if (!editor) return;

    switch (command) {
      case "bold":
        editor.chain().focus().toggleBold().run();
        break;
      case "italic":
        editor.chain().focus().toggleItalic().run();
        break;
      case "underline":
        editor.chain().focus().toggleUnderline().run();
        break;
      case "h1":
        editor.chain().focus().toggleHeading({ level: 1 }).run();
        break;
      case "h2":
        editor.chain().focus().toggleHeading({ level: 2 }).run();
        break;
      case "h3":
        editor.chain().focus().toggleHeading({ level: 3 }).run();
        break;
      case "bullet":
        editor.chain().focus().toggleBulletList().run();
        break;
      case "ordered":
        editor.chain().focus().toggleOrderedList().run();
        break;
      case "left":
        editor.chain().focus().setTextAlign("left").run();
        break;
      case "center":
        editor.chain().focus().setTextAlign("center").run();
        break;
      case "right":
        editor.chain().focus().setTextAlign("right").run();
        break;
      case "justify":
        editor.chain().focus().setTextAlign("justify").run();
        break;
    }
  };

  // Insert an image
  const insertImage = () => {
    const url = window.prompt("Enter image URL");
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  };

  return (
    <div className="document-editor flex flex-col h-full">
      {/* Editor Toolbar */}
      <div className="flex items-center bg-white border-b border-gray-200 p-2 space-x-1">
        {/* Text Formatting */}
        <ToolbarButton
          icon="B"
          title="Bold"
          onClick={() => formatText("bold")}
          isActive={editor?.isActive("bold")}
        />
        <ToolbarButton
          icon="I"
          title="Italic"
          onClick={() => formatText("italic")}
          isActive={editor?.isActive("italic")}
        />
        <ToolbarButton
          icon="U"
          title="Underline"
          onClick={() => formatText("underline")}
          isActive={editor?.isActive("underline")}
        />

        <Divider />

        {/* Headings */}
        <ToolbarButton
          icon="H1"
          title="Heading 1"
          onClick={() => formatText("h1")}
          isActive={editor?.isActive("heading", { level: 1 })}
        />
        <ToolbarButton
          icon="H2"
          title="Heading 2"
          onClick={() => formatText("h2")}
          isActive={editor?.isActive("heading", { level: 2 })}
        />
        <ToolbarButton
          icon="H3"
          title="Heading 3"
          onClick={() => formatText("h3")}
          isActive={editor?.isActive("heading", { level: 3 })}
        />

        <Divider />

        {/* Lists */}
        <ToolbarButton
          icon="•"
          title="Bullet List"
          onClick={() => formatText("bullet")}
          isActive={editor?.isActive("bulletList")}
        />
        <ToolbarButton
          icon="1."
          title="Ordered List"
          onClick={() => formatText("ordered")}
          isActive={editor?.isActive("orderedList")}
        />

        <Divider />

        {/* Alignment */}
        <ToolbarButton
          icon="≡"
          title="Align Left"
          onClick={() => formatText("left")}
          isActive={editor?.isActive({ textAlign: "left" })}
        />
        <ToolbarButton
          icon="≡"
          title="Align Center"
          onClick={() => formatText("center")}
          isActive={editor?.isActive({ textAlign: "center" })}
        />
        <ToolbarButton
          icon="≡"
          title="Align Right"
          onClick={() => formatText("right")}
          isActive={editor?.isActive({ textAlign: "right" })}
        />
        <ToolbarButton
          icon="≡"
          title="Justify"
          onClick={() => formatText("justify")}
          isActive={editor?.isActive({ textAlign: "justify" })}
        />

        <Divider />

        {/* Image */}
        <ToolbarButton icon="🖼️" title="Insert Image" onClick={insertImage} />

        <div className="flex-grow"></div>

        {/* View Modes */}
        <div className="flex rounded border border-gray-300 overflow-hidden">
          <button
            className={`px-3 py-1 ${
              viewMode === "edit" ? "bg-blue-500 text-white" : "bg-gray-100"
            }`}
            onClick={() => setViewMode("edit")}
          >
            Edit
          </button>
          <button
            className={`px-3 py-1 ${
              viewMode === "preview" ? "bg-blue-500 text-white" : "bg-gray-100"
            }`}
            onClick={() => setViewMode("preview")}
          >
            Preview
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center ml-4">
          <button
            className="p-1 bg-gray-100 rounded"
            onClick={() => setScale(Math.max(0.5, scale - 0.1))}
          >
            -
          </button>
          <span className="mx-2">{Math.round(scale * 100)}%</span>
          <button
            className="p-1 bg-gray-100 rounded"
            onClick={() => setScale(Math.min(2, scale + 0.1))}
          >
            +
          </button>
        </div>
      </div>

      {/* Page Navigation */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 p-2 bg-gray-100">
          <button
            className="p-1 bg-white rounded border border-gray-300 disabled:opacity-50"
            disabled={currentPage <= 1}
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          >
            ◀
          </button>
          <span>
            Page {currentPage} of {totalPages}
          </span>
          <button
            className="p-1 bg-white rounded border border-gray-300 disabled:opacity-50"
            disabled={currentPage >= totalPages}
            onClick={() =>
              setCurrentPage(Math.min(totalPages, currentPage + 1))
            }
          >
            ▶
          </button>
        </div>
      )}

      {/* Editor Content Area */}
      <div className="flex-grow overflow-auto bg-gray-100 p-4">
        <div
          className={`editor-container bg-white shadow-md mx-auto rounded-lg overflow-hidden transition-all`}
          style={{
            transform: `scale(${scale})`,
            transformOrigin: "top center",
            width: "816px", // Roughly 8.5" at 96dpi
            minHeight: "1056px", // Roughly 11" at 96dpi
            padding: "3rem",
          }}
        >
          {viewMode === "edit" ? (
            <>
              {editor && <EditorContent editor={editor} />}

              {/* Bubble menu for quick formatting */}
              {editor && (
                <BubbleMenu editor={editor} tippyOptions={{ duration: 100 }}>
                  <div className="flex bg-white shadow-lg rounded overflow-hidden border border-gray-200">
                    <button
                      onClick={() => editor.chain().focus().toggleBold().run()}
                      className={`p-1.5 ${
                        editor.isActive("bold") ? "bg-gray-200" : ""
                      }`}
                    >
                      B
                    </button>
                    <button
                      onClick={() =>
                        editor.chain().focus().toggleItalic().run()
                      }
                      className={`p-1.5 ${
                        editor.isActive("italic") ? "bg-gray-200" : ""
                      }`}
                    >
                      I
                    </button>
                    <button
                      onClick={() =>
                        editor.chain().focus().toggleUnderline().run()
                      }
                      className={`p-1.5 ${
                        editor.isActive("underline") ? "bg-gray-200" : ""
                      }`}
                    >
                      U
                    </button>
                  </div>
                </BubbleMenu>
              )}
            </>
          ) : (
            // Preview mode - display rendered HTML content
            <div
              className="preview-content prose max-w-none"
              dangerouslySetInnerHTML={{ __html: editorContent }}
            ></div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="p-4 border-t border-gray-200 bg-white flex justify-end space-x-3">
        {isDirty && (
          <button
            className="px-4 py-2 bg-red-50 text-red-600 rounded hover:bg-red-100"
            onClick={() => {
              if (editor && window.confirm("Discard changes?")) {
                editor.commands.setContent(section?.content || "");
                setIsDirty(false);
              }
            }}
          >
            Discard Changes
          </button>
        )}
        <button
          className="px-6 py-2 bg-blue-500 text-white font-medium rounded hover:bg-blue-600 disabled:opacity-50"
          onClick={handleSave}
          disabled={!isDirty}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default DocumentEditor;
