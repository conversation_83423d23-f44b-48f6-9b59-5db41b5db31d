import { databaseService } from '../pseo/databaseService';
import { PSEO_CONSTANTS } from '../../utilities/pseo/constants';
import type { AuditProgress } from '../../types';

class AuditProgressService {
  /**
   * Create progress object
   */
  createProgress(
    auditId: string,
    currentStep: string,
    completedSteps: string[],
    hasError: boolean = false,
    errorMessage?: string
  ): AuditProgress {
    const totalSteps = Object.keys(PSEO_CONSTANTS.AUDIT_STEPS).length;
    const progressPercentage = Math.round((completedSteps.length / totalSteps) * 100);

    return {
      auditId,
      currentStep,
      completedSteps,
      totalSteps,
      isComplete: completedSteps.length === totalSteps && !hasError,
      hasError,
      errorMessage,
      progressPercentage,
    };
  }

  /**
   * Get audit progress from database
   */
  async getAuditProgress(auditId: string): Promise<AuditProgress> {
    try {
      const audit = await databaseService.getAuditById(auditId);
      if (!audit) {
        throw new Error(PSEO_CONSTANTS.ERROR_MESSAGES.AUDIT_NOT_FOUND);
      }

      const steps = await databaseService.getAuditSteps(auditId);
      const completedSteps = steps
        .filter(step => step.status === PSEO_CONSTANTS.STEP_STATUS.COMPLETED)
        .map(step => step.step_name);
      
      const runningStep = steps.find(step => step.status === PSEO_CONSTANTS.STEP_STATUS.RUNNING);
      const failedStep = steps.find(step => step.status === PSEO_CONSTANTS.STEP_STATUS.FAILED);

      const currentStep = runningStep?.step_name || 
                         (audit.status === PSEO_CONSTANTS.AUDIT_STATUS.COMPLETED ? 'Completed' : 'Pending');

      return this.createProgress(
        auditId,
        currentStep,
        completedSteps,
        !!failedStep,
        failedStep?.error_message || audit.error_message || undefined
      );
    } catch (error) {
      console.error(`Failed to get audit progress for ${auditId}:`, error);
      throw error;
    }
  }

  /**
   * Update audit step status
   */
  async updateAuditStep(
    auditId: string,
    stepName: string,
    status: 'pending' | 'running' | 'completed' | 'failed',
    errorMessage?: string,
    stepData?: Record<string, unknown>
  ): Promise<void> {
    try {
      await databaseService.updateAuditStepByName(auditId, stepName, {
        status,
        error_message: errorMessage,
        step_data: stepData || {},
        ...(status === PSEO_CONSTANTS.STEP_STATUS.COMPLETED && { completed_at: new Date().toISOString() }),
      });
    } catch (error) {
      console.error(`Failed to update audit step ${stepName}:`, error);
    }
  }

  /**
   * Calculate progress percentage
   */
  calculateProgressPercentage(completedSteps: number, totalSteps: number): number {
    return Math.round((completedSteps / totalSteps) * 100);
  }

  /**
   * Get step status summary
   */
  getStepStatusSummary(steps: Array<{ step_name: string; status: string }>): {
    pending: number;
    running: number;
    completed: number;
    failed: number;
  } {
    return steps.reduce((summary, step) => {
      switch (step.status) {
        case PSEO_CONSTANTS.STEP_STATUS.PENDING:
          summary.pending++;
          break;
        case PSEO_CONSTANTS.STEP_STATUS.RUNNING:
          summary.running++;
          break;
        case PSEO_CONSTANTS.STEP_STATUS.COMPLETED:
          summary.completed++;
          break;
        case PSEO_CONSTANTS.STEP_STATUS.FAILED:
          summary.failed++;
          break;
      }
      return summary;
    }, { pending: 0, running: 0, completed: 0, failed: 0 });
  }
}

export const auditProgressService = new AuditProgressService(); 