import React from "react";
import { <PERSON> } from "react-router-dom";
import { Header } from "../components/Header";
import { <PERSON><PERSON> } from "@base/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function TermsOfService() {
  return (
    <>
      <Header />
      <div className="container max-w-4xl py-12">
        <Button variant="ghost" size="sm" asChild className="mb-6">
          <Link to="/landingpage">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
        </Button>

        <h1 className="text-3xl font-bold mb-8">Terms of Service</h1>

        <div className="prose prose-slate max-w-none">
          <h2>1. Acceptance of Terms</h2>
          <p>
            By accessing or using ScopingAI's website and services, you agree to
            be bound by these Terms of Service. If you do not agree to these
            terms, please do not use our services.
          </p>

          <h2>2. Description of Services</h2>
          <p>
            ScopingAI provides tools for creating, managing, and enhancing
            scoping documents and proposals for software development and related
            projects. Our services include AI-powered document generation,
            content enhancement, and document management features.
          </p>

          <h2>3. User Accounts</h2>
          <p>
            To access certain features of our service, you must register for an
            account. You are responsible for maintaining the confidentiality of
            your account information and for all activities that occur under
            your account. You agree to:
          </p>
          <ul>
            <li>
              Provide accurate and complete information when creating your
              account
            </li>
            <li>Update your information to keep it current</li>
            <li>Safeguard your account credentials</li>
            <li>
              Notify us immediately of any unauthorized access to your account
            </li>
          </ul>

          <h2>4. User Content</h2>
          <p>
            You retain ownership of any content you upload, submit, or create
            using our services. By using our services, you grant us a
            non-exclusive, worldwide, royalty-free license to use, store, and
            process your content solely for the purpose of providing and
            improving our services.
          </p>

          <h2>5. Acceptable Use</h2>
          <p>When using our services, you agree not to:</p>
          <ul>
            <li>Violate any applicable laws or regulations</li>
            <li>Infringe upon the rights of others</li>
            <li>
              Submit or upload any content that is unlawful, harmful,
              threatening, abusive, or otherwise objectionable
            </li>
            <li>Interfere with or disrupt our services or servers</li>
            <li>
              Attempt to gain unauthorized access to any part of our services
            </li>
            <li>Use our services for any illegal or unauthorized purpose</li>
          </ul>

          <h2>6. Intellectual Property</h2>
          <p>
            Our services, including all content, features, and functionality,
            are owned by ScopingAI or our licensors and are protected by
            copyright, trademark, and other intellectual property laws. You may
            not reproduce, distribute, modify, or create derivative works from
            our content without our express permission.
          </p>

          <h2>7. Payment Terms</h2>
          <p>
            If you subscribe to a paid plan, you agree to pay all fees
            associated with your subscription. All subscription fees are
            non-refundable unless otherwise specified. We may change our fees at
            any time, but will provide notice before any changes take effect.
          </p>

          <h2>8. Termination</h2>
          <p>
            We may terminate or suspend your access to our services immediately,
            without prior notice or liability, for any reason, including if you
            breach these Terms. Upon termination, your right to use our services
            will cease immediately.
          </p>

          <h2>9. Disclaimer of Warranties</h2>
          <p>
            Our services are provided "as is" and "as available" without
            warranties of any kind, either express or implied. We do not
            guarantee that our services will be uninterrupted, secure, or
            error-free.
          </p>

          <h2>10. Limitation of Liability</h2>
          <p>
            To the maximum extent permitted by law, in no event shall ScopingAI
            be liable for any indirect, incidental, special, consequential, or
            punitive damages arising out of or relating to your use of our
            services.
          </p>

          <h2>11. Indemnification</h2>
          <p>
            You agree to indemnify and hold harmless ScopingAI and its officers,
            directors, employees, and agents from any claims, liabilities,
            damages, losses, and expenses arising out of or in any way connected
            with your use of our services or violation of these Terms.
          </p>

          <h2>12. Changes to Terms</h2>
          <p>
            We may modify these Terms at any time by posting the revised Terms
            on our website. Your continued use of our services after any such
            changes constitutes your acceptance of the new Terms.
          </p>

          <h2>13. Governing Law</h2>
          <p>
            These Terms shall be governed by and construed in accordance with
            the laws of the jurisdiction in which our company is registered,
            without regard to its conflict of law provisions.
          </p>

          <h2>14. Contact Us</h2>
          <p>
            If you have any questions about these Terms, please contact us at
            <EMAIL>.
          </p>

          <p className="mt-8 text-sm text-gray-500">
            Last Updated:{" "}
            {new Date().toLocaleDateString("en-US", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })}
          </p>
        </div>
      </div>
    </>
  );
}
