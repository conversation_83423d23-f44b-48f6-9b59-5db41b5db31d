import React, { useState, useEffect } from 'react';
import { databaseService } from '../../services/pseo/databaseService';
import type { PSEOClient, PSEOWebsite } from '../../types';

interface WebsiteSelectorProps {
  clients: PSEOClient[];
  selectedWebsite: PSEOWebsite | null;
  onWebsiteSelect: (website: PSEOWebsite | null) => void;
  loading: boolean;
}

export const WebsiteSelector: React.FC<WebsiteSelectorProps> = ({
  clients,
  selectedWebsite,
  onWebsiteSelect,
  loading
}) => {
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [websites, setWebsites] = useState<PSEOWebsite[]>([]);
  const [websitesLoading, setWebsitesLoading] = useState(false);

  useEffect(() => {
    if (selectedClientId) {
      loadWebsites();
    } else {
      setWebsites([]);
      onWebsiteSelect(null);
    }
  }, [selectedClientId]);

  const loadWebsites = async () => {
    if (!selectedClientId) return;

    try {
      setWebsitesLoading(true);
      const websitesData = await databaseService.getWebsitesByClientId(selectedClientId);
      setWebsites(websitesData);
      
      // Auto-select if there's only one website
      if (websitesData.length === 1) {
        onWebsiteSelect(websitesData[0]);
      }
    } catch (err) {
      console.error('Failed to load websites:', err);
    } finally {
      setWebsitesLoading(false);
    }
  };

  const handleClientChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedClientId(e.target.value);
  };

  const handleWebsiteChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const websiteId = e.target.value;
    const website = websites.find(w => w.id === websiteId) || null;
    onWebsiteSelect(website);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading clients...</div>
      </div>
    );
  }

  if (clients.length === 0) {
    return (
      <div className="text-center p-8 bg-muted rounded-lg">
        <p className="text-muted-foreground mb-4">No clients found</p>
        <p className="text-sm text-muted-foreground">
          You need to create a client and add a website first in Website Management
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Client Selection */}
      <div>
        <label htmlFor="client-select" className="block text-sm font-medium text-foreground mb-2">
          Select Client
        </label>
        <select
          id="client-select"
          value={selectedClientId}
          onChange={handleClientChange}
          className="w-full p-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent"
        >
          <option value="">Choose a client...</option>
          {clients.map((client) => (
            <option key={client.id} value={client.id}>
              {client.name} {client.company && `(${client.company})`}
            </option>
          ))}
        </select>
      </div>

      {/* Website Selection */}
      {selectedClientId && (
        <div>
          <label htmlFor="website-select" className="block text-sm font-medium text-foreground mb-2">
            Select Website
          </label>
          {websitesLoading ? (
            <div className="p-3 border border-border rounded-lg bg-muted text-muted-foreground">
              Loading websites...
            </div>
          ) : websites.length === 0 ? (
            <div className="p-3 border border-border rounded-lg bg-muted text-muted-foreground">
              No websites found for this client
            </div>
          ) : (
            <select
              id="website-select"
              value={selectedWebsite?.id || ''}
              onChange={handleWebsiteChange}
              className="w-full p-3 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">Choose a website...</option>
              {websites.map((website) => (
                <option key={website.id} value={website.id}>
                  {website.name} - {website.domain}
                </option>
              ))}
            </select>
          )}
        </div>
      )}

      {/* Selected Website Preview */}
      {selectedWebsite && (
        <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="text-2xl">🌐</div>
            <div>
              <h3 className="font-semibold text-foreground">{selectedWebsite.name}</h3>
              <p className="text-sm text-muted-foreground">{selectedWebsite.url}</p>
              <p className="text-xs text-muted-foreground">
                Added {new Date(selectedWebsite.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 