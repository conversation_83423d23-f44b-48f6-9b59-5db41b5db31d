import { BaseEmbedding, Document } from 'llamaindex';
import { CohereClient } from 'cohere-ai';
import { environment } from '../../config/environment';

export class CohereEmbeddings implements BaseEmbedding {
  private client: CohereClient;
  public embedBatchSize: number = 96;
  public similarity: 'cosine' | 'euclidean' | 'dot_product' = 'cosine';

  constructor() {
    if (!process.env.COHERE_API_KEY) {
      throw new Error('COHERE_API_KEY environment variable is required');
    }
    this.client = new CohereClient({ token: process.env.COHERE_API_KEY });
  }

  async getQueryEmbedding(query: string): Promise<number[]> {
    const response = await this.client.embed({
      texts: [query],
      model: 'embed-english-v3.0',
      inputType: 'search_query'
    });
    return response.embeddings[0];
  }

  async getTextEmbeddingsBatch(texts: string[]): Promise<number[][]> {
    const response = await this.client.embed({
      texts,
      model: 'embed-english-v3.0',
      inputType: 'search_document'
    });
    return response.embeddings;
  }

  async transform(documents: Document[]): Promise<Document[]> {
    // No transformation needed for Cohere embeddings
    return documents;
  }
} 