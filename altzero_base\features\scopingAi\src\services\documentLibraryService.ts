import { supabase } from "../lib/supabase";
import {
  ProposalTemplate,
  LibraryDocument,
  DocumentData,
  ProcessedSection,
} from "../types/document-library";

// Fetch all proposal templates for the current user
export const fetchProposalTemplates = async (): Promise<LibraryDocument[]> => {
  try {
    const { data, error } = await supabase
      .from("scopingai_proposaltemplates")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    if (!data) {
      return [];
    }

    // Transform data to match the expected format
    const formattedTemplates = await Promise.all(
      data.map(async (template) => {
        // Initialize content variables
        let templateContent = "";
        let sections: ProcessedSection[] = [];
        let markdown_path = template.markdown_path || "";

        // Try to get the markdown content
        if (template.markdown_path) {
          try {
            const { data: markdownData, error: markdownError } =
              await supabase.storage
                .from("proposaltemplates")
                .download(template.markdown_path);

            if (markdownError) {
              console.warn("Error fetching markdown:", markdownError);
            } else if (markdownData) {
              // Successfully retrieved markdown
              templateContent = await markdownData.text();

              // Parse markdown content to extract sections
              const markdownSections =
                extractSectionsFromMarkdown(templateContent);
              if (markdownSections.length > 0) {
                sections = markdownSections;
              }
            }
          } catch (fileError) {
            console.error("Error processing markdown file:", fileError);
          }
        }

        // Check for any image references in the content and resolve them
        const imageRegex = /!\[.*?\]\((.*?)\)/g;
        let match;
        const imagePromises = [];

        while ((match = imageRegex.exec(templateContent)) !== null) {
          const imagePath = match[1];
          if (
            !imagePath.startsWith("data:") &&
            !imagePath.startsWith("http") &&
            !imagePath.includes(";base64,") && // Extra check for base64 content
            imagePath.length < 1000 // Skip very long paths that are likely base64 data
          ) {
            // It's a relative path, try to get the image from storage
            const fullImagePath = imagePath.includes("/")
              ? imagePath
              : `${template.id}/${imagePath}`;
            imagePromises.push(
              supabase.storage
                .from("proposaltemplates")
                .createSignedUrl(fullImagePath, 60 * 60) // 1 hour expiry
                .then(({ data }) => {
                  if (data) {
                    // Replace the image path with the signed URL
                    templateContent = templateContent.replace(
                      imagePath,
                      data.signedUrl
                    );
                  }
                })
                .catch((err) =>
                  console.warn(
                    `Could not sign URL for image: ${imagePath}`,
                    err
                  )
                )
            );
          }
        }

        if (imagePromises.length > 0) {
          await Promise.all(imagePromises);
        }

        return {
          id: template.id,
          title: template.name,
          client: template.client_name || "No client specified",
          date: new Date(template.created_at).toLocaleDateString(),
          type: template.description?.includes(":")
            ? template.description.split(":")[0]
            : "Document",
          sections:
            sections.length > 0
              ? sections.map(
                  (s: ProcessedSection) => s.title || "Untitled Section"
                )
              : ["No sections available"],
          rawData: {
            content: templateContent,
            sections: sections,
            metadata: {
              author: template.author || "Unknown Author",
              createdDate: template.created_at || "Unknown Date",
              modifiedDate: template.updated_at || "Unknown Date",
              pageCount: sections.length,
              fileType: template.description?.includes(":")
                ? template.description.split(":")[0]
                : "Document",
            },
          },
          markdown_path: markdown_path,
        };
      })
    );

    return formattedTemplates;
  } catch (error) {
    console.error("Error fetching proposal templates:", error);
    throw error;
  }
};

// Make a document public (convert to template)
export const makeDocumentPublic = async (docId: string): Promise<void> => {
  const { error } = await supabase
    .from("proposal_templates")
    .update({ is_public: true })
    .eq("id", docId);

  if (error) {
    console.error("Error making document public:", error);
    throw error;
  }
};

// Save document to Supabase storage and database
export const saveDocumentToStorage = async (
  document: DocumentData
): Promise<void> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session?.user?.id) {
      throw new Error("User not authenticated");
    }

    // Create a unique file path for the bucket
    const basePath = `${session.user.id}/${Date.now()}_${document.title.replace(
      /[^a-zA-Z0-9]/g,
      "_"
    )}`;
    const markdownPath = `${basePath}.md`;

    // Upload the markdown content
    const { error: uploadError } = await supabase.storage
      .from("proposaltemplates")
      .upload(markdownPath, document.content, {
        contentType: "text/markdown",
      });

    if (uploadError) {
      throw uploadError;
    }

    // Save to database
    const { error: dbError } = await supabase
      .from("proposal_templates")
      .insert({
        name: document.title,
        description: document.metadata.fileType || "Document",
        author: document.metadata.author || session.user.email || "Unknown",
        markdown_path: markdownPath,
        client_name: "Unknown Client",
        is_public: false,
        user_id: session.user.id,
      });

    if (dbError) {
      throw dbError;
    }
  } catch (error) {
    console.error("Error saving document to storage:", error);
    throw error;
  }
};

// Fetch markdown content from storage
export const fetchMarkdownContent = async (markdownPath: string) => {
  try {
    console.log("Fetching markdown from path:", markdownPath);

    // Fetch the markdown file from Supabase storage
    const { data: markdownData, error: markdownError } = await supabase.storage
      .from("proposaltemplates")
      .download(markdownPath);

    if (markdownError) {
      console.error("Error fetching markdown:", markdownError);
      throw markdownError;
    }

    if (!markdownData) {
      throw new Error("No markdown data found");
    }

    // Read the markdown content
    const markdownContent = await markdownData.text();
    console.log("Markdown content loaded successfully");

    // Process image references in markdown content to generate signed URLs
    let processedContent = markdownContent;

    // Find all image references in the markdown content
    const imageRegex = /!\[.*?\]\((.*?)\)/g;
    let match;
    let imagePaths = [];

    // Extract all image paths first
    while ((match = imageRegex.exec(markdownContent)) !== null) {
      const imagePath = match[1];
      // Check for valid image paths, exclude base64 data and URLs
      if (
        imagePath &&
        !imagePath.startsWith("data:") &&
        !imagePath.startsWith("http") &&
        !imagePath.includes(";base64,") &&
        imagePath.length < 1000 // Avoid base64 data
      ) {
        imagePaths.push({
          fullMatch: match[0],
          path: imagePath,
        });
      }
    }

    // Process each image path to create signed URLs
    if (imagePaths.length > 0) {
      for (const imageRef of imagePaths) {
        try {
          const fullImagePath = imageRef.path;
          console.log(`Processing image: ${fullImagePath}`);

          // Try to create a signed URL with 1-hour expiry
          const { data, error } = await supabase.storage
            .from("proposaltemplates")
            .createSignedUrl(fullImagePath, 60 * 60);

          if (error) {
            console.error(
              `Error creating signed URL for ${fullImagePath}:`,
              error
            );
            continue;
          }

          if (data?.signedUrl) {
            // Replace the original image reference with the signed URL
            processedContent = processedContent.replace(
              imageRef.fullMatch,
              imageRef.fullMatch.replace(imageRef.path, data.signedUrl)
            );
          }
        } catch (imageError) {
          console.error(`Error processing image ${imageRef.path}:`, imageError);
        }
      }
    }

    // Parse the markdown content into sections
    const sections = extractSectionsFromMarkdown(processedContent);

    return {
      content: processedContent,
      sections: sections,
    };
  } catch (error) {
    console.error("Error fetching markdown content:", error);
    throw error;
  }
};

// Simple markdown section extraction
const extractSectionsFromMarkdown = (markdown: string): ProcessedSection[] => {
  const lines = markdown.split("\n");
  const sections: ProcessedSection[] = [];
  let currentSection: ProcessedSection | null = null;
  let currentContent: string[] = [];

  for (const line of lines) {
    if (line.startsWith("## ")) {
      // Save previous section
      if (currentSection) {
        currentSection.content = currentContent.join("\n").trim();
        sections.push(currentSection);
      }

      // Start new section
      currentSection = {
        title: line.replace("## ", "").trim(),
        content: "",
        order: sections.length,
      };
      currentContent = [];
    } else if (currentSection) {
      currentContent.push(line);
    }
  }

  // Save the last section
  if (currentSection) {
    currentSection.content = currentContent.join("\n").trim();
    sections.push(currentSection);
  }

  return sections;
};
