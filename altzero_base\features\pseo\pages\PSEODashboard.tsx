import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/pseo/databaseService';
import { useUser } from '../../../base/contextapi/UserContext';
import type { PSEOClient, PSEOWebsite, PSEOAudit } from '../types';
import { supabase } from '../../../base/utils/supabaseClient';
import PSEOLayout from '../components/PSEOLayout';

const PSEODashboard: React.FC = () => {
  const { user } = useUser();
  const [stats, setStats] = useState({
    totalClients: 0,
    totalWebsites: 0,
    totalAudits: 0,
    totalAnalysisJobs: 0,
    loading: true
  });
  
  const [recentAudits, setRecentAudits] = useState<PSEOAudit[]>([]);
  const [auditsLoading, setAuditsLoading] = useState(true);
  
  const [recentAnalysisJobs, setRecentAnalysisJobs] = useState<any[]>([]);
  const [analysisLoading, setAnalysisLoading] = useState(true);
  
  const [fullSiteAnalysisResults, setFullSiteAnalysisResults] = useState<any[]>([]);
  const [fullSiteLoading, setFullSiteLoading] = useState(true);
  
  const [contentOpportunities, setContentOpportunities] = useState<any[]>([]);
  const [contentLoading, setContentLoading] = useState(true);
  
  const [keywordResearch, setKeywordResearch] = useState<any[]>([]);
  const [keywordLoading, setKeywordLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadStats();
      loadRecentAudits();
      loadRecentAnalysisJobs();
      loadFullSiteAnalysisResults();
      loadContentOpportunities();
      loadKeywordResearch();
    }
  }, [user?.id]);

  const loadStats = async () => {
    if (!user?.id) return;

    try {
      // Use the optimized client dashboard view for better performance
      const { data: clientDashboard, error: clientError } = await supabase
        .from('pseo_client_dashboard')
        .select('*')
        .eq('user_id', user.id);

      if (clientError) {
        console.warn('Client dashboard view failed, falling back to individual queries:', clientError);
        // Fallback to the existing approach
        await loadStatsFallback();
        return;
      }

      // Calculate stats from the dashboard view (much more efficient)
      const totalClients = clientDashboard?.length || 0;
      const totalWebsites = clientDashboard?.reduce((sum: number, client: any) => sum + (client.website_count || 0), 0) || 0;
      const totalAudits = clientDashboard?.reduce((sum: number, client: any) => sum + (client.total_audits || 0), 0) || 0;

      // Get total analysis jobs count
      let totalAnalysisJobs = 0;
      try {
        const { data: analysisJobs, error: jobsError } = await supabase
          .from('pseo_agent_jobs')
          .select('id', { count: 'exact' })
          .limit(1);
        
        if (!jobsError) {
          totalAnalysisJobs = analysisJobs?.length || 0;
        }
      } catch (err) {
        console.warn('Could not get analysis jobs count:', err);
      }

      setStats({
        totalClients,
        totalWebsites,
        totalAudits,
        totalAnalysisJobs,
        loading: false
      });
    } catch (err) {
      console.error('Failed to load stats from dashboard view:', err);
      await loadStatsFallback();
    }
  };

  const loadStatsFallback = async () => {
    if (!user?.id) return;
    
    try {
      // Fallback: Get basic data efficiently
      const [clients, recentAuditsData] = await Promise.all([
        databaseService.getClientsByUserId(user.id),
        databaseService.getAuditsByUserId(user.id, 20) // Get more for better count estimate
      ]);
      
      let totalWebsites = 0;
      const totalAudits = recentAuditsData.length;
      let totalAnalysisJobs = 0;

      // Only get website counts if we have few clients (to avoid many queries)
      if (clients.length <= 5) {
        const websitePromises = clients.map(client => 
          databaseService.getWebsitesByClientId(client.id)
        );
        
        const websitesArrays = await Promise.all(websitePromises);
        totalWebsites = websitesArrays.reduce((sum, websites) => sum + websites.length, 0);
      } else {
        // For many clients, estimate based on average
        totalWebsites = Math.round(clients.length * 2.5); // Rough estimate
      }

      // Get analysis jobs count
      try {
        const { count } = await supabase
          .from('pseo_agent_jobs')
          .select('*', { count: 'exact', head: true });
        totalAnalysisJobs = count || 0;
      } catch (err) {
        console.warn('Could not get analysis jobs count:', err);
      }

      setStats({
        totalClients: clients.length,
        totalWebsites,
        totalAudits,
        totalAnalysisJobs,
        loading: false
      });
    } catch (err) {
      console.error('Failed to load stats:', err);
      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  const loadRecentAudits = async () => {
    if (!user?.id) return;

    try {
      setAuditsLoading(true);
      const audits = await databaseService.getAuditsByUserId(user.id, 5);
      setRecentAudits(audits);
    } catch (err) {
      console.error('Failed to load recent audits:', err);
    } finally {
      setAuditsLoading(false);
    }
  };

  const loadRecentAnalysisJobs = async () => {
    if (!user?.id) return;

    try {
      setAnalysisLoading(true);
      
      // Get clients for this user
      const clients = await databaseService.getClientsByUserId(user.id);
      if (clients.length === 0) {
        setRecentAnalysisJobs([]);
        return;
      }

      // Get websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsites = websitesArrays.flat();
      const websiteIds = allWebsites.map(w => w.id);

      if (websiteIds.length === 0) {
        setRecentAnalysisJobs([]);
        return;
      }

      // Get recent analysis jobs for these websites
      const { data: analysisJobs, error } = await supabase
        .from('pseo_agent_jobs')
        .select('*')
        .in('website_id', websiteIds)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Failed to load analysis jobs:', error);
        setRecentAnalysisJobs([]);
        return;
      }

      // Enhance with website and client info
      const enhancedJobs = (analysisJobs || []).map(job => {
        const website = allWebsites.find(w => w.id === job.website_id);
        const client = clients.find(c => c.id === website?.client_id);
        
        return {
          ...job,
          website_name: website?.name || 'Unknown Website',
          website_url: website?.url || '',
          client_name: client?.name || 'Unknown Client',
          website
        };
      });

      setRecentAnalysisJobs(enhancedJobs);
    } catch (err) {
      console.error('Failed to load recent analysis jobs:', err);
      setRecentAnalysisJobs([]);
    } finally {
      setAnalysisLoading(false);
    }
  };

  const loadFullSiteAnalysisResults = async () => {
    if (!user?.id) return;

    try {
      setFullSiteLoading(true);
      
      // Get clients for this user
      const clients = await databaseService.getClientsByUserId(user.id);
      if (clients.length === 0) {
        setFullSiteAnalysisResults([]);
        return;
      }

      // Get websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsites = websitesArrays.flat();
      const websiteIds = allWebsites.map(w => w.id);

      if (websiteIds.length === 0) {
        setFullSiteAnalysisResults([]);
        return;
      }

      // Get completed full site analysis jobs
      const { data: completedJobs, error } = await supabase
        .from('pseo_agent_jobs')
        .select('*')
        .in('website_id', websiteIds)
        .eq('job_type', 'full_site_audit')
        .eq('status', 'completed')
        .order('completed_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Failed to load full site analysis jobs:', error);
        setFullSiteAnalysisResults([]);
        return;
      }

      // For each completed job, get the detailed analysis results
      const enhancedResults = await Promise.all(
        (completedJobs || []).map(async (job) => {
          const website = allWebsites.find(w => w.id === job.website_id);
          const client = clients.find(c => c.id === website?.client_id);
          
          // Get analysis data for this website
          const [pages, keywords, contentOpportunities, generatedContent] = await Promise.all([
            databaseService.getWebsitePages(job.website_id, { limit: 1000 }),
            databaseService.getKeywordResearch(job.website_id, { limit: 1000 }),
            databaseService.getContentOpportunities(job.website_id, { limit: 1000 }),
            databaseService.getGeneratedContentItems(job.website_id, { limit: 1000 })
          ]);

          return {
            ...job,
            website_name: website?.name || 'Unknown Website',
            website_url: website?.url || '',
            client_name: client?.name || 'Unknown Client',
            website,
            analysis_summary: {
              pages_discovered: pages.length,
              keywords_found: keywords.length,
              content_opportunities: contentOpportunities.length,
              generated_content: generatedContent.length,
              high_priority_opportunities: contentOpportunities.filter(opp => opp.priority === 'high').length,
              estimated_traffic: contentOpportunities.reduce((sum, opp) => sum + (opp.estimated_traffic || 0), 0)
            }
          };
        })
      );

      setFullSiteAnalysisResults(enhancedResults);
    } catch (err) {
      console.error('Failed to load full site analysis results:', err);
      setFullSiteAnalysisResults([]);
    } finally {
      setFullSiteLoading(false);
    }
  };

  const loadContentOpportunities = async () => {
    if (!user?.id) return;

    try {
      setContentLoading(true);
      
      // Get clients for this user
      const clients = await databaseService.getClientsByUserId(user.id);
      if (clients.length === 0) {
        setContentOpportunities([]);
        return;
      }

      // Get websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsites = websitesArrays.flat();
      const websiteIds = allWebsites.map(w => w.id);

      if (websiteIds.length === 0) {
        setContentOpportunities([]);
        return;
      }

      // Get content opportunities
      const { data: contentOpps, error } = await supabase
        .from('pseo_content_opportunities')
        .select('*')
        .in('website_id', websiteIds)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Failed to load content opportunities:', error);
        setContentOpportunities([]);
        return;
      }

      // Enhance with website and client info
      const enhancedOpps = (contentOpps || []).map(opp => {
        const website = allWebsites.find(w => w.id === opp.website_id);
        const client = clients.find(c => c.id === website?.client_id);
        
        return {
          ...opp,
          website_name: website?.name || 'Unknown Website',
          website_url: website?.url || '',
          client_name: client?.name || 'Unknown Client',
          website
        };
      });

      setContentOpportunities(enhancedOpps);
    } catch (err) {
      console.error('Failed to load content opportunities:', err);
      setContentOpportunities([]);
    } finally {
      setContentLoading(false);
    }
  };

  const loadKeywordResearch = async () => {
    if (!user?.id) return;

    try {
      setKeywordLoading(true);
      
      // Get clients for this user
      const clients = await databaseService.getClientsByUserId(user.id);
      if (clients.length === 0) {
        setKeywordResearch([]);
        return;
      }

      // Get websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsites = websitesArrays.flat();
      const websiteIds = allWebsites.map(w => w.id);

      if (websiteIds.length === 0) {
        setKeywordResearch([]);
        return;
      }

      // Get keyword research
      const { data: keywords, error } = await supabase
        .from('pseo_keywords')
        .select('*')
        .in('website_id', websiteIds)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Failed to load keyword research:', error);
        setKeywordResearch([]);
        return;
      }

      // Enhance with website and client info
      const enhancedKeywords = (keywords || []).map(keyword => {
        const website = allWebsites.find(w => w.id === keyword.website_id);
        const client = clients.find(c => c.id === website?.client_id);
        
        return {
          ...keyword,
          website_name: website?.name || 'Unknown Website',
          website_url: website?.url || '',
          client_name: client?.name || 'Unknown Client',
          website
        };
      });

      setKeywordResearch(enhancedKeywords);
    } catch (err) {
      console.error('Failed to load keyword research:', err);
      setKeywordResearch([]);
    } finally {
      setKeywordLoading(false);
    }
  };

  const getAnalysisTypeIcon = (jobType: string, agentName: string) => {
    if (jobType === 'full_site_audit' || agentName === 'AgentOrchestrator') return '🚀';
    if (agentName === 'PageDiscoveryAgent') return '🔍';
    if (agentName === 'KeywordResearchAgent') return '🎯';
    if (agentName === 'ContentGenerationAgent') return '📝';
    if (agentName === 'BacklinkAnalysisAgent') return '🔗';
    return '🤖';
  };

  const getAnalysisResultsLink = (job: any) => {
    if (job.status !== 'completed' || !job.website) return null;
    
    const websiteId = job.website.id;
    
    // For full site analysis, show the main analysis page
    if (job.job_type === 'full_site_audit' || job.agent_name === 'AgentOrchestrator') {
      return `/full-site-analysis`;
    }
    
    // For specific agent types, link to their results pages
    switch (job.agent_name) {
      case 'PageDiscoveryAgent':
        return `/page-discovery-results/${websiteId}`;
      case 'KeywordResearchAgent':
        return `/keyword-research-results/${websiteId}`;
      case 'ContentGenerationAgent':
        return `/content-generation-results/${websiteId}`;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">

          {/* Getting Started */}
          {stats.totalClients === 0 && !stats.loading && (
            <div className="mt-8 bg-accent/50 rounded-lg border p-6">
              <h2 className="text-xl font-semibold mb-4">🚀 Getting Started</h2>
              <p className="text-muted-foreground mb-4">
                Welcome to pSEO! To get started with your first SEO audit, follow these steps:
              </p>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>
                  <a href="/website-management" className="text-primary hover:underline">
                    Create your first client and add a website
                  </a>
                </li>
                <li>
                  <a href="/full-site-analysis" className="text-primary hover:underline">
                    Run your first full site analysis
                  </a>
                </li>
                <li>
                  <a href="/create-blog-post" className="text-primary hover:underline">
                    Generate your first AI blog post
                  </a>
                </li>
              </ol>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            {/* Recent AI Analysis Results */}
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                🤖 Recent AI Analysis Jobs
              </h2>
              {analysisLoading ? (
                <div className="text-center text-muted-foreground">Loading analysis results...</div>
              ) : recentAnalysisJobs.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <div className="text-4xl mb-2">🤖</div>
                  <p>No AI analysis results yet.</p>
                  <a href="/full-site-analysis" className="text-primary hover:underline text-sm">
                    Start your first AI analysis →
                  </a>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentAnalysisJobs.slice(0, 5).map((job) => (
                    <div key={job.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="text-xl">{getAnalysisTypeIcon(job.job_type, job.agent_name)}</div>
                        <div>
                          <p className="text-sm font-medium text-foreground">{job.website_name}</p>
                          <p className="text-xs text-muted-foreground">{job.client_name}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-xs text-muted-foreground">
                          {formatDate(job.created_at)}
                        </div>
                        {job.status === 'completed' && getAnalysisResultsLink(job) && (
                          <a 
                            href={getAnalysisResultsLink(job)}
                            className="text-xs text-primary hover:underline font-medium"
                          >
                            View Results →
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Content Opportunities */}
            <div className="bg-card rounded-lg border p-6">
              <h2 className="text-xl font-semibold mb-4">📝 Content Opportunities</h2>
              {contentLoading ? (
                <div className="text-center text-muted-foreground">Loading content opportunities...</div>
              ) : contentOpportunities.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <div className="text-4xl mb-2">📝</div>
                  <p>No content opportunities found.</p>
                  <a href="/keyword-research" className="text-primary hover:underline text-sm">
                    Discover content opportunities →
                  </a>
                </div>
              ) : (
                <div className="space-y-4">
                  {contentOpportunities.map((opp) => (
                    <div key={opp.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="text-xl">📝</div>
                        <div>
                          <p className="text-sm font-medium text-foreground">{opp.keyword || opp.title}</p>
                          <p className="text-xs text-muted-foreground">{opp.website_name}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-xs text-muted-foreground">
                          {formatDate(opp.created_at)}
                        </div>
                        <a 
                          href={`/content-opportunity/${opp.id}`}
                          className="text-xs text-primary hover:underline font-medium"
                        >
                          View →
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        </div>
    </PSEOLayout>
  );
};

export default PSEODashboard; 
