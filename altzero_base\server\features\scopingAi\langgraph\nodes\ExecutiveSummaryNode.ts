// =====================================================
// EXECUTIVE SUMMARY NODE - SCOPINGAI LANGGRAPH
// =====================================================

import { BaseNode, ExecutiveSummaryNodeResult } from '../types/NodeTypes';
import { ScopingAiWorkflowState, WorkflowContext } from '../types/WorkflowState';

export class ExecutiveSummaryNode implements BaseNode {
  name = 'executive_summary';
  description = 'Generates compelling executive summary for the proposal';

  async execute(context: WorkflowContext): Promise<Partial<ScopingAiWorkflowState>> {
    const { state, tools, logger } = context;
    
    logger.info('Starting executive summary generation', {
      workflow_id: state.workflow_id,
      has_research: !!(state.research_analysis?.content),
      client_name: state.client?.name
    });

    try {
      const startTime = Date.now();
      
      // Generate executive summary
      const summaryResult = await this.generateExecutiveSummary(state, tools, logger);
      
      const processingTime = Date.now() - startTime;

      logger.info('Executive summary completed', {
        word_count: summaryResult.word_count,
        key_points: summaryResult.key_points.length,
        readability: summaryResult.readability_score
      });

      return {
        executive_summary: {
          content: summaryResult.content,
          key_points: summaryResult.key_points,
          value_proposition: summaryResult.value_proposition
        },
        current_step: 'executive_summary_completed',
        progress: 65,
        processing_time: (state.processing_time || 0) + processingTime,
        api_calls_made: [
          ...(state.api_calls_made || []),
          {
            provider: 'ai_generation',
            endpoint: 'executive_summary',
            calls_made: 2, // Summary + key points extraction
            success_rate: 1.0,
            average_response_time: processingTime / 2,
            cost_estimate: 0.20,
            timestamp: new Date().toISOString()
          }
        ],
        last_updated: new Date().toISOString(),
        node_data: {
          ...state.node_data,
          executive_summary: summaryResult
        }
      };

    } catch (error) {
      logger.error('Executive summary generation failed', error);
      throw error;
    }
  }

  private async generateExecutiveSummary(
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<ExecutiveSummaryNodeResult> {
    
    // Create executive summary prompt
    const summaryPrompt = this.createSummaryPrompt(state);
    
    // Generate the executive summary
    const summaryContent = await tools.ai.generateText(summaryPrompt, {
      model: 'gpt-4o-mini',
      temperature: 0.6,
      max_tokens: 1500
    });

    // Extract key points
    const keyPoints = await this.extractKeyPoints(summaryContent, tools, logger);
    
    // Extract value proposition
    const valueProposition = await this.extractValueProposition(summaryContent, state, tools, logger);
    
    // Calculate metrics
    const wordCount = summaryContent.split(/\s+/).length;
    const readabilityScore = this.calculateReadabilityScore(summaryContent);

    return {
      content: summaryContent,
      key_points: keyPoints,
      value_proposition: valueProposition,
      word_count: wordCount,
      readability_score: readabilityScore
    };
  }

  private createSummaryPrompt(state: ScopingAiWorkflowState): string {
    const client = state.client;
    const project = state.project;
    const researchAnalysis = state.research_analysis;
    const clientAnalysis = state.client_analysis;

    return `Create a compelling executive summary for a business proposal. This summary will be read by C-level executives and decision-makers.

CLIENT INFORMATION:
- Company: ${client?.name || 'Unknown'}
- Industry: ${client?.industry || 'Unknown'}
- Size: ${client?.size || 'Unknown'}
- Location: ${client?.location || 'Unknown'}

PROJECT OVERVIEW:
- Title: ${project?.title || 'Unknown'}
- Description: ${project?.description || 'No description provided'}
- Timeline: ${project?.timeline || 'To be determined'}
- Budget Range: ${project?.budget_range || 'To be discussed'}

${researchAnalysis ? `
RESEARCH INSIGHTS:
${researchAnalysis.content}

KEY FINDINGS:
${researchAnalysis.key_findings?.join('\n- ') || 'None available'}

STRATEGIC RECOMMENDATIONS:
${researchAnalysis.recommendations?.join('\n- ') || 'None available'}
` : ''}

${clientAnalysis ? `
CLIENT ANALYSIS:
- Market Position: ${clientAnalysis.market_position}
- Key Challenges: ${clientAnalysis.key_challenges?.join(', ') || 'None identified'}
- Opportunities: ${clientAnalysis.opportunities?.join(', ') || 'None identified'}
` : ''}

EXECUTIVE SUMMARY REQUIREMENTS:
1. Professional tone suitable for C-level executives
2. Clear value proposition and business impact
3. Strategic alignment with client's objectives
4. Compelling case for why this project is essential
5. Brief overview of our approach and capabilities
6. Expected outcomes and benefits
7. Call to action for next steps

WRITING GUIDELINES:
- Keep it concise but comprehensive (400-600 words)
- Lead with the most compelling business case
- Use executive-level language and terminology
- Focus on business outcomes, not technical details
- Include specific benefits and value drivers
- End with a strong call to action

STYLE GUIDANCE: ${state.ai_prompts?.style_guidance || 'Professional, confident, and results-focused'}

Create an executive summary that positions this proposal as a strategic imperative for the client's success.`;
  }

  private async extractKeyPoints(
    summaryContent: string,
    tools: any,
    logger: any
  ): Promise<string[]> {
    try {
      const prompt = `Extract 4-6 key points from the following executive summary. These should be the most important takeaways that executives would remember.

Executive Summary:
${summaryContent}

Provide the key points as a JSON array of strings. Each point should be concise but impactful.`;

      const response = await tools.ai.generateStructuredData(prompt, {
        type: 'array',
        items: { type: 'string' }
      });

      return Array.isArray(response) ? response : response.key_points || [];
    } catch (error) {
      logger.warn('Key points extraction failed, using fallback', error);
      return [
        'Strategic initiative aligned with business objectives',
        'Proven approach with measurable outcomes',
        'Competitive advantage through innovation',
        'Strong ROI and business value creation',
        'Expert team with industry experience'
      ];
    }
  }

  private async extractValueProposition(
    summaryContent: string,
    state: ScopingAiWorkflowState,
    tools: any,
    logger: any
  ): Promise<string> {
    try {
      const prompt = `Extract the core value proposition from the following executive summary. This should be a single, compelling statement that captures why the client should choose this proposal.

Executive Summary:
${summaryContent}

Client Context:
- Company: ${state.client?.name}
- Industry: ${state.client?.industry}
- Project: ${state.project?.title}

Provide a concise value proposition (1-2 sentences) that clearly articulates the unique value we bring to this client.`;

      const response = await tools.ai.generateText(prompt, {
        temperature: 0.5,
        max_tokens: 200
      });

      return response.trim();
    } catch (error) {
      logger.warn('Value proposition extraction failed, using fallback', error);
      return `We deliver proven solutions that drive measurable business results for ${state.client?.industry || 'your industry'} leaders, combining deep expertise with innovative approaches to achieve your strategic objectives.`;
    }
  }

  private calculateReadabilityScore(content: string): number {
    // Simplified readability calculation (Flesch Reading Ease approximation)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    const words = content.split(/\s+/).length;
    const syllables = this.countSyllables(content);

    if (sentences === 0 || words === 0) return 0;

    const avgWordsPerSentence = words / sentences;
    const avgSyllablesPerWord = syllables / words;

    // Flesch Reading Ease formula (simplified)
    const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    
    // Convert to 0-100 scale where higher is better
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private countSyllables(text: string): number {
    // Simple syllable counting heuristic
    const words = text.toLowerCase().match(/[a-z]+/g) || [];
    let syllableCount = 0;

    for (const word of words) {
      // Count vowel groups
      const vowelGroups = word.match(/[aeiouy]+/g) || [];
      syllableCount += vowelGroups.length;
      
      // Adjust for silent 'e'
      if (word.endsWith('e') && vowelGroups.length > 1) {
        syllableCount--;
      }
      
      // Minimum of 1 syllable per word
      if (vowelGroups.length === 0) {
        syllableCount++;
      }
    }

    return syllableCount;
  }

  // Validate executive summary quality
  private validateSummaryQuality(content: string): { score: number; issues: string[] } {
    const issues: string[] = [];
    let score = 100;

    // Check length
    const wordCount = content.split(/\s+/).length;
    if (wordCount < 300) {
      issues.push('Summary is too short');
      score -= 20;
    } else if (wordCount > 800) {
      issues.push('Summary is too long for executive audience');
      score -= 10;
    }

    // Check for key elements
    if (!content.toLowerCase().includes('benefit')) {
      issues.push('Missing clear benefits statement');
      score -= 15;
    }

    if (!content.toLowerCase().includes('value')) {
      issues.push('Missing value proposition');
      score -= 15;
    }

    if (!content.toLowerCase().includes('recommend')) {
      issues.push('Missing recommendation or call to action');
      score -= 10;
    }

    // Check sentence structure
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = content.split(/\s+/).length / sentences.length;
    
    if (avgSentenceLength > 25) {
      issues.push('Sentences are too long for executive readability');
      score -= 10;
    }

    return { score: Math.max(0, score), issues };
  }

  // Generate alternative summary if quality is low
  private async generateAlternativeSummary(
    state: ScopingAiWorkflowState,
    tools: any,
    originalIssues: string[]
  ): Promise<string> {
    const improvedPrompt = `${this.createSummaryPrompt(state)}

QUALITY IMPROVEMENTS NEEDED:
${originalIssues.join('\n- ')}

Please address these specific issues in your response and ensure the summary meets executive standards for clarity, conciseness, and impact.`;

    return await tools.ai.generateText(improvedPrompt, {
      model: 'gpt-4o-mini',
      temperature: 0.5,
      max_tokens: 1500
    });
  }
}
