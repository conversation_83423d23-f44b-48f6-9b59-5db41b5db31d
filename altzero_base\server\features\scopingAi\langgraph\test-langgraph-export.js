// =====================================================
// TEST SCRIPT FOR LANGGRAPH STUDIO EXPORT
// =====================================================

const { proposalWorkflow, createProposalWorkflow } = require('./langGraphStudioExport.js');

async function testWorkflowExport() {
  console.log('🧪 Testing LangGraph Studio Export for ScopingAI Proposal Workflow');
  console.log('=' .repeat(70));

  try {
    // Test 1: Verify workflow creation
    console.log('\n1️⃣ Testing workflow creation...');
    const workflow = createProposalWorkflow();
    console.log('✅ Workflow created successfully');
    console.log(`   Type: ${typeof workflow}`);
    console.log(`   Has invoke method: ${typeof workflow.invoke === 'function'}`);

    // Test 2: Test with sample input
    console.log('\n2️⃣ Testing workflow execution with sample input...');
    
    const sampleInput = {
      workflow_id: 'test_workflow_' + Date.now(),
      user_id: 'test_user_123',
      client_id: 'test_client_456',
      client_data: {
        company_name: 'TechCorp Inc',
        industry: 'Technology',
        company_size: 'Medium',
        budget_range: '500000-750000',
        timeline: '6 months',
        decision_makers: ['CTO', 'CEO']
      },
      template_id: 'digital_transformation',
      requirements: [
        'Cloud migration strategy',
        'Process automation',
        'Team training program'
      ],
      knowledge_documents: [
        {
          document_id: 'doc_1',
          title: 'Cloud Migration Best Practices',
          custom_prompt: 'Focus on security and compliance aspects'
        }
      ],
      ai_prompts: [],
      config: {
        quality_threshold: 0.7,
        max_sections: 10,
        enable_market_research: true,
        enable_competitive_analysis: true,
        timeout_seconds: 600,
        retry_attempts: 3
      },
      started_at: new Date().toISOString()
    };

    console.log('📝 Sample input prepared:');
    console.log(`   Workflow ID: ${sampleInput.workflow_id}`);
    console.log(`   Client: ${sampleInput.client_data.company_name}`);
    console.log(`   Template: ${sampleInput.template_id}`);
    console.log(`   Requirements: ${sampleInput.requirements.length} items`);

    // Execute workflow
    console.log('\n🚀 Executing workflow...');
    const startTime = Date.now();
    
    const result = await workflow.invoke(sampleInput);
    
    const endTime = Date.now();
    const executionTime = endTime - startTime;

    console.log('✅ Workflow execution completed!');
    console.log(`   Execution time: ${executionTime}ms`);
    console.log(`   Final status: ${result.status}`);
    console.log(`   Progress: ${result.progress}%`);
    console.log(`   Current step: ${result.current_step}`);

    // Test 3: Verify output structure
    console.log('\n3️⃣ Verifying output structure...');
    
    const requiredFields = [
      'workflow_id',
      'status',
      'progress',
      'current_step',
      'last_updated'
    ];

    let allFieldsPresent = true;
    for (const field of requiredFields) {
      if (result[field] === undefined) {
        console.log(`❌ Missing required field: ${field}`);
        allFieldsPresent = false;
      } else {
        console.log(`✅ Field present: ${field} = ${result[field]}`);
      }
    }

    if (allFieldsPresent) {
      console.log('✅ All required fields present in output');
    }

    // Test 4: Check node execution data
    console.log('\n4️⃣ Checking node execution data...');
    
    if (result.node_data) {
      const executedNodes = Object.keys(result.node_data);
      console.log(`✅ Node data present for ${executedNodes.length} nodes:`);
      executedNodes.forEach(node => {
        console.log(`   - ${node}: ${JSON.stringify(result.node_data[node], null, 2).substring(0, 100)}...`);
      });
    } else {
      console.log('❌ No node execution data found');
    }

    // Test 5: Check final proposal structure
    console.log('\n5️⃣ Checking final proposal structure...');
    
    if (result.final_proposal) {
      console.log('✅ Final proposal generated:');
      console.log(`   Title: ${result.final_proposal.title}`);
      console.log(`   Sections: ${result.final_proposal.sections?.length || 0}`);
      console.log(`   Word count: ${result.final_proposal.metadata?.word_count || 'N/A'}`);
      console.log(`   Generated at: ${result.final_proposal.metadata?.generated_at || 'N/A'}`);
    } else {
      console.log('⚠️  No final proposal in result (may be expected if workflow failed)');
    }

    // Summary
    console.log('\n📊 Test Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ Workflow creation: PASSED`);
    console.log(`✅ Workflow execution: PASSED`);
    console.log(`✅ Output structure: ${allFieldsPresent ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Node data: ${result.node_data ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Final proposal: ${result.final_proposal ? 'PASSED' : 'PARTIAL'}`);
    console.log(`⏱️  Total execution time: ${executionTime}ms`);

    if (result.status === 'completed') {
      console.log('\n🎉 All tests PASSED! LangGraph Studio export is ready to use.');
      console.log('\n🚀 To start LangGraph Studio, run:');
      console.log('   npx @langchain/langgraph-cli dev --port 8123 --no-pull');
      console.log('\n🌐 Then open: http://localhost:8123');
    } else {
      console.log('\n⚠️  Workflow completed with status:', result.status);
      if (result.errors && result.errors.length > 0) {
        console.log('   Errors:');
        result.errors.forEach(error => {
          console.log(`   - ${error.node_name}: ${error.error_message}`);
        });
      }
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:');
    console.error(error);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Check that all dependencies are installed');
    console.log('2. Verify the langGraphStudioExport.js file is valid');
    console.log('3. Ensure @langchain/langgraph is installed');
    console.log('4. Check the console for detailed error messages');
  }
}

// Run the test
if (require.main === module) {
  testWorkflowExport().catch(console.error);
}

module.exports = { testWorkflowExport };
