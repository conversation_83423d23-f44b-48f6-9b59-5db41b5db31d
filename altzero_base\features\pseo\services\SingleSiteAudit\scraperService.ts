import { PSEO_CONSTANTS, API_ENDPOINTS } from '../../utilities/pseo/constants';
import type { ScrapedData, PSEOError } from '../../types';

export class WebsiteScraperService {
  private baseUrl: string;

  constructor() {
    // Get API URL from environment or use default based on current location
    let apiUrl = 'http://localhost:3001';
    
    // Try to get from Vite environment variables
    if (typeof window !== 'undefined') {
      // In browser, construct from current location
      const protocol = window.location.protocol;
      const hostname = window.location.hostname;
      apiUrl = `${protocol}//${hostname}:3001`;
    }
    
    this.baseUrl = apiUrl;
  }

  async scrapeWebsite(url: string): Promise<ScrapedData> {
    const startTime = Date.now();
    
    try {
      // Validate URL format
      this.validateUrl(url);

      console.log(`Scraping website via server: ${url}`);

      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.SCRAPE}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': 'altzero_base',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw this.createError(
          'HTTP_ERROR',
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          { statusCode: response.status, statusText: response.statusText, details: errorData }
        );
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        throw this.createError('INVALID_RESPONSE', 'Invalid response from scraping service');
      }

      return result.data;
      
    } catch (error: unknown) {
      if (error instanceof Error) {
        // Re-throw our custom errors
        if (error.message.includes('HTTP_ERROR') || error.message.includes('INVALID_RESPONSE') || error.message.includes('INVALID_')) {
          throw error;
        }
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown scraping error';
      throw this.createError(
        'SCRAPING_FAILED',
        `Failed to scrape website: ${errorMessage}`,
        { url, originalError: errorMessage }
      );
    }
  }

  extractMetadata(html: string): Record<string, unknown> {
    try {
      // Basic HTML validation
      if (!html || typeof html !== 'string') {
        return this.getEmptyMetadata();
      }

      // Extract title
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      const title = titleMatch?.[1]?.trim() || '';

      // Extract meta description
      const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
      const description = descriptionMatch?.[1]?.trim() || '';

      // Extract meta keywords
      const keywordsMatch = html.match(/<meta[^>]*name=["']keywords["'][^>]*content=["']([^"']+)["']/i);
      const keywords = keywordsMatch?.[1]?.trim() || '';

      // Extract viewport meta tag
      const viewportMatch = html.match(/<meta[^>]*name=["']viewport["'][^>]*content=["']([^"']+)["']/i);
      const viewport = viewportMatch?.[1]?.trim() || '';

      // Extract charset
      const charsetMatch = html.match(/<meta[^>]*charset=["']?([^"'\s>]+)["']?/i);
      const charset = charsetMatch?.[1]?.trim() || '';

      // Extract Open Graph data
      const ogTitleMatch = html.match(/<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["']/i);
      const ogTitle = ogTitleMatch?.[1]?.trim() || '';

      const ogDescriptionMatch = html.match(/<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']+)["']/i);
      const ogDescription = ogDescriptionMatch?.[1]?.trim() || '';

      const ogImageMatch = html.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["']/i);
      const ogImage = ogImageMatch?.[1]?.trim() || '';

      // Extract canonical URL
      const canonicalMatch = html.match(/<link[^>]*rel=["']canonical["'][^>]*href=["']([^"']+)["']/i);
      const canonical = canonicalMatch?.[1]?.trim() || '';

      // Count various elements
      const h1Count = (html.match(/<h1[^>]*>/gi) || []).length;
      const h2Count = (html.match(/<h2[^>]*>/gi) || []).length;
      const h3Count = (html.match(/<h3[^>]*>/gi) || []).length;
      const imageCount = (html.match(/<img[^>]*>/gi) || []).length;
      const linkCount = (html.match(/<a[^>]*href/gi) || []).length;
      const scriptCount = (html.match(/<script[^>]*>/gi) || []).length;
      const styleCount = (html.match(/<style[^>]*>/gi) || []).length;

      // Check for important SEO elements
      const hasH1 = h1Count > 0;
      const hasMetaDescription = description.length > 0;
      const hasMetaKeywords = keywords.length > 0;
      const hasCanonical = canonical.length > 0;
      const hasViewport = viewport.length > 0;
      const hasOgTags = ogTitle.length > 0 || ogDescription.length > 0;

      // Extract structured data (JSON-LD)
      const jsonLdMatches = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis);
      const structuredDataCount = jsonLdMatches ? jsonLdMatches.length : 0;

      // Check for common SEO issues
      const hasMultipleH1 = h1Count > 1;
      const titleLength = title.length;
      const descriptionLength = description.length;
      const isTitleTooLong = titleLength > 60;
      const isTitleTooShort = titleLength < 30;
      const isDescriptionTooLong = descriptionLength > 160;
      const isDescriptionTooShort = descriptionLength < 120;

      return {
        // Basic metadata
        title,
        description,
        keywords,
        charset,
        viewport,
        canonical,
        
        // Open Graph
        ogTitle,
        ogDescription,
        ogImage,
        
        // Content analysis
        htmlLength: html.length,
        wordCount: this.estimateWordCount(html),
        
        // Element counts
        h1Count,
        h2Count,
        h3Count,
        imageCount,
        linkCount,
        scriptCount,
        styleCount,
        structuredDataCount,
        
        // SEO flags
        hasH1,
        hasMetaDescription,
        hasMetaKeywords,
        hasCanonical,
        hasViewport,
        hasOgTags,
        hasMultipleH1,
        
        // SEO metrics
        titleLength,
        descriptionLength,
        isTitleTooLong,
        isTitleTooShort,
        isDescriptionTooLong,
        isDescriptionTooShort,
        
        // Technical
        extractedAt: new Date().toISOString()
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown metadata extraction error';
      console.warn('Failed to extract metadata:', errorMessage);
      return this.getEmptyMetadata();
    }
  }

  private validateUrl(url: string): void {
    try {
      const urlObj = new URL(url);
      
      // Check if protocol is HTTP or HTTPS
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw this.createError(
          'INVALID_PROTOCOL',
          'URL must use HTTP or HTTPS protocol',
          { url, protocol: urlObj.protocol }
        );
      }

      // Check if hostname exists
      if (!urlObj.hostname) {
        throw this.createError(
          'INVALID_HOSTNAME',
          'URL must have a valid hostname',
          { url }
        );
      }

    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('INVALID_')) {
        throw error;
      }
      
      throw this.createError(
        'INVALID_URL_FORMAT',
        PSEO_CONSTANTS.ERROR_MESSAGES.INVALID_URL,
        { url }
      );
    }
  }

  private estimateWordCount(html: string): number {
    try {
      // Remove HTML tags and extract text content
      const textContent = html
        .replace(/<script[^>]*>.*?<\/script>/gis, '') // Remove scripts
        .replace(/<style[^>]*>.*?<\/style>/gis, '') // Remove styles
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();

      // Count words (split by whitespace and filter empty strings)
      const words = textContent.split(/\s+/).filter(word => word.length > 0);
      return words.length;
    } catch (error: unknown) {
      return 0;
    }
  }

  private getEmptyMetadata(): Record<string, unknown> {
    return {
      title: '',
      description: '',
      keywords: '',
      charset: '',
      viewport: '',
      canonical: '',
      ogTitle: '',
      ogDescription: '',
      ogImage: '',
      htmlLength: 0,
      wordCount: 0,
      h1Count: 0,
      h2Count: 0,
      h3Count: 0,
      imageCount: 0,
      linkCount: 0,
      scriptCount: 0,
      styleCount: 0,
      structuredDataCount: 0,
      hasH1: false,
      hasMetaDescription: false,
      hasMetaKeywords: false,
      hasCanonical: false,
      hasViewport: false,
      hasOgTags: false,
      hasMultipleH1: false,
      titleLength: 0,
      descriptionLength: 0,
      isTitleTooLong: false,
      isTitleTooShort: true,
      isDescriptionTooLong: false,
      isDescriptionTooShort: true,
      extractedAt: new Date().toISOString()
    };
  }

  private createError(code: string, message: string, details?: Record<string, unknown>): PSEOError {
    return {
      code,
      message,
      details,
      timestamp: new Date().toISOString()
    };
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}${API_ENDPOINTS.HEALTH}`, {
        method: 'GET',
        headers: {
          'x-api-key': 'altzero_base',
        },
      });

      return response.ok;
    } catch (error: unknown) {
      return false;
    }
  }
}

export const scraperService = new WebsiteScraperService(); 