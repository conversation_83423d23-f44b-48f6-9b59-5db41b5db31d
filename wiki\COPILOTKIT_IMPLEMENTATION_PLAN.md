# AltZero CopilotKit Implementation Plan

## 🎯 Project Goals

Create a comprehensive CopilotKit module with MCP (Model Context Protocol) integration that enables:
- Natural language database querying with `@entity` syntax
- Component interaction across the entire AltZero platform
- Supabase MCP client for direct database access
- Advanced AI-powered workflows and automation

## 📁 Module Structure

```
altzero_base/
├── copilotkit/
│   ├── components/
│   │   ├── AltzeroChat.tsx           # Main CopilotKit chat interface
│   │   ├── MCPClient.tsx             # MCP client component
│   │   ├── DatabaseQuery.tsx         # Database query interface
│   │   ├── ComponentInteractor.tsx   # Component interaction UI
│   │   ├── QueryResultView.tsx       # Display query results
│   │   └── MCPIndicator.tsx          # MCP connection status
│   ├── providers/
│   │   ├── CopilotProvider.tsx       # Main CopilotKit provider wrapper
│   │   ├── MCPProvider.tsx           # MCP context provider
│   │   └── SupabaseMCPProvider.tsx   # Supabase MCP integration
│   ├── lib/
│   │   ├── mcp-client.ts             # Core MCP client implementation
│   │   ├── supabase-mcp.ts           # Supabase MCP adapter
│   │   ├── copilot-actions.ts        # CopilotKit action definitions
│   │   ├── query-parser.ts           # Natural language query parser
│   │   ├── component-registry.ts     # App component registry
│   │   └── schema-introspector.ts    # Database schema analysis
│   ├── hooks/
│   │   ├── useMCPClient.ts           # MCP client hook
│   │   ├── useSupabaseQuery.ts       # Database query hook
│   │   ├── useCopilotActions.ts      # CopilotKit actions hook
│   │   ├── useComponentInteraction.ts # Component control hook
│   │   └── useQueryParser.ts         # Query parsing hook
│   ├── contexts/
│   │   ├── MCPContext.tsx            # MCP state management
│   │   └── CopilotContext.tsx        # Enhanced CopilotKit context
│   ├── pages/
│   │   └── CopilotDashboard.tsx      # Main CopilotKit interface
│   ├── types/
│   │   ├── mcp.ts                    # MCP protocol types
│   │   ├── copilot.ts                # CopilotKit types
│   │   ├── query.ts                  # Query result types
│   │   └── component.ts              # Component interaction types
│   └── utils/
│       ├── constants.ts              # CopilotKit constants
│       ├── formatters.ts             # Data formatting utilities
│       └── validators.ts             # Input validation
```

## 🚀 Implementation Phases

### Phase 1: Core Infrastructure Setup
**Priority: HIGH | Timeline: 1-2 days**

#### 1.1 Module Structure Creation
- [x] Create directory structure
- [x] Set up base configuration files
- [x] Install required dependencies
- [x] Create type definitions

#### 1.2 Dependencies Installation
```bash
yarn add @copilotkit/react-core @copilotkit/react-ui @copilotkit/react-textarea
yarn add @modelcontextprotocol/sdk ws uuid
yarn add @types/ws @types/uuid --dev
```
✅ **COMPLETED**: All dependencies installed successfully

#### 1.3 Route Integration
- [x] Add `/copilot-dashboard` route to App.tsx
- [x] Create "AltZero Copilot" dashboard page
- [x] Set up dedicated layout wrapper

✅ **PHASE 1 COMPLETED** 

**Files Created:**
- `copilotkit/types/mcp.ts` - MCP protocol type definitions
- `copilotkit/types/copilot.ts` - CopilotKit type definitions  
- `copilotkit/utils/constants.ts` - Configuration constants
- `copilotkit/pages/CopilotDashboard.tsx` - Main dashboard page
- `COPILOTKIT_IMPLEMENTATION_PLAN.md` - Implementation plan documentation

**Integration:**
- Route added to `App.tsx` at `/copilot-dashboard`
- Protected route requiring authentication
- Follows existing AltZero architecture patterns

### Phase 2: MCP Protocol Integration
**Priority: HIGH | Timeline: 2-3 days**

#### 2.1 Core MCP Client
```typescript
// copilotkit/lib/mcp-client.ts
interface MCPClient {
  connect(serverUrl: string): Promise<void>;
  disconnect(): Promise<void>;
  sendRequest(method: string, params: any): Promise<any>;
  subscribe(event: string, handler: Function): void;
}
```

#### 2.2 Supabase MCP Adapter
```typescript
// copilotkit/lib/supabase-mcp.ts
interface SupabaseMCPAdapter {
  introspectSchema(): Promise<DatabaseSchema>;
  executeQuery(query: string): Promise<QueryResult>;
  parseNaturalLanguage(prompt: string): Promise<SQLQuery>;
}
```

#### 2.3 Query Parser
```typescript
// copilotkit/lib/query-parser.ts
interface QueryParser {
  parseEntitySyntax(input: string): ParsedQuery;
  // "@customer John" -> { entity: "customer", filter: { name: "John" } }
  // "@orders today" -> { entity: "orders", filter: { date: "today" } }
}
```

### Phase 3: CopilotKit Integration
**Priority: HIGH | Timeline: 2-3 days**

#### 3.1 Enhanced Chat Interface
```typescript
// copilotkit/components/AltzeroChat.tsx
- Advanced chat UI with MCP indicators
- Query result visualization
- Component interaction buttons
- Real-time data updates
- Source attribution for database queries
```

#### 3.2 CopilotKit Actions
```typescript
// copilotkit/lib/copilot-actions.ts
const actions = [
  {
    name: "queryDatabase",
    description: "Query the database using natural language",
    parameters: [{ name: "query", type: "string" }],
    handler: async ({ query }) => await executeNaturalQuery(query)
  },
  {
    name: "navigateToPage",
    description: "Navigate to a specific page in the application",
    parameters: [{ name: "path", type: "string" }],
    handler: async ({ path }) => navigate(path)
  },
  {
    name: "interactWithComponent",
    description: "Interact with application components",
    parameters: [
      { name: "component", type: "string" },
      { name: "action", type: "string" },
      { name: "params", type: "object" }
    ],
    handler: async ({ component, action, params }) => 
      await componentRegistry.execute(component, action, params)
  }
];
```

#### 3.3 Component Registry
```typescript
// copilotkit/lib/component-registry.ts
interface ComponentRegistry {
  register(name: string, component: ComponentDefinition): void;
  execute(component: string, action: string, params: any): Promise<any>;
  getAvailableActions(): ActionDefinition[];
}
```

### Phase 4: Database Integration
**Priority: MEDIUM | Timeline: 2-3 days**

#### 4.1 Schema Introspection
```typescript
// copilotkit/lib/schema-introspector.ts
interface SchemaIntrospector {
  analyzeDatabase(): Promise<DatabaseSchema>;
  generateDescriptions(): Promise<TableDescriptions>;
  createQueryTemplates(): Promise<QueryTemplates>;
}
```

#### 4.2 Natural Language Processing
```typescript
// Query Examples:
// "@customer John" -> SELECT * FROM profiles WHERE name ILIKE '%John%'
// "@orders today" -> SELECT * FROM orders WHERE created_at >= CURRENT_DATE
// "@users active" -> SELECT * FROM profiles WHERE last_sign_in_at > NOW() - INTERVAL '30 days'
// "@sales last month" -> SELECT SUM(amount) FROM orders WHERE created_at >= DATE_TRUNC('month', NOW() - INTERVAL '1 month')
```

#### 4.3 Result Visualization
```typescript
// copilotkit/components/QueryResultView.tsx
- Table view for structured data
- Chart generation for numeric data
- Export capabilities (CSV, JSON)
- Pagination for large datasets
```

### Phase 5: Advanced Features
**Priority: MEDIUM | Timeline: 3-4 days**

#### 5.1 Multi-Component Orchestration
```typescript
// Examples:
// "Upload a document and create a summary"
// "Run SEO audit and save results to dashboard"
// "Create new user and send welcome email"
```

#### 5.2 Workflow Automation
```typescript
// copilotkit/lib/workflow-engine.ts
interface WorkflowEngine {
  defineWorkflow(steps: WorkflowStep[]): Workflow;
  executeWorkflow(workflow: Workflow, params: any): Promise<WorkflowResult>;
  scheduleWorkflow(workflow: Workflow, schedule: CronExpression): void;
}
```

#### 5.3 AI-Powered Analytics
```typescript
// Features:
// - Generate insights from query results
// - Create visualizations automatically
// - Export comprehensive reports
// - Trend analysis and predictions
```

### Phase 6: Backend Integration
**Priority: HIGH | Timeline: 2-3 days**

#### 6.1 MCP Server Routes
```typescript
// server/copilotkit/routes/mcp.ts
router.post('/mcp/query', async (req, res) => {
  const { query, userId } = req.body;
  const result = await mcpService.executeQuery(query, userId);
  res.json(result);
});

router.get('/mcp/schema', async (req, res) => {
  const schema = await mcpService.getSchema();
  res.json(schema);
});
```

#### 6.2 Supabase Integration
```typescript
// server/copilotkit/services/supabase-mcp.ts
class SupabaseMCPService {
  async executeQuery(query: string, userId: string): Promise<QueryResult> {
    // Parse natural language query
    // Convert to SQL with user-specific filtering
    // Execute with RLS policies
    // Return formatted results
  }
}
```

#### 6.3 Security Layer
```typescript
// Row Level Security enforcement
// Query validation and sanitization
// User permission checking
// Audit logging for database queries
```

## 🔧 Technical Implementation Details

### Query Syntax Examples

| Natural Language | Parsed Query | SQL Output |
|-----------------|--------------|------------|
| `@customer John` | `{ entity: "profiles", filter: { name: "John" } }` | `SELECT * FROM profiles WHERE name ILIKE '%John%' AND user_id = $1` |
| `@orders today` | `{ entity: "orders", timeFilter: "today" }` | `SELECT * FROM orders WHERE created_at >= CURRENT_DATE AND user_id = $1` |
| `@users active last 30 days` | `{ entity: "profiles", filter: { active: true, period: "30d" } }` | `SELECT * FROM profiles WHERE last_sign_in_at > NOW() - INTERVAL '30 days'` |
| `@sales this month total` | `{ entity: "orders", aggregation: "sum", field: "amount", period: "month" }` | `SELECT SUM(amount) FROM orders WHERE created_at >= DATE_TRUNC('month', NOW())` |

### Component Interaction Examples

| Command | Action | Implementation |
|---------|---------|----------------|
| `"Navigate to dashboard"` | `navigate("/dashboard")` | React Router navigation |
| `"Upload document to knowledge base"` | `triggerFileUpload()` | File input trigger |
| `"Run SEO audit for example.com"` | `startSEOAudit("example.com")` | PSEO module integration |
| `"Show latest chat sessions"` | `loadChatSessions()` | Knowledge context update |

### CopilotKit Actions Registry

```typescript
const COPILOT_ACTIONS = {
  // Database Operations
  QUERY_DATABASE: "queryDatabase",
  EXPORT_DATA: "exportData",
  ANALYZE_DATA: "analyzeData",
  
  // Navigation
  NAVIGATE_TO_PAGE: "navigateToPage",
  OPEN_MODAL: "openModal",
  CLOSE_DIALOG: "closeDialog",
  
  // Component Interaction
  UPLOAD_DOCUMENT: "uploadDocument",
  CREATE_TEAM: "createTeam",
  SEND_INVITATION: "sendInvitation",
  
  // Workflow Automation
  RUN_SEO_AUDIT: "runSEOAudit",
  GENERATE_REPORT: "generateReport",
  SCHEDULE_TASK: "scheduleTask"
};
```

## 🎯 Success Criteria

### Phase 1 Success Metrics
- [ ] CopilotKit module structure created
- [ ] Basic routing and navigation working
- [ ] Dependencies installed and configured
- [ ] Type definitions established

### Phase 2 Success Metrics
- [ ] MCP client successfully connects to Supabase
- [ ] Basic query parsing working for `@entity` syntax
- [ ] Database schema introspection functional
- [ ] Query results properly formatted and displayed

### Phase 3 Success Metrics
- [ ] CopilotKit chat interface responsive and intuitive
- [ ] Component interaction actions working
- [ ] Real-time query result updates
- [ ] Error handling and loading states implemented

### Final Success Criteria
- [ ] Users can query database using natural language
- [ ] `@customer`, `@user`, `@order` syntax works reliably
- [ ] Component interactions execute successfully
- [ ] Security and permissions properly enforced
- [ ] Performance acceptable for real-time use
- [ ] Documentation complete and accurate

## 🔄 Development Workflow

### Daily Tasks
1. **Morning**: Review previous day's progress, plan current day
2. **Development**: Focus on current phase objectives
3. **Testing**: Unit tests and integration tests for new features
4. **Documentation**: Update progress and document new features
5. **Evening**: Commit changes, update task status

### Quality Assurance
- Follow `.cursorrules` coding standards
- Maintain TypeScript strict mode
- Implement proper error handling
- Write comprehensive tests
- Document all public APIs
- Regular code reviews and refactoring

### Progress Tracking
- [ ] Phase 1: Core Infrastructure (Days 1-2)
- [ ] Phase 2: MCP Integration (Days 3-5)
- [ ] Phase 3: CopilotKit Features (Days 6-8)
- [ ] Phase 4: Database Integration (Days 9-11)
- [ ] Phase 5: Advanced Features (Days 12-15)
- [ ] Phase 6: Backend Integration (Days 16-18)
- [ ] Testing & Polish (Days 19-20)

## 📚 References

- **Architecture**: Follow patterns established in `ARCHITECTURE.md`
- **Coding Standards**: Adhere to `.cursorrules` guidelines
- **CopilotKit Documentation**: Reference `copilot-llms-full.txt`
- **Existing Patterns**: Study `base/` module structure
- **Type Safety**: Maintain strict TypeScript usage
- **Theme System**: Use established theme variables from `index.css`

---

**Next Steps**: Begin Phase 1 implementation with module structure creation and dependency installation. 