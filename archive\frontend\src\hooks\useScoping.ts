import { useState, useCallback } from 'react';

export interface ClientInfo {
  name: string;
  industry: string;
  company: string;
  contactPerson: string;
  email: string;
}

export interface ScopingInfo {
  projectName: string;
  projectDescription: string;
  timeline: string;
  budget: string;
  goals: string[];
}

export interface PromptTemplate {
  id: string;
  name: string;
  content: string;
}

export interface SectionDefinition {
  id: string;
  title: string;
  description: string;
}

export interface ScopingSection {
  title: string;
  content: string;
}

export interface Scoping {
  id: string;
  clientName: string;
  projectName: string;
  description: string;
  sections: ScopingSection[];
  createdAt: Date;
  updatedAt: Date;
}

export type ScopingState = {
  clientInfo: ClientInfo | null;
  scopingInfo: ScopingInfo | null;
  promptTemplate: PromptTemplate | null;
  sections: SectionDefinition[];
  documentFile: File | null;
};

export type ScopingEvent = {
  type: 'started' | 'research' | 'summary' | 'section' | 'completed' | 'error';
  scopingId: string;
  data?: any;
  error?: string;
};

export interface UseScopingReturn {
  state: ScopingState;
  messages: string[];
  isLoading: boolean;
  error: string | null;
  setClientInfo: (clientInfo: ClientInfo) => void;
  setScopingInfo: (scopingInfo: ScopingInfo) => void;
  setPromptTemplate: (promptTemplate: PromptTemplate) => void;
  addSection: (section: SectionDefinition) => void;
  removeSection: (sectionId: string) => void;
  updateSection: (sectionId: string, updatedSection: Partial<SectionDefinition>) => void;
  setDocumentFile: (file: File | null) => void;
  generateScoping: () => Promise<void>;
  resetScoping: () => void;
  result: Scoping | null;
  saveScoping: (updatedScoping: Scoping) => void;
}

export const useScoping = (): UseScopingReturn => {
  const [state, setState] = useState<ScopingState>({
    clientInfo: null,
    scopingInfo: null,
    promptTemplate: null,
    sections: [],
    documentFile: null,
  });
  
  const [messages, setMessages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<Scoping | null>(null);

  const resetScoping = useCallback(() => {
    setState({
      clientInfo: null,
      scopingInfo: null,
      promptTemplate: null,
      sections: [],
      documentFile: null,
    });
    setMessages([]);
    setIsLoading(false);
    setError(null);
    setResult(null);
  }, []);

  const setClientInfo = useCallback((clientInfo: ClientInfo) => {
    setState(prev => ({ ...prev, clientInfo }));
  }, []);

  const setScopingInfo = useCallback((scopingInfo: ScopingInfo) => {
    setState(prev => ({ ...prev, scopingInfo }));
  }, []);

  const setPromptTemplate = useCallback((promptTemplate: PromptTemplate) => {
    setState(prev => ({ ...prev, promptTemplate }));
  }, []);

  const addSection = useCallback((section: SectionDefinition) => {
    setState(prev => ({
      ...prev,
      sections: [...prev.sections, section],
    }));
  }, []);

  const removeSection = useCallback((sectionId: string) => {
    setState(prev => ({
      ...prev,
      sections: prev.sections.filter(section => section.id !== sectionId),
    }));
  }, []);

  const updateSection = useCallback((sectionId: string, updatedSection: Partial<SectionDefinition>) => {
    setState(prev => ({
      ...prev,
      sections: prev.sections.map(section => 
        section.id === sectionId 
          ? { ...section, ...updatedSection } 
          : section
      ),
    }));
  }, []);

  const setDocumentFile = useCallback((file: File | null) => {
    setState(prev => ({ ...prev, documentFile: file }));
  }, []);

  const generateScoping = useCallback(async () => {
    // Validate required fields
    if (!state.clientInfo) {
      setError('Client information is required');
      return;
    }

    if (!state.scopingInfo) {
      setError('Scoping information is required');
      return;
    }

    if (!state.promptTemplate) {
      setError('Prompt template is required');
      return;
    }

    if (state.sections.length === 0) {
      setError('At least one section must be defined');
      return;
    }

    setIsLoading(true);
    setError(null);
    setMessages(['Starting AI scoping document generation...']);

    try {
      // Upload document file if present
      let documentId: string | null = null;
      
      if (state.documentFile) {
        const fileName = state.documentFile.name;
        setMessages(prev => [...prev, `Uploading document: ${fileName}...`]);
        
        const formData = new FormData();
        formData.append('file', state.documentFile);
        
        const apiKey = import.meta.env.VITE_API_KEY;
        const uploadResponse = await fetch(`${import.meta.env.VITE_API_URL}/documents?apiKey=${apiKey}`, {
          method: 'POST',
          body: formData,
        });
        
        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload document');
        }
        
        const uploadData = await uploadResponse.json();
        documentId = uploadData.id;
        setMessages(prev => [...prev, `Document uploaded successfully: ${fileName}`]);
      }

      // Prepare request data
      const requestBody = {
        clientInfo: state.clientInfo,
        scopingInfo: state.scopingInfo,
        promptTemplate: state.promptTemplate,
        sections: state.sections,
        documentId,
      };

      // API URL and key
      const apiUrl = import.meta.env.VITE_API_URL;
      const apiKey = import.meta.env.VITE_API_KEY;

      // Send POST request to initialize the streaming process
      const response = await fetch(`${apiUrl}/scoping/stream?apiKey=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
        },
        body: JSON.stringify(requestBody),
      });

      // Set up event source for streaming
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        throw new Error('Failed to initialize stream reader');
      }

      // Process the stream
      const processStream = async () => {
        let buffer = '';
        
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }
          
          buffer += decoder.decode(value, { stream: true });
          
          // Process complete events in the buffer
          const lines = buffer.split('\n\n');
          buffer = lines.pop() || '';
          
          for (const line of lines) {
            if (line.trim() === '') continue;
            
            // Extract event and data
            const eventMatch = line.match(/event: (.*)\n/);
            const dataMatch = line.match(/data: (.*)/);
            
            if (eventMatch && dataMatch) {
              const eventType = eventMatch[1];
              const eventData = JSON.parse(dataMatch[1]);
              
              // Handle different event types
              switch (eventType) {
                case 'started':
                  setMessages(prev => [...prev, eventData.message || 'Starting...']);
                  break;
                  
                case 'research':
                  if (eventData.status === 'in_progress') {
                    setMessages(prev => [...prev, 'Analyzing reference document...']);
                  } else if (eventData.status === 'completed') {
                    setMessages(prev => [...prev, 'Document analysis completed']);
                  }
                  break;
                  
                case 'summary':
                  if (eventData.status === 'in_progress') {
                    setMessages(prev => [...prev, 'Generating document introduction...']);
                  } else if (eventData.status === 'completed') {
                    setMessages(prev => [...prev, 'Introduction completed']);
                  }
                  break;
                  
                case 'section':
                  if (eventData.status === 'in_progress') {
                    setMessages(prev => [...prev, `Generating section: ${eventData.section.title}...`]);
                  } else if (eventData.status === 'completed') {
                    setMessages(prev => [...prev, `Completed section: ${eventData.section.title}`]);
                  }
                  break;
                  
                case 'completed':
                  setMessages(prev => [...prev, 'Scoping document generation completed']);
                  setResult(eventData.result);
                  setIsLoading(false);
                  break;
                  
                case 'error':
                  setError(eventData.error || 'An error occurred');
                  setMessages(prev => [...prev, `Error: ${eventData.error}`]);
                  setIsLoading(false);
                  break;
                  
                case 'end':
                  // Stream ended
                  break;
                  
                default:
                  console.log('Unknown event type:', eventType, eventData);
              }
            }
          }
        }
      };
      
      // Start processing the stream
      processStream().catch(err => {
        console.error('Error processing stream:', err);
        setError('Error processing response stream');
        setIsLoading(false);
      });
      
    } catch (err: any) {
      console.error('Error in generateScoping:', err);
      setError(err.message || 'Failed to generate scoping document');
      setIsLoading(false);
    }
  }, [state]);

  const saveScoping = useCallback((updatedScoping: Scoping) => {
    setResult(updatedScoping);
    setMessages(prev => [...prev, 'Scoping document updated']);
  }, []);

  return {
    state,
    messages,
    isLoading,
    error,
    setClientInfo,
    setScopingInfo,
    setPromptTemplate,
    addSection,
    removeSection,
    updateSection,
    setDocumentFile,
    generateScoping,
    resetScoping,
    result,
    saveScoping,
  };
}; 