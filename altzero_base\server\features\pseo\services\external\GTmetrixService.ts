import { BaseExternalService, ExternalServiceResult, ExternalServiceConfig, SEOMetrics, SEOIssue } from './BaseExternalService';

export class GTmetrixService extends BaseExternalService {
  private readonly API_URL = 'https://gtmetrix.com/api/2.0';

  constructor(config: ExternalServiceConfig) {
    super('GTmetrix', 'free', config);
  }

  isConfigured(): boolean {
    return !!this.config.apiKey;
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_URL}/status`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
        }
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async analyze(url: string): Promise<ExternalServiceResult> {
    if (!this.isEnabled()) {
      return this.createErrorResult('GTmetrix service is not enabled or configured');
    }

    try {
      await this.handleRateLimit();

      const result = await this.withRetry(async () => {
        // Start test
        const testResponse = await fetch(`${this.API_URL}/reports`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            url: url,
            report: 'lighthouse'
          }),
          signal: AbortSignal.timeout(this.config.timeout || 30000)
        });

        if (!testResponse.ok) {
          throw new Error(`GTmetrix API failed: ${testResponse.statusText}`);
        }

        const testData = await testResponse.json();
        const reportId = testData.data.id;

        // Poll for results
        return await this.pollForResults(reportId);
      });

      return this.parseResponse(result);

    } catch (error) {
      return this.createErrorResult(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  protected getFreeTierLimit(): number {
    return 20; // 20 reports per month
  }

  protected getPremiumCost(): number {
    return 0; // Free service
  }

  private async pollForResults(reportId: string, maxAttempts: number = 30): Promise<any> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const response = await fetch(`${this.API_URL}/reports/${reportId}`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to get GTmetrix report: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.data.state === 'completed') {
        return data.data;
      } else if (data.data.state === 'error') {
        throw new Error(`GTmetrix analysis failed: ${data.data.error}`);
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    throw new Error('GTmetrix analysis timed out');
  }

  private parseResponse(data: any): ExternalServiceResult {
    const lighthouse = data.reports?.lighthouse || {};
    const categories = lighthouse.categories || {};

    const metrics: SEOMetrics = {
      overall: Math.round((categories.seo?.score || 0) * 100),
      technical: Math.round((categories['best-practices']?.score || 0) * 100),
      content: Math.round((categories.seo?.score || 0) * 100),
      performance: Math.round((categories.performance?.score || 0) * 100),
      accessibility: Math.round((categories.accessibility?.score || 0) * 100),
      seo: Math.round((categories.seo?.score || 0) * 100),
      details: lighthouse
    };

    const issues: SEOIssue[] = this.extractIssues(lighthouse.audits || {});

    return {
      provider: this.name,
      metrics,
      issues,
      rawData: data,
      timestamp: new Date().toISOString(),
      success: true
    };
  }

  private extractIssues(audits: any): SEOIssue[] {
    const issues: SEOIssue[] = [];

    const seoAudits = [
      { id: 'document-title', category: 'Meta Tags' },
      { id: 'meta-description', category: 'Meta Tags' },
      { id: 'viewport', category: 'Mobile' },
      { id: 'image-alt', category: 'Images' },
      { id: 'link-text', category: 'Links' },
      { id: 'crawlable-anchors', category: 'Links' },
      { id: 'is-crawlable', category: 'Technical SEO' }
    ];

    seoAudits.forEach(({ id, category }) => {
      const audit = audits[id];
      if (audit && audit.score !== null && audit.score < 1) {
        issues.push({
          category,
          severity: audit.score === 0 ? 'critical' : 'warning',
          title: audit.title || id,
          description: audit.description || 'GTmetrix SEO issue detected',
          recommendation: this.getRecommendation(id),
          impact: audit.score === 0 ? 'high' : 'medium'
        });
      }
    });

    return issues;
  }

  private getRecommendation(auditId: string): string {
    const recommendations: { [key: string]: string } = {
      'document-title': 'Add a unique, descriptive title tag',
      'meta-description': 'Add a compelling meta description',
      'viewport': 'Add viewport meta tag for mobile optimization',
      'image-alt': 'Add alt text to all images',
      'link-text': 'Use descriptive link text',
      'crawlable-anchors': 'Ensure links are crawlable by search engines',
      'is-crawlable': 'Make sure page is crawlable by search engines'
    };

    return recommendations[auditId] || 'Fix this SEO issue for better rankings';
  }

  getRateLimit(): number {
    return 1; // 1 request per minute for free tier
  }
} 