import React, { useMemo, useCallback } from "react";
import { Button } from "../../../../../base/components/ui/button";
import {
  GeneratedDocumentData,
  PageData,
  DocumentTheme,
} from "../../../types/documentTypes";
import {
  renderBlocksToHTML,
  convertContentToBlocks,
} from "../../../utils/documentUtils";
import { themePresets } from "../../../utils/themeUtils";

interface PagedDocumentPreviewProps {
  document: GeneratedDocumentData;
  pages: PageData[];
  currentPage: number;
  setCurrentPage: (page: number) => void;
  isEditMode?: boolean;
  editedContent?: { [sectionId: string]: string };
  onEditContent?: (sectionId: string, content: string) => void;
}

export const PagedDocumentPreview: React.FC<PagedDocumentPreviewProps> = ({
  document,
  pages,
  currentPage,
  setCurrentPage,
  isEditMode = false,
  editedContent = {},
  onEditContent = (_sectionId: string, _content: string) => {},
}) => {
  // Use useMemo for theme and showTitlePage to prevent recalculations on every render
  const theme = useMemo(
    () => document.theme || themePresets.professional,
    [document.theme]
  );
  const showTitlePage = useMemo(
    () => theme?.showCoverPage !== false,
    [theme?.showCoverPage]
  );

  // Use useMemo for derived values to prevent recalculations
  const totalPageCount = useMemo(
    () => (showTitlePage ? pages.length + 1 : pages.length),
    [showTitlePage, pages.length]
  );

  const currentPageNumber = currentPage + 1;

  // Extract contentPageIndex calculation to avoid recalculations
  const contentPageIndex = useMemo(
    () => (showTitlePage ? currentPage - 1 : currentPage),
    [showTitlePage, currentPage]
  );

  // More efficient validation check
  const hasValidContentForCurrentPage = useMemo(
    () =>
      showTitlePage
        ? currentPage > 0 && contentPageIndex < pages.length
        : contentPageIndex >= 0 && contentPageIndex < pages.length,
    [showTitlePage, currentPage, contentPageIndex, pages.length]
  );

  // Fixed navigation functions
  const goToNextPage = useCallback(() => {
    // Calculate the maximum page index
    const maxPageIndex = showTitlePage ? pages.length : pages.length - 1;

    if (currentPage < maxPageIndex) {
      setCurrentPage(currentPage + 1);
    }
  }, [currentPage, pages.length, setCurrentPage, showTitlePage]);

  const goToPrevPage = useCallback(() => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage, setCurrentPage]);

  if (pages.length === 0) {
    return (
      <div className="text-center py-10 text-gray-500">
        No content available to preview
      </div>
    );
  }

  // Enhanced professional title page
  const renderTitlePage = () => {
    const currentDate = new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const brandName = theme?.brandName || "Altzero base";
    const brandTagline =
      theme?.brandTagline || "Digital Transformation Solutions";

    return (
      <div className="flex flex-col h-full relative overflow-hidden">
        {/* Super Professional Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="flex justify-between items-center p-8">
            <div className="flex items-center gap-4">
              {theme?.logo && (
                <div className="bg-white p-2 rounded-lg">
                  <img
                    src={theme.logo}
                    alt="Company Logo"
                    className="h-12 object-contain"
                    onError={(e) => {
                      console.error("Logo failed to load in title page");
                      const imgElement = e.target as HTMLImageElement;
                      imgElement.style.display = "none";
                    }}
                  />
                </div>
              )}
              <div className="text-left">
                <div className="text-xl font-bold">{brandName}</div>
                <div className="text-blue-100 text-sm">{brandTagline}</div>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white/20 px-4 py-2 rounded-lg backdrop-blur-sm">
                <div className="font-semibold text-sm">BUSINESS PROPOSAL</div>
                <div className="text-blue-100 text-xs">{currentDate}</div>
              </div>
            </div>
          </div>
          <div className="h-1 bg-gradient-to-r from-yellow-400 to-orange-400"></div>
        </div>

        {/* Main Title Section */}
        <div className="flex-1 flex flex-col justify-center items-center px-8 py-16 bg-gradient-to-b from-gray-50 to-white">
          <div className="text-center max-w-4xl">
            <div className="mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
                Professional Services Proposal
              </div>
            </div>

            <h1 className="text-6xl font-bold mb-8 leading-tight text-gray-900 tracking-tight">
              {document.title || "Professional Service Proposal"}
            </h1>

            {document.client && (
              <div className="mb-12">
                <p className="text-lg mb-4 text-gray-600 font-medium">
                  Prepared exclusively for:
                </p>
                <div className="bg-white p-6 rounded-xl shadow-lg border-l-4 border-blue-600">
                  <p className="text-4xl font-bold text-blue-700 mb-2">
                    {document.client}
                  </p>
                  <p className="text-gray-600">Valued Partner</p>
                </div>
              </div>
            )}

            <div className="flex items-center justify-center mb-8">
              <div className="w-16 h-1 bg-blue-600 rounded-full"></div>
              <div className="w-4 h-4 bg-yellow-400 rounded-full mx-4"></div>
              <div className="w-16 h-1 bg-blue-600 rounded-full"></div>
            </div>

            <div className="text-lg text-gray-700 bg-white p-6 rounded-lg shadow-sm">
              <p className="mb-2 font-semibold">
                Comprehensive Solution & Implementation Plan
              </p>
              <p className="text-base">{currentDate}</p>
            </div>
          </div>
        </div>

        {/* Super Professional Footer */}
        <div className="bg-gray-900 text-white">
          <div className="p-8">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                {theme?.logo && (
                  <div className="bg-white p-2 rounded">
                    <img
                      src={theme.logo}
                      alt="Logo"
                      className="h-6 object-contain opacity-80"
                    />
                  </div>
                )}
                <div>
                  <div className="font-bold text-lg">{brandName}</div>
                  <div className="text-gray-400 text-sm">{brandTagline}</div>
                  <div className="text-gray-500 text-xs mt-1">
                    Confidential & Proprietary Document
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="bg-blue-600 px-4 py-2 rounded-lg">
                  <div className="text-white font-semibold text-sm">Page 1</div>
                  <div className="text-blue-100 text-xs">
                    of {totalPageCount}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="h-1 bg-gradient-to-r from-blue-600 to-blue-700"></div>
        </div>
      </div>
    );
  };

  // Enhanced professional header for content pages
  const renderHeader = () => {
    const brandName = theme?.brandName || "Altzero base";
    const brandTagline =
      theme?.brandTagline || "Digital Transformation Solutions";

    return (
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
        <div className="py-4 px-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              {theme?.logo && (
                <div className="bg-white p-1.5 rounded">
                  <img
                    src={theme.logo}
                    alt="Company Logo"
                    className="h-8 object-contain"
                    onError={(e) => {
                      console.warn("Error loading logo image:", e);
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
              )}
              <div>
                <div className="text-lg font-bold">{brandName}</div>
                <div className="text-blue-100 text-xs">{brandTagline}</div>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white/20 px-3 py-1.5 rounded backdrop-blur-sm">
                <div className="font-medium text-sm">CONFIDENTIAL</div>
                <div className="text-blue-100 text-xs">
                  {new Date().toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="h-0.5 bg-gradient-to-r from-yellow-400 to-orange-400"></div>
      </div>
    );
  };

  // Enhanced professional footer for content pages
  const renderFooter = () => {
    const brandName = theme?.brandName || "Altzero base";

    return (
      <div className="bg-gray-900 text-white mt-auto">
        <div className="h-0.5 bg-gradient-to-r from-blue-600 to-blue-700"></div>
        <div className="py-4 px-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              {theme?.logo && (
                <div className="bg-white p-1 rounded opacity-90">
                  <img
                    src={theme.logo}
                    alt="Logo"
                    className="h-5 object-contain"
                  />
                </div>
              )}
              <div>
                <div className="font-bold text-sm">{brandName}</div>
                <div className="text-gray-400 text-xs">
                  <EMAIL> | +1 (555) 123-4567
                </div>
              </div>
            </div>
            <div className="bg-blue-600 px-4 py-2 rounded">
              <div className="text-white font-semibold text-sm">
                Page {currentPageNumber} of {totalPageCount}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Optimized renderPageContent function
  const renderPageContent = () => {
    // If we're showing title page and we're on page 0, return the title page
    if (showTitlePage && currentPage === 0) {
      return renderTitlePage();
    }

    // Otherwise, show regular content page
    if (hasValidContentForCurrentPage) {
      const section = pages[contentPageIndex]?.content?.[0];

      if (!section) {
        return (
          <div className="p-8 flex-grow flex items-center justify-center">
            <p className="text-gray-500">No content available for this page</p>
          </div>
        );
      }

      // Get either edited content or original content
      const sectionContent = editedContent[section.id] || section.content;

      // Use existing blocks if available, otherwise create from content
      // This ensures we preserve uploaded images and other block-specific data
      let blocksToRender = section.blocks;

      // Only convert from content if blocks don't exist or are empty
      // This prevents overwriting image uploads and other block data
      if (
        !blocksToRender ||
        !Array.isArray(blocksToRender) ||
        blocksToRender.length === 0
      ) {
        console.log(
          "PagedDocumentPreview: Converting content to blocks for section:",
          section.id
        );
        blocksToRender = convertContentToBlocks(
          sectionContent || section.content || ""
        );
      } else {
        console.log(
          "PagedDocumentPreview: Using existing blocks for section:",
          section.id,
          "Block count:",
          blocksToRender.length
        );
        // Log image blocks specifically
        const imageBlocks = blocksToRender.filter(
          (block: any) => block.type === "image"
        );
        if (imageBlocks.length > 0) {
          console.log(
            "PagedDocumentPreview: Found image blocks:",
            imageBlocks.length
          );
          imageBlocks.forEach((block: any, index: number) => {
            console.log(`PagedDocumentPreview: Image block ${index + 1}:`, {
              id: block.id,
              hasContent: !!block.content,
              contentType: block.content?.startsWith("data:image")
                ? "base64"
                : "other",
              contentLength: block.content?.length || 0,
              caption: block.caption,
            });
          });
        }
      }

      return (
        <div className="p-8 flex-grow">
          {!showTitlePage && currentPage === 0 && (
            <>
              <div className="text-3xl font-bold mb-6 text-gray-900">
                {document.title || "Document Title"}
              </div>
              {document.client && (
                <p className="text-gray-700 mb-6">Client: {document.client}</p>
              )}
            </>
          )}

          {section && (
            <div className="mb-8">
              <h2 className="text-2xl font-semibold mb-4 pb-2 border-b border-blue-200 text-gray-900">
                {section.title}
              </h2>

              {isEditMode ? (
                <textarea
                  className="w-full min-h-[400px] p-4 border rounded text-md font-light text-gray-700"
                  value={sectionContent}
                  onChange={(e) => onEditContent(section.id, e.target.value)}
                />
              ) : (
                <div
                  className="prose prose-lg max-w-none text-gray-700"
                  style={{
                    fontFamily:
                      "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
                    lineHeight: "1.7",
                  }}
                  dangerouslySetInnerHTML={{
                    __html: renderBlocksToHTML(blocksToRender),
                  }}
                />
              )}
            </div>
          )}
        </div>
      );
    }

    // Fallback if page is out of range
    return (
      <div className="p-8 flex-grow flex items-center justify-center">
        <p className="text-gray-500">No content available for this page</p>
      </div>
    );
  };

  // Main component return
  return (
    <div className="flex">
      {/* Left sidebar with page thumbnails */}
      <div className="w-[180px] pr-4 border-r overflow-y-auto max-h-[800px]">
        <div className="flex flex-col gap-3 p-2">
          {/* Add title page thumbnail if logo exists */}
          {showTitlePage && (
            <div
              onClick={() => setCurrentPage(0)}
              className={`cursor-pointer transition-all ${
                currentPage === 0
                  ? "border-2 border-blue-500"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="bg-white aspect-[1/1.4] p-2 flex flex-col text-xs shadow-sm">
                <div className="text-center font-semibold mb-1 truncate">
                  Cover Page
                </div>
                <div className="overflow-hidden flex flex-col items-center justify-center">
                  {/* Mini logo preview */}
                  <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center mb-1">
                    {theme?.logo ? (
                      <img
                        src={theme.logo}
                        alt="Logo"
                        className="max-w-[80%] max-h-[80%] object-contain"
                        onError={(e) => {
                          console.warn("Error loading logo thumbnail");
                          e.currentTarget.style.display = "none";
                          e.currentTarget.parentElement
                            ?.querySelector(".text-gray-400")
                            ?.classList.remove("hidden");
                        }}
                      />
                    ) : (
                      <div className="text-[6px] text-gray-400">Title</div>
                    )}
                    <div className="text-[6px] text-gray-400 hidden">Title</div>
                  </div>
                  <div className="h-2 bg-gray-200 rounded w-2/3 mx-auto mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2 mb-1"></div>
                </div>
              </div>
              <div className="text-center text-xs py-1 bg-gray-50">Title</div>
            </div>
          )}

          {/* Regular page thumbnails */}
          {pages.map((page, index) => (
            <div
              key={page.id}
              onClick={() => setCurrentPage(showTitlePage ? index + 1 : index)}
              className={`cursor-pointer transition-all ${
                currentPage === (showTitlePage ? index + 1 : index)
                  ? "border-2 border-blue-500"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="bg-white aspect-[1/1.4] p-2 flex flex-col text-xs shadow-sm">
                <div className="text-center font-semibold mb-1 truncate">
                  {page.content[0]?.title || `Page ${index + 1}`}
                </div>
                <div className="overflow-hidden text-[7px] text-gray-500 text-center">
                  {/* Simple representation of content */}
                  <div className="h-2 bg-gray-200 rounded w-2/3 mx-auto mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-full mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-5/6 mb-1"></div>
                </div>
              </div>
              <div className="text-center text-xs py-1 bg-gray-50">
                {showTitlePage ? index + 2 : index + 1}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Document content */}
        <div className="flex-1 bg-gray-100 p-4 overflow-y-auto">
          <div
            className="mx-auto bg-white shadow-lg rounded-lg max-w-[794px] min-h-[1100px] flex flex-col"
            style={{
              aspectRatio: "1/1.4",
            }}
          >
            {/* Document Header - Don't show on title page */}
            {!(showTitlePage && currentPage === 0) && renderHeader()}

            {/* Document Content - Use our new renderPageContent function */}
            {renderPageContent()}

            {/* Document Footer - Don't show on title page */}
            {!(showTitlePage && currentPage === 0) && renderFooter()}
          </div>
        </div>

        {/* Page navigation controls */}
        <div className="flex justify-between items-center p-4 border-t bg-white">
          <div className="text-sm text-gray-600 font-medium">
            Page {currentPageNumber} of {totalPageCount}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPrevPage}
              disabled={currentPage === 0}
              className="px-4 py-2"
            >
              ← Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={
                currentPage >= (showTitlePage ? pages.length : pages.length - 1)
              }
              className="px-4 py-2"
            >
              Next →
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
