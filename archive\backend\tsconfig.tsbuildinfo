{"root": ["./src/app.ts", "./src/index.ts", "./src/new-app.ts", "./src/config/environment.ts", "./src/middleware/auth.ts", "./src/services/chat/chat.ts", "./src/services/document/processor.ts", "./src/services/document/simpleparser.ts", "./src/services/embeddings/cohere.ts", "./src/services/embeddings/openai.ts", "./src/services/llm/openai.ts", "./src/services/llm/openrouter.ts", "./src/services/proposal/mock-proposal.ts", "./src/services/proposal/proposal-stream.ts", "./src/services/proposal/proposal.ts", "./src/services/scoping/scoping-stream.ts", "./src/services/vectorstore/pinecone.ts", "./src/types/llama.ts", "./src/types/pdf-parse.d.ts", "./src/types/scoping.ts"], "errors": true, "version": "5.8.2"}