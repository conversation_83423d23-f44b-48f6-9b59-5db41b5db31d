import { useState, useCallback, useEffect } from 'react';
import { API_URL, API_KEY, ENDPOINTS } from '../utils/constants';

export interface Document {
  id: string;
  title: string;
  filename?: string;
  status: 'success' | 'error' | 'processing';
  chunk_count?: number;
  vector_count?: number;
  tags?: string[];
  error?: string;
  created_at?: string;
  [key: string]: any;
}

export const useDocuments = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshDocuments = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log(`Fetching documents from: ${API_URL}${ENDPOINTS.DOCUMENTS}`);
      
      const response = await fetch(`${API_URL}${ENDPOINTS.DOCUMENTS}`, {
        method: 'GET',
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`Error response: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error('Error details:', errorText);
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Documents response:', data);
      
      // Handle different response formats
      if (Array.isArray(data)) {
        setDocuments(data);
      } else if (data.documents && Array.isArray(data.documents)) {
        setDocuments(data.documents);
      } else {
        console.warn('Unexpected response format:', data);
        setDocuments([]);
      }
    } catch (err) {
      console.error('Error fetching documents:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const uploadDocument = useCallback(async (file: File, metadata: Record<string, any>) => {
    try {
      setError(null);
      console.log(`Uploading document to: ${API_URL}${ENDPOINTS.DOCUMENTS}`);
      console.log('File:', file.name, file.size, file.type);
      console.log('Metadata:', metadata);

      const formData = new FormData();
      formData.append('file', file);
      
      // Add metadata
      Object.entries(metadata).forEach(([key, value]) => {
        if (typeof value === 'object') {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, String(value));
        }
      });

      const response = await fetch(`${API_URL}${ENDPOINTS.DOCUMENTS}`, {
        method: 'POST',
        headers: {
          'X-API-Key': API_KEY
          // Don't set Content-Type for FormData, browser will set it with boundary
        },
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Upload error response:', errorText);
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error || 'Failed to upload document');
        } catch (jsonError) {
          throw new Error(`Failed to upload document: ${response.statusText}`);
        }
      }

      // Get response data
      const data = await response.json();
      console.log('Upload response:', data);
      
      // Create a temporary document entry while processing
      const tempDoc: Document = {
        id: data.id || `temp-${Date.now()}`,
        title: data.title || metadata.title || file.name,
        filename: file.name,
        status: 'processing',
        created_at: new Date().toISOString(),
        tags: metadata.tags || []
      };
      
      // Add to documents list
      setDocuments(prev => [tempDoc, ...prev]);
      
      // Refresh document list after a delay to get updated status
      // Increase delay to give more time for processing
      setTimeout(() => {
        refreshDocuments();
      }, 5000);  // Increased from 3000ms to 5000ms
      
      return data;
    } catch (err) {
      console.error('Error uploading document:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      throw err;
    }
  }, [refreshDocuments]);

  const deleteDocument = useCallback(async (id: string) => {
    try {
      setError(null);
      console.log(`Deleting document: ${id}`);

      const response = await fetch(`${API_URL}${ENDPOINTS.DOCUMENTS}/${id}`, {
        method: 'DELETE',
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete error response:', errorText);
        throw new Error(`Failed to delete document: ${response.statusText}`);
      }

      // Update local state
      setDocuments(prev => prev.filter(doc => doc.id !== id));
      console.log(`Document deleted: ${id}`);
    } catch (err) {
      console.error('Error deleting document:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      throw err;
    }
  }, []);

  const reprocessDocument = useCallback(async (id: string) => {
    try {
      setError(null);
      console.log(`Reprocessing document: ${id}`);
      
      // Update document status locally to show processing
      setDocuments(prev => prev.map(doc => 
        doc.id === id ? { ...doc, status: 'processing' } : doc
      ));

      // Check if REPROCESS endpoint contains '{id}' placeholder
      const reprocessEndpoint = ENDPOINTS.REPROCESS.includes('{id}') 
        ? ENDPOINTS.REPROCESS.replace('{id}', id)
        : `${ENDPOINTS.REPROCESS}/${id}`;

      const response = await fetch(`${API_URL}${reprocessEndpoint}`, {
        method: 'POST',
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Reprocess error response:', errorText);
        throw new Error(`Failed to reprocess document: ${response.statusText}`);
      }

      // Refresh document list after a delay to get updated status
      setTimeout(() => {
        refreshDocuments();
      }, 5000);  // Increased from 3000ms to 5000ms
      
      console.log(`Document reprocessing initiated: ${id}`);
    } catch (err) {
      console.error('Error reprocessing document:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      
      // Update document status back to error
      setDocuments(prev => prev.map(doc => 
        doc.id === id ? { ...doc, status: 'error', error: err instanceof Error ? err.message : 'Unknown error' } : doc
      ));
      
      throw err;
    }
  }, [refreshDocuments]);

  // Load documents on initial mount
  useEffect(() => {
    refreshDocuments();
  }, [refreshDocuments]);

  return {
    documents,
    isLoading,
    error,
    refreshDocuments,
    uploadDocument,
    deleteDocument,
    reprocessDocument
  };
}; 