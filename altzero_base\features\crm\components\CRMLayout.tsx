import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  BarChart3,
  User,
  Building,
  Target,
  Activity,
  Calendar,
  ArrowLeft,
  Users,
  MapPin,
  GitBranch,
  Layers,
  UserPlus,
  Briefcase,
  FileText
} from 'lucide-react';

interface CRMLayoutProps {
  children: React.ReactNode;
}

const CRMLayout: React.FC<CRMLayoutProps> = ({ children }) => {
  const location = useLocation();

  const menuItems = [
    {
      name: 'Dashboard',
      path: '/crm',
      icon: BarChart3,
      description: 'Overview & Analytics',
      category: 'main'
    },
    {
      name: 'Contacts',
      path: '/crm/contacts',
      icon: User,
      description: 'Manage customer contacts',
      category: 'main'
    },
    {
      name: 'Companies',
      path: '/crm/companies',
      icon: Building,
      description: 'Business relationships',
      category: 'main'
    },
    {
      name: 'Opportunities',
      path: '/crm/opportunities',
      icon: Target,
      description: 'Sales pipeline',
      category: 'main'
    },
    {
      name: 'Activities',
      path: '/crm/activities',
      icon: Activity,
      description: 'Communication log',
      category: 'main'
    },
    {
      name: 'Events',
      path: '/crm/events',
      icon: Calendar,
      description: 'Schedule & meetings',
      category: 'main'
    },
    {
      name: 'Contact Groups',
      path: '/crm/contact-groups',
      icon: UserPlus,
      description: 'Organize contacts',
      category: 'organization'
    },
    {
      name: 'Regions',
      path: '/crm/regions',
      icon: MapPin,
      description: 'Geographic regions',
      category: 'organization'
    },
    {
      name: 'Pipelines',
      path: '/crm/pipelines',
      icon: GitBranch,
      description: 'Sales pipeline setup',
      category: 'configuration'
    },
    {
      name: 'Pipeline Stages',
      path: '/crm/pipeline-stages',
      icon: Layers,
      description: 'Configure stages',
      category: 'configuration'
    },
    {
      name: 'Jobs',
      path: '/crm/jobs',
      icon: Briefcase,
      description: 'Job postings',
      category: 'recruitment'
    },
    {
      name: 'Applications',
      path: '/crm/job-applications',
      icon: FileText,
      description: 'Job applications',
      category: 'recruitment'
    }
  ];

  const isActive = (path: string) => {
    if (path === '/crm') {
      return location.pathname === '/crm' || location.pathname === '/crm/dashboard';
    }
    return location.pathname === path;
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900">CRM</h1>
            </div>
          </div>
          
          {/* Back to Main Dashboard */}
          <Link
            to="/"
            className="flex items-center text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Link>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 p-4 overflow-y-auto">
          {/* Main CRM Features */}
          <div className="mb-6">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Core CRM
            </h3>
            <ul className="space-y-1">
              {menuItems.filter(item => item.category === 'main').map((item) => {
                const Icon = item.icon;
                const active = isActive(item.path);

                return (
                  <li key={item.path}>
                    <Link
                      to={item.path}
                      className={`flex items-center p-3 rounded-lg transition-colors group ${
                        active
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                      }`}
                    >
                      <Icon className={`w-5 h-5 mr-3 ${
                        active ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'
                      }`} />
                      <div className="flex-1">
                        <div className={`font-medium ${active ? 'text-blue-700' : 'text-gray-900'}`}>
                          {item.name}
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          {item.description}
                        </div>
                      </div>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Organization */}
          <div className="mb-6">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Organization
            </h3>
            <ul className="space-y-1">
              {menuItems.filter(item => item.category === 'organization').map((item) => {
                const Icon = item.icon;
                const active = isActive(item.path);

                return (
                  <li key={item.path}>
                    <Link
                      to={item.path}
                      className={`flex items-center p-2 rounded-lg transition-colors group ${
                        active
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                      }`}
                    >
                      <Icon className={`w-4 h-4 mr-3 ${
                        active ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'
                      }`} />
                      <div className="flex-1">
                        <div className={`text-sm font-medium ${active ? 'text-blue-700' : 'text-gray-900'}`}>
                          {item.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {item.description}
                        </div>
                      </div>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Configuration */}
          <div className="mb-6">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Configuration
            </h3>
            <ul className="space-y-1">
              {menuItems.filter(item => item.category === 'configuration').map((item) => {
                const Icon = item.icon;
                const active = isActive(item.path);

                return (
                  <li key={item.path}>
                    <Link
                      to={item.path}
                      className={`flex items-center p-2 rounded-lg transition-colors group ${
                        active
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                      }`}
                    >
                      <Icon className={`w-4 h-4 mr-3 ${
                        active ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'
                      }`} />
                      <div className="flex-1">
                        <div className={`text-sm font-medium ${active ? 'text-blue-700' : 'text-gray-900'}`}>
                          {item.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {item.description}
                        </div>
                      </div>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Recruitment */}
          <div className="mb-6">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Recruitment
            </h3>
            <ul className="space-y-1">
              {menuItems.filter(item => item.category === 'recruitment').map((item) => {
                const Icon = item.icon;
                const active = isActive(item.path);

                return (
                  <li key={item.path}>
                    <Link
                      to={item.path}
                      className={`flex items-center p-2 rounded-lg transition-colors group ${
                        active
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                      }`}
                    >
                      <Icon className={`w-4 h-4 mr-3 ${
                        active ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'
                      }`} />
                      <div className="flex-1">
                        <div className={`text-sm font-medium ${active ? 'text-blue-700' : 'text-gray-900'}`}>
                          {item.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {item.description}
                        </div>
                      </div>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            CRM System v1.0
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Content Area */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default CRMLayout;
