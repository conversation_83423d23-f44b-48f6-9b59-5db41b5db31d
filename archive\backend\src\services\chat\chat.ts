// src/services/chat/chat.ts
import { Document, VectorStoreIndex } from "llamaindex";
import { PineconeVectorStore } from "../vectorstore/pinecone";
import { DocumentMetadata, QueryResponse, SourceNode } from "../../types/llama";

export class ChatService {
  private vectorIndex: VectorStoreIndex | null = null;

  async initialize(documentIds?: string[]): Promise<void> {
    const pineconeStore = new PineconeVectorStore();

    const queryResponse = await pineconeStore.index.query({
      vector: Array(1024).fill(0),
      topK: 1000,
      includeMetadata: true,
    });

    const matches = queryResponse.matches || [];

    const filteredMatches = documentIds?.length
      ? matches.filter((match) => documentIds.includes(match?.id || ""))
      : matches;

    const topMatches = filteredMatches.filter(
      (m) => m.metadata?.preview && m.metadata.preview.length > 100
    );

    if (topMatches.length === 0) {
      throw new Error(
        "No matching documents with valid content found in Pinecone."
      );
    }

    const docs: Document<DocumentMetadata>[] = topMatches.map((match) => {
      return new Document({
        text: match.metadata.preview.slice(0, 1500),
        metadata: {
          id: match.id,
          title: match.metadata?.title,
          type: match.metadata?.type,
          createdAt: match.metadata?.createdAt,
          size: match.metadata?.size,
        },
      });
    });

    this.vectorIndex = await VectorStoreIndex.fromDocuments(docs);
  }

  async chat(
    query: string,
    history: { role: string; content: string }[] = [],
    documentIds?: string[]
  ): Promise<QueryResponse> {
    if (!this.vectorIndex || documentIds?.length) {
      await this.initialize(documentIds);
    }

    if (!this.vectorIndex) {
      throw new Error("Vector index not initialized.");
    }

    const queryEngine = this.vectorIndex.asQueryEngine();
    const response = await queryEngine.query({ query });

    const sources: SourceNode[] =
      response.sourceNodes?.map((node) => ({
        text: (node.node as any).text.slice(0, 1000), // trimmed for UI
        score: node.score,
      })) || [];

    return {
      answer: response.response,
      sources,
    };
  }
}
