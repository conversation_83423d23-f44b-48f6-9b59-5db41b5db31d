// =====================================================
// SEMRUSH API INTEGRATION TOOL FOR LANGGRAPH
// =====================================================

// Using simplified tool interface instead of LangChain BaseTool
interface ToolInterface {
  name: string;
  description: string;
  _call(input: string): Promise<string>;
}

export interface SemrushConfig {
  apiKey: string;
  enabled: boolean;
  rateLimit: number;
  timeout: number;
  retries: number;
}

export interface SemrushKeywordData {
  keyword: string;
  search_volume: number;
  keyword_difficulty: number;
  cpc: number;
  competition: 'low' | 'medium' | 'high';
  intent: 'informational' | 'navigational' | 'commercial' | 'transactional';
  trend: 'rising' | 'stable' | 'declining';
  related_keywords: string[];
}

export class SemrushTool implements ToolInterface {
  name = 'semrush_keyword_research';
  description = 'Research keywords using Semrush API for accurate search volume and competition data';
  
  private config: SemrushConfig;
  private lastRequestTime = 0;

  constructor(config: SemrushConfig) {
    this.config = config;
  }

  async _call(input: string): Promise<string> {
    try {
      const { keywords, options } = JSON.parse(input);
      
      if (!Array.isArray(keywords)) {
        throw new Error('Keywords must be an array');
      }

      const results = await this.getKeywordData(keywords, options);
      return JSON.stringify(results);
    } catch (error) {
      console.error('Semrush tool error:', error);
      throw error;
    }
  }

  // Get keyword data from Semrush API
  async getKeywordData(keywords: string[], options: any = {}): Promise<SemrushKeywordData[]> {
    if (!this.config.enabled || !this.config.apiKey) {
      throw new Error('Semrush API not configured');
    }

    const results: SemrushKeywordData[] = [];
    
    for (const keyword of keywords.slice(0, 10)) { // Limit to prevent API overuse
      try {
        await this.enforceRateLimit();
        
        // Get keyword overview
        const overviewData = await this.getKeywordOverview(keyword);
        
        // Get related keywords
        const relatedData = await this.getRelatedKeywords(keyword, 5);
        
        results.push({
          keyword,
          search_volume: overviewData.search_volume || 0,
          keyword_difficulty: overviewData.keyword_difficulty || 50,
          cpc: overviewData.cpc || 0,
          competition: this.mapCompetitionLevel(overviewData.competition),
          intent: this.inferKeywordIntent(keyword),
          trend: overviewData.trend || 'stable',
          related_keywords: relatedData
        });

      } catch (error) {
        console.warn(`Failed to get Semrush data for keyword: ${keyword}`, error);
        
        // Return fallback data to prevent workflow failure
        results.push({
          keyword,
          search_volume: 0,
          keyword_difficulty: 50,
          cpc: 0,
          competition: 'medium',
          intent: this.inferKeywordIntent(keyword),
          trend: 'stable',
          related_keywords: []
        });
      }
    }

    return results;
  }

  // Get keyword overview from Semrush
  private async getKeywordOverview(keyword: string): Promise<any> {
    const url = 'https://api.semrush.com/';
    const params = new URLSearchParams({
      type: 'phrase_this',
      key: this.config.apiKey,
      phrase: keyword,
      database: 'us', // Default to US database
      export_columns: 'Ph,Nq,Cp,Co,Nr,Td'
    });

    const response = await this.makeRequest(`${url}?${params}`);
    
    if (!response.ok) {
      throw new Error(`Semrush API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.text();
    const lines = data.trim().split('\n');
    
    if (lines.length < 2) {
      throw new Error('No data returned from Semrush API');
    }

    const values = lines[1].split(';');
    
    return {
      search_volume: parseInt(values[1]) || 0,
      cpc: parseFloat(values[2]) || 0,
      competition: parseFloat(values[3]) || 0,
      results_count: parseInt(values[4]) || 0,
      keyword_difficulty: this.calculateDifficulty(parseFloat(values[3]), parseInt(values[4])),
      trend: 'stable' // Semrush doesn't provide trend in basic API
    };
  }

  // Get related keywords from Semrush
  private async getRelatedKeywords(keyword: string, limit: number = 5): Promise<string[]> {
    const url = 'https://api.semrush.com/';
    const params = new URLSearchParams({
      type: 'phrase_related',
      key: this.config.apiKey,
      phrase: keyword,
      database: 'us',
      export_columns: 'Ph,Nq',
      display_limit: limit.toString()
    });

    try {
      const response = await this.makeRequest(`${url}?${params}`);
      
      if (!response.ok) {
        return [];
      }

      const data = await response.text();
      const lines = data.trim().split('\n');
      
      return lines.slice(1) // Skip header
        .map(line => line.split(';')[0]) // Get keyword column
        .filter(kw => kw && kw !== keyword)
        .slice(0, limit);
        
    } catch (error) {
      console.warn('Failed to get related keywords from Semrush:', error);
      return [];
    }
  }

  // Make HTTP request with retry logic
  private async makeRequest(url: string): Promise<Response> {
    let lastError: Error = new Error('Unknown error');
    
    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'AltZero-pSEO-Bot/1.0'
          },
          signal: controller.signal
        });

        clearTimeout(timeout);
        return response;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retries) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  // Enforce rate limiting
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    const minInterval = 1000 / this.config.rateLimit; // Convert rate limit to interval

    if (timeSinceLastRequest < minInterval) {
      const delay = minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.lastRequestTime = Date.now();
  }

  // Map Semrush competition level to our format
  private mapCompetitionLevel(competition: number): 'low' | 'medium' | 'high' {
    if (competition < 0.33) return 'low';
    if (competition < 0.67) return 'medium';
    return 'high';
  }

  // Calculate keyword difficulty based on competition and results count
  private calculateDifficulty(competition: number, resultsCount: number): number {
    // Simple formula combining competition level and results count
    const competitionScore = competition * 50;
    const resultsScore = Math.min(resultsCount / 1000000 * 30, 30);
    const difficulty = Math.round(competitionScore + resultsScore + Math.random() * 20);
    
    return Math.min(Math.max(difficulty, 1), 100);
  }

  // Infer keyword intent
  private inferKeywordIntent(keyword: string): 'informational' | 'navigational' | 'commercial' | 'transactional' {
    const keywordLower = keyword.toLowerCase();
    
    if (keywordLower.includes('buy') || keywordLower.includes('price') || keywordLower.includes('cost') || keywordLower.includes('purchase')) {
      return 'transactional';
    }
    
    if (keywordLower.includes('review') || keywordLower.includes('vs') || keywordLower.includes('best') || keywordLower.includes('compare')) {
      return 'commercial';
    }
    
    if (keywordLower.includes('how') || keywordLower.includes('what') || keywordLower.includes('guide') || keywordLower.includes('tutorial')) {
      return 'informational';
    }
    
    return 'informational';
  }

  // Check if Semrush API is available
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.config.enabled || !this.config.apiKey) {
        return false;
      }

      // Test with a simple query
      const url = 'https://api.semrush.com/';
      const params = new URLSearchParams({
        type: 'phrase_this',
        key: this.config.apiKey,
        phrase: 'test',
        database: 'us',
        export_columns: 'Ph'
      });

      const response = await this.makeRequest(`${url}?${params}`);
      return response.ok;
    } catch (error) {
      console.warn('Semrush health check failed:', error);
      return false;
    }
  }

  // Get API usage statistics
  async getUsageStats(): Promise<any> {
    try {
      const url = 'https://api.semrush.com/';
      const params = new URLSearchParams({
        type: 'info',
        key: this.config.apiKey
      });

      const response = await this.makeRequest(`${url}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to get usage stats');
      }

      const data = await response.text();
      const lines = data.trim().split('\n');
      
      if (lines.length < 2) {
        throw new Error('Invalid usage stats response');
      }

      const values = lines[1].split(';');
      
      return {
        requests_left: parseInt(values[0]) || 0,
        requests_limit: parseInt(values[1]) || 0,
        usage_percentage: values[1] ? ((parseInt(values[1]) - parseInt(values[0])) / parseInt(values[1]) * 100).toFixed(2) : 0
      };
    } catch (error) {
      console.warn('Failed to get Semrush usage stats:', error);
      return {
        requests_left: 0,
        requests_limit: 0,
        usage_percentage: 0
      };
    }
  }
}
