"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragOverEvent,
  Over,
  rectIntersection,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "../../../../../../base/components/ui/button";
import { Card, CardContent } from "../../../../../../base/components/ui/card";
import { Badge } from "../../../../../../base/components/ui/badge";
import { Input } from "../../../../../../base/components/ui/input";
import { Textarea } from "../../../../../../base/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../../base/components/ui/select";
import {
  Plus,
  Trash2,
  Edit3,
  Save,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  Image as ImageIcon,
  GripVertical,
  FileText,
  Upload,
  CheckCircle,
  AlertCircle,
  ChevronDown,
  X,
  Columns,
  Columns2,
  Columns3,
  Layout,
  ArrowRight,
} from "lucide-react";
import { useToast } from "../../../../../../base/hooks/use-toast";
import { useDocumentContext } from "../../../contexts/DocumentContext";
import {
  convertContentToBlocks,
  renderBlocksToHTML,
} from "../../../utils/documentUtils";

interface UnifiedContentEditorProps {
  sections: any[];
  readonly?: boolean;
}

// Simple draggable block component with fixed state management
function EditableBlock({
  block,
  sectionId,
  onUpdate,
  onDelete,
  readonly = false,
  isInColumn = false,
  columnId,
}: {
  block: any;
  sectionId: string;
  onUpdate: (blockId: string, content: string) => void;
  onDelete: (blockId: string) => void;
  readonly?: boolean;
  isInColumn?: boolean;
  columnId?: string;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: block.id,
    data: {
      type: "block",
      blockId: block.id,
      isInColumn,
      columnId,
    },
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(block.content || "");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Update edit content when block content changes from outside
  useEffect(() => {
    setEditContent(block.content || "");
  }, [block.content]);

  const handleSave = () => {
    // Update the parent component immediately
    onUpdate(block.id, editContent);
    setIsEditing(false);
    toast({
      title: "Saved",
      description: "Content has been saved.",
      duration: 1500,
    });
  };

  const handleCancel = () => {
    // Reset to original content
    setEditContent(block.content || "");
    setIsEditing(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageData = event.target?.result as string;
        setEditContent(imageData);
        onUpdate(block.id, imageData);
        setIsEditing(false);
        toast({
          title: "Image Added",
          description: "Image has been uploaded.",
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height =
        Math.max(120, textareaRef.current.scrollHeight) + "px";
    }
  };

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      adjustTextareaHeight();
    }
  }, [isEditing]);

  // Render content based on block type
  const renderContent = () => {
    const content = editContent || block.content || "";

    if (block.type === "image") {
      // Check if it's a placeholder or real image
      const isPlaceholder =
        content === "placeholder" ||
        content === "![Image placeholder](placeholder)" ||
        !content ||
        content.trim() === "";

      if (
        !isPlaceholder &&
        (content.startsWith("data:image") || content.startsWith("http"))
      ) {
        return (
          <div className="flex justify-center my-4">
            <img
              src={content}
              alt={block.caption || "Image"}
              className="max-w-full h-auto rounded-lg shadow-sm border"
              style={{ maxHeight: isInColumn ? "200px" : "300px" }}
              onError={(e) => {
                console.error("Error loading image:", e);
                // If image fails to load, show placeholder
                e.currentTarget.style.display = "none";
                const container = e.currentTarget.parentElement;
                if (container) {
                  container.innerHTML = `
                    <div class="border-2 border-dashed border-red-300 rounded-lg p-8 text-center text-red-500">
                      <div class="flex flex-col items-center gap-2">
                        <div class="text-xl">⚠️</div>
                        <p class="font-medium">Failed to load image</p>
                        <p class="text-xs text-red-400">The image could not be displayed</p>
                      </div>
                    </div>
                  `;
                }
              }}
            />
            {block.caption && (
              <p className="text-sm text-gray-600 mt-2 text-center italic">
                {block.caption}
              </p>
            )}
          </div>
        );
      } else {
        // Show placeholder for empty or placeholder images
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500 hover:border-blue-400 hover:text-blue-600 transition-colors">
            <div className="flex flex-col items-center gap-2">
              <ImageIcon className="h-8 w-8" />
              <p className="font-medium">Click to add an image</p>
              <p className="text-xs text-gray-400">
                Upload JPG, PNG, GIF, or WebP
              </p>
            </div>
          </div>
        );
      }
    }

    // For non-image content, use the HTML renderer
    return (
      <div
        className="prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{
          __html: renderBlocksToHTML([{ ...block, content }]),
        }}
      />
    );
  };

  if (isEditing) {
    return (
      <div ref={setNodeRef} style={style} className="mb-3">
        <div className="border-2 border-blue-500 rounded-lg p-4 bg-blue-50">
          <div className="flex items-center justify-between mb-3">
            <Badge variant="secondary" className="text-xs">
              {block.type} - Editing
            </Badge>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleSave}
                className="h-7 px-3 bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Save
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancel}
                className="h-7 px-2"
              >
                <X className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onDelete(block.id)}
                className="h-7 px-2 text-red-600"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {block.type === "header" && (
            <div className="mb-3">
              <Select
                value={(block.level || 2).toString()}
                onValueChange={(value) => {
                  const level = parseInt(value);
                  const prefix = "#".repeat(level);
                  const text = editContent.replace(/^#+\s*/, "");
                  const newContent = `${prefix} ${text}`;
                  setEditContent(newContent);
                }}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Heading 1</SelectItem>
                  <SelectItem value="2">Heading 2</SelectItem>
                  <SelectItem value="3">Heading 3</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-3">
            {block.type === "image" ? (
              <div className="space-y-3">
                {(() => {
                  const isPlaceholder =
                    editContent === "placeholder" ||
                    editContent === "![Image placeholder](placeholder)" ||
                    !editContent ||
                    editContent.trim() === "";
                  const hasValidImage =
                    !isPlaceholder &&
                    (editContent.startsWith("data:image") ||
                      editContent.startsWith("http"));

                  return (
                    <>
                      {hasValidImage && (
                        <div className="flex justify-center">
                          <img
                            src={editContent}
                            alt="Preview"
                            className="max-w-full h-auto rounded-lg border"
                            style={{ maxHeight: "200px" }}
                          />
                        </div>
                      )}

                      {!hasValidImage && (
                        <div className="border-2 border-dashed border-blue-300 rounded-lg p-6 text-center bg-blue-50">
                          <div className="flex flex-col items-center gap-2">
                            <ImageIcon className="h-6 w-6 text-blue-500" />
                            <p className="text-sm text-blue-700 font-medium">
                              No image selected
                            </p>
                            <p className="text-xs text-blue-600">
                              Choose an image to upload
                            </p>
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2">
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          {hasValidImage ? "Replace Image" : "Upload Image"}
                        </Button>
                        {hasValidImage && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditContent(
                                "![Image placeholder](placeholder)"
                              );
                              onUpdate(
                                block.id,
                                "![Image placeholder](placeholder)"
                              );
                            }}
                            className="gap-2 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                            Remove
                          </Button>
                        )}
                      </div>
                    </>
                  );
                })()}
              </div>
            ) : (
              <Textarea
                ref={textareaRef}
                value={editContent}
                onChange={(e) => {
                  setEditContent(e.target.value);
                  setTimeout(adjustTextareaHeight, 0);
                }}
                placeholder={`Enter ${block.type} content...`}
                className="w-full p-3 border rounded-md resize-none min-h-[120px] bg-white"
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={setNodeRef} style={style} className="mb-3">
      <div
        className={`group relative hover:bg-gray-50 transition-colors rounded-lg p-3 cursor-pointer border ${
          isInColumn
            ? "border-purple-200 bg-purple-50 hover:border-purple-300"
            : "border-transparent hover:border-gray-200"
        }`}
        onClick={() => !readonly && setIsEditing(true)}
      >
        {!readonly && (
          <>
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsEditing(true);
                  }}
                  className="h-6 w-6 p-0 bg-white shadow-sm"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(block.id);
                  }}
                  className="h-6 w-6 p-0 bg-white shadow-sm text-red-600"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <div
              {...attributes}
              {...listeners}
              className="absolute left-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
            >
              <div className="flex items-center gap-1 opacity-60">
                <GripVertical className="h-4 w-4 text-gray-300" />
                {isInColumn && (
                  <div className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full opacity-75">
                    Drag out to extract
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        <div className="ml-6">{renderContent()}</div>
      </div>
    </div>
  );
}

// Add Block Buttons Component
function AddBlockButtons({
  onAddBlock,
  readonly = false,
}: {
  onAddBlock: (type: "text" | "header" | "list" | "image" | "columns") => void;
  readonly?: boolean;
}) {
  if (readonly) return null;

  return (
    <div className="flex justify-center py-3">
      <div className="flex gap-2 opacity-50 hover:opacity-100 transition-opacity">
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAddBlock("text")}
          className="gap-2 h-8 px-3 text-xs bg-white shadow-sm hover:bg-blue-50 hover:border-blue-300"
        >
          <Type className="h-3 w-3" />
          Text
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAddBlock("header")}
          className="gap-2 h-8 px-3 text-xs bg-white shadow-sm hover:bg-blue-50 hover:border-blue-300"
        >
          <Heading2 className="h-3 w-3" />
          Heading
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAddBlock("list")}
          className="gap-2 h-8 px-3 text-xs bg-white shadow-sm hover:bg-blue-50 hover:border-blue-300"
        >
          <List className="h-3 w-3" />
          List
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAddBlock("image")}
          className="gap-2 h-8 px-3 text-xs bg-white shadow-sm hover:bg-blue-50 hover:border-blue-300"
        >
          <ImageIcon className="h-3 w-3" />
          Image
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAddBlock("columns")}
          className="gap-2 h-8 px-3 text-xs bg-white shadow-sm hover:bg-blue-50 hover:border-blue-300"
        >
          <Columns2 className="h-3 w-3" />
          Columns
        </Button>
      </div>
    </div>
  );
}

// Enhanced Droppable Column Component
function DroppableColumn({
  columnId,
  blocks,
  sectionId,
  onUpdate,
  onDelete,
  onAddBlock,
  readonly = false,
  columnWidth = "1fr",
  onUpdateColumnWidth,
}: {
  columnId: string;
  blocks: any[];
  sectionId: string;
  onUpdate: (blockId: string, content: string) => void;
  onDelete: (blockId: string) => void;
  onAddBlock: (
    type: "text" | "header" | "list" | "image",
    columnId?: string
  ) => void;
  readonly?: boolean;
  columnWidth?: string;
  onUpdateColumnWidth?: (columnId: string, width: string) => void;
}) {
  const { setNodeRef, isOver } = useSortable({
    id: columnId,
    data: {
      type: "column",
      columnId,
    },
  });

  const [isEditingWidth, setIsEditingWidth] = useState(false);
  const [tempWidth, setTempWidth] = useState(columnWidth);

  const handleWidthSave = () => {
    if (onUpdateColumnWidth) {
      onUpdateColumnWidth(columnId, tempWidth);
    }
    setIsEditingWidth(false);
  };

  // Convert different width formats to flex value
  const getFlexValue = () => {
    if (columnWidth.includes("%")) {
      return `0 0 ${columnWidth}`;
    }
    if (columnWidth.includes("px")) {
      return `0 0 ${columnWidth}`;
    }
    return columnWidth; // assume it's already a flex value like "1fr" or "2"
  };

  return (
    <div
      ref={setNodeRef}
      className={`min-h-[200px] p-3 rounded-lg border-2 transition-all ${
        isOver
          ? "border-blue-500 bg-blue-50"
          : "border-dashed border-gray-300 bg-gray-50"
      }`}
      style={{
        flex: getFlexValue(),
        minWidth: "200px", // Ensure minimum width for 3+ columns
        maxWidth: "100%",
      }}
    >
      {/* Column Width Control */}
      {!readonly && (
        <div className="mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
          {isEditingWidth ? (
            <div className="flex gap-1 mb-2">
              <Select value={tempWidth} onValueChange={setTempWidth}>
                <SelectTrigger className="h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Small (1fr)</SelectItem>
                  <SelectItem value="2">Medium (2fr)</SelectItem>
                  <SelectItem value="3">Large (3fr)</SelectItem>
                  <SelectItem value="25%">25%</SelectItem>
                  <SelectItem value="33%">33%</SelectItem>
                  <SelectItem value="50%">50%</SelectItem>
                  <SelectItem value="66%">66%</SelectItem>
                  <SelectItem value="75%">75%</SelectItem>
                </SelectContent>
              </Select>
              <Button
                size="sm"
                onClick={handleWidthSave}
                className="h-6 px-2 text-xs bg-green-600 hover:bg-green-700"
              >
                ✓
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setTempWidth(columnWidth);
                  setIsEditingWidth(false);
                }}
                className="h-6 px-2 text-xs"
              >
                ✕
              </Button>
            </div>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsEditingWidth(true)}
              className="h-6 px-2 text-xs mb-2 opacity-50 hover:opacity-100"
            >
              Width: {columnWidth}
            </Button>
          )}
        </div>
      )}

      <div className="space-y-2">
        {blocks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="flex flex-col items-center gap-2">
              <Layout className="h-6 w-6 text-gray-400" />
              <p className="text-sm">Drop blocks here</p>
            </div>
          </div>
        ) : (
          <SortableContext
            items={blocks.map((b: any) => b.id)}
            strategy={verticalListSortingStrategy}
          >
            {blocks.map((block: any) => (
              <EditableBlock
                key={block.id}
                block={block}
                sectionId={sectionId}
                onUpdate={onUpdate}
                onDelete={onDelete}
                readonly={readonly}
                isInColumn={true}
                columnId={columnId}
              />
            ))}
          </SortableContext>
        )}

        {/* Add Block Buttons for Column */}
        {!readonly && (
          <div className="flex justify-center py-2 opacity-50 hover:opacity-100 transition-opacity">
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onAddBlock("text", columnId)}
                className="h-6 px-2 text-xs bg-white"
              >
                <Type className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onAddBlock("header", columnId)}
                className="h-6 px-2 text-xs bg-white"
              >
                <Heading2 className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onAddBlock("list", columnId)}
                className="h-6 px-2 text-xs bg-white"
              >
                <List className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onAddBlock("image", columnId)}
                className="h-6 px-2 text-xs bg-white"
              >
                <ImageIcon className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Enhanced Columns Container Component
function ColumnsContainer({
  block,
  sectionId,
  onUpdate,
  onDelete,
  onUpdateColumns,
  readonly = false,
}: {
  block: any;
  sectionId: string;
  onUpdate: (blockId: string, content: string) => void;
  onDelete: (blockId: string) => void;
  onUpdateColumns: (blockId: string, columns: any[]) => void;
  readonly?: boolean;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: block.id });

  const [isEditing, setIsEditing] = useState(false);
  const [columnCount, setColumnCount] = useState(block.columns?.length || 2);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleColumnCountChange = (newCount: number) => {
    setColumnCount(newCount);
    const currentColumns = block.columns || [];

    if (newCount > currentColumns.length) {
      // Add new columns with proper width initialization
      const newColumns = [...currentColumns];
      for (let i = currentColumns.length; i < newCount; i++) {
        newColumns.push({
          id: `column-${block.id}-${i}`,
          blocks: [],
          width: "1", // Default equal width
        });
      }
      onUpdateColumns(block.id, newColumns);
    } else if (newCount < currentColumns.length) {
      // Remove columns (move blocks from removed columns to the last remaining column)
      const newColumns = currentColumns.slice(0, newCount);
      const removedColumns = currentColumns.slice(newCount);

      // Move all blocks from removed columns to the last column
      if (newColumns.length > 0) {
        const allRemovedBlocks = removedColumns.flatMap(
          (col: any) => col.blocks || []
        );
        newColumns[newColumns.length - 1].blocks = [
          ...(newColumns[newColumns.length - 1].blocks || []),
          ...allRemovedBlocks,
        ];

        // Ensure all remaining columns have width property
        newColumns.forEach((col: any) => {
          if (!col.width) {
            col.width = "1";
          }
        });
      }

      onUpdateColumns(block.id, newColumns);
    }
  };

  const handleAddBlockToColumn = (
    type: "text" | "header" | "list" | "image",
    columnId?: string
  ) => {
    const newBlock = {
      id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
      type,
      content:
        type === "header"
          ? "## New Heading"
          : type === "list"
          ? "- New list item"
          : type === "image"
          ? "![Image placeholder](placeholder)"
          : "New content...",
    };

    const updatedColumns = (block.columns || []).map((col: any) => {
      if (col.id === columnId) {
        return {
          ...col,
          blocks: [...(col.blocks || []), newBlock],
        };
      }
      return col;
    });

    onUpdateColumns(block.id, updatedColumns);
  };

  const handleUpdateColumnWidth = (columnId: string, width: string) => {
    const updatedColumns = (block.columns || []).map((col: any) => {
      if (col.id === columnId) {
        return {
          ...col,
          width: width,
        };
      }
      return col;
    });

    onUpdateColumns(block.id, updatedColumns);
  };

  if (isEditing) {
    return (
      <div ref={setNodeRef} style={style} className="mb-4">
        <div className="border-2 border-blue-500 rounded-lg p-4 bg-blue-50">
          <div className="flex items-center justify-between mb-4">
            <Badge variant="secondary" className="text-xs">
              Columns Layout - Editing
            </Badge>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => setIsEditing(false)}
                className="h-7 px-3 bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Done
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onDelete(block.id)}
                className="h-7 px-2 text-red-600"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div className="mb-4">
            <label className="text-sm font-medium mb-2 block">
              Number of Columns:
            </label>
            <div className="flex gap-2">
              {[2, 3, 4].map((count) => (
                <Button
                  key={count}
                  size="sm"
                  variant={columnCount === count ? "default" : "outline"}
                  onClick={() => handleColumnCountChange(count)}
                  className="gap-2"
                >
                  {count === 2 && <Columns2 className="h-4 w-4" />}
                  {count === 3 && <Columns3 className="h-4 w-4" />}
                  {count === 4 && <Columns className="h-4 w-4" />}
                  {count} Cols
                </Button>
              ))}
            </div>
          </div>

          {/* Column Width Presets */}
          <div className="mb-4">
            <label className="text-sm font-medium mb-2 block">
              Column Presets:
            </label>
            <div className="flex gap-2 flex-wrap">
              {columnCount === 2 && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const updatedColumns = (block.columns || []).map(
                        (col: any, index: number) => ({
                          ...col,
                          width: index === 0 ? "1" : "1",
                        })
                      );
                      onUpdateColumns(block.id, updatedColumns);
                    }}
                    className="text-xs"
                  >
                    Equal (50-50)
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const updatedColumns = (block.columns || []).map(
                        (col: any, index: number) => ({
                          ...col,
                          width: index === 0 ? "2" : "1",
                        })
                      );
                      onUpdateColumns(block.id, updatedColumns);
                    }}
                    className="text-xs"
                  >
                    Wide-Narrow (66-33)
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const updatedColumns = (block.columns || []).map(
                        (col: any, index: number) => ({
                          ...col,
                          width: index === 0 ? "1" : "2",
                        })
                      );
                      onUpdateColumns(block.id, updatedColumns);
                    }}
                    className="text-xs"
                  >
                    Narrow-Wide (33-66)
                  </Button>
                </>
              )}
              {columnCount === 3 && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const updatedColumns = (block.columns || []).map(
                        (col: any) => ({
                          ...col,
                          width: "1",
                        })
                      );
                      onUpdateColumns(block.id, updatedColumns);
                    }}
                    className="text-xs"
                  >
                    Equal (33-33-33)
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const updatedColumns = (block.columns || []).map(
                        (col: any, index: number) => ({
                          ...col,
                          width: index === 1 ? "2" : "1",
                        })
                      );
                      onUpdateColumns(block.id, updatedColumns);
                    }}
                    className="text-xs"
                  >
                    Center Wide (25-50-25)
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={setNodeRef} style={style} className="mb-4">
      <div className="group relative border rounded-lg p-4 hover:bg-gray-50 transition-colors">
        {!readonly && (
          <>
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  className="h-6 w-6 p-0 bg-white shadow-sm"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onDelete(block.id)}
                  className="h-6 w-6 p-0 bg-white shadow-sm text-red-600"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <div
              {...attributes}
              {...listeners}
              className="absolute left-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
            >
              <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
          </>
        )}

        <div className="ml-6">
          <div className="flex items-center gap-2 mb-3">
            <Layout className="h-4 w-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">
              {columnCount} Column Layout
            </span>
          </div>

          <div className="flex gap-4" style={{ minHeight: "100px" }}>
            {(block.columns || []).map((column: any, index: number) => {
              // Create specialized update handlers for blocks inside this column
              const handleUpdateBlockInColumn = (
                blockId: string,
                content: string
              ) => {
                const updatedColumns = block.columns.map((col: any) => {
                  if (col.id === column.id) {
                    const updatedBlocks = col.blocks.map((b: any) =>
                      b.id === blockId ? { ...b, content } : b
                    );
                    return { ...col, blocks: updatedBlocks };
                  }
                  return col;
                });
                onUpdateColumns(block.id, updatedColumns);
              };

              const handleDeleteBlockInColumn = (blockId: string) => {
                const updatedColumns = block.columns.map((col: any) => {
                  if (col.id === column.id) {
                    const updatedBlocks = col.blocks.filter(
                      (b: any) => b.id !== blockId
                    );
                    return { ...col, blocks: updatedBlocks };
                  }
                  return col;
                });
                onUpdateColumns(block.id, updatedColumns);
              };

              return (
                <DroppableColumn
                  key={column.id}
                  columnId={column.id}
                  blocks={column.blocks || []}
                  sectionId={sectionId}
                  onUpdate={handleUpdateBlockInColumn}
                  onDelete={handleDeleteBlockInColumn}
                  onAddBlock={handleAddBlockToColumn}
                  readonly={readonly}
                  columnWidth={column.width || "1"}
                  onUpdateColumnWidth={handleUpdateColumnWidth}
                />
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

// Enhanced Side-by-side drop zone component
function SideBySideDropZone({
  blockIndex,
  onCreateColumnsFromBlocks,
  isActive,
  targetBlock,
}: {
  blockIndex: number;
  onCreateColumnsFromBlocks: (
    leftBlockIndex: number,
    rightBlockIndex: number
  ) => void;
  isActive: boolean;
  targetBlock?: any;
}) {
  const { setNodeRef } = useSortable({
    id: `side-drop-${blockIndex}`,
    data: {
      type: "side-drop",
      blockIndex,
    },
  });

  if (!isActive) return null;

  const isColumnTarget = targetBlock?.type === "columns";

  return (
    <div
      ref={setNodeRef}
      className={`absolute right-0 top-0 h-full w-12 ${
        isColumnTarget
          ? "bg-green-500 bg-opacity-20 border-2 border-dashed border-green-400"
          : "bg-blue-500 bg-opacity-20 border-2 border-dashed border-blue-400"
      } rounded-r-lg flex flex-col items-center justify-center transition-all`}
    >
      {isColumnTarget ? (
        <div className="flex flex-col items-center text-green-600">
          <Plus className="h-3 w-3 mb-1" />
          <span className="text-xs font-bold">ADD</span>
          <span className="text-xs">COL</span>
        </div>
      ) : (
        <div className="flex flex-col items-center text-blue-600">
          <Columns2 className="h-4 w-4 mb-1" />
          <span className="text-xs font-bold">2COL</span>
        </div>
      )}
    </div>
  );
}

export const UnifiedContentEditor: React.FC<UnifiedContentEditorProps> = ({
  sections,
  readonly = false,
}) => {
  const { handleUpdateBlock, setEditableSections } = useDocumentContext();
  const { toast } = useToast();
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(
    null
  );
  const [activeBlockId, setActiveBlockId] = useState<string | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);

  // Initialize selected section
  useEffect(() => {
    if (!selectedSectionId && sections.length > 0) {
      setSelectedSectionId(sections[0].id);
    }
  }, [sections, selectedSectionId]);

  const selectedSection = selectedSectionId
    ? sections.find((s: any) => s.id === selectedSectionId)
    : sections[0];

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Get current blocks for the selected section
  const getCurrentBlocks = () => {
    if (!selectedSection) return [];

    // Initialize blocks if they don't exist
    if (!selectedSection.blocks && selectedSection.content) {
      const blocks = convertContentToBlocks(selectedSection.content);
      // Update the section with blocks
      setEditableSections((prev: any) =>
        prev.map((section: any) =>
          section.id === selectedSection.id ? { ...section, blocks } : section
        )
      );
      return blocks;
    }

    return selectedSection.blocks || [];
  };

  // Update section content through context
  const updateSectionContent = (sectionId: string, newContent: string) => {
    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === sectionId) {
          const blocks = convertContentToBlocks(newContent);
          return {
            ...section,
            content: newContent,
            blocks: blocks,
          };
        }
        return section;
      })
    );
  };

  // Update columns for a specific block
  const handleUpdateColumns = (blockId: string, columns: any[]) => {
    if (!selectedSection) return;

    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === selectedSection.id) {
          const updatedBlocks = section.blocks.map((block: any) =>
            block.id === blockId ? { ...block, columns } : block
          );
          return {
            ...section,
            blocks: updatedBlocks,
          };
        }
        return section;
      })
    );

    toast({
      title: "Columns Updated",
      description: "Column layout has been updated.",
      duration: 1500,
    });
  };

  const handleUpdateBlockContent = (blockId: string, content: string) => {
    if (!selectedSection) return;

    // Update the block content through the context
    handleUpdateBlock(selectedSection.id, blockId, { content });

    toast({
      title: "Saved",
      description: "Content has been saved.",
      duration: 1500,
    });
  };

  const handleDeleteBlock = (blockId: string) => {
    if (!selectedSection) return;

    const blocks = getCurrentBlocks();
    const updatedBlocks = blocks.filter((b: any) => b.id !== blockId);

    // Update through context by converting blocks back to content
    const textContent = updatedBlocks
      .map((block: any) => {
        switch (block.type) {
          case "header":
            return block.content;
          case "image":
            // Always include image blocks, even if empty, so they show up as placeholders
            if (
              block.content &&
              block.content !== "![Image placeholder](placeholder)"
            ) {
              return `![${block.caption || "Image"}](${block.content})`;
            } else {
              return "![Image placeholder](placeholder)"; // Placeholder for empty images
            }
          case "columns":
            return "[Column Layout]"; // Placeholder for columns in content
          default:
            return block.content;
        }
      })
      .join("\n\n"); // Don't filter - keep all blocks including empty image placeholders

    updateSectionContent(selectedSection.id, textContent);

    toast({
      title: "Block Deleted",
      description: "Content block has been removed.",
    });
  };

  const addNewBlock = (
    type: "text" | "header" | "list" | "image" | "columns",
    position?: number,
    columnId?: string
  ) => {
    if (!selectedSection) return;

    const blocks = getCurrentBlocks();
    let newBlock: any;

    if (type === "columns") {
      newBlock = {
        id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
        type: "columns",
        content: "[Column Layout]",
        columns: [
          {
            id: `column-${Date.now()}-0`,
            blocks: [],
            width: "1",
          },
          {
            id: `column-${Date.now()}-1`,
            blocks: [],
            width: "1",
          },
        ],
      };
    } else {
      newBlock = {
        id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
        type,
        content:
          type === "header"
            ? "## New Heading"
            : type === "list"
            ? "- New list item"
            : type === "image"
            ? "![Image placeholder](placeholder)"
            : "New content...",
      };
    }

    // If we're adding to a specific column, handle that
    if (columnId) {
      setEditableSections((prev: any) =>
        prev.map((section: any) => {
          if (section.id === selectedSection.id) {
            const updatedBlocks = section.blocks.map((block: any) => {
              if (block.type === "columns" && block.columns) {
                const updatedColumns = block.columns.map((col: any) => {
                  if (col.id === columnId) {
                    return {
                      ...col,
                      blocks: [...(col.blocks || []), newBlock],
                    };
                  }
                  return col;
                });
                return { ...block, columns: updatedColumns };
              }
              return block;
            });
            return {
              ...section,
              blocks: updatedBlocks,
            };
          }
          return section;
        })
      );
    } else {
      // Regular block addition
      const updatedBlocks = [...blocks];
      // Insert at specific position or at the end
      if (position !== undefined) {
        updatedBlocks.splice(position, 0, newBlock);
      } else {
        updatedBlocks.push(newBlock);
      }

      // Update section with new blocks
      setEditableSections((prev: any) =>
        prev.map((section: any) => {
          if (section.id === selectedSection.id) {
            return {
              ...section,
              blocks: updatedBlocks,
            };
          }
          return section;
        })
      );
    }

    toast({
      title: "Block Added",
      description: `New ${type} block added.`,
    });
  };

  // Enhanced drag handling for columns and side-by-side
  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;

    if (over?.data?.current?.type === "column") {
      setDragOverColumn(over.data.current.columnId);
    } else {
      setDragOverColumn(null);
    }
  };

  // Enhanced drag end with smart column management
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setDragOverColumn(null);

    if (!over || !selectedSection) return;

    const activeData = active.data.current;
    const overData = over.data.current;
    const blocks = getCurrentBlocks();

    // Handle side-by-side column creation or expansion
    if (overData?.type === "side-drop" && activeData?.type === "block") {
      const draggedBlockIndex = blocks.findIndex(
        (b: any) => b.id === active.id
      );
      const targetBlockIndex = overData.blockIndex;

      if (
        draggedBlockIndex !== -1 &&
        targetBlockIndex !== -1 &&
        draggedBlockIndex !== targetBlockIndex
      ) {
        const targetBlock = blocks[targetBlockIndex];

        // Check if target block is a column layout
        if (targetBlock.type === "columns") {
          // Add to existing column layout instead of creating nested columns
          expandExistingColumnLayout(draggedBlockIndex, targetBlockIndex);
        } else {
          // Create new column layout
          createColumnsFromSideBySide(draggedBlockIndex, targetBlockIndex);
        }
        setActiveBlockId(null);
        return;
      }
    }

    // Handle dropping block into a column
    if (overData?.type === "column" && activeData?.type === "block") {
      const sourceBlockId = active.id as string;
      const targetColumnId = overData.columnId;
      const sourceBlock = blocks.find((b: any) => b.id === sourceBlockId);

      if (!sourceBlock) return;

      // Remove block from its current location and add to target column
      if (activeData.isInColumn) {
        // Moving from column to column
        moveBlockBetweenColumns(
          sourceBlockId,
          sourceBlock,
          targetColumnId,
          activeData.columnId
        );
      } else {
        // Moving from main area to column
        moveBlockToColumn(sourceBlockId, sourceBlock, targetColumnId);
      }

      toast({
        title: "Block Moved",
        description: "Block moved to column successfully.",
      });

      setActiveBlockId(null);
      return;
    }

    // Handle dropping block outside columns (extract from column)
    if (!overData?.type && activeData?.isInColumn && activeData?.columnId) {
      extractBlockFromColumn(active.id as string, activeData.columnId);
      setActiveBlockId(null);
      return;
    }

    // Regular block reordering (only for non-column blocks)
    if (active.id !== over?.id && !overData?.type && !activeData?.isInColumn) {
      const oldIndex = blocks.findIndex((b: any) => b.id === active.id);
      const newIndex = blocks.findIndex((b: any) => b.id === over?.id);

      if (oldIndex === -1 || newIndex === -1) return;

      const reorderedBlocks = arrayMove(blocks, oldIndex, newIndex);

      setEditableSections((prev: any) =>
        prev.map((section: any) => {
          if (section.id === selectedSection.id) {
            return {
              ...section,
              blocks: reorderedBlocks,
            };
          }
          return section;
        })
      );

      toast({
        title: "Blocks Reordered",
        description: "Content blocks have been reordered.",
      });
    }

    setActiveBlockId(null);
  };

  // Expand existing column layout by adding a new column
  const expandExistingColumnLayout = (
    draggedBlockIndex: number,
    targetColumnIndex: number
  ) => {
    const blocks = getCurrentBlocks();
    const draggedBlock = blocks[draggedBlockIndex];
    const targetColumnBlock = blocks[targetColumnIndex];

    if (
      !draggedBlock ||
      !targetColumnBlock ||
      targetColumnBlock.type !== "columns"
    )
      return;

    // Add new column to existing layout
    const newColumn = {
      id: `column-${Date.now()}-${targetColumnBlock.columns.length}`,
      blocks: [draggedBlock],
      width: "1",
    };

    const updatedColumns = [...(targetColumnBlock.columns || []), newColumn];

    // Remove the dragged block from main area and update column layout
    const updatedBlocks = blocks.filter(
      (_: any, index: number) => index !== draggedBlockIndex
    );

    // Update the target column block
    const adjustedTargetIndex =
      draggedBlockIndex < targetColumnIndex
        ? targetColumnIndex - 1
        : targetColumnIndex;
    updatedBlocks[adjustedTargetIndex] = {
      ...targetColumnBlock,
      columns: updatedColumns,
    };

    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === selectedSection.id) {
          return {
            ...section,
            blocks: updatedBlocks,
          };
        }
        return section;
      })
    );

    toast({
      title: "Column Added",
      description: `Expanded to ${updatedColumns.length} columns!`,
    });
  };

  // Move block between columns
  const moveBlockBetweenColumns = (
    sourceBlockId: string,
    sourceBlock: any,
    targetColumnId: string,
    sourceColumnId: string
  ) => {
    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === selectedSection.id) {
          const updatedBlocks = section.blocks.map((block: any) => {
            if (block.type === "columns" && block.columns) {
              const updatedColumns = block.columns.map((col: any) => {
                // Remove from source column
                if (col.id === sourceColumnId) {
                  return {
                    ...col,
                    blocks: (col.blocks || []).filter(
                      (b: any) => b.id !== sourceBlockId
                    ),
                  };
                }
                // Add to target column
                if (col.id === targetColumnId) {
                  return {
                    ...col,
                    blocks: [...(col.blocks || []), sourceBlock],
                  };
                }
                return col;
              });
              return { ...block, columns: updatedColumns };
            }
            return block;
          });
          return {
            ...section,
            blocks: updatedBlocks,
          };
        }
        return section;
      })
    );
  };

  // Move block from main area to column
  const moveBlockToColumn = (
    sourceBlockId: string,
    sourceBlock: any,
    targetColumnId: string
  ) => {
    const blocks = getCurrentBlocks();
    const remainingBlocks = blocks.filter((b: any) => b.id !== sourceBlockId);

    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === selectedSection.id) {
          const updatedBlocks = remainingBlocks.map((block: any) => {
            if (block.type === "columns" && block.columns) {
              const updatedColumns = block.columns.map((col: any) => {
                if (col.id === targetColumnId) {
                  return {
                    ...col,
                    blocks: [...(col.blocks || []), sourceBlock],
                  };
                }
                return col;
              });
              return { ...block, columns: updatedColumns };
            }
            return block;
          });
          return {
            ...section,
            blocks: updatedBlocks,
          };
        }
        return section;
      })
    );
  };

  // Extract block from column (drag out to main area)
  const extractBlockFromColumn = (blockId: string, sourceColumnId: string) => {
    let extractedBlock: any = null;

    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === selectedSection.id) {
          const updatedBlocks = [...section.blocks];

          // Find and extract the block from columns
          section.blocks.forEach((block: any, blockIndex: number) => {
            if (block.type === "columns" && block.columns) {
              block.columns.forEach((col: any) => {
                if (col.id === sourceColumnId) {
                  const blockToExtract = (col.blocks || []).find(
                    (b: any) => b.id === blockId
                  );
                  if (blockToExtract) {
                    extractedBlock = blockToExtract;

                    // Remove block from column
                    const updatedColumns = block.columns.map((c: any) => {
                      if (c.id === sourceColumnId) {
                        return {
                          ...c,
                          blocks: (c.blocks || []).filter(
                            (b: any) => b.id !== blockId
                          ),
                        };
                      }
                      return c;
                    });

                    // Check if any columns are now empty and remove them if needed
                    const nonEmptyColumns = updatedColumns.filter(
                      (c: any) => (c.blocks || []).length > 0
                    );

                    if (nonEmptyColumns.length <= 1) {
                      // If only one column left with content, convert back to regular blocks
                      if (nonEmptyColumns.length === 1) {
                        const remainingBlocks = nonEmptyColumns[0].blocks || [];
                        updatedBlocks.splice(blockIndex, 1, ...remainingBlocks);
                      } else {
                        // Remove empty column layout
                        updatedBlocks.splice(blockIndex, 1);
                      }
                    } else {
                      // Update column layout with remaining columns
                      updatedBlocks[blockIndex] = {
                        ...block,
                        columns: nonEmptyColumns,
                      };
                    }
                  }
                }
              });
            }
          });

          // Add extracted block to main area
          if (extractedBlock) {
            updatedBlocks.push(extractedBlock);
          }

          return {
            ...section,
            blocks: updatedBlocks,
          };
        }
        return section;
      })
    );

    if (extractedBlock) {
      toast({
        title: "Block Extracted",
        description: "Block moved out of column layout.",
      });
    }
  };

  // Create columns from side-by-side drop
  const createColumnsFromSideBySide = (
    leftBlockIndex: number,
    rightBlockIndex: number
  ) => {
    const blocks = getCurrentBlocks();
    const leftBlock = blocks[leftBlockIndex];
    const rightBlock = blocks[rightBlockIndex];

    if (!leftBlock || !rightBlock) return;

    // Create new column layout with proper width initialization
    const newColumnBlock = {
      id: `block-${Date.now()}-${Math.random().toString(36).slice(2)}`,
      type: "columns",
      content: "[Column Layout]",
      columns: [
        {
          id: `column-${Date.now()}-0`,
          blocks: [leftBlock],
          width: "1",
        },
        {
          id: `column-${Date.now()}-1`,
          blocks: [rightBlock],
          width: "1",
        },
      ],
    };

    // Remove original blocks and insert column layout
    const updatedBlocks = blocks.filter(
      (_: any, index: number) =>
        index !== leftBlockIndex && index !== rightBlockIndex
    );

    // Insert at the position of the leftmost block
    const insertIndex = Math.min(leftBlockIndex, rightBlockIndex);
    updatedBlocks.splice(insertIndex, 0, newColumnBlock);

    // Update section
    setEditableSections((prev: any) =>
      prev.map((section: any) => {
        if (section.id === selectedSection.id) {
          return {
            ...section,
            blocks: updatedBlocks,
          };
        }
        return section;
      })
    );

    toast({
      title: "Columns Created",
      description: "Two blocks combined into a column layout!",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}

      {/* Section Selection */}
      <Card className="border shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-gray-600" />
              <span className="font-semibold text-gray-900">Edit Section:</span>
            </div>

            <div className="flex-1">
              <Select
                value={selectedSectionId || ""}
                onValueChange={setSelectedSectionId}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a section to edit" />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {sections.map((section: any, index: number) => (
                    <SelectItem key={section.id} value={section.id}>
                      {index + 1}. {section.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {sections.length} sections
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Selected Section Content */}
      {selectedSection && (
        <Card className="border shadow-lg">
          <CardContent className="p-6">
            {/* Section Header */}
            <div className="flex items-center justify-between mb-6 pb-4 border-b">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {sections.findIndex((s: any) => s.id === selectedSection.id) +
                    1}
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {selectedSection.title}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {getCurrentBlocks().length} content blocks
                  </p>
                </div>
              </div>
            </div>

            {/* Section Content */}
            <div className="min-h-[200px]">
              {(() => {
                const blocks = getCurrentBlocks();

                if (blocks.length > 0) {
                  return (
                    <DndContext
                      sensors={sensors}
                      collisionDetection={rectIntersection}
                      onDragStart={(event) =>
                        setActiveBlockId(event.active.id as string)
                      }
                      onDragOver={handleDragOver}
                      onDragEnd={handleDragEnd}
                    >
                      <SortableContext
                        items={[
                          ...blocks.map((b: any) => b.id),
                          ...blocks
                            .filter((b: any) => b.type === "columns")
                            .flatMap((b: any) =>
                              (b.columns || []).map((col: any) => col.id)
                            ),
                          ...blocks
                            .filter((b: any) => b.type === "columns")
                            .flatMap((b: any) =>
                              (b.columns || []).flatMap((col: any) =>
                                (col.blocks || []).map((block: any) => block.id)
                              )
                            ),
                          // Add side-drop zones
                          ...blocks.map(
                            (_: any, index: number) => `side-drop-${index}`
                          ),
                        ]}
                        strategy={verticalListSortingStrategy}
                      >
                        <div className="space-y-1">
                          {/* Add blocks at the beginning */}
                          <AddBlockButtons
                            onAddBlock={(type) => addNewBlock(type, 0)}
                            readonly={readonly}
                          />

                          {blocks.map((block: any, index: number) => (
                            <React.Fragment key={block.id}>
                              <div className="relative">
                                {block.type === "columns" ? (
                                  <ColumnsContainer
                                    block={block}
                                    sectionId={selectedSection.id}
                                    onUpdate={handleUpdateBlockContent}
                                    onDelete={handleDeleteBlock}
                                    onUpdateColumns={handleUpdateColumns}
                                    readonly={readonly}
                                  />
                                ) : (
                                  <EditableBlock
                                    block={block}
                                    sectionId={selectedSection.id}
                                    onUpdate={handleUpdateBlockContent}
                                    onDelete={handleDeleteBlock}
                                    readonly={readonly}
                                    isInColumn={false}
                                    columnId={dragOverColumn || undefined}
                                  />
                                )}

                                {/* Side-by-side drop zone */}
                                {activeBlockId &&
                                  activeBlockId !== block.id && (
                                    <SideBySideDropZone
                                      blockIndex={index}
                                      onCreateColumnsFromBlocks={
                                        createColumnsFromSideBySide
                                      }
                                      isActive={true}
                                      targetBlock={block}
                                    />
                                  )}
                              </div>

                              {/* Add blocks after each block */}
                              <AddBlockButtons
                                onAddBlock={(type) =>
                                  addNewBlock(type, index + 1)
                                }
                                readonly={readonly}
                              />
                            </React.Fragment>
                          ))}
                        </div>
                      </SortableContext>

                      <DragOverlay>
                        {activeBlockId ? (
                          <div className="opacity-75 transform rotate-2 shadow-2xl">
                            {(() => {
                              const activeBlock = blocks.find(
                                (b: any) => b.id === activeBlockId
                              );
                              if (!activeBlock) return null;

                              if (activeBlock.type === "columns") {
                                return (
                                  <ColumnsContainer
                                    block={activeBlock}
                                    sectionId={selectedSection.id}
                                    onUpdate={() => {}}
                                    onDelete={() => {}}
                                    onUpdateColumns={() => {}}
                                    readonly={true}
                                  />
                                );
                              } else {
                                return (
                                  <EditableBlock
                                    block={activeBlock}
                                    sectionId={selectedSection.id}
                                    onUpdate={() => {}}
                                    onDelete={() => {}}
                                    readonly={true}
                                    isInColumn={false}
                                    columnId={dragOverColumn || undefined}
                                  />
                                );
                              }
                            })()}
                          </div>
                        ) : null}
                      </DragOverlay>
                    </DndContext>
                  );
                }

                return (
                  <div className="text-center py-12 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                    <div className="flex flex-col items-center gap-4">
                      <div className="p-4 bg-gray-200 rounded-full">
                        <Type className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-700 mb-2">
                          No content in this section yet
                        </h4>
                        <p className="text-gray-500 mb-4">
                          Add your first content block to get started
                        </p>
                      </div>
                      <div className="flex gap-3">
                        <Button
                          size="sm"
                          onClick={() => addNewBlock("text")}
                          className="gap-2 bg-blue-600 hover:bg-blue-700"
                          disabled={readonly}
                        >
                          <Type className="h-4 w-4" />
                          Add Text
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addNewBlock("header")}
                          className="gap-2"
                          disabled={readonly}
                        >
                          <Heading2 className="h-4 w-4" />
                          Add Heading
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addNewBlock("columns")}
                          className="gap-2"
                          disabled={readonly}
                        >
                          <Columns2 className="h-4 w-4" />
                          Add Columns
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UnifiedContentEditor;
