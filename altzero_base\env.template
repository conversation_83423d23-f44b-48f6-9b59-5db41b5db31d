# Frontend Environment Variables
VITE_API_BASE_URL=http://localhost:3001
VITE_API_KEY=your-api-key-here

# Backend Environment Variables
PORT=3001
NODE_ENV=development

# AI Services
OPENAI_API_KEY=your-openai-api-key-here
LLAMA_CLOUD_API_KEY=your-llama-cloud-api-key-here
LLAMA_CLOUD_BASE_URL=https://api.cloud.llamaindex.ai

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_INDEX_NAME=altzero-knowledge-base

# LangSmith (Optional - for debugging and monitoring)
LANGSMITH_API_KEY=your-langsmith-api-key-here
LANGSMITH_PROJECT=altzero-knowledge-base
LANGSMITH_TRACING=true

# Chat Configuration
CHAT_MODEL=gpt-4
CHAT_TEMPERATURE=0.7
MAX_TOKENS=2048
MAX_RETRIEVAL_RESULTS=10
MIN_RELEVANCE_SCORE=0.7

# Document Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Supabase (if using)
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database (if using PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/altzero_db 