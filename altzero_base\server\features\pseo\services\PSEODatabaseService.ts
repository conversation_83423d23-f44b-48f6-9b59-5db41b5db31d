import { supabase } from "../../../base/common/apps/supabase";

export class PSEODatabaseService {
  // =====================================================
  // AGENT JOB MANAGEMENT
  // =====================================================

  async createAgentJob(websiteId: string, jobType: string, agentName: string, options?: {
    parentJobId?: string;
    priority?: number;
    inputData?: Record<string, unknown>;
  }): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_jobs')
        .insert({
          website_id: websiteId,
          job_type: jobType,
          agent_name: agentName,
          parent_job_id: options?.parentJobId,
          priority: options?.priority || 0,
          status: 'queued',
          result_data: options?.inputData || {},
          retry_count: 0,
          max_retries: 3
        })
        .select('id')
        .single();

      if (error) {
        throw new Error(`Failed to create agent job: ${error.message}`);
      }
      
      return data.id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error creating agent job: ${errorMessage}`);
    }
  }

  async updateAgentJob(jobId: string, updates: {
    status?: string;
    startedAt?: string;
    completedAt?: string;
    processingTimeSeconds?: number;
    resultData?: Record<string, unknown>;
    errorMessage?: string;
    retryCount?: number;
  }): Promise<void> {
    try {
      const updateData: Record<string, unknown> = {};
      
      if (updates.status) updateData.status = updates.status;
      if (updates.startedAt) updateData.started_at = updates.startedAt;
      if (updates.completedAt) updateData.completed_at = updates.completedAt;
      if (updates.processingTimeSeconds) updateData.processing_time_seconds = updates.processingTimeSeconds;
      if (updates.resultData) updateData.result_data = updates.resultData;
      if (updates.errorMessage) updateData.error_message = updates.errorMessage;
      if (updates.retryCount !== undefined) updateData.retry_count = updates.retryCount;

      const { error } = await supabase
        .from('pseo_agent_jobs')
        .update(updateData)
        .eq('id', jobId);

      if (error) {
        throw new Error(`Failed to update agent job: ${error.message}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error updating agent job: ${errorMessage}`);
    }
  }

  async getAgentJobById(jobId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_jobs')
        .select('*')
        .eq('id', jobId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(`Failed to fetch agent job: ${error.message}`);
      }
      
      return data;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error fetching agent job: ${errorMessage}`);
    }
  }

  async getAgentJobs(websiteId: string, options?: {
    status?: string;
    jobType?: string;
    limit?: number;
  }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_agent_jobs')
        .select('*')
        .eq('website_id', websiteId)
        .order('created_at', { ascending: false });

      if (options?.status) {
        query = query.eq('status', options.status);
      }
      if (options?.jobType) {
        query = query.eq('job_type', options.jobType);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch agent jobs: ${error.message}`);
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error fetching agent jobs: ${errorMessage}`);
    }
  }

  // =====================================================
  // FULL SITE ANALYSIS JOBS
  // =====================================================

  async createFullSiteAnalysisJob(websiteId: string, analysisTypes: string[], options?: Record<string, unknown>): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_jobs')
        .insert({
          website_id: websiteId,
          job_type: 'page_discovery',
          agent_name: 'AgentOrchestrator',
          priority: 1,
          status: 'queued',
          result_data: {
            analysis_types: analysisTypes,
            options: options || {}
          },
          retry_count: 0,
          max_retries: 1
        })
        .select('id')
        .single();

      if (error) {
        throw new Error(`Failed to create full site analysis job: ${error.message}`);
      }
      
      return data.id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error creating full site analysis job: ${errorMessage}`);
    }
  }

  // =====================================================
  // DATA STORAGE METHODS
  // =====================================================

  async saveDiscoveredPages(websiteId: string, pages: Array<{
    url: string;
    title?: string;
    metaDescription?: string;
    pageType?: string;
    status?: string;
    discoveredVia?: string;
    lastCrawled?: string;
    wordCount?: number;
    responseCode?: number;
    loadTimeMs?: number;
  }>): Promise<void> {
    try {
      const insertData = pages.map(page => ({
        website_id: websiteId,
        url: page.url,
        title: page.title,
        meta_description: page.metaDescription,
        page_type: page.pageType || 'page',
        status: page.status || 'discovered',
        discovered_via: page.discoveredVia || 'manual',
        last_crawled: page.lastCrawled,
        word_count: page.wordCount,
        response_code: page.responseCode,
        load_time_ms: page.loadTimeMs
      }));

      const { error } = await supabase
        .from('pseo_website_pages')
        .upsert(insertData, { 
          onConflict: 'website_id,url',
          ignoreDuplicates: false 
        });

      if (error) {
        throw new Error(`Failed to save discovered pages: ${error.message}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error saving discovered pages: ${errorMessage}`);
    }
  }

  async saveKeywordData(websiteId: string, keywords: Array<{
    keyword: string;
    searchVolume?: number;
    keywordDifficulty?: number;
    cpc?: number;
    competition?: string;
    rankingPosition?: number;
    rankingUrl?: string;
    intent?: string;
    dataSource?: string;
  }>): Promise<void> {
    try {
      const insertData = keywords.map(kw => ({
        website_id: websiteId,
        keyword: kw.keyword,
        search_volume: kw.searchVolume,
        keyword_difficulty: kw.keywordDifficulty,
        cpc: kw.cpc,
        competition: kw.competition,
        ranking_position: kw.rankingPosition,
        ranking_url: kw.rankingUrl,
        intent: kw.intent,
        data_source: kw.dataSource || 'manual'
      }));

      const { error } = await supabase
        .from('pseo_keywords')
        .upsert(insertData, { 
          onConflict: 'website_id,keyword',
          ignoreDuplicates: false 
        });

      if (error) {
        throw new Error(`Failed to save keyword data: ${error.message}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error saving keyword data: ${errorMessage}`);
    }
  }

  async saveContentOpportunities(websiteId: string, opportunities: Array<{
    targetKeyword: string;
    contentType: string;
    title: string;
    contentBrief?: string;
    targetWordCount?: number;
    priority?: string;
    difficultyScore?: number;
    estimatedTraffic?: number;
    status?: string;
    generatedContent?: string;
    aiModelUsed?: string;
  }>): Promise<void> {
    try {
      const insertData = opportunities.map(opp => ({
        website_id: websiteId,
        target_keyword: opp.targetKeyword,
        content_type: opp.contentType,
        title: opp.title,
        content_brief: opp.contentBrief,
        target_word_count: opp.targetWordCount,
        priority: opp.priority || 'medium',
        difficulty_score: opp.difficultyScore,
        estimated_traffic: opp.estimatedTraffic,
        status: opp.status || 'identified',
        generated_content: opp.generatedContent,
        ai_model_used: opp.aiModelUsed
      }));

      const { error } = await supabase
        .from('pseo_content_opportunities')
        .upsert(insertData, { 
          onConflict: 'website_id,target_keyword,content_type',
          ignoreDuplicates: false 
        });

      if (error) {
        throw new Error(`Failed to save content opportunities: ${error.message}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error saving content opportunities: ${errorMessage}`);
    }
  }

  async updateWebsiteAnalysis(websiteId: string, analysis: {
    totalPages?: number;
    crawledPages?: number;
    totalKeywords?: number;
    rankingKeywords?: number;
    totalBacklinks?: number;
    referringDomains?: number;
    domainAuthority?: number;
    organicTrafficEstimate?: number;
    contentGapsIdentified?: number;
    technicalIssuesCount?: number;
    lastFullAnalysis?: string;
    analysisStatus?: string;
  }): Promise<void> {
    try {
      const updateData: Record<string, unknown> = { website_id: websiteId };
      
      if (analysis.totalPages !== undefined) updateData.total_pages = analysis.totalPages;
      if (analysis.crawledPages !== undefined) updateData.crawled_pages = analysis.crawledPages;
      if (analysis.totalKeywords !== undefined) updateData.total_keywords = analysis.totalKeywords;
      if (analysis.rankingKeywords !== undefined) updateData.ranking_keywords = analysis.rankingKeywords;
      if (analysis.totalBacklinks !== undefined) updateData.total_backlinks = analysis.totalBacklinks;
      if (analysis.referringDomains !== undefined) updateData.referring_domains = analysis.referringDomains;
      if (analysis.domainAuthority !== undefined) updateData.domain_authority = analysis.domainAuthority;
      if (analysis.organicTrafficEstimate !== undefined) updateData.organic_traffic_estimate = analysis.organicTrafficEstimate;
      if (analysis.contentGapsIdentified !== undefined) updateData.content_gaps_identified = analysis.contentGapsIdentified;
      if (analysis.technicalIssuesCount !== undefined) updateData.technical_issues_count = analysis.technicalIssuesCount;
      if (analysis.lastFullAnalysis) updateData.last_full_analysis = analysis.lastFullAnalysis;
      if (analysis.analysisStatus) updateData.analysis_status = analysis.analysisStatus;

      const { error } = await supabase
        .from('pseo_website_analysis')
        .upsert(updateData, { 
          onConflict: 'website_id',
          ignoreDuplicates: false 
        });

      if (error) {
        throw new Error(`Failed to update website analysis: ${error.message}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error updating website analysis: ${errorMessage}`);
    }
  }

  // =====================================================
  // WEBSITE OPERATIONS
  // =====================================================

  async getWebsiteById(websiteId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('pseo_websites')
        .select('*')
        .eq('id', websiteId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw new Error(`Failed to fetch website: ${error.message}`);
      }
      
      return data;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error fetching website: ${errorMessage}`);
    }
  }

  // =====================================================
  // PROGRESS AND STATUS TRACKING
  // =====================================================

  async getRunningAgentJobs(websiteId?: string): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_agent_jobs')
        .select('*')
        .eq('status', 'running')
        .order('created_at', { ascending: false });

      if (websiteId) {
        query = query.eq('website_id', websiteId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch running jobs: ${error.message}`);
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error fetching running jobs: ${errorMessage}`);
    }
  }

  async cancelAgentJob(jobId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('pseo_agent_jobs')
        .update({ 
          status: 'cancelled',
          completed_at: new Date().toISOString(),
          error_message: 'Job cancelled by user'
        })
        .eq('id', jobId);

      if (error) {
        throw new Error(`Failed to cancel agent job: ${error.message}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Unexpected error cancelling agent job: ${errorMessage}`);
    }
  }

  // =====================================================
  // GENERIC DATABASE OPERATIONS (for ToolRegistry)
  // =====================================================

  async executeQuery(sql: string, params: any[] = []): Promise<any> {
    try {
      // Note: Supabase doesn't support raw SQL queries directly
      // This is a placeholder for compatibility with ToolRegistry
      console.warn('executeQuery called but not implemented for Supabase');
      return { data: [], error: null };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Query execution failed: ${errorMessage}`);
    }
  }

  async insert(table: string, data: Record<string, any>): Promise<any> {
    try {
      const { data: result, error } = await supabase
        .from(table)
        .insert(data)
        .select()
        .single();

      if (error) {
        throw new Error(`Insert failed: ${error.message}`);
      }

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Insert operation failed: ${errorMessage}`);
    }
  }

  async update(table: string, data: Record<string, any>, where: Record<string, any>): Promise<any> {
    try {
      let query = supabase.from(table).update(data);

      // Apply where conditions
      Object.entries(where).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      const { data: result, error } = await query.select();

      if (error) {
        throw new Error(`Update failed: ${error.message}`);
      }

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Update operation failed: ${errorMessage}`);
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid URL format';
      throw new Error(`Invalid URL format: ${errorMessage}`);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('pseo_websites')
        .select('id')
        .limit(1);

      return !error;
    } catch (error: unknown) {
      return false;
    }
  }
}

export const pseoDbService = new PSEODatabaseService(); 