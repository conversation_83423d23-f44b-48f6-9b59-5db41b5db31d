import express from 'express';
import cors from 'cors';
import fileUpload from 'express-fileupload';
import session from 'express-session';
import { validateApiKey } from './middleware/auth';
import { environment } from './config/environment';
import { ScopingStreamService } from './services/scoping/scoping-stream';
import { SimpleDocumentParser } from './services/document/simpleParser';

// Declare session types
declare module 'express-session' {
  interface SessionData {
    documentStore?: {
      [key: string]: {
        id: string;
        filename: string;
        content: string;
        size: number;
      }
    }
  }
}

// Initialize the app
const app = express();

// Configure middleware
app.use(cors({
  origin: 'http://localhost:5173', // Frontend URL
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
  credentials: true,
}));

// Configure file upload
app.use(express.json());
app.use(fileUpload({
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  useTempFiles: true,
  tempFileDir: '/tmp/'
}));

// Session configuration
app.use(session({
  secret: environment.sessionSecret || 'scopingai-secret-key',
  resave: false,
  saveUninitialized: true,
  cookie: { secure: environment.nodeEnv === 'production' }
}));

// Logging middleware
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Create service instances
const scopingService = new ScopingStreamService();
const documentParser = new SimpleDocumentParser();

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Document upload endpoint
app.post('/documents', validateApiKey, async (req, res) => {
  try {
    // Check if files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({ error: 'No files were uploaded' });
    }

    const uploadedFile = req.files.file;
    
    // Process the document
    const document = await documentParser.processDocument(uploadedFile);
    
    // Store document in session for later use
    if (!req.session.documentStore) {
      req.session.documentStore = {};
    }
    req.session.documentStore[document.id] = document;
    
    // Return success with document ID
    return res.status(200).json({
      id: document.id,
      filename: document.filename,
      size: document.size
    });
  } catch (error: any) {
    console.error('Error uploading document:', error);
    return res.status(500).json({ error: error.message || 'Failed to process document' });
  }
});

// Scoping document generation endpoint
app.post('/scoping/stream', validateApiKey, (req, res) => {
  scopingService.streamResponse(req, res);
});

export default app; 