const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';
const BACKEND_TYPE = import.meta.env.VITE_BACKEND_TYPE || 'typescript';

interface BackendConfig {
  baseUrl: string;
  endpoints: {
    chat: string;
    documents: string;
    proposals: string;
  };
}

const pythonConfig: BackendConfig = {
  baseUrl: `${API_URL}/api`,
  endpoints: {
    chat: '/chat',
    documents: '/documents',
    proposals: '/proposals',
  },
};

const typescriptConfig: BackendConfig = {
  baseUrl: API_URL,
  endpoints: {
    chat: '/api/chat',
    documents: '/api/documents',
    proposals: '/api/proposals',
  },
};

export const backendConfig: BackendConfig = BACKEND_TYPE === 'python' ? pythonConfig : typescriptConfig;

export const getApiUrl = (endpoint: keyof BackendConfig['endpoints']) => {
  return `${backendConfig.baseUrl}${backendConfig.endpoints[endpoint]}`;
};

export const ENDPOINTS = {
  CHAT: '/api/chat',
  DOCUMENTS: '/api/documents',
  PROPOSALS: '/api/proposals',
  // ...
}; 