/* Professional Document Print Styles for PDF Generation */

/* Page break utilities */
.page-break-before {
  page-break-before: always;
  break-before: page;
}

.page-break-after {
  page-break-after: always;
  break-after: page;
}

.page-break-inside-avoid {
  page-break-inside: avoid;
  break-inside: avoid;
}

/* Cover page styles */
.document-cover-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background: white;
}

.cover-logo {
  max-width: 200px;
  max-height: 120px;
  margin-bottom: 3rem;
  object-fit: contain;
}

.cover-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  line-height: 1.2;
  color: #1a365d;
}

.cover-client {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #2d3748;
}

.cover-type {
  font-size: 1.2rem;
  font-style: italic;
  margin-bottom: 3rem;
  color: #4a5568;
}

.cover-date {
  font-size: 1rem;
  color: #718096;
  margin-top: auto;
}

/* Content page styles */
.document-content-page {
  min-height: 100vh;
  padding: 1.5rem;
  background: white;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #2d3748;
}

.document-header {
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #718096;
}

.document-footer {
  border-top: 1px solid #e2e8f0;
  padding-top: 0.5rem;
  margin-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #a0aec0;
}

/* Section styles */
.document-section {
  margin-bottom: 3rem;
  page-break-inside: avoid;
}

.section-title {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1a365d;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 3px solid #3182ce;
}

.section-content {
  font-size: 1rem;
  line-height: 1.7;
  text-align: justify;
}

.section-content p {
  margin-bottom: 1rem;
}

.section-content h1,
.section-content h2,
.section-content h3 {
  color: #2d3748;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.section-content h1 {
  font-size: 1.4rem;
}

.section-content h2 {
  font-size: 1.2rem;
}

.section-content h3 {
  font-size: 1.1rem;
}

.section-content ul,
.section-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.section-content li {
  margin-bottom: 0.5rem;
}

/* Table styles */
.section-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.section-content th,
.section-content td {
  border: 1px solid #e2e8f0;
  padding: 0.75rem;
  text-align: left;
}

.section-content th {
  background-color: #f7fafc;
  font-weight: 600;
  color: #2d3748;
}

/* Print-specific styles */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  body {
    margin: 0;
    padding: 0;
    background: white !important;
  }

  .document-cover-page,
  .document-content-page {
    margin: 0;
    padding: 1.5rem;
    box-shadow: none;
    border: none;
  }

  .page-break-before {
    page-break-before: always;
  }

  .page-break-after {
    page-break-after: always;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid;
  }

  /* Hide navigation and UI elements */
  .no-print {
    display: none !important;
  }

  /* Ensure proper spacing */
  .document-section {
    orphans: 3;
    widows: 3;
  }

  /* Optimize font sizes for print */
  .cover-title {
    font-size: 2.2rem;
  }

  .section-title {
    font-size: 1.6rem;
  }

  .section-content {
    font-size: 0.95rem;
  }
}

/* PDF generation specific styles */
.pdf-document {
  background: white;
  color: #2d3748;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.pdf-document * {
  box-sizing: border-box;
}

/* Ensure images fit properly */
.pdf-document img {
  max-width: 100%;
  height: auto;
  page-break-inside: avoid;
}

/* Professional spacing */
.pdf-document .section-spacing {
  margin-bottom: 2rem;
}

/* Highlight boxes */
.highlight-box {
  background-color: #f7fafc;
  border-left: 4px solid #3182ce;
  padding: 1rem;
  margin: 1rem 0;
  page-break-inside: avoid;
}

/* Quote styles */
.quote-block {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #4a5568;
}

/* Professional list styles */
.professional-list {
  list-style: none;
  padding-left: 0;
}

.professional-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.professional-list li::before {
  content: "▸";
  color: #3182ce;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .document-content-page {
    padding: 1rem;
  }

  .cover-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }
}
