# Plugin System User & System Level Feature Control

## Overview

This document outlines a comprehensive plan to implement feature control based on user level and system level in the database, allowing granular control over which plugins/features are available to users based on their subscription, role, and system-wide configuration.

## Current State Analysis

### Existing Plugin Registry Structure
- **Frontend Registry**: `altzero_base/plugins/registry.ts` - Controls UI feature availability
- **Backend Registry**: `altzero_base/server/plugins/registry.ts` - Controls API/backend feature availability
- **Current Features**: knowledge, pseo, aichat, crm, scopingAi
- **Current Control**: Static boolean `enabled` flag per plugin

### Existing Database Structure
- **Profiles Table**: Contains user role (`role` field with 'user' default)
- **Subscription System**: Complete subscription management with plan tiers, resource limits
- **Organization System**: Multi-tenant with roles (admin/member)

## Proposed Solution Architecture

### 1. System-Level Feature Control

#### 1.1 New Database Tables

```sql
-- System-wide plugin configuration
CREATE TABLE system_plugin_config (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    plugin_key TEXT NOT NULL UNIQUE, -- matches PLUGIN_REGISTRY keys
    is_enabled BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT true, -- if false, only specific users can access
    min_subscription_tier INTEGER DEFAULT 0, -- minimum tier level required
    required_permissions TEXT[] DEFAULT '{}', -- required permissions
    configuration JSONB DEFAULT '{}', -- plugin-specific config
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User-specific plugin access control
CREATE TABLE user_plugin_access (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    plugin_key TEXT NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    granted_by UUID REFERENCES auth.users(id), -- who granted this access
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE, -- optional expiration
    metadata JSONB DEFAULT '{}', -- additional metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, plugin_key)
);

-- Organization-level plugin access control
CREATE TABLE organisation_plugin_access (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organisation_id UUID NOT NULL REFERENCES organisations(id) ON DELETE CASCADE,
    plugin_key TEXT NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    granted_by UUID REFERENCES auth.users(id), -- who granted this access
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE, -- optional expiration
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organisation_id, plugin_key)
);

-- Plugin feature flags (for A/B testing, gradual rollouts)
CREATE TABLE plugin_feature_flags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    plugin_key TEXT NOT NULL,
    flag_key TEXT NOT NULL,
    is_enabled BOOLEAN DEFAULT false,
    rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    target_user_ids UUID[] DEFAULT '{}',
    target_organisation_ids UUID[] DEFAULT '{}',
    conditions JSONB DEFAULT '{}', -- complex conditions
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(plugin_key, flag_key)
);
```

#### 1.2 Enhanced Plan Tiers Integration

```sql
-- Link plugins to subscription plans
CREATE TABLE plan_plugin_access (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    plan_id UUID NOT NULL REFERENCES plan_tiers(id) ON DELETE CASCADE,
    plugin_key TEXT NOT NULL,
    is_included BOOLEAN DEFAULT true,
    feature_limits JSONB DEFAULT '{}', -- plugin-specific limits
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(plan_id, plugin_key)
);
```

### 2. Enhanced User Profile Structure

#### 2.1 Profile Table Enhancements

```sql
-- Add plugin-related fields to profiles
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS plugin_permissions TEXT[] DEFAULT '{}';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_plugin_admin BOOLEAN DEFAULT false;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS plugin_access_level TEXT DEFAULT 'standard' 
    CHECK (plugin_access_level IN ('standard', 'premium', 'enterprise', 'admin'));
```

### 3. Service Layer Implementation

#### 3.1 Plugin Access Service

Create `altzero_base/base/services/pluginAccessService.ts`:

```typescript
export interface PluginAccessResult {
  hasAccess: boolean;
  reason?: string;
  limitations?: Record<string, any>;
  expiresAt?: string;
}

export interface UserPluginContext {
  userId: string;
  organisationId?: string;
  subscriptionTier?: number;
  userRole?: string;
  isPluginAdmin?: boolean;
}

export class PluginAccessService {
  // Check if user has access to a specific plugin
  async checkPluginAccess(
    pluginKey: string, 
    context: UserPluginContext
  ): Promise<PluginAccessResult>
  
  // Get all accessible plugins for a user
  async getUserAccessiblePlugins(
    context: UserPluginContext
  ): Promise<string[]>
  
  // Grant plugin access to user
  async grantUserPluginAccess(
    userId: string,
    pluginKey: string,
    grantedBy: string,
    expiresAt?: string
  ): Promise<void>
  
  // Revoke plugin access from user
  async revokeUserPluginAccess(
    userId: string,
    pluginKey: string
  ): Promise<void>
}
```

#### 3.2 Enhanced Plugin Registry

Update both frontend and backend registries to support dynamic access control:

```typescript
// Enhanced PluginConfig interface
export interface PluginConfig {
  enabled: boolean;
  name: string;
  icon: string;
  route: string;
  backend: boolean;
  permissions?: string[];
  dependencies?: string[];
  description?: string;
  version?: string;
  // New fields for access control
  requiresSubscription?: boolean;
  minSubscriptionTier?: number;
  requiresExplicitAccess?: boolean;
  isSystemPlugin?: boolean; // core system plugins that can't be disabled
}

// Dynamic plugin registry functions
export const getAccessiblePlugins = async (
  userId: string,
  organisationId?: string
): Promise<Record<string, PluginConfig>>

export const isPluginAccessible = async (
  pluginName: string,
  userId: string,
  organisationId?: string
): Promise<boolean>
```

### 4. Access Control Logic Flow

#### 4.1 Plugin Access Determination Algorithm

```
For each plugin access request:
1. Check system_plugin_config.is_enabled (system-wide toggle)
2. Check user's subscription tier vs min_subscription_tier
3. Check plan_plugin_access for user's current plan
4. Check user_plugin_access for explicit grants/denials
5. Check organisation_plugin_access if user is in an org
6. Check plugin_feature_flags for any active flags
7. Apply any time-based restrictions (expires_at)
8. Return final access decision with reasoning
```

#### 4.2 Hierarchy of Access Control

```
Priority Order (highest to lowest):
1. System-wide disable (system_plugin_config.is_enabled = false)
2. Explicit user denial (user_plugin_access.is_enabled = false)
3. Explicit user grant (user_plugin_access.is_enabled = true)
4. Organization-level access (organisation_plugin_access)
5. Subscription plan access (plan_plugin_access)
6. Default plugin configuration (PLUGIN_REGISTRY.enabled)
```

### 5. Integration with Subscription System

#### 5.1 Subscription-Based Access

- Link plugins to subscription tiers via `plan_plugin_access`
- Automatically grant/revoke access when subscription changes
- Support for plugin-specific usage limits within subscriptions
- Integration with existing `subscriptionService.ts`

#### 5.2 Enhanced Subscription Service

```typescript
// Add to subscriptionService.ts
export async function getSubscriptionPluginAccess(
  subscriptionId: string
): Promise<Record<string, any>>

export async function updatePluginAccessOnSubscriptionChange(
  subscriptionId: string,
  oldPlanId: string,
  newPlanId: string
): Promise<void>
```

### 6. Admin Interface Requirements

#### 6.1 System Admin Features

- Global plugin enable/disable toggles
- Subscription tier plugin mapping
- User-specific plugin access management
- Plugin usage analytics and monitoring
- Feature flag management interface

#### 6.2 Organization Admin Features

- Organization-level plugin access control
- Member plugin access management
- Plugin usage reporting within organization
- Bulk plugin access operations

### 7. API Endpoints

#### 7.1 Plugin Access Management APIs

```
GET /api/plugins/access - Get user's accessible plugins
POST /api/plugins/access/grant - Grant plugin access to user
DELETE /api/plugins/access/revoke - Revoke plugin access from user
GET /api/plugins/system-config - Get system plugin configuration
PUT /api/plugins/system-config - Update system plugin configuration
GET /api/plugins/feature-flags - Get active feature flags
POST /api/plugins/feature-flags - Create/update feature flags
```

### 8. Implementation Phases

#### Phase 1: Database Schema & Core Service
1. Create new database tables
2. Implement PluginAccessService
3. Create database migration scripts
4. Add basic API endpoints

#### Phase 2: Registry Integration
1. Update plugin registries to use dynamic access control
2. Implement access checking in frontend components
3. Add middleware for backend API protection
4. Update existing plugin routes to check access

#### Phase 3: Subscription Integration
1. Link plugins to subscription plans
2. Implement automatic access updates on subscription changes
3. Add plugin limits and usage tracking
4. Update subscription management UI

#### Phase 4: Admin Interface
1. Create system admin plugin management interface
2. Add organization admin plugin controls
3. Implement user plugin access management
4. Add plugin analytics and reporting

#### Phase 5: Advanced Features
1. Implement feature flags system
2. Add A/B testing capabilities
3. Create plugin marketplace foundation
4. Add plugin usage analytics

### 9. Security Considerations

- Row Level Security (RLS) policies for all new tables
- Audit logging for plugin access changes
- Rate limiting for plugin access checks
- Secure API endpoints with proper authentication
- Validation of plugin access at both frontend and backend

### 10. Migration Strategy

- Backward compatibility with existing plugin system
- Gradual migration of existing plugins
- Default access for existing users
- Data migration scripts for current subscriptions
- Rollback procedures for each phase

This comprehensive plan provides a robust foundation for implementing granular plugin access control while maintaining compatibility with the existing subscription and organization systems.

## 11. Database Functions & Procedures

### 11.1 Core Access Check Function

```sql
-- Function to check if user has access to a plugin
CREATE OR REPLACE FUNCTION check_user_plugin_access(
    p_user_id UUID,
    p_plugin_key TEXT,
    p_organisation_id UUID DEFAULT NULL
) RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_result JSONB;
    v_system_config RECORD;
    v_user_subscription RECORD;
    v_user_access RECORD;
    v_org_access RECORD;
    v_plan_access RECORD;
    v_has_access BOOLEAN DEFAULT false;
    v_reason TEXT DEFAULT 'access_denied';
    v_limitations JSONB DEFAULT '{}';
BEGIN
    -- Initialize result
    v_result := jsonb_build_object(
        'hasAccess', false,
        'reason', 'access_denied',
        'limitations', '{}'::jsonb
    );

    -- Check system-wide plugin configuration
    SELECT * INTO v_system_config
    FROM system_plugin_config
    WHERE plugin_key = p_plugin_key;

    -- If plugin is disabled system-wide, deny access
    IF v_system_config.is_enabled = false THEN
        v_result := jsonb_build_object(
            'hasAccess', false,
            'reason', 'plugin_disabled_system_wide'
        );
        RETURN v_result;
    END IF;

    -- Check explicit user access (highest priority)
    SELECT * INTO v_user_access
    FROM user_plugin_access
    WHERE user_id = p_user_id AND plugin_key = p_plugin_key
    AND (expires_at IS NULL OR expires_at > NOW());

    IF FOUND THEN
        IF v_user_access.is_enabled = false THEN
            v_result := jsonb_build_object(
                'hasAccess', false,
                'reason', 'explicitly_denied'
            );
            RETURN v_result;
        ELSE
            v_has_access := true;
            v_reason := 'explicit_grant';
        END IF;
    END IF;

    -- Check organization access if applicable
    IF p_organisation_id IS NOT NULL AND NOT v_has_access THEN
        SELECT * INTO v_org_access
        FROM organisation_plugin_access
        WHERE organisation_id = p_organisation_id AND plugin_key = p_plugin_key
        AND (expires_at IS NULL OR expires_at > NOW());

        IF FOUND AND v_org_access.is_enabled THEN
            v_has_access := true;
            v_reason := 'organization_grant';
        END IF;
    END IF;

    -- Check subscription-based access if not already granted
    IF NOT v_has_access THEN
        -- Get user's active subscription
        SELECT sr.*, pt.tier_level INTO v_user_subscription
        FROM subscription_records sr
        JOIN plan_tiers pt ON sr.plan_id = pt.id
        WHERE sr.owner_id = p_user_id
        AND sr.owner_type = 'user'
        AND sr.status = 'active'
        ORDER BY sr.created_at DESC
        LIMIT 1;

        IF FOUND THEN
            -- Check if plugin is included in user's plan
            SELECT * INTO v_plan_access
            FROM plan_plugin_access
            WHERE plan_id = v_user_subscription.plan_id
            AND plugin_key = p_plugin_key
            AND is_included = true;

            IF FOUND THEN
                v_has_access := true;
                v_reason := 'subscription_plan';
                v_limitations := COALESCE(v_plan_access.feature_limits, '{}'::jsonb);
            ELSIF v_system_config.min_subscription_tier IS NOT NULL
                AND v_user_subscription.tier_level >= v_system_config.min_subscription_tier THEN
                v_has_access := true;
                v_reason := 'subscription_tier';
            END IF;
        END IF;
    END IF;

    -- Build final result
    v_result := jsonb_build_object(
        'hasAccess', v_has_access,
        'reason', v_reason,
        'limitations', v_limitations
    );

    IF v_user_access.expires_at IS NOT NULL THEN
        v_result := v_result || jsonb_build_object('expiresAt', v_user_access.expires_at);
    END IF;

    RETURN v_result;
END;
$$;
```

### 11.2 Bulk Access Check Function

```sql
-- Function to get all accessible plugins for a user
CREATE OR REPLACE FUNCTION get_user_accessible_plugins(
    p_user_id UUID,
    p_organisation_id UUID DEFAULT NULL
) RETURNS TABLE(
    plugin_key TEXT,
    access_info JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        spc.plugin_key,
        check_user_plugin_access(p_user_id, spc.plugin_key, p_organisation_id) as access_info
    FROM system_plugin_config spc
    WHERE spc.is_enabled = true;
END;
$$;
```

## 12. Implementation Examples

### 12.1 Frontend Plugin Access Hook

```typescript
// hooks/usePluginAccess.ts
import { useEffect, useState } from 'react';
import { useUser } from '@/hooks/useUser';
import { PluginAccessService } from '@/services/pluginAccessService';

export interface PluginAccess {
  [pluginKey: string]: {
    hasAccess: boolean;
    reason?: string;
    limitations?: Record<string, any>;
    expiresAt?: string;
  };
}

export const usePluginAccess = () => {
  const { user, organisation } = useUser();
  const [pluginAccess, setPluginAccess] = useState<PluginAccess>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) return;

    const loadPluginAccess = async () => {
      try {
        const accessService = new PluginAccessService();
        const access = await accessService.getUserAccessiblePlugins({
          userId: user.id,
          organisationId: organisation?.id,
          subscriptionTier: user.subscription?.plan?.tier_level,
          userRole: user.role,
          isPluginAdmin: user.is_plugin_admin
        });
        setPluginAccess(access);
      } catch (error) {
        console.error('Failed to load plugin access:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPluginAccess();
  }, [user, organisation]);

  const hasPluginAccess = (pluginKey: string): boolean => {
    return pluginAccess[pluginKey]?.hasAccess ?? false;
  };

  const getPluginLimitations = (pluginKey: string): Record<string, any> => {
    return pluginAccess[pluginKey]?.limitations ?? {};
  };

  return {
    pluginAccess,
    hasPluginAccess,
    getPluginLimitations,
    loading
  };
};
```

### 12.2 Enhanced Plugin Registry with Access Control

```typescript
// plugins/dynamicRegistry.ts
import { PluginConfig, PLUGIN_REGISTRY } from './registry';
import { PluginAccessService } from '@/services/pluginAccessService';

export class DynamicPluginRegistry {
  private accessService: PluginAccessService;

  constructor() {
    this.accessService = new PluginAccessService();
  }

  async getAccessiblePlugins(
    userId: string,
    organisationId?: string
  ): Promise<Record<string, PluginConfig>> {
    const accessiblePlugins: Record<string, PluginConfig> = {};

    for (const [pluginKey, config] of Object.entries(PLUGIN_REGISTRY)) {
      const access = await this.accessService.checkPluginAccess(pluginKey, {
        userId,
        organisationId
      });

      if (access.hasAccess) {
        accessiblePlugins[pluginKey] = {
          ...config,
          limitations: access.limitations
        };
      }
    }

    return accessiblePlugins;
  }

  async isPluginAccessible(
    pluginKey: string,
    userId: string,
    organisationId?: string
  ): Promise<boolean> {
    const access = await this.accessService.checkPluginAccess(pluginKey, {
      userId,
      organisationId
    });

    return access.hasAccess;
  }
}
```

### 12.3 Backend Middleware for Plugin Access

```typescript
// middleware/pluginAccessMiddleware.ts
import { Request, Response, NextFunction } from 'express';
import { PluginAccessService } from '@/services/pluginAccessService';

export const requirePluginAccess = (pluginKey: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const accessService = new PluginAccessService();
      const access = await accessService.checkPluginAccess(pluginKey, {
        userId,
        organisationId: req.user?.organisation_id
      });

      if (!access.hasAccess) {
        return res.status(403).json({
          error: 'Plugin access denied',
          reason: access.reason
        });
      }

      // Add access info to request for use in handlers
      req.pluginAccess = access;
      next();
    } catch (error) {
      console.error('Plugin access check failed:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };
};

// Usage in routes
app.use('/api/crm', requirePluginAccess('crm'));
app.use('/api/pseo', requirePluginAccess('pseo'));
```

## 13. Migration Scripts

### 13.1 Initial Data Migration

```sql
-- Insert default system plugin configurations
INSERT INTO system_plugin_config (plugin_key, is_enabled, is_public, min_subscription_tier)
VALUES
    ('knowledge', true, true, 0),
    ('pseo', true, true, 1),
    ('aichat', true, true, 0),
    ('crm', true, true, 1),
    ('scopingAi', true, true, 2);

-- Create default plan plugin access for existing plans
INSERT INTO plan_plugin_access (plan_id, plugin_key, is_included)
SELECT
    pt.id,
    spc.plugin_key,
    CASE
        WHEN pt.tier_level >= spc.min_subscription_tier THEN true
        ELSE false
    END
FROM plan_tiers pt
CROSS JOIN system_plugin_config spc
WHERE pt.is_active = true;

-- Grant access to existing users based on their current subscriptions
INSERT INTO user_plugin_access (user_id, plugin_key, is_enabled, granted_by, metadata)
SELECT DISTINCT
    sr.owner_id,
    ppa.plugin_key,
    ppa.is_included,
    sr.owner_id, -- self-granted during migration
    jsonb_build_object('migration', true, 'migration_date', NOW())
FROM subscription_records sr
JOIN plan_plugin_access ppa ON sr.plan_id = ppa.plan_id
WHERE sr.owner_type = 'user'
AND sr.status = 'active'
AND ppa.is_included = true;
```

### 13.2 Rollback Procedures

```sql
-- Rollback script to remove plugin access control
-- WARNING: This will remove all plugin access control data

-- Drop new tables (in reverse dependency order)
DROP TABLE IF EXISTS plugin_feature_flags;
DROP TABLE IF EXISTS plan_plugin_access;
DROP TABLE IF EXISTS organisation_plugin_access;
DROP TABLE IF EXISTS user_plugin_access;
DROP TABLE IF EXISTS system_plugin_config;

-- Remove added columns from profiles
ALTER TABLE profiles DROP COLUMN IF EXISTS plugin_permissions;
ALTER TABLE profiles DROP COLUMN IF EXISTS is_plugin_admin;
ALTER TABLE profiles DROP COLUMN IF EXISTS plugin_access_level;

-- Drop functions
DROP FUNCTION IF EXISTS check_user_plugin_access(UUID, TEXT, UUID);
DROP FUNCTION IF EXISTS get_user_accessible_plugins(UUID, UUID);
```

This completes the comprehensive plan for implementing plugin system user and system level feature control. The solution provides:

1. **Granular Control**: System, organization, and user-level access control
2. **Subscription Integration**: Seamless integration with existing subscription system
3. **Scalability**: Supports future plugin marketplace and advanced features
4. **Security**: Proper RLS policies and audit trails
5. **Flexibility**: Feature flags, time-based access, and custom limitations
6. **Backward Compatibility**: Maintains existing functionality during migration
