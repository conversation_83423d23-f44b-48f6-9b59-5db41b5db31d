import React, {
  create<PERSON>ontext,
  use<PERSON>ontext,
  useReducer,
  use<PERSON><PERSON>back,
  ReactNode,
} from "react";
import {
  Document,
  ChatMessage,
  ChatSession,
  NavigationTab,
  DocumentStatus,
  KnowledgeBaseStats,
  UploadProgress,
} from "../types/knowledge";

interface KnowledgeState {
  // Navigation
  activeTab: NavigationTab;

  // Documents
  documents: Document[];
  selectedDocuments: string[];
  uploadProgress: Record<string, UploadProgress>;
  stats: KnowledgeBaseStats | null;

  // Chat
  currentSession: ChatSession | null;
  chatSessions: ChatSession[];
  isLoading: boolean;

  // UI State
  isUploading: boolean;
  error: string | null;
}

type KnowledgeAction =
  | { type: "SET_ACTIVE_TAB"; payload: NavigationTab }
  | { type: "SET_DOCUMENTS"; payload: Document[] }
  | { type: "ADD_DOCUMENT"; payload: Document }
  | {
      type: "UPDATE_DOCUMENT";
      payload: { id: string; updates: Partial<Document> };
    }
  | { type: "DELETE_DOCUMENT"; payload: string }
  | { type: "SET_SELECTED_DOCUMENTS"; payload: string[] }
  | { type: "TOGGLE_DOCUMENT_SELECTION"; payload: string }
  | { type: "SET_UPLOAD_PROGRESS"; payload: UploadProgress }
  | { type: "CLEAR_UPLOAD_PROGRESS"; payload: string }
  | { type: "SET_STATS"; payload: KnowledgeBaseStats }
  | { type: "SET_CURRENT_SESSION"; payload: ChatSession | null }
  | {
      type: "ADD_MESSAGE";
      payload: { sessionId: string; message: ChatMessage };
    }
  | { type: "SET_CHAT_SESSIONS"; payload: ChatSession[] }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_UPLOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "CLEAR_ERROR" };

const initialState: KnowledgeState = {
  activeTab: "knowledge",
  documents: [],
  selectedDocuments: [],
  uploadProgress: {},
  stats: null,
  currentSession: null,
  chatSessions: [],
  isLoading: false,
  isUploading: false,
  error: null,
};

function knowledgeReducer(
  state: KnowledgeState,
  action: KnowledgeAction
): KnowledgeState {
  switch (action.type) {
    case "SET_ACTIVE_TAB":
      return { ...state, activeTab: action.payload };

    case "SET_DOCUMENTS":
      return {
        ...state,
        documents: Array.isArray(action.payload) ? action.payload : [],
      };

    case "ADD_DOCUMENT":
      return {
        ...state,
        documents: action.payload
          ? [...(state.documents || []), action.payload]
          : state.documents || [],
      };

    case "UPDATE_DOCUMENT":
      return {
        ...state,
        documents: (state.documents || []).map((doc) =>
          doc.id === action.payload.id
            ? { ...doc, ...action.payload.updates }
            : doc
        ),
      };

    case "DELETE_DOCUMENT":
      return {
        ...state,
        documents: (state.documents || []).filter(
          (doc) => doc.id !== action.payload
        ),
        selectedDocuments: state.selectedDocuments.filter(
          (id) => id !== action.payload
        ),
      };

    case "SET_SELECTED_DOCUMENTS":
      return { ...state, selectedDocuments: action.payload };

    case "TOGGLE_DOCUMENT_SELECTION":
      const isSelected = state.selectedDocuments.includes(action.payload);
      return {
        ...state,
        selectedDocuments: isSelected
          ? state.selectedDocuments.filter((id) => id !== action.payload)
          : [...state.selectedDocuments, action.payload],
      };

    case "SET_UPLOAD_PROGRESS":
      return {
        ...state,
        uploadProgress: {
          ...state.uploadProgress,
          [action.payload.documentId]: action.payload,
        },
      };

    case "CLEAR_UPLOAD_PROGRESS":
      const { [action.payload]: removed, ...rest } = state.uploadProgress;
      return { ...state, uploadProgress: rest };

    case "SET_STATS":
      return { ...state, stats: action.payload };

    case "SET_CURRENT_SESSION":
      return { ...state, currentSession: action.payload };

    case "ADD_MESSAGE":
      const { sessionId, message } = action.payload;
      return {
        ...state,
        currentSession:
          state.currentSession?.id === sessionId
            ? {
                ...state.currentSession,
                messages: [...state.currentSession.messages, message],
                updatedAt: new Date().toISOString(),
              }
            : state.currentSession,
        chatSessions: state.chatSessions.map((session) =>
          session.id === sessionId
            ? {
                ...session,
                messages: [...session.messages, message],
                updatedAt: new Date().toISOString(),
              }
            : session
        ),
      };

    case "SET_CHAT_SESSIONS":
      return { ...state, chatSessions: action.payload };

    case "SET_LOADING":
      return { ...state, isLoading: action.payload };

    case "SET_UPLOADING":
      return { ...state, isUploading: action.payload };

    case "SET_ERROR":
      return { ...state, error: action.payload };

    case "CLEAR_ERROR":
      return { ...state, error: null };

    default:
      return state;
  }
}

interface KnowledgeContextType {
  state: KnowledgeState;

  // Navigation actions
  setActiveTab: (tab: NavigationTab) => void;

  // Document actions
  setDocuments: (documents: Document[]) => void;
  addDocument: (document: Document) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  deleteDocument: (id: string) => void;
  setSelectedDocuments: (ids: string[]) => void;
  toggleDocumentSelection: (id: string) => void;

  // Upload actions
  setUploadProgress: (progress: UploadProgress) => void;
  clearUploadProgress: (documentId: string) => void;

  // Stats actions
  setStats: (stats: KnowledgeBaseStats) => void;

  // Chat actions
  setCurrentSession: (session: ChatSession | null) => void;
  addMessage: (sessionId: string, message: ChatMessage) => void;
  setChatSessions: (sessions: ChatSession[]) => void;

  // UI actions
  setLoading: (loading: boolean) => void;
  setUploading: (uploading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

const KnowledgeContext = createContext<KnowledgeContextType | undefined>(
  undefined
);

export const useKnowledge = (): KnowledgeContextType => {
  const context = useContext(KnowledgeContext);
  if (!context) {
    throw new Error("useKnowledge must be used within a KnowledgeProvider");
  }
  return context;
};

interface KnowledgeProviderProps {
  children: ReactNode;
}

export const KnowledgeProvider: React.FC<KnowledgeProviderProps> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(knowledgeReducer, initialState);

  // Navigation actions
  const setActiveTab = useCallback((tab: NavigationTab) => {
    dispatch({ type: "SET_ACTIVE_TAB", payload: tab });
  }, []);

  // Document actions
  const setDocuments = useCallback((documents: Document[]) => {
    dispatch({ type: "SET_DOCUMENTS", payload: documents });
  }, []);

  const addDocument = useCallback((document: Document) => {
    dispatch({ type: "ADD_DOCUMENT", payload: document });
  }, []);

  const updateDocument = useCallback(
    (id: string, updates: Partial<Document>) => {
      dispatch({ type: "UPDATE_DOCUMENT", payload: { id, updates } });
    },
    []
  );

  const deleteDocument = useCallback((id: string) => {
    dispatch({ type: "DELETE_DOCUMENT", payload: id });
  }, []);

  const setSelectedDocuments = useCallback((ids: string[]) => {
    dispatch({ type: "SET_SELECTED_DOCUMENTS", payload: ids });
  }, []);

  const toggleDocumentSelection = useCallback((id: string) => {
    dispatch({ type: "TOGGLE_DOCUMENT_SELECTION", payload: id });
  }, []);

  // Upload actions
  const setUploadProgress = useCallback((progress: UploadProgress) => {
    dispatch({ type: "SET_UPLOAD_PROGRESS", payload: progress });
  }, []);

  const clearUploadProgress = useCallback((documentId: string) => {
    dispatch({ type: "CLEAR_UPLOAD_PROGRESS", payload: documentId });
  }, []);

  // Stats actions
  const setStats = useCallback((stats: KnowledgeBaseStats) => {
    dispatch({ type: "SET_STATS", payload: stats });
  }, []);

  // Chat actions
  const setCurrentSession = useCallback((session: ChatSession | null) => {
    dispatch({ type: "SET_CURRENT_SESSION", payload: session });
  }, []);

  const addMessage = useCallback((sessionId: string, message: ChatMessage) => {
    dispatch({ type: "ADD_MESSAGE", payload: { sessionId, message } });
  }, []);

  const setChatSessions = useCallback((sessions: ChatSession[]) => {
    dispatch({ type: "SET_CHAT_SESSIONS", payload: sessions });
  }, []);

  // UI actions
  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: "SET_LOADING", payload: loading });
  }, []);

  const setUploading = useCallback((uploading: boolean) => {
    dispatch({ type: "SET_UPLOADING", payload: uploading });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: "SET_ERROR", payload: error });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: "CLEAR_ERROR" });
  }, []);

  const value: KnowledgeContextType = {
    state,
    setActiveTab,
    setDocuments,
    addDocument,
    updateDocument,
    deleteDocument,
    setSelectedDocuments,
    toggleDocumentSelection,
    setUploadProgress,
    clearUploadProgress,
    setStats,
    setCurrentSession,
    addMessage,
    setChatSessions,
    setLoading,
    setUploading,
    setError,
    clearError,
  };

  return (
    <KnowledgeContext.Provider value={value}>
      {children}
    </KnowledgeContext.Provider>
  );
};
