import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "../../../../base/components/ui/dialog";
import { <PERSON><PERSON> } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import { Label } from "../../../../base/components/ui/label";
import { useEffect } from "react";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../../../base/components/ui/tabs";
import { DocumentEditor } from "./DocumentEditor";
import {
  File,
  Save,
  AlignLeft,
  AlignCenter,
  AlignRight,
  RotateCw,
  Edit,
  Download,
  Plus,
  Trash,
  Eye,
  MoveUp,
  MoveDown,
  Image as ImageIcon,
  PlusCircle,
  Pencil,
  X,
  Check,
} from "lucide-react";
import { Block } from "./DocumentEditor";
import { cn } from "../../../../base/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../../base/components/ui/popover";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { ScrollArea } from "../../../../base/components/ui/scroll-area";
import { toast } from "../../../../base/hooks/use-toast";

interface DocumentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: (document: { title: string; content: string }) => void;
  document: {
    title: string;
    content?: string;
    metadata?: {
      author?: string;
      createdDate?: string;
      modifiedDate?: string;
      pageCount?: number;
      fileType?: string;
    };
    sections?: Array<{
      title: string;
      content: string;
      description?: string;
      imageData?: Array<{
        data: string;
        width?: number;
        height?: number;
        position?: {
          x: number;
          y: number;
          page?: number;
          width?: number;
          height?: number;
        };
      }>;
    }>;
    structured_content?: {
      pages: Array<{
        page_number: number;
        content: Array<{
          type: string;
          content: string;
          position: {
            x: number;
            y: number;
            page: number;
          };
          font?: string;
          fontSize?: number;
          color?: string;
        }>;
      }>;
    };
  };
  readOnly?: boolean;
}

function DocumentPage({
  pageNumber,
  content,
  images,
  isEditable = false,
  onEditClick,
}: any) {
  const PAGE_WIDTH = 794;
  const PAGE_HEIGHT = 1123;

  // Debug the image data
  useEffect(() => {
    console.log(`DocumentPage ${pageNumber} rendered with:`, {
      hasContent: !!content,
      contentCount: content?.length,
      hasImages: !!images,
      imageCount: images?.length,
    });

    if (images && images.length > 0) {
      images.forEach((img: any, idx: number) => {
        console.log(`Page ${pageNumber} image ${idx}:`, {
          hasData: !!img.data,
          dataLength: img.data?.length,
          dataType: typeof img.data,
          position: img.position,
        });
      });
    }
  }, [pageNumber, content, images]);

  const pageImages =
    images?.filter((img: any) => img.position?.page === pageNumber) || [];

  console.log(`Page ${pageNumber} filtered images:`, pageImages.length);

  return (
    <div className="relative bg-white shadow-lg rounded-lg mb-6 overflow-hidden group">
      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
        <span className="text-sm font-medium text-gray-600">
          Page {pageNumber}
        </span>
        {isEditable && (
          <Button
            size="sm"
            variant="ghost"
            className="opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={() => onEditClick && onEditClick(pageNumber)}
          >
            <Pencil className="h-4 w-4 mr-1" />
            Edit Page
          </Button>
        )}
      </div>

      <div
        className="relative bg-white"
        style={{
          width: "100%",
          maxWidth: PAGE_WIDTH,
          height: 0,
          paddingBottom: `${(PAGE_HEIGHT / PAGE_WIDTH) * 100}%`,
          margin: "0 auto",
        }}
      >
        <div className="absolute inset-0">
          {/* Images Layer */}
          <div className="absolute inset-0 z-0">
            {pageImages.map((img: any, index: number) => {
              // Get proper image src - handle both data URLs and raw base64
              let imgSrc = "";
              try {
                if (img.data?.startsWith("data:")) {
                  imgSrc = img.data;
                } else if (img.data) {
                  const format = img.format || "png";
                  imgSrc = `data:image/${format};base64,${img.data}`;
                } else {
                  console.warn(
                    `No data for image ${index} on page ${pageNumber}`
                  );
                  return null;
                }

                return (
                  <div
                    key={`img-${index}`}
                    className="absolute"
                    style={{
                      left: `${img.position.x * 100}%`,
                      top: `${img.position.y * 100}%`,
                      width: `${img.position.width * 100}%`,
                      height: `${img.position.height * 100}%`,
                    }}
                  >
                    <img
                      src={imgSrc}
                      alt={`Image ${index + 1}`}
                      className="w-full h-full object-contain"
                      onError={(e) => {
                        console.error(`Error loading page image ${index}:`, e);
                        // Hide broken image and add error message
                        const target = e.target as HTMLImageElement;
                        target.style.display = "none";
                        const fallback = document.createElement("div");
                        fallback.className =
                          "w-full h-full bg-gray-100 flex items-center justify-center text-sm text-gray-500";
                        fallback.innerHTML = `Image ${index + 1} Error`;
                        target.parentNode?.appendChild(fallback);
                      }}
                    />
                  </div>
                );
              } catch (err) {
                console.error(`Error processing page image ${index}:`, err);
                return (
                  <div
                    key={`img-error-${index}`}
                    className="absolute bg-gray-50 flex items-center justify-center"
                    style={{
                      left: `${img.position?.x * 100 || 0}%`,
                      top: `${img.position?.y * 100 || 0}%`,
                      width: `${img.position?.width * 100 || 10}%`,
                      height: `${img.position?.height * 100 || 10}%`,
                    }}
                  >
                    <div className="text-xs text-red-500">
                      Image {index + 1} Error
                    </div>
                  </div>
                );
              }
            })}
          </div>

          {/* Text Layer */}
          <div className="absolute inset-0 z-10">
            {content?.map((item: any, index: number) => (
              <div
                key={`text-${index}`}
                className="absolute bg-white bg-opacity-80 rounded px-2 py-1"
                style={{
                  left: `${item.position.x * 100}%`,
                  top: `${item.position.y * 100}%`,
                  fontSize: item.fontSize ? `${item.fontSize}px` : "inherit",
                  fontFamily: item.font || "inherit",
                  color: item.color || "inherit",
                }}
              >
                {item.content}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Section editor component for editing document sections
function SectionEditor({
  section,
  onSave,
  images,
  onCancel,
}: {
  section: any;
  onSave: (updatedSection: any) => void;
  images?: any[];
  onCancel: () => void;
}) {
  const [title, setTitle] = useState(section.title || "");
  const [content, setContent] = useState(section.content || "");
  const [description, setDescription] = useState(section.description || "");
  const [sectionImages, setSectionImages] = useState(section.imageData || []);

  // Log the contents for debugging
  useEffect(() => {
    console.log("SectionEditor initialized with:", {
      title: section.title,
      contentLength: section.content?.length,
      hasImageData: !!section.imageData,
      imageCount: section.imageData?.length,
    });

    if (section.imageData && section.imageData.length > 0) {
      section.imageData.forEach((img: any, idx: number) => {
        console.log(`Section image ${idx}:`, {
          hasData: !!img.data,
          format: img.format,
          dataLength: img.data?.length,
          dataPreview: img.data?.substring(0, 30) + "...",
        });
      });
    }
  }, [section]);

  const handleSave = () => {
    onSave({
      ...section,
      title,
      content,
      description,
      imageData: sectionImages,
    });
  };

  // Function to render section images
  const renderSectionImages = () => {
    if (!sectionImages || sectionImages.length === 0) {
      return (
        <div className="text-center text-gray-500 py-4">
          No images in this section
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 gap-4 my-4">
        {sectionImages.map((img: any, idx: number) => {
          // Create the image source - handle both data URLs and raw base64
          let imgSrc = "";
          try {
            if (img.data?.startsWith("data:")) {
              imgSrc = img.data;
            } else if (img.data) {
              const format = img.format || "png";
              imgSrc = `data:image/${format};base64,${img.data}`;
            }

            return (
              <div key={idx} className="border rounded-md p-2 text-center">
                <div className="mb-1 text-sm font-medium">Image {idx + 1}</div>
                <img
                  src={imgSrc}
                  alt={`Image ${idx + 1}`}
                  className="max-w-full max-h-40 mx-auto object-contain"
                  onError={(e) => {
                    console.error(`Error loading section image ${idx}:`, e);
                    // Hide the broken image and show text
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";

                    // Create fallback element with image details
                    const fallback = document.createElement("div");
                    fallback.className = "p-2 bg-gray-50 text-xs text-gray-600";
                    fallback.innerText = `Error loading image. Format: ${
                      img.format || "unknown"
                    }, Data length: ${img.data?.length || 0}`;
                    target.parentNode?.appendChild(fallback);
                  }}
                />
              </div>
            );
          } catch (err) {
            console.error(`Error processing section image ${idx}:`, err);
            return (
              <div
                key={idx}
                className="border rounded-md p-2 text-center bg-gray-50"
              >
                <div className="text-sm text-red-500">
                  Error loading image {idx + 1}
                </div>
                <div className="text-xs text-gray-500">
                  Format: {img.format || "unknown"}, Data length:{" "}
                  {img.data?.length || 0}
                </div>
              </div>
            );
          }
        })}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Edit Section</CardTitle>
        <CardDescription>Make changes to this document section</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="section-title">Title</Label>
          <Input
            id="section-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Section title"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="section-description">Description</Label>
          <Input
            id="section-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Brief description"
          />
        </div>

        <div className="space-y-2">
          <Label>Content</Label>
          <div className="border rounded-md">
            <DocumentEditor
              content={content}
              onChange={setContent}
              images={images}
            />
          </div>
        </div>

        {/* Show images in this section */}
        <div className="space-y-2">
          <Label>Section Images</Label>
          {renderSectionImages()}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={handleSave}>
          <Check className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
      </CardFooter>
    </Card>
  );
}

// Add a helper function to render section content
const renderSectionContent = (content: string, imageData?: any[]) => {
  console.log("Rendering section with imageData:", imageData?.length);

  // Log detailed image data for debugging
  if (imageData && imageData.length > 0) {
    imageData.forEach((img, idx) => {
      console.log(`Image ${idx} details:`, {
        hasData: !!img.data,
        dataLength: img.data?.length,
        format: img.format,
        dataPreview: img.data?.substring(0, 50) + "...",
        position: img.position,
      });
    });
  }

  // If there's no content, try to render image data directly
  if (imageData && imageData.length > 0) {
    return (
      <div className="flex flex-wrap gap-4 justify-center">
        {imageData.map((img, idx) => {
          // Ensure the data is a complete data URL
          let imgSrc = "";

          try {
            // First, check if it's already a data URL
            if (img.data?.startsWith("data:")) {
              imgSrc = img.data;
            }
            // If it's a base64 string without prefix, add the prefix
            else if (img.data) {
              const format = img.format || "png";
              imgSrc = `data:image/${format};base64,${img.data}`;
            }

            return (
              <div key={idx} className="text-center">
                <p className="text-xs text-gray-500 mb-1">Image {idx + 1}</p>
                <img
                  src={imgSrc}
                  alt={`Image ${idx + 1}`}
                  className="max-w-full max-h-60 object-contain rounded-md border border-gray-200"
                  onError={(e) => {
                    console.error(`Error loading image ${idx}:`, e);
                    // Fallback to showing image data directly
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";

                    // Create a fallback element to display image info
                    const fallback = document.createElement("div");
                    fallback.className = "p-4 bg-gray-100 rounded text-xs";
                    fallback.innerHTML = `
                      <p>Image format: ${img.format || "unknown"}</p>
                      <p>Data length: ${img.data?.length || 0} chars</p>
                      <p>Data preview: ${
                        img.data?.substring(0, 30) || "N/A"
                      }...</p>
                    `;
                    target.parentNode?.appendChild(fallback);
                  }}
                />
              </div>
            );
          } catch (err) {
            console.error(`Error processing image ${idx}:`, err);
            return (
              <div key={idx} className="p-4 bg-gray-100 rounded text-xs">
                <p>Error displaying image {idx + 1}</p>
                <p>Format: {img.format || "unknown"}</p>
                <p>Data length: {img.data?.length || 0} chars</p>
              </div>
            );
          }
        })}
      </div>
    );
  }

  // Otherwise, render the content
  return (
    <div
      dangerouslySetInnerHTML={{
        __html: content?.substring(0, 300) || "No content",
      }}
    />
  );
};

export function DocumentDialog({
  isOpen,
  onClose,
  onSave,
  document,
  readOnly = false,
}: DocumentDialogProps) {
  console.log("DocumentDialog rendering with:", {
    isOpen,
    readOnly,
    documentTitle: document?.title,
    hasContent: !!document?.content,
    contentLength: document?.content?.length,
    sectionsCount: document?.sections?.length,
  });

  const [activeTab, setActiveTab] = useState<"preview" | "edit">("preview");
  const [editedContent, setEditedContent] = useState(document?.content || "");
  const [documentTitle, setDocumentTitle] = useState(document?.title || "");
  const [editingTitle, setEditingTitle] = useState(false);
  const [sections, setSections] = useState<any[]>([]);
  const [editMode, setEditMode] = useState<"page" | "section" | null>(null);
  const [currentEditSection, setCurrentEditSection] = useState<any>(null);
  const [currentEditPage, setCurrentEditPage] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  // Update state when document prop changes
  useEffect(() => {
    console.log("Document prop changed, updating state");
    setEditedContent(document?.content || "");
    setDocumentTitle(document?.title || "");

    // Process sections - ensure image data is properly formatted
    if (document?.sections && document.sections.length > 0) {
      const processedSections = document.sections.map((section) => {
        // Process each section
        const processedSection = { ...section };

        // If there's imageData, make sure it's properly formatted for display
        if (section.imageData && section.imageData.length > 0) {
          processedSection.imageData = section.imageData.map((img: any) => {
            // If the image data isn't already a data URL, convert it
            if (img.data && !img.data.startsWith("data:")) {
              const format = img.format || "png";
              return {
                ...img,
                data: `data:image/${format};base64,${img.data}`,
              };
            }
            return img;
          });
        }

        return processedSection;
      });

      setSections(processedSections);
    } else {
      setSections(document?.sections || []);
    }

    // Log the document content for debugging
    console.log("Document content:", {
      length: document?.content?.length,
      preview: document?.content?.substring(0, 100),
    });
  }, [document]);

  const handleSave = async () => {
    if (!document || !onSave) return;

    setLoading(true);
    try {
      // Combine all section content
      const combinedContent = sections
        .map((section) => section.content)
        .join("\n\n");

      // Create structured content with images
      const templatedDocument = {
        title: documentTitle,
        content: combinedContent,
        sections: sections.map((section) => ({
          ...section,
          // Ensure imageData is preserved
          imageData: section.imageData || document.sections?.[0]?.imageData,
        })),
        metadata: document.metadata,
        structured_content: document.structured_content,
      };

      await onSave(templatedDocument);
      toast.success("Document saved successfully");
    } catch (error) {
      console.error("Failed to save document:", error);
      toast.error("Failed to save document");
    } finally {
      setLoading(false);
    }
  };

  const handleContentChange = (content: string) => {
    setEditedContent(content);
  };

  // Update a section
  const updateSection = (updatedSection: any, index: number) => {
    const newSections = [...sections];
    newSections[index] = updatedSection;
    setSections(newSections);
  };

  // Add a new section
  const addSection = () => {
    const newSection = {
      id: `section-${Date.now()}`,
      title: "New Section",
      content: "",
      description: "",
    };
    setSections([...sections, newSection]);
  };

  // Delete a section
  const deleteSection = (index: number) => {
    if (sections.length <= 1) {
      toast.error("Cannot delete the only section");
      return;
    }

    const newSections = [...sections];
    newSections.splice(index, 1);
    setSections(newSections);
  };

  // Move a section up or down
  const moveSection = (index: number, direction: "up" | "down") => {
    if (
      (direction === "up" && index === 0) ||
      (direction === "down" && index === sections.length - 1)
    ) {
      return;
    }

    const newSections = [...sections];
    const targetIndex = direction === "up" ? index - 1 : index + 1;

    // Swap sections
    [newSections[index], newSections[targetIndex]] = [
      newSections[targetIndex],
      newSections[index],
    ];

    setSections(newSections);
  };

  // Edit a specific section
  const editSection = (section: any) => {
    setCurrentEditSection(section);
    setEditMode("section");
  };

  // Save section changes
  const saveSectionChanges = (updatedSection: any) => {
    const index = sections.findIndex((s) => s.id === updatedSection.id);
    if (index !== -1) {
      updateSection(updatedSection, index);
    }
    setCurrentEditSection(null);
    setEditMode(null);
  };

  // Edit page content
  const editPage = (pageNumber: number) => {
    setActiveTab("edit");
    // Could scroll to specific page content
  };

  // Render document pages for preview
  const renderPages = () => {
    if (!document) return null;

    const pages = document.structured_content?.pages || [];
    if (pages.length === 0) {
      return (
        <div className="flex justify-center items-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <File className="h-12 w-12 text-gray-300 mx-auto" />
            <p className="mt-2 text-gray-500">No pages available</p>
          </div>
        </div>
      );
    }

    return pages.map((page, index) => (
      <DocumentPage
        key={index}
        pageNumber={page.page_number}
        content={page.content}
        images={document.sections?.[0]?.imageData}
        isEditable={!readOnly}
        onEditClick={editPage}
      />
    ));
  };

  // Render sections for editing
  const renderSections = () => {
    console.log("Rendering sections:", document?.sections?.length);

    if (!document?.sections || document.sections.length === 0) {
      console.log("No sections to render, using full content");
      // If no sections, show the full content
      return (
        <div className="markdown-content p-6">
          {editedContent || document?.content || "No content available"}
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Document Sections</h3>
          <Button size="sm" onClick={addSection} variant="outline">
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Section
          </Button>
        </div>

        <div className="space-y-4">
          {sections.map((section, index) => (
            <Card key={section.id} className="overflow-hidden">
              <CardHeader className="bg-gray-50 py-3 px-4">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">{section.title}</CardTitle>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => moveSection(index, "up")}
                      disabled={index === 0}
                    >
                      <MoveUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => moveSection(index, "down")}
                      disabled={index === sections.length - 1}
                    >
                      <MoveDown className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => editSection(section)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-red-500"
                      onClick={() => deleteSection(index)}
                      disabled={sections.length <= 1}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                {section.description && (
                  <CardDescription>{section.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent className="p-4">
                <div className="max-h-60 overflow-hidden relative">
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-white" />
                  {renderSectionContent(section.content, section.imageData)}
                </div>
              </CardContent>
              <CardFooter className="bg-gray-50 py-2 px-4 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-auto"
                  onClick={() => editSection(section)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Content
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to convert markdown to HTML (simple version)
  function formatMarkdownToHtml(md: string) {
    if (!md) return "";

    // Process images
    let processedHtml = md.replace(
      /!\[([^\]]*)\]\(([^)]+)\)/g,
      (match, alt, src) => {
        console.log("Image match found:", { alt, src });

        try {
          // Check if src is a JSON object
          if (src.startsWith("{") && src.endsWith("}")) {
            try {
              const imgObj = JSON.parse(src);
              if (imgObj.data) {
                console.log("JSON image data found", {
                  dataLength: imgObj.data.length,
                  hasDataPrefix: imgObj.data.startsWith("data:"),
                  format: imgObj.format,
                });

                // Handle data URL format
                if (imgObj.data.startsWith("data:")) {
                  return `<img src="${imgObj.data}" alt="${
                    alt || "Image"
                  }" style="max-width: 100%;">`;
                } else {
                  // Detect format from the base64 data if possible
                  let format = imgObj.format || "png";

                  // Try to detect image format from base64 header
                  if (!imgObj.format && imgObj.data) {
                    // Convert base64 to binary and check magic numbers
                    try {
                      const binaryData = atob(imgObj.data.substring(0, 100));
                      const bytes = new Uint8Array(binaryData.length);
                      for (let i = 0; i < binaryData.length; i++) {
                        bytes[i] = binaryData.charCodeAt(i);
                      }

                      // Check common image format signatures
                      if (bytes[0] === 0xff && bytes[1] === 0xd8) {
                        format = "jpeg";
                      } else if (bytes[0] === 0x89 && bytes[1] === 0x50) {
                        format = "png";
                      } else if (bytes[0] === 0x47 && bytes[1] === 0x49) {
                        format = "gif";
                      } else if (
                        bytes[0] === 0x52 &&
                        bytes[1] === 0x49 &&
                        bytes[2] === 0x46 &&
                        bytes[3] === 0x46
                      ) {
                        format = "webp";
                      }
                      console.log("Detected format:", format);
                    } catch (formatErr) {
                      console.warn("Format detection failed:", formatErr);
                      // Fall back to png if detection fails
                    }
                  }

                  return `<img src="data:image/${format};base64,${
                    imgObj.data
                  }" alt="${alt || "Image"}" style="max-width: 100%;">`;
                }
              }
            } catch (parseErr) {
              console.error("Error parsing JSON image data:", parseErr);
            }
          }

          // Handle data URLs directly in the markdown
          if (src.startsWith("data:")) {
            return `<img src="${src}" alt="${
              alt || "Image"
            }" style="max-width: 100%;">`;
          }

          // Handle regular image paths
          return `<img src="${src}" alt="${
            alt || "Image"
          }" style="max-width: 100%;">`;
        } catch (err) {
          console.error("Error processing image in markdown:", err);
          return match;
        }
      }
    );

    // Process headings
    processedHtml = processedHtml
      .replace(/^### (.*$)/gim, "<h3>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1>$1</h1>");

    // Process lists
    processedHtml = processedHtml
      .replace(/^\* (.*$)/gim, "<ul><li>$1</li></ul>")
      .replace(/^- (.*$)/gim, "<ul><li>$1</li></ul>")
      .replace(/^([0-9]+)\. (.*$)/gim, "<ol><li>$2</li></ol>");

    // Process emphasis and links
    processedHtml = processedHtml
      .replace(/\*\*(.*?)\*\*/gim, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/gim, "<em>$1</em>")
      .replace(/\[(.*?)\]\((.*?)\)/gim, '<a href="$2">$1</a>')
      .replace(/\n/gim, "<br>");

    // Fix duplicate list tags
    processedHtml = processedHtml
      .replace(/<\/ul><ul>/gim, "")
      .replace(/<\/ol><ol>/gim, "");

    return processedHtml;
  }

  // Render markdown content for the preview tab
  const renderMarkdownPreview = () => {
    if (!document?.content && !editedContent) {
      return (
        <div className="p-6 text-center text-gray-500">
          No content available
        </div>
      );
    }

    console.log(
      "Rendering markdown preview with content length:",
      (editedContent || document?.content || "").length
    );

    // Create a div that will render the markdown content with improved styling
    const htmlContent = formatMarkdownToHtml(
      editedContent || document?.content || ""
    );

    // If the document has structured content, use that instead
    if (
      document?.structured_content?.pages &&
      document?.structured_content?.pages.length > 0
    ) {
      // Get all images from sections
      let allImages: any[] = [];
      if (document?.sections) {
        document.sections.forEach((section) => {
          if (section.imageData && section.imageData.length > 0) {
            allImages = [...allImages, ...section.imageData];
          }
        });
      }

      return (
        <div className="p-6 space-y-8">
          {/* Render structured pages */}
          {document.structured_content.pages.map((page, pageIndex) => (
            <div
              key={`page-${pageIndex}`}
              className="relative bg-white shadow-lg rounded-lg mb-6 overflow-hidden"
            >
              {/* Page header */}
              <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                <span className="text-sm font-medium text-gray-600">
                  Page {page.page_number}
                </span>
              </div>

              {/* Page content - maintain aspect ratio */}
              <div
                className="relative bg-white"
                style={{
                  width: "100%",
                  maxWidth: 794, // A4 width at 96 DPI
                  height: 0,
                  paddingBottom: `${(1123 / 794) * 100}%`, // A4 aspect ratio
                  margin: "0 auto",
                }}
              >
                <div className="absolute inset-0">
                  {/* Images layer */}
                  <div className="absolute inset-0 z-0">
                    {allImages
                      .filter((img) => img.position?.page === page.page_number)
                      .map((img, imgIndex) => {
                        let imgSrc = "";
                        try {
                          // Process image source
                          if (img.data?.startsWith("data:")) {
                            imgSrc = img.data;
                          } else if (img.data) {
                            const format = img.format || "png";
                            imgSrc = `data:image/${format};base64,${img.data}`;
                          }

                          if (!imgSrc) return null;

                          return (
                            <div
                              key={`img-${imgIndex}`}
                              className="absolute"
                              style={{
                                left: `${(img.position?.x || 0) * 100}%`,
                                top: `${(img.position?.y || 0) * 100}%`,
                                width: `${(img.position?.width || 0.2) * 100}%`,
                                height: `${
                                  (img.position?.height || 0.2) * 100
                                }%`,
                              }}
                            >
                              <img
                                src={imgSrc}
                                alt={`Image ${imgIndex + 1}`}
                                className="w-full h-full object-contain"
                                onError={(e) => {
                                  console.error(
                                    `Error loading image ${imgIndex}:`,
                                    e
                                  );
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = "none";
                                }}
                              />
                            </div>
                          );
                        } catch (err) {
                          console.error(
                            `Error processing image ${imgIndex}:`,
                            err
                          );
                          return null;
                        }
                      })}
                  </div>

                  {/* Text content layer */}
                  <div className="absolute inset-0 z-10">
                    {page.content.map((item, contentIndex) => (
                      <div
                        key={`content-${contentIndex}`}
                        className="absolute bg-white bg-opacity-80 rounded px-2 py-1"
                        style={{
                          left: `${item.position.x * 100}%`,
                          top: `${item.position.y * 100}%`,
                          fontSize: item.fontSize
                            ? `${item.fontSize}px`
                            : "inherit",
                          fontFamily: item.font || "inherit",
                          color: item.color || "inherit",
                        }}
                      >
                        {item.content}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    // Fallback to simple markdown rendering
    return (
      <div className="p-6">
        <div
          className="markdown-preview"
          dangerouslySetInnerHTML={{
            __html: htmlContent,
          }}
          style={{
            fontFamily:
              "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            lineHeight: "1.6",
            color: "#374151",
          }}
        />
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex flex-row items-center justify-between">
          {editingTitle ? (
            <div className="flex items-center gap-2">
              <Input
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
                className="text-xl font-semibold"
                autoFocus
              />
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setEditingTitle(false)}
              >
                <Check className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <DialogTitle className="text-xl">
                {documentTitle || "Document"}
              </DialogTitle>
              {!readOnly && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 opacity-50 hover:opacity-100"
                  onClick={() => setEditingTitle(true)}
                >
                  <Pencil className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {!readOnly && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RotateCw className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          )}
        </DialogHeader>

        {editMode === "section" && currentEditSection ? (
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-[calc(90vh-80px)]">
              <div className="px-4 py-6">
                <SectionEditor
                  section={currentEditSection}
                  onSave={saveSectionChanges}
                  images={document?.sections?.[0]?.imageData}
                  onCancel={() => {
                    setCurrentEditSection(null);
                    setEditMode(null);
                  }}
                />
              </div>
            </ScrollArea>
          </div>
        ) : (
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as "preview" | "edit")}
            className="flex-1 flex flex-col min-h-0"
          >
            <div className="flex justify-between items-center border-b">
              <TabsList>
                <TabsTrigger value="preview">Preview</TabsTrigger>
                {!readOnly && <TabsTrigger value="edit">Edit</TabsTrigger>}
              </TabsList>
            </div>

            <div className="flex-1 overflow-hidden">
              <TabsContent value="preview" className="h-full overflow-hidden">
                <ScrollArea className="h-[calc(90vh-140px)]">
                  {renderMarkdownPreview()}
                </ScrollArea>
              </TabsContent>

              <TabsContent value="edit" className="h-full overflow-hidden">
                <ScrollArea className="h-[calc(90vh-140px)]">
                  {renderSections()}
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        )}
      </DialogContent>
    </Dialog>
  );
}
