// import { J<PERSON><PERSON> } from 'jsdom'; // Remove this line

export interface HTMLIssueLocation {
  issue: string;
  severity: 'critical' | 'warning' | 'info';
  lineNumber: number;
  columnNumber: number;
  cssSelector: string;
  xpath: string;
  element: string;
  context: {
    before: string;
    current: string;
    after: string;
  };
  recommendation: string;
  impact: string;
}

export interface HTMLAnalysisResult {
  issues: HTMLIssueLocation[];
  totalIssues: number;
  issuesByType: {
    critical: number;
    warning: number;
    info: number;
  };
  seoScore: number;
  performanceMetrics: {
    analysisTime: number;
    contentSize: number;
    elementsAnalyzed: number;
  };
}

export interface SEOElement {
  tag: string;
  attributes?: { [key: string]: string };
  content?: string;
  required: boolean;
  selector: string;
}

class HTMLAnalysisService {
  private readonly SEO_ELEMENTS: SEOElement[] = [
    { tag: 'title', required: true, selector: 'title' },
    { tag: 'meta', attributes: { name: 'description' }, required: true, selector: 'meta[name="description"]' },
    { tag: 'meta', attributes: { name: 'keywords' }, required: false, selector: 'meta[name="keywords"]' },
    { tag: 'meta', attributes: { property: 'og:title' }, required: false, selector: 'meta[property="og:title"]' },
    { tag: 'meta', attributes: { property: 'og:description' }, required: false, selector: 'meta[property="og:description"]' },
    { tag: 'meta', attributes: { property: 'og:image' }, required: false, selector: 'meta[property="og:image"]' },
    { tag: 'meta', attributes: { name: 'twitter:card' }, required: false, selector: 'meta[name="twitter:card"]' },
    { tag: 'link', attributes: { rel: 'canonical' }, required: false, selector: 'link[rel="canonical"]' },
    { tag: 'h1', required: true, selector: 'h1' },
    { tag: 'h2', required: false, selector: 'h2' },
    { tag: 'h3', required: false, selector: 'h3' },
    { tag: 'img', attributes: { alt: '' }, required: false, selector: 'img' },
    { tag: 'a', attributes: { href: '' }, required: false, selector: 'a[href]' }
  ];

  /**
   * Analyze HTML content for SEO issues using browser DOM API
   */
  async analyzeHTML(htmlContent: string): Promise<HTMLAnalysisResult> {
    try {
      const startTime = Date.now();
      const issues: HTMLIssueLocation[] = [];
      
      // Use DOMParser for browser compatibility
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');
      
      // Split HTML into lines for line number tracking
      const htmlLines = htmlContent.split('\n');
      
      // Analyze meta tags
      issues.push(...this.analyzeMetaTags(doc, htmlLines));
      
      // Analyze heading structure
      issues.push(...this.analyzeHeadingStructure(doc, htmlLines));
      
      // Analyze images
      issues.push(...this.analyzeImages(doc, htmlLines));
      
      // Analyze links
      issues.push(...this.analyzeLinks(doc, htmlLines));
      
      // Analyze page structure
      issues.push(...this.analyzePageStructure(doc, htmlLines));
      
      // Analyze content quality
      issues.push(...this.analyzeContentQuality(doc, htmlLines));
      
      const analysisTime = Date.now() - startTime;
      
      // Calculate scores
      const totalIssues = issues.length;
      const criticalIssues = issues.filter(i => i.severity === 'critical').length;
      const warningIssues = issues.filter(i => i.severity === 'warning').length;
      const infoIssues = issues.filter(i => i.severity === 'info').length;
      
      // SEO score calculation (0-100)
      let seoScore = 100;
      seoScore -= criticalIssues * 20; // -20 per critical issue
      seoScore -= warningIssues * 10;  // -10 per warning
      seoScore -= infoIssues * 2;      // -2 per info issue
      seoScore = Math.max(0, seoScore); // Don't go below 0
      
      return {
        issues,
        totalIssues,
        issuesByType: {
          critical: criticalIssues,
          warning: warningIssues,
          info: infoIssues
        },
        seoScore,
        performanceMetrics: {
          analysisTime,
          contentSize: htmlContent.length,
          elementsAnalyzed: doc.querySelectorAll('*').length
        }
      };
    } catch (error) {
      console.error('HTML analysis failed:', error);
      throw new Error(`HTML analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Analyze meta tags for SEO issues
   */
  private analyzeMetaTags(document: Document, htmlLines: string[]): HTMLIssueLocation[] {
    const issues: HTMLIssueLocation[] = [];
    
    // Check title tag
    const titleElement = document.querySelector('title');
    if (!titleElement) {
      issues.push(this.createIssue(
        'Missing title tag',
        'critical',
        'title',
        'head > title',
        'Title tag is missing',
        'Add a title tag with 50-60 characters',
        'Major impact on search rankings',
        htmlLines
      ));
    } else {
      const titleText = titleElement.textContent || '';
      if (titleText.length < 30) {
        issues.push(this.createIssueFromElement(
          'Title too short (less than 30 characters)',
          'warning',
          titleElement,
          `Title: "${titleText}"`,
          'Extend title to 50-60 characters',
          'Reduced click-through rates',
          htmlLines
        ));
      } else if (titleText.length > 60) {
        issues.push(this.createIssueFromElement(
          'Title too long (more than 60 characters)',
          'warning',
          titleElement,
          `Title: "${titleText}"`,
          'Shorten title to 50-60 characters',
          'Title may be truncated in search results',
          htmlLines
        ));
      }
    }
    
    // Check meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      issues.push(this.createIssue(
        'Missing meta description',
        'critical',
        'meta[name="description"]',
        'head > meta[name="description"]',
        'Meta description is missing',
        'Add meta description with 150-160 characters',
        'Major impact on click-through rates',
        htmlLines
      ));
    } else {
      const content = metaDescription.getAttribute('content') || '';
      if (content.length < 120) {
        issues.push(this.createIssueFromElement(
          'Meta description too short (less than 120 characters)',
          'warning',
          metaDescription,
          `Content: "${content}"`,
          'Extend description to 150-160 characters',
          'Reduced click-through rates',
          htmlLines
        ));
      } else if (content.length > 160) {
        issues.push(this.createIssueFromElement(
          'Meta description too long (more than 160 characters)',
          'warning',
          metaDescription,
          `Content: "${content}"`,
          'Shorten description to 150-160 characters',
          'Description may be truncated',
          htmlLines
        ));
      }
    }
    
    return issues;
  }

  /**
   * Analyze heading structure for SEO issues
   */
  private analyzeHeadingStructure(document: Document, htmlLines: string[]): HTMLIssueLocation[] {
    const issues: HTMLIssueLocation[] = [];
    
    // Check H1 tags
    const h1Elements = document.querySelectorAll('h1');
    if (h1Elements.length === 0) {
      issues.push(this.createIssue(
        'Missing H1 tag',
        'critical',
        'h1',
        'body h1',
        'No H1 heading found',
        'Add exactly one H1 tag to the page',
        'Major impact on content structure',
        htmlLines
      ));
    } else if (h1Elements.length > 1) {
      h1Elements.forEach((element, index) => {
        if (index > 0) {
          issues.push(this.createIssueFromElement(
            'Multiple H1 tags found',
            'warning',
            element,
            `Extra H1: "${element.textContent?.substring(0, 50)}..."`,
            'Use only one H1 tag per page',
            'Confuses search engines about page topic',
            htmlLines
          ));
        }
      });
    }
    
    return issues;
  }

  /**
   * Analyze images for SEO issues
   */
  private analyzeImages(document: Document, htmlLines: string[]): HTMLIssueLocation[] {
    const issues: HTMLIssueLocation[] = [];
    
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
      const alt = img.getAttribute('alt');
      const src = img.getAttribute('src');
      
      // Check for missing alt text
      if (alt === null) {
        issues.push(this.createIssueFromElement(
          'Image missing alt attribute',
          'warning',
          img,
          `Image: ${src?.substring(0, 50)}...`,
          'Add descriptive alt text',
          'Poor accessibility and SEO',
          htmlLines
        ));
      } else if (alt.length > 125) {
        issues.push(this.createIssueFromElement(
          'Alt text too long (over 125 characters)',
          'warning',
          img,
          `Alt: "${alt.substring(0, 50)}..."`,
          'Shorten alt text to be more concise',
          'Screen readers may truncate long alt text',
          htmlLines
        ));
      }
    });
    
    return issues;
  }

  /**
   * Analyze links for SEO issues
   */
  private analyzeLinks(document: Document, htmlLines: string[]): HTMLIssueLocation[] {
    const issues: HTMLIssueLocation[] = [];
    
    const links = document.querySelectorAll('a[href]');
    
    links.forEach(link => {
      const text = link.textContent?.trim();
      
      // Check for empty link text
      if (!text || text.length === 0) {
        issues.push(this.createIssueFromElement(
          'Link with empty text',
          'warning',
          link,
          `Link: ${link.getAttribute('href')?.substring(0, 50)}...`,
          'Add descriptive link text',
          'Poor accessibility and user experience',
          htmlLines
        ));
      }
    });
    
    return issues;
  }

  /**
   * Analyze page structure for SEO issues
   */
  private analyzePageStructure(document: Document, htmlLines: string[]): HTMLIssueLocation[] {
    const issues: HTMLIssueLocation[] = [];
    
    // Check for main content area
    const main = document.querySelector('main');
    if (!main) {
      issues.push(this.createIssue(
        'Missing main content landmark',
        'warning',
        'main',
        'body > main',
        'No main element found',
        'Add <main> element to identify primary content',
        'Poor accessibility and content structure',
        htmlLines
      ));
    }
    
    return issues;
  }

  /**
   * Analyze content quality for SEO issues
   */
  private analyzeContentQuality(document: Document, htmlLines: string[]): HTMLIssueLocation[] {
    const issues: HTMLIssueLocation[] = [];
    
    // Check content length
    const textContent = document.body?.textContent || '';
    const wordCount = textContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    
    if (wordCount < 300) {
      issues.push(this.createIssue(
        'Insufficient content length',
        'warning',
        'body',
        'body',
        `Only ${wordCount} words found`,
        'Add more valuable content (aim for 300+ words)',
        'May be considered thin content by search engines',
        htmlLines
      ));
    }
    
    return issues;
  }

  /**
   * Create an issue object for elements found in DOM
   */
  private createIssueFromElement(
    issue: string,
    severity: 'critical' | 'warning' | 'info',
    element: Element,
    elementDisplay: string,
    recommendation: string,
    impact: string,
    htmlLines: string[]
  ): HTMLIssueLocation {
    const cssSelector = this.generateCSSSelector(element);
    const xpath = this.generateXPath(element);
    const location = this.findElementInHTML(element.outerHTML, htmlLines);
    
    return {
      issue,
      severity,
      lineNumber: location.lineNumber,
      columnNumber: location.columnNumber,
      cssSelector,
      xpath,
      element: elementDisplay,
      context: location.context,
      recommendation,
      impact
    };
  }

  /**
   * Create an issue object for missing elements
   */
  private createIssue(
    issue: string,
    severity: 'critical' | 'warning' | 'info',
    selector: string,
    expectedSelector: string,
    elementDisplay: string,
    recommendation: string,
    impact: string,
    htmlLines: string[]
  ): HTMLIssueLocation {
    return {
      issue,
      severity,
      lineNumber: 1,
      columnNumber: 1,
      cssSelector: selector,
      xpath: expectedSelector,
      element: elementDisplay,
      context: {
        before: '',
        current: elementDisplay,
        after: ''
      },
      recommendation,
      impact
    };
  }

  /**
   * Generate CSS selector for element
   */
  private generateCSSSelector(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    let selector = element.tagName.toLowerCase();
    
    if (element.className) {
      const classes = element.className.trim().split(/\s+/);
      selector += '.' + classes.join('.');
    }
    
    // Add position if needed
    const parent: Element | null = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children).filter((el: Element) => el.tagName === element.tagName);
      if (siblings.length > 1) {
        const index = siblings.indexOf(element) + 1;
        selector += `:nth-of-type(${index})`;
      }
    }
    
    return selector;
  }

  /**
   * Generate XPath for element
   */
  private generateXPath(element: Element): string {
    const parts: string[] = [];
    let current: Element | null = element;
    
    while (current && current.nodeType === 1) {
      let tagName = current.tagName.toLowerCase();
      
      if (current.id) {
        parts.unshift(`//${tagName}[@id="${current.id}"]`);
        break;
      }
      
      const parent: Element | null = current.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children).filter((el: Element) => el.tagName === current!.tagName);
        if (siblings.length > 1) {
          const index = siblings.indexOf(current) + 1;
          tagName += `[${index}]`;
        }
      }
      
      parts.unshift(tagName);
      current = parent;
    }
    
    return '/' + parts.join('/');
  }

  /**
   * Find element location in HTML source
   */
  private findElementInHTML(elementHTML: string, htmlLines: string[]): {
    lineNumber: number;
    columnNumber: number;
    context: {
      before: string;
      current: string;
      after: string;
    };
  } {
    // Simple search for now - could be enhanced with more sophisticated matching
    const searchText = elementHTML.substring(0, 100);
    
    for (let i = 0; i < htmlLines.length; i++) {
      const line = htmlLines[i];
      const index = line.indexOf(searchText.substring(0, 50));
      
      if (index !== -1) {
        return {
          lineNumber: i + 1,
          columnNumber: index + 1,
          context: {
            before: htmlLines[i - 1] || '',
            current: line,
            after: htmlLines[i + 1] || ''
          }
        };
      }
    }
    
    return {
      lineNumber: 1,
      columnNumber: 1,
      context: {
        before: '',
        current: elementHTML,
        after: ''
      }
    };
  }
}

export const htmlAnalysisService = new HTMLAnalysisService(); 