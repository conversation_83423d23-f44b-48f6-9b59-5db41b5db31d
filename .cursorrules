# AltZero Platform Cursor Rules

## Avoid Hardcoded Values
- Move all hardcoded strings, URLs, API keys, and configuration values to environment variables or constants files
- Use environment variables for sensitive information (API keys, secrets) via process.env
- Define application constants in dedicated files (base/utils/constants.ts)
- Implement configuration objects for environment-specific settings
- Extract all UI text to i18n files or constants for future localization support
- Never hardcode database table names or column names; use enum or constants
- Avoid magic numbers; use named constants with clear meaning
- Never hardcode CSS colors, sizes, or font values; always use theme variables from index.css
- Do not use direct color names or hex values (e.g., "blue", "#ff0000"); use theme variables instead

## Theme System
- Always use the base theme system defined in index.css for all styling
- Use Tailwind's theme color utilities (bg-primary, text-muted-foreground, etc.) instead of hardcoded colors
- Define component styles in the @layer components section of index.css
- Use custom utility classes from the @layer utilities section of index.css
- Create new theme variables in index.css when introducing new semantic color roles
- Keep header, layout, and UI elements consistent with the base theme
- When theming components, check if a theme variable already exists before creating a new one
- Use HSL color definitions for all theme variables to maintain consistency
- Support both light and dark mode by defining variables for both in index.css

## TypeScript Best Practices
- Use strict TypeScript configuration (strict: true in tsconfig.json)
- Define explicit return types for functions, especially exported ones
- Create interfaces or types for component props, API responses, and state
- Avoid using 'any' type; use unknown with type guards instead when needed
- Use discriminated unions for handling different states (loading, success, error)
- Leverage TypeScript utility types (Partial, Pick, Omit, Record, etc.)
- Use type inference where it improves readability
- Add JSDoc comments for complex functions and types
- Use readonly modifiers for immutable properties
- Use enums for predefined sets of values

## React Best Practices
- Prefer functional components with hooks over class components
- Use React.FC type for function components with proper prop types
- Implement proper error boundaries for graceful error handling
- Use React context for state that needs to be accessed by many components
- Extract reusable logic into custom hooks
- Use memoization (useMemo, useCallback) for expensive operations
- Keep components small and focused on a single responsibility
- Use React.lazy and Suspense for code splitting
- Implement proper loading states for asynchronous operations

## Supabase Best Practices
- Use typed queries with Supabase (leverage TypeScript types)
- Implement proper error handling for all Supabase operations
- Use Row Level Security (RLS) policies for data access control
- Keep authentication logic in dedicated services
- Implement proper session management
- Use transactions for operations that modify multiple tables
- Add proper database indexes for performance

## Code Organization
- Follow consistent file and folder naming conventions
- Group related functionality in dedicated modules
- Maintain separation of concerns (UI, business logic, data access)
- Use barrel exports (index.ts files) for cleaner imports
- Organize imports in a consistent order
- Use absolute imports for better readability

## Development Workflow
- Document complex logic with clear comments
- Write unit tests for critical business logic
- Use ESLint and Prettier for code quality and consistency
- Keep components and functions pure when possible
- Implement proper logging for debugging and monitoring
- Use proper error tracking in production

## Security Practices
- Validate all user inputs
- Implement proper CSRF protection
- Use HTTPOnly cookies for sensitive data
- Implement proper authentication and authorization checks
- Sanitize output to prevent XSS attacks
- Keep dependencies updated to avoid vulnerabilities
- Use Content Security Policy (CSP) headers 