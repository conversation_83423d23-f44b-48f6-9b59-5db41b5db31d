-- Migration: Add SEO Analysis Columns
-- Description: Add columns to store detailed HTML and SEO analysis results
-- Run this in your Supabase SQL Editor to fix the 500 error

-- Add HTML Analysis columns to pseo_audits (with IF NOT EXISTS to avoid errors)
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS html_analysis JSONB DEFAULT '{}';
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS html_issues JSONB DEFAULT '[]';
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS total_issues INTEGER DEFAULT 0;
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS issues_by_type JSONB DEFAULT '{"critical": 0, "warning": 0, "info": 0}';
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS seo_score INTEGER DEFAULT 0;
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS performance_metrics JSON<PERSON> DEFAULT '{}';

-- Add SEO Scoring columns to pseo_audits
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS overall_score INTEGER DEFAULT 0;
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS grade VARCHAR(2);
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS seo_metrics JSONB DEFAULT '{}';
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS recommendations JSONB DEFAULT '[]';
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS provider_breakdown JSONB DEFAULT '{}';

-- Add updated_at column if it doesn't exist
ALTER TABLE pseo_audits ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add step type and result fields to audit steps
ALTER TABLE pseo_audit_steps ADD COLUMN IF NOT EXISTS step_type VARCHAR(50);
ALTER TABLE pseo_audit_steps ADD COLUMN IF NOT EXISTS result TEXT;
ALTER TABLE pseo_audit_steps ADD COLUMN IF NOT EXISTS metadata TEXT;

-- Add indexes for new columns (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_pseo_audits_seo_score ON pseo_audits(seo_score);
CREATE INDEX IF NOT EXISTS idx_pseo_audits_overall_score ON pseo_audits(overall_score);
CREATE INDEX IF NOT EXISTS idx_pseo_audits_grade ON pseo_audits(grade);
CREATE INDEX IF NOT EXISTS idx_pseo_audits_updated_at ON pseo_audits(updated_at);

-- Add constraints (only if they don't exist)
DO $$ BEGIN
    -- Check if seo_score constraint exists, if not add it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'pseo_audits' AND constraint_name = 'chk_seo_score_range'
    ) THEN
        ALTER TABLE pseo_audits ADD CONSTRAINT chk_seo_score_range 
            CHECK (seo_score IS NULL OR (seo_score >= 0 AND seo_score <= 100));
    END IF;
    
    -- Check if overall_score constraint exists, if not add it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'pseo_audits' AND constraint_name = 'chk_overall_score_range'
    ) THEN
        ALTER TABLE pseo_audits ADD CONSTRAINT chk_overall_score_range 
            CHECK (overall_score IS NULL OR (overall_score >= 0 AND overall_score <= 100));
    END IF;
    
    -- Check if grade constraint exists, if not add it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'pseo_audits' AND constraint_name = 'chk_seo_grade_valid'
    ) THEN
        ALTER TABLE pseo_audits ADD CONSTRAINT chk_seo_grade_valid 
            CHECK (grade IS NULL OR grade IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D', 'F'));
    END IF;
END $$;

-- Add update trigger for updated_at (only if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if it exists, then create it
DROP TRIGGER IF EXISTS update_pseo_audits_updated_at ON pseo_audits;
CREATE TRIGGER update_pseo_audits_updated_at 
    BEFORE UPDATE ON pseo_audits 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add helpful comments
COMMENT ON COLUMN pseo_audits.html_analysis IS 'JSON containing detailed HTML analysis results from htmlAnalysisService';
COMMENT ON COLUMN pseo_audits.html_issues IS 'Array of HTML issues found during analysis with exact locations';
COMMENT ON COLUMN pseo_audits.total_issues IS 'Total number of SEO issues found across all categories';
COMMENT ON COLUMN pseo_audits.issues_by_type IS 'Breakdown of issues by severity: critical, warning, info';
COMMENT ON COLUMN pseo_audits.seo_score IS 'SEO score from HTML analysis (0-100)';
COMMENT ON COLUMN pseo_audits.performance_metrics IS 'Performance metrics from HTML analysis';
COMMENT ON COLUMN pseo_audits.overall_score IS 'Overall SEO score combining all factors (0-100)';
COMMENT ON COLUMN pseo_audits.grade IS 'Letter grade representation of overall score (A+ to F)';
COMMENT ON COLUMN pseo_audits.seo_metrics IS 'Detailed SEO metrics from seoScoringService with provider breakdowns';
COMMENT ON COLUMN pseo_audits.recommendations IS 'Array of SEO recommendations with priorities and implementations';
COMMENT ON COLUMN pseo_audits.provider_breakdown IS 'Breakdown of scores by SEO provider (lighthouse, ahrefs, etc.)';
COMMENT ON COLUMN pseo_audits.updated_at IS 'Timestamp of last update to audit record';

COMMENT ON COLUMN pseo_audit_steps.step_type IS 'Type of audit step (html_analysis, seo_scoring, etc.)';
COMMENT ON COLUMN pseo_audit_steps.result IS 'JSON string containing step execution results';
COMMENT ON COLUMN pseo_audit_steps.metadata IS 'JSON string containing additional step metadata';

-- Verify the migration was successful
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'pseo_audits' 
    AND column_name IN ('html_analysis', 'seo_metrics', 'overall_score', 'grade', 'updated_at')
ORDER BY column_name;

-- Show success message
SELECT 'SEO Analysis Database Migration Completed Successfully! The 500 error should now be resolved.' as status;

-- Create a view for SEO analysis results
CREATE OR REPLACE VIEW pseo_audit_analysis AS
SELECT 
  a.id as audit_id,
  w.url,
  w.domain,
  a.created_at,
  a.completed_at,
  a.status,
  a.seo_score,
  a.overall_score,
  a.grade,
  a.total_issues,
  a.issues_by_type,
  a.seo_metrics,
  a.provider_breakdown,
  COUNT(s.id) as total_steps,
  COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_steps
FROM pseo_audits a
JOIN pseo_websites w ON a.website_id = w.id
LEFT JOIN pseo_audit_steps s ON a.id = s.audit_id
GROUP BY 
  a.id, w.url, w.domain, a.created_at, a.completed_at, 
  a.status, a.seo_score, a.overall_score, a.grade, 
  a.total_issues, a.issues_by_type, a.seo_metrics, 
  a.provider_breakdown;

-- Grant permissions on the new view
GRANT SELECT ON pseo_audit_analysis TO authenticated; 