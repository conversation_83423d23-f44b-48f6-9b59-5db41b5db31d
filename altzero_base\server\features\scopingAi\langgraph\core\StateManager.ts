// =====================================================
// STATE MANAGER FOR SCOPINGAI LANGGRAPH WORKFLOWS
// =====================================================

import { ScopingAiWorkflowState } from '../types/WorkflowState';

export class StateManager {
  private workflowStates: Map<string, ScopingAiWorkflowState> = new Map();
  private executionHistory: Map<string, any[]> = new Map();
  private stateSnapshots: Map<string, ScopingAiWorkflowState[]> = new Map();

  // Save workflow state
  async saveWorkflowState(workflowId: string, state: ScopingAiWorkflowState): Promise<void> {
    try {
      // Store in memory (in production, this would be a database)
      this.workflowStates.set(workflowId, { ...state });

      // Save snapshot for history
      const snapshots = this.stateSnapshots.get(workflowId) || [];
      snapshots.push({ ...state });
      this.stateSnapshots.set(workflowId, snapshots);

      // Log state change
      this.addExecutionHistory(workflowId, {
        timestamp: new Date().toISOString(),
        action: 'state_saved',
        step: state.current_step,
        progress: state.progress,
        status: state.status
      });

      console.log(`💾 State saved for workflow: ${workflowId} - Step: ${state.current_step} (${state.progress}%)`);
    } catch (error) {
      console.error(`❌ Failed to save workflow state for ${workflowId}:`, error);
      throw error;
    }
  }

  // Get workflow state
  async getWorkflowState(workflowId: string): Promise<ScopingAiWorkflowState | null> {
    try {
      const state = this.workflowStates.get(workflowId);
      return state ? { ...state } : null;
    } catch (error) {
      console.error(`❌ Failed to get workflow state for ${workflowId}:`, error);
      return null;
    }
  }

  // Get workflow status (simplified state)
  async getWorkflowStatus(workflowId: string): Promise<any> {
    try {
      const state = this.workflowStates.get(workflowId);
      
      if (!state) {
        return {
          status: 'not_found',
          message: `Workflow ${workflowId} not found`
        };
      }

      return {
        workflow_id: state.workflow_id,
        status: state.status,
        progress: state.progress,
        current_step: state.current_step,
        started_at: state.started_at,
        completed_at: state.completed_at,
        last_updated: state.last_updated,
        processing_time: state.processing_time,
        errors: state.errors,
        warnings: state.warnings
      };
    } catch (error) {
      console.error(`❌ Failed to get workflow status for ${workflowId}:`, error);
      return {
        status: 'error',
        message: 'Failed to retrieve workflow status'
      };
    }
  }

  // Cancel workflow
  async cancelWorkflow(workflowId: string): Promise<boolean> {
    try {
      const state = this.workflowStates.get(workflowId);
      
      if (!state) {
        return false;
      }

      if (state.status === 'completed' || state.status === 'failed' || state.status === 'cancelled') {
        return false; // Cannot cancel already finished workflows
      }

      // Update state to cancelled
      const cancelledState: ScopingAiWorkflowState = {
        ...state,
        status: 'cancelled',
        completed_at: new Date().toISOString(),
        last_updated: new Date().toISOString(),
        current_step: 'cancelled'
      };

      await this.saveWorkflowState(workflowId, cancelledState);

      this.addExecutionHistory(workflowId, {
        timestamp: new Date().toISOString(),
        action: 'workflow_cancelled',
        step: state.current_step,
        progress: state.progress
      });

      console.log(`🚫 Workflow cancelled: ${workflowId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to cancel workflow ${workflowId}:`, error);
      return false;
    }
  }

  // Get active workflows
  getActiveWorkflows(): Array<{ workflowId: string; state: ScopingAiWorkflowState }> {
    const activeWorkflows: Array<{ workflowId: string; state: ScopingAiWorkflowState }> = [];

    for (const [workflowId, state] of this.workflowStates.entries()) {
      if (state.status === 'running' || state.status === 'initializing') {
        activeWorkflows.push({ workflowId, state: { ...state } });
      }
    }

    return activeWorkflows;
  }

  // Add execution history entry
  private addExecutionHistory(workflowId: string, entry: any): void {
    const history = this.executionHistory.get(workflowId) || [];
    history.push(entry);
    this.executionHistory.set(workflowId, history);
  }

  // Get execution history
  getExecutionHistory(workflowId: string): any[] {
    return this.executionHistory.get(workflowId) || [];
  }

  // Get state snapshots
  getStateSnapshots(workflowId: string): ScopingAiWorkflowState[] {
    return this.stateSnapshots.get(workflowId) || [];
  }

  // Get workflow statistics
  getWorkflowStats(): any {
    const totalWorkflows = this.workflowStates.size;
    const activeWorkflows = this.getActiveWorkflows().length;
    
    let completedWorkflows = 0;
    let failedWorkflows = 0;
    let cancelledWorkflows = 0;

    for (const state of this.workflowStates.values()) {
      switch (state.status) {
        case 'completed':
          completedWorkflows++;
          break;
        case 'failed':
          failedWorkflows++;
          break;
        case 'cancelled':
          cancelledWorkflows++;
          break;
      }
    }

    return {
      total_workflows: totalWorkflows,
      active_workflows: activeWorkflows,
      completed_workflows: completedWorkflows,
      failed_workflows: failedWorkflows,
      cancelled_workflows: cancelledWorkflows,
      success_rate: totalWorkflows > 0 ? Math.round((completedWorkflows / totalWorkflows) * 100) : 0
    };
  }

  // Clean up old workflows (optional - for memory management)
  cleanupOldWorkflows(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();
    const workflowsToDelete: string[] = [];

    for (const [workflowId, state] of this.workflowStates.entries()) {
      const stateAge = now - new Date(state.started_at).getTime();
      
      if (stateAge > maxAge && (state.status === 'completed' || state.status === 'failed' || state.status === 'cancelled')) {
        workflowsToDelete.push(workflowId);
      }
    }

    for (const workflowId of workflowsToDelete) {
      this.workflowStates.delete(workflowId);
      this.executionHistory.delete(workflowId);
      this.stateSnapshots.delete(workflowId);
    }

    if (workflowsToDelete.length > 0) {
      console.log(`🧹 Cleaned up ${workflowsToDelete.length} old workflows`);
    }
  }

  // Update workflow progress
  async updateProgress(workflowId: string, progress: number, currentStep: string): Promise<void> {
    const state = this.workflowStates.get(workflowId);
    
    if (state) {
      const updatedState: ScopingAiWorkflowState = {
        ...state,
        progress,
        current_step: currentStep,
        last_updated: new Date().toISOString()
      };

      await this.saveWorkflowState(workflowId, updatedState);
    }
  }

  // Add error to workflow
  async addError(workflowId: string, error: any): Promise<void> {
    const state = this.workflowStates.get(workflowId);
    
    if (state) {
      const updatedState: ScopingAiWorkflowState = {
        ...state,
        errors: [...state.errors, error],
        last_updated: new Date().toISOString()
      };

      await this.saveWorkflowState(workflowId, updatedState);
    }
  }

  // Add warning to workflow
  async addWarning(workflowId: string, warning: string): Promise<void> {
    const state = this.workflowStates.get(workflowId);
    
    if (state) {
      const updatedState: ScopingAiWorkflowState = {
        ...state,
        warnings: [...state.warnings, warning],
        last_updated: new Date().toISOString()
      };

      await this.saveWorkflowState(workflowId, updatedState);
    }
  }
}
