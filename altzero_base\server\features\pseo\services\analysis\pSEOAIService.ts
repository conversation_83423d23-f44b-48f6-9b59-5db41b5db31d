import OpenAI from 'openai';

export interface OpenAIRequest {
  prompt: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemMessage?: string;
  userId?: string;
}

export interface OpenAIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  processingTime: number;
}

export interface PSEOAnalysisRequest {
  scrapedContent: string;
  url: string;
  userId?: string;
}

export interface PSEOTechnicalAnalysis {
  criticalIssues: Array<{
    issue: string;
    impact: string;
    recommendation: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  quickWins: Array<{
    opportunity: string;
    implementation: string;
    expectedImpact: string;
  }>;
  opportunities: Array<{
    area: string;
    description: string;
    effort: string;
    impact: string;
  }>;
}

export interface PSEOContentAnalysis {
  analysis: {
    contentQuality: string;
    keywordOptimization: string;
    structureAssessment: string;
    userExperience: string;
  };
  recommendations: Array<{
    category: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    implementation: string;
  }>;
}

export interface PSEOAnalysisResponse {
  technical: PSEOTechnicalAnalysis;
  content: PSEOContentAnalysis;
  processingTime: number;
  tokensUsed: number;
}

class PSEOAIService {
  private client: OpenAI | null = null;
  private isEnabled: boolean = false;
  private defaultModel: string = 'gpt-4o-mini';

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn('OPENAI_API_KEY not found. OpenAI service will be disabled.');
      this.isEnabled = false;
    } else {
      try {
        this.client = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
        this.isEnabled = true;
        console.log('✅ pSEO AI service initialized successfully');
      } catch (error) {
        console.error('Failed to initialize pSEO AI service:', error);
        this.isEnabled = false;
      }
    }
  }

  /**
   * Clean JSON response that may be wrapped in markdown code blocks
   */
  private cleanJsonResponse(response: string): string {
    // Remove markdown code blocks if present
    const codeBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
    const match = response.match(codeBlockRegex);
    
    if (match) {
      return match[1].trim();
    }
    
    // If no code blocks found, return the original response trimmed
    return response.trim();
  }

  /**
   * Parse JSON response with error handling for markdown-wrapped content
   */
  private parseJsonResponse<T>(response: string, context: string): T {
    try {
      const cleanedResponse = this.cleanJsonResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error(`Error parsing ${context} JSON:`, error);
      console.error(`Raw response:`, response);
      console.error(`Cleaned response:`, this.cleanJsonResponse(response));
      throw new Error(`Failed to parse ${context} response`);
    }
  }

  async generateResponse(request: OpenAIRequest): Promise<OpenAIResponse> {
    if (!this.isEnabled || !this.client) {
      throw new Error('OpenAI service not available - API key not configured');
    }

    const startTime = Date.now();

    try {
      console.log(`Generating OpenAI response for user: ${request.userId || 'anonymous'}`);

      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];
      
      if (request.systemMessage) {
        messages.push({
          role: 'system',
          content: request.systemMessage,
        });
      }

      messages.push({
        role: 'user',
        content: request.prompt,
      });

      const completion = await this.client.chat.completions.create({
        model: request.model || this.defaultModel,
        messages,
        temperature: request.temperature || 0.7,
        max_tokens: request.maxTokens || 2048,
      });

      const processingTime = Date.now() - startTime;

      if (!completion.choices[0]?.message?.content) {
        throw new Error('No response content received from OpenAI');
      }

      return {
        content: completion.choices[0].message.content,
        usage: {
          promptTokens: completion.usage?.prompt_tokens || 0,
          completionTokens: completion.usage?.completion_tokens || 0,
          totalTokens: completion.usage?.total_tokens || 0,
        },
        model: completion.model,
        processingTime,
      };
    } catch (error) {
      console.error('Error generating OpenAI response:', error);
      throw error;
    }
  }

  async performPSEOAnalysis(request: PSEOAnalysisRequest): Promise<PSEOAnalysisResponse> {
    if (!this.isEnabled || !this.client) {
      throw new Error('OpenAI service not available - API key not configured');
    }

    const startTime = Date.now();
    let totalTokensUsed = 0;

    try {
      console.log(`Performing pSEO analysis for URL: ${request.url}, User: ${request.userId || 'anonymous'}`);

      // Technical SEO Analysis
      const technicalPrompt = `Analyze the following website content for technical SEO issues and provide a structured response:

Website URL: ${request.url}
Content: ${request.scrapedContent}

Please provide a comprehensive technical SEO audit with the following structure:

1. CRITICAL ISSUES (issues that significantly impact SEO performance):
   - Issue description
   - Impact on SEO
   - Specific recommendation
   - Priority level (high/medium/low)

2. QUICK WINS (easy-to-implement improvements with good ROI):
   - Opportunity description
   - Implementation steps
   - Expected impact

3. OPPORTUNITIES (longer-term improvements):
   - Area of improvement
   - Detailed description
   - Effort required
   - Potential impact

Focus on: page speed, mobile optimization, meta tags, heading structure, internal linking, schema markup, technical errors, crawlability, and indexability.

Provide your response in a structured JSON format that matches this schema:
{
  "criticalIssues": [
    {
      "issue": "string",
      "impact": "string", 
      "recommendation": "string",
      "priority": "high|medium|low"
    }
  ],
  "quickWins": [
    {
      "opportunity": "string",
      "implementation": "string",
      "expectedImpact": "string"
    }
  ],
  "opportunities": [
    {
      "area": "string",
      "description": "string", 
      "effort": "string",
      "impact": "string"
    }
  ]
}`;

      // Content SEO Analysis
      const contentPrompt = `Analyze the following website content for content SEO optimization and provide a structured response:

Website URL: ${request.url}
Content: ${request.scrapedContent}

Please provide a comprehensive content SEO analysis with the following structure:

1. ANALYSIS:
   - Content Quality Assessment
   - Keyword Optimization Review
   - Content Structure Assessment
   - User Experience Evaluation

2. RECOMMENDATIONS:
   - Category of improvement
   - Specific suggestion
   - Priority level (high/medium/low)
   - Implementation guidance

Focus on: keyword usage, content quality, readability, user intent, content structure, internal linking opportunities, content gaps, and user engagement factors.

Provide your response in a structured JSON format that matches this schema:
{
  "analysis": {
    "contentQuality": "string",
    "keywordOptimization": "string", 
    "structureAssessment": "string",
    "userExperience": "string"
  },
  "recommendations": [
    {
      "category": "string",
      "suggestion": "string",
      "priority": "high|medium|low",
      "implementation": "string"
    }
  ]
}`;

      // Run both analyses in parallel
      const [technicalResponse, contentResponse] = await Promise.all([
        this.generateResponse({
          prompt: technicalPrompt,
          model: this.defaultModel,
          temperature: 0.3,
          maxTokens: 2048,
          systemMessage: 'You are an expert technical SEO analyst. Provide detailed, actionable insights in valid JSON format only.',
          userId: request.userId,
        }),
        this.generateResponse({
          prompt: contentPrompt,
          model: this.defaultModel,
          temperature: 0.3,
          maxTokens: 2048,
          systemMessage: 'You are an expert content SEO analyst. Provide detailed, actionable insights in valid JSON format only.',
          userId: request.userId,
        }),
      ]);

      totalTokensUsed = technicalResponse.usage.totalTokens + contentResponse.usage.totalTokens;

      // Parse the JSON responses
      let technicalAnalysis: PSEOTechnicalAnalysis;
      let contentAnalysis: PSEOContentAnalysis;

      try {
        technicalAnalysis = this.parseJsonResponse<PSEOTechnicalAnalysis>(
          technicalResponse.content, 
          'technical SEO analysis'
        );
      } catch (error) {
        console.error('Error parsing technical analysis JSON:', error);
        throw new Error('Failed to parse technical SEO analysis response');
      }

      try {
        contentAnalysis = this.parseJsonResponse<PSEOContentAnalysis>(
          contentResponse.content, 
          'content SEO analysis'
        );
      } catch (error) {
        console.error('Error parsing content analysis JSON:', error);
        throw new Error('Failed to parse content SEO analysis response');
      }

      const processingTime = Date.now() - startTime;

      return {
        technical: technicalAnalysis,
        content: contentAnalysis,
        processingTime,
        tokensUsed: totalTokensUsed,
      };
    } catch (error) {
      console.error('Error performing pSEO analysis:', error);
      throw error;
    }
  }

  async generateMarkdownReport(
    technicalAnalysis: PSEOTechnicalAnalysis,
    contentAnalysis: PSEOContentAnalysis,
    url: string,
    domain: string
  ): Promise<string> {
    const reportPrompt = `Generate a comprehensive SEO audit report in markdown format based on the following analysis:

URL: ${url}
Domain: ${domain}

Technical Analysis:
${JSON.stringify(technicalAnalysis, null, 2)}

Content Analysis:
${JSON.stringify(contentAnalysis, null, 2)}

Create a professional, well-structured markdown report that includes:
1. Executive Summary
2. Technical SEO Findings
3. Content SEO Findings  
4. Priority Recommendations
5. Implementation Roadmap

Make it actionable and easy to understand for both technical and non-technical stakeholders.`;

    try {
      const response = await this.generateResponse({
        prompt: reportPrompt,
        model: this.defaultModel,
        temperature: 0.5,
        maxTokens: 3000,
        systemMessage: 'You are an expert SEO consultant creating professional audit reports. Write in clear, actionable markdown format.',
      });

      return response.content;
    } catch (error) {
      console.error('Error generating markdown report:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    if (!this.isEnabled || !this.client) {
      return false;
    }

    try {
      // Test with a simple completion
      const response = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5,
      });

      return !!response.choices[0]?.message?.content;
    } catch (error) {
      console.error('OpenAI service health check failed:', error);
      return false;
    }
  }

  // Getter to check if OpenAI service is enabled
  get enabled(): boolean {
    return this.isEnabled;
  }

  // Get available models
  async getAvailableModels(): Promise<string[]> {
    if (!this.isEnabled || !this.client) {
      return [];
    }

    try {
      const models = await this.client.models.list();
      return models.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      console.error('Error fetching available models:', error);
      return [this.defaultModel];
    }
  }

  // Update default model
  setDefaultModel(model: string): void {
    this.defaultModel = model;
    console.log(`Default OpenAI model updated to: ${model}`);
  }
}

export const pSEOAIService = new PSEOAIService(); 