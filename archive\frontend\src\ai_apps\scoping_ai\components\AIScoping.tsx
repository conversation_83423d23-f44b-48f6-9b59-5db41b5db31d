import { useState } from 'react';
import { useScoping } from '../hooks/useScoping';
import Layout from '../../../components/Layout';
import ClientInfoForm from './scoping/forms/ClientInfoForm';
import ScopingInfoForm from './scoping/forms/ScopingInfoForm';
import PromptTemplateForm from './scoping/forms/PromptTemplateForm';
import SectionDefinitionForm from './scoping/forms/SectionDefinitionForm';
import SingleScopingForm from './scoping/forms/SingleScopingForm';
import { default as SingleScopeResult } from './scoping/results/SingleScopeResult';
import { Scoping as TypesScoping, Section } from '../../../types/scoping';

// Adapter function to convert useScoping result to expected format
const adaptScopingResult = (result: any): TypesScoping | null => {
  if (!result) return null;
  
  return {
    ...result,
    sections: result.sections.map((section: any) => ({
      ...section,
      id: section.id || `section-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      status: section.status || 'completed',
      order: section.order || 0,
      updatedAt: section.updatedAt || new Date().toISOString()
    }))
  };
};

enum Step {
  ClientInfo = 'Client Information',
  ScopingInfo = 'Scoping Information',
  PromptTemplate = 'Prompt Template',
  SectionDefinition = 'Section Definition',
  CreateScoping = 'Create Scoping',
  Result = 'Result',
}

const AIScoping = () => {
  const [currentStep, setCurrentStep] = useState<Step>(Step.ClientInfo);
  const scopingHook = useScoping();
  const { state, result, isLoading } = scopingHook;

  // Function to advance to the next step
  const goToNextStep = () => {
    switch (currentStep) {
      case Step.ClientInfo:
        setCurrentStep(Step.ScopingInfo);
        break;
      case Step.ScopingInfo:
        setCurrentStep(Step.PromptTemplate);
        break;
      case Step.PromptTemplate:
        setCurrentStep(Step.SectionDefinition);
        break;
      case Step.SectionDefinition:
        setCurrentStep(Step.CreateScoping);
        break;
      case Step.CreateScoping:
        // This step submits the form and transitions to result when complete
        break;
      default:
        break;
    }
  };

  // Function to go back to previous step
  const goToPreviousStep = () => {
    switch (currentStep) {
      case Step.ScopingInfo:
        setCurrentStep(Step.ClientInfo);
        break;
      case Step.PromptTemplate:
        setCurrentStep(Step.ScopingInfo);
        break;
      case Step.SectionDefinition:
        setCurrentStep(Step.PromptTemplate);
        break;
      case Step.CreateScoping:
        setCurrentStep(Step.SectionDefinition);
        break;
      case Step.Result:
        setCurrentStep(Step.CreateScoping);
        break;
      default:
        break;
    }
  };

  // Show result page when generation is complete
  if (result && currentStep !== Step.Result) {
    setCurrentStep(Step.Result);
  }

  // Determine which component to show based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case Step.ClientInfo:
        return <ClientInfoForm 
          onNext={goToNextStep} 
          state={scopingHook.state}
          updateClientInfo={(info) => {
            const currentInfo = scopingHook.state.clientInfo || {
              name: '',
              industry: '',
              company: '',
              contactPerson: '',
              email: ''
            };
            
            scopingHook.setClientInfo({
              ...currentInfo,
              ...info
            });
          }}
        />;
      case Step.ScopingInfo:
        return <ScopingInfoForm 
          onNext={goToNextStep} 
          onBack={goToPreviousStep} 
          state={scopingHook.state}
          setScopingInfo={scopingHook.setScopingInfo}
        />;
      case Step.PromptTemplate:
        return <PromptTemplateForm 
          onNext={goToNextStep} 
          onBack={goToPreviousStep} 
          state={{ promptTemplate: scopingHook.state.promptTemplate || undefined }}
          setPromptTemplate={scopingHook.setPromptTemplate}
        />;
      case Step.SectionDefinition:
        return <SectionDefinitionForm 
          onNext={goToNextStep} 
          onBack={goToPreviousStep} 
          state={scopingHook.state}
          addSection={scopingHook.addSection}
          removeSection={scopingHook.removeSection}
          updateSection={scopingHook.updateSection}
        />;
      case Step.CreateScoping:
        return <SingleScopingForm onBack={goToPreviousStep} {...scopingHook} />;
      case Step.Result:
        return <SingleScopeResult 
          onReset={() => {
            scopingHook.resetScoping();
            setCurrentStep(Step.ClientInfo);
          }} 
          result={adaptScopingResult(scopingHook.result)}
          onSave={scopingHook.saveScoping}
        />;
      default:
        return null;
    }
  };

  // List of all steps for progress tracking
  const steps = [
    Step.ClientInfo,
    Step.ScopingInfo,
    Step.PromptTemplate,
    Step.SectionDefinition,
    Step.CreateScoping,
    Step.Result,
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="px-4 py-6 sm:px-0">
            <h1 className="text-3xl font-bold text-gray-900">AI Scoping Tool</h1>
            <p className="mt-2 text-sm text-gray-600">
              Create detailed project scopes with AI assistance
            </p>
          </div>

          {/* Progress Steps */}
          <div className="px-4 sm:px-0">
            <div className="border-t border-gray-200">
              <nav aria-label="Progress">
                <ol className="border border-gray-300 rounded-md divide-y divide-gray-300 md:flex md:divide-y-0">
                  {steps.map((step, stepIdx) => {
                    const isCurrentStep = currentStep === step;
                    const isCompleted = steps.indexOf(currentStep) > stepIdx || 
                      (step === Step.Result && result !== null);
                    
                    return (
                      <li key={step} className="relative md:flex-1 md:flex">
                        <div
                          className={`group flex items-center px-6 py-4 text-sm font-medium ${
                            isCompleted
                              ? 'bg-indigo-50 hover:bg-indigo-100'
                              : isCurrentStep
                              ? 'bg-white'
                              : 'bg-gray-50'
                          } ${
                            isCompleted || isCurrentStep
                              ? 'text-indigo-600'
                              : 'text-gray-500'
                          } ${
                            stepIdx === 0 ? 'rounded-tl-md rounded-tr-md md:rounded-tr-none md:rounded-tl-md' : ''
                          } ${
                            stepIdx === steps.length - 1
                              ? 'rounded-bl-md rounded-br-md md:rounded-bl-none md:rounded-br-md'
                              : ''
                          }`}
                        >
                          <span
                            className={`flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full ${
                              isCompleted
                                ? 'bg-indigo-600 text-white'
                                : isCurrentStep
                                ? 'border-2 border-indigo-600 text-indigo-600'
                                : 'border-2 border-gray-300 text-gray-500'
                            }`}
                          >
                            {isCompleted ? (
                              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            ) : (
                              <span>{stepIdx + 1}</span>
                            )}
                          </span>
                          <span className="ml-4 text-sm font-medium">
                            {step}
                          </span>
                        </div>

                        {stepIdx !== steps.length - 1 ? (
                          <div className="hidden md:block absolute top-0 right-0 h-full w-5">
                            <svg
                              className="h-full w-full text-gray-300"
                              viewBox="0 0 22 80"
                              fill="none"
                              preserveAspectRatio="none"
                            >
                              <path
                                d="M0 -2L20 40L0 82"
                                vectorEffect="non-scaling-stroke"
                                stroke="currentColor"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                        ) : null}
                      </li>
                    );
                  })}
                </ol>
              </nav>
            </div>
          </div>

          {/* Step Content */}
          <div className="mt-8 px-4 sm:px-0">
            <div className="bg-white shadow rounded-lg p-6">
              {renderStepContent()}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AIScoping; 