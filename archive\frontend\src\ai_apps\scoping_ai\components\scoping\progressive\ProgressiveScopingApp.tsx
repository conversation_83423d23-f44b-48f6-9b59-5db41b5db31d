import React from "react";
import { Link } from "react-router-dom";
import Layout from "../../../../../components/Layout";

const ProgressiveScopingApp: React.FC = () => {
  const scopingTools = [
    {
      name: "Create Document",
      description:
        "Start a new scoping document with section-by-section generation",
      link: "/scoping/create",
      color: "blue",
      icon: (
        <svg
          className="w-10 h-10"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
      ),
    },
    {
      name: "Clients Information",
      description: "Add and edit client profiles for your scoping documents",
      link: "/scoping/clients",
      color: "indigo",
      icon: (
        <svg
          className="w-10 h-10"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      ),
    },
    {
      name: "Manage Prompt Templates",
      description:
        "Create and edit AI prompt templates for document generation",
      link: "/scoping/templates",
      color: "purple",
      icon: (
        <svg
          className="w-10 h-10"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
          />
        </svg>
      ),
    },
    {
      name: "Manage Requirements",
      description: "Create and edit predefined scope templates for efficiency",
      link: "/scoping/scope-templates",
      color: "indigo",
      icon: (
        <svg
          className="w-10 h-10"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
          />
        </svg>
      ),
    },
    {
      name: "Proposal Templates",
      description: "Create and edit predefined section templates for documents",
      link: "/scoping/section-templates",
      color: "green",
      icon: (
        <svg
          className="w-10 h-10"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
          />
        </svg>
      ),
    },
    {
      name: "Manage Sections",
      description: "Create and edit individual section templates",
      link: "/scoping/sections",
      color: "teal",
      icon: (
        <svg
          className="w-10 h-10"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M4 6h16M4 12h16M4 18h16"
          />
        </svg>
      ),
    },
  ];

  const recentDocuments = [
    {
      id: "1",
      name: "E-commerce Website Proposal",
      client: "Acme Corporation",
      date: "Apr 5, 2023",
    },
    {
      id: "2",
      name: "Mobile App Development Scope",
      client: "GlobalTech Solutions",
      date: "Apr 2, 2023",
    },
    {
      id: "3",
      name: "CRM Integration Project",
      client: "InnovateCorp",
      date: "Mar 28, 2023",
    },
  ];

  return (
    <Layout>
      <div className="container mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Progressive Scoping System
          </h1>
          <p className="mt-2 text-gray-600">
            Create detailed scoping documents with AI assistance. Generate
            content section-by-section for improved control and quality.
          </p>
        </div>

        {/* Main Tools */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {scopingTools.map((tool, index) => (
            <Link
              key={index}
              to={tool.link}
              className={`bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md hover:border-${tool.color}-200 transition-all overflow-hidden`}
            >
              <div className={`bg-${tool.color}-50 p-6 flex justify-center`}>
                <div className={`text-${tool.color}-600`}>{tool.icon}</div>
              </div>
              <div className="p-6">
                <h3 className="font-semibold text-lg text-gray-900 mb-2">
                  {tool.name}
                </h3>
                <p className="text-gray-600 text-sm">{tool.description}</p>
              </div>
            </Link>
          ))}
        </div>

        {/* Recent Documents */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-8">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-xl font-semibold text-gray-900">
              Recent Documents
            </h2>
          </div>

          <div className="divide-y">
            {recentDocuments.map((doc) => (
              <Link
                key={doc.id}
                to={`/scoping?id=${doc.id}`}
                className="px-6 py-4 flex items-center justify-between hover:bg-gray-50"
              >
                <div>
                  <h3 className="font-medium text-blue-600">{doc.name}</h3>
                  <p className="text-sm text-gray-600">{doc.client}</p>
                </div>
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 mr-4">{doc.date}</span>
                  <svg
                    className="w-5 h-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Getting Started Guide */}
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-md overflow-hidden">
          <div className="px-6 py-8 md:py-10 md:px-8">
            <div className="md:max-w-2xl mx-auto">
              <h2 className="text-2xl font-bold text-white mb-4">
                Getting Started with Progressive Scoping
              </h2>
              <p className="text-blue-100 mb-6">
                The Progressive Scoping System allows you to create detailed
                project scopes by generating sections one at a time. This
                approach provides better control over the document content and
                quality.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-white bg-opacity-10 rounded-lg p-4">
                  <div className="rounded-full bg-white w-8 h-8 flex items-center justify-center text-indigo-600 font-bold mb-3">
                    1
                  </div>
                  <h3 className="font-semibold text-white mb-1">
                    Create a New Document
                  </h3>
                  <p className="text-blue-100 text-sm">
                    Start by adding your client and project details
                  </p>
                </div>

                <div className="bg-white bg-opacity-10 rounded-lg p-4">
                  <div className="rounded-full bg-white w-8 h-8 flex items-center justify-center text-indigo-600 font-bold mb-3">
                    2
                  </div>
                  <h3 className="font-semibold text-white mb-1">
                    Generate Base Content
                  </h3>
                  <p className="text-blue-100 text-sm">
                    The system creates an outline and introduction
                  </p>
                </div>

                <div className="bg-white bg-opacity-10 rounded-lg p-4">
                  <div className="rounded-full bg-white w-8 h-8 flex items-center justify-center text-indigo-600 font-bold mb-3">
                    3
                  </div>
                  <h3 className="font-semibold text-white mb-1">
                    Enhance Each Section
                  </h3>
                  <p className="text-blue-100 text-sm">
                    Generate and edit sections progressively
                  </p>
                </div>
              </div>

              <Link
                to="/scoping/create"
                className="inline-block bg-white rounded-lg px-6 py-3 text-indigo-600 font-medium shadow-sm hover:bg-opacity-95 transition-colors"
              >
                Create Your First Document
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProgressiveScopingApp;
