import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Plus,
  Search,
  Copy,
  Edit,
  Trash2,
  MoreHorizontal,
  Star,
  StarOff,
  Check,
  Tag,
  Sparkles,
} from "lucide-react";
import { <PERSON><PERSON> } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { Badge } from "../../../../base/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../../../base/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../../base/components/ui/dropdown-menu";
import { Label } from "../../../../base/components/ui/label";
import { Textarea } from "../../../../base/components/ui/textarea";
import { supabase } from "../lib/supabase";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../base/components/ui/select";
import { useUser } from "../../../../base/contextapi/UserContext";
import { useToast } from "../../../../base/hooks/use-toast";
import ScopingAILayout from "../components/ScopingAILayout";
import {
  fetchPrompts,
  savePrompt,
  updatePrompt,
  deletePrompt,
  PromptTemplate,
  PromptFormData,
} from "../services/promptService";

export default function KnowledgeBasePrompts() {
  const { user } = useUser();
  const { toast } = useToast();
  const [prompts, setPrompts] = useState<PromptTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<PromptTemplate | null>(
    null
  );
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [formData, setFormData] = useState<PromptFormData>({
    name: "",
    description: "",
    content: "",
  });

  useEffect(() => {
    if (user) {
      loadPrompts();
    }
  }, [user]);

  const loadPrompts = async () => {
    try {
      setIsLoading(true);
      const data = await fetchPrompts();
      setPrompts(data);
    } catch (error) {
      console.error("Error fetching prompts:", error);
      toast({
        title: "Error",
        description: "Failed to load prompt templates",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePrompt = async () => {
    try {
      if (!formData.name.trim() || !formData.content.trim()) {
        toast({
          title: "Missing fields",
          description: "Prompt name and content are required",
          variant: "destructive",
        });
        return;
      }

      if (editingPrompt) {
        // Update existing prompt
        const updatedPrompt = await updatePrompt(editingPrompt.id, formData);
        setPrompts((prev) =>
          prev.map((prompt) =>
            prompt.id === editingPrompt.id ? updatedPrompt : prompt
          )
        );
        toast({
          title: "Success",
          description: "Prompt template updated successfully",
        });
      } else {
        // Create new prompt
        const newPrompt = await savePrompt(formData);
        setPrompts((prev) => [newPrompt, ...prev]);
        toast({
          title: "Success",
          description: "Prompt template added successfully",
        });
      }

      // Reset form and close dialog
      setIsDialogOpen(false);
      setEditingPrompt(null);
      setFormData({
        name: "",
        description: "",
        content: "",
      });
    } catch (error) {
      console.error("Error saving prompt:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save prompt",
        variant: "destructive",
      });
    }
  };

  const handleEditPrompt = (prompt: PromptTemplate) => {
    setEditingPrompt(prompt);
    setFormData({
      name: prompt.name,
      description: prompt.description,
      content: prompt.content,
    });
    setIsDialogOpen(true);
  };

  const handleDeletePrompt = async (id: string) => {
    try {
      if (!confirm("Are you sure you want to delete this prompt template?"))
        return;

      await deletePrompt(id);
      setPrompts((prev) => prev.filter((prompt) => prompt.id !== id));

      toast({
        title: "Success",
        description: "Prompt template deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting prompt:", error);
      toast({
        title: "Error",
        description: "Failed to delete prompt template",
        variant: "destructive",
      });
    }
  };

  const handleCopyPrompt = async (prompt: PromptTemplate) => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      setCopiedId(prompt.id);

      toast({
        title: "Copied",
        description: "Prompt content copied to clipboard",
      });

      // Reset copy indicator after 2 seconds
      setTimeout(() => setCopiedId(null), 2000);
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const filteredPrompts = prompts.filter((prompt) => {
    const matchesSearch =
      prompt.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prompt.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prompt.content?.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  return (
    <ScopingAILayout>
      <div className="container mx-auto px-6 py-8">
        <div className="space-y-6">
          <div>
            <Link
              to="/scopingai/knowledge-base"
              className="flex items-center text-muted-foreground hover:text-foreground mb-4"
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Knowledge Base
            </Link>
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold flex items-center">
                  <Sparkles className="h-8 w-8 mr-2 text-primary" />
                  AI Prompt Templates
                </h1>
                <p className="text-muted-foreground mt-1">
                  Create and manage reusable AI prompts for your documents and
                  projects
                </p>
              </div>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => {
                      setEditingPrompt(null);
                      setFormData({
                        name: "",
                        description: "",
                        content: "",
                      });
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Prompt
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>
                      {editingPrompt
                        ? "Edit Prompt Template"
                        : "Add New Prompt Template"}
                    </DialogTitle>
                    <DialogDescription>
                      {editingPrompt
                        ? "Update prompt template information"
                        : "Create a reusable AI prompt template for your documents and projects"}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Prompt Name</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) =>
                            setFormData({ ...formData, name: e.target.value })
                          }
                          placeholder="Enter a descriptive title"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">
                        Description (optional)
                      </Label>
                      <Input
                        id="description"
                        value={formData.description}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            description: e.target.value,
                          })
                        }
                        placeholder="Brief description of what this prompt does"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="content">Prompt Content</Label>
                      <Textarea
                        id="content"
                        value={formData.content}
                        onChange={(e) =>
                          setFormData({ ...formData, content: e.target.value })
                        }
                        placeholder="Enter your AI prompt text here... Use {{variableName}} for variables."
                        rows={8}
                      />
                      <p className="text-sm text-muted-foreground">
                        Use the format {"{{"} variableName {"}}"} to define
                        variables in your prompt.
                      </p>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleSavePrompt}>
                      {editingPrompt ? "Update" : "Add"} Prompt
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search prompt templates..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Prompts Grid */}
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredPrompts.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Sparkles className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  No prompt templates found
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery
                    ? "No prompts match your search."
                    : "Create your first AI prompt template."}
                </p>
                <Button onClick={() => setIsDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Prompt
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPrompts.map((prompt) => (
                <Card
                  key={prompt.id}
                  className="flex flex-col h-[400px] hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3 flex-shrink-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {prompt.variables && prompt.variables.length > 0 && (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <Tag className="h-3 w-3" />
                            {prompt.variables.length} Variables
                          </Badge>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => handleCopyPrompt(prompt)}
                          >
                            {copiedId === prompt.id ? (
                              <>
                                <Check className="mr-2 h-4 w-4" />
                                Copied
                              </>
                            ) : (
                              <>
                                <Copy className="mr-2 h-4 w-4" />
                                Copy
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditPrompt(prompt)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeletePrompt(prompt.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <CardTitle className="text-lg line-clamp-1 font-semibold">
                      {prompt.name}
                    </CardTitle>
                    <CardDescription className="line-clamp-2 text-sm">
                      {prompt.description || "No description provided"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow pt-0 flex flex-col overflow-hidden">
                    <div className="bg-muted/50 rounded-md p-3 mb-3 flex-grow min-h-0">
                      <p className="text-sm text-muted-foreground line-clamp-6 overflow-hidden">
                        {prompt.content.length > 200
                          ? `${prompt.content.substring(0, 200)}...`
                          : prompt.content}
                      </p>
                    </div>
                    <div className="flex-shrink-0 space-y-3">
                      {prompt.variables && prompt.variables.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {prompt.variables
                            .slice(0, 2)
                            .map((variable, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="flex items-center gap-1 text-xs"
                              >
                                <Tag className="h-3 w-3" />
                                {variable.length > 12
                                  ? `${variable.substring(0, 12)}...`
                                  : variable}
                              </Badge>
                            ))}
                          {prompt.variables.length > 2 && (
                            <Badge variant="secondary" className="text-xs">
                              +{prompt.variables.length - 2} more
                            </Badge>
                          )}
                        </div>
                      )}
                      <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                        <span>
                          Created{" "}
                          {new Date(prompt.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </ScopingAILayout>
  );
}
