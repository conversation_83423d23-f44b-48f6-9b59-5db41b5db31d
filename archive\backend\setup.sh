#!/bin/bash

# Check if Python is installed
if ! command -v python &> /dev/null; then
    echo "Python not found. Installing Python..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew install python
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        sudo apt-get update
        sudo apt-get install -y python3 python3-pip
    elif [[ "$OSTYPE" == "msys" ]]; then
        # Windows
        echo "Please install Python manually from https://www.python.org/downloads/"
        exit 1
    fi
fi

# Install Python dependencies
echo "Installing Python dependencies..."
cd python_scripts
pip install -r requirements.txt

echo "Setup complete!" 