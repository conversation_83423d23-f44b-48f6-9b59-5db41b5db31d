import React from 'react';
import { RouteObject } from 'react-router-dom';
import { PluginModule, NavigationItem } from '../../plugins/types';
import { KnowledgeProvider } from './context/KnowledgeContext';
import KnowledgeBase from './components/KnowledgeBase';

// Define routes for the knowledge plugin
const routes: RouteObject[] = [
  {
    path: '/knowledge',
    element: React.createElement(KnowledgeBase)
  }
];

// Define navigation items
const navigation: NavigationItem[] = [
  {
    name: 'Knowledge Base',
    route: '/knowledge',
    icon: 'Brain',
    order: 2,
    permissions: ['knowledge:read']
  }
];

// Knowledge plugin module
const knowledgePlugin: PluginModule = {
  routes,
  navigation,
  providers: [KnowledgeProvider],
  config: {
    name: 'Knowledge Base',
    version: '1.0.0',
    description: 'AI-powered document management and chat system'
  },
  initialize: async () => {
    console.log('🧠 Knowledge Base plugin initialized');
  },
  cleanup: async () => {
    console.log('🧠 Knowledge Base plugin cleaned up');
  }
};

export default knowledgePlugin; 