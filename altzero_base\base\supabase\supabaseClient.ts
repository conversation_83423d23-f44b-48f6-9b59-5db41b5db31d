import { createClient, type Session } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error("Missing Supabase environment variables");
}

// Create the Supabase client
const supabase = createClient(supabaseUrl || "", supabaseAnonKey || "", {
  auth: {
    persistSession: true,
  },
  global: {
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  },
});

// Setup session refresh handling
export const setupSessionRefresh = async (): Promise<void> => {
  try {
    // Get the current session
    const { data, error } = await supabase.auth.getSession();

    // If there was an error or no session, return early
    if (error || !data.session) {
      return;
    }

    // If we have a session, set up automatic refresh
    const session = data.session;

    // Set up a timer to refresh the session before it expires
    // JWT typically expires in 1 hour (3600 seconds)
    // We'll refresh 5 minutes before expiration (300 seconds)
    const expiresIn = session.expires_in || 3600;
    const refreshTime = Math.max((expiresIn - 300) * 1000, 10000); // Convert to milliseconds, minimum 10 seconds

    // Set a timeout to refresh the session
    setTimeout(async () => {
      const { error } = await supabase.auth.refreshSession();
      if (error) {
        console.error("Failed to refresh session:", error);
      } else {
        console.log("Session refreshed successfully");
        // Set up the next refresh
        setupSessionRefresh();
      }
    }, refreshTime);

    console.log(
      `Session refresh scheduled in ${refreshTime / 1000 / 60} minutes`
    );
  } catch (error) {
    console.error("Error setting up session refresh:", error);
  }
};

// Export the supabase client
export { supabase };
