"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { useRouter } from "next/navigation";
import { useUser } from "../contexts/UserContext";
import { supabase } from "../lib/supabase";
import { Button } from "./ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { UserIcon, Settings, LogOut } from "lucide-react";

export function UserButton() {
  const { user, userDetails, isLoading } = useUser();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await supabase.auth.signOut();
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Show login button if not logged in
  if (!user && !isLoading) {
    return (
      <Button variant="outline" size="sm" asChild>
        <Link href="/login">Login</Link>
      </Button>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <Avatar className="h-8 w-8">
        <AvatarFallback className="bg-primary/10">...</AvatarFallback>
      </Avatar>
    );
  }

  // Get user initials for avatar
  const getUserInitials = () => {
    if (userDetails?.name) {
      return userDetails.name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2);
    }
    
    return user?.email?.[0].toUpperCase() || "U";
  };

  // Get user display name
  const getDisplayName = () => {
    return userDetails?.name || user?.email || "User";
  };

  // Render user dropdown
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage src={userDetails?.avatar || ""} alt={getDisplayName()} />
            <AvatarFallback className="bg-primary text-primary-foreground">
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{getDisplayName()}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/settings/profile" className="flex w-full cursor-pointer items-center">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="cursor-pointer"
          disabled={isLoggingOut}
          onClick={handleLogout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>{isLoggingOut ? "Signing out..." : "Sign out"}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 