import { createClient } from "@supabase/supabase-js";

// Use a more resilient way to access environment variables that works in both Next.js and Vite
const getEnvVar = (key: string): string | undefined => {
  // First check process.env (Node.js/Next.js)
  if (typeof process !== "undefined" && process.env && process.env[key]) {
    return process.env[key];
  }

  // Then check import.meta.env (Vite)
  if (
    typeof import.meta !== "undefined" &&
    import.meta.env &&
    import.meta.env[key]
  ) {
    return import.meta.env[key];
  }

  // Check window.ENV for runtime environment variables (if available)
  if (
    typeof window !== "undefined" &&
    (window as any).ENV &&
    (window as any).ENV[key]
  ) {
    return (window as any).ENV[key];
  }

  return undefined;
};

// Try multiple environment variable naming conventions
const supabaseUrl =
  getEnvVar("VITE_SUPABASE_URL") ||
  getEnvVar("NEXT_PUBLIC_SUPABASE_URL") ||
  getEnvVar("SUPABASE_URL");

const supabaseAnonKey =
  getEnvVar("VITE_SUPABASE_ANON_KEY") ||
  getEnvVar("NEXT_PUBLIC_SUPABASE_ANON_KEY") ||
  getEnvVar("SUPABASE_ANON_KEY");

if (!supabaseUrl || !supabaseAnonKey) {
  const missingVars = [];
  if (!supabaseUrl) missingVars.push("SUPABASE_URL");
  if (!supabaseAnonKey) missingVars.push("SUPABASE_ANON_KEY");

  console.error(
    `Missing required Supabase environment variables: ${missingVars.join(", ")}`
  );
  console.error(
    "Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file"
  );
}

// Create the Supabase client with proper configuration
const supabase = createClient(supabaseUrl || "", supabaseAnonKey || "", {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
});

// Export the supabase client
export { supabase };
