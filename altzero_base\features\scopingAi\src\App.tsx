import { Routes, Route, Navigate } from "react-router-dom";
import { UserProvider } from "../../base/contextapi/UserContext";
import { ThemeProvider } from "../../base/components/theme-provider";

// Import auth components from @base
import Login from "../../base/components/auth/Login";
import Signup from "../../base/components/auth/Signup";
import ForgotPassword from "../../base/pages/forgot-password";
import UpdatePassword from "../../base/pages/update-password";
import Settings from "../../base/pages/Settings";

// Import unique scopingAi page components
import LandingPage from "./pages/LandingPage";
import Dashboard from "./pages/Dashboard";
import ScopingProposals from "./pages/ScopingProposals";
import Documents from "./pages/Documents";
import Scoping from "./pages/Scoping";
import KnowledgeBase from "./pages/KnowledgeBase";
import TermsOfService from "./pages/TermsOfService";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TestPdf from "./pages/TestPdf";
import HomePage from "./pages/HomePage";
import KnowledgeBaseDocuments from "./pages/KnowledgeBaseDocuments";
import DocumentsLibrary from "./pages/DocumentsLibrary";
import KnowledgeBaseClients from "./pages/KnowledgeBaseClients";
import KnowledgeBasePrompts from "./pages/KnowledgeBasePrompts";
import DocumentsGenerated from "./pages/DocumentsGenerated";
import DocumentsNew from "./pages/DocumentsNew";
import SharedDocument from "./pages/SharedDocument";

function App() {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <UserProvider>
        <Routes>
          {/* Main Routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/landingpage" element={<LandingPage />} />
          <Route path="/dashboard" element={<Dashboard />} />

          {/* Authentication Routes - Using @base components */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/update-password" element={<UpdatePassword />} />

          {/* Shared Document Route - Public access */}
          <Route path="/shared/:token" element={<SharedDocument />} />

          {/* Documents Routes - ScopingAi specific */}
          <Route path="/documents" element={<Documents />} />
          <Route path="/documents/generated" element={<DocumentsGenerated />} />
          <Route path="/documents/new" element={<DocumentsNew />} />
          <Route path="/documents/library" element={<DocumentsLibrary />} />

          {/* Scoping Proposals Routes - ScopingAi specific */}
          <Route path="/scoping-proposals" element={<ScopingProposals />} />
          <Route path="/scoping-proposals/new" element={<DocumentsNew />} />
          <Route
            path="/scoping-proposals/generated"
            element={<DocumentsGenerated />}
          />

          {/* Knowledge Base Routes - ScopingAi specific */}
          <Route path="/knowledge-base" element={<KnowledgeBase />} />
          <Route
            path="/knowledge-base/documents"
            element={<DocumentsLibrary />}
          />
          <Route
            path="/knowledge-base/clients"
            element={<KnowledgeBaseClients />}
          />
          <Route
            path="/knowledge-base/prompts"
            element={<KnowledgeBasePrompts />}
          />

          {/* Settings Routes - Using @base component */}
          <Route path="/settings" element={<Settings />} />
          <Route path="/settings/profile" element={<Settings />} />

          {/* Other ScopingAi specific routes */}
          <Route path="/scoping" element={<Scoping />} />
          <Route path="/test-pdf" element={<TestPdf />} />

          {/* Legal Routes - ScopingAi specific */}
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </UserProvider>
    </ThemeProvider>
  );
}

export default App;
