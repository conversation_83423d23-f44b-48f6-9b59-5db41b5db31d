import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import * as crypto from 'crypto';
import pdfParse from 'pdf-parse';
import { StoredDocument } from '../../types/scoping';

/**
 * Simple document parser that extracts text content from files,
 * particularly focusing on PDFs.
 */
export class SimpleDocumentParser {
  private tempDir: string;

  constructor() {
    this.tempDir = os.tmpdir();
  }

  /**
   * Process an uploaded document and extract its content
   */
  async processDocument(file: any): Promise<StoredDocument> {
    try {
      console.log('Processing document:', {
        name: file.name,
        size: file.size,
        mimetype: file.mimetype,
        tempFilePath: file.tempFilePath || 'none',
        hasData: !!file.data,
        dataLength: file.data ? file.data.length : 0
      });

      // Generate a unique ID for the document
      const documentId = crypto.randomUUID();
      
      // Get file extension
      const fileExtension = path.extname(file.name).toLowerCase();
      
      // Create a temporary file path
      const tempFilePath = file.tempFilePath || path.join(this.tempDir, `${documentId}${fileExtension}`);
      
      // If file wasn't saved to temp location already, save it now
      if (!file.tempFilePath) {
        console.log(`Writing file to temp location: ${tempFilePath}`);
        await fs.promises.writeFile(tempFilePath, file.data);
      }
      
      // Check if file exists and has content
      const stats = await fs.promises.stat(tempFilePath);
      console.log(`File stats: size=${stats.size}, isFile=${stats.isFile()}`);
      
      if (stats.size === 0) {
        throw new Error('Uploaded file is empty');
      }
      
      // Extract content based on file type
      let content = '';
      
      if (fileExtension === '.pdf') {
        content = await this.parsePdf(tempFilePath);
      } else {
        // For now, we only support PDF files
        throw new Error('Unsupported file type. Only PDF files are currently supported.');
      }
      
      // Cleanup temp file if we created it (not if express-fileupload created it)
      if (!file.tempFilePath) {
        await this.cleanupDocument(tempFilePath);
      }
      
      // Return document details
      return {
        id: documentId,
        filename: file.name,
        content,
        size: file.size
      };
    } catch (error: any) {
      console.error('Error processing document:', error);
      throw new Error(`Failed to process document: ${error.message}`);
    }
  }
  
  /**
   * Parse PDF file and extract text content
   */
  private async parsePdf(filePath: string): Promise<string> {
    try {
      console.log(`Reading PDF file from: ${filePath}`);
      const dataBuffer = await fs.promises.readFile(filePath);
      
      // Check if buffer has data
      if (!dataBuffer || dataBuffer.length === 0) {
        throw new Error('PDF file is empty');
      }
      
      console.log(`Parsing PDF with buffer size: ${dataBuffer.length} bytes`);
      const pdfData = await pdfParse(dataBuffer);
      console.log(`PDF parsing successful: extracted ${pdfData.text.length} characters`);
      return pdfData.text;
    } catch (error: any) {
      console.error('Error parsing PDF:', error);
      throw new Error(`Failed to parse PDF: ${error.message}`);
    }
  }
  
  /**
   * Remove temporary file
   */
  private async cleanupDocument(filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath);
      console.log(`Cleaned up temporary file: ${filePath}`);
    } catch (error) {
      console.error('Error cleaning up document:', error);
      // We don't throw an error here, as this is just cleanup
    }
  }
} 