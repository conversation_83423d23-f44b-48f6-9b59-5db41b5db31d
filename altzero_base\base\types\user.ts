export interface User {
  id: string;
  email?: string;
  email_confirmed_at?: string;
  created_at?: string;
  last_sign_in_at?: string;
  app_metadata?: Record<string, any>;
  user_metadata?: Record<string, any>;
  aud?: string;
}

export interface Profile {
  id: string;
  full_name?: string;
  avatar_url?: string;
  updated_at?: string;
  about?: string;
  role?: "retail" | "corporate";
  email?: string;
  is_onboarded?: boolean;

  // Joined data
  user?: User;
}
