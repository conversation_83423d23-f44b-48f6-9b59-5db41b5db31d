import React from "react";

/**
 * CopilotDashboard - Main interface for AltZero CopilotKit features
 * Provides access to MCP database querying, component interaction, and AI assistance
 */
const CopilotDashboard: React.FC = () => {
  return (
    <div className="bg-card rounded-lg shadow-md p-6 text-card-foreground">
      {/* Header */}
      <div className="text-center space-y-4 mb-8">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 002 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 className="text-4xl font-bold text-primary">AltZero Copilot</h1>
        </div>
        <p className="text-muted-foreground text-lg max-w-3xl mx-auto">
          Advanced AI assistant with database querying, component interaction, and workflow automation.
          Use natural language to interact with your data and application components.
        </p>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Database Querying */}
        <div className="dashboard-card hover:shadow-lg transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Database Querying</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Query your database using natural language. Try "@customers from California" or "@orders today".
            </p>
            <div className="space-y-2">
              <div className="text-sm bg-muted/50 p-2 rounded">
                <span className="text-primary">@customer</span> John
              </div>
              <div className="text-sm bg-muted/50 p-2 rounded">
                <span className="text-primary">@orders</span> last month
              </div>
              <div className="text-sm bg-muted/50 p-2 rounded">
                <span className="text-primary">@users</span> active
              </div>
            </div>
          </div>
        </div>

        {/* Component Interaction */}
        <div className="dashboard-card hover:shadow-lg transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Component Control</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Interact with application components through natural language commands.
            </p>
            <div className="space-y-2">
              <div className="text-sm bg-muted/50 p-2 rounded">
                "Navigate to dashboard"
              </div>
              <div className="text-sm bg-muted/50 p-2 rounded">
                "Upload document to knowledge base"
              </div>
              <div className="text-sm bg-muted/50 p-2 rounded">
                "Run SEO audit for example.com"
              </div>
            </div>
          </div>
        </div>

        {/* AI Chat */}
        <div className="dashboard-card hover:shadow-lg transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">AI Assistant</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Get help with complex workflows and data analysis through AI-powered conversations.
            </p>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 px-3 py-1 bg-primary/10 border border-primary/20 rounded-full">
                <svg className="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span className="text-xs font-medium text-primary">
                  CopilotKit Powered
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* MCP Status */}
        <div className="dashboard-card">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">MCP Connection</h4>
                <p className="text-xs text-muted-foreground">Model Context Protocol</p>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-yellow-600">Connecting</span>
              </div>
            </div>
          </div>
        </div>

        {/* Database Status */}
        <div className="dashboard-card">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">Database</h4>
                <p className="text-xs text-muted-foreground">Supabase Connection</p>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-green-600">Connected</span>
              </div>
            </div>
          </div>
        </div>

        {/* AI Model Status */}
        <div className="dashboard-card">
          <div className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">AI Model</h4>
                <p className="text-xs text-muted-foreground">GPT-4</p>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-green-600">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="dashboard-card">
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Quick Start</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Try Database Queries:</h4>
              <div className="space-y-2">
                <button className="w-full text-left p-3 bg-muted/30 hover:bg-muted/50 rounded-lg transition-colors text-sm">
                  Show me all customers from this month
                </button>
                <button className="w-full text-left p-3 bg-muted/30 hover:bg-muted/50 rounded-lg transition-colors text-sm">
                  Count active users in the last 30 days
                </button>
                <button className="w-full text-left p-3 bg-muted/30 hover:bg-muted/50 rounded-lg transition-colors text-sm">
                  Find orders with status pending
                </button>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Component Actions:</h4>
              <div className="space-y-2">
                <button className="w-full text-left p-3 bg-muted/30 hover:bg-muted/50 rounded-lg transition-colors text-sm">
                  Navigate to the knowledge base
                </button>
                <button className="w-full text-left p-3 bg-muted/30 hover:bg-muted/50 rounded-lg transition-colors text-sm">
                  Create a new team
                </button>
                <button className="w-full text-left p-3 bg-muted/30 hover:bg-muted/50 rounded-lg transition-colors text-sm">
                  Generate a performance report
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="text-center mt-8">
        <div className="inline-flex items-center space-x-2 px-4 py-2 bg-amber-500/10 border border-amber-500/20 rounded-full">
          <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <span className="text-sm font-medium text-amber-700">
            Full CopilotKit integration coming soon!
          </span>
        </div>
      </div>
    </div>
  );
};

export default CopilotDashboard; 