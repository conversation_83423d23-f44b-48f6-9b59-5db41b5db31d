import { OpenRouterClient } from '../llm/openrouter';
import { environment } from '../../config/environment';
import { OpenAI } from 'openai';
import { Request, Response } from 'express';
import { ClientInfo, ScopingInfo, PromptTemplate, SectionDefinition } from '../../types/scoping';
import axios from 'axios';
import { PROMPTS, fillTemplate } from './prompts';
import { logLLMRequest, logLLMResponse, logError, sanitizeErrorMessage } from '../../utils/logger';

interface ScopingRequest {
  clientInfo: ClientInfo;
  scopingInfo: ScopingInfo;
  promptTemplate: PromptTemplate;
  sections: SectionDefinition[];
  apiKey: string;
}

interface ScopingSection {
  title: string;
  content: string;
}

interface Scoping {
  id: string;
  clientName: string;
  projectName: string;
  description: string;
  sections: ScopingSection[];
  createdAt: Date;
  updatedAt: Date;
}

type ScopingEvent = {
  type: 'started' | 'research' | 'summary' | 'section' | 'completed' | 'error';
  scopingId: string;
  data?: any;
  error?: string;
};

// Define session extension
declare module 'express-session' {
  interface SessionData {
    documentStore?: {
      [key: string]: {
        id: string;
        filename: string;
        content: string;
        size: number;
      }
    }
  }
}

export class ScopingStreamService {
  private llm: any; // OpenAI or OpenRouter client
  private initialized = false;

  constructor() {
    // Will initialize LLM on first use
  }

  async initialize(): Promise<void> {
    try {
      console.log("Initializing LLM service for scoping...");
      console.log("Using OpenRouter:", environment.useOpenRouter);
      
      // Correctly set up the LLM client based on configuration
      if (environment.useOpenRouter) {
        console.log("Initializing OpenRouter client with Gemini model:", environment.geminiModel);
        this.llm = new OpenRouterClient();
        this.initialized = true;
        console.log("OpenRouter client initialized successfully");
        return;
      } else {
        // OpenAI configuration (fallback)
        console.log("Initializing OpenAI client with model:", process.env.OPENAI_MODEL);
        this.llm = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
          baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
          timeout: 60000,
          maxRetries: 3
        });
      }
      
      // Only do connection test for OpenAI
      if (!environment.useOpenRouter) {
        console.log("Testing OpenAI connection...");
        
        try {
          const model = process.env.OPENAI_MODEL || "gpt-3.5-turbo";
          const testPrompt = "Test connection";
          logLLMRequest(model, "initialize", testPrompt);
            
          const testResponse = await this.llm.chat.completions.create({
            messages: [{ role: "user", content: testPrompt }],
            model: model,
            max_tokens: 10
          });

          const responseText = testResponse.choices[0]?.message?.content || "No response";
          logLLMResponse("initialize", responseText);
        } catch (testError: any) {
          const sanitizedError = sanitizeErrorMessage(testError.message);
          logError("initialize", sanitizedError);
          throw testError;
        }
      }
      
      console.log("LLM service initialized successfully");
      this.initialized = true;
    } catch (error: any) {
      const sanitizedError = sanitizeErrorMessage(error.message);
      logError("initialize", sanitizedError);
      throw error;
    }
  }

  async *generateScopingStream(request: ScopingRequest): AsyncGenerator<ScopingEvent> {
    const scopingId = `scop_${Date.now()}`;
    
    try {
      // Initialize LLM if not already initialized
      if (!this.initialized) {
        console.log('Initializing LLM...');
        await this.initialize();
      }

      // Send initial event
      yield {
        type: 'started',
        scopingId,
        data: { message: 'Starting scoping document generation...' }
      };

      // Determine model to use - explicitly use Gemini with OpenRouter
      const model = environment.useOpenRouter 
        ? environment.geminiModel  // Use Gemini model with OpenRouter
        : (process.env.OPENAI_MODEL || "gpt-3.5-turbo");
        
      console.log(`Using model: ${model}`);
      console.log(`Starting scoping document generation for client: ${request.clientInfo.name}`);
      
      const sections: ScopingSection[] = [];

      // Research phase
      yield {
        type: 'research',
        scopingId,
        data: { status: 'started' },
      };

      // Use the prompt template from the request or default
      const promptPrefix = request.promptTemplate.content || PROMPTS.SYSTEM_SCOPING;
      
      // Build a comprehensive prompt with all the client and scoping information
      const clientInfoStr = `
        Client Name: ${request.clientInfo.name}
        Industry: ${request.clientInfo.industry}
        Company: ${request.clientInfo.company}
        Contact Person: ${request.clientInfo.contactPerson}
        Email: ${request.clientInfo.email}
      `;
      
      const scopingInfoStr = `
        Project Name: ${request.scopingInfo.projectName}
        Project Description: ${request.scopingInfo.projectDescription}
        Timeline: ${request.scopingInfo.timeline}
        Budget: ${request.scopingInfo.budget}
        Goals: ${request.scopingInfo.goals.join(", ")}
      `;
      
      const sectionsStr = request.sections.map(section => 
        `${section.title}: ${section.description}`
      ).join("\n");
      
      // Use template from prompts.ts
      const researchPrompt = fillTemplate(PROMPTS.RESEARCH, {
        clientInfo: clientInfoStr,
        projectInfo: scopingInfoStr,
        sections: sectionsStr,
        documentContent: '' // No document content at this stage
      });

      let researchResponse;

      // Add progress events before each major operation
      yield {
        type: 'started',
        scopingId,
        data: { message: 'Processing...' }
      };
      
      try {
        // Log the request for debugging
        logLLMRequest(model, "research", researchPrompt, {
          max_tokens: 1000,
          temperature: 0.7
        });
        
        const startTime = Date.now();
        
        // Make API call to LLM
        researchResponse = await this.llm.chat.completions.create({
          messages: [{ role: "user", content: researchPrompt }],
          model: model,
          max_tokens: 1000,
          temperature: 0.7,
        });
        
        const timeTaken = Date.now() - startTime;
        
        // Extract research content
        const researchContent = researchResponse.choices[0]?.message?.content || 'No research content generated';
        
        // Log the response for debugging
        logLLMResponse("research", researchContent, timeTaken);
      } catch (apiError: any) {
        logError("research", apiError);
        
        // Yield error event that the frontend can handle
        yield { 
          type: 'error', 
          scopingId,
          error: apiError.message || 'API call failed',
          data: { 
            details: JSON.stringify(apiError)
          }
        };
        return;
      }

      // Extract research content
      const researchContent = researchResponse.choices[0]?.message?.content || 'No research content generated';
      
      // Send research completion event
      yield {
        type: 'research',
        scopingId,
        data: { status: 'completed', content: researchContent },
      };

      // Generate summary
      yield {
        type: 'summary',
        scopingId,
        data: { status: 'started' },
      };

      // Use template from prompts.ts
      const summaryPrompt = fillTemplate(PROMPTS.SUMMARY, {
        clientName: request.clientInfo.name,
        projectName: request.scopingInfo.projectName,
        researchContent: researchContent
      });

      let summaryResponse;
      
      try {
        // Log the request for debugging
        logLLMRequest(model, "summary", summaryPrompt, {
          max_tokens: 500,
          temperature: 0.7
        });
        
        const startTime = Date.now();
        
        summaryResponse = await this.llm.chat.completions.create({
          messages: [{ role: "user", content: summaryPrompt }],
          model: model,
          max_tokens: 500,
          temperature: 0.7,
        });
        
        const timeTaken = Date.now() - startTime;
        
        // Extract summary content
        const summaryContent = summaryResponse.choices[0]?.message?.content || 'No summary content generated';
        
        // Log the response for debugging
        logLLMResponse("summary", summaryContent, timeTaken);
      } catch (apiError: any) {
        logError("summary", apiError);
        yield { 
          type: 'error', 
          scopingId,
          error: apiError.message || 'API call failed during summary generation',
        };
        return;
      }

      const summaryContent = summaryResponse.choices[0]?.message?.content || 'No summary content generated';
      
      // Send summary completion event
      yield {
        type: 'summary',
        scopingId,
        data: { status: 'completed', content: summaryContent },
      };

      // Generate each section
      for (const section of request.sections) {
        yield {
          type: 'section',
          scopingId,
          data: { status: 'started', title: section.title },
        };

        // Use template from prompts.ts
        const sectionPrompt = fillTemplate(PROMPTS.SECTION, {
          researchContent: researchContent,
          clientInfo: clientInfoStr,
          projectInfo: scopingInfoStr,
          sectionTitle: section.title,
          sectionDescription: section.description
        });

        let sectionResponse;
        
        try {
          // Log the request for debugging
          logLLMRequest(
            environment.geminiModel, 
            `section-${section.title}`, 
            sectionPrompt,
            { system_prompt: model }
          );
          
          const startTime = Date.now();
          
          sectionResponse = await this.llm.chat.completions.create({
            messages: [{ role: "user", content: sectionPrompt }],
            model: model,
            max_tokens: 1000,
            temperature: 0.7,
          });
          
          const timeTaken = Date.now() - startTime;
          
          // Extract section content
          const sectionContent = sectionResponse.choices[0]?.message?.content || `No content generated for ${section.title}`;
          
          // Log the response for debugging
          logLLMResponse(`section-${section.title}`, sectionContent, timeTaken);
        } catch (apiError: any) {
          logError(`section-${section.title}`, apiError);
          yield { 
            type: 'error', 
            scopingId,
            error: apiError.message || `API call failed during section "${section.title}" generation`,
          };
          continue;
        }

        const sectionContent = sectionResponse.choices[0]?.message?.content || `No content generated for ${section.title}`;
        
        const sectionData = {
          title: section.title,
          content: sectionContent,
        };
        
        sections.push(sectionData);
        
        // Send section completion event
        yield {
          type: 'section',
          scopingId,
          data: { status: 'completed', section: sectionData },
        };
      }

      // Send completion event with all sections
      yield {
        type: 'completed',
        scopingId,
        data: {
          scoping: {
            id: scopingId,
            clientName: request.clientInfo.name,
            projectName: request.scopingInfo.projectName,
            description: request.scopingInfo.projectDescription,
            sections: sections,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }
      };
    } catch (error: any) {
      logError('generateScopingStream', error);
      yield {
        type: 'error',
        scopingId,
        error: error.message || 'Unknown error occurred',
      };
    }
  }

  async streamResponse(req: Request, res: Response): Promise<void> {
    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    });

    // Handle connection close
    const handleClose = () => {
      console.log('Client closed connection');
      // Clean up any resources if needed
    };
    
    // Listen for client disconnect
    req.on('close', handleClose);

    try {
      // Extract data from request
      const { clientInfo, scopingInfo, promptTemplate, sections, documentId } = req.body;

      console.log('Received scoping stream request with document ID:', documentId);

      // Validation
      if (!clientInfo || !scopingInfo || !promptTemplate || !sections || sections.length === 0) {
        this.sendEvent(res, 'error', 'Missing required fields');
        return this.endStream(res);
      }

      // Signal start
      this.sendEvent(res, 'started', { message: 'Starting AI scoping document generation' });

      // Get document content if a document was uploaded
      let documentContent = '';
      if (documentId && req.session?.documentStore && req.session.documentStore[documentId]) {
        documentContent = req.session.documentStore[documentId].content;
        console.log(`Found document in session with ID ${documentId}, content length: ${documentContent.length} characters`);
        this.sendEvent(res, 'research', { 
          status: 'in_progress', 
          message: 'Analyzing uploaded reference document'
        });
      } else if (documentId) {
        console.log(`Document ID ${documentId} provided but not found in session`);
        this.sendEvent(res, 'research', { 
          status: 'warning', 
          message: 'Referenced document not found or empty, proceeding without it'
        });
      }

      // Generate the scoping document
      await this.generateScopingDocument(
        res, 
        clientInfo, 
        scopingInfo, 
        promptTemplate, 
        sections,
        documentContent
      );

      // End the stream
      this.endStream(res);
    } catch (error: any) {
      logError('streamResponse', error);
      try {
        this.sendEvent(res, 'error', { message: error.message || 'An error occurred during scoping document generation' });
        this.endStream(res);
      } catch (writeError) {
        console.error('Failed to send error event:', writeError);
      }
    } finally {
      // Remove listener
      req.off('close', handleClose);
    }
  }

  private async generateScopingDocument(
    res: Response,
    clientInfo: ClientInfo,
    scopingInfo: ScopingInfo,
    promptTemplate: PromptTemplate,
    sections: SectionDefinition[],
    documentContent: string = ''
  ): Promise<void> {
    try {
      // Build the system prompt
      const systemPrompt = this.buildSystemPrompt(promptTemplate);
      
      // Build the user prompt
      const userPrompt = this.buildUserPrompt(
        clientInfo, 
        scopingInfo, 
        sections,
        documentContent
      );

      // Generate introduction and summary
      this.sendEvent(res, 'summary', { status: 'in_progress', message: 'Generating document introduction' });
      
      const introduction = await this.generateIntroduction(systemPrompt, userPrompt);
      
      this.sendEvent(res, 'summary', { status: 'completed', message: 'Introduction generated' });
      
      // Generate each section
      const sectionResults = [];
      for (const section of sections) {
        this.sendEvent(res, 'section', { 
          status: 'in_progress', 
          section: { title: section.title } 
        });
        
        const sectionContent = await this.generateSection(
          systemPrompt, 
          userPrompt, 
          section,
          introduction
        );
        
        sectionResults.push({
          title: section.title,
          content: sectionContent
        });
        
        this.sendEvent(res, 'section', { 
          status: 'completed', 
          section: { 
            title: section.title,
            preview: sectionContent.substring(0, 100) + '...' 
          } 
        });
      }
      
      // Create the final document
      const result = {
        id: `scoping_${Date.now()}`,
        clientName: clientInfo.name,
        projectName: scopingInfo.projectName,
        description: introduction,
        sections: sectionResults,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      this.sendEvent(res, 'completed', { result });
    } catch (error) {
      logError('generateScopingDocument', error);
      this.sendEvent(res, 'error', { error: 'Error generating scoping document' });
    }
  }

  private buildSystemPrompt(promptTemplate: PromptTemplate): string {
    // Use the provided template or default to our system prompt
    return promptTemplate.content || PROMPTS.SYSTEM_SCOPING;
  }

  private buildUserPrompt(
    clientInfo: ClientInfo, 
    scopingInfo: ScopingInfo, 
    sections: SectionDefinition[],
    documentContent: string = ''
  ): string {
    // Format goals into a bulleted list
    const goalsFormatted = scopingInfo.goals.map((goal: string, index: number) => 
      `${index + 1}. ${goal}`
    ).join("\n");
    
    // Format sections into a bulleted list
    const sectionsFormatted = sections.map((section, index) => 
      `${index + 1}. **${section.title}**: ${section.description}`
    ).join("\n");
    
    // Format document content section if available
    let docContentSection = '';
    if (documentContent && documentContent.trim().length > 0) {
      docContentSection = `## Reference Document Content
Please use the following reference document content to inform and enhance your scoping document:

${documentContent.length > 4000 
  ? documentContent.substring(0, 4000) + '...\n\n[Note: The reference document has been truncated due to length.]' 
  : documentContent}`;
    }
    
    // Use template from prompts.ts and fill in values
    return fillTemplate(PROMPTS.USER_FULL_PROMPT, {
      clientName: clientInfo.name,
      industry: clientInfo.industry,
      company: clientInfo.company,
      contactPerson: clientInfo.contactPerson,
      email: clientInfo.email,
      projectName: scopingInfo.projectName,
      projectDescription: scopingInfo.projectDescription,
      timeline: scopingInfo.timeline,
      budget: scopingInfo.budget,
      goals: goalsFormatted,
      sections: sectionsFormatted,
      documentContent: docContentSection
    });
  }

  private async generateIntroduction(systemPrompt: string, userPrompt: string): Promise<string> {
    try {
      // Ensure LLM is initialized
      if (!this.initialized) {
        console.log('Initializing LLM...');
        await this.initialize();
      }

      // Determine model to use - explicitly use Gemini with OpenRouter
      const model = environment.useOpenRouter 
        ? environment.geminiModel  // Use Gemini model with OpenRouter
        : (process.env.OPENAI_MODEL || "gpt-3.5-turbo");

      const prompt = userPrompt + '\n\nPlease provide an executive summary and introduction for this project.';
      
      // Log the request for debugging
      logLLMRequest(model, "introduction", prompt, { system_prompt: systemPrompt });
      
      const startTime = Date.now();
      
      const response = await this.llm.chat.completions.create({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        model: model,
        max_tokens: 1000,
        temperature: 0.7
      });
      
      const timeTaken = Date.now() - startTime;
      const responseText = response.choices[0]?.message?.content?.trim() || '';
      
      // Log the response for debugging
      logLLMResponse("introduction", responseText, timeTaken);

      return responseText;
    } catch (error) {
      logError("introduction", error);
      console.log('Introduction generation failed with error:', error);
      throw new Error('Failed to generate introduction');
    }
  }

  private async generateSection(
    systemPrompt: string, 
    userPrompt: string, 
    section: SectionDefinition,
    introduction: string
  ): Promise<string> {
    try {
      // Ensure LLM is initialized
      if (!this.initialized) {
        console.log('Initializing LLM...');
        await this.initialize();
      }

      // Determine model to use - explicitly use Gemini with OpenRouter
      const model = environment.useOpenRouter 
        ? environment.geminiModel  // Use Gemini model with OpenRouter
        : (process.env.OPENAI_MODEL || "gpt-3.5-turbo");

      const sectionPrompt = `${userPrompt}
      
Introduction:
${introduction}

Now, please generate detailed content for the "${section.title}" section.
This section is described as: ${section.description}

Please make this section comprehensive, professional, and aligned with the project requirements.`;

      // Log the request for debugging
      logLLMRequest(model, `section-${section.title}`, sectionPrompt, { system_prompt: systemPrompt });
      
      const startTime = Date.now();
      
      const response = await this.llm.chat.completions.create({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: sectionPrompt }
        ],
        model: model,
        max_tokens: 1500,
        temperature: 0.7
      });
      
      const timeTaken = Date.now() - startTime;
      const responseText = response.choices[0]?.message?.content?.trim() || '';
      
      // Log the response for debugging
      logLLMResponse(`section-${section.title}`, responseText, timeTaken);

      return responseText;
    } catch (error) {
      logError(`section-${section.title}`, error);
      console.log(`Section ${section.title} generation failed with error:`, error);
      throw new Error(`Failed to generate section: ${section.title}`);
    }
  }

  private sendEvent(res: Response, type: string, data: any): void {
    res.write(`event: ${type}\n`);
    res.write(`data: ${JSON.stringify({ type, ...data })}\n\n`);
  }

  private endStream(res: Response): void {
    res.write('event: end\n');
    res.write('data: {"type": "end"}\n\n');
    res.end();
  }
} 