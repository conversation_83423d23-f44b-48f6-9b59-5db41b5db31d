// =====================================================
// STATE MANAGER FOR LANGGRAPH WORKFLOWS
// =====================================================

import { PSEOWorkflowState, WorkflowError } from '../types/WorkflowState';
import { DetailedNodeResult } from '../types/NodeTypes';

export class StateManager {
  private static instance: StateManager;
  private workflowStates: Map<string, PSEOWorkflowState> = new Map();
  private executionHistory: Map<string, DetailedNodeResult[]> = new Map();
  private stateSnapshots: Map<string, PSEOWorkflowState[]> = new Map();

  private constructor() {
    // Initialize state manager
    this.setupCleanupInterval();
  }

  // Singleton pattern
  public static getInstance(): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager();
    }
    return StateManager.instance;
  }

  // Save workflow state
  public async saveWorkflowState(workflowId: string, state: PSEOWorkflowState): Promise<void> {
    try {
      // Update in-memory state
      this.workflowStates.set(workflowId, { ...state });
      
      // Create snapshot for history
      this.createStateSnapshot(workflowId, state);
      
      // Persist to database if needed
      await this.persistStateToDatabase(workflowId, state);
      
    } catch (error) {
      console.error(`Failed to save workflow state for ${workflowId}:`, error);
      throw error;
    }
  }

  // Get workflow state
  public async getWorkflowState(workflowId: string): Promise<PSEOWorkflowState | null> {
    try {
      // Try in-memory first
      const memoryState = this.workflowStates.get(workflowId);
      if (memoryState) {
        return { ...memoryState };
      }

      // Try database
      const dbState = await this.loadStateFromDatabase(workflowId);
      if (dbState) {
        this.workflowStates.set(workflowId, dbState);
        return { ...dbState };
      }

      return null;
    } catch (error) {
      console.error(`Failed to get workflow state for ${workflowId}:`, error);
      return null;
    }
  }

  // Update workflow progress
  public async updateProgress(workflowId: string, progress: number, currentStep: string): Promise<void> {
    const state = await this.getWorkflowState(workflowId);
    if (state) {
      state.progress = progress;
      state.current_step = currentStep;
      state.last_updated = new Date().toISOString();
      await this.saveWorkflowState(workflowId, state);
    }
  }

  // Add error to workflow state
  public async addError(workflowId: string, error: WorkflowError): Promise<void> {
    const state = await this.getWorkflowState(workflowId);
    if (state) {
      state.errors = [...(state.errors || []), error];
      state.status = 'failed';
      state.last_updated = new Date().toISOString();
      await this.saveWorkflowState(workflowId, state);
    }
  }

  // Update workflow status
  public async updateStatus(workflowId: string, status: PSEOWorkflowState['status']): Promise<void> {
    const state = await this.getWorkflowState(workflowId);
    if (state) {
      state.status = status;
      state.last_updated = new Date().toISOString();
      
      if (status === 'completed' || status === 'failed') {
        state.completed_at = new Date().toISOString();
      }
      
      await this.saveWorkflowState(workflowId, state);
    }
  }

  // Get workflow status
  public async getWorkflowStatus(workflowId: string): Promise<any> {
    const state = await this.getWorkflowState(workflowId);
    if (!state) {
      return { status: 'not_found' };
    }

    return {
      workflow_id: workflowId,
      status: state.status,
      progress: state.progress,
      current_step: state.current_step,
      started_at: state.started_at,
      completed_at: state.completed_at,
      processing_time: state.processing_time,
      errors: state.errors,
      keywords_found: state.keywords.length,
      last_updated: state.last_updated
    };
  }

  // Record node execution
  public async recordNodeExecution(workflowId: string, result: DetailedNodeResult): Promise<void> {
    try {
      const history = this.executionHistory.get(workflowId) || [];
      history.push(result);
      this.executionHistory.set(workflowId, history);

      // Update workflow state with node result
      const state = await this.getWorkflowState(workflowId);
      if (state) {
        state.node_data = {
          ...state.node_data,
          [result.node_name]: result
        };
        await this.saveWorkflowState(workflowId, state);
      }
    } catch (error) {
      console.error(`Failed to record node execution for ${workflowId}:`, error);
    }
  }

  // Get execution history
  public getExecutionHistory(workflowId: string): DetailedNodeResult[] {
    return this.executionHistory.get(workflowId) || [];
  }

  // Create state snapshot
  private createStateSnapshot(workflowId: string, state: PSEOWorkflowState): void {
    const snapshots = this.stateSnapshots.get(workflowId) || [];
    snapshots.push({ ...state });
    
    // Keep only last 10 snapshots
    if (snapshots.length > 10) {
      snapshots.shift();
    }
    
    this.stateSnapshots.set(workflowId, snapshots);
  }

  // Get state snapshots
  public getStateSnapshots(workflowId: string): PSEOWorkflowState[] {
    return this.stateSnapshots.get(workflowId) || [];
  }

  // Rollback to previous state
  public async rollbackToPreviousState(workflowId: string): Promise<boolean> {
    const snapshots = this.stateSnapshots.get(workflowId) || [];
    if (snapshots.length < 2) {
      return false;
    }

    // Remove current state and get previous
    snapshots.pop();
    const previousState = snapshots[snapshots.length - 1];
    
    if (previousState) {
      await this.saveWorkflowState(workflowId, previousState);
      return true;
    }
    
    return false;
  }

  // Persist state to database
  private async persistStateToDatabase(workflowId: string, state: PSEOWorkflowState): Promise<void> {
    try {
      // This would integrate with your database service
      // For now, we'll create a placeholder implementation
      
      const stateData = {
        workflow_id: workflowId,
        user_id: state.user_id,
        website_id: state.website_id,
        workflow_type: 'keyword_research',
        status: state.status,
        input_data: {
          domain: state.domain,
          seed_keywords: state.seed_keywords,
          research_method: state.research_method,
          topic_input: state.topic_input,
          data_sources: state.data_sources
        },
        output_data: {
          keywords: state.keywords,
          keyword_clusters: state.keyword_clusters,
          competitor_data: state.competitor_data,
          content_suggestions: state.content_suggestions
        },
        progress: state.progress,
        current_step: state.current_step,
        errors: state.errors,
        processing_time: state.processing_time,
        api_calls_made: state.api_calls_made,
        total_cost: state.total_cost,
        started_at: state.started_at,
        completed_at: state.completed_at,
        last_updated: state.last_updated
      };

      // You would implement actual database persistence here
      // await databaseService.upsertWorkflowState(stateData);
      
    } catch (error) {
      console.error('Failed to persist state to database:', error);
      // Don't throw error to avoid breaking workflow execution
    }
  }

  // Load state from database
  private async loadStateFromDatabase(workflowId: string): Promise<PSEOWorkflowState | null> {
    try {
      // This would integrate with your database service
      // const dbState = await databaseService.getWorkflowState(workflowId);
      // return dbState ? this.mapDbStateToWorkflowState(dbState) : null;
      
      return null; // Placeholder
    } catch (error) {
      console.error('Failed to load state from database:', error);
      return null;
    }
  }

  // Clean up old states
  private setupCleanupInterval(): void {
    // Clean up completed workflows older than 24 hours
    setInterval(() => {
      this.cleanupOldStates();
    }, 60 * 60 * 1000); // Run every hour
  }

  private cleanupOldStates(): void {
    const cutoffTime = Date.now() - (2 * 60 * 60 * 1000); // 2 hours ago (more generous)

    const workflowIds = Array.from(this.workflowStates.keys());
    let cleanedCount = 0;

    for (const workflowId of workflowIds) {
      const state = this.workflowStates.get(workflowId);
      if (state && state.completed_at) {
        const completedTime = new Date(state.completed_at).getTime();
        if (completedTime < cutoffTime) {
          this.workflowStates.delete(workflowId);
          this.executionHistory.delete(workflowId);
          this.stateSnapshots.delete(workflowId);
          cleanedCount++;
        }
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} old workflow states`);
    }
  }

  // Get all active workflows (including recently completed ones)
  public getActiveWorkflows(): Array<{ workflowId: string; state: PSEOWorkflowState }> {
    const activeWorkflows: Array<{ workflowId: string; state: PSEOWorkflowState }> = [];

    const workflowIds = Array.from(this.workflowStates.keys());
    for (const workflowId of workflowIds) {
      const state = this.workflowStates.get(workflowId);
      if (state && (state.status === 'running' || state.status === 'pending')) {
        activeWorkflows.push({ workflowId, state: { ...state } });
      }
    }

    return activeWorkflows;
  }

  // Get all workflows (including completed ones for recent access)
  public getAllWorkflows(): Array<{ workflowId: string; state: PSEOWorkflowState }> {
    const allWorkflows: Array<{ workflowId: string; state: PSEOWorkflowState }> = [];

    const workflowIds = Array.from(this.workflowStates.keys());
    for (const workflowId of workflowIds) {
      const state = this.workflowStates.get(workflowId);
      if (state) {
        allWorkflows.push({ workflowId, state: { ...state } });
      }
    }

    return allWorkflows;
  }

  // Get workflow statistics
  public getWorkflowStats(): {
    total: number;
    active: number;
    completed: number;
    failed: number;
    average_processing_time: number;
  } {
    let total = 0;
    let active = 0;
    let completed = 0;
    let failed = 0;
    let totalProcessingTime = 0;
    let completedCount = 0;

    const states = Array.from(this.workflowStates.values());
    for (const state of states) {
      total++;

      switch (state.status) {
        case 'running':
        case 'pending':
          active++;
          break;
        case 'completed':
          completed++;
          completedCount++;
          totalProcessingTime += state.processing_time;
          break;
        case 'failed':
          failed++;
          break;
      }
    }

    return {
      total,
      active,
      completed,
      failed,
      average_processing_time: completedCount > 0 ? totalProcessingTime / completedCount : 0
    };
  }

  // Cancel workflow
  public async cancelWorkflow(workflowId: string): Promise<boolean> {
    const state = await this.getWorkflowState(workflowId);
    if (state && (state.status === 'running' || state.status === 'pending')) {
      await this.updateStatus(workflowId, 'failed');
      await this.addError(workflowId, {
        node_name: 'system',
        error_message: 'Workflow cancelled by user',
        error_code: 'WORKFLOW_CANCELLED',
        timestamp: new Date().toISOString(),
        recoverable: false
      });
      return true;
    }
    return false;
  }

  // Clear all states (for testing/development)
  public clearAllStates(): void {
    this.workflowStates.clear();
    this.executionHistory.clear();
    this.stateSnapshots.clear();
  }
}
