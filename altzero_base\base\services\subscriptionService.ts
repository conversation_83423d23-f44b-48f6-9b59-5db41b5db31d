import { supabase } from "../utils/supabaseClient";
import {
  BillingCycle,
  Invoice,
  OwnerType,
  PaymentMethod,
  PlanTier,
  ResourceAddonPack,
  ResourceSummary,
  ResourceType,
  ResourceUsage,
  SubscriptionAddon,
  SubscriptionRecord,
  SubscriptionStatus,
  SubscriptionSummary,
  SubscriptionUser,
} from "../types/subscription";

// Get plan tiers
export async function getAvailablePlans(): Promise<PlanTier[]> {
  const { data, error } = await supabase
    .from("plan_tiers")
    .select("*")
    .eq("is_active", true)
    .eq("is_public", true)
    .order("tier_level", { ascending: true });

  if (error) {
    console.error("Error fetching plans:", error);
    throw error;
  }

  return data as PlanTier[];
}

// Get resource types
export async function getResourceTypes(): Promise<ResourceType[]> {
  const { data, error } = await supabase
    .from("resource_types")
    .select("*")
    .order("name");

  if (error) {
    console.error("Error fetching resource types:", error);
    throw error;
  }

  return data as ResourceType[];
}

// Get user subscription
export async function getUserSubscription(
  userId: string
): Promise<SubscriptionRecord | null> {
  const { data, error } = await supabase
    .from("subscription_records")
    .select(
      `
      *,
      plan:plan_id (*)
    `
    )
    .eq("owner_id", userId)
    .eq("owner_type", "user")
    .order("created_at", { ascending: false })
    .limit(1)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // No subscription found
      return null;
    }
    console.error("Error fetching user subscription:", error);
    throw error;
  }

  return data as SubscriptionRecord;
}

// Get organization subscription
export async function getOrganizationSubscription(
  orgId: string
): Promise<SubscriptionRecord | null> {
  const { data, error } = await supabase
    .from("subscription_records")
    .select(
      `
      *,
      plan:plan_id (*)
    `
    )
    .eq("owner_id", orgId)
    .eq("owner_type", "organisation")
    .order("created_at", { ascending: false })
    .limit(1)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // No subscription found
      return null;
    }
    console.error("Error fetching organization subscription:", error);
    throw error;
  }

  return data as SubscriptionRecord;
}

// Get resource usage for a subscription
export async function getResourceUsage(
  subscriptionId: string
): Promise<ResourceUsage[]> {
  const { data, error } = await supabase
    .from("resource_usage")
    .select(
      `
      *,
      resource_type:resource_type_id (*)
    `
    )
    .eq("subscription_id", subscriptionId);

  if (error) {
    console.error("Error fetching resource usage:", error);
    throw error;
  }

  return data as ResourceUsage[];
}

// Get subscription summary with resource usage
export async function getSubscriptionSummary(
  subscriptionId: string
): Promise<SubscriptionSummary | null> {
  const { data, error } = await supabase.rpc("get_subscription_usage_summary", {
    subscription_id: subscriptionId,
  });

  if (error) {
    console.error("Error fetching subscription summary:", error);
    throw error;
  }

  return data as SubscriptionSummary;
}

// Get subscription addons
export async function getSubscriptionAddons(
  subscriptionId: string
): Promise<SubscriptionAddon[]> {
  const { data, error } = await supabase
    .from("subscription_addons")
    .select(
      `
      *,
      addon_pack:addon_pack_id (
        *,
        resource_type:resource_type_id (*)
      )
    `
    )
    .eq("subscription_id", subscriptionId)
    .eq("is_active", true);

  if (error) {
    console.error("Error fetching subscription addons:", error);
    throw error;
  }

  return data as SubscriptionAddon[];
}

// Get available addon packs
export async function getAvailableAddonPacks(): Promise<ResourceAddonPack[]> {
  const { data, error } = await supabase
    .from("resource_addon_packs")
    .select(
      `
      *,
      resource_type:resource_type_id (*)
    `
    )
    .eq("is_active", true)
    .order("name", { ascending: true });

  if (error) {
    console.error("Error fetching addon packs:", error);
    throw error;
  }

  return data as ResourceAddonPack[];
}

// Get subscription payment methods
export async function getPaymentMethods(
  subscriptionId: string
): Promise<PaymentMethod[]> {
  const { data, error } = await supabase
    .from("payment_methods")
    .select("*")
    .eq("subscription_id", subscriptionId)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching payment methods:", error);
    throw error;
  }

  return data as PaymentMethod[];
}

// Get subscription invoices
export async function getInvoices(subscriptionId: string): Promise<Invoice[]> {
  const { data, error } = await supabase
    .from("invoices")
    .select("*")
    .eq("subscription_id", subscriptionId)
    .order("invoice_date", { ascending: false });

  if (error) {
    console.error("Error fetching invoices:", error);
    throw error;
  }

  return data as Invoice[];
}

// Create or update subscription
export async function createOrUpdateSubscription(
  subscription: Partial<SubscriptionRecord>
): Promise<SubscriptionRecord> {
  const { data, error } = await supabase
    .from("subscription_records")
    .upsert(subscription)
    .select()
    .single();

  if (error) {
    console.error("Error creating/updating subscription:", error);
    throw error;
  }

  return data as SubscriptionRecord;
}

// Cancel subscription
export async function cancelSubscription(
  subscriptionId: string,
  cancelAtPeriodEnd: boolean = true
): Promise<SubscriptionRecord> {
  const { data, error } = await supabase
    .from("subscription_records")
    .update({
      cancel_at_period_end: cancelAtPeriodEnd,
      status: cancelAtPeriodEnd
        ? SubscriptionStatus.ACTIVE
        : SubscriptionStatus.CANCELED,
      updated_at: new Date().toISOString(),
    })
    .eq("id", subscriptionId)
    .select()
    .single();

  if (error) {
    console.error("Error canceling subscription:", error);
    throw error;
  }

  return data as SubscriptionRecord;
}

// Change subscription plan
export async function changeSubscriptionPlan(
  subscriptionId: string,
  planId: string
): Promise<SubscriptionRecord> {
  const { data, error } = await supabase
    .from("subscription_records")
    .update({
      plan_id: planId,
      updated_at: new Date().toISOString(),
    })
    .eq("id", subscriptionId)
    .select()
    .single();

  if (error) {
    console.error("Error changing subscription plan:", error);
    throw error;
  }

  return data as SubscriptionRecord;
}

// Change billing cycle
export async function changeBillingCycle(
  subscriptionId: string,
  billingCycle: BillingCycle
): Promise<SubscriptionRecord> {
  const { data, error } = await supabase
    .from("subscription_records")
    .update({
      billing_cycle: billingCycle,
      updated_at: new Date().toISOString(),
    })
    .eq("id", subscriptionId)
    .select()
    .single();

  if (error) {
    console.error("Error changing billing cycle:", error);
    throw error;
  }

  return data as SubscriptionRecord;
}

// Add addon to subscription
export async function addSubscriptionAddon(
  subscriptionId: string,
  addonPackId: string,
  quantity: number = 1
): Promise<SubscriptionAddon> {
  const { data, error } = await supabase
    .from("subscription_addons")
    .insert({
      subscription_id: subscriptionId,
      addon_pack_id: addonPackId,
      quantity,
      is_active: true,
      start_date: new Date().toISOString(),
      created_at: new Date().toISOString(),
    })
    .select()
    .single();

  if (error) {
    console.error("Error adding subscription addon:", error);
    throw error;
  }

  return data as SubscriptionAddon;
}

// Remove addon from subscription
export async function removeSubscriptionAddon(addonId: string): Promise<void> {
  const { error } = await supabase
    .from("subscription_addons")
    .update({
      is_active: false,
    })
    .eq("id", addonId);

  if (error) {
    console.error("Error removing subscription addon:", error);
    throw error;
  }
}

// Get subscription users
export async function getSubscriptionUsers(
  subscriptionId: string
): Promise<SubscriptionUser[]> {
  const { data, error } = await supabase
    .from("subscription_users")
    .select(
      `
      *,
      user:user_id (
        email,
        profiles (
          full_name,
          avatar_url
        )
      ),
      added_by_user:added_by (
        email,
        profiles (
          full_name
        )
      )
    `
    )
    .eq("subscription_id", subscriptionId)
    .order("added_at", { ascending: false });

  if (error) {
    console.error("Error fetching subscription users:", error);
    throw error;
  }

  return data as SubscriptionUser[];
}

// Add user to subscription
export async function addUserToSubscription(
  subscriptionId: string,
  userId: string,
  addedBy: string
): Promise<SubscriptionUser> {
  const { data, error } = await supabase
    .from("subscription_users")
    .insert({
      subscription_id: subscriptionId,
      user_id: userId,
      added_by: addedBy,
      added_at: new Date().toISOString(),
      status: "active",
    })
    .select()
    .single();

  if (error) {
    console.error("Error adding user to subscription:", error);
    throw error;
  }

  return data as SubscriptionUser;
}

// Remove user from subscription
export async function removeUserFromSubscription(
  subscriptionId: string,
  userId: string
): Promise<void> {
  const { error } = await supabase
    .from("subscription_users")
    .delete()
    .eq("subscription_id", subscriptionId)
    .eq("user_id", userId);

  if (error) {
    console.error("Error removing user from subscription:", error);
    throw error;
  }
}
