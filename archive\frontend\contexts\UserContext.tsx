import React, { createContext, useState, useContext, useEffect, ReactNode } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "../src/utils/supabaseClient";

interface UserDetails {
  id: string;
  name: string | null;
  phone: string | null;
  avatar: string | null;
  updated_at: string | null;
}

interface UserContextType {
  user: User | null;
  userDetails: UserDetails | null;
  setUserDetails: (details: UserDetails | null) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);

  useEffect(() => {
    const fetchUserProfile = async (userId: string) => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) throw error;
        setUserDetails(data);
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_, session) => {
        const currentUser = session?.user ?? null;
        setUser(currentUser);
        if (currentUser) await fetchUserProfile(currentUser.id);
        else setUserDetails(null);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return (
    <UserContext.Provider value={{ user, userDetails, setUserDetails }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
} 