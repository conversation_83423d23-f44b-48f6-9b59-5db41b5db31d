import { useState, useCallback, useEffect } from "react";
import { useToast } from "../../../base/hooks/use-toast";
import {
  DocumentShareData,
  DocumentShareLink,
  ShareTarget,
  ShareRequest,
  ShareLinkRequest,
  ShareResponse,
  ShareLinkResponse,
  ShareSearchResult,
  SharePermissionLevel,
} from "../types/sharing";
import {
  shareDocument,
  createShareableLink,
  getDocumentShares,
  searchShareTargets,
  revokeDocumentShare,
  revokeShareLink,
  updateDocumentShare,
  getUserDocumentPermission,
  logDocumentActivity,
} from "../services/documentSharingService";

interface UseDocumentSharingOptions {
  documentId: string;
  autoLoad?: boolean;
}

interface UseDocumentSharingReturn {
  // State
  shares: DocumentShareData[];
  shareLinks: DocumentShareLink[];
  isLoading: boolean;
  isSharing: boolean;
  error: string | null;
  userPermission: SharePermissionLevel | null;

  // Actions
  loadShares: () => Promise<void>;
  shareWithTargets: (request: ShareRequest) => Promise<ShareResponse>;
  createLink: (request: ShareLinkRequest) => Promise<ShareLinkResponse>;
  updateShare: (
    shareId: string,
    updates: Partial<DocumentShareData>
  ) => Promise<void>;
  revokeShare: (shareId: string) => Promise<void>;
  revokeLink: (linkId: string) => Promise<void>;
  searchTargets: (query: string) => Promise<ShareSearchResult>;
  checkPermission: (userId?: string) => Promise<SharePermissionLevel | null>;

  // Utility
  canShare: boolean;
  canManage: boolean;
  canAdmin: boolean;
}

export const useDocumentSharing = ({
  documentId,
  autoLoad = true,
}: UseDocumentSharingOptions): UseDocumentSharingReturn => {
  const { toast } = useToast();

  // State
  const [shares, setShares] = useState<DocumentShareData[]>([]);
  const [shareLinks, setShareLinks] = useState<DocumentShareLink[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userPermission, setUserPermission] =
    useState<SharePermissionLevel | null>(null);

  // Computed properties
  const canShare = userPermission === "manage" || userPermission === "admin";
  const canManage = userPermission === "manage" || userPermission === "admin";
  const canAdmin = userPermission === "admin";

  // Load shares for the document
  const loadShares = useCallback(async () => {
    if (!documentId) return;

    try {
      setIsLoading(true);
      setError(null);

      const [sharesData, permission] = await Promise.all([
        getDocumentShares(documentId),
        getUserDocumentPermission(documentId),
      ]);

      setShares(sharesData.shares);
      setShareLinks(sharesData.links);
      setUserPermission(permission);

      // Log view activity
      await logDocumentActivity(documentId, "view_shares");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load shares";
      setError(errorMessage);
      console.error("Error loading shares:", err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  // Share document with targets
  const shareWithTargets = useCallback(
    async (request: ShareRequest): Promise<ShareResponse> => {
      if (!documentId) {
        return { success: false, message: "No document ID provided" };
      }

      try {
        setIsSharing(true);
        setError(null);

        const response = await shareDocument(documentId, request);

        if (response.success) {
          toast({
            title: "Document shared successfully",
            description: response.message,
          });

          // Refresh shares list
          await loadShares();
        } else {
          toast({
            title: "Sharing failed",
            description: response.message,
            variant: "destructive",
          });
        }

        return response;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to share document";
        setError(errorMessage);

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });

        return { success: false, message: errorMessage };
      } finally {
        setIsSharing(false);
      }
    },
    [documentId, toast, loadShares]
  );

  // Create shareable link
  const createLink = useCallback(
    async (request: ShareLinkRequest): Promise<ShareLinkResponse> => {
      if (!documentId) {
        return { success: false, message: "No document ID provided" };
      }

      try {
        setIsSharing(true);
        setError(null);

        const response = await createShareableLink(documentId, request);

        if (response.success) {
          toast({
            title: "Share link created",
            description: "Copy the link to share with others",
          });

          // Refresh shares list
          await loadShares();
        } else {
          toast({
            title: "Link creation failed",
            description: response.message,
            variant: "destructive",
          });
        }

        return response;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to create share link";
        setError(errorMessage);

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });

        return { success: false, message: errorMessage };
      } finally {
        setIsSharing(false);
      }
    },
    [documentId, toast, loadShares]
  );

  // Update existing share
  const updateShare = useCallback(
    async (
      shareId: string,
      updates: Partial<DocumentShareData>
    ): Promise<void> => {
      try {
        setError(null);
        await updateDocumentShare(shareId, updates);

        toast({
          title: "Share updated",
          description: "Permission changes have been applied",
        });

        await loadShares();
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update share";
        setError(errorMessage);

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [toast, loadShares]
  );

  // Revoke document share
  const revokeShare = useCallback(
    async (shareId: string): Promise<void> => {
      try {
        setError(null);
        await revokeDocumentShare(shareId);

        toast({
          title: "Share revoked",
          description: "Access has been removed",
        });

        await loadShares();
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to revoke share";
        setError(errorMessage);

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [toast, loadShares]
  );

  // Revoke share link
  const revokeLink = useCallback(
    async (linkId: string): Promise<void> => {
      try {
        setError(null);
        await revokeShareLink(linkId);

        toast({
          title: "Link revoked",
          description: "The share link has been deactivated",
        });

        await loadShares();
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to revoke link";
        setError(errorMessage);

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [toast, loadShares]
  );

  // Search for share targets
  const searchTargets = useCallback(
    async (query: string): Promise<ShareSearchResult> => {
      try {
        setError(null);
        return await searchShareTargets(query, documentId);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to search targets";
        setError(errorMessage);

        return {
          users: [],
          teams: [],
          organizations: [],
          isLoading: false,
          hasMore: false,
        };
      }
    },
    [documentId]
  );

  // Check user permission
  const checkPermission = useCallback(
    async (userId?: string): Promise<SharePermissionLevel | null> => {
      try {
        setError(null);
        const permission = await getUserDocumentPermission(documentId, userId);
        if (!userId) {
          setUserPermission(permission);
        }
        return permission;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to check permission";
        setError(errorMessage);
        return null;
      }
    },
    [documentId]
  );

  // Auto-load shares on mount
  useEffect(() => {
    if (autoLoad && documentId) {
      loadShares();
    }
  }, [autoLoad, documentId, loadShares]);

  return {
    // State
    shares,
    shareLinks,
    isLoading,
    isSharing,
    error,
    userPermission,

    // Actions
    loadShares,
    shareWithTargets,
    createLink,
    updateShare,
    revokeShare,
    revokeLink,
    searchTargets,
    checkPermission,

    // Utility
    canShare,
    canManage,
    canAdmin,
  };
};

// Additional utility hook for sharing a single document
export const useDocumentShare = (documentId: string) => {
  const sharing = useDocumentSharing({ documentId });

  return {
    ...sharing,
    isShared: sharing.shares.length > 0 || sharing.shareLinks.length > 0,
    shareCount: sharing.shares.length + sharing.shareLinks.length,
  };
};

// Hook for checking if current user can access a document
export const useDocumentAccess = (documentId: string) => {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [permission, setPermission] = useState<SharePermissionLevel | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      if (!documentId) return;

      try {
        const userPermission = await getUserDocumentPermission(documentId);
        setPermission(userPermission);
        setHasAccess(userPermission !== null);
      } catch (error) {
        console.error("Error checking document access:", error);
        setHasAccess(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAccess();
  }, [documentId]);

  return {
    hasAccess,
    permission,
    isLoading,
    canView: permission !== null,
    canComment:
      permission &&
      ["comment", "suggest", "edit", "manage", "admin"].includes(permission),
    canEdit: permission && ["edit", "manage", "admin"].includes(permission),
    canManage: permission && ["manage", "admin"].includes(permission),
    canAdmin: permission === "admin",
  };
};
