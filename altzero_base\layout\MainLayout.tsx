import React, { ReactNode } from "react";
import Header from "../header/Header";

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="main-layout w-full">
      <Header />
      <main className="flex-grow bg-background/50 w-full">
        <div className="w-full h-full">{children}</div>
      </main>
    </div>
  );
};

export default MainLayout;
