import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import type { PSEOAudit, PSEOWebsite } from '../../types';
import PSEOLayout from '../../components/PSEOLayout';

const AuditResults: React.FC = () => {
  const { auditId } = useParams<{ auditId: string }>();
  const navigate = useNavigate();
  const [audit, setAudit] = useState<PSEOAudit | null>(null);
  const [website, setWebsite] = useState<PSEOWebsite | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'technical' | 'content' | 'html-analysis' | 'seo-scoring' | 'report'>('overview');

  useEffect(() => {
    if (auditId) {
      loadAuditResults();
    }
  }, [auditId]);

  const loadAuditResults = async () => {
    try {
      setLoading(true);
      const auditData = await databaseService.getAuditById(auditId!);
      
      if (!auditData) {
        throw new Error('Audit not found');
      }
      
      setAudit(auditData);
      
      // Get website information
      const websiteData = await databaseService.getWebsiteById(auditData.website_id);
      setWebsite(websiteData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load audit results');
    } finally {
      setLoading(false);
    }
  };

  const downloadReport = () => {
    if (!audit?.combined_report) return;
    
    const blob = new Blob([audit.combined_report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `seo-audit-${website?.name || 'report'}-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyReport = async () => {
    if (!audit?.combined_report) return;
    
    try {
      await navigator.clipboard.writeText(audit.combined_report);
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy report:', err);
    }
  };

  // Helper function to safely parse JSON
  const parseAnalysisData = (jsonString: string | undefined) => {
    if (!jsonString) return null;
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Failed to parse analysis data:', error);
      return null;
    }
  };

  // Helper function to safely parse JSON objects (already parsed)
  const parseJSONObject = (data: any) => {
    if (!data) return null;
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.error('Failed to parse JSON object:', error);
        return null;
      }
    }
    return data;
  };

  // Helper function to get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high': 
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': 
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': 
      case 'info': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Render Technical SEO Analysis - Enhanced to show both AI and server-side analysis
  const renderTechnicalAnalysis = () => {
    const aiTechnicalData = parseAnalysisData(audit?.technical_audit_raw);
    const serverTechnicalData = parseJSONObject(audit?.technical_analysis);
    const htmlAnalysisData = parseJSONObject(audit?.html_analysis);
    
    if (!aiTechnicalData && !serverTechnicalData && !htmlAnalysisData) {
      return <p className="text-muted-foreground">Technical analysis data not available</p>;
    }

    return (
      <div className="space-y-6">
        {/* Server-side HTML Analysis Results */}
        {htmlAnalysisData && (
          <div>
            <h4 className="text-lg font-medium text-blue-700 mb-3">🔍 Server-side HTML Analysis</h4>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{htmlAnalysisData.totalIssues || 0}</div>
                  <p className="text-sm text-muted-foreground">Total Issues</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">{htmlAnalysisData.issuesByType?.critical || 0}</div>
                  <p className="text-sm text-muted-foreground">Critical</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{htmlAnalysisData.issuesByType?.warning || 0}</div>
                  <p className="text-sm text-muted-foreground">Warnings</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">{htmlAnalysisData.issuesByType?.info || 0}</div>
                  <p className="text-sm text-muted-foreground">Info</p>
                </div>
              </div>
              <div className="mt-3 text-center">
                <span className="text-lg font-medium text-blue-700">HTML Score: {htmlAnalysisData.seoScore || 0}/100</span>
              </div>
            </div>
          </div>
        )}

        {/* AI Analysis Critical Issues */}
        {aiTechnicalData?.criticalIssues && aiTechnicalData.criticalIssues.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-red-700 mb-3">🚨 AI Analysis - Critical Issues</h4>
            <div className="space-y-3">
              {aiTechnicalData.criticalIssues.map((issue: any, index: number) => (
                <div key={index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <div className="flex items-start justify-between mb-2">
                    <h5 className="font-medium text-red-800">{issue.issue}</h5>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(issue.priority)}`}>
                      {issue.priority?.toUpperCase() || 'HIGH'} PRIORITY
                    </span>
                  </div>
                  <p className="text-sm text-red-700 mb-2"><strong>Impact:</strong> {issue.impact}</p>
                  <p className="text-sm text-red-600"><strong>Recommendation:</strong> {issue.recommendation}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Server-side Technical Analysis from technical_analysis field */}
        {serverTechnicalData?.criticalIssues && serverTechnicalData.criticalIssues.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-red-700 mb-3">🔧 Server Analysis - Critical Issues</h4>
            <div className="space-y-3">
              {serverTechnicalData.criticalIssues.map((issue: any, index: number) => (
                <div key={index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <div className="flex items-start justify-between mb-2">
                    <h5 className="font-medium text-red-800">{issue.issue}</h5>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(issue.priority)}`}>
                      {issue.priority?.toUpperCase() || 'HIGH'} PRIORITY
                    </span>
                  </div>
                  <p className="text-sm text-red-700 mb-2"><strong>Impact:</strong> {issue.impact}</p>
                  <p className="text-sm text-red-600"><strong>Recommendation:</strong> {issue.recommendation}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Analysis Quick Wins */}
        {aiTechnicalData?.quickWins && aiTechnicalData.quickWins.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-green-700 mb-3">⚡ AI Analysis - Quick Wins</h4>
            <div className="space-y-3">
              {aiTechnicalData.quickWins.map((win: any, index: number) => (
                <div key={index} className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <h5 className="font-medium text-green-800 mb-2">{win.opportunity}</h5>
                  <p className="text-sm text-green-700 mb-2"><strong>Implementation:</strong> {win.implementation}</p>
                  <p className="text-sm text-green-600"><strong>Expected Impact:</strong> {win.expectedImpact}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Server-side Quick Wins */}
        {serverTechnicalData?.quickWins && serverTechnicalData.quickWins.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-green-700 mb-3">🔧 Server Analysis - Quick Wins</h4>
            <div className="space-y-3">
              {serverTechnicalData.quickWins.map((win: any, index: number) => (
                <div key={index} className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <h5 className="font-medium text-green-800 mb-2">{win.opportunity}</h5>
                  <p className="text-sm text-green-700 mb-2"><strong>Implementation:</strong> {win.implementation}</p>
                  <p className="text-sm text-green-600"><strong>Expected Impact:</strong> {win.expectedImpact}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Analysis Opportunities */}
        {aiTechnicalData?.opportunities && aiTechnicalData.opportunities.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-blue-700 mb-3">🔍 AI Analysis - Opportunities for Improvement</h4>
            <div className="space-y-3">
              {aiTechnicalData.opportunities.map((opportunity: any, index: number) => (
                <div key={index} className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <h5 className="font-medium text-blue-800 mb-2">{opportunity.area}</h5>
                  <p className="text-sm text-blue-700 mb-2"><strong>Description:</strong> {opportunity.description}</p>
                  <div className="flex justify-between text-sm">
                    <span className="text-blue-600"><strong>Effort Required:</strong> {opportunity.effort}</span>
                    <span className="text-blue-600"><strong>Potential Impact:</strong> {opportunity.impact}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Server-side Opportunities */}
        {serverTechnicalData?.opportunities && serverTechnicalData.opportunities.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-blue-700 mb-3">🔧 Server Analysis - Opportunities for Improvement</h4>
            <div className="space-y-3">
              {serverTechnicalData.opportunities.map((opportunity: any, index: number) => (
                <div key={index} className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <h5 className="font-medium text-blue-800 mb-2">{opportunity.area}</h5>
                  <p className="text-sm text-blue-700 mb-2"><strong>Description:</strong> {opportunity.description}</p>
                  <div className="flex justify-between text-sm">
                    <span className="text-blue-600"><strong>Effort Required:</strong> {opportunity.effort}</span>
                    <span className="text-blue-600"><strong>Potential Impact:</strong> {opportunity.impact}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render Content Analysis - Enhanced to show both AI and server-side analysis
  const renderContentAnalysis = () => {
    const aiContentData = parseAnalysisData(audit?.content_audit_raw);
    const serverContentData = parseJSONObject(audit?.content_analysis);
    const seoMetricsData = parseJSONObject(audit?.seo_metrics);
    
    if (!aiContentData && !serverContentData && !seoMetricsData) {
      return <p className="text-muted-foreground">Content analysis data not available</p>;
    }

    return (
      <div className="space-y-6">
        {/* Server-side SEO Metrics */}
        {seoMetricsData && (
          <div>
            <h4 className="text-lg font-medium text-purple-700 mb-3">📊 Server-side SEO Metrics</h4>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-purple-600">{Math.round(seoMetricsData.technicalScore || 0)}</div>
                  <p className="text-sm text-muted-foreground">Technical Score</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">{Math.round(seoMetricsData.contentScore || 0)}</div>
                  <p className="text-sm text-muted-foreground">Content Score</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{Math.round(seoMetricsData.performanceScore || 0)}</div>
                  <p className="text-sm text-muted-foreground">Performance Score</p>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">{Math.round(audit?.overall_score || 0)}</div>
                  <p className="text-sm text-muted-foreground">Overall Score</p>
                </div>
              </div>
            </div>

            {/* Content Metrics Details */}
            {seoMetricsData.contentMetrics && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-white border rounded-lg p-3">
                  <h5 className="font-medium text-gray-700 mb-2">Word Count</h5>
                  <p className="text-lg font-bold text-blue-600">{seoMetricsData.contentMetrics.wordCount || 0}</p>
                </div>
                <div className="bg-white border rounded-lg p-3">
                  <h5 className="font-medium text-gray-700 mb-2">Content Density</h5>
                  <p className="text-lg font-bold text-green-600">{((seoMetricsData.contentMetrics.contentDensity || 0) * 100).toFixed(1)}%</p>
                </div>
                <div className="bg-white border rounded-lg p-3">
                  <h5 className="font-medium text-gray-700 mb-2">Readability</h5>
                  <p className="text-lg font-bold text-purple-600">{Math.round(seoMetricsData.contentMetrics.readabilityScore || 0)}/100</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* AI Analysis Overview */}
        {aiContentData?.analysis && (
          <div>
            <h4 className="text-lg font-medium text-purple-700 mb-3">📊 AI Content Analysis Overview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(aiContentData.analysis).map(([key, value]: [string, any]) => (
                <div key={key} className="border border-purple-200 rounded-lg p-4 bg-purple-50">
                  <h5 className="font-medium text-purple-800 mb-2 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </h5>
                  <p className="text-sm text-purple-700">{value}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Server-side Content Analysis */}
        {serverContentData?.analysis && (
          <div>
            <h4 className="text-lg font-medium text-indigo-700 mb-3">🔧 Server Content Analysis Overview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(serverContentData.analysis).map(([key, value]: [string, any]) => (
                <div key={key} className="border border-indigo-200 rounded-lg p-4 bg-indigo-50">
                  <h5 className="font-medium text-indigo-800 mb-2 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </h5>
                  <p className="text-sm text-indigo-700">{value}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* AI Content Recommendations */}
        {aiContentData?.recommendations && aiContentData.recommendations.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-indigo-700 mb-3">💡 AI Content Recommendations</h4>
            <div className="space-y-3">
              {aiContentData.recommendations.map((rec: any, index: number) => (
                <div key={index} className="border border-indigo-200 rounded-lg p-4 bg-indigo-50">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <span className="text-xs font-medium text-indigo-600 bg-indigo-100 px-2 py-1 rounded">
                        {rec.category}
                      </span>
                      <h5 className="font-medium text-indigo-800 mt-2">{rec.suggestion}</h5>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(rec.priority)}`}>
                      {rec.priority?.toUpperCase() || 'MEDIUM'} PRIORITY
                    </span>
                  </div>
                  <p className="text-sm text-indigo-600"><strong>Implementation:</strong> {rec.implementation}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Server-side Content Recommendations */}
        {serverContentData?.recommendations && serverContentData.recommendations.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-green-700 mb-3">🔧 Server Content Recommendations</h4>
            <div className="space-y-3">
              {serverContentData.recommendations.map((rec: any, index: number) => (
                <div key={index} className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded">
                        {rec.category}
                      </span>
                      <h5 className="font-medium text-green-800 mt-2">{rec.suggestion}</h5>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(rec.priority)}`}>
                      {rec.priority?.toUpperCase() || 'MEDIUM'} PRIORITY
                    </span>
                  </div>
                  <p className="text-sm text-green-600"><strong>Implementation:</strong> {rec.implementation}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (error) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="bg-destructive/10 border border-destructive/20 rounded-md p-6">
              <h2 className="text-lg font-semibold text-destructive mb-2">Error Loading Audit Results</h2>
              <p className="text-destructive">{error}</p>
              <button
                onClick={() => navigate('/pseo/history')}
                className="mt-4 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90"
              >
                Back to Audit History
              </button>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (!audit || !website) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-12">
              <p className="text-muted-foreground">Audit not found</p>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              SEO Audit Results
            </h1>
            <p className="text-muted-foreground">
              {website.name} - {website.url}
            </p>
            <p className="text-sm text-muted-foreground">
              Completed on {new Date(audit.completed_at || audit.created_at).toLocaleDateString()} 
              {audit.processing_time_seconds && ` • ${audit.processing_time_seconds}s processing time`}
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => navigate('/pseo/history')}
              className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90 transition-colors"
            >
              📊 View All Audits
            </button>
            <button
              onClick={copyReport}
              className="bg-muted text-muted-foreground px-4 py-2 rounded-md hover:bg-muted/90 transition-colors"
            >
              Copy Report
            </button>
            <button
              onClick={downloadReport}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
            >
              Download Report
            </button>
            <button
              onClick={() => navigate('/audit-runner')}
              className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
            >
              🔄 Run New Audit
            </button>
          </div>
        </div>

        {/* Status Banner */}
        <div className={`p-4 rounded-lg mb-6 ${
          audit.status === 'completed' ? 'bg-green-50 border border-green-200' :
          audit.status === 'failed' ? 'bg-red-50 border border-red-200' :
          'bg-yellow-50 border border-yellow-200'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`font-medium ${
                audit.status === 'completed' ? 'text-green-700' :
                audit.status === 'failed' ? 'text-red-700' :
                'text-yellow-700'
              }`}>
                {audit.status === 'completed' ? '✅ Audit Completed Successfully' :
                 audit.status === 'failed' ? '❌ Audit Failed' :
                 '⏳ Audit In Progress'}
              </h3>
              {audit.error_message && (
                <p className="text-sm text-red-600 mt-1">{audit.error_message}</p>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              Audit ID: {audit.id}
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-border mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'technical', label: 'Technical SEO' },
              { id: 'content', label: 'Content Analysis' },
              { id: 'html-analysis', label: 'HTML Analysis' },
              { id: 'seo-scoring', label: 'SEO Scoring' },
              { id: 'report', label: 'Full Report' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* SEO Score Overview - Always prioritize server analysis results */}
              {(() => {
                // Read from the CORRECT database fields where server analysis data is stored
                const htmlAnalysis = parseJSONObject(audit?.html_analysis); // This is where HTML analysis is stored
                const seoScoring = parseJSONObject(audit?.seo_metrics);     // This is where SEO scoring is stored
                
                // Use server analysis data when available
                if (seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore) {
                  const overallScore = seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0;
                  const grade = seoScoring?.grade || (
                    overallScore >= 90 ? 'A+' :
                    overallScore >= 80 ? 'A' :
                    overallScore >= 70 ? 'B' :
                    overallScore >= 60 ? 'C' :
                    overallScore >= 50 ? 'D' : 'F'
                  );
                  
                  return (
                    <div className="bg-card rounded-lg border p-6">
                      <h3 className="text-lg font-semibold mb-4">📊 Server Analysis Score</h3>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className={`text-4xl font-bold mb-2 ${
                            overallScore >= 80 ? 'text-green-600' :
                            overallScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {Math.round(overallScore)}/100
                          </div>
                          <div className={`text-2xl font-bold px-3 py-1 rounded ${
                            grade === 'A+' || grade === 'A' ? 'bg-green-100 text-green-800' :
                            grade === 'B+' || grade === 'B' ? 'bg-blue-100 text-blue-800' :
                            grade === 'C+' || grade === 'C' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {grade}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">Server Analysis Score</p>
                        </div>
                        
                        {/* Show server analysis breakdown if available */}
                        {seoScoring?.metrics?.technicalSEO && (
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">
                              {Math.round(seoScoring.metrics.technicalSEO)}/100
                            </div>
                            <p className="text-sm text-muted-foreground">Technical SEO</p>
                          </div>
                        )}
                        
                        {seoScoring?.metrics?.contentQuality && (
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">
                              {Math.round(seoScoring.metrics.contentQuality)}/100
                            </div>
                            <p className="text-sm text-muted-foreground">Content Quality</p>
                          </div>
                        )}
                        
                        {seoScoring?.metrics?.performance && (
                          <div className="text-center">
                            <div className="text-2xl font-bold text-orange-600">
                              {Math.round(seoScoring.metrics.performance)}/100
                            </div>
                            <p className="text-sm text-muted-foreground">Performance</p>
                          </div>
                        )}
                      </div>
                      
                      {/* Show analysis source */}
                      <div className="mt-4 text-center">
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                          ✅ Server Analysis Results
                        </span>
                      </div>
                    </div>
                  );
                }
                
                // Fallback to basic client-side data only if no server analysis data available
                return null;
              })()}

              {/* HTML Issues Summary - Server analysis data */}
              {(() => {
                const htmlAnalysis = parseJSONObject(audit?.html_analysis); // Correct field name
                
                if (htmlAnalysis?.totalIssues) {
                  const totalIssues = htmlAnalysis.totalIssues || 0;
                  const criticalCount = htmlAnalysis.criticalIssues?.length || 0;
                  const warningCount = htmlAnalysis.warnings?.length || 0;
                  const infoCount = htmlAnalysis.info?.length || 0;
                  
                  return (
                    <div className="bg-card rounded-lg border p-6">
                      <h3 className="text-lg font-semibold mb-4">🔍 Server HTML Issue Analysis</h3>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-gray-600">{totalIssues}</div>
                          <p className="text-sm text-muted-foreground">Total Issues Found</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-red-600">
                            {criticalCount}
                          </div>
                          <p className="text-sm text-muted-foreground">Critical</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-600">
                            {warningCount}
                          </div>
                          <p className="text-sm text-muted-foreground">Warnings</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {infoCount}
                          </div>
                          <p className="text-sm text-muted-foreground">Info</p>
                        </div>
                      </div>
                      
                      <div className="mt-4 text-center">
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                          🔍 Server HTML Analysis (JSDOM Parser)
                        </span>
                      </div>
                    </div>
                  );
                }
                return null;
              })()}
            </div>
          )}

          {activeTab === 'technical' && (
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Technical SEO Analysis</h3>
                <button
                  onClick={() => {
                    if (audit?.technical_audit_raw) {
                      navigator.clipboard.writeText(audit.technical_audit_raw);
                    }
                  }}
                  className="text-xs bg-muted text-muted-foreground px-3 py-1 rounded hover:bg-muted/80"
                >
                  Copy Raw Data
                </button>
              </div>
              {renderTechnicalAnalysis()}
            </div>
          )}

          {activeTab === 'content' && (
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Content Analysis</h3>
                <button
                  onClick={() => {
                    if (audit?.content_audit_raw) {
                      navigator.clipboard.writeText(audit.content_audit_raw);
                    }
                  }}
                  className="text-xs bg-muted text-muted-foreground px-3 py-1 rounded hover:bg-muted/80"
                >
                  Copy Raw Data
                </button>
              </div>
              {renderContentAnalysis()}
            </div>
          )}

          {activeTab === 'html-analysis' && (
            <div className="bg-card rounded-lg border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">🔍 HTML Analysis Results</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      if (audit?.scraped_content) {
                        const blob = new Blob([audit.scraped_content], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `scraped-html-${website?.name || 'website'}.html`;
                        a.click();
                        URL.revokeObjectURL(url);
                      }
                    }}
                    className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded hover:bg-blue-200"
                  >
                    📄 Download Source HTML
                  </button>
                  <button
                    onClick={() => {
                      if (audit?.html_analysis) {
                        navigator.clipboard.writeText(JSON.stringify(audit.html_analysis, null, 2));
                      }
                    }}
                    className="text-xs bg-muted text-muted-foreground px-3 py-1 rounded hover:bg-muted/80"
                  >
                    Copy Analysis Data
                  </button>
                </div>
              </div>
              
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-700">
                  <strong>📍 About Line Numbers:</strong> The line numbers below refer to specific lines in the HTML source code 
                  that was scraped from <strong>{website?.url}</strong>. You can download the source HTML file above to see the exact content.
                </p>
              </div>
              
              {audit.html_issues && Array.isArray(audit.html_issues) && audit.html_issues.length > 0 ? (
                <div className="space-y-4">
                  {/* Group issues by severity */}
                  {['critical', 'warning', 'info'].map(severity => {
                    const severityIssues = audit.html_issues?.filter((issue: any) => issue.severity === severity) || [];
                    if (severityIssues.length === 0) return null;

                    return (
                      <div key={severity}>
                        <h4 className={`text-lg font-medium mb-3 ${
                          severity === 'critical' ? 'text-red-700' :
                          severity === 'warning' ? 'text-yellow-700' : 'text-blue-700'
                        }`}>
                          {severity === 'critical' ? '🚨' : severity === 'warning' ? '⚠️' : 'ℹ️'} 
                          {' '}{severity.charAt(0).toUpperCase() + severity.slice(1)} Issues ({severityIssues.length})
                        </h4>
                        <div className="space-y-3">
                          {severityIssues.map((issue: any, index: number) => (
                            <div key={index} className={`border rounded-lg p-4 ${
                              severity === 'critical' ? 'border-red-200 bg-red-50' :
                              severity === 'warning' ? 'border-yellow-200 bg-yellow-50' : 'border-blue-200 bg-blue-50'
                            }`}>
                              <div className="flex items-start justify-between mb-2">
                                <h5 className={`font-medium ${
                                  severity === 'critical' ? 'text-red-800' :
                                  severity === 'warning' ? 'text-yellow-800' : 'text-blue-800'
                                }`}>
                                  {issue.issue}
                                </h5>
                                <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                  Line {Math.floor(issue.lineNumber || 1)}:{Math.floor(issue.columnNumber || 1)}
                                </div>
                              </div>
                              <p className={`text-sm mb-2 ${
                                severity === 'critical' ? 'text-red-700' :
                                severity === 'warning' ? 'text-yellow-700' : 'text-blue-700'
                              }`}>
                                <strong>Impact:</strong> {issue.impact}
                              </p>
                              <p className={`text-sm mb-3 ${
                                severity === 'critical' ? 'text-red-600' :
                                severity === 'warning' ? 'text-yellow-600' : 'text-blue-600'
                              }`}>
                                <strong>Recommendation:</strong> {issue.recommendation}
                              </p>
                              
                              {/* Element Details */}
                              <div className="space-y-2">
                                <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                                  <strong>CSS Selector:</strong> <code>{issue.cssSelector}</code>
                                </div>
                                {issue.xpath && (
                                  <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                                    <strong>XPath:</strong> <code>{issue.xpath}</code>
                                  </div>
                                )}
                                {issue.context && issue.context.current && (
                                  <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                                    <strong>HTML Context:</strong>
                                    <pre className="mt-1 whitespace-pre-wrap break-all">{issue.context.current.substring(0, 200)}{issue.context.current.length > 200 ? '...' : ''}</pre>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-muted-foreground">No HTML analysis issues found or data not available</p>
              )}
            </div>
          )}

          {activeTab === 'seo-scoring' && (
            <div className="space-y-6">
              {/* Server-side SEO Analysis Results - Read from CORRECT fields */}
              {(() => {
                const htmlAnalysis = parseJSONObject(audit?.html_analysis); // HTML analysis data
                const seoScoring = parseJSONObject(audit?.seo_metrics);     // SEO scoring data
                
                if (seoScoring || htmlAnalysis) {
                  return (
                    <div className="bg-card rounded-lg border p-6">
                      <h3 className="text-lg font-semibold mb-4">📊 Server-side SEO Analysis Report</h3>
                      
                      {/* Overall Score Section */}
                      <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <h4 className="font-medium text-green-800 mb-3">🎯 Analysis Summary</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="text-3xl font-bold text-green-600">
                              {Math.round(seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0)}/100
                            </div>
                            <p className="text-sm text-green-700">Overall SEO Score</p>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-gray-600">
                              {htmlAnalysis?.totalIssues || 0}
                            </div>
                            <p className="text-sm text-gray-700">Issues Found</p>
                          </div>
                          <div className="text-center">
                            <div className={`text-2xl font-bold px-3 py-1 rounded ${
                              (seoScoring?.grade || 'F') === 'A+' || (seoScoring?.grade || 'F') === 'A' ? 'bg-green-100 text-green-800' :
                              (seoScoring?.grade || 'F') === 'B+' || (seoScoring?.grade || 'F') === 'B' ? 'bg-blue-100 text-blue-800' :
                              (seoScoring?.grade || 'F') === 'C+' || (seoScoring?.grade || 'F') === 'C' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {seoScoring?.grade || 
                                ((seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0) >= 90 ? 'A+' :
                                 (seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0) >= 80 ? 'A' :
                                 (seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0) >= 70 ? 'B' :
                                 (seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0) >= 60 ? 'C' :
                                 (seoScoring?.overallScore || htmlAnalysis?.summary?.seoScore || 0) >= 50 ? 'D' : 'F')}
                            </div>
                            <p className="text-sm text-gray-700">Letter Grade</p>
                          </div>
                        </div>
                        
                        {/* Show what actually generated this score */}
                        <div className="mt-4 text-center">
                          {(() => {
                            const activeSources = [];
                            if (seoScoring?.providerAnalysis?.lighthouse) {
                              activeSources.push('Google Lighthouse');
                            }
                            if (seoScoring?.metrics?.contentQuality > 0) {
                              activeSources.push('HTML Content Analysis');
                            }
                            if (htmlAnalysis?.totalIssues > 0) {
                              activeSources.push('Server HTML Parser');
                            }
                            
                            const sourceText = activeSources.length > 0 
                              ? `Generated by: ${activeSources.join(' + ')}`
                              : 'Generated by Server-side Analysis';
                              
                            return (
                              <span className="text-xs bg-green-100 text-green-700 px-3 py-1 rounded">
                                ✅ {sourceText}
                              </span>
                            );
                          })()}
                        </div>
                      </div>

                      {/* Detailed Breakdown - Server-side metrics */}
                      {seoScoring?.metrics && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                          {/* Technical SEO Score */}
                          <div className="border rounded-lg p-4 bg-blue-50">
                            <h4 className="font-medium text-blue-700 mb-3">🔧 Technical SEO</h4>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-blue-600 mb-2">
                                {Math.round(seoScoring.metrics.technicalSEO || 0)}/100
                              </div>
                              <div className="text-xs text-blue-600">
                                Server Analysis
                              </div>
                            </div>
                          </div>

                          {/* Content Quality Score */}
                          <div className="border rounded-lg p-4 bg-purple-50">
                            <h4 className="font-medium text-purple-700 mb-3">📝 Content Quality</h4>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-purple-600 mb-2">
                                {Math.round(seoScoring.metrics.contentQuality || 0)}/100
                              </div>
                              <div className="text-xs text-purple-600">
                                Content Analysis
                              </div>
                            </div>
                          </div>

                          {/* User Experience Score */}
                          <div className="border rounded-lg p-4 bg-indigo-50">
                            <h4 className="font-medium text-indigo-700 mb-3">👤 User Experience</h4>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-indigo-600 mb-2">
                                {Math.round(seoScoring.metrics.userExperience || 0)}/100
                              </div>
                              <div className="text-xs text-indigo-600">
                                UX Analysis
                              </div>
                            </div>
                          </div>

                          {/* Performance Score */}
                          <div className="border rounded-lg p-4 bg-orange-50">
                            <h4 className="font-medium text-orange-700 mb-3">⚡ Performance</h4>
                            <div className="text-center">
                              <div className="text-2xl font-bold text-orange-600 mb-2">
                                {Math.round(seoScoring.metrics.performance || 0)}/100
                              </div>
                              <div className="text-xs text-orange-600">
                                Speed Analysis
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Provider Analysis Breakdown - Show actual working providers */}
                      {seoScoring?.providerAnalysis && (
                        <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-3">🏢 Analysis Engines Used</h4>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {/* Google Lighthouse (PageSpeed Insights API) */}
                            {seoScoring.providerAnalysis.lighthouse && (
                              <div className="text-center p-3 bg-white rounded border border-green-200">
                                <div className="text-lg font-bold text-green-600">✅ Google PageSpeed Insights</div>
                                <div className="text-sm text-gray-600 mt-1">
                                  Performance: {Math.round((seoScoring.providerAnalysis.lighthouse.performance || 0) * 100)}%
                                </div>
                                <div className="text-sm text-gray-600">
                                  SEO: {Math.round((seoScoring.providerAnalysis.lighthouse.seo || 0) * 100)}%
                                </div>
                                <div className="text-xs text-green-600 mt-1">Lighthouse API Active</div>
                              </div>
                            )}
                            
                            {/* Semrush - only if has actual data */}
                            {seoScoring.providerAnalysis.semrush && 
                             seoScoring.providerAnalysis.semrush.status !== 'configured' && 
                             seoScoring.providerAnalysis.semrush.data && (
                              <div className="text-center p-3 bg-white rounded border border-green-200">
                                <div className="text-lg font-bold text-green-600">✅ Semrush</div>
                                <div className="text-sm text-gray-600 mt-1">
                                  {seoScoring.providerAnalysis.semrush.data}
                                </div>
                                <div className="text-xs text-green-600 mt-1">API Active</div>
                              </div>
                            )}
                            
                            {/* Ahrefs - only if has actual data */}
                            {seoScoring.providerAnalysis.ahrefs && 
                             seoScoring.providerAnalysis.ahrefs.status !== 'configured' && 
                             seoScoring.providerAnalysis.ahrefs.data && (
                              <div className="text-center p-3 bg-white rounded border border-green-200">
                                <div className="text-lg font-bold text-green-600">✅ Ahrefs</div>
                                <div className="text-sm text-gray-600 mt-1">
                                  {seoScoring.providerAnalysis.ahrefs.data}
                                </div>
                                <div className="text-xs text-green-600 mt-1">API Active</div>
                              </div>
                            )}
                            
                            {/* HTML Content Analysis Engine */}
                            {seoScoring.metrics?.contentQuality > 0 && (
                              <div className="text-center p-3 bg-white rounded border border-blue-200">
                                <div className="text-lg font-bold text-blue-600">✅ HTML Content Analysis</div>
                                <div className="text-sm text-gray-600 mt-1">
                                  Content Quality: {Math.round(seoScoring.metrics.contentQuality)}%
                                </div>
                                <div className="text-xs text-blue-600 mt-1">JSDOM Parser Active</div>
                              </div>
                            )}
                          </div>
                          
                          {/* Show inactive engines only if they exist but have no data */}
                          {(() => {
                            const inactiveEngines = [];
                            
                            // Check if Lighthouse is configured but has no data
                            if (!seoScoring.providerAnalysis.lighthouse) {
                              inactiveEngines.push('❌ Google PageSpeed Insights (API key needed)');
                            }
                            
                            // Check if Semrush is configured but has no real data
                            if (!seoScoring.providerAnalysis.semrush || 
                                (seoScoring.providerAnalysis.semrush.status === 'configured' && !seoScoring.providerAnalysis.semrush.data)) {
                              inactiveEngines.push('❌ Semrush (API key needed)');
                            }
                            
                            // Check if Ahrefs is configured but has no real data
                            if (!seoScoring.providerAnalysis.ahrefs || 
                                (seoScoring.providerAnalysis.ahrefs.status === 'configured' && !seoScoring.providerAnalysis.ahrefs.data)) {
                              inactiveEngines.push('❌ Ahrefs (API key needed)');
                            }
                            
                            if (inactiveEngines.length > 0) {
                              return (
                                <div className="mt-4 pt-3 border-t border-gray-200">
                                  <div className="text-sm text-gray-500 text-center">
                                    <span className="font-medium">Inactive Engines:</span>
                                    {inactiveEngines.map((engine, index) => (
                                      <span key={index} className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                        {engine}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })()}

              {/* Server-side Recommendations */}
              {(() => {
                const seoScoring = parseJSONObject(audit?.seo_metrics);
                
                if (seoScoring?.recommendations && Array.isArray(seoScoring.recommendations) && seoScoring.recommendations.length > 0) {
                  return (
                    <div className="bg-card rounded-lg border p-6">
                      <h3 className="text-lg font-semibold mb-4">💡 Server-side SEO Recommendations</h3>
                      <div className="space-y-3">
                        {seoScoring.recommendations.map((rec: string, index: number) => (
                          <div key={index} className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <span className="text-xs font-medium px-2 py-1 rounded bg-blue-100 text-blue-700">
                                  Server Analysis
                                </span>
                                <h5 className="font-medium mt-2 text-blue-800">{rec}</h5>
                              </div>
                              <span className="px-2 py-1 rounded-full text-xs font-medium border bg-blue-100 text-blue-700 border-blue-200">
                                RECOMMENDED
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                }
                return null;
              })()}

              {/* Processing Information */}
              {(() => {
                const seoScoring = parseJSONObject(audit?.seo_metrics);
                const htmlAnalysis = parseJSONObject(audit?.html_analysis);
                
                if (seoScoring?.processingTime || htmlAnalysis?.performanceMetrics) {
                  return (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-800 mb-3">ℹ️ Analysis Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        {seoScoring?.processingTime && (
                          <div>
                            <span className="font-medium">SEO Scoring Time:</span>
                            <div className="text-gray-600">{Math.round(seoScoring.processingTime)}ms</div>
                          </div>
                        )}
                        {htmlAnalysis?.performanceMetrics?.analysisTime && (
                          <div>
                            <span className="font-medium">HTML Analysis Time:</span>
                            <div className="text-gray-600">{Math.round(htmlAnalysis.performanceMetrics.analysisTime)}ms</div>
                          </div>
                        )}
                        {htmlAnalysis?.performanceMetrics?.elementCount && (
                          <div>
                            <span className="font-medium">HTML Elements:</span>
                            <div className="text-gray-600">{htmlAnalysis.performanceMetrics.elementCount.toLocaleString()}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                }
                return null;
              })()}

              {/* Fallback message if no server-side data */}
              {(() => {
                const seoScoring = parseJSONObject(audit?.seo_metrics);
                const htmlAnalysis = parseJSONObject(audit?.html_analysis);
                
                if (!seoScoring && !htmlAnalysis) {
                  return (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                      <h3 className="text-lg font-medium text-yellow-800 mb-2">⚠️ Server-side Analysis Not Available</h3>
                      <p className="text-yellow-700">
                        Server-side SEO analysis data is not available for this audit. 
                        This may occur with older audits or if the server-side analysis failed.
                      </p>
                    </div>
                  );
                }
                return null;
              })()}
            </div>
          )}

          {activeTab === 'report' && (
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">Complete SEO Audit Report</h3>
              {audit.report_html ? (
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: audit.report_html }}
                />
              ) : audit.combined_report ? (
                <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded overflow-auto">
                  {audit.combined_report}
                </pre>
              ) : (
                <p className="text-muted-foreground">Report not available</p>
              )}
            </div>
          )}
        </div>
        </div>
      </div>
    </PSEOLayout>
  );
};

export default AuditResults; 