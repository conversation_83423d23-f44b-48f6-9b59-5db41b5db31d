"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragOverEvent,
  rectIntersection,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "../../../../../base/components/ui/button";
import { Card, CardContent } from "../../../../../base/components/ui/card";
import { Badge } from "../../../../../base/components/ui/badge";
import { Input } from "../../../../../base/components/ui/input";
import { Textarea } from "../../../../../base/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import {
  Plus,
  Trash2,
  Edit3,
  Save,
  Type,
  Heading1,
  Heading2,
  Heading3,
  List,
  Image as ImageIcon,
  GripVertical,
  Copy,
  Settings,
  Upload,
  Columns,
  Columns2,
  Columns3,
  Layout,
  ArrowRight,
  ChevronDown,
  X,
  Move,
  Sparkles,
  Grid,
} from "lucide-react";
import { useToast } from "../../../../../base/hooks/use-toast";

// Enhanced Block Interface
export interface EnhancedBlock {
  id: string;
  type:
    | "text"
    | "heading"
    | "list"
    | "image"
    | "columns"
    | "spacer"
    | "divider";
  content: any;
  style?: {
    backgroundColor?: string;
    textColor?: string;
    padding?: string;
    margin?: string;
    textAlign?: "left" | "center" | "right";
    fontSize?: string;
    fontWeight?: string;
  };
  layout?: {
    columns?: number;
    children?: { [key: string]: EnhancedBlock[] };
    columnWidths?: string[];
  };
}

interface EnhancedDragDropEditorProps {
  sections: any[];
  onUpdate: (sections: any[]) => void;
  readonly?: boolean;
}

// Enhanced Sortable Block Component
function EnhancedSortableBlock({
  block,
  sectionId,
  onUpdate,
  onDelete,
  onDuplicate,
  readonly = false,
  isInColumn = false,
  columnId,
}: {
  block: EnhancedBlock;
  sectionId: string;
  onUpdate: (blockId: string, updates: Partial<EnhancedBlock>) => void;
  onDelete: (blockId: string) => void;
  onDuplicate: (blockId: string) => void;
  readonly?: boolean;
  isInColumn?: boolean;
  columnId?: string;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: block.id,
    data: {
      type: "block",
      blockId: block.id,
      isInColumn,
      columnId,
      sectionId,
    },
  });

  const [isEditing, setIsEditing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [localContent, setLocalContent] = useState(block.content || "");
  const [isSaving, setIsSaving] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.3 : 1,
    zIndex: isDragging ? 1000 : "auto",
    cursor: isDragging ? "grabbing" : "grab",
  };

  // Sync local content with block content
  useEffect(() => {
    if (!isEditing && block.content !== localContent) {
      setLocalContent(block.content || "");
    }
  }, [block.content, isEditing]);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${Math.max(120, textareaRef.current.scrollHeight)}px`;
    }
  }, []);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      adjustTextareaHeight();
    }
  }, [isEditing, adjustTextareaHeight]);

  // Handle content save with validation
  const handleSave = async () => {
    if (isSaving) return;

    try {
      setIsSaving(true);

      // Validate content based on block type
      let validatedContent = localContent;
      if (block.type === "heading" && !validatedContent.trim()) {
        validatedContent = "New Heading";
      } else if (block.type === "text" && !validatedContent.trim()) {
        validatedContent = "Enter your text here...";
      }

      await onUpdate(block.id, { content: validatedContent });
      setIsEditing(false);

      toast({
        title: "Saved",
        description: "Content has been saved successfully.",
        duration: 1500,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setLocalContent(block.content || "");
    setIsEditing(false);
  };

  // Enhanced image upload with validation
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type and size
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Invalid File",
        description: "Please select an image file.",
        variant: "destructive",
      });
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      // 5MB limit
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 5MB.",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const imageData = event.target?.result as string;
      onUpdate(block.id, { content: imageData });
      toast({
        title: "Image Uploaded",
        description: "Image has been added successfully.",
      });
    };
    reader.readAsDataURL(file);
  };

  // Render content based on block type with enhanced UI
  const renderContent = () => {
    const content = localContent || block.content || "";

    switch (block.type) {
      case "heading":
        const level =
          block.style?.fontSize === "text-3xl"
            ? 1
            : block.style?.fontSize === "text-2xl"
              ? 2
              : 3;

        if (isEditing) {
          return (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Select
                  value={level.toString()}
                  onValueChange={(value) => {
                    const newLevel = parseInt(value);
                    const fontSize =
                      newLevel === 1
                        ? "text-3xl"
                        : newLevel === 2
                          ? "text-2xl"
                          : "text-xl";
                    onUpdate(block.id, {
                      style: { ...block.style, fontSize },
                    });
                  }}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">H1</SelectItem>
                    <SelectItem value="2">H2</SelectItem>
                    <SelectItem value="3">H3</SelectItem>
                  </SelectContent>
                </Select>
                <Badge variant="outline">Heading</Badge>
              </div>
              <Input
                value={localContent}
                onChange={(e) => setLocalContent(e.target.value)}
                placeholder="Enter heading text..."
                className="text-lg font-semibold"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSave();
                  }
                }}
              />
            </div>
          );
        }

        return (
          <div
            className={`font-bold ${
              level === 1 ? "text-3xl" : level === 2 ? "text-2xl" : "text-xl"
            } text-gray-800`}
          >
            {content || "New Heading"}
          </div>
        );

      case "text":
        if (isEditing) {
          return (
            <div className="space-y-3">
              <Badge variant="outline">Paragraph</Badge>
              <Textarea
                ref={textareaRef}
                value={localContent}
                onChange={(e) => {
                  setLocalContent(e.target.value);
                  adjustTextareaHeight();
                }}
                placeholder="Enter your text here..."
                className="min-h-[120px] resize-none"
                onInput={adjustTextareaHeight}
              />
            </div>
          );
        }

        return (
          <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {content || "Enter your text here..."}
          </div>
        );

      case "list":
        const items = Array.isArray(block.content)
          ? block.content
          : typeof block.content === "string"
            ? block.content.split("\n").filter(Boolean)
            : ["New list item"];

        if (isEditing) {
          return (
            <div className="space-y-3">
              <Badge variant="outline">List</Badge>
              {items.map((item: string, index: number) => (
                <div key={index} className="flex items-center gap-2">
                  <span className="text-gray-500">•</span>
                  <Input
                    value={item}
                    onChange={(e) => {
                      const newItems = [...items];
                      newItems[index] = e.target.value;
                      setLocalContent(newItems);
                    }}
                    placeholder="List item..."
                    className="flex-1"
                  />
                  {items.length > 1 && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        const newItems = items.filter(
                          (_: any, i: number) => i !== index
                        );
                        setLocalContent(newItems);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                size="sm"
                variant="outline"
                onClick={() => setLocalContent([...items, ""])}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          );
        }

        return (
          <ul className="space-y-2">
            {items.map((item: string, index: number) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-gray-500 mt-1">•</span>
                <span className="text-gray-700">{item || "List item"}</span>
              </li>
            ))}
          </ul>
        );

      case "image":
        const isValidImage =
          content &&
          (content.startsWith("data:image") ||
            content.startsWith("http") ||
            content.startsWith("https"));

        if (isEditing || !isValidImage) {
          return (
            <div className="space-y-3">
              <Badge variant="outline">Image</Badge>
              {isValidImage ? (
                <div className="relative group">
                  <img
                    src={content}
                    alt="Preview"
                    className="max-w-full h-auto rounded-lg border"
                    style={{ maxHeight: isInColumn ? "200px" : "400px" }}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                    <Button
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Change Image
                    </Button>
                  </div>
                </div>
              ) : (
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <ImageIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">Click to upload an image</p>
                  <p className="text-sm text-gray-400 mt-1">
                    PNG, JPG, GIF up to 5MB
                  </p>
                </div>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>
          );
        }

        return (
          <div className="text-center">
            <img
              src={content}
              alt="Content image"
              className="max-w-full h-auto rounded-lg shadow-sm border mx-auto"
              style={{ maxHeight: isInColumn ? "200px" : "400px" }}
              onError={(e) => {
                console.error("Failed to load image:", content);
                e.currentTarget.style.display = "none";
              }}
            />
          </div>
        );

      default:
        return (
          <div className="text-gray-500 italic">
            Unknown block type: {block.type}
          </div>
        );
    }
  };

  // Prevent event propagation on interactive elements
  const stopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (readonly) {
    return (
      <div ref={setNodeRef} className="mb-4">
        <Card className="border-transparent shadow-none">
          <CardContent className="p-4">{renderContent()}</CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div ref={setNodeRef} style={style} className="mb-4 group">
      <Card
        className={`transition-all duration-200 ${
          isDragging
            ? "shadow-lg ring-2 ring-blue-200"
            : "hover:shadow-md hover:ring-1 hover:ring-gray-200"
        }`}
      >
        <CardContent className="p-4">
          {/* Block Header with Controls */}
          <div className="flex items-center justify-between mb-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex items-center gap-2">
              <div
                {...attributes}
                {...listeners}
                className="cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100"
                onMouseDown={stopPropagation}
              >
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
              <Badge variant="secondary" className="text-xs">
                {block.type.toUpperCase()}
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              {!isEditing && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsEditing(true)}
                  onMouseDown={stopPropagation}
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
              )}

              <Button
                size="sm"
                variant="ghost"
                onClick={() => onDuplicate(block.id)}
                onMouseDown={stopPropagation}
              >
                <Copy className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowSettings(!showSettings)}
                onMouseDown={stopPropagation}
              >
                <Settings className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="ghost"
                onClick={() => onDelete(block.id)}
                onMouseDown={stopPropagation}
                className="text-red-500 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Block Content */}
          {renderContent()}

          {/* Edit Controls */}
          {isEditing && (
            <div className="flex items-center justify-end gap-2 mt-4 pt-3 border-t">
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancel}
                onMouseDown={stopPropagation}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleSave}
                onMouseDown={stopPropagation}
                disabled={isSaving}
              >
                {isSaving ? (
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-1" />
                ) : (
                  <Save className="h-4 w-4 mr-1" />
                )}
                Save
              </Button>
            </div>
          )}

          {/* Settings Panel */}
          {showSettings && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg border-t">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Text Alignment
                  </label>
                  <Select
                    value={block.style?.textAlign || "left"}
                    onValueChange={(value) => {
                      onUpdate(block.id, {
                        style: { ...block.style, textAlign: value as any },
                      });
                    }}
                  >
                    <SelectTrigger className="w-full mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="center">Center</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Background Color
                  </label>
                  <Input
                    type="color"
                    value={block.style?.backgroundColor || "#ffffff"}
                    onChange={(e) => {
                      onUpdate(block.id, {
                        style: {
                          ...block.style,
                          backgroundColor: e.target.value,
                        },
                      });
                    }}
                    className="w-full mt-1 h-8"
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export { EnhancedSortableBlock };
