import express from 'express';
import { getEnabledBackendPlugins, getBackendPluginConfig } from './registry';

export interface BackendPlugin {
  router: express.Router;
  config: {
    name: string;
    version: string;
    apiPrefix: string;
  };
  middleware?: express.RequestHandler[];
  initialize?: () => Promise<void> | void;
  cleanup?: () => Promise<void> | void;
  healthCheck?: () => Promise<boolean> | boolean;
}

export class PluginLoader {
  private static loadedPlugins: Map<string, BackendPlugin> = new Map();

  static async loadPlugins(app: express.Application): Promise<void> {
    const enabledPlugins = getEnabledBackendPlugins();
    
    console.log(`🚀 Loading ${enabledPlugins.length} backend plugins:`, enabledPlugins);

    for (const pluginName of enabledPlugins) {
      try {
        await this.mountPlugin(app, pluginName);
      } catch (error) {
        console.error(`❌ Failed to load backend plugin: ${pluginName}`, error);
      }
    }

    console.log(`✅ Backend plugin loading complete. Loaded: ${this.loadedPlugins.size} plugins`);
  }

  private static async mountPlugin(app: express.Application, pluginName: string): Promise<void> {
    try {
      console.log(`🔄 Loading backend plugin: ${pluginName}`);
      
      const pluginConfig = getBackendPluginConfig(pluginName);
      if (!pluginConfig) {
        throw new Error(`No configuration found for plugin: ${pluginName}`);
      }

      // Try to import the plugin from features directory
      let pluginModule: BackendPlugin;
      
      try {
        // Try to import from features/{pluginName}/backend/index.ts
        const moduleImport = await import(`../features/${pluginName}/backend/index.ts`);
        pluginModule = moduleImport.default || moduleImport;
      } catch (importError) {
        // Fallback: try to import from features/{pluginName}/routes.ts
        try {
          const routesImport = await import(`../features/${pluginName}/routes.ts`);
          
          // Create a plugin wrapper for backward compatibility
          pluginModule = {
            router: routesImport.default || routesImport,
            config: {
              name: pluginConfig.name,
              version: pluginConfig.version,
              apiPrefix: pluginConfig.apiPrefix
            }
          };
        } catch (fallbackError) {
          console.error(`Import attempts failed for ${pluginName}:`, { importError, fallbackError });
          throw new Error(`Could not import plugin from any location: ${pluginName}`);
        }
      }

      if (!pluginModule.router) {
        throw new Error(`Plugin ${pluginName} does not export a router`);
      }

      // Run plugin initialization if available
      if (pluginModule.initialize) {
        console.log(`🔧 Initializing plugin: ${pluginName}`);
        await pluginModule.initialize();
      }

      // Apply plugin-specific middleware if available
      if (pluginModule.middleware && Array.isArray(pluginModule.middleware)) {
        pluginModule.middleware.forEach(middleware => {
          app.use(pluginConfig.apiPrefix, middleware);
        });
      }

      // Mount the plugin router
      app.use(pluginConfig.apiPrefix, pluginModule.router);
      
      // Store the loaded plugin
      this.loadedPlugins.set(pluginName, pluginModule);
      
      console.log(`✅ Mounted backend plugin: ${pluginName} at ${pluginConfig.apiPrefix}`);
      
    } catch (error) {
      console.error(`❌ Error mounting backend plugin ${pluginName}:`, error);
      throw error;
    }
  }

  static getLoadedPlugins(): Map<string, BackendPlugin> {
    return this.loadedPlugins;
  }

  static getPlugin(pluginName: string): BackendPlugin | undefined {
    return this.loadedPlugins.get(pluginName);
  }

  static async healthCheck(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const [pluginName, plugin] of this.loadedPlugins) {
      try {
        if (plugin.healthCheck) {
          results[pluginName] = await plugin.healthCheck();
        } else {
          results[pluginName] = true; // Assume healthy if no health check
        }
      } catch (error) {
        console.error(`Health check failed for plugin ${pluginName}:`, error);
        results[pluginName] = false;
      }
    }
    
    return results;
  }

  static async unloadPlugin(pluginName: string): Promise<boolean> {
    const plugin = this.loadedPlugins.get(pluginName);
    
    if (!plugin) {
      return false;
    }
    
    try {
      // Run cleanup if available
      if (plugin.cleanup) {
        await plugin.cleanup();
      }
      
      this.loadedPlugins.delete(pluginName);
      console.log(`🗑️ Unloaded plugin: ${pluginName}`);
      return true;
    } catch (error) {
      console.error(`❌ Error unloading plugin ${pluginName}:`, error);
      return false;
    }
  }
} 