aiPrompts: {
  style: {
    tone: "technical",
    formality: "formal",
    perspective: "third-person",
    audience: "technical-team"
  },
  
  focus: {
    primaryFocus: "Seamless integration with legacy systems while modernizing architecture",
    secondaryFocus: ["Data migration", "API standardization", "Security hardening"],
    industryContext: "Large enterprise with 20+ year old systems requiring careful modernization",
    businessPriorities: ["Minimal downtime", "Data integrity", "Future flexibility"],
    technicalEmphasis: ["API-first architecture", "Event-driven design", "Microservices"]
  },
  
  structure: {
    executiveSummaryStyle: "brief",
    sectionDepth: "comprehensive", 
    includeExamples: true,
    includeMetrics: true,
    includeDiagrams: true
  },
  
  global: {
    mustInclude: [
      "Legacy system compatibility matrix",
      "Data migration strategy with rollback plans",
      "API versioning and backward compatibility",
      "Phased implementation approach",
      "Integration testing strategy"
    ],
    mustAvoid: [
      "Big bang migrations",
      "Breaking changes to existing APIs",
      "Vendor lock-in solutions",
      "Single points of failure"
    ],
    keyTerminology: [
      "Enterprise Service Bus (ESB)",
      "Extract, Transform, Load (ETL)",
      "Application Programming Interface (API)",
      "Service-Oriented Architecture (SOA)"
    ],
    complianceRequirements: ["SOX", "Internal audit requirements"],
    additionalInstructions: "Emphasize risk mitigation and phased approach. Include detailed technical architecture diagrams. Focus on maintainability and long-term sustainability."
  }
}


aiPrompts: {
  style: {
    tone: "professional",
    formality: "semi-formal",
    perspective: "consultative", 
    audience: "mixed"
  },
  
  focus: {
    primaryFocus: "High-performance e-commerce with seamless user experience",
    secondaryFocus: ["Mobile optimization", "Payment security", "Inventory management"],
    industryContext: "Competitive e-commerce market requiring fast, secure, and scalable solutions",
    businessPriorities: ["Revenue growth", "Customer retention", "Operational efficiency"],
    technicalEmphasis: ["Sub-second page loads", "99.9% uptime", "PCI DSS compliance"]
  },
  
  structure: {
    executiveSummaryStyle: "strategic",
    sectionDepth: "detailed",
    includeExamples: true,
    includeMetrics: true,
    includeDiagrams: false
  },
  
  global: {
    mustInclude: [
      "Mobile-first responsive design",
      "Payment gateway integration (Stripe/PayPal)",
      "Real-time inventory tracking",
      "SEO optimization strategy",
      "Analytics and conversion tracking"
    ],
    mustAvoid: [
      "Single points of failure",
      "Slow database queries",
      "Insecure payment processing",
      "Poor mobile experience"
    ],
    keyTerminology: [
      "Conversion rate optimization",
      "Shopping cart abandonment",
      "Payment Card Industry (PCI)",
      "Search engine optimization (SEO)"
    ],
    complianceRequirements: ["PCI DSS", "GDPR", "CCPA"],
    additionalInstructions: "Focus on ROI and business impact. Include performance benchmarks and competitive analysis. Emphasize scalability for Black Friday traffic spikes."
  }
}

aiPrompts: {
  style: {
    tone: "professional",
    formality: "formal", 
    perspective: "consultative",
    audience: "executives"
  },
  
  focus: {
    primaryFocus: "HIPAA compliance and patient data security",
    secondaryFocus: ["Scalability for 10,000+ patients", "Integration with existing EHR"],
    industryContext: "Healthcare regulations require strict data protection and audit trails",
    businessPriorities: ["Regulatory compliance", "Patient safety", "Operational efficiency"],
    technicalEmphasis: ["End-to-end encryption", "Role-based access control", "Audit logging"]
  },
  
  structure: {
    executiveSummaryStyle: "strategic",
    sectionDepth: "comprehensive",
    includeExamples: true,
    includeMetrics: true,
    includeDiagrams: true
  },
  
  global: {
    mustInclude: [
      "HIPAA compliance checklist",
      "Data breach prevention measures", 
      "Patient consent management",
      "Integration with Epic/Cerner systems"
    ],
    mustAvoid: [
      "Storing PHI in unsecured locations",
      "Third-party analytics without BAA",
      "Hardcoded credentials"
    ],
    keyTerminology: [
      "Protected Health Information (PHI)",
      "Business Associate Agreement (BAA)",
      "Electronic Health Record (EHR)",
      "Patient portal"
    ],
    complianceRequirements: ["HIPAA", "HITECH", "State privacy laws"],
    additionalInstructions: "Emphasize how each technical decision supports patient care quality and regulatory compliance. Include specific examples from similar healthcare implementations."
  }
}