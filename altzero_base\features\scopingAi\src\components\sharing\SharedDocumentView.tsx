import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { But<PERSON> } from "../../../../base/components/ui/button";
import { Input } from "../../../../base/components/ui/input";
import { Label } from "../../../../base/components/ui/label";
import { Alert, AlertDescription } from "../../../../base/components/ui/alert";
import { Loader2, Lock, FileText, AlertCircle, Eye } from "lucide-react";
import { supabase } from "../../lib/supabase";
import { verifyPassword } from "../../utils/cryptoUtils";
import { useToast } from "../../../../base/hooks/use-toast";
import { DocumentShareLink } from "../../types/sharing";

interface SharedDocumentViewProps {
  onAccessGranted?: (documentId: string, permission: string) => void;
}

export const SharedDocumentView: React.FC<SharedDocumentViewProps> = ({
  onAccessGranted,
}) => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [shareLink, setShareLink] = useState<DocumentShareLink | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [accessGranted, setAccessGranted] = useState(false);

  // Load share link details
  useEffect(() => {
    const loadShareLink = async () => {
      if (!token) {
        setError("Invalid share link");
        setIsLoading(false);
        return;
      }

      try {
        // Query unified document_shares table for link shares
        const { data, error } = await supabase
          .from("document_shares")
          .select(
            `
            *,
            document:documents(id, title, created_by, status)
          `
          )
          .eq("token", token)
          .eq("entity_type", "link")
          .eq("is_active", true)
          .single();

        if (error || !data) {
          setError("Share link not found or has been revoked");
          setIsLoading(false);
          return;
        }

        // Check if link has expired
        if (data.expires_at && new Date(data.expires_at) < new Date()) {
          setError("This share link has expired");
          setIsLoading(false);
          return;
        }

        // Check if max access count reached
        if (
          data.max_access_count &&
          data.access_count >= data.max_access_count
        ) {
          setError("This share link has reached its maximum usage limit");
          setIsLoading(false);
          return;
        }

        // Transform to expected format
        const shareData: DocumentShareLink = {
          id: data.id,
          documentId: data.document_id,
          token: data.token,
          permissionLevel: data.permission_level,
          createdBy: data.shared_by,
          expiresAt: data.expires_at,
          passwordHash: data.password_hash,
          isActive: data.is_active,
          accessCount: data.access_count,
          maxAccessCount: data.max_access_count,
          allowedDomains: data.allowed_domains || [],
          createdAt: data.shared_at,
          lastAccessedAt: data.last_accessed_at,
          document: data.document,
        };

        setShareLink(shareData);

        // If no password required, grant access immediately
        if (!data.password_hash) {
          await grantAccess(shareData);
        }

        setIsLoading(false);
      } catch (err) {
        console.error("Error loading share link:", err);
        setError("Failed to load share link");
        setIsLoading(false);
      }
    };

    loadShareLink();
  }, [token]);

  // Grant access to the document
  const grantAccess = async (linkData: DocumentShareLink) => {
    try {
      // Increment access count
      await supabase
        .from("document_shares")
        .update({
          access_count: linkData.accessCount + 1,
          last_accessed_at: new Date().toISOString(),
        })
        .eq("id", linkData.id);

      // Log activity if user is authenticated
      const user = await supabase.auth.getUser();
      if (user.data.user) {
        await supabase.from("document_interactions").insert({
          document_id: linkData.documentId,
          user_id: user.data.user.id,
          interaction_type: "activity",
          action: "view_via_link",
          metadata: { token, permission_level: linkData.permissionLevel },
        });
      }

      setAccessGranted(true);
      onAccessGranted?.(linkData.documentId, linkData.permissionLevel);

      // Redirect to document view
      navigate(
        `/documents/${linkData.documentId}?shared=true&permission=${linkData.permissionLevel}`
      );
    } catch (err) {
      console.error("Error granting access:", err);
      setError("Failed to access document");
    }
  };

  // Handle password verification
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!shareLink || !password.trim()) {
      toast({
        title: "Password required",
        description: "Please enter the password for this share link",
        variant: "destructive",
      });
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      // Verify password
      const isValid = await verifyPassword(password, shareLink.passwordHash!);

      if (isValid) {
        await grantAccess(shareLink);
        toast({
          title: "Access granted",
          description: "Password verified successfully",
        });
      } else {
        setError("Incorrect password");
        toast({
          title: "Access denied",
          description: "The password you entered is incorrect",
          variant: "destructive",
        });
      }
    } catch (err) {
      console.error("Error verifying password:", err);
      setError("Failed to verify password");
    } finally {
      setIsValidating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading share link...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={() => navigate("/")}
            >
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (accessGranted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <Eye className="h-8 w-8 text-green-600 mx-auto mb-4" />
            <p className="text-green-600 font-medium">Access Granted!</p>
            <p className="text-muted-foreground">Redirecting to document...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show password form if required
  if (shareLink?.passwordHash) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Password Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">
                    {shareLink.document?.title}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  This document is password protected. Enter the password to
                  access it.
                </p>
              </div>

              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="mt-1"
                    autoFocus
                  />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isValidating || !password.trim()}
                >
                  {isValidating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    "Access Document"
                  )}
                </Button>
              </form>

              <div className="text-center">
                <Button variant="ghost" size="sm" onClick={() => navigate("/")}>
                  Cancel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // This shouldn't happen, but just in case
  return null;
};
