import { API_URL, API_KEY, ENDPOINTS } from "./constants";

export interface Document {
  id: string;
  title: string;
  filename?: string;
  status: 'success' | 'error' | 'processing';
  chunk_count?: number;
  vector_count?: number;
  tags?: string[];
  error?: string;
  created_at?: string;
  [key: string]: any;
}

export interface Message {
  role: "user" | "assistant" | "system";
  content: string;
  sources?: Source[];
}

export interface Source {
  text: string;
  score: number;
  source?: string;
  document?: {
    id: string;
    title: string;
    filename: string;
    [key: string]: any;
  };
}

export interface ProposalRequest {
  clientName: string;
  clientDescription: string;
  projectScope: string;
}

export interface ProposalResponse {
  executiveSummary: string;
  sections: Array<{
    title: string;
    content: string;
  }>;
}

// Helper function to handle API errors
const handleError = (error: any) => {
  console.error('API Error:', error);
  throw error;
};

// Format text to clean up formatting
const formatText = (text: string): string => {
  try {
    return text
      .replace(/\\n/g, "\n")
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'")
      .replace(/\n{3,}/g, "\n\n") // prevent excessive newlines
      .trim();
  } catch (err) {
    return text;
  }
};

// Documents API
export const documentsAPI = {
  async listDocuments(): Promise<Document[]> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.GET_DOCUMENTS}`, {
        method: 'GET',
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Handle different response formats
      if (Array.isArray(data)) {
        return data;
      } else if (data.documents && Array.isArray(data.documents)) {
        return data.documents;
      } else {
        console.warn('Unexpected response format:', data);
        return [];
      }
    } catch (err) {
      return handleError(err);
    }
  },

  async uploadDocument(file: File, metadata: Record<string, any>): Promise<Document> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // Add metadata
      Object.entries(metadata).forEach(([key, value]) => {
        if (typeof value === 'object') {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, String(value));
        }
      });

      const response = await fetch(`${API_URL}${ENDPOINTS.UPLOAD_FILE}`, {
        method: 'POST',
        headers: {
          'X-API-Key': API_KEY
        },
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error || 'Failed to upload document');
        } catch (jsonError) {
          throw new Error(`Failed to upload document: ${response.statusText}`);
        }
      }

      return await response.json();
    } catch (err) {
      return handleError(err);
    }
  },

  async deleteDocument(id: string): Promise<void> {
    try {
      const deleteEndpoint = ENDPOINTS.DELETE_DOCUMENT.replace(':id', id);
      const response = await fetch(`${API_URL}${deleteEndpoint}`, {
        method: 'DELETE',
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to delete document: ${response.statusText}`);
      }
    } catch (err) {
      return handleError(err);
    }
  },

  async reprocessDocument(id: string): Promise<void> {
    try {
      const getDocumentEndpoint = ENDPOINTS.GET_DOCUMENT.replace(':id', id);
      const response = await fetch(`${API_URL}${getDocumentEndpoint}`, {
        method: 'POST',
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to reprocess document: ${response.statusText}`);
      }
    } catch (err) {
      return handleError(err);
    }
  },
};

// Chat API
export const chatAPI = {
  async sendMessage(message: string, history: Message[], documentIds?: string[]): Promise<string> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.CHAT_COMPLETION}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
        },
        body: JSON.stringify({
          query: message,
          history: history.map(m => ({
            role: m.role,
            content: m.content
          })),
          documentIds: documentIds?.length ? documentIds : undefined,
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Handle structured or fallback response
      return formatText(data.answer || data.response || JSON.stringify(data));
    } catch (err) {
      return handleError(err);
    }
  },

  async streamChat(message: string, history: Message[], documentIds?: string[]): Promise<ReadableStream<Uint8Array> | null> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.CHAT_COMPLETION}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
        },
        body: JSON.stringify({
          query: message,
          history: history.map(m => ({
            role: m.role,
            content: m.content
          })),
          documentIds: documentIds?.length ? documentIds : undefined,
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.body;
    } catch (err) {
      return handleError(err);
    }
  }
};

// Proposals API
export const proposalsAPI = {
  async generateProposal(request: ProposalRequest): Promise<ProposalResponse> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.PROGRESSIVE_SCOPING}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        throw new Error('Proposal generation failed');
      }

      return await response.json();
    } catch (err) {
      return handleError(err);
    }
  },
  
  async generateProposalStream(
    request: ProposalRequest,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${API_URL}${ENDPOINTS.PROGRESSIVE_SCOPING}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
        },
        body: JSON.stringify({
          ...request,
          stream: true
        })
      });

      if (!response.ok) throw new Error('Proposal streaming failed');
      if (!response.body) throw new Error('No response body');

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        onChunk(chunk);
      }
    } catch (err) {
      handleError(err);
    }
  }
}; 