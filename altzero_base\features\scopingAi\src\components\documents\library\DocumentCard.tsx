import React from "react";
import { Link } from "react-router-dom";
import { Share2 } from "lucide-react";
import { But<PERSON> } from "@base/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@base/components/ui/card";
import { Badge } from "@base/components/ui/badge";

interface DocumentCardProps {
  document: {
    id: number | string;
    title: string;
    client: string;
    date: string;
    type: string;
    sections: string[];
    markdown_path?: string;
  };
  onPreview: (id: number | string) => void;
  onEdit: (id: number | string) => void;
  onMakePublic: (id: string) => void;
}

export function DocumentCard({
  document,
  onPreview,
  onEdit,
  onMakePublic,
}: DocumentCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{document.title}</CardTitle>
          <Badge variant="outline">{document.type}</Badge>
        </div>
        <CardDescription>Client: {document.client}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground mb-3">
          Last updated: {document.date}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPreview(document.id)}
          >
            Preview
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(document.id)}
          >
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onMakePublic(document.id.toString())}
            title="Mark as template"
          >
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
        <Link to={`/documents/new?reference=${document.id}`}>
          <Button size="sm">Create Proposal</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
