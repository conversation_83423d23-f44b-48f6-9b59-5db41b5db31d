import React, { useRef } from "react";
import { Button } from "../../../../base/components/ui/button";
import { Save } from "lucide-react";
import { DocumentTheme } from "../types/documentTypes";
import { themePresets } from "../utils/themeUtils";
import { convertFileToBase64 } from "../utils/documentUtils";
import { useDocumentContext } from "../contexts/DocumentContext";

interface ThemeControlsProps {
  activeTab?: "theme" | "branding";
}

export const ThemeControls: React.FC<ThemeControlsProps> = ({
  activeTab = "theme",
}) => {
  const {
    documentTheme,
    updateThemeProperty,
    updateHeaderProperty,
    updateFooterProperty,
    applyThemePreset,
    saveDocumentTheme,
    handleLogoUpload,
    isLoading,
  } = useDocumentContext();

  return (
    <div className="space-y-6">
      {/* Theme Presets Section */}
      <div>
        <h3 className="text-sm font-medium mb-3">Color Palettes</h3>
        <div className="grid grid-cols-2 gap-3">
          <button
            className="border rounded-md p-3 hover:border-blue-500 transition-colors"
            onClick={() => applyThemePreset("professional")}
          >
            <div className="space-y-2">
              <div className="h-4 bg-blue-600 rounded"></div>
              <div className="h-3 bg-blue-500 rounded"></div>
              <div className="h-2 bg-slate-700 rounded"></div>
              <div className="h-2 bg-slate-100 rounded"></div>
              <span className="text-xs mt-2 block">Professional</span>
            </div>
          </button>
          <button
            className="border rounded-md p-3 hover:border-blue-500 transition-colors"
            onClick={() => applyThemePreset("modern")}
          >
            <div className="space-y-2">
              <div className="h-4 bg-teal-600 rounded"></div>
              <div className="h-3 bg-teal-500 rounded"></div>
              <div className="h-2 bg-slate-700 rounded"></div>
              <div className="h-2 bg-slate-100 rounded"></div>
              <span className="text-xs mt-2 block">Modern</span>
            </div>
          </button>
          <button
            className="border rounded-md p-3 hover:border-blue-500 transition-colors"
            onClick={() => applyThemePreset("creative")}
          >
            <div className="space-y-2">
              <div className="h-4 bg-purple-600 rounded"></div>
              <div className="h-3 bg-purple-500 rounded"></div>
              <div className="h-2 bg-slate-700 rounded"></div>
              <div className="h-2 bg-slate-100 rounded"></div>
              <span className="text-xs mt-2 block">Creative</span>
            </div>
          </button>
          <button
            className="border rounded-md p-3 hover:border-blue-500 transition-colors"
            onClick={() => applyThemePreset("formal")}
          >
            <div className="space-y-2">
              <div className="h-4 bg-gray-700 rounded"></div>
              <div className="h-3 bg-gray-600 rounded"></div>
              <div className="h-2 bg-slate-700 rounded"></div>
              <div className="h-2 bg-slate-100 rounded"></div>
              <span className="text-xs mt-2 block">Formal</span>
            </div>
          </button>
        </div>
      </div>

      {/* Colors Section */}
      <div>
        <h3 className="text-sm font-medium mb-3">Colors</h3>
        <div className="space-y-4">
          <div>
            <label className="text-xs text-gray-500 block mb-1">
              Heading Color
            </label>
            <div className="flex items-center gap-2">
              <div
                className="w-8 h-8 border rounded"
                style={{ backgroundColor: documentTheme.headingColor }}
              ></div>
              <input
                type="color"
                value={documentTheme.headingColor}
                onChange={(e) =>
                  updateThemeProperty("headingColor", e.target.value)
                }
                className="h-8 w-10 cursor-pointer"
              />
            </div>
          </div>
          <div>
            <label className="text-xs text-gray-500 block mb-1">
              Subheading Color
            </label>
            <div className="flex items-center gap-2">
              <div
                className="w-8 h-8 border rounded"
                style={{ backgroundColor: documentTheme.subheadingColor }}
              ></div>
              <input
                type="color"
                value={documentTheme.subheadingColor}
                onChange={(e) =>
                  updateThemeProperty("subheadingColor", e.target.value)
                }
                className="h-8 w-10 cursor-pointer"
              />
            </div>
          </div>
          <div>
            <label className="text-xs text-gray-500 block mb-1">
              Text Color
            </label>
            <div className="flex items-center gap-2">
              <div
                className="w-8 h-8 border rounded"
                style={{ backgroundColor: documentTheme.textColor }}
              ></div>
              <input
                type="color"
                value={documentTheme.textColor}
                onChange={(e) =>
                  updateThemeProperty("textColor", e.target.value)
                }
                className="h-8 w-10 cursor-pointer"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Typography Section */}
      <div>
        <h3 className="text-sm font-medium mb-3">Typography</h3>
        <div className="space-y-4">
          <div>
            <label className="text-xs text-gray-500 block mb-1">
              Font Family
            </label>
            <select
              className="w-full border rounded p-2 text-sm"
              value={documentTheme.fontFamily}
              onChange={(e) =>
                updateThemeProperty("fontFamily", e.target.value)
              }
            >
              <option>Inter (Sans-serif)</option>
              <option>Merriweather (Serif)</option>
              <option>JetBrains Mono (Monospace)</option>
              <option>Playfair Display (Display)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Branding & Identity Section */}
      <div>
        <h3 className="text-sm font-medium mb-3">Branding & Identity</h3>

        {/* Logo Upload Section */}
        <div className="mb-6">
          <label className="text-xs text-gray-500 block mb-2">
            Company Logo
          </label>
          <Button
            variant="outline"
            size="sm"
            onClick={handleLogoUpload}
            className="w-full mb-4"
            disabled={isLoading}
          >
            {documentTheme.logo ? "Change Logo" : "Upload Logo"}
          </Button>

          {documentTheme.logo && (
            <div className="p-4 border rounded-md mb-4 bg-gray-50">
              <p className="text-xs text-gray-500 mb-2">Current Logo Preview</p>
              <div className="flex justify-center p-2 bg-white rounded border">
                <img
                  src={documentTheme.logo}
                  alt="Document Logo"
                  className="max-h-24 max-w-full object-contain"
                />
              </div>
            </div>
          )}
        </div>

        {/* Brand Name Section */}
        <div className="mb-4">
          <label className="text-xs text-gray-500 block mb-2">Brand Name</label>
          <input
            type="text"
            value={documentTheme.brandName || ""}
            onChange={(e) => updateThemeProperty("brandName", e.target.value)}
            placeholder="Enter your company/brand name"
            className="w-full border rounded p-2 text-sm"
          />
          <p className="text-xs text-gray-400 mt-1">
            This will appear in headers and footers
          </p>
        </div>

        {/* Brand Tagline Section */}
        <div className="mb-4">
          <label className="text-xs text-gray-500 block mb-2">
            Brand Tagline
          </label>
          <input
            type="text"
            value={documentTheme.brandTagline || ""}
            onChange={(e) =>
              updateThemeProperty("brandTagline", e.target.value)
            }
            placeholder="Enter your company tagline or subtitle"
            className="w-full border rounded p-2 text-sm"
          />
          <p className="text-xs text-gray-400 mt-1">
            A short description or motto for your brand
          </p>
        </div>

        {/* Preview Section */}
        {(documentTheme.brandName || documentTheme.brandTagline) && (
          <div className="p-4 border rounded-md bg-blue-50 border-blue-200">
            <p className="text-xs text-blue-600 mb-2 font-medium">
              Brand Preview
            </p>
            <div className="flex items-center gap-3">
              {documentTheme.logo && (
                <img
                  src={documentTheme.logo}
                  alt="Logo"
                  className="h-8 object-contain"
                />
              )}
              <div>
                <div className="text-sm font-bold text-gray-800">
                  {documentTheme.brandName || "Your Brand Name"}
                </div>
                <div className="text-xs text-gray-600">
                  {documentTheme.brandTagline || "Your brand tagline"}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="border-t pt-4">
        <Button
          variant="default"
          onClick={saveDocumentTheme}
          disabled={isLoading}
          className="w-full"
        >
          Apply Theme Changes
        </Button>
      </div>
    </div>
  );
};
