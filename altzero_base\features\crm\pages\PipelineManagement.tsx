import React, { useState, useEffect } from 'react';
import { GitBranch, Search, Plus, Edit, Trash2, Settings } from 'lucide-react';
import CRMLayout from '../components/CRMLayout';

interface Pipeline {
  id: string;
  organisation_id: string;
  name: string;
  description?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

const PipelineManagement: React.FC = () => {
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadPipelines();
  }, []);

  const loadPipelines = async () => {
    try {
      setLoading(true);
      // TODO: Implement API call to fetch pipelines
      // const response = await crmService.getPipelines();
      // setPipelines(response.data);
      
      // Mock data for now
      setPipelines([
        {
          id: '1',
          organisation_id: 'org1',
          name: 'Sales Pipeline',
          description: 'Standard sales process for new customers',
          is_default: true,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          organisation_id: 'org1',
          name: 'Enterprise Pipeline',
          description: 'Extended process for enterprise clients',
          is_default: false,
          created_at: '2024-01-16T10:00:00Z',
          updated_at: '2024-01-16T10:00:00Z'
        },
        {
          id: '3',
          organisation_id: 'org1',
          name: 'Partner Pipeline',
          description: 'Partnership and channel sales process',
          is_default: false,
          created_at: '2024-01-17T10:00:00Z',
          updated_at: '2024-01-17T10:00:00Z'
        }
      ]);
    } catch (error) {
      console.error('Error loading pipelines:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPipelines = pipelines.filter(pipeline =>
    pipeline.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    pipeline.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <GitBranch className="w-8 h-8 text-blue-600 mr-3" />
              Sales Pipelines
            </h1>
            <p className="text-gray-600 mt-1">Configure and manage your sales processes</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            New Pipeline
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search pipelines..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Pipelines Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            <div className="col-span-full text-center py-8 text-gray-500">
              Loading pipelines...
            </div>
          ) : filteredPipelines.length === 0 ? (
            <div className="col-span-full text-center py-8 text-gray-500">
              {searchTerm ? 'No pipelines found matching your search.' : 'No pipelines found. Create your first pipeline to get started.'}
            </div>
          ) : (
            filteredPipelines.map((pipeline) => (
              <div key={pipeline.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-lg font-semibold text-gray-900">{pipeline.name}</h3>
                      {pipeline.is_default && (
                        <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                          Default
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">{pipeline.description || 'No description'}</p>
                  </div>
                  <div className="flex items-center space-x-1 ml-4">
                    <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                      <Settings className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    {!pipeline.is_default && (
                      <button className="p-1 text-gray-400 hover:text-red-600 transition-colors">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>Created: {new Date(pipeline.created_at).toLocaleDateString()}</span>
                    <button className="text-blue-600 hover:text-blue-700 font-medium">
                      Manage Stages →
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Stats */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-800">
              <strong>{filteredPipelines.length}</strong> pipelines configured
            </div>
            <div className="text-xs text-blue-600">
              💡 Tip: Create different pipelines for different types of sales processes
            </div>
          </div>
        </div>
      </div>
    </CRMLayout>
  );
};

export default PipelineManagement;
