import { OpenAI } from "openai";

export interface LLMConfig {
  apiKey?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  maxRetries?: number;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason?: string;
}

export class LLMService {
  private client: OpenAI;
  private config: Required<LLMConfig>;

  constructor(config: LLMConfig = {}) {
    this.config = {
      apiKey: config.apiKey || process.env.OPENAI_API_KEY || '',
      model: config.model || process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
      temperature: config.temperature ?? 0.7,
      maxTokens: config.maxTokens || 2000,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 3,
    };

    if (!this.config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      timeout: this.config.timeout,
      maxRetries: this.config.maxRetries,
    });
  }

  async chat(messages: ChatMessage[], options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  }): Promise<LLMResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: options?.model || this.config.model,
        messages: messages,
        temperature: options?.temperature ?? this.config.temperature,
        max_tokens: options?.maxTokens || this.config.maxTokens,
        stream: options?.stream || false,
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new Error('No response from LLM');
      }

      return {
        content: choice.message.content || '',
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
        model: response.model,
        finishReason: choice.finish_reason || undefined,
      };
    } catch (error: any) {
      const sanitizedError = error.message?.replace(/sk-[a-zA-Z0-9_-]+/g, '[API_KEY]');
      console.error('❌ LLM Service error:', sanitizedError);
      throw new Error(`LLM request failed: ${sanitizedError}`);
    }
  }

  async *chatStream(messages: ChatMessage[], options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  }): AsyncGenerator<string> {
    try {
      const stream = await this.client.chat.completions.create({
        model: options?.model || this.config.model,
        messages: messages,
        temperature: options?.temperature ?? this.config.temperature,
        max_tokens: options?.maxTokens || this.config.maxTokens,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          yield content;
        }
      }
    } catch (error: any) {
      const sanitizedError = error.message?.replace(/sk-[a-zA-Z0-9_-]+/g, '[API_KEY]');
      console.error('❌ LLM Stream error:', sanitizedError);
      throw new Error(`LLM stream failed: ${sanitizedError}`);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.chat([
        { role: 'user', content: 'Test connection' }
      ], { maxTokens: 10 });
      
      return !!response.content;
    } catch (error) {
      console.error('❌ LLM connection test failed:', error);
      return false;
    }
  }

  getConfig(): Readonly<Required<LLMConfig>> {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Recreate client if API key changed
    if (newConfig.apiKey) {
      this.client = new OpenAI({
        apiKey: this.config.apiKey,
        timeout: this.config.timeout,
        maxRetries: this.config.maxRetries,
      });
    }
  }

  // Utility method to count tokens (approximate)
  estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  // Utility method to truncate text to fit token limit
  truncateToTokenLimit(text: string, maxTokens: number): string {
    const estimatedTokens = this.estimateTokens(text);
    if (estimatedTokens <= maxTokens) {
      return text;
    }
    
    const ratio = maxTokens / estimatedTokens;
    const truncatedLength = Math.floor(text.length * ratio * 0.9); // 10% buffer
    return text.substring(0, truncatedLength) + '...';
  }

  // Method to build system prompts for different use cases
  buildSystemPrompt(type: 'research' | 'section' | 'summary' | 'chat'): string {
    const prompts = {
      research: `You are a senior business analyst conducting comprehensive research for client proposals. 
Focus on understanding the client's business, industry context, challenges, and opportunities. 
Provide detailed, actionable insights that will inform proposal development.`,

      section: `You are an expert proposal writer creating professional document sections. 
Write clear, compelling content that demonstrates value and expertise. 
Use industry-appropriate language and structure content logically with clear headings and bullet points.`,

      summary: `You are a strategic consultant creating executive summaries. 
Synthesize complex information into clear, concise overviews that highlight key value propositions and outcomes. 
Focus on business impact and strategic alignment.`,

      chat: `You are a knowledgeable assistant helping with document analysis and proposal development. 
Provide helpful, accurate responses based on the available context and documents. 
Be concise but thorough in your explanations.`
    };

    return prompts[type];
  }
}

// Export singleton instance
export const llmService = new LLMService();
