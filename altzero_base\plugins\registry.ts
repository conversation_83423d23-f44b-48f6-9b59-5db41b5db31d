export interface PluginConfig {
  enabled: boolean;
  name: string;
  icon: string;
  route: string;
  backend: boolean;
  permissions?: string[];
  dependencies?: string[];
  description?: string;
  version?: string;
}

// 🔍 MASTER LOOKUP - Single source of truth for all plugins
export const PLUGIN_REGISTRY: Record<string, PluginConfig> = {
  knowledge: {
    enabled: true,
    name: "Knowledge Base",
    icon: "Brain",
    route: "/knowledge",
    backend: true,
    description: "AI-powered document management and chat",
    permissions: ["knowledge:read", "knowledge:write"],
    version: "1.0.0",
  },
  pseo: {
    enabled: true,
    name: "pSEO",
    icon: "BarChart3",
    route: "/pseo",
    backend: true,
    description:
      "Comprehensive SEO toolkit with single-site audits and full-site analysis",
    permissions: ["pseo:read", "pseo:write", "pseo:audit", "pseo:analysis"],
    version: "2.0.0",
    dependencies: ["database", "ai-services"],
  },
  aichat: {
    enabled: true,
    name: "AI Chat",
    icon: "MessageSquare",
    route: "/copilot-chat",
    backend: true,
    description: "CopilotKit-powered AI assistant",
    permissions: ["chat:read"],
    version: "1.0.0",
  },
  crm: {
    enabled: true,
    name: "CRM",
    icon: "Users",
    route: "/crm",
    backend: true,
    description:
      "Customer Relationship Management system with contacts, companies, opportunities, and activities",
    permissions: ["crm:read", "crm:write", "crm:delete"],
    version: "1.0.0",
    dependencies: ["database", "auth"],
  },
  scopingAi: {
    enabled: true,
    name: "ScopingAI",
    icon: "FileText",
    route: "/scopingai",
    backend: true,
    description: "AI-powered project scoping and proposal generation system",
    permissions: ["scopingai:read", "scopingai:write", "scopingai:generate"],
    version: "1.0.0",
    dependencies: ["database", "auth", "ai-services"],
  },
} as const;

// Helper functions
export const getEnabledPlugins = (): string[] => {
  return Object.entries(PLUGIN_REGISTRY)
    .filter(([_, config]) => config.enabled)
    .map(([name, _]) => name);
};

export const getPluginConfig = (
  pluginName: string
): PluginConfig | undefined => {
  return PLUGIN_REGISTRY[pluginName];
};

export const isPluginEnabled = (pluginName: string): boolean => {
  return PLUGIN_REGISTRY[pluginName]?.enabled ?? false;
};

export const getAllPlugins = (): Record<string, PluginConfig> => {
  return PLUGIN_REGISTRY;
};

export const getEnabledPluginConfigs = (): Record<string, PluginConfig> => {
  return Object.fromEntries(
    Object.entries(PLUGIN_REGISTRY).filter(([_, config]) => config.enabled)
  );
};
