/**
 * Crypto utilities for document sharing - Browser compatible
 */

/**
 * Generate a secure random token using Web Crypto API
 */
export const generateSecureToken = (length: number = 32): string => {
  // Use Web Crypto API which is available in browsers
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);

  // Convert to base64url
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=/g, "");
};

/**
 * Simple hash function for browser compatibility
 * Note: For production, consider moving password hashing to server-side
 */
export const hashPassword = async (password: string): Promise<string> => {
  // Use Web Crypto API for hashing
  const encoder = new TextEncoder();
  // Use a fixed salt for consistency (in production, use proper salt management)
  const data = encoder.encode(password + "scoping_salt_2024");
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);

  // Convert to hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
};

/**
 * Verify a password against a hash (simplified for browser)
 * Note: This is basic - in production, use proper server-side password verification
 */
export const verifyPassword = async (
  password: string,
  hash: string
): Promise<boolean> => {
  // Hash the input password with the same salt and compare
  const hashedInput = await hashPassword(password);
  return hashedInput === hash;
};

/**
 * Generate a simple UUID v4 using crypto.getRandomValues
 */
export const generateUUID = (): string => {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);

  // Set version (4) and variant bits
  array[6] = (array[6] & 0x0f) | 0x40;
  array[8] = (array[8] & 0x3f) | 0x80;

  // Convert to UUID format
  const hex = Array.from(array, (byte) =>
    byte.toString(16).padStart(2, "0")
  ).join("");
  return [
    hex.slice(0, 8),
    hex.slice(8, 12),
    hex.slice(12, 16),
    hex.slice(16, 20),
    hex.slice(20, 32),
  ].join("-");
};
