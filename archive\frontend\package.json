{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^3.16.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@supabase/supabase-js": "^2.48.1", "@tinymce/tinymce-react": "^6.1.0", "@tiptap/extension-color": "^2.1.6", "@tiptap/extension-font-family": "^2.1.6", "@tiptap/extension-image": "^2.1.6", "@tiptap/extension-subscript": "^2.1.6", "@tiptap/extension-superscript": "^2.1.6", "@tiptap/extension-table": "^2.1.6", "@tiptap/extension-table-cell": "^2.1.6", "@tiptap/extension-table-header": "^2.1.6", "@tiptap/extension-table-row": "^2.1.6", "@tiptap/extension-text-align": "^2.1.6", "@tiptap/extension-text-style": "^2.1.6", "@tiptap/extension-underline": "^2.1.6", "@tiptap/react": "^2.1.6", "@tiptap/starter-kit": "^2.1.6", "lucide-react": "^0.344.0", "marked": "^15.0.8", "openai": "^4.28.0", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.2", "react-to-pdf": "^1.0.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "supabase": "^2.19.7", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}