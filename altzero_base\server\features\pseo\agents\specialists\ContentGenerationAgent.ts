// =====================================================
// CONTENT GENERATION AGENT - SEO CONTENT CREATION
// =====================================================

import { BaseAgent } from '../core/BaseAgent';
import { 
  AgentContext, 
  AgentResult, 
  AgentInput,
  ContentGenerationInput,
  ContentGenerationResult,
  GeneratedContent,
  ContentCalendarItem
} from '../core/AgentTypes';
import { providerConfigService } from '../../services/external/ProviderConfigService';
import { GoogleAnalyticsService } from '../../services/external/GoogleAnalyticsService';

export class ContentGenerationAgent extends BaseAgent {
  
  constructor() {
    super(
      'ContentGenerationAgent',
      'Generates SEO-optimized content based on keyword opportunities and content gaps',
      ['content_generation', 'seo_optimization']
    );
  }

  async execute(context: AgentContext): Promise<AgentResult> {
    return this.executeWithMetrics(context, async () => {
      // Validate configuration and tools
      this.validateConfig(context);
      this.checkRequiredTools(context);

      const input = context.job.result_data as unknown as ContentGenerationInput;
      if (!this.validateInput(input)) {
        return this.createErrorResult('Invalid input parameters');
      }

      const website = context.website;
      const params = input.parameters;
      
      context.logger.info('Starting content generation', {
        website_id: website.id,
        domain: website.domain,
        target_keywords: params.target_keywords.length,
        content_types: params.content_types
      });

      const contentOpportunities: any[] = [];
      const generatedContent: GeneratedContent[] = [];
      const contentCalendar: ContentCalendarItem[] = [];
      let totalApiCalls = 0;

      try {
        // Step 1: Analyze existing content and identify gaps
        await this.reportProgress(context, 10, 'Analyzing existing content');
        
        const contentGaps = await this.analyzeContentGaps(website, params.target_keywords, context);
        
        // Step 1.5: Enhance with real analytics data if available
        const enhancedGaps = await this.enhanceWithAnalyticsData(contentGaps, context);
        
        await this.reportProgress(context, 20, `Identified ${enhancedGaps.length} content gaps`);

        // Step 2: Generate content opportunities
        await this.reportProgress(context, 30, 'Creating content opportunities');
        
        const opportunities = await this.createContentOpportunities(
          enhancedGaps,
          params.content_types,
          params.target_keywords,
          context
        );
        
        contentOpportunities.push(...opportunities);
        await this.reportProgress(context, 40, `Created ${opportunities.length} content opportunities`);

        // Step 3: Generate actual content for high-priority opportunities
        await this.reportProgress(context, 50, 'Generating content for top opportunities');
        
        const topOpportunities = opportunities
          .sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0))
          .slice(0, Math.min(5, opportunities.length)); // Limit for API efficiency

        for (let i = 0; i < topOpportunities.length; i++) {
          const opportunity = topOpportunities[i];
          
          await this.reportProgress(
            context, 
            50 + (i / topOpportunities.length) * 30, 
            `Generating content: ${opportunity.title}`
          );

          try {
            const content = await this.generateContent(
              opportunity,
              params.ai_model || 'gpt-4o',
              params.tone_of_voice,
              context
            );
            
            if (content) {
              generatedContent.push(content);
              totalApiCalls += 1;
            }
          } catch (error) {
            context.logger.warn(`Failed to generate content for: ${opportunity.title}`, { error });
          }
        }

        await this.reportProgress(context, 85, `Generated ${generatedContent.length} pieces of content`);

        // Step 4: Create content calendar
        await this.reportProgress(context, 90, 'Creating content calendar');
        
        const calendar = await this.createContentCalendar(contentOpportunities, generatedContent, context);
        contentCalendar.push(...calendar);

        // Step 5: Save content opportunities to database
        await this.reportProgress(context, 95, 'Saving content opportunities');
        await this.saveContentOpportunities(website.id, contentOpportunities, generatedContent, context);

        await this.reportProgress(context, 100, 'Content generation completed');

        return this.createSuccessResult({
          content_opportunities: contentOpportunities,
          generated_content: generatedContent,
          content_calendar: contentCalendar
        }, {
          data_points_processed: contentOpportunities.length + generatedContent.length,
          api_calls_made: totalApiCalls
        });

      } catch (error) {
        const agentError = this.handleError(error, 'Content generation failed');
        return this.createErrorResult(agentError.message);
      }
    });
  }

  validateInput(input: AgentInput): boolean {
    this.validateCommonInput(input);
    
    const params = input.parameters as ContentGenerationInput['parameters'];
    
    if (!params.target_keywords || params.target_keywords.length === 0) {
      throw new Error('At least one target keyword must be specified');
    }

    if (!params.content_types || params.content_types.length === 0) {
      throw new Error('At least one content type must be specified');
    }

    const validTypes = ['blog', 'landing', 'product', 'guide', 'faq'];
    const invalidTypes = params.content_types.filter(t => !validTypes.includes(t));
    if (invalidTypes.length > 0) {
      throw new Error(`Invalid content types: ${invalidTypes.join(', ')}`);
    }

    if (params.target_word_count && params.target_word_count <= 0) {
      throw new Error('target_word_count must be a positive number');
    }

    return true;
  }

  getRequiredTools(): string[] {
    return ['ai', 'database', 'cache', 'http'];
  }

  // =====================================================
  // CONTENT GAP ANALYSIS
  // =====================================================

  private async analyzeContentGaps(
    website: any, 
    targetKeywords: string[], 
    context: AgentContext
  ): Promise<any[]> {
    const gaps: any[] = [];

    try {
      // Get existing pages to analyze current content coverage
      const existingPages = await this.getExistingPages(website.id, context);
      
      // Analyze each keyword for content gaps
      for (const keyword of targetKeywords) {
        const gap = await this.analyzeKeywordGap(keyword, existingPages, website.domain, context);
        if (gap) {
          gaps.push(gap);
        }
      }

      // Add competitor content gap analysis
      const competitorGaps = await this.analyzeCompetitorContentGaps(website.domain, targetKeywords, context);
      gaps.push(...competitorGaps);

    } catch (error) {
      context.logger.error('Content gap analysis failed', error as Error);
    }

    return gaps;
  }

  private async getExistingPages(websiteId: string, context: AgentContext): Promise<any[]> {
    try {
      const sql = `
        SELECT url, title, meta_description, page_type, word_count
        FROM pseo_website_pages 
        WHERE website_id = ? AND status = 'analyzed'
      `;
      
      const result = await context.tools.database.query(sql, [websiteId]);
      return (result as any).rows || [];
    } catch (error) {
      context.logger.warn('Failed to get existing pages', { error });
      return [];
    }
  }

  private async analyzeKeywordGap(
    keyword: string, 
    existingPages: any[], 
    domain: string, 
    context: AgentContext
  ): Promise<any | null> {
    // Check if keyword is already well-covered by existing content
    const relevantPages = existingPages.filter(page => 
      page.title?.toLowerCase().includes(keyword.toLowerCase()) ||
      page.meta_description?.toLowerCase().includes(keyword.toLowerCase())
    );

    if (relevantPages.length >= 2) {
      // Keyword appears to be well covered
      return null;
    }

    // Determine content type based on keyword intent
    const contentType = this.inferContentType(keyword);
    const priority = this.calculatePriority(keyword, relevantPages.length);

    return {
      target_keyword: keyword,
      content_type: contentType,
      priority,
      gap_type: relevantPages.length === 0 ? 'no_coverage' : 'partial_coverage',
      existing_coverage: relevantPages.length,
      opportunity_reason: this.generateOpportunityReason(keyword, relevantPages.length)
    };
  }

  private async analyzeCompetitorContentGaps(
    domain: string, 
    keywords: string[], 
    context: AgentContext
  ): Promise<any[]> {
    const gaps: any[] = [];

    // Simulate competitor content analysis
    // In production, this would use actual competitor analysis tools
    
    const competitorTopics = [
      'industry best practices',
      'comparison guides', 
      'case studies',
      'tutorial content',
      'product reviews'
    ];

    for (const topic of competitorTopics) {
      gaps.push({
        target_keyword: `${domain.split('.')[0]} ${topic}`,
        content_type: 'blog',
        priority: 'medium',
        gap_type: 'competitor_coverage',
        opportunity_reason: `Competitors are ranking well for ${topic} content`
      });
    }

    return gaps.slice(0, 3); // Limit for efficiency
  }

  // =====================================================
  // CONTENT OPPORTUNITY CREATION
  // =====================================================

  private async createContentOpportunities(
    contentGaps: any[],
    contentTypes: string[],
    targetKeywords: string[],
    context: AgentContext
  ): Promise<any[]> {
    const opportunities: any[] = [];

    for (const gap of contentGaps) {
      if (contentTypes.includes(gap.content_type)) {
        const opportunity = await this.createOpportunityFromGap(gap, context);
        if (opportunity) {
          opportunities.push(opportunity);
        }
      }
    }

    // Add additional opportunities based on content types
    for (const contentType of contentTypes) {
      const typeOpportunities = await this.generateOpportunitiesByType(
        contentType, 
        targetKeywords, 
        context
      );
      opportunities.push(...typeOpportunities);
    }

    // Deduplicate and prioritize
    return this.deduplicateAndPrioritizeOpportunities(opportunities);
  }

  private async createOpportunityFromGap(gap: any, context: AgentContext): Promise<any | null> {
    try {
      const title = await this.generateContentTitle(gap.target_keyword, gap.content_type, context);
      const brief = await this.generateContentBrief(gap.target_keyword, gap.content_type, context);

      return {
        target_keyword: gap.target_keyword,
        content_type: gap.content_type,
        title,
        content_brief: brief,
        priority: gap.priority,
        priority_score: this.calculatePriorityScore(gap),
        difficulty_score: this.calculateDifficultyScore(gap.target_keyword),
        estimated_traffic: this.estimateTrafficPotential(gap.target_keyword),
        status: 'identified',
        gap_analysis: gap
      };
    } catch (error) {
      context.logger.warn(`Failed to create opportunity from gap: ${gap.target_keyword}`, { error });
      return null;
    }
  }

  private async generateOpportunitiesByType(
    contentType: string, 
    keywords: string[], 
    context: AgentContext
  ): Promise<any[]> {
    const opportunities: any[] = [];
    
    // Generate content ideas based on type
    const typeTemplates = this.getContentTypeTemplates(contentType);
    
    for (const keyword of keywords.slice(0, 5)) { // Limit for efficiency
      for (const template of typeTemplates.slice(0, 2)) {
        const title = template.replace('{keyword}', keyword);
        
        opportunities.push({
          target_keyword: keyword,
          content_type: contentType,
          title,
          content_brief: `${contentType} content targeting ${keyword}`,
          priority: 'medium',
          priority_score: 50,
          difficulty_score: Math.floor(Math.random() * 100),
          estimated_traffic: Math.floor(Math.random() * 1000) + 100,
          status: 'identified'
        });
      }
    }

    return opportunities;
  }

  // =====================================================
  // CONTENT GENERATION
  // =====================================================

  private async generateContent(
    opportunity: any,
    aiModel: string,
    toneOfVoice: string = 'professional',
    context: AgentContext
  ): Promise<GeneratedContent | null> {
    try {
      const prompt = this.buildContentPrompt(opportunity, toneOfVoice);
      
      const content = await context.tools.ai.generateText(prompt, {
        model: aiModel,
        temperature: 0.7,
        max_tokens: 2000
      });

      const wordCount = this.countWords(content);
      const readabilityScore = this.calculateReadabilityScore(content);
      const seoScore = this.calculateSEOScore(content, opportunity.target_keyword);

      return {
        title: opportunity.title,
        content,
        word_count: wordCount,
        target_keywords: [opportunity.target_keyword],
        meta_description: this.generateMetaDescription(content, opportunity.target_keyword),
        content_type: opportunity.content_type,
        readability_score: readabilityScore,
        seo_score: seoScore
      };

    } catch (error) {
      if (error instanceof Error && error.message.includes('not configured')) {
        context.logger.warn(`AI service not configured - skipping content generation for: ${opportunity.title}`, {
          message: 'Configure OpenAI API key to enable AI content generation'
        });
        return null;
      }
      context.logger.error(`Failed to generate content for: ${opportunity.title}`, error as Error);
      return null;
    }
  }

  private buildContentPrompt(opportunity: any, toneOfVoice: string): string {
    const prompts = {
      blog: `Write a comprehensive blog post about "${opportunity.target_keyword}". 
            Title: ${opportunity.title}
            Tone: ${toneOfVoice}
            Include: Introduction, main points, actionable tips, and conclusion.
            Target length: 800-1200 words.
            SEO requirements: Use the target keyword naturally throughout the content.`,
      
      landing: `Create a compelling landing page for "${opportunity.target_keyword}".
             Title: ${opportunity.title}
             Tone: ${toneOfVoice}
             Include: Headline, benefits, features, social proof, and call-to-action.
             Target length: 400-600 words.
             Focus on conversion optimization.`,
      
      guide: `Write a detailed guide about "${opportunity.target_keyword}".
            Title: ${opportunity.title}
            Tone: ${toneOfVoice}
            Include: Step-by-step instructions, examples, tips, and best practices.
            Target length: 1000-1500 words.
            Make it actionable and comprehensive.`,
      
      faq: `Create a FAQ section for "${opportunity.target_keyword}".
          Title: ${opportunity.title}
          Tone: ${toneOfVoice}
          Include: 8-12 common questions and detailed answers.
          Target length: 600-800 words.
          Address common concerns and objections.`,
      
      product: `Write product content for "${opportunity.target_keyword}".
              Title: ${opportunity.title}
              Tone: ${toneOfVoice}
              Include: Product description, features, benefits, and specifications.
              Target length: 300-500 words.
              Focus on value proposition and user benefits.`
    };

    return prompts[opportunity.content_type as keyof typeof prompts] || prompts.blog;
  }

  // =====================================================
  // CONTENT CALENDAR CREATION
  // =====================================================

  private async createContentCalendar(
    opportunities: any[],
    generatedContent: GeneratedContent[],
    context: AgentContext
  ): Promise<ContentCalendarItem[]> {
    const calendar: ContentCalendarItem[] = [];
    const startDate = new Date();

    // Sort opportunities by priority and estimated traffic
    const prioritizedOpportunities = opportunities
      .sort((a, b) => 
        (b.priority_score || 0) - (a.priority_score || 0) ||
        (b.estimated_traffic || 0) - (a.estimated_traffic || 0)
      );

    for (let i = 0; i < prioritizedOpportunities.length; i++) {
      const opportunity = prioritizedOpportunities[i];
      const publishDate = new Date(startDate);
      publishDate.setDate(startDate.getDate() + (i * 3)); // Space out by 3 days

      const status = generatedContent.some(content => 
        content.title === opportunity.title
      ) ? 'ready' : 'planned';

      calendar.push({
        title: opportunity.title,
        target_keyword: opportunity.target_keyword,
        content_type: opportunity.content_type,
        priority: opportunity.priority_score || 50,
        estimated_traffic: opportunity.estimated_traffic || 0,
        suggested_publish_date: publishDate.toISOString(),
        status
      });
    }

    return calendar;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  private async generateContentTitle(targetKeyword: string, contentType: string, context: AgentContext): Promise<string> {
    const templates = this.getContentTypeTemplates(contentType);
    const template = templates[Math.floor(Math.random() * templates.length)];
    return template.replace('{keyword}', targetKeyword);
  }

  private async generateContentBrief(targetKeyword: string, contentType: string, context: AgentContext): Promise<string> {
    const briefs = {
      blog: `Comprehensive blog post targeting "${targetKeyword}" with actionable insights and industry best practices.`,
      landing: `High-converting landing page for "${targetKeyword}" focused on lead generation and user engagement.`,
      guide: `Step-by-step guide covering "${targetKeyword}" with detailed instructions and practical examples.`,
      faq: `Frequently asked questions about "${targetKeyword}" addressing common user concerns and objections.`,
      product: `Product-focused content for "${targetKeyword}" highlighting features, benefits, and value proposition.`
    };
    
    return briefs[contentType as keyof typeof briefs] || briefs.blog;
  }

  private inferContentType(keyword: string): string {
    const keywordLower = keyword.toLowerCase();
    
    if (keywordLower.includes('how to') || keywordLower.includes('guide') || keywordLower.includes('tutorial')) {
      return 'guide';
    }
    
    if (keywordLower.includes('vs') || keywordLower.includes('comparison') || keywordLower.includes('review')) {
      return 'blog';
    }
    
    if (keywordLower.includes('pricing') || keywordLower.includes('cost') || keywordLower.includes('price')) {
      return 'landing';
    }
    
    if (keywordLower.includes('what is') || keywordLower.includes('faq') || keywordLower.includes('questions')) {
      return 'faq';
    }
    
    return 'blog'; // Default
  }

  private calculatePriority(keyword: string, existingCoverage: number): string {
    // Higher priority for keywords with no coverage
    if (existingCoverage === 0) {
      return Math.random() > 0.5 ? 'high' : 'critical';
    }
    
    if (existingCoverage === 1) {
      return 'medium';
    }
    
    return 'low';
  }

  private calculatePriorityScore(gap: any): number {
    const priorityMap = { critical: 100, high: 80, medium: 60, low: 40 };
    const baseScore = priorityMap[gap.priority as keyof typeof priorityMap] || 50;
    
    // Adjust based on gap type
    const gapTypeBonus = gap.gap_type === 'no_coverage' ? 20 : 10;
    
    return Math.min(100, baseScore + gapTypeBonus);
  }

  private calculateDifficultyScore(keyword: string): number {
    // Simulate difficulty calculation based on keyword characteristics
    const wordCount = keyword.split(' ').length;
    const hasCommercialIntent = /buy|price|cost|purchase/.test(keyword.toLowerCase());
    
    let difficulty = 30; // Base difficulty
    
    if (wordCount === 1) difficulty += 30; // Single words are harder
    if (wordCount > 4) difficulty -= 10; // Long-tail is easier
    if (hasCommercialIntent) difficulty += 20; // Commercial terms are competitive
    
    return Math.min(100, Math.max(10, difficulty));
  }

  private estimateTrafficPotential(keyword: string): number {
    // Simulate traffic estimation
    const wordCount = keyword.split(' ').length;
    const baseTraffic = Math.floor(Math.random() * 1000) + 100;
    
    // Long-tail keywords typically have lower but more targeted traffic
    const multiplier = wordCount > 3 ? 0.5 : 1.5;
    
    return Math.floor(baseTraffic * multiplier);
  }

  private generateOpportunityReason(keyword: string, existingCoverage: number): string {
    if (existingCoverage === 0) {
      return `No existing content targets "${keyword}" - significant opportunity for new traffic`;
    }
    
    if (existingCoverage === 1) {
      return `Limited content for "${keyword}" - opportunity to expand coverage`;
    }
    
    return `Additional content for "${keyword}" could improve topical authority`;
  }

  private getContentTypeTemplates(contentType: string): string[] {
    const templates = {
      blog: [
        'Complete Guide to {keyword}',
        'How to Master {keyword} in 2024',
        'Top 10 Tips for {keyword}',
        '{keyword}: Everything You Need to Know',
        'Best Practices for {keyword}'
      ],
      landing: [
        'Professional {keyword} Services',
        'Get Started with {keyword} Today',
        'The Best {keyword} Solution',
        'Transform Your Business with {keyword}'
      ],
      guide: [
        'Step-by-Step {keyword} Tutorial',
        'Beginner\'s Guide to {keyword}',
        'Advanced {keyword} Techniques',
        'Complete {keyword} Walkthrough'
      ],
      faq: [
        'Frequently Asked Questions About {keyword}',
        '{keyword} FAQ: Common Questions Answered',
        'Everything You Want to Know About {keyword}'
      ],
      product: [
        '{keyword} Product Overview',
        'Features and Benefits of {keyword}',
        'Why Choose Our {keyword} Solution'
      ]
    };

    return templates[contentType as keyof typeof templates] || templates.blog;
  }

  private countWords(content: string): number {
    return content.trim().split(/\s+/).length;
  }

  private calculateReadabilityScore(content: string): number {
    // Simple readability calculation
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.trim().split(/\s+/);
    const avgWordsPerSentence = words.length / sentences.length;
    
    // Lower score for longer sentences (harder to read)
    let score = 100 - Math.min(50, avgWordsPerSentence * 2);
    
    return Math.round(Math.max(0, score));
  }

  private calculateSEOScore(content: string, targetKeyword: string): number {
    const contentLower = content.toLowerCase();
    const keywordLower = targetKeyword.toLowerCase();
    
    let score = 0;
    
    // Keyword in title area (first 100 chars)
    if (contentLower.substring(0, 100).includes(keywordLower)) {
      score += 30;
    }
    
    // Keyword density (should be 1-3%)
    const keywordCount = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length;
    const wordCount = content.split(/\s+/).length;
    const density = (keywordCount / wordCount) * 100;
    
    if (density >= 1 && density <= 3) {
      score += 40;
    } else if (density > 0) {
      score += 20;
    }
    
    // Content length (800+ words gets bonus)
    if (wordCount >= 800) {
      score += 30;
    } else if (wordCount >= 400) {
      score += 20;
    }
    
    return Math.min(100, score);
  }

  private generateMetaDescription(content: string, targetKeyword: string): string {
    // Extract first meaningful sentence containing the keyword
    const sentences = content.split(/[.!?]+/);
    const keywordSentence = sentences.find(s => 
      s.toLowerCase().includes(targetKeyword.toLowerCase())
    );
    
    if (keywordSentence) {
      const cleaned = keywordSentence.trim();
      return cleaned.length <= 160 ? cleaned : cleaned.substring(0, 157) + '...';
    }
    
    // Fallback: use first sentence
    const firstSentence = sentences[0]?.trim() || '';
    return firstSentence.length <= 160 ? firstSentence : firstSentence.substring(0, 157) + '...';
  }

  private deduplicateAndPrioritizeOpportunities(opportunities: any[]): any[] {
    // Remove duplicates based on target keyword
    const seen = new Set<string>();
    const unique = opportunities.filter(opp => {
      const key = opp.target_keyword.toLowerCase();
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });

    // Sort by priority score
    return unique.sort((a, b) => (b.priority_score || 0) - (a.priority_score || 0));
  }

  // =====================================================
  // DATABASE OPERATIONS
  // =====================================================

  private async saveContentOpportunities(
    websiteId: string, 
    opportunities: any[], 
    generatedContent: GeneratedContent[],
    context: AgentContext
  ): Promise<void> {
    // Save content opportunities first
    for (const opportunity of opportunities) {
      try {
        const opportunityId = await this.insertContentOpportunity(websiteId, opportunity, context);
        
        // If we have generated content for this opportunity, save it to pseo_content_items
        const generatedForThisOpportunity = generatedContent.find(
          content => content.title === opportunity.title
        );

        if (generatedForThisOpportunity && opportunityId) {
          await this.insertContentItem(
            websiteId, 
            opportunityId,
            generatedForThisOpportunity, 
            context
          );
          
          // Update opportunity status to indicate content was generated
          await this.updateOpportunityStatus(opportunityId, 'generated', context);
        }
      } catch (error) {
        context.logger.error(`Failed to save opportunity: ${opportunity.title}`, error as Error);
      }
    }
  }

  private async insertContentOpportunity(
    websiteId: string, 
    opportunity: any, 
    context: AgentContext
  ): Promise<string | null> {
    const sql = `
      INSERT INTO pseo_content_opportunities (
        website_id, target_keyword, content_type, title, content_brief,
        target_word_count, priority, difficulty_score, estimated_traffic, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT (website_id, title) DO UPDATE SET
        content_brief = EXCLUDED.content_brief,
        priority = EXCLUDED.priority,
        difficulty_score = EXCLUDED.difficulty_score,
        estimated_traffic = EXCLUDED.estimated_traffic,
        updated_at = NOW()
      RETURNING id
    `;

    const params = [
      websiteId,
      opportunity.target_keyword,
      opportunity.content_type,
      opportunity.title,
      opportunity.content_brief,
      opportunity.target_word_count || 800,
      opportunity.priority,
      opportunity.difficulty_score,
      opportunity.estimated_traffic,
      'identified'
    ];

    try {
      const result = await context.tools.database.query(sql, params);
      return (result[0] as any)?.id || null;
    } catch (error) {
      context.logger.error('Failed to insert content opportunity', error as Error);
      return null;
    }
  }

  private async insertContentItem(
    websiteId: string,
    opportunityId: string,
    generatedContent: GeneratedContent,
    context: AgentContext
  ): Promise<void> {
    const slug = this.generateSlug(generatedContent.title);
    const contentHtml = await this.convertMarkdownToHtml(generatedContent.content);

    const sql = `
      INSERT INTO pseo_content_items (
        website_id, opportunity_id, title, slug, content_markdown, content_html,
        meta_description, target_keywords, content_type, word_count,
        ai_generated, generated_by_agent, ai_model_used, seo_score, 
        readability_score, status, author
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON CONFLICT (website_id, slug) DO UPDATE SET
        content_markdown = EXCLUDED.content_markdown,
        content_html = EXCLUDED.content_html,
        meta_description = EXCLUDED.meta_description,
        target_keywords = EXCLUDED.target_keywords,
        word_count = EXCLUDED.word_count,
        seo_score = EXCLUDED.seo_score,
        readability_score = EXCLUDED.readability_score,
        updated_at = NOW()
    `;

    const params = [
      websiteId,
      opportunityId,
      generatedContent.title,
      slug,
      generatedContent.content,
      contentHtml,
      generatedContent.meta_description,
      generatedContent.target_keywords,
      generatedContent.content_type,
      generatedContent.word_count,
      true, // ai_generated
      'ContentGenerationAgent', // generated_by_agent
      'gpt-4o', // ai_model_used
      generatedContent.seo_score,
      generatedContent.readability_score,
      'draft', // status
      'AI Agent' // author
    ];

    await context.tools.database.query(sql, params);
  }

  private async updateOpportunityStatus(
    opportunityId: string,
    status: string,
    context: AgentContext
  ): Promise<void> {
    const sql = `
      UPDATE pseo_content_opportunities 
      SET status = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await context.tools.database.query(sql, [status, opportunityId]);
  }

  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .substring(0, 100);
  }

  private async convertMarkdownToHtml(markdown: string): Promise<string> {
    // Simple markdown to HTML conversion
    // In a real implementation, you'd use a proper markdown parser
    return markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/\n/gim, '<br>');
  }

  // =====================================================
  // REAL ANALYTICS DATA INTEGRATION METHODS
  // =====================================================

  /**
   * Enhance content gaps with real Google Analytics data
   */
  private async enhanceWithAnalyticsData(
    contentGaps: any[],
    context: AgentContext
  ): Promise<any[]> {
    try {
      // Get configured generator provider for analytics
      const providerConfig = providerConfigService.getProviderForFunction('generator');
      
      // Check if Google Analytics is configured
      const analyticsService = new GoogleAnalyticsService({
        clientId: process.env.GOOGLE_ANALYTICS_CLIENT_ID || '',
        clientSecret: process.env.GOOGLE_ANALYTICS_CLIENT_SECRET || '',
        redirectUri: process.env.GOOGLE_ANALYTICS_REDIRECT_URI || '',
        refreshToken: process.env.GOOGLE_ANALYTICS_REFRESH_TOKEN || '',
        propertyId: process.env.GOOGLE_ANALYTICS_PROPERTY_ID || ''
      });

      if (!analyticsService.isAuthenticatedStatus()) {
        context.logger.info('Google Analytics not configured, using basic content gap analysis');
        return contentGaps;
      }

      context.logger.info('Enhancing content gaps with Google Analytics data');

      const enhancedGaps: any[] = [];
      
      for (const gap of contentGaps) {
        try {
          // Get traffic data for gap-related keywords
          const trafficData = await this.getTrafficDataForKeyword(
            gap.target_keyword,
            analyticsService,
            context
          );

          // Get content performance data
          const contentPerformance = await this.getContentPerformanceData(
            gap.existing_content_url,
            analyticsService,
            context
          );

          enhancedGaps.push({
            ...gap,
            analytics_data: {
              traffic_potential: trafficData.sessions || 0,
              bounce_rate: trafficData.bounceRate || 0,
              avg_session_duration: trafficData.avgSessionDuration || 0,
              conversion_rate: 0, // Not available in traffic sources
              existing_content_performance: contentPerformance,
              opportunity_score: this.calculateOpportunityScore(trafficData, contentPerformance),
              enhanced_with_analytics: true
            }
          });

        } catch (error) {
          context.logger.debug(`Failed to enhance gap with analytics: ${gap.target_keyword}`, { error });
          enhancedGaps.push({
            ...gap,
            analytics_data: null,
            enhanced_with_analytics: false
          });
        }
      }

      context.logger.info(`Enhanced ${enhancedGaps.length} content gaps with analytics data`);
      return enhancedGaps;

    } catch (error) {
      context.logger.warn('Failed to enhance with analytics data', { error });
      return contentGaps;
    }
  }

  /**
   * Get traffic data for a specific keyword from Google Analytics
   */
  private async getTrafficDataForKeyword(
    keyword: string,
    analyticsService: GoogleAnalyticsService,
    context: AgentContext
  ): Promise<any> {
    try {
      // Get traffic data for keyword-related queries
      const trafficReport = await analyticsService.getTrafficSources(
        new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90 days ago as string
        new Date().toISOString().split('T')[0], // today as string
        50 // limit parameter
      );

      // Filter for keyword-related traffic (simplified approach)
      const keywordTraffic = trafficReport.find((item: any) => 
        item.source && item.source.toLowerCase().includes(keyword.toLowerCase())
      );

      return {
        sessions: keywordTraffic?.sessions || 0,
        bounceRate: keywordTraffic?.bounceRate || 0,
        avgSessionDuration: keywordTraffic?.avgSessionDuration || 0
      };

    } catch (error) {
      context.logger.debug(`Failed to get traffic data for keyword: ${keyword}`, { error });
      return {
        sessions: 0,
        bounceRate: 0,
        avgSessionDuration: 0
      };
    }
  }

  /**
   * Get content performance data for existing content
   */
  private async getContentPerformanceData(
    contentUrl: string | undefined,
    analyticsService: GoogleAnalyticsService,
    context: AgentContext
  ): Promise<any> {
    if (!contentUrl) {
      return null;
    }

    try {
      // Get page performance data using getPageAnalytics method
      const pageReport = await analyticsService.getPageAnalytics(
        contentUrl,
        new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        new Date().toISOString().split('T')[0]
      );

      return {
        page_views: pageReport.pageviews || 0,
        unique_page_views: pageReport.uniquePageviews || 0,
        avg_time_on_page: pageReport.avgTimeOnPage || 0,
        bounce_rate: pageReport.bounceRate || 0,
        exit_rate: pageReport.exitRate || 0
      };

    } catch (error) {
      context.logger.debug(`Failed to get content performance for: ${contentUrl}`, { error });
      return null;
    }
  }

  /**
   * Calculate opportunity score based on analytics data
   */
  private calculateOpportunityScore(trafficData: any, contentPerformance: any): number {
    let score = 50; // Base score

    // Traffic potential bonus
    if (trafficData.sessions > 1000) score += 20;
    else if (trafficData.sessions > 100) score += 10;

    // Low bounce rate bonus (good engagement)
    if (trafficData.bounceRate < 0.3) score += 15;
    else if (trafficData.bounceRate < 0.5) score += 10;

    // Session duration bonus
    if (trafficData.avgSessionDuration > 180) score += 15; // 3+ minutes
    else if (trafficData.avgSessionDuration > 60) score += 10; // 1+ minute

    // Existing content performance penalty
    if (contentPerformance && contentPerformance.page_views > 0) {
      if (contentPerformance.bounce_rate > 0.7) score += 20; // High bounce = opportunity to improve
      else if (contentPerformance.bounce_rate > 0.5) score += 10;
      
      if (contentPerformance.avg_time_on_page < 30) score += 15; // Low engagement = opportunity
    }

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Enhance content opportunities with competitor performance data
   */
  private async enhanceWithCompetitorAnalytics(
    opportunities: any[],
    context: AgentContext
  ): Promise<any[]> {
    // This would integrate with competitor analysis tools
    // For now, return opportunities as-is
    context.logger.info('Competitor analytics enhancement not yet implemented');
    return opportunities;
  }
} 