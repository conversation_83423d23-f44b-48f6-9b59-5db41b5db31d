import React, { useState, useEffect } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Mail,
  Phone,
  Building,
  User,
  Filter,
  Download,
  Upload,
  RefreshCw,
  MoreHorizontal,
  Eye,
  MapPin,
  Calendar,
  Tag,
  ChevronDown,
  SortAsc,
  SortDesc,
} from "lucide-react";
import { crmService } from "../services/crmService";
import { Contact, ContactFilters, PaginatedResponse } from "../types";
import ContactForm from "../components/ContactForm";
import Modal from "../components/Modal";
import CRMLayout from "../components/CRMLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../base/components/ui/card";
import { Button } from "../../../base/components/ui/button";
import { Badge } from "../../../base/components/ui/badge";
import { Input } from "../../../base/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../base/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../base/components/ui/select";
import { Avatar, AvatarFallback } from "../../../base/components/ui/avatar";

const ContactManagement: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  // Removed viewMode - using table view only for better 1000+ contact handling
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Optimized for large datasets
  const pageSize = 50; // Increased page size for better performance

  useEffect(() => {
    loadContacts();
  }, [currentPage, searchTerm, selectedTags]);

  const loadContacts = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: ContactFilters = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        tags: selectedTags.length > 0 ? selectedTags : undefined,
      };

      const response: PaginatedResponse<Contact> = await crmService.getContacts(
        filters
      );
      setContacts(response.data);
      setTotal(response.total);
    } catch (err) {
      console.error("Error loading contacts:", err);
      setError(err instanceof Error ? err.message : "Failed to load contacts");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateContact = async (contactData: any) => {
    try {
      await crmService.createContact(contactData);
      setShowCreateModal(false);
      loadContacts();
    } catch (err) {
      console.error("Error creating contact:", err);
      throw err;
    }
  };

  const handleEditContact = async (contactData: any) => {
    if (!selectedContact?.id) return;

    try {
      await crmService.updateContact(selectedContact.id, contactData);
      setShowEditModal(false);
      setSelectedContact(null);
      loadContacts();
    } catch (err) {
      console.error("Error updating contact:", err);
      throw err;
    }
  };

  const handleDeleteContact = async () => {
    if (!selectedContact?.id) return;

    try {
      await crmService.deleteContact(selectedContact.id);
      setShowDeleteModal(false);
      setSelectedContact(null);
      loadContacts();
    } catch (err) {
      console.error("Error deleting contact:", err);
      setError(err instanceof Error ? err.message : "Failed to delete contact");
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    loadContacts();
  };

  const openEditModal = (contact: Contact) => {
    setSelectedContact(contact);
    setShowEditModal(true);
  };

  const openDeleteModal = (contact: Contact) => {
    setSelectedContact(contact);
    setShowDeleteModal(true);
  };

  // Bulk operations for large datasets
  const handleBulkDelete = async () => {
    if (selectedContacts.length === 0) return;

    try {
      setLoading(true);
      // In a real implementation, you'd call a bulk delete API
      // await crmService.bulkDeleteContacts(selectedContacts);
      setSelectedContacts([]);
      await loadContacts();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to delete contacts"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBulkExport = () => {
    if (selectedContacts.length === 0) return;

    // In a real implementation, you'd export the selected contacts
    console.log("Exporting contacts:", selectedContacts);
  };

  const totalPages = Math.ceil(total / pageSize);

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Contact Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Manage your customer contacts and relationships
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={loadContacts}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <MoreHorizontal className="h-4 w-4" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem className="gap-2">
                    <Upload className="h-4 w-4" />
                    Import Contacts
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Download className="h-4 w-4" />
                    Export Contacts
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="gap-2">
                    <Filter className="h-4 w-4" />
                    Advanced Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Contact
              </Button>
            </div>
          </div>

          {/* Search and Filters - Optimized for 1000+ Contacts */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col lg:flex-row gap-3">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      type="text"
                      placeholder="Search 1000+ contacts by name, email, phone, or company..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full_name">Name A-Z</SelectItem>
                      <SelectItem value="created_at">Date Added</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="company_id">Company</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                    }
                    className="px-2"
                  >
                    {sortOrder === "asc" ? (
                      <SortAsc className="h-4 w-4" />
                    ) : (
                      <SortDesc className="h-4 w-4" />
                    )}
                  </Button>

                  <Button
                    type="button"
                    onClick={handleSearch}
                    size="sm"
                    className="gap-2"
                  >
                    <Search className="h-4 w-4" />
                    Search
                  </Button>
                </div>
              </div>

              {/* Bulk Actions Bar */}
              {selectedContacts.length > 0 && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-blue-900">
                      {selectedContacts.length} contact
                      {selectedContacts.length > 1 ? "s" : ""} selected
                    </span>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="gap-2">
                        <Download className="h-4 w-4" />
                        Export Selected
                      </Button>
                      <Button variant="outline" size="sm" className="gap-2">
                        <Tag className="h-4 w-4" />
                        Add Tags
                      </Button>
                      <Button variant="destructive" size="sm" className="gap-2">
                        <Trash2 className="h-4 w-4" />
                        Delete Selected
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedContacts([])}
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Contacts
                </CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Email
                </CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contacts.filter((c) => c.email).length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Phone
                </CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contacts.filter((c) => c.phone || c.mobile).length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Company
                </CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contacts.filter((c) => c.company_id).length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Error Display */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                    <User className="w-6 h-6 text-destructive" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-destructive">
                      Error Loading Contacts
                    </h3>
                    <p className="text-muted-foreground mt-1">{error}</p>
                  </div>
                  <Button onClick={loadContacts} variant="destructive">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Optimized Contact List for 1000+ Contacts */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Contacts ({total.toLocaleString()})
                </CardTitle>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    Page {currentPage} of {totalPages}
                  </span>
                  <span>•</span>
                  <span>{pageSize} per page</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-4">
                  <div className="space-y-2">
                    {[...Array(8)].map((_, i) => (
                      <div
                        key={i}
                        className="flex items-center space-x-3 p-3 border rounded"
                      >
                        <div className="w-8 h-8 bg-muted rounded-full animate-pulse"></div>
                        <div className="flex-1 space-y-1">
                          <div className="h-3 bg-muted rounded w-1/4 animate-pulse"></div>
                          <div className="h-2 bg-muted rounded w-1/3 animate-pulse"></div>
                        </div>
                        <div className="h-6 w-16 bg-muted rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : contacts.length === 0 ? (
                <div className="p-12 text-center">
                  <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No contacts found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm
                      ? `No contacts match "${searchTerm}". Try adjusting your search.`
                      : "Get started by creating your first contact."}
                  </p>
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Contact
                  </Button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="border-b bg-muted/20">
                      <tr className="text-left">
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          <input
                            type="checkbox"
                            checked={
                              selectedContacts.length === contacts.length &&
                              contacts.length > 0
                            }
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedContacts(contacts.map((c) => c.id));
                              } else {
                                setSelectedContacts([]);
                              }
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Contact
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Email & Phone
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Company
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Job Title
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Tags
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Date Added
                        </th>
                        <th className="p-4 text-xs font-semibold uppercase tracking-wider text-muted-foreground">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {contacts.map((contact) => (
                        <tr
                          key={contact.id}
                          className="hover:bg-muted/50 transition-colors group"
                        >
                          <td className="p-4">
                            <input
                              type="checkbox"
                              checked={selectedContacts.includes(contact.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedContacts([
                                    ...selectedContacts,
                                    contact.id,
                                  ]);
                                } else {
                                  setSelectedContacts(
                                    selectedContacts.filter(
                                      (id) => id !== contact.id
                                    )
                                  );
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                                  {contact.full_name
                                    ?.split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase() || "?"}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-semibold text-sm text-foreground">
                                  {contact.full_name}
                                </div>
                                {contact.salutation && (
                                  <div className="text-xs text-muted-foreground">
                                    {contact.salutation}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="space-y-1">
                              {contact.email && (
                                <div className="flex items-center gap-2">
                                  <Mail className="h-3 w-3 text-muted-foreground" />
                                  <a
                                    href={`mailto:${contact.email}`}
                                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {contact.email}
                                  </a>
                                </div>
                              )}
                              {(contact.phone || contact.mobile) && (
                                <div className="flex items-center gap-2">
                                  <Phone className="h-3 w-3 text-muted-foreground" />
                                  <a
                                    href={`tel:${
                                      contact.phone || contact.mobile
                                    }`}
                                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {contact.phone || contact.mobile}
                                  </a>
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            {contact.crm_companies ? (
                              <div className="flex items-center gap-2">
                                <Building className="h-3 w-3 text-muted-foreground" />
                                <span className="text-sm font-medium">
                                  {contact.crm_companies.name}
                                </span>
                              </div>
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                -
                              </span>
                            )}
                          </td>
                          <td className="p-4">
                            {contact.job_title ? (
                              <Badge
                                variant="outline"
                                className="text-xs font-medium"
                              >
                                {contact.job_title}
                              </Badge>
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                -
                              </span>
                            )}
                          </td>
                          <td className="p-4">
                            <div className="flex flex-wrap gap-1">
                              {contact.tags && contact.tags.length > 0 ? (
                                <>
                                  {contact.tags
                                    .slice(0, 2)
                                    .map((tag, index) => (
                                      <Badge
                                        key={index}
                                        variant="secondary"
                                        className="text-xs"
                                      >
                                        {tag}
                                      </Badge>
                                    ))}
                                  {contact.tags.length > 2 && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      +{contact.tags.length - 2}
                                    </Badge>
                                  )}
                                </>
                              ) : (
                                <span className="text-sm text-muted-foreground">
                                  -
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm text-muted-foreground">
                                {contact.created_at
                                  ? new Date(
                                      contact.created_at
                                    ).toLocaleDateString()
                                  : "-"}
                              </span>
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => openEditModal(contact)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => openEditModal(contact)}
                                    className="gap-2"
                                  >
                                    <Eye className="h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => openEditModal(contact)}
                                    className="gap-2"
                                  >
                                    <Edit className="h-4 w-4" />
                                    Edit Contact
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => openDeleteModal(contact)}
                                    className="gap-2 text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    Delete Contact
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>

            {/* Enhanced Pagination for Large Datasets */}
            {totalPages > 1 && (
              <div className="px-4 py-3 border-t bg-muted/20">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                  <div className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * pageSize + 1} to{" "}
                    {Math.min(currentPage * pageSize, total)} of{" "}
                    {total.toLocaleString()} contacts
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="px-2"
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>

                    {/* Page Numbers - Smart Display for Large Datasets */}
                    <div className="flex items-center gap-1">
                      {currentPage > 3 && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setCurrentPage(1)}
                          >
                            1
                          </Button>
                          {currentPage > 4 && (
                            <span className="text-muted-foreground">...</span>
                          )}
                        </>
                      )}

                      {Array.from(
                        { length: Math.min(5, totalPages) },
                        (_, i) => {
                          const pageNum =
                            Math.max(
                              1,
                              Math.min(totalPages - 4, currentPage - 2)
                            ) + i;
                          if (pageNum <= totalPages) {
                            return (
                              <Button
                                key={pageNum}
                                variant={
                                  pageNum === currentPage ? "default" : "ghost"
                                }
                                size="sm"
                                onClick={() => setCurrentPage(pageNum)}
                                className="w-8"
                              >
                                {pageNum}
                              </Button>
                            );
                          }
                          return null;
                        }
                      )}

                      {currentPage < totalPages - 2 && (
                        <>
                          {currentPage < totalPages - 3 && (
                            <span className="text-muted-foreground">...</span>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setCurrentPage(totalPages)}
                          >
                            {totalPages}
                          </Button>
                        </>
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="px-2"
                    >
                      Last
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Create Contact Modal */}
          <Modal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            title="Create New Contact"
          >
            <ContactForm
              onSubmit={handleCreateContact}
              onCancel={() => setShowCreateModal(false)}
            />
          </Modal>

          {/* Edit Contact Modal */}
          <Modal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            title="Edit Contact"
          >
            {selectedContact && (
              <ContactForm
                contact={selectedContact}
                onSubmit={handleEditContact}
                onCancel={() => setShowEditModal(false)}
              />
            )}
          </Modal>

          {/* Delete Confirmation Modal */}
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Contact"
          >
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                  <Trash2 className="w-6 h-6 text-destructive" />
                </div>
                <div>
                  <h3 className="font-semibold">Delete Contact</h3>
                  <p className="text-muted-foreground">
                    Are you sure you want to delete{" "}
                    <strong>{selectedContact?.full_name}</strong>? This action
                    cannot be undone.
                  </p>
                </div>
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteContact}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Contact
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </CRMLayout>
  );
};

export default ContactManagement;
