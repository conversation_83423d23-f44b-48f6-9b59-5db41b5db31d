{"id": "iLpBIRuhpWToO22N", "meta": {"instanceId": "e8ec316b54e91908f34cbfdc330e5d1d5e97aa0ea8f7277c00d8a8a3892c9983", "templateCredsSetupCompleted": true}, "name": "🤖 On-Page SEO Audit", "tags": [{"id": "TF9zcHoRnyCYBNVV", "name": "SEO", "createdAt": "2025-03-14T12:08:26.948Z", "updatedAt": "2025-03-14T12:08:26.948Z"}], "nodes": [{"id": "f4a971be-a961-4ad6-b38d-830c5fca5407", "name": "Landing Page Url", "type": "n8n-nodes-base.formTrigger", "position": [-180, 0], "webhookId": "afe067a5-4878-4c9d-b746-691f77190f54", "parameters": {"options": {}, "formTitle": "Conversion Rate Optimizer", "formFields": {"values": [{"fieldLabel": "Landing Page Url", "placeholder": "https://yuzuu.co", "requiredField": true}]}, "formDescription": "Your Landing Page is Leaking Sales—Fix It Now"}, "typeVersion": 2.2}, {"id": "e280139f-94b8-49dc-91e7-c6ffa0c04716", "name": "Scrape Website", "type": "n8n-nodes-base.httpRequest", "position": [20, 0], "parameters": {"url": "={{ $json['Landing Page Url'] }}", "options": {}}, "typeVersion": 4.2}, {"id": "de9ff0da-4ef9-4878-af0d-5733e010402c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [320, 20], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "MtyWeuRTqwi3Yx9H", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "********-4b1c-42ad-969c-efbb605be9e5", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [360, 400], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "MtyWeuRTqwi3Yx9H", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "0f135a2d-156c-43ee-b254-581c7d543a8c", "name": "Content Audit", "type": "@n8n/n8n-nodes-langchain.agent", "position": [380, 200], "parameters": {"text": "=You are the best SEO Manager in the country—a world-class expert in optimizing websites to rank on Google.\n\nIn this task, you will analyze the content of the webpage and perform a detailed and structured SEO Content Audit.\n\nAudit Structure\nYou will divide your audit in 2 parts:\n- The first part is the Analysis\n- The second is the Recommendations\n\nIn the Analysis, you will include:\n- Content Quality Assessment – Evaluate the content's overall quality, accuracy, and relevance to the target audience.\n- Keyword Research and Analysis – Identify primary and secondary keywords, keyword density, and keyword placement strategies.\n- Readability Analysis – Assess the content's readability score using metrics such as Flesch-Kincaid Grade Level, Flesch Reading Ease, and Gunning-Fog Index.\n\nIn the Recommendations, you will present your recommendations and actionable suggestions in clear, organized bullet points. Recommendations must improve the rankings in Google but also the user engagement. \n\nEnsure the output is properly formatted, clean, and highly readable. Do not include any introductory or explanatory text—only the audit findings.\n\nHere is the content of my landing page: {{ $json.data }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "b693e35c-c0d4-4202-8c5e-2a5646a16cc4", "name": "Technical Audit", "type": "@n8n/n8n-nodes-langchain.agent", "position": [380, -200], "parameters": {"text": "=You are the best SEO Manager in the country—a world-class expert in optimizing websites to rank on Google.\nIn this task, you will analyze the HTML code of a webpage and perform a detailed and structured On-Page Technical SEO Audit.\n\nAudit Structure\nYou will review all technical SEO aspects of the page. Once completed, you will present your findings and recommendations in clear, organized bullet points, categorized into three sections:\n- Critical Issues – Must be fixed immediately.\n- Quick Wins – Easy fixes with a big impact.\n- Opportunities for Improvement – Require more effort but offer potential benefits.\n\nEnsure the output is properly formatted, clean, and highly readable. Do not include any introductory or explanatory text—only the audit findings.\n\nHere is the content of my landing page: {{ $json.data }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "3d172f93-7d94-4a43-9403-5cec799bbe47", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [880, 0], "parameters": {}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "2081bf62-0e47-497e-8a3e-d30d330f6a9d", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [1080, 0], "parameters": {"options": {}, "fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}]}}, "typeVersion": 1}, {"id": "e1cfc16e-e0dc-4298-9b94-ffb7f23b45aa", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [1280, 0], "parameters": {"mode": "markdownToHtml", "options": {}, "markdown": "=# On-Page Technical Audit\n{{ $json.output[0] }}\n\n# On-Page SEO Content Audit\n{{ $json.output[1] }}"}, "typeVersion": 1}, {"id": "7dc41215-e276-439c-be11-92278b1c3a60", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1360, -160], "parameters": {"color": 3, "width": 360, "height": 100, "content": "## Send Email \nConnect your credentials & Easily send emails from a Gmail address. "}, "typeVersion": 1}, {"id": "28aea6bd-beef-4116-97c2-e8b88e96d5ac", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [320, -380], "parameters": {"color": 3, "width": 420, "height": 140, "content": "## Open AI Setup\n- Add your credentials\n- Select o1 model for (way) better results. \n- One run = one page audit = around $0.3 with o1"}, "typeVersion": 1}, {"id": "3242a0c3-4439-4ad1-8185-47185046080d", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1480, 0], "webhookId": "2979e4dc-1689-447e-8cd4-eb907b4eedf4", "parameters": {"sendTo": "<EMAIL>", "message": "={{ $json.data }}", "options": {}, "subject": "=On-Page SEO Audit -  {{ $('Landing Page Url').item.json['Landing Page Url'] }}"}, "credentials": {"gmailOAuth2": {"id": "9EELWJ0jA3PIbx13", "name": "Gmail account"}}, "typeVersion": 2.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "bc4ac79c-71a0-4dae-805d-55b682b0c199", "connections": {"Merge": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Markdown": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Content Audit": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Scrape Website": {"main": [[{"node": "Content Audit", "type": "main", "index": 0}, {"node": "Technical Audit", "type": "main", "index": 0}]]}, "Technical Audit": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Landing Page Url": {"main": [[{"node": "Scrape Website", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Technical Audit", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Content Audit", "type": "ai_languageModel", "index": 0}]]}}}