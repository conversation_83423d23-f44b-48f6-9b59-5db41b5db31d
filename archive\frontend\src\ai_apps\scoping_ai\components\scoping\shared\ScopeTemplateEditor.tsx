import React, { useState } from 'react';
import { ScopeTemplate } from '../../types/scoping';

interface ScopeTemplateEditorProps {
  template: ScopeTemplate;
  onSave: (template: ScopeTemplate) => void;
  onCancel: () => void;
}

const ScopeTemplateEditor: React.FC<ScopeTemplateEditorProps> = ({ 
  template, 
  onSave, 
  onCancel 
}) => {
  const [editedTemplate, setEditedTemplate] = useState<ScopeTemplate>({...template});
  const [deliverables, setDeliverables] = useState<string[]>(
    template.content?.deliverables || []
  );
  const [newDeliverable, setNewDeliverable] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedTemplate(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedTemplate(prev => ({
      ...prev,
      content: {
        ...prev.content,
        [name]: value
      }
    }));
  };

  const handleAddDeliverable = () => {
    if (!newDeliverable.trim()) return;
    
    const updatedDeliverables = [...deliverables, newDeliverable.trim()];
    setDeliverables(updatedDeliverables);
    setNewDeliverable('');
    
    setEditedTemplate(prev => ({
      ...prev,
      content: {
        ...prev.content,
        deliverables: updatedDeliverables
      }
    }));
  };

  const handleRemoveDeliverable = (index: number) => {
    const updatedDeliverables = deliverables.filter((_, i) => i !== index);
    setDeliverables(updatedDeliverables);
    
    setEditedTemplate(prev => ({
      ...prev,
      content: {
        ...prev.content,
        deliverables: updatedDeliverables
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...editedTemplate,
      updatedAt: new Date()
    });
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">
        {template.id.startsWith('new') ? 'Create Scope Template' : 'Edit Scope Template'}
      </h2>
      
      <form onSubmit={handleSubmit}>
        {/* Basic information */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">Basic Information</h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
              <input
                type="text"
                name="name"
                value={editedTemplate.name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                name="description"
                value={editedTemplate.description || ''}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded"
                rows={3}
              />
            </div>
          </div>
        </div>
        
        {/* Content */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">Scope Content</h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Project Type</label>
              <input
                type="text"
                name="projectType"
                value={editedTemplate.content?.projectType || ''}
                onChange={handleContentChange}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Timeline</label>
              <input
                type="text"
                name="timeline"
                value={editedTemplate.content?.timeline || ''}
                onChange={handleContentChange}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
            
            {/* Deliverables */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Deliverables</label>
              <div className="mb-2">
                <div className="flex">
                  <input
                    type="text"
                    value={newDeliverable}
                    onChange={(e) => setNewDeliverable(e.target.value)}
                    className="flex-1 p-2 border border-gray-300 rounded-l"
                    placeholder="Add a deliverable"
                  />
                  <button
                    type="button"
                    onClick={handleAddDeliverable}
                    className="px-4 py-2 bg-blue-600 text-white rounded-r hover:bg-blue-700"
                  >
                    Add
                  </button>
                </div>
              </div>
              
              {deliverables.length > 0 ? (
                <ul className="bg-gray-50 rounded p-2 border border-gray-200">
                  {deliverables.map((deliverable, index) => (
                    <li key={index} className="flex justify-between items-center py-1">
                      <span>{deliverable}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveDeliverable(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm">No deliverables added</p>
              )}
            </div>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Save Template
          </button>
        </div>
      </form>
    </div>
  );
};

export default ScopeTemplateEditor; 