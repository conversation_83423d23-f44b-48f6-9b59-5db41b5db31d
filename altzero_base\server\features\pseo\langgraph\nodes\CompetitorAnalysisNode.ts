// =====================================================
// COMPETITOR ANALYSIS NODE - LANGGRAPH IMPLEMENTATION
// =====================================================

import { BaseNode } from '../types/NodeTypes';
import { PSEOWorkflowState, WorkflowContext, CompetitorData, KeywordData } from '../types/WorkflowState';

export class CompetitorAnalysisNode implements BaseNode {
  name = 'competitor_analysis';
  description = 'Analyzes competitor keywords and identifies opportunities';

  async execute(context: WorkflowContext): Promise<Partial<PSEOWorkflowState>> {
    const { state, tools, logger, config } = context;
    
    logger.info('Starting competitor analysis', {
      website_id: state.website_id,
      domain: state.domain,
      competitor_domains: state.competitor_domains?.length || 0
    });

    try {
      const startTime = Date.now();
      let competitorData: CompetitorData[] = [];
      let apiCallsCount = 0;
      let totalCost = 0;

      // Step 1: Identify competitors if not provided
      const competitors = state.competitor_domains && state.competitor_domains.length > 0
        ? state.competitor_domains
        : await this.identifyCompetitors(state.domain, state.keywords, tools, logger);

      logger.info(`Analyzing ${competitors.length} competitors`);

      // Step 2: Analyze each competitor
      for (const competitorDomain of competitors.slice(0, 5)) { // Limit to 5 competitors
        try {
          const analysis = await this.analyzeCompetitor(
            competitorDomain,
            state.domain,
            state.keywords,
            tools,
            logger
          );
          
          competitorData.push(analysis.data);
          apiCallsCount += analysis.apiCalls;
          totalCost += analysis.cost;
          
        } catch (error) {
          logger.warn(`Failed to analyze competitor ${competitorDomain}:`, error);
        }
      }

      // Step 3: Identify keyword gaps and opportunities
      const keywordOpportunities = await this.identifyKeywordOpportunities(
        state.keywords,
        competitorData,
        tools,
        logger
      );

      const processingTime = Date.now() - startTime;

      logger.info('Competitor analysis completed', {
        competitors_analyzed: competitorData.length,
        opportunities_found: keywordOpportunities.length,
        processing_time: processingTime,
        api_calls: apiCallsCount
      });

      return {
        competitor_data: competitorData,
        keywords: [...state.keywords, ...keywordOpportunities],
        current_step: 'competitor_analysis_completed',
        progress: 70,
        processing_time: (state.processing_time || 0) + processingTime,
        api_calls_made: [
          ...(state.api_calls_made || []),
          {
            provider: 'competitor_analysis_node',
            endpoint: 'competitor_analysis',
            calls_made: apiCallsCount,
            success_rate: 1.0,
            average_response_time: processingTime / Math.max(apiCallsCount, 1),
            cost_estimate: totalCost
          }
        ],
        total_cost: (state.total_cost || 0) + totalCost,
        last_updated: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Competitor analysis failed', error);
      throw error;
    }
  }

  // Identify competitors based on domain and keywords
  private async identifyCompetitors(
    domain: string,
    keywords: KeywordData[],
    tools: any,
    logger: any
  ): Promise<string[]> {
    try {
      // Use AI to identify potential competitors
      const topKeywords = keywords
        .sort((a, b) => b.search_volume - a.search_volume)
        .slice(0, 5)
        .map(k => k.keyword);

      const prompt = `
        Identify 3-5 main competitors for the domain "${domain}" based on these keywords: ${topKeywords.join(', ')}
        
        Consider:
        - Direct competitors in the same industry
        - Companies targeting similar keywords
        - Well-known brands in the space
        
        Return only domain names (without http/https) as a JSON array.
        Example: ["competitor1.com", "competitor2.com", "competitor3.com"]
      `;

      const response = await tools.ai.generateText(prompt, {
        model: 'gpt-4o-mini',
        temperature: 0.3,
        response_format: 'json'
      });

      const competitors = this.extractJsonFromResponse(response);
      return Array.isArray(competitors) ? competitors.slice(0, 5) : [];

    } catch (error) {
      logger.warn('Failed to identify competitors using AI, using fallback method', error);
      
      // Fallback: generate generic competitors based on domain
      const domainParts = domain.replace(/^www\./, '').split('.');
      const baseName = domainParts[0];
      
      return [
        `${baseName}alternative.com`,
        `best${baseName}.com`,
        `${baseName}competitor.com`
      ];
    }
  }

  // Analyze a single competitor
  private async analyzeCompetitor(
    competitorDomain: string,
    ownDomain: string,
    ownKeywords: KeywordData[],
    tools: any,
    logger: any
  ): Promise<{ data: CompetitorData; apiCalls: number; cost: number }> {
    let apiCalls = 0;
    let cost = 0;

    try {
      // Step 1: Get competitor's website content
      const competitorContent = await this.getCompetitorContent(competitorDomain, tools);
      apiCalls++;

      // Step 2: Extract competitor keywords using AI
      const competitorKeywords = await this.extractCompetitorKeywords(
        competitorDomain,
        competitorContent,
        tools,
        logger
      );
      apiCalls++;
      cost += 0.02;

      // Step 3: Calculate ranking overlap
      const rankingOverlap = this.calculateRankingOverlap(ownKeywords, competitorKeywords);

      // Step 4: Calculate authority score (simplified)
      const authorityScore = await this.calculateAuthorityScore(competitorDomain, tools);

      const competitorData: CompetitorData = {
        domain: competitorDomain,
        keywords: competitorKeywords,
        ranking_overlap: rankingOverlap,
        authority_score: authorityScore
      };

      return {
        data: competitorData,
        apiCalls,
        cost
      };

    } catch (error) {
      logger.warn(`Failed to analyze competitor ${competitorDomain}:`, error);
      
      // Return minimal data to prevent workflow failure
      return {
        data: {
          domain: competitorDomain,
          keywords: [],
          ranking_overlap: 0,
          authority_score: 50
        },
        apiCalls,
        cost
      };
    }
  }

  // Get competitor website content
  private async getCompetitorContent(domain: string, tools: any): Promise<string> {
    try {
      const url = domain.startsWith('http') ? domain : `https://${domain}`;
      const content = await tools.http.get(url, { timeout: 15000 });
      
      if (typeof content === 'string') {
        // Extract text content and limit size
        const textContent = content
          .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
          .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();
        
        return textContent.substring(0, 5000); // Limit to 5000 characters
      }
      
      return '';
    } catch (error) {
      console.warn(`Failed to get content for ${domain}:`, error);
      return '';
    }
  }

  // Extract keywords from competitor content using AI
  private async extractCompetitorKeywords(
    domain: string,
    content: string,
    tools: any,
    logger: any
  ): Promise<KeywordData[]> {
    try {
      if (!content || content.length < 100) {
        return [];
      }

      const prompt = `
        Analyze the following website content from ${domain} and extract 10-15 SEO keywords they are likely targeting.
        Focus on:
        - Main products/services mentioned
        - Industry-specific terms
        - Branded keywords
        - Commercial intent keywords
        
        Content: ${content.substring(0, 2000)}...
        
        Return keywords as a JSON array of objects with this structure:
        [
          {
            "keyword": "example keyword",
            "intent": "informational|commercial|transactional|navigational",
            "estimated_volume": 100-10000,
            "estimated_difficulty": 1-100
          }
        ]
      `;

      const response = await tools.ai.generateText(prompt, {
        model: 'gpt-4o-mini',
        temperature: 0.5,
        response_format: 'json'
      });

      const aiKeywords = this.extractJsonFromResponse(response);
      
      return aiKeywords.slice(0, 15).map((item: any) => ({
        keyword: item.keyword,
        search_volume: item.estimated_volume || Math.floor(Math.random() * 3000) + 100,
        keyword_difficulty: item.estimated_difficulty || Math.floor(Math.random() * 80) + 20,
        cpc: Math.random() * 2 + 0.5,
        competition: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
        intent: item.intent || this.inferKeywordIntent(item.keyword),
        trend: ['rising', 'stable', 'declining'][Math.floor(Math.random() * 3)] as any,
        data_source: 'competitor_analysis'
      }));

    } catch (error) {
      logger.warn(`Failed to extract keywords for ${domain}:`, error);
      return [];
    }
  }

  // Calculate ranking overlap between own keywords and competitor keywords
  private calculateRankingOverlap(ownKeywords: KeywordData[], competitorKeywords: KeywordData[]): number {
    if (ownKeywords.length === 0 || competitorKeywords.length === 0) {
      return 0;
    }

    const ownKeywordSet = new Set(ownKeywords.map(k => k.keyword.toLowerCase()));
    const competitorKeywordSet = new Set(competitorKeywords.map(k => k.keyword.toLowerCase()));
    
    const intersection = new Set(Array.from(ownKeywordSet).filter(k => competitorKeywordSet.has(k)));
    const union = new Set([...Array.from(ownKeywordSet), ...Array.from(competitorKeywordSet)]);
    
    return Math.round((intersection.size / union.size) * 100);
  }

  // Calculate authority score (simplified)
  private async calculateAuthorityScore(domain: string, tools: any): Promise<number> {
    try {
      // This is a simplified authority calculation
      // In a real implementation, you might use tools like Moz, Ahrefs, or Semrush
      
      // For now, we'll use a combination of factors:
      // 1. Domain age (estimated)
      // 2. Content quality (estimated from content length)
      // 3. Random factor to simulate real authority metrics
      
      const domainParts = domain.split('.');
      const tld = domainParts[domainParts.length - 1];
      
      let baseScore = 30;
      
      // TLD bonus
      if (tld === 'com') baseScore += 10;
      else if (tld === 'org') baseScore += 5;
      
      // Domain length penalty (shorter domains often have higher authority)
      if (domainParts[0].length < 8) baseScore += 10;
      
      // Add random factor to simulate real metrics
      const randomFactor = Math.floor(Math.random() * 40);
      
      return Math.min(baseScore + randomFactor, 100);
      
    } catch (error) {
      return 50; // Default authority score
    }
  }

  // Identify keyword opportunities from competitor analysis
  private async identifyKeywordOpportunities(
    ownKeywords: KeywordData[],
    competitorData: CompetitorData[],
    tools: any,
    logger: any
  ): Promise<KeywordData[]> {
    const opportunities: KeywordData[] = [];
    const ownKeywordSet = new Set(ownKeywords.map(k => k.keyword.toLowerCase()));

    // Find keywords that competitors rank for but we don't
    for (const competitor of competitorData) {
      for (const keyword of competitor.keywords) {
        if (!ownKeywordSet.has(keyword.keyword.toLowerCase())) {
          // This is a potential opportunity
          if (keyword.search_volume > 100 && keyword.keyword_difficulty < 70) {
            opportunities.push({
              ...keyword,
              data_source: 'competitor_opportunity'
            });
          }
        }
      }
    }

    // Deduplicate and limit opportunities
    const uniqueOpportunities = this.deduplicateKeywords(opportunities);
    return uniqueOpportunities.slice(0, 20); // Limit to top 20 opportunities
  }

  // Infer keyword intent
  private inferKeywordIntent(keyword: string): 'informational' | 'navigational' | 'commercial' | 'transactional' {
    const keywordLower = keyword.toLowerCase();
    
    if (keywordLower.includes('buy') || keywordLower.includes('price') || keywordLower.includes('cost') || keywordLower.includes('purchase')) {
      return 'transactional';
    }
    
    if (keywordLower.includes('review') || keywordLower.includes('vs') || keywordLower.includes('best') || keywordLower.includes('compare')) {
      return 'commercial';
    }
    
    if (keywordLower.includes('how') || keywordLower.includes('what') || keywordLower.includes('guide') || keywordLower.includes('tutorial')) {
      return 'informational';
    }
    
    return 'informational';
  }

  // Deduplicate keywords
  private deduplicateKeywords(keywords: KeywordData[]): KeywordData[] {
    const seen = new Set<string>();
    return keywords.filter(keyword => {
      const key = keyword.keyword.toLowerCase();
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  // Extract JSON from AI response that might contain markdown code blocks
  private extractJsonFromResponse(response: string): any {
    try {
      // First try direct JSON parsing
      return JSON.parse(response);
    } catch {
      try {
        // Try to extract JSON from markdown code blocks
        const jsonMatch = response.match(/```(?:json)?\s*(\[[\s\S]*?\]|\{[\s\S]*?\})\s*```/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[1]);
        }

        // Try to find JSON array or object in the response
        const arrayMatch = response.match(/\[[\s\S]*?\]/);
        if (arrayMatch) {
          return JSON.parse(arrayMatch[0]);
        }

        const objectMatch = response.match(/\{[\s\S]*?\}/);
        if (objectMatch) {
          return JSON.parse(objectMatch[0]);
        }

        // If no JSON found, return empty array
        return [];
      } catch {
        return [];
      }
    }
  }

  // Validate node execution
  async validate(state: PSEOWorkflowState): Promise<boolean> {
    // Competitor analysis is optional, so always valid
    return true;
  }
}
