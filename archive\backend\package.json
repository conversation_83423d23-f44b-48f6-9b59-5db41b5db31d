{"name": "backend", "version": "1.0.0", "description": "LlamaIndex RAG Backend", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --transpile-only --no-deps src/index.ts", "test": "jest", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@llamaindex/openai": "^0.1.61", "@pinecone-database/pinecone": "^5.1.1", "@supabase/supabase-js": "^2.49.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/node": "^20.11.24", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.3", "express-fileupload": "^1.5.1", "express-session": "^1.18.1", "gpt-tokenizer": "^2.9.0", "llamaindex": "^0.9.13", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.0", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pinecone-client": "^1.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/express-fileupload": "^1.5.1", "@types/jest": "^29.5.12", "@types/multer": "^1.4.11", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "ts-node-dev": "^2.0.0"}}