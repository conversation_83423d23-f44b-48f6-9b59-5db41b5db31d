// =====================================================
// GOOGLE SEARCH CONSOLE INTEGRATION SERVICE
// Phase 5: External API Integration
// =====================================================

import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

export interface SearchConsoleMetrics {
  // Query data
  query: string;
  clicks: number;
  impressions: number;
  ctr: number;
  position: number;
  
  // Page data
  page?: string;
  device?: 'desktop' | 'mobile' | 'tablet';
  country?: string;
  date?: string;
}

export interface SearchConsolePageData {
  page: string;
  clicks: number;
  impressions: number;
  ctr: number;
  position: number;
  queries: SearchConsoleMetrics[];
}

export interface SearchConsoleSiteOverview {
  totalClicks: number;
  totalImpressions: number;
  averageCTR: number;
  averagePosition: number;
  totalQueries: number;
  totalPages: number;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface SearchConsoleIntegrationConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  refreshToken?: string;
}

export class GoogleSearchConsoleService {
  private oauth2Client: OAuth2Client;
  private searchConsole: any;
  private isAuthenticated: boolean = false;

  constructor(config: SearchConsoleIntegrationConfig) {
    // Initialize OAuth2 client
    this.oauth2Client = new google.auth.OAuth2(
      config.clientId,
      config.clientSecret,
      config.redirectUri
    );

    // Set refresh token if available
    if (config.refreshToken) {
      this.oauth2Client.setCredentials({
        refresh_token: config.refreshToken
      });
      this.isAuthenticated = true;
    }

    // Initialize Search Console API
    this.searchConsole = google.searchconsole({
      version: 'v1',
      auth: this.oauth2Client
    });
  }

  /**
   * Generate OAuth2 authorization URL
   */
  generateAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/webmasters.readonly',
      'https://www.googleapis.com/auth/webmasters'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(authCode: string): Promise<void> {
    try {
      const { tokens } = await this.oauth2Client.getToken(authCode);
      this.oauth2Client.setCredentials(tokens);
      this.isAuthenticated = true;
      
      console.log('✅ Google Search Console authentication successful');
    } catch (error) {
      console.error('❌ Failed to exchange code for tokens:', error);
      throw new Error('Authentication failed');
    }
  }

  /**
   * Get user's Search Console sites
   */
  async getSites(): Promise<string[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Search Console');
    }

    try {
      const response = await this.searchConsole.sites.list();
      return response.data.siteEntry?.map((site: any) => site.siteUrl) || [];
    } catch (error) {
      console.error('Failed to fetch Search Console sites:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive site overview
   */
  async getSiteOverview(
    siteUrl: string, 
    startDate: string, 
    endDate: string
  ): Promise<SearchConsoleSiteOverview> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Search Console');
    }

    try {
      // Get overall performance data
      const performanceResponse = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: [],
          aggregationType: 'auto'
        }
      });

      const overallData = performanceResponse.data.rows?.[0] || {};

      // Get unique queries count
      const queriesResponse = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['query'],
          rowLimit: 25000
        }
      });

      // Get unique pages count
      const pagesResponse = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['page'],
          rowLimit: 25000
        }
      });

      return {
        totalClicks: overallData.clicks || 0,
        totalImpressions: overallData.impressions || 0,
        averageCTR: overallData.ctr || 0,
        averagePosition: overallData.position || 0,
        totalQueries: queriesResponse.data.rows?.length || 0,
        totalPages: pagesResponse.data.rows?.length || 0,
        dateRange: {
          start: startDate,
          end: endDate
        }
      };
    } catch (error) {
      console.error('Failed to fetch site overview:', error);
      throw error;
    }
  }

  /**
   * Get top performing queries
   */
  async getTopQueries(
    siteUrl: string,
    startDate: string,
    endDate: string,
    limit: number = 100
  ): Promise<SearchConsoleMetrics[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Search Console');
    }

    try {
      const response = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['query'],
          rowLimit: limit,
          startRow: 0
        }
      });

      return response.data.rows?.map((row: any) => ({
        query: row.keys[0],
        clicks: row.clicks || 0,
        impressions: row.impressions || 0,
        ctr: row.ctr || 0,
        position: row.position || 0
      })) || [];
    } catch (error) {
      console.error('Failed to fetch top queries:', error);
      throw error;
    }
  }

  /**
   * Get top performing pages
   */
  async getTopPages(
    siteUrl: string,
    startDate: string,
    endDate: string,
    limit: number = 100
  ): Promise<SearchConsolePageData[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Search Console');
    }

    try {
      // Get page performance data
      const pagesResponse = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['page'],
          rowLimit: limit,
          startRow: 0
        }
      });

      const pages = pagesResponse.data.rows?.map((row: any) => ({
        page: row.keys[0],
        clicks: row.clicks || 0,
        impressions: row.impressions || 0,
        ctr: row.ctr || 0,
        position: row.position || 0,
        queries: []
      })) || [];

      // Get top queries for each page (limited for performance)
      const topPages = pages.slice(0, 10);
      
      for (const page of topPages) {
        try {
          const queriesResponse = await this.searchConsole.searchanalytics.query({
            siteUrl,
            requestBody: {
              startDate,
              endDate,
              dimensions: ['query'],
              dimensionFilterGroups: [{
                filters: [{
                  dimension: 'page',
                  expression: page.page
                }]
              }],
              rowLimit: 20
            }
          });

          page.queries = queriesResponse.data.rows?.map((row: any) => ({
            query: row.keys[0],
            clicks: row.clicks || 0,
            impressions: row.impressions || 0,
            ctr: row.ctr || 0,
            position: row.position || 0,
            page: page.page
          })) || [];
        } catch (error) {
          console.warn(`Failed to fetch queries for page ${page.page}:`, error);
        }
      }

      return pages;
    } catch (error) {
      console.error('Failed to fetch top pages:', error);
      throw error;
    }
  }

  /**
   * Get keyword ranking data for specific keywords
   */
  async getKeywordRankings(
    siteUrl: string,
    keywords: string[],
    startDate: string,
    endDate: string
  ): Promise<SearchConsoleMetrics[]> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Search Console');
    }

    const results: SearchConsoleMetrics[] = [];

    try {
      // Process keywords in batches to avoid API limits
      const batchSize = 5;
      for (let i = 0; i < keywords.length; i += batchSize) {
        const batch = keywords.slice(i, i + batchSize);
        
        for (const keyword of batch) {
          try {
            const response = await this.searchConsole.searchanalytics.query({
              siteUrl,
              requestBody: {
                startDate,
                endDate,
                dimensions: ['query'],
                dimensionFilterGroups: [{
                  filters: [{
                    dimension: 'query',
                    expression: keyword,
                    operator: 'equals'
                  }]
                }]
              }
            });

            if (response.data.rows && response.data.rows.length > 0) {
              const row = response.data.rows[0];
              results.push({
                query: keyword,
                clicks: row.clicks || 0,
                impressions: row.impressions || 0,
                ctr: row.ctr || 0,
                position: row.position || 0
              });
            } else {
              // Keyword not found in Search Console data
              results.push({
                query: keyword,
                clicks: 0,
                impressions: 0,
                ctr: 0,
                position: 0
              });
            }
          } catch (error) {
            console.warn(`Failed to fetch data for keyword "${keyword}":`, error);
          }
        }

        // Add delay between batches to respect rate limits
        if (i + batchSize < keywords.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    } catch (error) {
      console.error('Failed to fetch keyword rankings:', error);
      throw error;
    }

    return results;
  }

  /**
   * Get page-specific performance data
   */
  async getPagePerformance(
    siteUrl: string,
    pageUrl: string,
    startDate: string,
    endDate: string
  ): Promise<SearchConsolePageData> {
    if (!this.isAuthenticated) {
      throw new Error('Not authenticated with Google Search Console');
    }

    try {
      // Get page performance
      const pageResponse = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['page'],
          dimensionFilterGroups: [{
            filters: [{
              dimension: 'page',
              expression: pageUrl,
              operator: 'equals'
            }]
          }]
        }
      });

      const pageData = pageResponse.data.rows?.[0] || {};

      // Get queries for this page
      const queriesResponse = await this.searchConsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['query'],
          dimensionFilterGroups: [{
            filters: [{
              dimension: 'page',
              expression: pageUrl,
              operator: 'equals'
            }]
          }],
          rowLimit: 100
        }
      });

      const queries = queriesResponse.data.rows?.map((row: any) => ({
        query: row.keys[0],
        clicks: row.clicks || 0,
        impressions: row.impressions || 0,
        ctr: row.ctr || 0,
        position: row.position || 0,
        page: pageUrl
      })) || [];

      return {
        page: pageUrl,
        clicks: pageData.clicks || 0,
        impressions: pageData.impressions || 0,
        ctr: pageData.ctr || 0,
        position: pageData.position || 0,
        queries
      };
    } catch (error) {
      console.error('Failed to fetch page performance:', error);
      throw error;
    }
  }

  /**
   * Get Search Console data for pSEO analysis integration
   */
  async getDataForPSEOAnalysis(
    siteUrl: string,
    targetKeywords: string[],
    discoveredPages: string[]
  ): Promise<{
    siteOverview: SearchConsoleSiteOverview;
    keywordPerformance: SearchConsoleMetrics[];
    pagePerformance: SearchConsolePageData[];
    topOpportunities: SearchConsoleMetrics[];
  }> {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    try {
      // Get overall site overview
      const siteOverview = await this.getSiteOverview(siteUrl, startDate, endDate);

      // Get keyword performance for target keywords
      const keywordPerformance = await this.getKeywordRankings(
        siteUrl, 
        targetKeywords.slice(0, 50), // Limit for performance
        startDate, 
        endDate
      );

      // Get performance for discovered pages
      const pagePerformance: SearchConsolePageData[] = [];
      const limitedPages = discoveredPages.slice(0, 20); // Limit for performance
      
      for (const pageUrl of limitedPages) {
        try {
          const pageData = await this.getPagePerformance(siteUrl, pageUrl, startDate, endDate);
          pagePerformance.push(pageData);
        } catch (error) {
          console.warn(`Failed to get data for page ${pageUrl}:`, error);
        }
      }

      // Get top performing queries for opportunity identification
      const topOpportunities = await this.getTopQueries(siteUrl, startDate, endDate, 100);

      return {
        siteOverview,
        keywordPerformance,
        pagePerformance,
        topOpportunities
      };
    } catch (error) {
      console.error('Failed to get Search Console data for pSEO analysis:', error);
      throw error;
    }
  }

  /**
   * Check if service is authenticated
   */
  isAuthenticatedStatus(): boolean {
    return this.isAuthenticated;
  }

  /**
   * Get refresh token (for storage)
   */
  getRefreshToken(): string | null {
    return this.oauth2Client.credentials.refresh_token || null;
  }
} 