import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { databaseService } from '../../services/pseo/databaseService';
import { useUser } from '../../../../base/contextapi/UserContext';
import { supabase } from '../../../../base/utils/supabaseClient';
import PSEOLayout from '../../components/PSEOLayout';

interface AnalysisHistoryItem {
  id: string;
  job_type: string;
  status: string;
  created_at: string;
  completed_at: string;
  website_id: string;
  website_name: string;
  website_url: string;
  client_name: string;
  analysis_summary: {
    pages_discovered: number;
    keywords_found: number;
    content_opportunities: number;
    generated_content: number;
    high_priority_opportunities: number;
    estimated_traffic: number;
  };
}

const FullSiteAnalysisHistory: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const [analyses, setAnalyses] = useState<AnalysisHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      loadAnalysisHistory();
    }
  }, [user?.id]);

  const loadAnalysisHistory = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Get clients for this user
      const clients = await databaseService.getClientsByUserId(user.id);
      if (clients.length === 0) {
        setAnalyses([]);
        return;
      }

      // Get websites for these clients
      const websitePromises = clients.map(client => 
        databaseService.getWebsitesByClientId(client.id)
      );
      const websitesArrays = await Promise.all(websitePromises);
      const allWebsites = websitesArrays.flat();
      const websiteIds = allWebsites.map(w => w.id);

      if (websiteIds.length === 0) {
        setAnalyses([]);
        return;
      }

      // Get completed full site analysis jobs
      const { data: completedJobs, error } = await supabase
        .from('pseo_agent_jobs')
        .select('*')
        .in('website_id', websiteIds)
        .eq('job_type', 'full_site_audit')
        .eq('status', 'completed')
        .order('completed_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to load analysis history: ${error.message}`);
      }

      // For each completed job, get the detailed analysis results
      const enhancedResults = await Promise.all(
        (completedJobs || []).map(async (job) => {
          const website = allWebsites.find(w => w.id === job.website_id);
          const client = clients.find(c => c.id === website?.client_id);
          
          // Get analysis data for this website
          const [pages, keywords, contentOpportunities, generatedContent] = await Promise.all([
            databaseService.getWebsitePages(job.website_id),
            databaseService.getKeywordResearch(job.website_id),
            databaseService.getContentOpportunities(job.website_id),
            databaseService.getGeneratedContentItems(job.website_id)
          ]);

          return {
            ...job,
            website_name: website?.name || 'Unknown Website',
            website_url: website?.url || '',
            client_name: client?.name || 'Unknown Client',
            analysis_summary: {
              pages_discovered: pages.length,
              keywords_found: keywords.length,
              content_opportunities: contentOpportunities.length,
              generated_content: generatedContent.length,
              high_priority_opportunities: contentOpportunities.filter(opp => opp.priority === 'high').length,
              estimated_traffic: contentOpportunities.reduce((sum, opp) => sum + (opp.estimated_traffic || 0), 0)
            }
          };
        })
      );

      setAnalyses(enhancedResults);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analysis history';
      setError(errorMessage);
      console.error('Failed to load analysis history:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const durationMs = end.getTime() - start.getTime();
    const minutes = Math.floor(durationMs / (1000 * 60));
    const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  if (loading) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading analysis history...</p>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  if (error) {
    return (
      <PSEOLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-6xl mx-auto">
            <div className="text-center py-12">
              <div className="text-4xl mb-4">❌</div>
              <h2 className="text-2xl font-bold text-foreground mb-2">Error Loading History</h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={() => window.history.back()}
                className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </PSEOLayout>
    );
  }

  return (
    <PSEOLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-2">
            <button
              onClick={() => navigate('/pseo')}
              className="text-muted-foreground hover:text-foreground"
            >
              ← Back to Dashboard
            </button>
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Full Site Analysis History
          </h1>
          <p className="text-muted-foreground">
            View all completed full site analyses and their results
          </p>
        </div>

        {/* Analysis History */}
        {analyses.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🚀</div>
            <h2 className="text-2xl font-bold text-foreground mb-2">No Analysis History</h2>
            <p className="text-muted-foreground mb-6">
              You haven't completed any full site analyses yet.
            </p>
            <button
              onClick={() => navigate('/full-site-analysis')}
              className="bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-md"
            >
              Start Your First Analysis
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {analyses.map((analysis) => (
              <div key={analysis.id} className="bg-card rounded-lg border p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-foreground mb-1">
                      {analysis.website_name}
                    </h3>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>Client: {analysis.client_name}</span>
                      <span>•</span>
                      <span>Completed: {formatDate(analysis.completed_at)}</span>
                      <span>•</span>
                      <span>Duration: {formatDuration(analysis.created_at, analysis.completed_at)}</span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => navigate(`/full-site-analysis-results/${analysis.id}`)}
                      className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm"
                    >
                      View Details
                    </button>
                  </div>
                </div>

                {/* Analysis Summary Stats */}
                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mt-4">
                  <div className="bg-muted rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-primary">{analysis.analysis_summary.pages_discovered}</div>
                    <div className="text-xs text-muted-foreground">Pages</div>
                  </div>
                  <div className="bg-muted rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-primary">{analysis.analysis_summary.keywords_found}</div>
                    <div className="text-xs text-muted-foreground">Keywords</div>
                  </div>
                  <div className="bg-muted rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-primary">{analysis.analysis_summary.content_opportunities}</div>
                    <div className="text-xs text-muted-foreground">Opportunities</div>
                  </div>
                  <div className="bg-muted rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-green-600">{analysis.analysis_summary.generated_content}</div>
                    <div className="text-xs text-muted-foreground">Generated</div>
                  </div>
                  <div className="bg-muted rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-red-600">{analysis.analysis_summary.high_priority_opportunities}</div>
                    <div className="text-xs text-muted-foreground">High Priority</div>
                  </div>
                  <div className="bg-muted rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-blue-600">{analysis.analysis_summary.estimated_traffic.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">Est. Traffic</div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex gap-2 mt-4 pt-4 border-t border-border">
                  <button
                    onClick={() => navigate(`/content-generation-results/${analysis.website_id}`)}
                    className="text-sm bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                  >
                    📝 View Generated Content
                  </button>
                  <button
                    onClick={() => navigate(`/keyword-research-results/${analysis.website_id}`)}
                    className="text-sm bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    🎯 View Keywords
                  </button>
                  <button
                    onClick={() => navigate(`/page-discovery-results/${analysis.website_id}`)}
                    className="text-sm bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
                  >
                    🔍 View Pages
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
        </div>
      </div>
    </PSEOLayout>
  );
};

export default FullSiteAnalysisHistory; 