import { OpenAIEmbedding } from '@llamaindex/openai';
import { Document } from 'llamaindex';

export class OpenAIEmbeddings extends OpenAIEmbedding {
  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }
    
    super({
      apiKey: process.env.OPENAI_API_KEY,
      model: 'text-embedding-3-small',
      dimensions: 1536,
      maxRetries: 3,
      timeout: 60
    });
  }

  async transform(documents: Document[]): Promise<Document[]> {
    // No transformation needed for OpenAI embeddings
    return documents;
  }
} 