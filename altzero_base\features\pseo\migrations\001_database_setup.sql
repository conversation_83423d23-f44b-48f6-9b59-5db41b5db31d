-- pSEO Database Setup Script
-- Run this in your Supabase SQL editor

-- =====================================================
-- 1. CREATE TABLES
-- =====================================================

-- Clients table
CREATE TABLE pseo_clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  company VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Websites table
CREATE TABLE pseo_websites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES pseo_clients(id) ON DELETE CASCADE,
  url VARCHAR(500) NOT NULL,
  domain VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SEO Audits table
CREATE TABLE pseo_audits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  website_id UUID REFERENCES pseo_websites(id) ON DELETE CASCADE,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'scraping', 'analyzing', 'completed', 'failed')),
  
  -- Workflow step data
  scraped_content TEXT,
  scrape_metadata JSONB DEFAULT '{}',
  technical_audit_raw TEXT,
  content_audit_raw TEXT,
  technical_analysis JSONB DEFAULT '{}',
  content_analysis JSONB DEFAULT '{}',
  combined_report TEXT,
  report_html TEXT,
  
  -- Metadata
  ai_model_used VARCHAR(100) DEFAULT 'gpt-4o-mini',
  processing_time_seconds INTEGER,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Audit steps tracking
CREATE TABLE pseo_audit_steps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  audit_id UUID REFERENCES pseo_audits(id) ON DELETE CASCADE,
  step_name VARCHAR(100) NOT NULL,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  step_data JSONB DEFAULT '{}'
);

-- =====================================================
-- 2. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for user-based queries
CREATE INDEX idx_pseo_clients_user_id ON pseo_clients(user_id);
CREATE INDEX idx_pseo_websites_client_id ON pseo_websites(client_id);
CREATE INDEX idx_pseo_audits_website_id ON pseo_audits(website_id);
CREATE INDEX idx_pseo_audit_steps_audit_id ON pseo_audit_steps(audit_id);

-- Index for status-based queries
CREATE INDEX idx_pseo_audits_status ON pseo_audits(status);
CREATE INDEX idx_pseo_audit_steps_status ON pseo_audit_steps(status);

-- Index for time-based queries
CREATE INDEX idx_pseo_audits_created_at ON pseo_audits(created_at DESC);
CREATE INDEX idx_pseo_websites_created_at ON pseo_websites(created_at DESC);

-- =====================================================
-- 3. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE pseo_clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_websites ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_audits ENABLE ROW LEVEL SECURITY;
ALTER TABLE pseo_audit_steps ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 4. CREATE RLS POLICIES
-- =====================================================

-- Clients policies
CREATE POLICY "Users can manage their own clients" ON pseo_clients
  FOR ALL USING (auth.uid() = user_id);

-- Websites policies
CREATE POLICY "Users can manage websites of their clients" ON pseo_websites
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_clients 
      WHERE pseo_clients.id = pseo_websites.client_id 
      AND pseo_clients.user_id = auth.uid()
    )
  );

-- Audits policies
CREATE POLICY "Users can manage audits of their websites" ON pseo_audits
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_websites w
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE w.id = pseo_audits.website_id 
      AND c.user_id = auth.uid()
    )
  );

-- Audit steps policies
CREATE POLICY "Users can view audit steps of their audits" ON pseo_audit_steps
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pseo_audits a
      JOIN pseo_websites w ON a.website_id = w.id
      JOIN pseo_clients c ON w.client_id = c.id
      WHERE a.id = pseo_audit_steps.audit_id 
      AND c.user_id = auth.uid()
    )
  );

-- =====================================================
-- 5. CREATE UPDATED_AT TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_pseo_clients_updated_at 
  BEFORE UPDATE ON pseo_clients 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pseo_websites_updated_at 
  BEFORE UPDATE ON pseo_websites 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. CREATE HELPFUL VIEWS (OPTIONAL)
-- =====================================================

-- View for client dashboard with website counts
CREATE VIEW pseo_client_dashboard AS
SELECT 
  c.*,
  COUNT(w.id) as website_count,
  COUNT(a.id) as total_audits,
  COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_audits,
  MAX(a.created_at) as last_audit_date
FROM pseo_clients c
LEFT JOIN pseo_websites w ON c.id = w.client_id
LEFT JOIN pseo_audits a ON w.id = a.website_id
GROUP BY c.id, c.user_id, c.name, c.email, c.company, c.created_at, c.updated_at;

-- View for website dashboard with audit stats
CREATE VIEW pseo_website_dashboard AS
SELECT 
  w.*,
  c.name as client_name,
  COUNT(a.id) as audit_count,
  COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_audits,
  COUNT(CASE WHEN a.status = 'failed' THEN 1 END) as failed_audits,
  MAX(a.created_at) as last_audit_date,
  MAX(CASE WHEN a.status = 'completed' THEN a.completed_at END) as last_completed_audit
FROM pseo_websites w
JOIN pseo_clients c ON w.client_id = c.id
LEFT JOIN pseo_audits a ON w.id = a.website_id
GROUP BY w.id, w.client_id, w.url, w.domain, w.name, w.status, w.created_at, w.updated_at, c.name;

-- =====================================================
-- 7. GRANT PERMISSIONS TO AUTHENTICATED USERS
-- =====================================================

-- Grant permissions to authenticated users
GRANT ALL ON pseo_clients TO authenticated;
GRANT ALL ON pseo_websites TO authenticated;
GRANT ALL ON pseo_audits TO authenticated;
GRANT ALL ON pseo_audit_steps TO authenticated;

-- Grant permissions on views
GRANT SELECT ON pseo_client_dashboard TO authenticated;
GRANT SELECT ON pseo_website_dashboard TO authenticated;

-- =====================================================
-- 8. INSERT SAMPLE DATA (OPTIONAL - FOR TESTING)
-- =====================================================

-- Uncomment the following lines if you want to insert sample data for testing
-- Note: Replace 'your-user-id-here' with an actual user ID from auth.users

/*
-- Sample client
INSERT INTO pseo_clients (user_id, name, email, company) VALUES 
('your-user-id-here', 'Sample Client', '<EMAIL>', 'Example Corp');

-- Get the client ID for sample website
-- Sample website
INSERT INTO pseo_websites (client_id, url, domain, name) VALUES 
((SELECT id FROM pseo_clients WHERE name = 'Sample Client' LIMIT 1), 
 'https://example.com', 'example.com', 'Example Website');
*/

-- =====================================================
-- SETUP COMPLETE
-- =====================================================

-- Verify tables were created
SELECT 
  schemaname,
  tablename,
  tableowner,
  hasindexes,
  hasrules,
  hastriggers
FROM pg_tables 
WHERE tablename LIKE 'pseo_%'
ORDER BY tablename;

-- Verify RLS is enabled
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE tablename LIKE 'pseo_%'
ORDER BY tablename; 