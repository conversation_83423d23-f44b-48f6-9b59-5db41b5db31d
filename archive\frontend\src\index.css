@tailwind base;
@tailwind components;
@tailwind utilities;

@import "bootstrap/dist/css/bootstrap.min.css";

/* Base styles */
:root {
  --primary-color: #4f46e5;
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
}

/* Global styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* Utility classes */
.prose {
  max-width: 65ch;
  line-height: 1.6;
}

.prose h1 {
  font-size: 2.25rem;
  margin-top: 0;
  margin-bottom: 1rem;
}

.prose h2 {
  font-size: 1.875rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h3 {
  font-size: 1.5rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.prose p {
  margin-bottom: 1.25rem;
}

.prose ul,
.prose ol {
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

/* Document Editor Styles */
.tiptap {
  outline: none;
  min-height: 300px;
  padding: 1rem;
}

.tiptap p {
  margin: 1em 0;
}

.tiptap h1,
.tiptap h2,
.tiptap h3,
.tiptap h4,
.tiptap h5,
.tiptap h6 {
  line-height: 1.1;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.tiptap h1 {
  font-size: 2em;
}

.tiptap h2 {
  font-size: 1.5em;
}

.tiptap h3 {
  font-size: 1.17em;
}

.tiptap ul,
.tiptap ol {
  padding-left: 2rem;
}

.tiptap ul {
  list-style-type: disc;
}

.tiptap ol {
  list-style-type: decimal;
}

.tiptap img {
  max-width: 100%;
  height: auto;
}

.tiptap blockquote {
  padding-left: 1rem;
  border-left: 2px solid #ddd;
  color: #666;
  font-style: italic;
}

.tiptap a {
  color: #0073e6;
  text-decoration: underline;
}

.tiptap table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.tiptap th,
.tiptap td {
  border: 1px solid #ddd;
  padding: 0.5rem;
}

.tiptap th {
  background-color: #f0f0f0;
  font-weight: bold;
}

.tiptap hr {
  margin: 2rem 0;
  border: none;
  border-top: 1px solid #ddd;
}

.tiptap .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

.editor-container {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.document-page {
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  margin: 1rem auto;
}

/* Image handling in documents */
.image-container {
  margin: 1.5rem 0;
}

.image-caption {
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* Text alignment utilities */
.tiptap .text-left {
  text-align: left;
}

.tiptap .text-center {
  text-align: center;
}

.tiptap .text-right {
  text-align: right;
}

.tiptap .text-justify {
  text-align: justify;
}
