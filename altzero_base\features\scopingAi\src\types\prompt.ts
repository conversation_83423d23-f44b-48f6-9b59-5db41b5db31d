// Prompt category types
export type PromptCategory = 'scoping' | 'requirements' | 'proposals' | 'general' | 'scope' | 'proposal' | 'technical' | 'creative';

// Main prompt template interface
export interface Prompt {
  id: string;
  user_id: string;
  name: string;
  title: string; // Alias for name for backward compatibility
  description: string;
  content: string;
  variables: string[];
  category: PromptCategory;
  created_at: string;
  updated_at: string;
  is_favorite: boolean;
  usage_count: number;
}

// Alternative interface name for backward compatibility
export interface PromptTemplate extends Prompt {}

// Form data interface for creating/updating prompts
export interface PromptFormData {
  name: string;
  title?: string; // Alias for name
  description: string;
  content: string;
  category: PromptCategory;
  variables?: string[];
}

// Prompt variable interface
export interface PromptVariable {
  name: string;
  description?: string;
  required: boolean;
  defaultValue?: string;
}

// Prompt usage analytics interface
export interface PromptUsage {
  id: string;
  prompt_id: string;
  user_id: string;
  used_at: string;
  context?: string;
}

// Prompt search/filter options
export interface PromptFilters {
  category?: PromptCategory | 'all';
  isFavorite?: boolean;
  search?: string;
  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'name';
  sortOrder?: 'asc' | 'desc';
}

// Prompt statistics interface
export interface PromptStats {
  totalPrompts: number;
  favoritePrompts: number;
  mostUsedPrompt?: Prompt;
  recentPrompts: Prompt[];
  categoryCounts: Record<PromptCategory, number>;
}

// API response interfaces
export interface PromptsResponse {
  data: Prompt[];
  count: number;
  error?: string;
}

export interface PromptResponse {
  data: Prompt | null;
  error?: string;
}

// Prompt export/import interfaces
export interface PromptExport {
  prompts: Prompt[];
  exportedAt: string;
  version: string;
}

export interface PromptImport {
  prompts: Omit<Prompt, 'id' | 'user_id' | 'created_at' | 'updated_at'>[];
  importOptions?: {
    overwriteExisting: boolean;
    preserveCategories: boolean;
  };
} 