import React from "react";
import { useNavigate } from "react-router-dom";
import { formatDistanceToNow } from "date-fns";

import { Button } from "../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Group } from "../../types/team";
import { MemberRole } from "../../types/organization";

interface TeamCardProps {
  team: Group;
  role: MemberRole;
  actions?: React.ReactNode;
}

export function TeamCard({ team, role, actions }: TeamCardProps) {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/teams/${team.id}`);
  };

  return (
    <Card className="transition-all hover:border-primary/50 cursor-pointer">
      <CardHeader className="pb-2">
        <CardTitle className="flex justify-between items-start">
          <span onClick={handleClick}>{team.name}</span>
          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
            {role}
          </span>
        </CardTitle>
        <CardDescription>
          Created{" "}
          {formatDistanceToNow(new Date(team.created_at), { addSuffix: true })}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {team.description || "No description provided."}
        </p>
        {team.organisation_id && (
          <div className="mt-2">
            <span className="text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded-full">
              Part of an organization
            </span>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2 flex justify-between">
        <Button variant="outline" size="sm" onClick={handleClick}>
          View Details
        </Button>
        {actions}
      </CardFooter>
    </Card>
  );
}
