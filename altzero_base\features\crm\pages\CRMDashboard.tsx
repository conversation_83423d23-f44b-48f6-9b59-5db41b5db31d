import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  Users,
  Building,
  Target,
  Activity,
  Calendar,
  Plus,
  ArrowRight,
  RefreshCw,
  MoreHorizontal,
  DollarSign,
  BarChart3,
  Filter,
  Download,
} from "lucide-react";
import { crmService } from "../services/crmService";
import {
  Contact,
  Company,
  Opportunity,
  Activity as ActivityType,
  Event,
} from "../types";
import CRMLayout from "../components/CRMLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../base/components/ui/card";
import { Button } from "../../../base/components/ui/button";
import { Badge } from "../../../base/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../base/components/ui/dropdown-menu";

interface DashboardStats {
  contacts: { total: number; recent: Contact[] };
  companies: { total: number; recent: Company[] };
  opportunities: { total: number; totalValue: number; recent: Opportunity[] };
  activities: { total: number; recent: ActivityType[] };
  events: { upcoming: Event[] };
}

const CRMDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load data in parallel
      const [
        contactsResponse,
        companiesResponse,
        opportunitiesResponse,
        activitiesResponse,
        eventsResponse,
      ] = await Promise.all([
        crmService.getContacts({ limit: 5 }),
        crmService.getCompanies({ limit: 5 }),
        crmService.getOpportunities({ limit: 5 }),
        crmService.getActivities({ limit: 5 }),
        crmService.getEvents({ limit: 5 }),
      ]);

      // Calculate opportunity total value
      const totalValue = opportunitiesResponse.data.reduce(
        (sum, opp) => sum + (opp.value || 0),
        0
      );

      setStats({
        contacts: {
          total: contactsResponse.total,
          recent: contactsResponse.data,
        },
        companies: {
          total: companiesResponse.total,
          recent: companiesResponse.data,
        },
        opportunities: {
          total: opportunitiesResponse.total,
          totalValue,
          recent: opportunitiesResponse.data,
        },
        activities: {
          total: activitiesResponse.total,
          recent: activitiesResponse.data,
        },
        events: {
          upcoming: eventsResponse.data,
        },
      });
    } catch (err) {
      console.error("Error loading dashboard data:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load dashboard data"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <CRMLayout>
        <div className="container mx-auto px-4 py-6">
          <div className="space-y-6">
            {/* Header Skeleton */}
            <div className="flex justify-between items-center">
              <div className="space-y-2">
                <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
                <div className="h-4 bg-muted rounded w-96 animate-pulse"></div>
              </div>
              <div className="flex gap-3">
                <div className="h-10 bg-muted rounded w-32 animate-pulse"></div>
                <div className="h-10 bg-muted rounded w-10 animate-pulse"></div>
              </div>
            </div>

            {/* Stats Cards Skeleton */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <div className="h-4 bg-muted rounded w-20 animate-pulse"></div>
                        <div className="h-8 bg-muted rounded w-16 animate-pulse"></div>
                      </div>
                      <div className="h-12 w-12 bg-muted rounded-full animate-pulse"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Content Skeleton */}
            <div className="grid gap-6 md:grid-cols-2">
              {[...Array(2)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <div className="h-6 bg-muted rounded w-32 animate-pulse"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[...Array(3)].map((_, j) => (
                        <div
                          key={j}
                          className="flex justify-between items-center"
                        >
                          <div className="space-y-2">
                            <div className="h-4 bg-muted rounded w-32 animate-pulse"></div>
                            <div className="h-3 bg-muted rounded w-24 animate-pulse"></div>
                          </div>
                          <div className="h-3 bg-muted rounded w-16 animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </CRMLayout>
    );
  }

  if (error) {
    return (
      <CRMLayout>
        <div className="container mx-auto px-4 py-6">
          <Card className="border-destructive">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                  <Target className="w-6 h-6 text-destructive" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-destructive">
                    Error Loading Dashboard
                  </h3>
                  <p className="text-muted-foreground mt-1">{error}</p>
                </div>
                <Button onClick={loadDashboardData} variant="destructive">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </CRMLayout>
    );
  }

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                CRM Dashboard
              </h1>
              <p className="text-muted-foreground mt-1">
                Overview of your customer relationship management
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={loadDashboardData}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <MoreHorizontal className="h-4 w-4" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem className="gap-2">
                    <Download className="h-4 w-4" />
                    Export Report
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Filter className="h-4 w-4" />
                    Filter Data
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Analytics
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button asChild className="gap-2">
                <Link to="/crm/contacts">
                  <Plus className="h-4 w-4" />
                  Add Contact
                </Link>
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Contacts
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.contacts.total || 0}
                </div>
                <Button variant="link" asChild className="p-0 h-auto text-xs">
                  <Link to="/crm/contacts" className="flex items-center gap-1">
                    View all <ArrowRight className="h-3 w-3" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Companies
                </CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.companies.total || 0}
                </div>
                <Button variant="link" asChild className="p-0 h-auto text-xs">
                  <Link to="/crm/companies" className="flex items-center gap-1">
                    View all <ArrowRight className="h-3 w-3" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Opportunities
                </CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.opportunities.total || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  ${(stats?.opportunities.totalValue || 0).toLocaleString()}{" "}
                  total value
                </p>
                <Button variant="link" asChild className="p-0 h-auto text-xs">
                  <Link
                    to="/crm/opportunities"
                    className="flex items-center gap-1"
                  >
                    View all <ArrowRight className="h-3 w-3" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Activities
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats?.activities.total || 0}
                </div>
                <Button variant="link" asChild className="p-0 h-auto text-xs">
                  <Link
                    to="/crm/activities"
                    className="flex items-center gap-1"
                  >
                    View all <ArrowRight className="h-3 w-3" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Contacts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Recent Contacts
                </CardTitle>
              </CardHeader>
              <CardContent>
                {stats?.contacts.recent.length ? (
                  <div className="space-y-4">
                    {stats.contacts.recent.map((contact) => (
                      <div
                        key={contact.id}
                        className="flex items-center justify-between p-3 rounded-lg border"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <Users className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{contact.full_name}</p>
                            <p className="text-sm text-muted-foreground">
                              {contact.email}
                            </p>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {contact.created_at &&
                            new Date(contact.created_at).toLocaleDateString()}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No contacts yet</p>
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="mt-2"
                    >
                      <Link to="/crm/contacts">Add your first contact</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Opportunities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Recent Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                {stats?.opportunities.recent.length ? (
                  <div className="space-y-4">
                    {stats.opportunities.recent.map((opportunity) => (
                      <div
                        key={opportunity.id}
                        className="flex items-center justify-between p-3 rounded-lg border"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <DollarSign className="h-4 w-4 text-purple-600" />
                          </div>
                          <div>
                            <p className="font-medium">{opportunity.title}</p>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {opportunity.stage}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                ${(opportunity.value || 0).toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {opportunity.created_at &&
                            new Date(
                              opportunity.created_at
                            ).toLocaleDateString()}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      No opportunities yet
                    </p>
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="mt-2"
                    >
                      <Link to="/crm/opportunities">
                        Create your first opportunity
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button
                  asChild
                  variant="outline"
                  className="h-auto p-4 flex-col gap-2"
                >
                  <Link to="/crm/contacts">
                    <Users className="h-6 w-6 text-blue-600" />
                    <span className="text-sm font-medium">Manage Contacts</span>
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="h-auto p-4 flex-col gap-2"
                >
                  <Link to="/crm/companies">
                    <Building className="h-6 w-6 text-green-600" />
                    <span className="text-sm font-medium">
                      Manage Companies
                    </span>
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="h-auto p-4 flex-col gap-2"
                >
                  <Link to="/crm/opportunities">
                    <Target className="h-6 w-6 text-purple-600" />
                    <span className="text-sm font-medium">Sales Pipeline</span>
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="h-auto p-4 flex-col gap-2"
                >
                  <Link to="/crm/events">
                    <Calendar className="h-6 w-6 text-orange-600" />
                    <span className="text-sm font-medium">Schedule Events</span>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CRMLayout>
  );
};

export default CRMDashboard;
