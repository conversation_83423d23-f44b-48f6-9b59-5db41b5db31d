import { useState, useEffect, useCallback, useRef } from 'react';

interface UseStableDataOptions<T> {
  fetchData: () => Promise<T>;
  dependencies?: any[];
  enableRefresh?: boolean;
  onError?: (error: Error) => void;
}

interface UseStableDataReturn<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  hasLoaded: boolean;
  refresh: () => Promise<void>;
}

/**
 * A stable data fetching hook that prevents infinite loops and circular dependencies
 * Each component manages its own data independently
 */
export function useStableData<T>({
  fetchData,
  dependencies = [],
  enableRefresh = false,
  onError
}: UseStableDataOptions<T>): UseStableDataReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  
  // Use ref to track if component is mounted
  const mountedRef = useRef(true);
  
  // Stable fetch function that won't cause re-renders
  const stableFetch = useCallback(async () => {
    if (!mountedRef.current) return;
    
    // Only show loading state if we haven't loaded before
    if (!hasLoaded) {
      setIsLoading(true);
    }
    
    setError(null);

    try {
      const result = await fetchData();
      
      if (mountedRef.current) {
        setData(result);
        setHasLoaded(true);
      }
    } catch (err) {
      if (mountedRef.current) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        
        if (onError) {
          onError(err instanceof Error ? err : new Error(errorMessage));
        }
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [fetchData, hasLoaded, onError]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    if (!enableRefresh) return;
    await stableFetch();
  }, [stableFetch, enableRefresh]);

  // Only fetch once on mount or when explicit dependencies change
  useEffect(() => {
    if (!hasLoaded) {
      stableFetch();
    }
  }, [stableFetch, hasLoaded]);

  // Handle dependency changes (if any)
  useEffect(() => {
    if (hasLoaded && dependencies.length > 0) {
      stableFetch();
    }
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    hasLoaded,
    refresh
  };
} 