"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>lette,
  Type,
  Layout,
  Sparkles,
  Download,
  Upload,
  <PERSON>otate<PERSON>c<PERSON>,
  Settings,
} from "lucide-react";
import { <PERSON><PERSON> } from "../../../../../base/components/ui/button";
import { Input } from "../../../../../base/components/ui/input";
import { Label } from "../../../../../base/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../../../base/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../base/components/ui/select";
import { Badge } from "../../../../../base/components/ui/badge";
import { useToast } from "../../../../../base/hooks/use-toast";
import { useDocumentContext } from "../../../contexts/DocumentContext";

interface ThemeControlsProps {
  activeTab: "theme" | "branding";
}

export function ThemeControls({ activeTab }: ThemeControlsProps) {
  const { documentTheme, applyThemePreset, updateThemeProperty } =
    useDocumentContext();
  const { toast } = useToast();

  const handlePresetChange = (presetName: string) => {
    if (
      presetName === "professional" ||
      presetName === "modern" ||
      presetName === "creative" ||
      presetName === "formal"
    ) {
      applyThemePreset(presetName);
      toast({
        title: "Theme applied",
        description: `${presetName} theme has been applied to your document.`,
      });
    }
  };

  const handlePropertyUpdate = (property: string, value: string) => {
    updateThemeProperty(property as any, value);
  };

  const handleLogoUpload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        const file = target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (event) => {
            if (event.target && typeof event.target.result === "string") {
              updateThemeProperty("logo", event.target.result as string);
              toast({
                title: "Logo uploaded",
                description: "Your logo has been added to the document theme.",
              });
            }
          };
          reader.readAsDataURL(file);
        }
      }
    };
    input.click();
  };

  if (activeTab === "branding") {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Logo & Brand Identity
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Logo Upload Section */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                Company Logo
              </Label>
              <Button
                variant="outline"
                onClick={handleLogoUpload}
                className="w-full"
              >
                <Upload className="h-4 w-4 mr-2" />
                {documentTheme.logo ? "Change Logo" : "Upload Logo"}
              </Button>

              {documentTheme.logo && (
                <div className="mt-4 p-4 border rounded-lg bg-gray-50">
                  <p className="text-xs text-gray-500 mb-2">
                    Current Logo Preview
                  </p>
                  <div className="flex justify-center p-2 bg-white rounded border">
                    <img
                      src={documentTheme.logo}
                      alt="Document Logo"
                      className="max-h-24 max-w-full object-contain"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Brand Name Section */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                Brand Name
              </Label>
              <Input
                value={documentTheme.brandName || ""}
                onChange={(e) =>
                  updateThemeProperty("brandName", e.target.value)
                }
                placeholder="Enter your company/brand name"
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                This will appear in headers and footers
              </p>
            </div>

            {/* Brand Tagline Section */}
            <div>
              <Label className="text-sm font-medium mb-2 block">
                Brand Tagline
              </Label>
              <Input
                value={documentTheme.brandTagline || ""}
                onChange={(e) =>
                  updateThemeProperty("brandTagline", e.target.value)
                }
                placeholder="Enter your company tagline or subtitle"
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                A short description or motto for your brand
              </p>
            </div>

            {/* Brand Preview Section */}
            {(documentTheme.brandName ||
              documentTheme.brandTagline ||
              documentTheme.logo) && (
              <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                <Label className="text-sm font-medium text-blue-800 mb-3 block">
                  Brand Preview
                </Label>
                <div className="flex items-center gap-3">
                  {documentTheme.logo && (
                    <div className="bg-white p-2 rounded border">
                      <img
                        src={documentTheme.logo}
                        alt="Logo Preview"
                        className="h-8 object-contain"
                      />
                    </div>
                  )}
                  <div>
                    <div className="text-sm font-bold text-gray-800">
                      {documentTheme.brandName || "Your Brand Name"}
                    </div>
                    <div className="text-xs text-gray-600">
                      {documentTheme.brandTagline || "Your brand tagline"}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Theme Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Theme Presets
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {[
              {
                key: "professional",
                name: "Professional",
                description: "Clean and formal",
              },
              {
                key: "modern",
                name: "Modern",
                description: "Contemporary design",
              },
              {
                key: "creative",
                name: "Creative",
                description: "Bold and vibrant",
              },
              {
                key: "formal",
                name: "Formal",
                description: "Traditional and elegant",
              },
            ].map(({ key, name, description }) => (
              <div
                key={key}
                className="p-3 border rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors"
                onClick={() => handlePresetChange(key)}
              >
                <h4 className="font-medium">{name}</h4>
                <p className="text-xs text-gray-600">{description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Theme Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Current Theme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Heading Color</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  type="color"
                  value={documentTheme.headingColor || "#2563eb"}
                  onChange={(e) =>
                    handlePropertyUpdate("headingColor", e.target.value)
                  }
                  className="w-12 h-8 p-1"
                />
                <Input
                  value={documentTheme.headingColor || "#2563eb"}
                  onChange={(e) =>
                    handlePropertyUpdate("headingColor", e.target.value)
                  }
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Accent Color</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  type="color"
                  value={documentTheme.accentColor || "#0ea5e9"}
                  onChange={(e) =>
                    handlePropertyUpdate("accentColor", e.target.value)
                  }
                  className="w-12 h-8 p-1"
                />
                <Input
                  value={documentTheme.accentColor || "#0ea5e9"}
                  onChange={(e) =>
                    handlePropertyUpdate("accentColor", e.target.value)
                  }
                  className="flex-1"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Font Family</Label>
              <Select
                value={documentTheme.fontFamily || "Inter"}
                onValueChange={(value) =>
                  handlePropertyUpdate("fontFamily", value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Inter">Inter</SelectItem>
                  <SelectItem value="Arial">Arial</SelectItem>
                  <SelectItem value="Georgia">Georgia</SelectItem>
                  <SelectItem value="Times New Roman">
                    Times New Roman
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layout className="h-5 w-5" />
            Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className="p-4 rounded-lg border"
            style={{
              backgroundColor: documentTheme.backgroundColor || "#ffffff",
              fontFamily: documentTheme.fontFamily || "Inter",
            }}
          >
            <h3
              className="text-lg font-semibold mb-2"
              style={{ color: documentTheme.headingColor || "#2563eb" }}
            >
              Sample Heading
            </h3>
            <p style={{ color: documentTheme.textColor || "#374151" }}>
              This is how your document will look with the current theme
              settings.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ThemeControls;
