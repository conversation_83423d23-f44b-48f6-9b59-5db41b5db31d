import sys
import json
import base64
import os
from pathlib import Path
import fitz  # PyMuPDF for PDF handling
import docx  # python-docx for Word documents
import traceback
import time
import shutil

def read_file_with_retries(file_path: str, max_retries: int = 3, delay: float = 1.0) -> bytes:
    """Read file with retries in case of file system issues."""
    last_error = None
    for attempt in range(max_retries):
        try:
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            last_error = e
            print(f"Attempt {attempt + 1} failed: {str(e)}", file=sys.stderr)
            time.sleep(delay)
    raise IOError(f"Failed to read file after {max_retries} attempts: {str(last_error)}")

def verify_file(file_path: str) -> tuple[bool, str]:
    """Verify that the file exists and is readable."""
    try:
        if not os.path.exists(file_path):
            return False, f"File not found: {file_path}"
        if not os.path.isfile(file_path):
            return False, f"Not a valid file: {file_path}"
        if not os.access(file_path, os.R_OK):
            return False, f"File is not readable: {file_path}"
        
        # Get file size
        size = os.path.getsize(file_path)
        if size == 0:
            return False, f"File is empty: {file_path}"
            
        # Try to read the file
        try:
            data = read_file_with_retries(file_path)
            print(f"Successfully read {len(data)} bytes from file", file=sys.stderr)
        except Exception as e:
            return False, f"Failed to read file: {str(e)}"
            
        print(f"File verified - Size: {size} bytes", file=sys.stderr)
        return True, ""
    except Exception as e:
        return False, f"Error verifying file: {str(e)}"

def wait_for_file(file_path: str, timeout: int = 10, check_interval: float = 0.5) -> bool:
    """Wait for file to be fully written to disk."""
    start_time = time.time()
    last_size = -1
    
    while time.time() - start_time < timeout:
        if os.path.exists(file_path):
            try:
                current_size = os.path.getsize(file_path)
                if current_size > 0 and current_size == last_size:
                    # File size hasn't changed, probably finished writing
                    with open(file_path, 'rb') as f:
                        # Try to read the whole file to verify
                        f.read()
                        print(f"File ready after {time.time() - start_time:.1f} seconds", file=sys.stderr)
                        return True
                last_size = current_size
            except Exception as e:
                print(f"Error checking file: {str(e)}", file=sys.stderr)
        time.sleep(check_interval)
    
    print(f"Timeout waiting for file after {timeout} seconds", file=sys.stderr)
    return False

def process_pdf(file_path: str):
    """Process a PDF file and extract text and images with precise positioning."""
    try:
        # Verify file before processing
        is_valid, error_msg = verify_file(file_path)
        if not is_valid:
            raise ValueError(error_msg)

        print(f"Opening PDF file: {file_path}", file=sys.stderr)
        
        # Read the file content first
        file_content = read_file_with_retries(file_path)
        print(f"Successfully read {len(file_content)} bytes", file=sys.stderr)
        
        # Open PDF with PyMuPDF
        doc = fitz.open(file_path)
        
        if doc.page_count == 0:
            doc.close()
            raise ValueError("PDF file has no pages")
            
        total_pages = doc.page_count
        print(f"PDF has {total_pages} pages", file=sys.stderr)
        
        # Initialize structured content storage
        structured_content = []
        images = []
        
        # Process each page
        for page_num, page in enumerate(doc, 1):
            print(f"Processing page {page_num}/{total_pages}", file=sys.stderr)
            
            # Get page dimensions
            page_width = page.rect.width
            page_height = page.rect.height
            
            # Extract text blocks with positions
            blocks = page.get_text("dict")["blocks"]
            for block in blocks:
                # Process text blocks
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            structured_content.append({
                                "type": "text",
                                "content": span["text"],
                                "position": {
                                    "x": span["origin"][0] / page_width,  # Normalize to 0-1 range
                                    "y": span["origin"][1] / page_height,
                                    "page": page_num
                                },
                                "font": span["font"],
                                "fontSize": span["size"],
                                "color": span["color"]
                            })
            
            # Extract images with precise positioning
            try:
                # Get all images on the page
                image_list = page.get_images(full=True)
                
                for img_index, img in enumerate(image_list):
                    try:
                        xref = img[0]  # Image reference number
                        base_image = doc.extract_image(xref)
                        
                        if base_image:
                            image_data = base_image["image"]
                            image_ext = base_image["ext"]
                            
                            # Find image on page and get its rectangle
                            image_bbox = None
                            for img_info in page.get_image_info():
                                try:
                                    if img_info["xref"] == xref:
                                        # Get the image rectangle directly from the page
                                        image_bbox = fitz.Rect(img_info["bbox"])
                                        break
                                except Exception as e:
                                    print(f"Error getting image bbox: {str(e)}", file=sys.stderr)
                                    continue
                            
                            # If we found the image position
                            if image_bbox:
                                # Normalize coordinates to 0-1 range
                                image_dict = {
                                    'data': base64.b64encode(image_data).decode('utf-8'),
                                    'width': base_image.get('width'),
                                    'height': base_image.get('height'),
                                    'position': {
                                        'x': image_bbox.x0 / page_width,
                                        'y': image_bbox.y0 / page_height,
                                        'width': (image_bbox.x1 - image_bbox.x0) / page_width,
                                        'height': (image_bbox.y1 - image_bbox.y0) / page_height,
                                        'page': page_num
                                    },
                                    'format': image_ext
                                }
                                images.append(image_dict)
                                print(f"Extracted image {img_index + 1} from page {page_num} ({image_ext} format)", file=sys.stderr)
                            else:
                                # If we can't find position, use default values
                                image_dict = {
                                    'data': base64.b64encode(image_data).decode('utf-8'),
                                    'width': base_image.get('width'),
                                    'height': base_image.get('height'),
                                    'position': {
                                        'x': 0,
                                        'y': 0,
                                        'width': 0.5,  # Use 50% of page width as default
                                        'height': 0.5 * (base_image.get('height', 100) / base_image.get('width', 100)),
                                        'page': page_num
                                    },
                                    'format': image_ext
                                }
                                images.append(image_dict)
                                print(f"Added image {img_index + 1} with default position", file=sys.stderr)
                                
                    except Exception as e:
                        print(f"Error processing image {img_index} on page {page_num}: {str(e)}", file=sys.stderr)
                        continue
                        
            except Exception as e:
                print(f"Error extracting images from page {page_num}: {str(e)}", file=sys.stderr)
                continue
        
        # Combine text content by pages
        text_by_pages = {}
        for content in structured_content:
            page_num = content["position"]["page"]
            if page_num not in text_by_pages:
                text_by_pages[page_num] = []
            text_by_pages[page_num].append(content)
        
        # Create final result
        result = {
            'text': "\n".join([content["content"] for content in structured_content]),
            'structured_content': {
                'pages': [
                    {
                        'page_number': page_num,
                        'content': text_by_pages.get(page_num, [])
                    }
                    for page_num in range(1, total_pages + 1)
                ]
            },
            'images': images,
            'page_count': total_pages,
            'image_count': len(images),
            'file_size': len(file_content)
        }
        
        # Close the document
        doc.close()
        print(f"Successfully processed PDF with {total_pages} pages and {len(images)} images", file=sys.stderr)
        
        return result
        
    except Exception as e:
        try:
            if 'doc' in locals() and doc is not None:
                doc.close()
        except:
            pass
        print(f"PDF processing error: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        raise

def process_docx(file_path: str):
    """Process a Word document and extract text and images."""
    try:
        # Verify file before processing
        is_valid, error_msg = verify_file(file_path)
        if not is_valid:
            raise ValueError(error_msg)

        print(f"Opening Word document: {file_path}", file=sys.stderr)
        doc = docx.Document(file_path)
        
        # Initialize variables
        text = []
        images = []
        paragraph_count = 0
        
        # Process paragraphs
        print("Processing paragraphs", file=sys.stderr)
        for para in doc.paragraphs:
            if para.text.strip():  # Only add non-empty paragraphs
                text.append(para.text)
                paragraph_count += 1
        
        # Process tables
        print("Processing tables", file=sys.stderr)
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():  # Only add non-empty cells
                        row_text.append(cell.text.strip())
                if row_text:  # Only add non-empty rows
                    text.append(" | ".join(row_text))
        
        # Process images
        print("Processing images", file=sys.stderr)
        for rel in doc.part.rels.values():
            if "image" in rel.target_ref:
                try:
                    image_data = rel._target.blob
                    images.append({
                        'data': base64.b64encode(image_data).decode('utf-8')
                    })
                    print("Extracted image from document", file=sys.stderr)
                except Exception as e:
                    print(f"Image extraction error: {str(e)}", file=sys.stderr)
                    continue
        
        result = {
            'text': '\n'.join(text),
            'images': images,
            'paragraph_count': paragraph_count,
            'image_count': len(images)
        }
        
        print(f"Successfully processed Word document with {paragraph_count} paragraphs and {len(images)} images", file=sys.stderr)
        return result
        
    except Exception as e:
        print(f"DOCX processing error: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        raise

def process_text(file_path: str):
    """Process a text file."""
    try:
        # Wait for file to be ready
        if not wait_for_file(file_path):
            raise ValueError(f"Timeout waiting for file to be ready: {file_path}")

        # Verify file before processing
        is_valid, error_msg = verify_file(file_path)
        if not is_valid:
            raise ValueError(error_msg)

        print(f"Opening text file: {file_path}", file=sys.stderr)
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()
            print(f"Read {len(text)} characters", file=sys.stderr)
            return {'text': text, 'images': [], 'character_count': len(text)}
    except Exception as e:
        print(f"Text file processing error: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        raise

def process_document(file_path: str):
    """Main function to process documents of various types."""
    try:
        print(f"Processing file: {file_path}", file=sys.stderr)
        
        # Basic file verification
        is_valid, error_msg = verify_file(file_path)
        if not is_valid:
            return {'error': error_msg}
        
        file_ext = Path(file_path).suffix.lower()
        print(f"File extension: {file_ext}", file=sys.stderr)
        
        # Process based on file type
        if file_ext == '.pdf':
            result = process_pdf(file_path)
        elif file_ext in ['.docx', '.doc']:
            result = process_docx(file_path)
        elif file_ext == '.txt':
            result = process_text(file_path)
        else:
            return {'error': f'Unsupported file format: {file_ext}'}
        
        # Add metadata to result
        result['title'] = Path(file_path).stem
        result['file_type'] = file_ext
        result['file_size'] = os.path.getsize(file_path)
        return result
        
    except Exception as e:
        error_msg = f"Error processing document: {str(e)}"
        print(error_msg, file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return {'error': error_msg}

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(json.dumps({'error': 'No file provided'}))
        sys.exit(1)
    
    file_path = sys.argv[1]
    print(f"Processing file: {file_path}", file=sys.stderr)
    
    result = process_document(file_path)
    if 'error' in result:
        print(f"Error: {result['error']}", file=sys.stderr)
        print(json.dumps(result))
        sys.exit(1)
    else:
        print(f"Successfully processed file with {len(result.get('text', ''))} characters", file=sys.stderr)
        print(json.dumps(result))
        sys.exit(0) 