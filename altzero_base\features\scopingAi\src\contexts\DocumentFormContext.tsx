import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
} from "react";
import { useToast } from "../../../../base/hooks/use-toast";
import {
  DocumentFormContextType,
  DocumentFormProviderProps,
  PromptTemplate,
  ClientFormData,
  ReferenceDocument,
  AiPrompts,
  ProjectInfo,
} from "../types/document-form";
import {
  generateDocument,
  prepareDocumentData,
} from "../utils/documentGeneration";
import {
  fetchClients,
  createClient,
  type Client,
} from "../services/clientService";
import { useUser } from "../../../../base/contextapi/UserContext";

// Create context with default values
export const DocumentFormContext = createContext<DocumentFormContextType>(
  {} as DocumentFormContextType
);

// Custom hook to use the context
export const useDocumentForm = () => useContext(DocumentFormContext);

// Define templates
const templates = [
  {
    id: "standard",
    name: "Standard Scope",
    description: "Comprehensive scope document for most projects",
    sections: [
      "Project Overview",
      "Objectives",
      "Deliverables",
      "Timeline",
      "Budget",
      "Resources",
    ],
  },
  {
    id: "agile",
    name: "Agile Project",
    description: "Focused on sprints and iterative development",
    sections: [
      "Project Vision",
      "User Stories",
      "Sprint Planning",
      "Acceptance Criteria",
      "Team Structure",
    ],
  },
  {
    id: "minimal",
    name: "Minimal Scope",
    description: "Simplified scope for smaller projects",
    sections: ["Overview", "Deliverables", "Timeline", "Budget"],
  },
  {
    id: "custom",
    name: "Custom Template",
    description: "Build your own template from scratch",
    sections: ["Custom sections"],
  },
];

// Context provider component
export const DocumentFormProvider: React.FC<DocumentFormProviderProps> = ({
  children,
}) => {
  const { toast } = useToast();
  const { user } = useUser();

  // State variables
  const [currentStep, setCurrentStep] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSavingClient, setIsSavingClient] = useState(false);
  const [isSavingPrompt, setIsSavingPrompt] = useState(false);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [clientsError, setClientsError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [referenceDocument, setReferenceDocument] =
    useState<ReferenceDocument | null>(null);
  const [aiPrompts, setAiPrompts] = useState<AiPrompts>({
    styleGuidance: "",
    contentFocus: "",
    additionalInstructions: "",
  });
  const [requirementsOption, setRequirementsOption] = useState("requirements");
  const [requirementsText, setRequirementsText] = useState("");
  const [selectedKnowledgeDocuments, setSelectedKnowledgeDocuments] = useState<
    string[]
  >([]);
  const [documentRequirements, setDocumentRequirements] = useState<
    Record<string, string>
  >({});

  // Generation tracking state
  const [generationStatus, setGenerationStatus] = useState(
    "Starting generation..."
  );
  const [generationProgress, setGenerationProgress] = useState(0);
  const [documentId, setDocumentId] = useState<string | null>(null);

  // EventSource reference
  const eventSourceRef = useRef<EventSource | null>(null);

  // Client state
  const [clients, setClients] = useState<Client[]>([]);
  const [clientSearchOpen, setClientSearchOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [clientData, setClientData] = useState<ClientFormData>({
    id: undefined,
    name: "",
    contactPerson: "",
    email: "",
    phone: "",
    industry: "",
    company: "",
  });
  const [isNewClient, setIsNewClient] = useState(false);

  // Prompt template state
  const [promptTemplates, setPromptTemplates] = useState<PromptTemplate[]>([
    {
      id: "1",
      name: "Standard Project Scoping Prompt",
      description:
        "Comprehensive project analysis and scope definition for enterprise clients",
      content: "Standard project scoping content",
      variables: [],
    },
    {
      id: "2",
      name: "Technical Requirements Prompt",
      description: "Focused on technical specifications and architecture",
      content: "Technical requirements content",
      variables: [],
    },
    {
      id: "3",
      name: "Agile Development Prompt",
      description: "Sprint-based development with iterative delivery",
      content: "Agile development content",
      variables: [],
    },
  ]);
  const [promptSearchOpen, setPromptSearchOpen] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<PromptTemplate | null>(
    null
  );
  const [promptData, setPromptData] = useState<PromptTemplate>({
    id: "",
    name: "",
    description: "",
    content: "",
    variables: [],
  });
  const [isNewPrompt, setIsNewPrompt] = useState(false);
  const [isPromptDialogOpen, setIsPromptDialogOpen] = useState(false);

  // Project info state
  const [projectInfo, setProjectInfo] = useState<ProjectInfo>({
    title: "",
    description: "",
    startDate: "",
    endDate: "",
    budget: "medium", // Default budget value
  });

  // Add a useEffect to clear requirements text when switching to upload
  useEffect(() => {
    if (requirementsOption === "upload") {
      // Add a small delay to ensure the warning is shown first
      const timer = setTimeout(() => {
        setRequirementsText("");
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [requirementsOption]);

  // Load clients when user is authenticated
  useEffect(() => {
    if (user && !isLoadingClients && clients.length === 0) {
      loadClients();
    }
  }, [user]);

  // Load clients from Supabase
  const loadClients = async (): Promise<Client[]> => {
    setIsLoadingClients(true);
    setClientsError(null);

    try {
      const clientsData = await fetchClients();
      setClients(clientsData);
      return clientsData;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to load clients";
      setClientsError(errorMessage);
      console.error("Error loading clients:", error);
      // Removed toast error to prevent UI spam
      return [];
    } finally {
      setIsLoadingClients(false);
    }
  };

  // Handle client selection
  const handleClientSelect = (clientId: string) => {
    const client = clients.find((c) => c.id === clientId);
    if (client) {
      setClientData({
        id: client.id,
        name: client.name,
        contactPerson: client.contactPerson || "",
        email: client.email,
        phone: client.phone || "",
        industry: client.industry || "",
        company: client.company || "",
      });
      setSelectedClient(client);
      setIsNewClient(false);
      setClientSearchOpen(false);
    }
  };

  // Handle client input changes
  const handleClientInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setClientData((prev) => ({ ...prev, [name]: value }));

    // If user is typing a new name, mark as new client and clear ID
    if (name === "name") {
      setClientData((prev) => ({ ...prev, id: undefined }));
      setIsNewClient(true);
      setSelectedClient(null);
    }
  };

  // Handle project info changes
  const handleProjectInfoChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;
    let fieldName = "";

    // Map form field IDs to state properties
    switch (id) {
      case "project-title":
        fieldName = "title";
        break;
      case "project-description":
        fieldName = "description";
        break;
      case "start-date":
        fieldName = "startDate";
        break;
      case "end-date":
        fieldName = "endDate";
        break;
      default:
        fieldName = id.replace("project-", "").replace("-", "");
    }

    setProjectInfo((prev) => ({ ...prev, [fieldName]: value }));
  };

  // Handle budget changes
  const handleBudgetChange = (value: string) => {
    setProjectInfo((prev) => ({ ...prev, budget: value }));
  };

  // Handle prompt selection
  const handlePromptSelect = (promptId: string) => {
    const prompt = promptTemplates.find((p) => p.id === promptId);
    if (prompt) {
      setPromptData({
        id: prompt.id,
        name: prompt.name,
        description: prompt.description,
        content: prompt.content,
        variables: prompt.variables,
      });
      setSelectedPrompt(prompt);
      setIsNewPrompt(false);
      setPromptSearchOpen(false);
    }
  };

  // Handle prompt input changes
  const handlePromptInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setPromptData((prev) => ({ ...prev, [name]: value }));

    // If user is typing a new name, mark as new prompt
    if (name === "name") {
      setIsNewPrompt(true);
      setSelectedPrompt(null);
    }
  };

  // Handle AI prompt changes
  const handlePromptChange = (field: keyof AiPrompts, value: string) => {
    setAiPrompts((prev) => ({ ...prev, [field]: value }));
  };

  // Save client data
  const saveClient = async () => {
    setIsSavingClient(true);
    try {
      if (isNewClient && clientData.name) {
        // Create new client in Supabase
        await createClient({
          name: clientData.name,
          contactPerson: clientData.contactPerson,
          email: clientData.email,
          phone: clientData.phone,
          industry: clientData.industry,
        });

        // Reload clients to get the updated list with the new client
        const updatedClients = await loadClients();

        // Find the newly created client and select it
        const newClient = updatedClients.find(
          (c) => c.name === clientData.name
        );
        if (newClient) {
          setSelectedClient(newClient);
        }

        setIsNewClient(false);

        toast({
          title: "Client saved successfully",
          description:
            "Client information has been saved and can now be selected",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to save client";
      console.error("Error saving client:", error);
      // Removed toast error to prevent UI spam
    } finally {
      setIsSavingClient(false);
    }
  };

  // Save prompt template data
  const savePromptTemplate = async () => {
    setIsSavingPrompt(true);
    try {
      // Mock API call - replace with actual implementation
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Add the new prompt to the list if it's truly new
      if (
        isNewPrompt &&
        !promptTemplates.find((p) => p.name === promptData.name)
      ) {
        const newPrompt: PromptTemplate = {
          ...promptData,
          id: Date.now().toString(),
          created_at: new Date().toISOString(),
        };
        setPromptTemplates((prev) => [...prev, newPrompt]);
        setSelectedPrompt(newPrompt);
      }

      setIsNewPrompt(false);

      toast({
        title: "Prompt template saved successfully",
        description: "Prompt template has been saved",
      });
    } catch (error) {
      console.error("Error saving prompt template:", error);
      // Removed toast error to prevent UI spam
    } finally {
      setIsSavingPrompt(false);
    }
  };

  // Navigation handlers
  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    } else if (currentStep === 4) {
      // Start generation process
      handleGenerate(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleRemoveReference = () => {
    setReferenceDocument(null);
    // Reset template to standard when reference is removed
    setSelectedTemplate("");
  };

  const handleTemplateSelect = (templateId: string) => {
    // Only allow changing template if no reference document is selected
    if (!referenceDocument) {
      setSelectedTemplate(templateId);
    }
  };

  // Generation handler
  const handleGenerate = async (preserveOriginalContent: boolean) => {
    console.log(
      "handleGenerate called with preserveOriginalContent:",
      preserveOriginalContent
    );
    console.log("Current project info:", projectInfo);
    console.log("Current client data:", clientData);
    console.log("Current selected template:", selectedTemplate);
    console.log("Selected knowledge documents:", selectedKnowledgeDocuments);
    console.log("Document requirements:", documentRequirements);

    // Validate required fields before starting generation
    if (!projectInfo.title || projectInfo.title.trim() === "") {
      console.log("Project title is required");
      // Removed toast error to prevent UI spam
      return;
    }

    if (!clientData.name || clientData.name.trim() === "") {
      console.log("Client information is required");
      // Removed toast error to prevent UI spam
      return;
    }

    if (
      requirementsOption === "requirements" &&
      (!requirementsText || requirementsText.trim() === "")
    ) {
      console.log("Requirements are required");
      // Removed toast error to prevent UI spam
      return;
    }

    if (
      requirementsOption === "knowledge" &&
      (!selectedKnowledgeDocuments || selectedKnowledgeDocuments.length === 0)
    ) {
      console.log("Knowledge base documents are required");
      // Removed toast error to prevent UI spam
      return;
    }

    setIsGenerating(true);
    setGenerationStatus("Preparing document generation...");
    setGenerationProgress(5);

    try {
      // Prepare document data with all real information
      const documentData = prepareDocumentData(
        projectInfo,
        clientData,
        selectedClient,
        selectedTemplate,
        referenceDocument,
        requirementsOption,
        requirementsText,
        selectedKnowledgeDocuments,
        documentRequirements,
        aiPrompts,
        templates
      );

      console.log(
        "Document title being sent for generation:",
        projectInfo.title
      );
      console.log("Sending document data for generation:", documentData);
      console.log(
        "Knowledge base documents included:",
        selectedKnowledgeDocuments
      );
      console.log("Preserve original content:", preserveOriginalContent);

      // Additional validation after data preparation
      if (!documentData.title) {
        throw new Error("Project title is required for document generation");
      }

      if (!documentData.client?.name && !clientData.name) {
        throw new Error(
          "Client information is required for document generation"
        );
      }

      if (requirementsOption === "requirements" && !requirementsText.trim()) {
        throw new Error(
          "Requirements text is required when manual entry is selected"
        );
      }

      if (
        requirementsOption === "knowledge" &&
        (!selectedKnowledgeDocuments || selectedKnowledgeDocuments.length === 0)
      ) {
        throw new Error("Please select at least one knowledge base document");
      }

      // Define callbacks for the streaming events
      const callbacks = {
        onStarted: (data: any) => {
          console.log("Generation started with data:", data);
          setGenerationStatus(
            preserveOriginalContent
              ? "Preparing document with original content..."
              : "Analyzing requirements and starting generation..."
          );
          setGenerationProgress(15);

          // Set document ID from event data or generate a fallback
          const newDocId =
            data.documentId || data.proposal?.id || `doc_${Date.now()}`;
          console.log("Setting document ID:", newDocId);
          setDocumentId(newDocId);
        },
        onProgress: (data: any) => {
          console.log("🔥 REAL-TIME PROGRESS UPDATE:", data);
          console.log("🔥 Progress:", data.progress, "Message:", data.message);
          // Update progress with real-time data from backend
          setGenerationProgress(data.progress || 0);
          setGenerationStatus(data.message || "Processing...");
        },
        onResearch: () => {
          setGenerationStatus(
            preserveOriginalContent
              ? "Organizing document structure..."
              : selectedKnowledgeDocuments.length > 0
              ? "Analyzing knowledge base documents..."
              : "Researching your requirements..."
          );
          setGenerationProgress(35);
        },
        onSummary: () => {
          setGenerationStatus(
            preserveOriginalContent
              ? "Preparing document summary..."
              : "Creating comprehensive document summary..."
          );
          setGenerationProgress(55);
        },
        onSection: (data: any) => {
          setGenerationStatus(
            preserveOriginalContent
              ? `Preparing ${data.section?.title || data.title || "section"}...`
              : `Generating ${
                  data.section?.title || data.title || "section"
                }...`
          );
          setGenerationProgress(75);
        },
        onCompleted: (data: any) => {
          console.log("Generation completed with data:", data);
          setGenerationStatus("Finalizing document...");
          setGenerationProgress(95);

          // Store document ID if available, with multiple fallback options
          const newDocId =
            data.proposal?.id ||
            data.documentId ||
            data.id ||
            documentId ||
            `doc_${Date.now()}`;
          console.log("Setting final document ID:", newDocId);
          setDocumentId(newDocId);
        },
        onEnd: (data: any) => {
          console.log("Generation ended with data:", data);
          // Set the status to indicate generation is complete
          setGenerationStatus("Document generation complete!");
          setGenerationProgress(100);

          // Get the document ID with multiple fallback options
          const docId =
            data.documentId ||
            data.proposal?.id ||
            data.id ||
            documentId ||
            `doc_${Date.now()}`;
          console.log("Final docId for navigation:", docId);

          // Ensure we have a valid document ID
          if (!docId || docId === "null" || docId === "undefined") {
            const fallbackId = `doc_${Date.now()}`;
            console.warn(
              "No valid document ID found, using fallback:",
              fallbackId
            );
            setDocumentId(fallbackId);
          }

          // Update state to move to next step
          setIsLoading(false);
          setCurrentStep(5); // Move to the document view step

          // Navigate to the generated document page after a short delay
          setTimeout(() => {
            // Use the final document ID or the fallback
            const finalDocId =
              docId && docId !== "null" && docId !== "undefined"
                ? docId
                : `doc_${Date.now()}`;

            // Include the title and preserveOriginalContent flag in the URL
            const titleParam = projectInfo.title
              ? `&title=${encodeURIComponent(projectInfo.title)}`
              : "";
            const preserveParam = preserveOriginalContent
              ? `&preserveOriginal=true`
              : "";
            const knowledgeParam =
              selectedKnowledgeDocuments.length > 0
                ? `&knowledgeUsed=true&knowledgeCount=${selectedKnowledgeDocuments.length}`
                : "";

            const finalUrl = `/scopingai/documents/generated?id=${finalDocId}${titleParam}${preserveParam}${knowledgeParam}`;
            console.log("Navigating to:", finalUrl);
            window.location.href = finalUrl;
          }, 1500);
        },
        onError: (error: any) => {
          console.error("Generation error:", error);
          setIsLoading(false);
          setIsGenerating(false);
          setGenerationStatus("Generation failed");
          setGenerationProgress(0);

          const errorMessage =
            error?.message ||
            "There was an error generating your document. Please try again.";
          toast({
            title: "Generation Failed",
            description: errorMessage,
            variant: "destructive",
          });
        },
      };

      // Start the streaming process
      await generateDocument(
        documentData,
        preserveOriginalContent,
        callbacks,
        eventSourceRef
      );
    } catch (error) {
      console.error("Error in handleGenerate:", error);
      setIsLoading(false);
      setIsGenerating(false);
      setGenerationStatus("Generation failed");
      setGenerationProgress(0);

      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      toast({
        title: "Generation Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  // Combine all values and functions to provide via context
  const contextValue: DocumentFormContextType = {
    // State
    currentStep,
    isGenerating,
    isLoading,
    isSavingClient,
    isSavingPrompt,
    isLoadingClients,
    clientsError,
    selectedTemplate,
    referenceDocument,
    aiPrompts,
    requirementsOption,
    requirementsText,
    selectedKnowledgeDocuments,
    documentRequirements,
    generationStatus,
    generationProgress,
    documentId,
    clients,
    clientSearchOpen,
    selectedClient,
    clientData,
    isNewClient,
    promptTemplates,
    promptSearchOpen,
    selectedPrompt,
    promptData,
    isNewPrompt,
    isPromptDialogOpen,
    projectInfo,
    eventSourceRef,

    // Methods
    setCurrentStep,
    setIsGenerating,
    setIsLoading,
    setIsLoadingClients,
    setClientsError,
    setSelectedTemplate,
    setReferenceDocument,
    handlePromptChange,
    setRequirementsOption,
    setRequirementsText,
    setSelectedKnowledgeDocuments,
    setDocumentRequirements,
    setGenerationStatus,
    setGenerationProgress,
    setDocumentId,
    setClients,
    setClientSearchOpen,
    setSelectedClient,
    setClientData,
    setIsNewClient,
    setIsSavingClient,
    setPromptTemplates,
    setPromptSearchOpen,
    setSelectedPrompt,
    setPromptData,
    setIsNewPrompt,
    setIsSavingPrompt,
    setIsPromptDialogOpen,
    setProjectInfo,
    handleClientInputChange,
    handleProjectInfoChange,
    handleBudgetChange,
    handleClientSelect,
    handlePromptSelect,
    handlePromptInputChange,
    saveClient,
    savePromptTemplate,
    loadClients,
    handleGenerate,
    handleNext,
    handleBack,
    handleRemoveReference,
    handleTemplateSelect,
  };

  return (
    <DocumentFormContext.Provider value={contextValue}>
      {children}
    </DocumentFormContext.Provider>
  );
};
