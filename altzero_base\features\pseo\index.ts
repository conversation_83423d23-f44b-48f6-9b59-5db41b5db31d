import React from 'react';
import { RouteObject } from 'react-router-dom';
import { PluginModule, NavigationItem } from '../../plugins/types';
import PSEODashboard from './pages/PSEODashboard';
import CreateBlogPost from './pages/CreateBlogPost';
import KeywordResearch from './pages/KeywordResearch';
import WebsiteManagement from './pages/WebsiteManagement';
import ClientManagement from './pages/ClientManagement';
import AuditRunner from './pages/SingleSiteAudit/AuditRunner';
import AuditResults from './pages/Results/AuditResults';
import AuditHistory from './pages/Results/AuditHistory';
import FullSiteAnalysis from './pages/FullSiteAnalysis';
import FullSiteAnalysisResults from './pages/Results/FullSiteAnalysisResults';
import FullSiteAnalysisHistory from './pages/Results/FullSiteAnalysisHistory';
import PageDiscoveryResults from './pages/Results/PageDiscoveryResults';
import KeywordResearchResults from './pages/Results/KeywordResearchResults';
import ContentGenerationResults from './pages/Results/ContentGenerationResults';

// Define routes for the pSEO plugin
const routes: RouteObject[] = [
  {
    path: '/pseo',
    element: React.createElement(PSEODashboard)
  },
  {
    path: '/create-blog-post',
    element: React.createElement(CreateBlogPost)
  },
  {
    path: '/keyword-research',
    element: React.createElement(KeywordResearch)
  },
  {
    path: '/website-management',
    element: React.createElement(WebsiteManagement)
  },
  {
    path: '/client-management',
    element: React.createElement(ClientManagement)
  },
  {
    path: '/audit-runner',
    element: React.createElement(AuditRunner)
  },
  {
    path: '/audit-results/:auditId',
    element: React.createElement(AuditResults)
  },
  {
    path: '/pseo/history',
    element: React.createElement(AuditHistory)
  },
  {
    path: '/full-site-analysis',
    element: React.createElement(FullSiteAnalysis)
  },
  {
    path: '/full-site-analysis-results/:jobId',
    element: React.createElement(FullSiteAnalysisResults)
  },
  {
    path: '/full-site-analysis-results',
    element: React.createElement(FullSiteAnalysisHistory)
  },
  {
    path: '/page-discovery-results/:websiteId',
    element: React.createElement(PageDiscoveryResults)
  },
  {
    path: '/page-discovery-results',
    element: React.createElement(PageDiscoveryResults)
  },
  {
    path: '/keyword-research-results/:websiteId',
    element: React.createElement(KeywordResearchResults)
  },
  {
    path: '/keyword-research-results',
    element: React.createElement(KeywordResearchResults)
  },
  {
    path: '/content-generation-results/:websiteId',
    element: React.createElement(ContentGenerationResults)
  },
  {
    path: '/content-generation-results',
    element: React.createElement(ContentGenerationResults)
  },
  {
    path: '/pseo/content-history',
    element: React.createElement(ContentGenerationResults)
  },
  {
    path: '/pseo/keyword-history',
    element: React.createElement(KeywordResearchResults)
  }
];

// Define navigation items
const navigation: NavigationItem[] = [
  {
    name: 'pSEO',
    route: '/pseo',
    icon: 'BarChart3',
    order: 5,
    permissions: ['pseo:read']
  }
];

// pSEO plugin module
const pseoPlugin: PluginModule = {
  routes,
  navigation,
  providers: [], // No specific providers for pSEO yet
  config: {
    name: 'pSEO',
    version: '1.0.0',
    description: 'Programmatic SEO tools and analytics'
  },
  initialize: async () => {
    console.log('📊 pSEO plugin initialized');
  },
  cleanup: async () => {
    console.log('📊 pSEO plugin cleaned up');
  }
};

export default pseoPlugin; 