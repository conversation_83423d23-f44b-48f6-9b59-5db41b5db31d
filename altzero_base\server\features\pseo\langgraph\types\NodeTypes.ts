// =====================================================
// LANGGRAPH NODE TYPE DEFINITIONS
// =====================================================

import { PSEOWorkflowState, NodeResult, WorkflowContext } from './WorkflowState';

// Base node interface that all nodes must implement
export interface BaseNode {
  name: string;
  description: string;
  execute(context: WorkflowContext): Promise<Partial<PSEOWorkflowState>>;
  validate?(state: PSEOWorkflowState): Promise<boolean>;
  rollback?(context: WorkflowContext): Promise<void>;
}

// Node execution status
export type NodeStatus = 'pending' | 'running' | 'completed' | 'failed' | 'skipped';

// Node metadata for tracking and debugging
export interface NodeMetadata {
  node_name: string;
  execution_order: number;
  dependencies: string[];
  optional: boolean;
  timeout_seconds: number;
  retry_attempts: number;
  resource_requirements: {
    memory_mb: number;
    cpu_cores: number;
  };
}

// Node execution result with detailed metrics
export interface DetailedNodeResult extends NodeResult {
  node_name: string;
  status: NodeStatus;
  started_at: string;
  completed_at?: string;
  execution_time_ms: number;
  memory_used_mb?: number;
  retry_count: number;
  warnings: string[];
}

// Conditional node execution
export interface ConditionalNode extends BaseNode {
  condition: (state: PSEOWorkflowState) => boolean;
  true_path: string;
  false_path: string;
}

// Parallel execution node
export interface ParallelNode extends BaseNode {
  parallel_nodes: string[];
  wait_for_all: boolean;
  timeout_seconds: number;
}

// Human approval node
export interface HumanApprovalNode extends BaseNode {
  approval_message: string;
  approval_timeout: number;
  default_action: 'approve' | 'reject' | 'skip';
  approval_data?: any;
}

// Node configuration for different types
export interface NodeConfig {
  // Keyword Research Node
  keyword_research?: {
    max_keywords_per_source: number;
    enable_ai_expansion: boolean;
    quality_threshold: number;
    clustering_enabled: boolean;
  };
  
  // Competitor Analysis Node
  competitor_analysis?: {
    max_competitors: number;
    analysis_depth: 'basic' | 'detailed' | 'comprehensive';
    include_backlinks: boolean;
    include_content_gaps: boolean;
  };
  
  // Content Generation Node
  content_generation?: {
    content_types: string[];
    max_suggestions: number;
    ai_model: string;
    creativity_level: number;
  };
  
  // Validation Node
  validation?: {
    strict_mode: boolean;
    required_fields: string[];
    data_quality_checks: boolean;
  };
}

// Edge conditions for workflow routing
export interface EdgeCondition {
  source_node: string;
  target_node: string;
  condition: (state: PSEOWorkflowState) => boolean;
  condition_description: string;
}

// Workflow step definition
export interface WorkflowStep {
  step_id: string;
  node_name: string;
  node_config: NodeConfig;
  dependencies: string[];
  parallel_execution: boolean;
  optional: boolean;
  retry_policy: {
    max_attempts: number;
    backoff_strategy: 'linear' | 'exponential';
    retry_delay_ms: number;
  };
}

// Node registry entry
export interface NodeRegistryEntry {
  name: string;
  class_name: string;
  description: string;
  version: string;
  capabilities: string[];
  required_tools: string[];
  optional_tools: string[];
  resource_requirements: {
    memory_mb: number;
    cpu_cores: number;
    timeout_seconds: number;
  };
  configuration_schema: any; // JSON schema for node configuration
}

// Node execution context with additional metadata
export interface NodeExecutionContext extends WorkflowContext {
  node_metadata: NodeMetadata;
  execution_id: string;
  parent_workflow_id: string;
  retry_count: number;
  previous_attempts: DetailedNodeResult[];
}

// Node factory interface for dynamic node creation
export interface NodeFactory {
  createNode(node_type: string, config: NodeConfig): BaseNode;
  getAvailableNodes(): NodeRegistryEntry[];
  validateNodeConfig(node_type: string, config: NodeConfig): boolean;
}

// Node monitoring and observability
export interface NodeMonitoring {
  track_execution: (node_name: string, result: DetailedNodeResult) => void;
  get_node_metrics: (node_name: string, time_range?: string) => NodeMetrics;
  get_workflow_health: (workflow_id: string) => WorkflowHealth;
}

export interface NodeMetrics {
  node_name: string;
  total_executions: number;
  success_rate: number;
  average_execution_time: number;
  error_rate: number;
  common_errors: string[];
  resource_usage: {
    avg_memory_mb: number;
    avg_cpu_percent: number;
  };
}

export interface WorkflowHealth {
  workflow_id: string;
  overall_health: 'healthy' | 'warning' | 'critical';
  node_health: Record<string, 'healthy' | 'warning' | 'critical'>;
  bottlenecks: string[];
  recommendations: string[];
}

// Error handling and recovery
export interface NodeErrorHandler {
  handle_error: (error: Error, context: NodeExecutionContext) => Promise<'retry' | 'skip' | 'fail'>;
  get_recovery_suggestions: (error: Error) => string[];
}

// Node communication interface for inter-node messaging
export interface NodeCommunication {
  send_message: (target_node: string, message: any) => Promise<void>;
  receive_message: (source_node: string) => Promise<any>;
  broadcast_message: (message: any) => Promise<void>;
}

// Custom node types for specific SEO operations
export type SEONodeType = 
  | 'keyword_research'
  | 'competitor_analysis' 
  | 'content_generation'
  | 'technical_audit'
  | 'backlink_analysis'
  | 'rank_tracking'
  | 'content_optimization'
  | 'schema_generation'
  | 'site_speed_analysis'
  | 'mobile_optimization';

// Node priority levels for execution ordering
export type NodePriority = 'critical' | 'high' | 'medium' | 'low';

// Node execution mode
export type NodeExecutionMode = 'sequential' | 'parallel' | 'conditional' | 'loop';

export interface LoopNode extends BaseNode {
  loop_condition: (state: PSEOWorkflowState, iteration: number) => boolean;
  max_iterations: number;
  loop_body_nodes: string[];
}
