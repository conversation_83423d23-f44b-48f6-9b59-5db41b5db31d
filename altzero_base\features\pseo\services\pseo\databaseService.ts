import { supabase } from "../../../../base/utils/supabaseClient";
import { PSEO_CONSTANTS } from "../../utilities/pseo/constants";
import type { 
  PSEOClient, 
  PSEOWebsite, 
  PSEOAudit, 
  PSEOAuditStep,
  CreateClientRequest,
  CreateWebsiteRequest,
  ClientDashboardData,
  WebsiteDashboardData,
  PSEOError
} from '../../types';

export class PSEODatabaseService {
  // Client operations
  async createClient(clientData: CreateClientRequest & { user_id: string }): Promise<PSEOClient> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .insert(clientData)
        .select()
        .single();

      if (error) {
        throw this.createError('CLIENT_CREATE_FAILED', `Failed to create client: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('CLIENT_CREATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CLIENT_CREATE_FAILED', `Unexpected error creating client: ${errorMessage}`);
    }
  }

  async getClientsByUserId(userId: string): Promise<PSEOClient[]> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw this.createError('CLIENT_FETCH_FAILED', `Failed to fetch clients: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('CLIENT_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CLIENT_FETCH_FAILED', `Unexpected error fetching clients: ${errorMessage}`);
    }
  }

  async getClientDashboardData(userId: string): Promise<ClientDashboardData[]> {
    try {
      const { data, error } = await supabase
        .from('pseo_client_dashboard')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw this.createError('CLIENT_DASHBOARD_FAILED', `Failed to fetch client dashboard: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('CLIENT_DASHBOARD_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CLIENT_DASHBOARD_FAILED', `Unexpected error fetching client dashboard: ${errorMessage}`);
    }
  }

  async updateClient(clientId: string, updates: Partial<Omit<PSEOClient, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<PSEOClient> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .update(updates)
        .eq('id', clientId)
        .select()
        .single();

      if (error) {
        throw this.createError('CLIENT_UPDATE_FAILED', `Failed to update client: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('CLIENT_UPDATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CLIENT_UPDATE_FAILED', `Unexpected error updating client: ${errorMessage}`);
    }
  }

  async deleteClient(clientId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .delete()
        .eq('id', clientId);

      if (error) {
        throw this.createError('CLIENT_DELETE_FAILED', `Failed to delete client: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('CLIENT_DELETE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CLIENT_DELETE_FAILED', `Unexpected error deleting client: ${errorMessage}`);
    }
  }

  async getClientById(clientId: string): Promise<PSEOClient | null> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .select('*')
        .eq('id', clientId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('CLIENT_FETCH_FAILED', `Failed to fetch client: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('CLIENT_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CLIENT_FETCH_FAILED', `Unexpected error fetching client: ${errorMessage}`);
    }
  }

  // Website operations
  async createWebsite(websiteData: CreateWebsiteRequest): Promise<PSEOWebsite> {
    try {
      // Extract domain from URL
      const domain = this.extractDomain(websiteData.url);
      
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.WEBSITES)
        .insert({
          ...websiteData,
          domain,
          status: PSEO_CONSTANTS.WEBSITE_STATUS.ACTIVE
        })
        .select()
        .single();

      if (error) {
        throw this.createError('WEBSITE_CREATE_FAILED', `Failed to create website: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('WEBSITE_CREATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_CREATE_FAILED', `Unexpected error creating website: ${errorMessage}`);
    }
  }

  async getWebsitesByClientId(clientId: string): Promise<PSEOWebsite[]> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.WEBSITES)
        .select('*')
        .eq('client_id', clientId)
        .order('created_at', { ascending: false });

      if (error) {
        throw this.createError('WEBSITE_FETCH_FAILED', `Failed to fetch websites: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('WEBSITE_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_FETCH_FAILED', `Unexpected error fetching websites: ${errorMessage}`);
    }
  }

  async getWebsiteDashboardData(clientId: string): Promise<WebsiteDashboardData[]> {
    try {
      const { data, error } = await supabase
        .from('pseo_website_dashboard')
        .select('*')
        .eq('client_id', clientId)
        .order('created_at', { ascending: false });

      if (error) {
        throw this.createError('WEBSITE_DASHBOARD_FAILED', `Failed to fetch website dashboard: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('WEBSITE_DASHBOARD_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_DASHBOARD_FAILED', `Unexpected error fetching website dashboard: ${errorMessage}`);
    }
  }

  async updateWebsite(websiteId: string, updates: Partial<Omit<PSEOWebsite, 'id' | 'client_id' | 'created_at' | 'updated_at'>>): Promise<PSEOWebsite> {
    try {
      // If URL is being updated, update domain as well
      if (updates.url) {
        updates.domain = this.extractDomain(updates.url);
      }

      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.WEBSITES)
        .update(updates)
        .eq('id', websiteId)
        .select()
        .single();

      if (error) {
        throw this.createError('WEBSITE_UPDATE_FAILED', `Failed to update website: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('WEBSITE_UPDATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_UPDATE_FAILED', `Unexpected error updating website: ${errorMessage}`);
    }
  }

  async deleteWebsite(websiteId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.WEBSITES)
        .delete()
        .eq('id', websiteId);

      if (error) {
        throw this.createError('WEBSITE_DELETE_FAILED', `Failed to delete website: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('WEBSITE_DELETE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_DELETE_FAILED', `Unexpected error deleting website: ${errorMessage}`);
    }
  }

  async getWebsiteById(websiteId: string): Promise<PSEOWebsite | null> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.WEBSITES)
        .select('*')
        .eq('id', websiteId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('WEBSITE_FETCH_FAILED', `Failed to fetch website: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('WEBSITE_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_FETCH_FAILED', `Unexpected error fetching website: ${errorMessage}`);
    }
  }

  // Audit operations
  async createAudit(auditData: Omit<PSEOAudit, 'id' | 'created_at'>): Promise<PSEOAudit> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDITS)
        .insert(auditData)
        .select()
        .single();

      if (error) {
        throw this.createError('AUDIT_CREATE_FAILED', `Failed to create audit: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_CREATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_CREATE_FAILED', `Unexpected error creating audit: ${errorMessage}`);
    }
  }

  async updateAudit(auditId: string, updates: Partial<PSEOAudit>): Promise<PSEOAudit> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDITS)
        .update(updates)
        .eq('id', auditId)
        .select()
        .single();

      if (error) {
        throw this.createError('AUDIT_UPDATE_FAILED', `Failed to update audit: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_UPDATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_UPDATE_FAILED', `Unexpected error updating audit: ${errorMessage}`);
    }
  }

  async getAuditById(auditId: string): Promise<PSEOAudit | null> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDITS)
        .select('*')
        .eq('id', auditId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('AUDIT_FETCH_FAILED', `Failed to fetch audit: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_FETCH_FAILED', `Unexpected error fetching audit: ${errorMessage}`);
    }
  }

  async getAuditsByWebsiteId(websiteId: string): Promise<PSEOAudit[]> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDITS)
        .select('*')
        .eq('website_id', websiteId)
        .order('created_at', { ascending: false });

      if (error) {
        throw this.createError('AUDIT_FETCH_FAILED', `Failed to fetch audits: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_FETCH_FAILED', `Unexpected error fetching audits: ${errorMessage}`);
    }
  }

  async getAuditsByUserId(userId: string, limit: number = 10): Promise<Array<PSEOAudit & { website_name: string; website_url: string; client_name: string }>> {
    try {
      // Use optimized single query with joins instead of multiple queries
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDITS)
        .select(`
          *,
          pseo_websites!inner(
            id,
            name,
            url,
            pseo_clients!inner(
              id,
              name
            )
          )
        `)
        .eq('pseo_websites.pseo_clients.user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.warn('Optimized query failed, falling back to basic query:', error);
        
        // Fallback: Use the existing multi-step approach if the optimized query fails
        return await this.getAuditsByUserIdFallback(userId, limit);
      }

      // Transform the joined data to the expected format
      return (data || []).map(audit => ({
        ...audit,
        website_name: audit.pseo_websites?.name || 'Unknown Website',
        website_url: audit.pseo_websites?.url || '',
        client_name: audit.pseo_websites?.pseo_clients?.name || 'Unknown Client',
        // Remove the nested objects to keep the interface clean
        pseo_websites: undefined
      }));

    } catch (error: unknown) {
      console.warn('Primary audit query failed, using fallback:', error);
      return await this.getAuditsByUserIdFallback(userId, limit);
    }
  }

  // Fallback method for when joins fail (e.g., missing schema)
  private async getAuditsByUserIdFallback(userId: string, limit: number = 10): Promise<Array<PSEOAudit & { website_name: string; website_url: string; client_name: string }>> {
    try {
      // Step 1: Get all clients for this user
      const { data: userClients, error: clientsError } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .select('id, name')
        .eq('user_id', userId);

      if (clientsError) {
        throw this.createError('CLIENT_FETCH_FAILED', `Failed to fetch user clients: ${clientsError.message}`, { error: clientsError });
      }

      if (!userClients || userClients.length === 0) {
        return [];
      }

      const clientIds = userClients.map(c => c.id);

      // Step 2: Get all websites for these clients
      const { data: userWebsites, error: websitesError } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.WEBSITES)
        .select('id, name, url, client_id')
        .in('client_id', clientIds);

      if (websitesError) {
        throw this.createError('WEBSITE_FETCH_FAILED', `Failed to fetch user websites: ${websitesError.message}`, { error: websitesError });
      }

      if (!userWebsites || userWebsites.length === 0) {
        return [];
      }

      const websiteIds = userWebsites.map(w => w.id);

      // Step 3: Get audits with basic columns only to avoid schema issues
      const { data: audits, error: auditsError } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDITS)
        .select(`
          id, website_id, status, scraped_content, scrape_metadata,
          technical_audit_raw, content_audit_raw, technical_analysis, content_analysis,
          combined_report, report_html, ai_model_used, processing_time_seconds,
          error_message, created_at, completed_at
        `)
        .in('website_id', websiteIds)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (auditsError) {
        throw this.createError('AUDIT_FETCH_FAILED', `Failed to fetch audits: ${auditsError.message}`, { error: auditsError });
      }

      // Step 4: Combine the data manually
      return (audits || []).map(audit => {
        const website = userWebsites.find(w => w.id === audit.website_id);
        const client = userClients.find(c => c.id === website?.client_id);
        
        return {
          ...audit,
          website_name: website?.name || 'Unknown Website',
          website_url: website?.url || '',
          client_name: client?.name || 'Unknown Client',
        };
      });

    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_FETCH_FAILED', `Unexpected error fetching user audits: ${errorMessage}`);
    }
  }

  // Keyword research operations
  async saveKeywordResearchResults(data: {
    workflow_id: string;
    user_id: string;
    website_id?: string;
    research_method: 'website' | 'topic' | 'keywords';
    domain?: string;
    topic_input?: string;
    seed_keywords: string[];
    data_sources: string[];
    keywords: any[];
    keyword_clusters?: any[];
    competitor_data?: any[];
    api_calls_made: any[];
    total_cost: number;
    processing_time: number;
    status: 'completed' | 'failed';
    error_message?: string;
  }): Promise<any> {
    try {
      const { data: result, error } = await supabase
        .from('pseo_keyword_research')
        .insert({
          workflow_id: data.workflow_id,
          user_id: data.user_id,
          website_id: data.website_id,
          research_method: data.research_method,
          domain: data.domain,
          topic_input: data.topic_input,
          seed_keywords: data.seed_keywords,
          data_sources: data.data_sources,
          keywords: data.keywords,
          keyword_clusters: data.keyword_clusters || [],
          competitor_data: data.competitor_data || [],
          api_calls_made: data.api_calls_made,
          total_cost: data.total_cost,
          processing_time_seconds: data.processing_time,
          status: data.status,
          error_message: data.error_message,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw this.createError('KEYWORD_RESEARCH_SAVE_FAILED', `Failed to save keyword research: ${error.message}`, { error });
      }

      return result;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('KEYWORD_RESEARCH_SAVE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('KEYWORD_RESEARCH_SAVE_FAILED', `Unexpected error saving keyword research: ${errorMessage}`);
    }
  }

  async getKeywordResearchByUserId(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('pseo_keyword_research')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw this.createError('KEYWORD_RESEARCH_FETCH_FAILED', `Failed to fetch keyword research: ${error.message}`, { error });
      }

      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('KEYWORD_RESEARCH_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('KEYWORD_RESEARCH_FETCH_FAILED', `Unexpected error fetching keyword research: ${errorMessage}`);
    }
  }

  async getKeywordResearchByWorkflowId(workflowId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('pseo_keyword_research')
        .select('*')
        .eq('workflow_id', workflowId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('KEYWORD_RESEARCH_FETCH_FAILED', `Failed to fetch keyword research: ${error.message}`, { error });
      }

      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('KEYWORD_RESEARCH_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('KEYWORD_RESEARCH_FETCH_FAILED', `Unexpected error fetching keyword research: ${errorMessage}`);
    }
  }

  // Audit step operations
  async createAuditStep(stepData: Omit<PSEOAuditStep, 'id'>): Promise<PSEOAuditStep> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDIT_STEPS)
        .insert(stepData)
        .select()
        .single();

      if (error) {
        throw this.createError('AUDIT_STEP_CREATE_FAILED', `Failed to create audit step: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_STEP_CREATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_STEP_CREATE_FAILED', `Unexpected error creating audit step: ${errorMessage}`);
    }
  }

  async updateAuditStep(stepId: string, updates: Partial<PSEOAuditStep>): Promise<PSEOAuditStep> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDIT_STEPS)
        .update(updates)
        .eq('id', stepId)
        .select()
        .single();

      if (error) {
        throw this.createError('AUDIT_STEP_UPDATE_FAILED', `Failed to update audit step: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_STEP_UPDATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_STEP_UPDATE_FAILED', `Unexpected error updating audit step: ${errorMessage}`);
    }
  }

  async getAuditSteps(auditId: string): Promise<PSEOAuditStep[]> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDIT_STEPS)
        .select('*')
        .eq('audit_id', auditId)
        .order('started_at', { ascending: true });

      if (error) {
        throw this.createError('AUDIT_STEP_FETCH_FAILED', `Failed to fetch audit steps: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_STEP_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_STEP_FETCH_FAILED', `Unexpected error fetching audit steps: ${errorMessage}`);
    }
  }

  // Utility methods
  extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid URL format';
      throw this.createError('INVALID_URL', PSEO_CONSTANTS.ERROR_MESSAGES.INVALID_URL, { url, error: errorMessage });
    }
  }

  private createError(code: string, message: string, details?: Record<string, unknown>): PSEOError {
    return {
      code,
      message,
      details,
      timestamp: new Date().toISOString()
    };
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.CLIENTS)
        .select('id')
        .limit(1);

      return !error;
    } catch (error: unknown) {
      return false;
    }
  }

  // Add missing methods that are referenced in workflow service
  async getCompletedAuditSteps(auditId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDIT_STEPS)
        .select('step_name')
        .eq('audit_id', auditId)
        .eq('status', PSEO_CONSTANTS.STEP_STATUS.COMPLETED);

      if (error) {
        throw this.createError('AUDIT_STEP_FETCH_FAILED', `Failed to fetch completed steps: ${error.message}`, { error });
      }
      
      return (data || []).map(step => step.step_name);
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_STEP_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_STEP_FETCH_FAILED', `Unexpected error fetching completed steps: ${errorMessage}`);
    }
  }

  async getRunningAuditSteps(auditId: string): Promise<PSEOAuditStep[]> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDIT_STEPS)
        .select('*')
        .eq('audit_id', auditId)
        .eq('status', PSEO_CONSTANTS.STEP_STATUS.RUNNING);

      if (error) {
        throw this.createError('AUDIT_STEP_FETCH_FAILED', `Failed to fetch running steps: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_STEP_FETCH_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_STEP_FETCH_FAILED', `Unexpected error fetching running steps: ${errorMessage}`);
    }
  }

  async updateAuditStepByName(auditId: string, stepName: string, updates: Partial<PSEOAuditStep>): Promise<PSEOAuditStep> {
    try {
      const { data, error } = await supabase
        .from(PSEO_CONSTANTS.TABLE_NAMES.AUDIT_STEPS)
        .update(updates)
        .eq('audit_id', auditId)
        .eq('step_name', stepName)
        .select()
        .single();

      if (error) {
        throw this.createError('AUDIT_STEP_UPDATE_FAILED', `Failed to update audit step: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      if (error instanceof Error && error.message.includes('AUDIT_STEP_UPDATE_FAILED')) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AUDIT_STEP_UPDATE_FAILED', `Unexpected error updating audit step: ${errorMessage}`);
    }
  }

  // =====================================================
  // AGENTIC pSEO DATABASE METHODS - NEW FUNCTIONALITY
  // =====================================================

  // Agent Job Management
  async createAgentJob(websiteId: string, jobType: string, agentName: string, options?: {
    parentJobId?: string;
    priority?: number;
    inputData?: Record<string, unknown>;
  }): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_jobs')
        .insert({
          website_id: websiteId,
          job_type: jobType,
          agent_name: agentName,
          parent_job_id: options?.parentJobId,
          priority: options?.priority || 0,
          status: 'queued',
          result_data: options?.inputData || {},
          retry_count: 0,
          max_retries: 3
        })
        .select('id')
        .single();

      if (error) {
        throw this.createError('AGENT_JOB_CREATE_FAILED', `Failed to create agent job: ${error.message}`, { error });
      }
      
      return data.id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AGENT_JOB_CREATE_FAILED', `Unexpected error creating agent job: ${errorMessage}`);
    }
  }

  async updateAgentJob(jobId: string, updates: {
    status?: string;
    startedAt?: string;
    completedAt?: string;
    processingTimeSeconds?: number;
    resultData?: Record<string, unknown>;
    errorMessage?: string;
    retryCount?: number;
  }): Promise<void> {
    try {
      const updateData: Record<string, unknown> = {};
      
      if (updates.status) updateData.status = updates.status;
      if (updates.startedAt) updateData.started_at = updates.startedAt;
      if (updates.completedAt) updateData.completed_at = updates.completedAt;
      if (updates.processingTimeSeconds) updateData.processing_time_seconds = updates.processingTimeSeconds;
      if (updates.resultData) updateData.result_data = updates.resultData;
      if (updates.errorMessage) updateData.error_message = updates.errorMessage;
      if (updates.retryCount !== undefined) updateData.retry_count = updates.retryCount;

      const { error } = await supabase
        .from('pseo_agent_jobs')
        .update(updateData)
        .eq('id', jobId);

      if (error) {
        throw this.createError('AGENT_JOB_UPDATE_FAILED', `Failed to update agent job: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AGENT_JOB_UPDATE_FAILED', `Unexpected error updating agent job: ${errorMessage}`);
    }
  }

  async getAgentJobs(websiteId: string, options?: {
    status?: string;
    jobType?: string;
    limit?: number;
  }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_agent_jobs')
        .select('*')
        .eq('website_id', websiteId)
        .order('created_at', { ascending: false });

      if (options?.status) {
        query = query.eq('status', options.status);
      }
      if (options?.jobType) {
        query = query.eq('job_type', options.jobType);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('AGENT_JOBS_FETCH_FAILED', `Failed to fetch agent jobs: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AGENT_JOBS_FETCH_FAILED', `Unexpected error fetching agent jobs: ${errorMessage}`);
    }
  }

  // Website Pages Management
  async saveDiscoveredPages(websiteId: string, pages: Array<{
    url: string;
    title?: string;
    metaDescription?: string;
    pageType?: string;
    status?: string;
    discoveredVia?: string;
    lastCrawled?: string;
    wordCount?: number;
    responseCode?: number;
    loadTimeMs?: number;
  }>): Promise<void> {
    try {
      const insertData = pages.map(page => ({
        website_id: websiteId,
        url: page.url,
        title: page.title,
        meta_description: page.metaDescription,
        page_type: page.pageType || 'page',
        status: page.status || 'discovered',
        discovered_via: page.discoveredVia || 'manual',
        last_crawled: page.lastCrawled,
        word_count: page.wordCount,
        response_code: page.responseCode,
        load_time_ms: page.loadTimeMs
      }));

      const { error } = await supabase
        .from('pseo_website_pages')
        .upsert(insertData, { 
          onConflict: 'website_id,url',
          ignoreDuplicates: false 
        });

      if (error) {
        throw this.createError('PAGES_SAVE_FAILED', `Failed to save discovered pages: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('PAGES_SAVE_FAILED', `Unexpected error saving discovered pages: ${errorMessage}`);
    }
  }

  async getWebsitePages(websiteId: string, options?: {
    status?: string;
    pageType?: string;
    limit?: number;
  } | { limit?: number }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_website_pages')
        .select('*')
        .eq('website_id', websiteId)
        .order('created_at', { ascending: false });

      // Handle both complex and simple options interfaces
      if (options) {
        if ('status' in options && options.status) {
          query = query.eq('status', options.status);
        }
        if ('pageType' in options && options.pageType) {
          query = query.eq('page_type', options.pageType);
        }
        if (options.limit) {
          query = query.limit(options.limit);
        }
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('PAGES_FETCH_FAILED', `Failed to fetch website pages: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('PAGES_FETCH_FAILED', `Unexpected error fetching website pages: ${errorMessage}`);
    }
  }

  // Keywords Management
  async saveKeywordData(websiteId: string, keywords: Array<{
    keyword: string;
    searchVolume?: number;
    keywordDifficulty?: number;
    cpc?: number;
    competition?: string;
    rankingPosition?: number;
    rankingUrl?: string;
    intent?: string;
    dataSource?: string;
  }>): Promise<void> {
    try {
      const insertData = keywords.map(kw => ({
        website_id: websiteId,
        keyword: kw.keyword,
        search_volume: kw.searchVolume,
        keyword_difficulty: kw.keywordDifficulty,
        cpc: kw.cpc,
        competition: kw.competition,
        ranking_position: kw.rankingPosition,
        ranking_url: kw.rankingUrl,
        intent: kw.intent,
        data_source: kw.dataSource || 'manual'
      }));

      const { error } = await supabase
        .from('pseo_keywords')
        .upsert(insertData, { 
          onConflict: 'website_id,keyword',
          ignoreDuplicates: false 
        });

      if (error) {
        throw this.createError('KEYWORDS_SAVE_FAILED', `Failed to save keyword data: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('KEYWORDS_SAVE_FAILED', `Unexpected error saving keyword data: ${errorMessage}`);
    }
  }

  async getKeywordResearch(websiteId: string, options?: {
    dataSource?: string;
    intent?: string;
    minSearchVolume?: number;
    limit?: number;
  } | { limit?: number }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_keywords')
        .select('*')
        .eq('website_id', websiteId)
        .order('search_volume', { ascending: false });

      // Handle both complex and simple options interfaces
      if (options) {
        if ('dataSource' in options && options.dataSource) {
          query = query.eq('data_source', options.dataSource);
        }
        if ('intent' in options && options.intent) {
          query = query.eq('intent', options.intent);
        }
        if ('minSearchVolume' in options && options.minSearchVolume) {
          query = query.gte('search_volume', options.minSearchVolume);
        }
        if (options.limit) {
          query = query.limit(options.limit);
        }
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('KEYWORDS_FETCH_FAILED', `Failed to fetch keyword research: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('KEYWORDS_FETCH_FAILED', `Unexpected error fetching keyword research: ${errorMessage}`);
    }
  }

  // Content Opportunities Management
  async saveContentOpportunities(websiteId: string, opportunities: Array<{
    targetKeyword: string;
    contentType: string;
    title: string;
    contentBrief?: string;
    targetWordCount?: number;
    priority?: string;
    difficultyScore?: number;
    estimatedTraffic?: number;
    status?: string;
    generatedContent?: string;
    aiModelUsed?: string;
  }>): Promise<void> {
    try {
      const insertData = opportunities.map(opp => ({
        website_id: websiteId,
        target_keyword: opp.targetKeyword,
        content_type: opp.contentType,
        title: opp.title,
        content_brief: opp.contentBrief,
        target_word_count: opp.targetWordCount,
        priority: opp.priority || 'medium',
        difficulty_score: opp.difficultyScore,
        estimated_traffic: opp.estimatedTraffic,
        status: opp.status || 'identified',
        generated_content: opp.generatedContent,
        ai_model_used: opp.aiModelUsed
      }));

      const { error } = await supabase
        .from('pseo_content_opportunities')
        .upsert(insertData, { 
          onConflict: 'website_id,target_keyword,content_type',
          ignoreDuplicates: false 
        });

      if (error) {
        throw this.createError('CONTENT_OPPORTUNITIES_SAVE_FAILED', `Failed to save content opportunities: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CONTENT_OPPORTUNITIES_SAVE_FAILED', `Unexpected error saving content opportunities: ${errorMessage}`);
    }
  }

  async getContentOpportunities(websiteId: string, options?: {
    priority?: string;
    status?: string;
    contentType?: string;
    limit?: number;
  } | { limit?: number }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_content_opportunities')
        .select('*')
        .eq('website_id', websiteId)
        .order('estimated_traffic', { ascending: false });

      // Handle both complex and simple options interfaces
      if (options) {
        if ('priority' in options && options.priority) {
          query = query.eq('priority', options.priority);
        }
        if ('status' in options && options.status) {
          query = query.eq('status', options.status);
        }
        if ('contentType' in options && options.contentType) {
          query = query.eq('content_type', options.contentType);
        }
        if (options.limit) {
          query = query.limit(options.limit);
        }
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('CONTENT_OPPORTUNITIES_FETCH_FAILED', `Failed to fetch content opportunities: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CONTENT_OPPORTUNITIES_FETCH_FAILED', `Unexpected error fetching content opportunities: ${errorMessage}`);
    }
  }

  // Website Analysis Summary
  async updateWebsiteAnalysis(websiteId: string, analysis: {
    totalPages?: number;
    crawledPages?: number;
    totalKeywords?: number;
    rankingKeywords?: number;
    totalBacklinks?: number;
    referringDomains?: number;
    domainAuthority?: number;
    organicTrafficEstimate?: number;
    contentGapsIdentified?: number;
    technicalIssuesCount?: number;
    lastFullAnalysis?: string;
    analysisStatus?: string;
  }): Promise<void> {
    try {
      const updateData: Record<string, unknown> = { website_id: websiteId };
      
      if (analysis.totalPages !== undefined) updateData.total_pages = analysis.totalPages;
      if (analysis.crawledPages !== undefined) updateData.crawled_pages = analysis.crawledPages;
      if (analysis.totalKeywords !== undefined) updateData.total_keywords = analysis.totalKeywords;
      if (analysis.rankingKeywords !== undefined) updateData.ranking_keywords = analysis.rankingKeywords;
      if (analysis.totalBacklinks !== undefined) updateData.total_backlinks = analysis.totalBacklinks;
      if (analysis.referringDomains !== undefined) updateData.referring_domains = analysis.referringDomains;
      if (analysis.domainAuthority !== undefined) updateData.domain_authority = analysis.domainAuthority;
      if (analysis.organicTrafficEstimate !== undefined) updateData.organic_traffic_estimate = analysis.organicTrafficEstimate;
      if (analysis.contentGapsIdentified !== undefined) updateData.content_gaps_identified = analysis.contentGapsIdentified;
      if (analysis.technicalIssuesCount !== undefined) updateData.technical_issues_count = analysis.technicalIssuesCount;
      if (analysis.lastFullAnalysis) updateData.last_full_analysis = analysis.lastFullAnalysis;
      if (analysis.analysisStatus) updateData.analysis_status = analysis.analysisStatus;

      const { error } = await supabase
        .from('pseo_website_analysis')
        .upsert(updateData, { 
          onConflict: 'website_id',
          ignoreDuplicates: false 
        });

      if (error) {
        throw this.createError('WEBSITE_ANALYSIS_UPDATE_FAILED', `Failed to update website analysis: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_ANALYSIS_UPDATE_FAILED', `Unexpected error updating website analysis: ${errorMessage}`);
    }
  }

  async getWebsiteAnalysis(websiteId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('pseo_website_analysis')
        .select('*')
        .eq('website_id', websiteId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('WEBSITE_ANALYSIS_FETCH_FAILED', `Failed to fetch website analysis: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('WEBSITE_ANALYSIS_FETCH_FAILED', `Unexpected error fetching website analysis: ${errorMessage}`);
    }
  }

  // Dashboard Views for Agentic Features
  async getWebsiteDiscoveryProgress(websiteId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('pseo_website_discovery_progress')
        .select('*')
        .eq('website_id', websiteId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('DISCOVERY_PROGRESS_FETCH_FAILED', `Failed to fetch discovery progress: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('DISCOVERY_PROGRESS_FETCH_FAILED', `Unexpected error fetching discovery progress: ${errorMessage}`);
    }
  }

  async getKeywordOpportunities(websiteId: string, options?: { limit?: number }): Promise<any[]> {
    try {
      // Get high-value, low-competition keywords from main keywords table
      let query = supabase
        .from('pseo_keywords')
        .select('*')
        .eq('website_id', websiteId)
        .gte('search_volume', 100) // At least 100 searches per month
        .lte('keyword_difficulty', 60) // Max difficulty of 60
        .order('search_volume', { ascending: false });

      if (options?.limit) {
        query = query.limit(options.limit);
      } else {
        query = query.limit(100); // Default limit
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('KEYWORD_OPPORTUNITIES_FETCH_FAILED', `Failed to fetch keyword opportunities: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('KEYWORD_OPPORTUNITIES_FETCH_FAILED', `Unexpected error fetching keyword opportunities: ${errorMessage}`);
    }
  }

  async getContentGaps(websiteId: string, options?: { limit?: number }): Promise<any[]> {
    try {
      // Query content opportunities that are identified as gaps
      let query = supabase
        .from('pseo_content_opportunities')
        .select('*')
        .eq('website_id', websiteId)
        .order('estimated_traffic', { ascending: false });

      if (options?.limit) {
        query = query.limit(options.limit);
      } else {
        query = query.limit(100); // Default limit
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('CONTENT_GAPS_FETCH_FAILED', `Failed to fetch content gaps: ${error.message}`, { error });
      }
      
      // Filter for gaps or return all opportunities as potential gaps
      return (data || []).map(item => ({
        ...item,
        gap_topic: item.target_keyword,
        target_keyword: item.target_keyword,
        description: item.content_brief || `Content gap opportunity for "${item.target_keyword}"`,
        competitor_count: 3, // Mock data
        opportunity_score: Math.min(100, Math.round((item.estimated_traffic || 0) / 10))
      }));
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('CONTENT_GAPS_FETCH_FAILED', `Unexpected error fetching content gaps: ${errorMessage}`);
    }
  }

  async getGeneratedContentItems(websiteId: string, options?: { limit?: number }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_content_items')
        .select(`
          *,
          opportunity:opportunity_id(
            target_keyword,
            priority,
            difficulty_score,
            estimated_traffic
          )
        `)
        .eq('website_id', websiteId)
        .eq('ai_generated', true)
        .order('created_at', { ascending: false });

      if (options?.limit) {
        query = query.limit(options.limit);
      } else {
        query = query.limit(100); // Default limit
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('GENERATED_CONTENT_FETCH_FAILED', `Failed to fetch generated content items: ${error.message}`, { error });
      }
      
      // Transform data to match expected format
      return (data || []).map(item => ({
        ...item,
        generated_content: item.content_markdown, // Map to expected property name
        target_keyword: item.opportunity?.target_keyword || item.target_keywords?.[0] || '',
        priority: item.opportunity?.priority || 'medium'
      }));
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('GENERATED_CONTENT_FETCH_FAILED', `Unexpected error fetching generated content items: ${errorMessage}`);
    }
  }

  async getAgentJobSummary(websiteId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_job_summary')
        .select('*')
        .eq('website_id', websiteId);

      if (error) {
        throw this.createError('AGENT_JOB_SUMMARY_FETCH_FAILED', `Failed to fetch agent job summary: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AGENT_JOB_SUMMARY_FETCH_FAILED', `Unexpected error fetching agent job summary: ${errorMessage}`);
    }
  }

  // Utility methods for agentic features
  async getAgentJobById(jobId: string): Promise<any | null> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_jobs')
        .select('*')
        .eq('id', jobId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw this.createError('AGENT_JOB_FETCH_FAILED', `Failed to fetch agent job: ${error.message}`, { error });
      }
      
      return data;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AGENT_JOB_FETCH_FAILED', `Unexpected error fetching agent job: ${errorMessage}`);
    }
  }

  async cancelAgentJob(jobId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('pseo_agent_jobs')
        .update({ 
          status: 'cancelled',
          completed_at: new Date().toISOString(),
          error_message: 'Job cancelled by user'
        })
        .eq('id', jobId);

      if (error) {
        throw this.createError('AGENT_JOB_CANCEL_FAILED', `Failed to cancel agent job: ${error.message}`, { error });
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('AGENT_JOB_CANCEL_FAILED', `Unexpected error cancelling agent job: ${errorMessage}`);
    }
  }

  async getRunningAgentJobs(websiteId?: string): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_agent_jobs')
        .select('*')
        .eq('status', 'running')
        .order('created_at', { ascending: false });

      if (websiteId) {
        query = query.eq('website_id', websiteId);
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('RUNNING_JOBS_FETCH_FAILED', `Failed to fetch running jobs: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('RUNNING_JOBS_FETCH_FAILED', `Unexpected error fetching running jobs: ${errorMessage}`);
    }
  }

  // Bulk operations for efficiency
  async bulkUpdateAgentJobs(updates: Array<{ jobId: string; updates: any }>): Promise<void> {
    try {
      // Note: This would ideally use a stored procedure or batch operation
      // For now, we'll process updates individually
      for (const update of updates) {
        await this.updateAgentJob(update.jobId, update.updates);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('BULK_UPDATE_FAILED', `Unexpected error in bulk update: ${errorMessage}`);
    }
  }

  // Full site analysis progress tracking
  async createFullSiteAnalysisJob(websiteId: string, analysisTypes: string[], options?: Record<string, unknown>): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('pseo_agent_jobs')
        .insert({
          website_id: websiteId,
          job_type: 'full_site_audit',
          agent_name: 'AgentOrchestrator',
          priority: 1,
          status: 'queued',
          result_data: {
            analysis_types: analysisTypes,
            options: options || {}
          },
          retry_count: 0,
          max_retries: 1
        })
        .select('id')
        .single();

      if (error) {
        throw this.createError('FULL_SITE_ANALYSIS_CREATE_FAILED', `Failed to create full site analysis job: ${error.message}`, { error });
      }
      
      return data.id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('FULL_SITE_ANALYSIS_CREATE_FAILED', `Unexpected error creating full site analysis job: ${errorMessage}`);
    }
  }

  // New method for backlink analysis
  async getBriefAnalysis(websiteId: string, options: { limit?: number } = {}): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('pseo_backlinks')
        .select('*')
        .eq('website_id', websiteId)
        .limit(options.limit || 100)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching brief analysis:', error);
      throw error;
    }
  }

  // Backlinks Management
  async getBacklinks(websiteId: string, options?: {
    status?: string;
    linkType?: string;
    minDomainAuthority?: number;
    limit?: number;
  }): Promise<any[]> {
    try {
      let query = supabase
        .from('pseo_backlinks')
        .select('*')
        .eq('website_id', websiteId)
        .order('domain_authority', { ascending: false });

      if (options) {
        if (options.status) {
          query = query.eq('status', options.status);
        }
        if (options.linkType) {
          query = query.eq('link_type', options.linkType);
        }
        if (options.minDomainAuthority) {
          query = query.gte('domain_authority', options.minDomainAuthority);
        }
        if (options.limit) {
          query = query.limit(options.limit);
        }
      } else {
        query = query.limit(200); // Default limit
      }

      const { data, error } = await query;

      if (error) {
        throw this.createError('BACKLINKS_FETCH_FAILED', `Failed to fetch backlinks: ${error.message}`, { error });
      }
      
      return data || [];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw this.createError('BACKLINKS_FETCH_FAILED', `Unexpected error fetching backlinks: ${errorMessage}`);
    }
  }
}

export const databaseService = new PSEODatabaseService(); 