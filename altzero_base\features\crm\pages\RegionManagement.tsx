import React, { useState, useEffect } from 'react';
import { MapPin, Search, Plus, Edit, Trash2 } from 'lucide-react';
import CRMLayout from '../components/CRMLayout';

interface Region {
  id: string;
  organisation_id: string;
  name: string;
  code?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

const RegionManagement: React.FC = () => {
  const [regions, setRegions] = useState<Region[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadRegions();
  }, []);

  const loadRegions = async () => {
    try {
      setLoading(true);
      // TODO: Implement API call to fetch regions
      // const response = await crmService.getRegions();
      // setRegions(response.data);
      
      // Mock data for now
      setRegions([
        {
          id: '1',
          organisation_id: 'org1',
          name: 'North America',
          code: 'NA',
          description: 'United States and Canada',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          organisation_id: 'org1',
          name: 'Europe',
          code: 'EU',
          description: 'European Union countries',
          created_at: '2024-01-16T10:00:00Z',
          updated_at: '2024-01-16T10:00:00Z'
        },
        {
          id: '3',
          organisation_id: 'org1',
          name: 'Asia Pacific',
          code: 'APAC',
          description: 'Asia and Pacific region',
          created_at: '2024-01-17T10:00:00Z',
          updated_at: '2024-01-17T10:00:00Z'
        }
      ]);
    } catch (error) {
      console.error('Error loading regions:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredRegions = regions.filter(region =>
    region.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    region.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    region.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <MapPin className="w-8 h-8 text-blue-600 mr-3" />
              Regions
            </h1>
            <p className="text-gray-600 mt-1">Manage geographic regions for your organization</p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            New Region
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search regions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Regions Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Region Name</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Code</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Description</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Created</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={5} className="text-center py-8 text-gray-500">
                      Loading regions...
                    </td>
                  </tr>
                ) : filteredRegions.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="text-center py-8 text-gray-500">
                      {searchTerm ? 'No regions found matching your search.' : 'No regions found. Create your first region to get started.'}
                    </td>
                  </tr>
                ) : (
                  filteredRegions.map((region) => (
                    <tr key={region.id} className="hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{region.name}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600 font-mono text-sm">
                          {region.code || '-'}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600">{region.description || '-'}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-600">
                          {new Date(region.created_at).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-red-600 transition-colors">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-800">
              <strong>{filteredRegions.length}</strong> regions configured
            </div>
            <div className="text-xs text-blue-600">
              💡 Tip: Use regions to organize contacts and companies by geographic location
            </div>
          </div>
        </div>
      </div>
    </CRMLayout>
  );
};

export default RegionManagement;
