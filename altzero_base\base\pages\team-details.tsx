import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "../components/ui/tabs";
import { Button } from "../components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "../components/ui/avatar";
import { Badge } from "../components/ui/badge";
import { toast } from "../hooks/use-toast";
import { InviteDialog } from "../components/invitation/InviteDialog";
import {
  getGroup,
  getGroupMembers,
  getGroupInvitations,
  isGroupAdmin,
} from "../services/teamService";
import { Group, GroupMember, GroupInvitation } from "../types/team";
import { MemberRole } from "../types/organization";

export default function TeamDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [team, setTeam] = useState<Group | null>(null);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (id) {
      fetchTeamDetails(id);
    }
  }, [id]);

  const fetchTeamDetails = async (teamId: string) => {
    setIsLoading(true);
    try {
      // Get team details
      const teamData = await getGroup(teamId);
      setTeam(teamData);

      // Check if current user is an admin
      const adminStatus = await isGroupAdmin(teamId);
      setIsAdmin(adminStatus);

      // Get members
      const membersList = await getGroupMembers(teamId);
      setMembers(membersList);

      // Get pending invitations (admins only)
      if (adminStatus) {
        const invitesList = await getGroupInvitations(teamId);
        setInvitations(invitesList);
      }
    } catch (error) {
      console.error("Error fetching team details:", error);
      toast({
        title: "Error",
        description: "Failed to load team details. Please try again.",
        variant: "destructive",
      });
      navigate("/teams");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteSent = () => {
    if (id) {
      fetchTeamDetails(id);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin w-10 h-10 border-4 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="container py-6 max-w-5xl">
        <div className="text-center p-10">
          <h2 className="text-2xl font-bold mb-2">Team not found</h2>
          <p className="text-muted-foreground mb-4">
            The team you're looking for doesn't exist or you don't have access
            to it.
          </p>
          <Button onClick={() => navigate("/teams")}>Back to Teams</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-3xl font-bold">{team.name}</h1>
          <p className="text-muted-foreground">
            {team.description || "No description provided"}
          </p>
          {team.organisation_id && (
            <Badge variant="outline" className="mt-2">
              Part of an organization
            </Badge>
          )}
        </div>
        <div className="flex space-x-2">
          {isAdmin && (
            <InviteDialog
              type="team"
              entityId={team.id}
              entityName={team.name}
              onSuccess={handleInviteSent}
              trigger={<Button>Invite Members</Button>}
            />
          )}
          <Button variant="outline" onClick={() => navigate("/teams")}>
            Back
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members ({members.length})</TabsTrigger>
          {isAdmin && (
            <TabsTrigger value="invitations">
              Invitations
              {invitations.length > 0 && (
                <span className="ml-2 px-2 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
                  {invitations.length}
                </span>
              )}
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Members</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{members.length}</p>
                <p className="text-muted-foreground">Total members</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Created</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg">
                  {new Date(team.created_at).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Your Role</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge
                  variant={isAdmin ? "default" : "outline"}
                  className="text-lg px-3 py-1"
                >
                  {isAdmin ? "Admin" : "Member"}
                </Badge>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <h3 className="text-xl font-semibold mb-4">Recent Activity</h3>
            <Card>
              <CardContent className="p-6 text-center text-muted-foreground">
                Coming soon: Team activity feed
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="members">
          {members.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground mb-4">No members found</p>
                {isAdmin && (
                  <InviteDialog
                    type="team"
                    entityId={team.id}
                    entityName={team.name}
                    onSuccess={handleInviteSent}
                  />
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {members.map((member) => (
                <Card key={member.id} className="flex items-center p-4">
                  <Avatar className="h-10 w-10 mr-4">
                    <AvatarImage src={member.avatar_url || undefined} />
                    <AvatarFallback>
                      {member.full_name?.charAt(0) || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium">
                      {member.full_name || "Unknown User"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {member.email || ""}
                    </p>
                  </div>
                  <Badge
                    variant={member.role === "admin" ? "default" : "outline"}
                  >
                    {member.role}
                  </Badge>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {isAdmin && (
          <TabsContent value="invitations">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Pending Invitations</h3>
              <InviteDialog
                type="team"
                entityId={team.id}
                entityName={team.name}
                onSuccess={handleInviteSent}
              />
            </div>

            {invitations.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-muted-foreground">
                  No pending invitations
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {invitations
                  .filter((invitation) => invitation.status === "pending")
                  .map((invitation) => (
                    <Card key={invitation.id} className="flex items-center p-4">
                      <div className="flex-1">
                        <p className="font-medium">{invitation.email}</p>
                        <p className="text-sm text-muted-foreground">
                          Invited:{" "}
                          {new Date(invitation.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant="secondary">pending</Badge>
                    </Card>
                  ))}
              </div>
            )}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
