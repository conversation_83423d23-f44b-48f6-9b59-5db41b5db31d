# ScopingAI LangGraph Implementation

## 🚀 Overview

This directory contains the **LangGraph workflow implementation** for ScopingAI, providing advanced AI-powered proposal generation with multi-step processing, state management, and quality assurance.

## 📁 Directory Structure

```
langgraph/
├── core/                    # Core workflow infrastructure
│   ├── BaseWorkflow.ts     # Abstract base class for workflows
│   ├── StateManager.ts     # Workflow state persistence
│   └── ToolRegistry.ts     # Tool and service registry
├── nodes/                   # Individual workflow nodes
│   ├── ValidationNode.ts   # Input validation
│   ├── KnowledgeRetrievalNode.ts  # Knowledge base integration
│   ├── ClientAnalysisNode.ts      # Client & market analysis
│   ├── ResearchAnalysisNode.ts    # Comprehensive research
│   ├── ExecutiveSummaryNode.ts    # Executive summary generation
│   ├── SectionGenerationNode.ts   # Proposal sections
│   ├── QualityReviewNode.ts       # Quality assessment
│   └── FinalizationNode.ts        # Final proposal assembly
├── workflows/               # Complete workflow definitions
│   └── ProposalWorkflow.ts # Main proposal generation workflow
├── routes/                  # API endpoints
│   └── langgraph.ts        # LangGraph-specific routes
├── types/                   # TypeScript type definitions
│   ├── WorkflowState.ts    # State and context types
│   └── NodeTypes.ts        # Node interface definitions
├── index.ts                # Main exports
└── README.md               # This file
```

## 🎯 Key Features

### ✅ **Advanced Workflow Orchestration**
- **Multi-step processing** with dependency management
- **State persistence** across workflow execution
- **Error recovery** and retry mechanisms
- **Progress tracking** with real-time updates

### ✅ **Intelligent Content Generation**
- **Knowledge base integration** with targeted retrieval
- **Client analysis** with industry insights
- **Market research** and competitive analysis
- **Quality assurance** with automated review

### ✅ **Scalable Architecture**
- **Modular node design** for easy extension
- **Tool registry** for service integration
- **Configuration management** with validation
- **Resource optimization** and cost tracking

## 🔧 Usage

### Basic Workflow Execution

```typescript
import { ProposalWorkflow } from './workflows/ProposalWorkflow';

// Create workflow with configuration
const workflow = await ProposalWorkflow.create({
  timeout_seconds: 600,
  quality_threshold: 0.8,
  enable_market_research: true
});

// Execute proposal generation
const result = await workflow.executeProposal({
  user_id: 'user123',
  client: {
    name: 'Acme Corp',
    industry: 'Technology',
    size: 'Enterprise'
  },
  project: {
    title: 'Digital Transformation',
    description: 'Modernize legacy systems...'
  },
  template: {
    name: 'Standard Proposal',
    sections: ['Introduction', 'Scope', 'Timeline', 'Budget']
  },
  selected_knowledge_documents: ['doc1', 'doc2'],
  document_requirements: {
    'doc1': 'Focus on pricing models',
    'doc2': 'Extract technical specifications'
  }
});
```

### API Endpoints

#### Execute Workflow
```bash
POST /api/scopingai/langgraph/workflow/execute
```

#### Stream Workflow (Real-time)
```bash
POST /api/scopingai/langgraph/workflow/stream
```

#### Get Workflow Status
```bash
GET /api/scopingai/langgraph/workflow/{workflowId}/status
```

## 🏗️ Workflow Architecture

### Node Execution Flow

```mermaid
graph TD
    A[Validation] --> B[Knowledge Retrieval]
    B --> C[Client Analysis]
    C --> D[Research Analysis]
    D --> E[Executive Summary]
    E --> F[Section Generation]
    F --> G[Quality Review]
    G --> H[Finalization]
    
    G -->|Quality Failed| F
    H --> I[Complete]
```

### State Management

Each workflow maintains comprehensive state including:
- **Input data** (client, project, requirements)
- **Generated content** (research, sections, summary)
- **Metadata** (processing time, costs, quality scores)
- **Error tracking** and recovery information

## 📊 Quality Assurance

### Multi-Level Quality Control

1. **Input Validation** - Ensures all required data is present
2. **Content Quality** - AI-powered assessment of generated content
3. **Business Value** - Validates alignment with client objectives
4. **Consistency** - Ensures uniform tone and messaging
5. **Completeness** - Verifies all sections are comprehensive

### Quality Metrics

- **Overall Score** (0-100)
- **Content Relevance** 
- **Technical Accuracy**
- **Business Value Focus**
- **Readability Score**
- **Consistency Rating**

## 🔌 Integration Points

### Knowledge Base
- **Pinecone vector search** for document retrieval
- **Targeted content extraction** based on requirements
- **Semantic similarity** matching

### CRM Integration
- **Client data** enrichment
- **Industry analysis** from CRM records
- **Historical project** patterns

### AI Services
- **OpenAI GPT models** for content generation
- **Structured data** extraction
- **Quality assessment** automation

## ⚙️ Configuration

### Default Configuration
```typescript
{
  timeout_seconds: 600,
  retry_attempts: 3,
  enable_caching: true,
  quality_threshold: 0.7,
  max_sections: 10,
  enable_market_research: true,
  enable_competitive_analysis: true
}
```

### Environment Variables
- `OPENAI_API_KEY` - Required for AI generation
- `PINECONE_API_KEY` - Required for knowledge base
- `SERP_API_KEY` - Optional for market research

## 🚀 Benefits vs Simple Implementation

### **Simple Implementation** (Current)
- ❌ Linear processing
- ❌ No error recovery
- ❌ Limited quality control
- ❌ No progress tracking
- ❌ Basic content generation

### **LangGraph Implementation** (New)
- ✅ **Multi-step orchestration** with dependency management
- ✅ **Intelligent error recovery** and retry mechanisms
- ✅ **Comprehensive quality assurance** at each step
- ✅ **Real-time progress tracking** with detailed status
- ✅ **Advanced content generation** with research integration
- ✅ **Scalable architecture** for future enhancements
- ✅ **Cost optimization** and resource management
- ✅ **Detailed analytics** and performance metrics

## 🎯 Use Cases

### **Enhanced Proposal Generation**
- Multi-source research integration
- Industry-specific content adaptation
- Competitive analysis inclusion
- Quality-assured output

### **Scalable Processing**
- Handle complex, multi-section proposals
- Process multiple proposals concurrently
- Optimize resource usage and costs
- Provide detailed progress feedback

### **Business Intelligence**
- Track proposal success patterns
- Analyze content quality trends
- Monitor processing performance
- Optimize workflow efficiency

## 🔮 Future Enhancements

- **Parallel node execution** for performance
- **Custom node development** for specific industries
- **Advanced caching** strategies
- **Machine learning** quality optimization
- **Integration** with additional data sources
- **Visual workflow** designer interface

---

**The LangGraph implementation transforms ScopingAI from a simple text generator into an intelligent, scalable proposal creation system that delivers professional, high-quality results with comprehensive quality assurance and real-time progress tracking.**
