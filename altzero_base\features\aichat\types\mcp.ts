/**
 * Model Context Protocol (MCP) Type Definitions
 * Based on @modelcontextprotocol/sdk and AltZero requirements
 */

// Core MCP Protocol Types
export interface MCPRequest {
  jsonrpc: "2.0";
  id: string | number;
  method: string;
  params?: Record<string, any>;
}

export interface MCPResponse {
  jsonrpc: "2.0";
  id: string | number;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

export interface MCPNotification {
  jsonrpc: "2.0";
  method: string;
  params?: Record<string, any>;
}

// Connection and Transport Types
export interface MCPTransport {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  send(message: MCPRequest | MCPResponse | MCPNotification): Promise<void>;
  onMessage(handler: (message: any) => void): void;
  onError(handler: (error: Error) => void): void;
  onClose(handler: () => void): void;
}

export interface MCPConnectionConfig {
  serverUrl: string;
  apiKey?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

// Server and Client Types
export interface MCPServer {
  name: string;
  version: string;
  capabilities: MCPCapabilities;
  tools?: MCPTool[];
  resources?: MCPResource[];
  prompts?: MCPPrompt[];
}

export interface MCPClient {
  connect(config: MCPConnectionConfig): Promise<void>;
  disconnect(): Promise<void>;
  sendRequest(method: string, params?: any): Promise<any>;
  callTool(name: string, args?: Record<string, any>): Promise<MCPToolResult>;
  getResource(uri: string): Promise<MCPResourceContent>;
  listTools(): Promise<MCPTool[]>;
  listResources(): Promise<MCPResource[]>;
  isConnected(): boolean;
}

// Capabilities
export interface MCPCapabilities {
  tools?: MCPToolCapability;
  resources?: MCPResourceCapability;
  prompts?: MCPPromptCapability;
  logging?: MCPLoggingCapability;
}

export interface MCPToolCapability {
  listChanged?: boolean;
}

export interface MCPResourceCapability {
  subscribe?: boolean;
  listChanged?: boolean;
}

export interface MCPPromptCapability {
  listChanged?: boolean;
}

export interface MCPLoggingCapability {
  level?: "debug" | "info" | "warning" | "error";
}

// Tool Types
export interface MCPTool {
  name: string;
  description: string;
  inputSchema: MCPToolInputSchema;
}

export interface MCPToolInputSchema {
  type: "object";
  properties: Record<string, MCPSchemaProperty>;
  required?: string[];
}

export interface MCPSchemaProperty {
  type: string;
  description?: string;
  enum?: any[];
  items?: MCPSchemaProperty;
  properties?: Record<string, MCPSchemaProperty>;
}

export interface MCPToolResult {
  content: MCPContent[];
  isError?: boolean;
}

// Resource Types
export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface MCPResourceContent {
  uri: string;
  mimeType?: string;
  text?: string;
  blob?: string;
}

// Prompt Types
export interface MCPPrompt {
  name: string;
  description?: string;
  arguments?: MCPPromptArgument[];
}

export interface MCPPromptArgument {
  name: string;
  description?: string;
  required?: boolean;
}

export interface MCPPromptMessage {
  role: "user" | "assistant";
  content: MCPContent;
}

// Content Types
export interface MCPContent {
  type: "text" | "image" | "resource";
  text?: string;
  data?: string;
  mimeType?: string;
}

// Supabase-Specific MCP Types
export interface SupabaseMCPConfig extends MCPConnectionConfig {
  projectUrl: string;
  serviceRoleKey: string;
  schema?: string;
  enableRLS?: boolean;
}

export interface DatabaseSchema {
  tables: DatabaseTable[];
  views: DatabaseView[];
  functions: DatabaseFunction[];
  enums: DatabaseEnum[];
}

export interface DatabaseTable {
  name: string;
  schema: string;
  columns: DatabaseColumn[];
  primaryKey?: string[];
  foreignKeys: DatabaseForeignKey[];
  description?: string;
}

export interface DatabaseColumn {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: any;
  description?: string;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
}

export interface DatabaseView {
  name: string;
  schema: string;
  definition: string;
  columns: DatabaseColumn[];
  description?: string;
}

export interface DatabaseFunction {
  name: string;
  schema: string;
  parameters: DatabaseParameter[];
  returnType: string;
  description?: string;
}

export interface DatabaseParameter {
  name: string;
  type: string;
  mode: "in" | "out" | "inout";
  defaultValue?: any;
}

export interface DatabaseForeignKey {
  columnName: string;
  referencedTable: string;
  referencedColumn: string;
  constraintName: string;
}

export interface DatabaseEnum {
  name: string;
  schema: string;
  values: string[];
  description?: string;
}

// Query Types
export interface ParsedQuery {
  entity: string;
  action: "select" | "insert" | "update" | "delete" | "count" | "aggregate";
  filters: QueryFilter[];
  aggregation?: QueryAggregation;
  timeFilter?: TimeFilter;
  sorting?: QuerySorting[];
  pagination?: QueryPagination;
  fields?: string[];
}

export interface QueryFilter {
  field: string;
  operator: "=" | "!=" | ">" | "<" | ">=" | "<=" | "like" | "ilike" | "in" | "not_in";
  value: any;
  logical?: "and" | "or";
}

export interface QueryAggregation {
  function: "count" | "sum" | "avg" | "min" | "max";
  field?: string;
  groupBy?: string[];
}

export interface TimeFilter {
  field: string;
  period: "today" | "yesterday" | "week" | "month" | "year" | "custom";
  customRange?: {
    start: string;
    end: string;
  };
}

export interface QuerySorting {
  field: string;
  direction: "asc" | "desc";
}

export interface QueryPagination {
  offset: number;
  limit: number;
}

// Query Result Types
export interface QueryResult {
  data: Record<string, any>[];
  totalCount?: number;
  affectedRows?: number;
  executionTime: number;
  query: string;
  metadata: QueryMetadata;
}

export interface QueryMetadata {
  tableUsed: string;
  fieldsReturned: string[];
  filtersApplied: QueryFilter[];
  orderBy?: QuerySorting[];
  limit?: number;
  offset?: number;
  hasMore?: boolean;
}

// Error Types
export interface MCPQueryError extends Error {
  code: string;
  query?: string;
  table?: string;
  details?: Record<string, any>;
}

// Event Types
export interface MCPEvent {
  type: string;
  timestamp: string;
  data: any;
}

export interface MCPConnectionEvent extends MCPEvent {
  type: "connection" | "disconnection" | "error" | "reconnect";
  serverInfo?: MCPServer;
  error?: Error;
}

export interface MCPQueryEvent extends MCPEvent {
  type: "query_start" | "query_complete" | "query_error";
  query: string;
  result?: QueryResult;
  error?: MCPQueryError;
  duration?: number;
}

// Status Types
export type MCPConnectionStatus = 
  | "disconnected" 
  | "connecting" 
  | "connected" 
  | "error" 
  | "reconnecting";

export interface MCPClientStatus {
  status: MCPConnectionStatus;
  serverInfo?: MCPServer;
  lastError?: Error;
  connectTime?: Date;
  lastActivity?: Date;
}

// Configuration Types
export interface MCPClientConfig {
  supabase: SupabaseMCPConfig;
  defaultTimeout: number;
  enableLogging: boolean;
  logLevel: "debug" | "info" | "warning" | "error";
  retryConfig: {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
  };
} 