import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON> } from "react-router-dom";
import {
  Plus,
  FileEdit,
  Loader2,
  Clock,
  Calendar,
  User,
  MoreHorizontal,
  Check,
  Trash2,
  Eye,
  Download,
  Grid3X3,
  List,
  Users,
  FileText,
  Search,
  CheckCircle,
  BookOpen,
} from "lucide-react";
import { But<PERSON> } from "../../../../base/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../../../base/components/ui/card";
import { Input } from "../../../../base/components/ui/input";
import { Badge } from "../../../../base/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../../base/components/ui/dropdown-menu";
import { useUser } from "../../../../base/contextapi/UserContext";
import { supabase } from "../lib/supabase";
import { useToast } from "../../../../base/hooks/use-toast";
import Layout from "../components/layout";

// Use the same interface from ScopingProposals.tsx
interface ScopingDocument {
  id: string;
  title: string;
  status: string;
  client_id?: string;
  created_at: string;
  updated_at: string;
  client_name?: string;
}

export default function Dashboard() {
  const navigate = useNavigate();
  const { user, isLoading: userIsLoading } = useUser();
  const [proposals, setProposals] = useState<ScopingDocument[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!userIsLoading && !user) {
      navigate("/login");
    }
  }, [user, userIsLoading, navigate]);

  // Fetch documents from Supabase - copied from ScopingProposals.tsx
  useEffect(() => {
    const fetchDocuments = async () => {
      // Skip if still loading auth or no user
      if (userIsLoading || !user) {
        return;
      }

      // Only load once to prevent continuous loading loops
      if (hasLoadedOnce) {
        return;
      }

      setIsLoading(true);

      try {
        // Use the user ID from @base context instead of direct supabase call
        const userId = user.id;
        if (!userId) {
          console.error("No user ID found");
          return;
        }

        // Fetch scoping documents
        const { data, error } = await supabase
          .from("scopingai_documents")
          .select(
            `
            id,
            title,
            created_at,
            updated_at,
            status,
            proposalFilePath,
            client_id,
            scopingai_clients (
              name
            )
          `
          )
          .eq("user_id", userId)
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching documents:", error);
          // Removed toast error to prevent UI spam
          console.error("Failed to load proposals:", error);
          return;
        }

        // Transform the data
        const transformedProposals = data.map((doc: any) => ({
          id: doc.id,
          title: doc.title,
          status: doc.status || "draft",
          client_id: doc.client_id,
          created_at: doc.created_at,
          updated_at: doc.updated_at,
          client_name: doc.scopingai_clients?.name || "Unknown Client",
        }));

        setProposals(transformedProposals);
        setHasLoadedOnce(true);
      } catch (error) {
        console.error("Error in fetchDocuments:", error);
        // Removed toast error to prevent UI spam
        console.error("An unexpected error occurred:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, [user, userIsLoading, hasLoadedOnce]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Delete a document - copied from ScopingProposals.tsx
  const handleDeleteDocument = async (id: string) => {
    try {
      // First ask for confirmation
      if (!confirm("Are you sure you want to delete this document?")) {
        return;
      }

      const { error } = await supabase
        .from("scoping_documents")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Document deleted successfully",
      });

      // Remove the deleted document from the state
      setProposals((prev) => prev.filter((doc) => doc.id !== id));
    } catch (error) {
      console.error("Error deleting document:", error);
      // Removed toast error to prevent UI spam
      console.error("Failed to delete document:", error);
    }
  };

  // Show loading state while checking authentication
  if (userIsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading dashboard...</p>
      </div>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8">
        {/* Professional Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              ScopingAI Dashboard
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your project proposals and scoping documents
            </p>
          </div>
          <Link to="/scopingai/documents/new">
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              New Proposal
            </Button>
          </Link>
        </div>

        {/* Statistics Cards */}
        <div className="flex flex-row gap-4 mb-8 overflow-x-auto">
          <Card className="flex-1 min-w-[200px] relative overflow-hidden bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-1">
                    Total Proposals
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {proposals.length}
                  </p>
                </div>
                <div className="p-2 rounded-lg bg-blue-100">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="flex-1 min-w-[200px] relative overflow-hidden bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-1">
                    Active Clients
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {
                      new Set(
                        proposals.map((p) => p.client_name).filter(Boolean)
                      ).size
                    }
                  </p>
                </div>
                <div className="p-2 rounded-lg bg-green-100">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="flex-1 min-w-[200px] relative overflow-hidden bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-1">
                    Draft Proposals
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {proposals.filter((p) => p.status === "draft").length}
                  </p>
                </div>
                <div className="p-2 rounded-lg bg-amber-100">
                  <Clock className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="flex-1 min-w-[200px] relative overflow-hidden bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-1">
                    Completed
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {proposals.filter((p) => p.status === "created").length}
                  </p>
                </div>
                <div className="p-2 rounded-lg bg-green-100">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link to="/scopingai/documents/new">
            <Card className="cursor-pointer transition-all duration-200 hover:shadow-md border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-lg bg-blue-100">
                    <Plus className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Create Proposal
                    </h3>
                    <p className="text-sm text-gray-600">
                      Start a new scoping document
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
          <Link to="/scopingai/knowledge-base">
            <Card className="cursor-pointer transition-all duration-200 hover:shadow-md border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-lg bg-green-100">
                    <BookOpen className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Knowledge Base
                    </h3>
                    <p className="text-sm text-gray-600">
                      Access document library
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
          <Link to="/scopingai/knowledge-base/clients">
            <Card className="cursor-pointer transition-all duration-200 hover:shadow-md border border-gray-200">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-3 rounded-lg bg-purple-100">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Manage Clients
                    </h3>
                    <p className="text-sm text-gray-600">Client information</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Search and View Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search proposals..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Recent Proposals Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Recent Proposals
            </h2>
            <Link
              to="/scopingai/proposals"
              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              View all
            </Link>
          </div>

          {/* Proposals Content */}
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-center space-y-4">
                <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto" />
                <p className="text-gray-600 text-lg">
                  Loading your proposals...
                </p>
              </div>
            </div>
          ) : proposals.length === 0 ? (
            <Card className="border border-gray-200">
              <CardContent className="p-12 text-center">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="p-4 rounded-lg bg-gray-100">
                    <FileEdit className="h-12 w-12 text-gray-600" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      No proposals yet
                    </h3>
                    <p className="text-gray-600 max-w-md">
                      Get started by creating your first scoping proposal.
                    </p>
                  </div>
                  <Link to="/scopingai/documents/new">
                    <Button className="gap-2">
                      <Plus className="h-4 w-4" />
                      Create New Proposal
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ) : viewMode === "grid" ? (
            // Grid View
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {proposals.slice(0, 6).map((proposal) => (
                <Card
                  key={proposal.id}
                  className="transition-shadow hover:shadow-md border border-gray-200"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {proposal.title}
                        </CardTitle>
                        <div className="flex items-center gap-2 text-gray-600">
                          <User className="h-4 w-4" />
                          <span className="font-medium">
                            {proposal.client_name}
                          </span>
                        </div>
                      </div>
                      <Badge
                        className={`${
                          proposal.status === "created"
                            ? "bg-green-100 text-green-800"
                            : proposal.status === "draft"
                            ? "bg-amber-100 text-amber-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {proposal.status === "created" && (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        )}
                        {proposal.status.charAt(0).toUpperCase() +
                          proposal.status.slice(1)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      <span>Created {formatDate(proposal.created_at)}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-4">
                    <Link
                      to={`/scopingai/documents/generated?id=${proposal.id}`}
                    >
                      <Button variant="outline" size="sm" className="gap-2">
                        <Eye className="h-4 w-4" />
                        View
                      </Button>
                    </Link>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem asChild>
                          <Link
                            to={`/scopingai/documents/generated?id=${proposal.id}`}
                          >
                            <FileEdit className="mr-2 h-4 w-4" />
                            Edit Proposal
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="mr-2 h-4 w-4" />
                          Download PDF
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteDocument(proposal.id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            // List View
            <div className="space-y-4">
              {proposals.slice(0, 5).map((proposal) => (
                <Card
                  key={proposal.id}
                  className="transition-shadow hover:shadow-md border border-gray-200"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 rounded-lg bg-blue-100">
                          <FileText className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="space-y-1">
                          <h3 className="font-semibold text-lg text-gray-900">
                            {proposal.title}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              <span>{proposal.client_name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              <span>{formatDate(proposal.created_at)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge
                          className={`${
                            proposal.status === "created"
                              ? "bg-green-100 text-green-800"
                              : proposal.status === "draft"
                              ? "bg-amber-100 text-amber-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {proposal.status === "created" && (
                            <CheckCircle className="mr-1 h-3 w-3" />
                          )}
                          {proposal.status.charAt(0).toUpperCase() +
                            proposal.status.slice(1)}
                        </Badge>
                        <Link
                          to={`/scopingai/documents/generated?id=${proposal.id}`}
                        >
                          <Button variant="outline" className="gap-2">
                            <Eye className="h-4 w-4" />
                            View
                          </Button>
                        </Link>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem asChild>
                              <Link
                                to={`/scopingai/documents/generated?id=${proposal.id}`}
                              >
                                <FileEdit className="mr-2 h-4 w-4" />
                                Edit Proposal
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download PDF
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteDocument(proposal.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
