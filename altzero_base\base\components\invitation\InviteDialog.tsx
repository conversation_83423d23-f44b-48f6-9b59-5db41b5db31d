import React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { <PERSON><PERSON> } from "../ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Tit<PERSON>,
  <PERSON><PERSON>Trigger,
} from "../ui/dialog";
import { Input } from "../ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { toast } from "../../hooks/use-toast";
import { inviteToOrganization } from "../../services/organizationService";
import { inviteToTeam } from "../../services/teamService";
import { MemberRole } from "../../types/organization";

type InviteType = "organization" | "team";

interface InviteFormSchema {
  email: string;
  role?: Member<PERSON><PERSON>;
}

const organizationFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  role: z.enum(["admin", "member"]).default("member"),
});

const teamFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
});

interface InviteDialogProps {
  type: InviteType;
  entityId: string;
  entityName: string;
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

export function InviteDialog({
  type,
  entityId,
  entityName,
  onSuccess,
  trigger,
}: InviteDialogProps) {
  const [open, setOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  const formSchema =
    type === "organization" ? organizationFormSchema : teamFormSchema;

  const form = useForm<InviteFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      role: "member",
    },
  });

  async function onSubmit(values: InviteFormSchema) {
    setIsLoading(true);
    try {
      let result;

      if (type === "organization") {
        result = await inviteToOrganization(
          entityId,
          values.email,
          values.role as MemberRole
        );
      } else {
        result = await inviteToTeam(entityId, values.email);
      }

      if (result) {
        toast({
          title: "Invitation sent",
          description: `Invitation sent to ${values.email}.`,
        });

        if (onSuccess) {
          onSuccess();
        }

        setOpen(false);
        form.reset();
      }
    } catch (error) {
      console.error(`Error inviting to ${type}:`, error);
      toast({
        title: "Error",
        description: `Failed to send invitation. ${
          (error as Error).message || "Please try again."
        }`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || <Button variant="outline">Invite Members</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite to {entityName}</DialogTitle>
          <DialogDescription>
            Send an invitation to join this {type}.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Email address"
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {type === "organization" && (
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Role</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="member" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Member - Can view and participate
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="admin" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Admin - Can invite others and manage settings
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Sending..." : "Send Invitation"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
