import html2pdf from "html2pdf.js";
import jsPD<PERSON> from "jspdf";

// Method 1: HTML to PDF (Recommended for styled documents)
export const generatePDFFromHTML = async (
  element: HTMLElement,
  filename: string,
  options?: any
) => {
  const defaultOptions = {
    margin: 1,
    filename: filename,
    image: { type: "jpeg", quality: 0.98 },
    html2canvas: { scale: 2, useCORS: true },
    jsPDF: { unit: "in", format: "letter", orientation: "portrait" },
  };

  const finalOptions = { ...defaultOptions, ...options };
  return html2pdf().set(finalOptions).from(element).save();
};

// Method 2: jsPDF with content extraction
export const generatePDFFromContent = (
  documentData: any,
  documentTheme: any, // TODO: Future enhancement - apply theme colors and fonts
  filename: string
) => {
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(20);
  doc.text(documentData.title || "Untitled Document", 20, 20);

  // Add client info if available
  if (documentData.client) {
    doc.setFontSize(12);
    doc.text(`Client: ${documentData.client}`, 20, 35);
  }

  // Add sections
  let yPosition = 50;
  const pageHeight = doc.internal.pageSize.height;
  const marginBottom = 20;

  if (documentData.sections && Array.isArray(documentData.sections)) {
    documentData.sections.forEach((section: any) => {
      // Check if we need a new page
      if (yPosition > pageHeight - marginBottom) {
        doc.addPage();
        yPosition = 20;
      }

      // Section title
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(
        section.title || section.name || "Untitled Section",
        20,
        yPosition
      );
      yPosition += 10;

      // Section content
      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");
      const content = section.content || section.description || "";
      const lines = doc.splitTextToSize(content, 170);

      // Check if content fits on current page
      const contentHeight = lines.length * 5;
      if (yPosition + contentHeight > pageHeight - marginBottom) {
        doc.addPage();
        yPosition = 20;
      }

      doc.text(lines, 20, yPosition);
      yPosition += contentHeight + 15;
    });
  }

  // If no sections, add a placeholder
  if (!documentData.sections || documentData.sections.length === 0) {
    doc.setFontSize(12);
    doc.text("No content available to display.", 20, yPosition);
  }

  doc.save(filename);
};
