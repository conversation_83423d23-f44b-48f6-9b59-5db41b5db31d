import html2pdf from "html2pdf.js";
import jsPDF from "jspdf";

// Enhanced PDF generation with professional formatting
export const generatePDFFromHTML = async (
  element: HTMLElement,
  filename: string,
  options?: any
) => {
  const defaultOptions = {
    margin: [0.75, 0.75, 0.75, 0.75], // Top, Right, Bottom, Left margins in inches
    filename: filename,
    image: {
      type: "jpeg",
      quality: 0.98,
      crossOrigin: "anonymous",
    },
    html2canvas: {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      removeContainer: true,
      imageTimeout: 15000,
      logging: false,
    },
    jsPDF: {
      unit: "in",
      format: "a4",
      orientation: "portrait",
      compress: true,
    },
    pagebreak: {
      mode: ["avoid-all", "css", "legacy"],
      before: ".page-break-before",
      after: ".page-break-after",
    },
  };

  const finalOptions = { ...defaultOptions, ...options };
  return html2pdf().set(finalOptions).from(element).save();
};

// Enhanced content-based PDF generation with professional formatting
export const generatePDFFromContent = (
  documentData: any,
  documentTheme: any,
  filename: string
) => {
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
    compress: true,
  });

  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 20;
  const contentWidth = pageWidth - margin * 2;

  // Theme colors
  const primaryColor = documentTheme?.headingColor || "#1a365d";
  const textColor = documentTheme?.textColor || "#2d3748";
  const accentColor = documentTheme?.accentColor || "#3182ce";

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : { r: 0, g: 0, b: 0 };
  };

  // Create cover page
  const createCoverPage = () => {
    // Background
    doc.setFillColor(255, 255, 255);
    doc.rect(0, 0, pageWidth, pageHeight, "F");

    // Logo (if available)
    if (documentTheme?.logo) {
      try {
        // Center logo at top
        const logoY = 60;
        const logoMaxWidth = 60;
        const logoMaxHeight = 40;

        // Note: In a real implementation, you'd need to handle image loading
        // For now, we'll add a placeholder for the logo space
        doc.setFillColor(240, 240, 240);
        doc.rect(
          (pageWidth - logoMaxWidth) / 2,
          logoY,
          logoMaxWidth,
          logoMaxHeight,
          "F"
        );

        doc.setFontSize(8);
        doc.setTextColor(150, 150, 150);
        doc.text("LOGO", pageWidth / 2, logoY + logoMaxHeight / 2, {
          align: "center",
        });
      } catch (error) {
        console.warn("Could not add logo to PDF:", error);
      }
    }

    // Title
    const titleY = documentTheme?.logo ? 140 : 100;
    doc.setFontSize(28);
    doc.setFont("helvetica", "bold");
    const titleRgb = hexToRgb(primaryColor);
    doc.setTextColor(titleRgb.r, titleRgb.g, titleRgb.b);

    const title = documentData.title || "Document Title";
    const titleLines = doc.splitTextToSize(title, contentWidth - 40);
    titleLines.forEach((line: string, index: number) => {
      doc.text(line, pageWidth / 2, titleY + index * 12, { align: "center" });
    });

    // Client information
    if (documentData.client) {
      const clientY = titleY + titleLines.length * 12 + 30;
      doc.setFontSize(16);
      doc.setFont("helvetica", "normal");
      const textRgb = hexToRgb(textColor);
      doc.setTextColor(textRgb.r, textRgb.g, textRgb.b);
      doc.text(`Prepared for: ${documentData.client}`, pageWidth / 2, clientY, {
        align: "center",
      });
    }

    // Document type
    if (documentData.type) {
      const typeY = documentData.client
        ? titleY + titleLines.length * 12 + 50
        : titleY + titleLines.length * 12 + 30;
      doc.setFontSize(14);
      doc.setFont("helvetica", "italic");
      doc.text(documentData.type, pageWidth / 2, typeY, { align: "center" });
    }

    // Date
    const dateY = pageHeight - 60;
    doc.setFontSize(12);
    doc.setFont("helvetica", "normal");
    const currentDate = new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    doc.text(currentDate, pageWidth / 2, dateY, { align: "center" });

    // Decorative line
    const lineY = dateY - 20;
    const accentRgb = hexToRgb(accentColor);
    doc.setDrawColor(accentRgb.r, accentRgb.g, accentRgb.b);
    doc.setLineWidth(0.5);
    doc.line(pageWidth / 2 - 30, lineY, pageWidth / 2 + 30, lineY);
  };

  // Create cover page
  createCoverPage();

  // Add content pages
  const sections = documentData.editableSections || documentData.sections || [];

  if (sections.length > 0) {
    sections.forEach((section: any, sectionIndex: number) => {
      // Add new page for content
      doc.addPage();

      let yPosition = margin + 10;

      // Add header with document title (smaller)
      doc.setFontSize(10);
      doc.setFont("helvetica", "normal");
      doc.setTextColor(100, 100, 100);
      doc.text(documentData.title || "Document", margin, 15);

      // Add page number
      const pageNum = sectionIndex + 2; // +2 because cover page is 1
      doc.text(`Page ${pageNum}`, pageWidth - margin, 15, { align: "right" });

      // Add separator line
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.2);
      doc.line(margin, 20, pageWidth - margin, 20);

      // Section title
      yPosition = 35;
      doc.setFontSize(18);
      doc.setFont("helvetica", "bold");
      const titleRgb = hexToRgb(primaryColor);
      doc.setTextColor(titleRgb.r, titleRgb.g, titleRgb.b);

      const sectionTitle =
        section.title || section.name || `Section ${sectionIndex + 1}`;
      doc.text(sectionTitle, margin, yPosition);

      // Underline for section title
      yPosition += 3;
      const accentRgb = hexToRgb(accentColor);
      doc.setDrawColor(accentRgb.r, accentRgb.g, accentRgb.b);
      doc.setLineWidth(0.8);
      doc.line(margin, yPosition, margin + 60, yPosition);

      yPosition += 15;

      // Section content
      doc.setFontSize(11);
      doc.setFont("helvetica", "normal");
      const textRgb = hexToRgb(textColor);
      doc.setTextColor(textRgb.r, textRgb.g, textRgb.b);

      const content = section.content || section.description || "";
      if (content) {
        // Process content with proper formatting
        const paragraphs = content.split("\n\n");

        paragraphs.forEach((paragraph: string) => {
          if (paragraph.trim()) {
            const lines = doc.splitTextToSize(paragraph.trim(), contentWidth);

            // Check if we need a new page
            if (yPosition + lines.length * 6 > pageHeight - margin) {
              doc.addPage();
              yPosition = margin + 10;

              // Add header on new page
              doc.setFontSize(10);
              doc.setTextColor(100, 100, 100);
              doc.text(documentData.title || "Document", margin, 15);
              doc.text(`Page ${pageNum} (continued)`, pageWidth - margin, 15, {
                align: "right",
              });
              doc.setDrawColor(200, 200, 200);
              doc.line(margin, 20, pageWidth - margin, 20);
              yPosition = 35;

              // Reset text formatting
              doc.setFontSize(11);
              doc.setTextColor(textRgb.r, textRgb.g, textRgb.b);
            }

            lines.forEach((line: string) => {
              doc.text(line, margin, yPosition);
              yPosition += 6;
            });

            yPosition += 4; // Extra space between paragraphs
          }
        });
      }
    });
  } else {
    // Add a content page even if no sections
    doc.addPage();
    doc.setFontSize(12);
    doc.setTextColor(100, 100, 100);
    doc.text("No content available to display.", margin, margin + 20);
  }

  // Save the document
  doc.save(filename);
};
