import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Wand2 } from 'lucide-react';
import Loading from '../../../components/common/Loading';

export default function InitialForm() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    company: '',
    documentType: '',
    client: '',
    objective: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.company.trim()) {
      newErrors.company = 'Please tell us about your company';
    }
    if (!formData.documentType.trim()) {
      newErrors.documentType = 'Please specify what you want to create';
    }
    if (!formData.client.trim()) {
      newErrors.client = 'Please specify your client';
    }
    if (!formData.objective.trim()) {
      newErrors.objective = 'Please specify your objective';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setLoading(false);
    navigate('/sections', { state: formData });
  };

  const handleInputChange = (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [field]: e.target.value });
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  return (
    <>
      {loading && <Loading />}
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center p-4">
        <div className="max-w-2xl w-full space-y-8 bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-indigo-900 mb-4 flex items-center justify-center gap-3">
              <Wand2 className="w-10 h-10" />
              What are we creating?
            </h1>
            <p className="text-gray-600 text-lg">
              Tell us what you'd like and we'll help you create the perfect document.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="grid gap-6">
              <div className="space-y-2">
                <label className="text-base font-semibold text-indigo-900 inline-block">
                  We're a <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="i.e. B2B marketing software company"
                  className={`w-full px-4 py-3 rounded-lg border ${
                    errors.company ? 'border-red-500 ring-red-100' : 'border-gray-300'
                  } focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 bg-white/50 backdrop-blur-sm text-gray-900 text-lg`}
                  value={formData.company}
                  onChange={handleInputChange('company')}
                  required
                />
                {errors.company && (
                  <p className="text-red-500 text-sm mt-1">{errors.company}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-base font-semibold text-indigo-900 inline-block">
                  make me a <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="i.e. Proposal, renewal, job ad..."
                  className={`w-full px-4 py-3 rounded-lg border ${
                    errors.documentType ? 'border-red-500 ring-red-100' : 'border-gray-300'
                  } focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 bg-white/50 backdrop-blur-sm text-gray-900 text-lg`}
                  value={formData.documentType}
                  onChange={handleInputChange('documentType')}
                  required
                />
                {errors.documentType && (
                  <p className="text-red-500 text-sm mt-1">{errors.documentType}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-base font-semibold text-indigo-900 inline-block">
                  for <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="i.e. IBM, Tesla, Starbucks..."
                  className={`w-full px-4 py-3 rounded-lg border ${
                    errors.client ? 'border-red-500 ring-red-100' : 'border-gray-300'
                  } focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 bg-white/50 backdrop-blur-sm text-gray-900 text-lg`}
                  value={formData.client}
                  onChange={handleInputChange('client')}
                  required
                />
                {errors.client && (
                  <p className="text-red-500 text-sm mt-1">{errors.client}</p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-base font-semibold text-indigo-900 inline-block">
                  to <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  placeholder="i.e. be their development partner for the ACME Corporation Account"
                  className={`w-full px-4 py-3 rounded-lg border ${
                    errors.objective ? 'border-red-500 ring-red-100' : 'border-gray-300'
                  } focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200 bg-white/50 backdrop-blur-sm text-gray-900 text-lg`}
                  value={formData.objective}
                  onChange={handleInputChange('objective')}
                  required
                />
                {errors.objective && (
                  <p className="text-red-500 text-sm mt-1">{errors.objective}</p>
                )}
              </div>
            </div>

            <button
              type="submit"
              className="w-full py-4 px-6 bg-indigo-600 text-white text-lg font-semibold rounded-lg shadow-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 mt-8"
            >
              Create outline
            </button>
          </form>
        </div>
      </div>
    </>
  );
}