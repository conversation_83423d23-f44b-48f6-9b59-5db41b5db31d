import React, { useState, useEffect } from "react";
import {
  Plus,
  Calendar,
  Clock,
  MapPin,
  User,
  Building,
  RefreshCw,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  Filter,
  Search,
  CheckCircle,
  AlertCircle,
  Video,
  Phone,
  MessageSquare,
} from "lucide-react";
import { crmService } from "../services/crmService";
import { Event, EventFilters, PaginatedResponse } from "../types";
import Modal from "../components/Modal";
import CRMLayout from "../components/CRMLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../base/components/ui/card";
import { Button } from "../../../base/components/ui/button";
import { Badge } from "../../../base/components/ui/badge";
import { Input } from "../../../base/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../base/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../base/components/ui/select";
import { Avatar, AvatarFallback } from "../../../base/components/ui/avatar";

const EventManagement: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [filterType, setFilterType] = useState("all");

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Optimized for large datasets
  const pageSize = 50;

  useEffect(() => {
    loadEvents();
  }, [currentPage]);

  // Filter events based on search term and filter type
  const filteredEvents = events.filter((event) => {
    const matchesSearch =
      searchTerm === "" ||
      event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.note?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.crm_contacts?.full_name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      event.crm_companies?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());

    const matchesFilter =
      filterType === "" ||
      filterType === "all" ||
      (filterType === "upcoming" &&
        event.start_time &&
        isUpcoming(event.start_time)) ||
      (filterType === "past" && event.start_time && isPast(event.start_time)) ||
      (filterType === "today" &&
        event.start_time &&
        new Date(event.start_time).toDateString() ===
          new Date().toDateString());

    return matchesSearch && matchesFilter;
  });

  const loadEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: EventFilters = {
        page: currentPage,
        limit: pageSize,
      };

      const response: PaginatedResponse<Event> = await crmService.getEvents(
        filters
      );
      setEvents(response.data);
      setTotal(response.total);
    } catch (err) {
      console.error("Error loading events:", err);
      setError(err instanceof Error ? err.message : "Failed to load events");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEvent = async (eventData: any) => {
    try {
      await crmService.createEvent(eventData);
      setShowCreateModal(false);
      loadEvents();
    } catch (err) {
      console.error("Error creating event:", err);
      throw err;
    }
  };

  const handleEditEvent = async (eventData: any) => {
    if (!selectedEvent?.id) return;

    try {
      await crmService.updateEvent(selectedEvent.id, eventData);
      setShowEditModal(false);
      setSelectedEvent(null);
      loadEvents();
    } catch (err) {
      console.error("Error updating event:", err);
      throw err;
    }
  };

  const handleDeleteEvent = async () => {
    if (!selectedEvent?.id) return;

    try {
      await crmService.deleteEvent(selectedEvent.id);
      setShowDeleteModal(false);
      setSelectedEvent(null);
      loadEvents();
    } catch (err) {
      console.error("Error deleting event:", err);
      setError(err instanceof Error ? err.message : "Failed to delete event");
    }
  };

  const openEditModal = (event: Event) => {
    setSelectedEvent(event);
    setShowEditModal(true);
  };

  const openDeleteModal = (event: Event) => {
    setSelectedEvent(event);
    setShowDeleteModal(true);
  };

  const totalPages = Math.ceil(total / pageSize);

  // Group filtered events by date for better display
  const eventsByDate = filteredEvents.reduce((acc, event) => {
    const date = event.start_time
      ? new Date(event.start_time).toDateString()
      : "No Date";
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(event);
    return acc;
  }, {} as Record<string, Event[]>);

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const isUpcoming = (dateString: string) => {
    return new Date(dateString) > new Date();
  };

  const isPast = (dateString: string) => {
    return new Date(dateString) < new Date();
  };

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Event Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Schedule and manage meetings, calls, and events
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={loadEvents}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <MoreHorizontal className="h-4 w-4" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem className="gap-2">
                    <Upload className="h-4 w-4" />
                    Import Events
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Download className="h-4 w-4" />
                    Export Calendar
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="gap-2">
                    <Filter className="h-4 w-4" />
                    Advanced Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Schedule Event
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search events..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="sm:w-48">
                  <Select
                    value={filterType}
                    onValueChange={(value) => {
                      setFilterType(value);
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Events" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Events</SelectItem>
                      <SelectItem value="upcoming">Upcoming</SelectItem>
                      <SelectItem value="past">Past</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Events
                </CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{total}</div>
                <p className="text-xs text-muted-foreground">
                  {total === 1 ? "event" : "events"} scheduled
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Upcoming Events
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {
                    filteredEvents.filter(
                      (e) => e.start_time && isUpcoming(e.start_time)
                    ).length
                  }
                </div>
                <p className="text-xs text-muted-foreground">in the future</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Contacts
                </CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredEvents.filter((e) => e.contact_id).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  contact meetings
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  With Companies
                </CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredEvents.filter((e) => e.company_id).length}
                </div>
                <p className="text-xs text-muted-foreground">company events</p>
              </CardContent>
            </Card>
          </div>

          {/* Error Display */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                    <AlertCircle className="w-6 h-6 text-destructive" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-destructive">
                      Error Loading Events
                    </h3>
                    <p className="text-muted-foreground">{error}</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={loadEvents}
                    className="gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Retry
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Events List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Scheduled Events</span>
                <Badge variant="secondary">
                  {searchTerm || (filterType && filterType !== "all")
                    ? `${filteredEvents.length} filtered`
                    : `${total} total`}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6">
                  <div className="animate-pulse space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex space-x-4">
                        <div className="h-16 w-16 bg-muted rounded"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-muted rounded w-3/4"></div>
                          <div className="h-3 bg-muted rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : filteredEvents.length === 0 ? (
                <div className="p-12 text-center">
                  <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No events scheduled
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm
                      ? "No events match your search criteria."
                      : "Start by scheduling your first event or meeting."}
                  </p>
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Schedule First Event
                  </Button>
                </div>
              ) : (
                <div className="divide-y divide-border">
                  {Object.entries(eventsByDate).map(([date, dateEvents]) => (
                    <div key={date} className="p-6">
                      <h3 className="text-lg font-semibold mb-4">{date}</h3>
                      <div className="space-y-4">
                        {dateEvents.map((event) => (
                          <div
                            key={event.id}
                            className="flex items-start space-x-4 p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors group"
                          >
                            <Avatar className="h-12 w-12">
                              <AvatarFallback
                                className={`${
                                  event.start_time &&
                                  isUpcoming(event.start_time)
                                    ? "bg-primary/10 text-primary"
                                    : "bg-muted text-muted-foreground"
                                }`}
                              >
                                <Calendar className="w-6 h-6" />
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  {event.title}
                                </h4>
                                <div className="flex items-center gap-2">
                                  {event.start_time && (
                                    <Badge
                                      variant={
                                        isUpcoming(event.start_time)
                                          ? "default"
                                          : "secondary"
                                      }
                                      className="text-xs"
                                    >
                                      {isUpcoming(event.start_time)
                                        ? "Upcoming"
                                        : "Past"}
                                    </Badge>
                                  )}
                                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => openEditModal(event)}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="h-8 w-8 p-0"
                                        >
                                          <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem
                                          onClick={() => openEditModal(event)}
                                          className="gap-2"
                                        >
                                          <Eye className="h-4 w-4" />
                                          View Details
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                          onClick={() => openEditModal(event)}
                                          className="gap-2"
                                        >
                                          <Edit className="h-4 w-4" />
                                          Edit Event
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                          onClick={() => openDeleteModal(event)}
                                          className="gap-2 text-destructive"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                          Delete Event
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                </div>
                              </div>
                              <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                                {event.start_time && (
                                  <div className="flex items-center">
                                    <Clock className="w-4 h-4 mr-1" />
                                    {formatTime(event.start_time)}
                                    {event.end_time &&
                                      ` - ${formatTime(event.end_time)}`}
                                  </div>
                                )}
                                {event.location && (
                                  <div className="flex items-center">
                                    <MapPin className="w-4 h-4 mr-1" />
                                    {event.location}
                                  </div>
                                )}
                              </div>
                              {event.crm_contacts && (
                                <div className="mt-1 flex items-center text-sm text-gray-500">
                                  <User className="w-4 h-4 mr-1" />
                                  {event.crm_contacts.full_name}
                                </div>
                              )}
                              {event.crm_companies && (
                                <div className="mt-1 flex items-center text-sm text-gray-500">
                                  <Building className="w-4 h-4 mr-1" />
                                  {event.crm_companies.name}
                                </div>
                              )}
                              {event.note && (
                                <p className="mt-2 text-sm text-gray-700">
                                  {event.note}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="px-4 py-3 border-t bg-muted/20">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                  <div className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * pageSize + 1} to{" "}
                    {Math.min(currentPage * pageSize, total)} of{" "}
                    {total.toLocaleString()} events
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="px-2"
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Badge variant="secondary" className="px-3 py-1">
                      {currentPage} of {totalPages}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="px-2"
                    >
                      Last
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Create Event Modal */}
          <Modal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            title="Schedule New Event"
          >
            <div className="p-6">
              <div className="text-center">
                <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Event Form</h3>
                <p className="text-muted-foreground mb-4">
                  Event creation form will be implemented here.
                </p>
                <div className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={() => setShowCreateModal(false)}>
                    Create Event
                  </Button>
                </div>
              </div>
            </div>
          </Modal>

          {/* Edit Event Modal */}
          <Modal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            title="Edit Event"
          >
            <div className="p-6">
              <div className="text-center">
                <Edit className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Edit Event</h3>
                <p className="text-muted-foreground mb-4">
                  Event editing form will be implemented here.
                  {selectedEvent && (
                    <span className="block mt-2 font-medium">
                      Editing: {selectedEvent.title}
                    </span>
                  )}
                </p>
                <div className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowEditModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={() => setShowEditModal(false)}>
                    Save Changes
                  </Button>
                </div>
              </div>
            </div>
          </Modal>

          {/* Delete Confirmation Modal */}
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Event"
          >
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                  <Trash2 className="w-6 h-6 text-destructive" />
                </div>
                <div>
                  <h3 className="font-semibold">Delete Event</h3>
                  <p className="text-muted-foreground">
                    Are you sure you want to delete{" "}
                    <strong>{selectedEvent?.title}</strong>? This action cannot
                    be undone.
                  </p>
                </div>
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteEvent}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Event
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </CRMLayout>
  );
};

export default EventManagement;
