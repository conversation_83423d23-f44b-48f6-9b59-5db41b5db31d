import React, { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useUser } from "../base/contextapi/UserContext";
import { usePluginNavigation } from "../plugins/loader";
import { supabase } from "../base/utils/supabaseClient";

const Header = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  
  // 🆕 Get navigation from plugins
  const { navigation: pluginNavigation, isLoading: navLoading } = usePluginNavigation();

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      navigate("/login");
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  // Check if the current route matches the link
  const isActive = (path: string) => {
    if (path === "/settings") {
      return (
        location.pathname.startsWith("/settings") ||
        location.pathname.startsWith("/organizations") ||
        location.pathname.startsWith("/teams") ||
        location.pathname.startsWith("/subscription")
      );
    }
    if (path === "/pseo") {
      return location.pathname.startsWith("/pseo");
    }
    if (path === "/copilot-chat") {
      return location.pathname.startsWith("/copilot-chat");
    }
    if (path === "/knowledge") {
      return location.pathname.startsWith("/knowledge");
    }
    return location.pathname === path;
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
    if (userMenuOpen) setUserMenuOpen(false);
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
    if (mobileMenuOpen) setMobileMenuOpen(false);
  };

  // Combine static navigation with plugin navigation
  const staticNavigation = [
    { name: 'Dashboard', route: '/dashboard', order: 1 },
    { name: 'Settings', route: '/settings', order: 10 }
  ];

  // Merge and sort navigation
  const allNavigation = [...staticNavigation, ...pluginNavigation]
    .sort((a, b) => (a.order || 999) - (b.order || 999));

  return (
    <header className="app-header sticky top-0 z-40">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-8">
            <Link to="/" className="flex items-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-6 h-6"
              >
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
              </svg>
              <span className="text-xl font-bold">AltZero</span>
            </Link>

            <nav className="hidden md:block">
              <ul className="flex space-x-6">
                {user && (
                  <>
                    {allNavigation.map((item) => (
                      <li key={item.route}>
                        <Link
                          to={item.route}
                          className={
                            isActive(item.route)
                              ? "header-link-active"
                              : "header-link"
                          }
                        >
                          {item.name}
                        </Link>
                      </li>
                    ))}
                  </>
                )}
              </ul>
            </nav>
          </div>

          {user ? (
            <div className="hidden md:flex items-center space-x-4">
              <div className="relative">
                <button
                  onClick={toggleUserMenu}
                  className="flex items-center space-x-2 px-3 py-1 rounded-full bg-primary/20 hover:bg-primary/30 transition-standard"
                >
                  <div className="w-8 h-8 rounded-full bg-primary/30 flex items-center justify-center text-primary-foreground">
                    {user.email?.charAt(0).toUpperCase() || "U"}
                  </div>
                  <span className="text-sm font-medium">
                    {user.email?.split("@")[0]}
                  </span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className={`transition-transform duration-200 ${
                      userMenuOpen ? "rotate-180" : ""
                    }`}
                  >
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg z-50">
                    <div className="py-1">
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-sm hover:bg-muted transition-standard"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        Profile
                      </Link>
                      <button
                        onClick={() => {
                          handleLogout();
                          setUserMenuOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-destructive hover:bg-muted transition-standard"
                      >
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="hidden md:block">
              <Link
                to="/login"
                className="px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-standard"
              >
                Login
              </Link>
            </div>
          )}

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              className="text-primary-foreground focus:outline-none"
              onClick={toggleMobileMenu}
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d={
                    mobileMenuOpen
                      ? "M6 18L18 6M6 6l12 12" // X shape for close
                      : "M4 6h16M4 12h16M4 18h16" // Hamburger menu
                  }
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-primary-foreground border-t border-border">
          <div className="container mx-auto px-4 py-3">
            <nav>
              <ul className="space-y-3">
                {user ? (
                  <>
                    {allNavigation.map((item) => (
                      <li key={`mobile-${item.route}`}>
                        <Link
                          to={item.route}
                          className={`block py-2 px-3 rounded-md ${isActive(item.route) ? "bg-primary text-primary-foreground" : "text-foreground hover:bg-muted"}`}
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      </li>
                    ))}
                    <li>
                      <Link
                        to="/profile"
                        className={`block py-2 px-3 rounded-md ${isActive("/profile") ? "bg-primary text-primary-foreground" : "text-foreground hover:bg-muted"}`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Profile
                      </Link>
                    </li>
                    <li className="pt-2 border-t border-border">
                      <button
                        onClick={() => {
                          handleLogout();
                          setMobileMenuOpen(false);
                        }}
                        className="block w-full text-left py-2 px-3 rounded-md text-destructive hover:bg-muted"
                      >
                        Logout ({user.email})
                      </button>
                    </li>
                  </>
                ) : (
                  <li>
                    <Link
                      to="/login"
                      className="block py-2 px-3 rounded-md bg-primary text-primary-foreground hover:bg-primary/90"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Login
                    </Link>
                  </li>
                )}
              </ul>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
