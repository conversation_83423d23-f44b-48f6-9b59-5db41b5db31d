import express from "express";
import multer from "multer";
import path from "path";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import { validateA<PERSON><PERSON>ey } from "../../../base/common/routes/auth";
import { pineconeService } from "../services/pineconeService";
import { langGraphService } from "../services/langGraphService";
import { llamaCloudService } from "../services/llamaCloudService";

// Document metadata interface
interface DocumentMetadata {
  id: string;
  name: string;
  type: string;
  size: number;
  status: "processing" | "success" | "error";
  uploadedAt: string;
  userId: string;
  error?: string;
}

// Helper function to extract user ID from request
function getUserIdFromRequest(req: express.Request): string {
  const userId =
    (req.headers["x-user-id"] as string) ||
    req.body.userId ||
    (req.query.userId as string) ||
    "anonymous";
  return userId;
}

const router = express.Router();

// Add debugging middleware for all requests to this router
router.use((req, res, next) => {
  console.log(`🚨 KNOWLEDGE ROUTER: ${req.method} ${req.path}`);
  console.log(`🚨 KNOWLEDGE ROUTER: Full URL: ${req.originalUrl}`);
  console.log(`🚨 KNOWLEDGE ROUTER: Headers:`, {
    "x-user-id": req.headers["x-user-id"],
    "x-api-key": req.headers["x-api-key"],
  });
  console.log(`🚨 KNOWLEDGE ROUTER: Query:`, req.query);
  next();
});

// Configure multer for file uploads
const upload = multer({
  dest: "uploads/",
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/plain",
      "text/markdown",
      "application/rtf",
      "application/vnd.oasis.opendocument.text",
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Invalid file type"));
    }
  },
});

// Get user-specific documents from Pinecone
router.get("/documents", async (req, res) => {
  try {
    const userId = getUserIdFromRequest(req);

    console.log(`🔍 GET /documents - User ID: ${userId}`);
    console.log(`📋 Request headers:`, {
      "x-user-id": req.headers["x-user-id"],
      "x-api-key": req.headers["x-api-key"],
    });
    console.log(`📋 Query params:`, req.query);

    // Fetch documents from Pinecone using the service
    const documents = await pineconeService.getUserDocuments(userId);

    console.log(
      `✅ Returning ${documents.length} documents for user ${userId}`
    );
    res.json(documents);
  } catch (error) {
    console.error("❌ Error fetching documents from Pinecone:", error);
    res.status(500).json({ error: "Failed to fetch documents" });
  }
});

// Upload documents: LlamaParser → Pinecone workflow
router.post("/documents/upload", upload.array("files"), async (req, res) => {
  try {
    const files = req.files as unknown as Express.Multer.File[];
    const userId = getUserIdFromRequest(req);

    if (!files || files.length === 0) {
      return res.status(400).json({ error: "No files uploaded" });
    }

    const uploadedDocuments = [];
    const errors = [];

    for (const file of files) {
      const documentId = uuidv4();

      try {
        console.log(
          `Processing document: ${file.originalname} for user: ${userId}`
        );

        // Step 1: Parse document using LlamaCloud service
        console.log(
          `📄 Parsing document with LlamaCloud: ${file.originalname}`
        );
        const parsedDocument = await llamaCloudService.parseDocument(
          file.path,
          file.originalname
        );

        console.log(`🚨 DEBUG: parseDocument completed successfully!`);
        console.log(`🚨 DEBUG: parsedDocument received:`, {
          id: parsedDocument.id,
          contentLength: parsedDocument.content.length,
          metadataKeys: Object.keys(parsedDocument.metadata),
          chunksLength: parsedDocument.chunks.length,
          hasContent: !!parsedDocument.content,
          contentPreview: parsedDocument.content.substring(0, 100),
        });

        console.log(
          `📝 Extracted content length: ${parsedDocument.content.length} characters`
        );
        console.log(`📊 Document chunks: ${parsedDocument.chunks.length}`);

        console.log(`🚨 DEBUG: About to call Pinecone processing...`);
        console.log(`🚨 DEBUG: parsedDocument structure:`, {
          id: parsedDocument.id,
          contentLength: parsedDocument.content.length,
          metadataKeys: Object.keys(parsedDocument.metadata),
          chunksLength: parsedDocument.chunks.length,
        });
        console.log(`🚨 DEBUG: documentId: ${documentId}`);
        console.log(`🚨 DEBUG: userId: ${userId}`);
        console.log(`🚨 DEBUG: file info:`, {
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
        });

        console.log(`🚨 STEP 1: Creating timeout promise...`);
        // Step 2: Store in Pinecone with user-specific metadata
        console.log(`🔄 Starting Pinecone processing with timeout...`);

        // Add timeout to prevent hanging
        const pineconeTimeout = new Promise((_, reject) => {
          setTimeout(
            () =>
              reject(new Error("Pinecone processing timeout after 60 seconds")),
            60000
          );
        });

        console.log(`🚨 STEP 2: Timeout promise created`);
        console.log(`🚨 DEBUG: About to create pineconeProcessing promise...`);

        console.log(`🚨 STEP 3: Checking Pinecone health...`);
        // Check Pinecone health before processing
        console.log(`🔍 Checking Pinecone health before processing...`);
        const pineconeHealthy = await pineconeService.healthCheck();
        console.log(`🔍 Pinecone health check result: ${pineconeHealthy}`);
        console.log(`🔍 Pinecone enabled: ${pineconeService.enabled}`);

        console.log(`🚨 STEP 4: Health check completed`);

        if (!pineconeHealthy) {
          console.error(`❌ Pinecone is not healthy! Skipping storage.`);
          throw new Error("Pinecone service is not available");
        }

        console.log(
          `🚨 DEBUG: About to call pineconeService.processLlamaCloudDocument...`
        );
        console.log(`🚨 DEBUG: pineconeService object:`, {
          hasProcessMethod:
            typeof pineconeService.processLlamaCloudDocument === "function",
          serviceEnabled: pineconeService.enabled,
          serviceType: typeof pineconeService,
        });

        console.log(`🚨 STEP 5: About to test method call...`);
        // Test if the method can be called at all
        console.log(`🧪 TEST: Trying to call method with minimal data...`);
        try {
          const testCall = pineconeService.processLlamaCloudDocument(
            {
              id: "test-id",
              content: "test content",
              metadata: { fileName: "test.txt" },
            },
            "test-user"
          );
          console.log(`🧪 TEST: Method call created promise successfully`);
          // Don't await it, just see if it can be called
        } catch (testError) {
          console.error(`🧪 TEST: Method call failed:`, testError);
        }

        console.log(`🚨 STEP 6: Test completed, now calling with real data...`);
        console.log(`🚨 DEBUG: Now calling with real data...`);

        console.log(`🚨 STEP 7: Creating real method call...`);
        const pineconeProcessing = pineconeService.processLlamaCloudDocument(
          {
            id: documentId,
            content: parsedDocument.content,
            metadata: {
              fileName: file.originalname,
              fileType: file.mimetype,
              fileSize: file.size,
              uploadedAt: new Date().toISOString(),
              userId: userId,
              llamaCloudId: documentId,
              pageCount: parsedDocument.metadata.pageCount,
              parsedAt: parsedDocument.metadata.parsedAt,
              originalLlamaCloudId: parsedDocument.id,
            },
          },
          userId
        );

        console.log(`🚨 STEP 8: Method call created, promise exists`);
        console.log(
          `🚨 DEBUG: pineconeProcessing promise created successfully`
        );
        console.log(
          `🚨 DEBUG: Created pineconeProcessing promise, about to race with timeout...`
        );

        console.log(`🚨 STEP 9: About to start Promise.race...`);
        const pineconeResult = await Promise.race([
          pineconeProcessing,
          pineconeTimeout,
        ]);
        console.log(`🚨 STEP 10: Promise.race completed!`);
        console.log(`✅ Pinecone processing completed:`, pineconeResult);

        uploadedDocuments.push({
          id: documentId,
          name: file.originalname,
          type: file.mimetype,
          size: file.size,
          status: "success",
          uploadedAt: new Date().toISOString(),
          error: null,
        });

        console.log(
          `Successfully processed document: ${file.originalname} for user: ${userId}`
        );

        // Clean up uploaded file
        fs.unlinkSync(file.path);
      } catch (error) {
        console.error(`Error processing document ${file.originalname}:`, error);

        errors.push({
          fileName: file.originalname,
          error: error instanceof Error ? error.message : "Unknown error",
        });

        // Clean up uploaded file
        try {
          fs.unlinkSync(file.path);
        } catch (cleanupError) {
          console.error("Error cleaning up file:", cleanupError);
        }
      }
    }

    res.json({ documents: uploadedDocuments, errors });
  } catch (error) {
    console.error("Error uploading documents:", error);
    res.status(500).json({ error: "Failed to upload documents" });
  }
});

// Search user-specific documents using Pinecone + LangGraph
router.post("/search", async (req, res) => {
  try {
    const {
      query,
      documentIds = [],
      maxResults = 10,
      minRelevanceScore = 0.5,
    } = req.body;
    const userId = getUserIdFromRequest(req);

    if (!query) {
      return res.status(400).json({ error: "Query is required" });
    }

    console.log(
      `Searching documents for user ${userId} with query: "${query}"`
    );

    // Search in Pinecone with user filter
    const searchResults = await langGraphService.searchDocuments(query, {
      topK: maxResults,
      minScore: minRelevanceScore,
      filter: {
        userId: userId, // Filter by user ID
        ...(documentIds.length > 0 && { documentId: { $in: documentIds } }),
      },
    });

    // Transform results to match expected format
    const transformedResults = searchResults.map((result) => ({
      id: result.id,
      text: result.text || "",
      score: result.score || 0,
      metadata: result.metadata,
    }));

    const response = {
      results: transformedResults,
      sources: transformedResults.map((result) => ({
        documentId: result.id,
        documentName: result.metadata?.fileName || "Unknown Document",
        relevanceScore: result.score,
        excerpt: (result.text || "").substring(0, 200) + "...",
      })),
      totalResults: transformedResults.length,
      provider: "LangGraph + Pinecone",
    };

    console.log(
      `Found ${transformedResults.length} search results for user ${userId}`
    );
    res.json(response);
  } catch (error) {
    console.error("Error searching documents:", error);
    res.status(500).json({ error: "Failed to search documents" });
  }
});

// Chat endpoint using LangGraph + Pinecone RAG (user-specific)
router.post("/chat", async (req, res) => {
  try {
    const { message, selectedDocuments = [], systemMessage } = req.body;
    const userId = getUserIdFromRequest(req);

    console.log(`🔍 CHAT ENDPOINT - Request received:`);
    console.log(`   Message: "${message}"`);
    console.log(`   UserId: ${userId}`);
    console.log(`   SelectedDocuments:`, selectedDocuments);
    console.log(`   SelectedDocuments type:`, typeof selectedDocuments);
    console.log(`   SelectedDocuments length:`, selectedDocuments?.length);
    console.log(
      `   SelectedDocuments array:`,
      Array.isArray(selectedDocuments)
    );
    if (selectedDocuments && selectedDocuments.length > 0) {
      console.log(
        `   SelectedDocuments values:`,
        selectedDocuments.map((id: any) => `"${id}"`)
      );
    }
    console.log(`   SystemMessage:`, systemMessage);

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Use LangGraph + Pinecone RAG with user filter
    const ragResponse = await langGraphService.performRAG({
      query: message,
      selectedDocuments,
      systemMessage,
      temperature: 0.7,
      maxTokens: 2048,
      userFilter: { userId: userId }, // Add user filter
    });

    // Map RAG response sources to expected DocumentSource format
    const mappedSources = ragResponse.sources.map((source) => ({
      documentId:
        source.documentId || source.metadata?.llamaCloudId || "unknown",
      documentName:
        source.documentName ||
        source.metadata?.fileName ||
        source.metadata?.filename ||
        "Unknown Document",
      pageNumber: source.metadata?.pageNumber,
      relevanceScore: source.score,
      excerpt: source.content,
    }));

    const response = {
      message: ragResponse.answer,
      sources: mappedSources,
      metadata: {
        ...ragResponse.metadata,
        uniqueDocuments: ragResponse.metadata.uniqueDocuments,
        documentsUsed: ragResponse.metadata.documentsUsed,
      },
    };

    console.log(
      `Generated response for user ${userId} using LangGraph + Pinecone`
    );
    res.json(response);
  } catch (error) {
    console.error("Error generating chat response:", error);
    res.status(500).json({ error: "Failed to generate response" });
  }
});

// Stream chat message using LangGraph + Pinecone RAG (user-specific)
router.post("/chat/stream", async (req, res) => {
  try {
    const { message, selectedDocuments = [], systemMessage } = req.body;
    const userId = getUserIdFromRequest(req);

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Set up Server-Sent Events
    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control",
    });

    try {
      // Use LangGraph for RAG processing with user filter
      const ragResponse = await langGraphService.performRAG({
        query: message,
        selectedDocuments,
        systemMessage,
        temperature: 0.7,
        maxTokens: 2048,
        userFilter: { userId: userId },
      });

      // Stream the response
      const chunks = ragResponse.answer.split(" ");
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i] + " ";
        res.write(`data: ${JSON.stringify({ chunk })}\n\n`);
        await new Promise((resolve) => setTimeout(resolve, 50));
      }

      // Map RAG response sources to expected DocumentSource format
      const mappedSources = ragResponse.sources.map((source) => ({
        documentId:
          source.documentId || source.metadata?.llamaCloudId || "unknown",
        documentName:
          source.documentName ||
          source.metadata?.fileName ||
          source.metadata?.filename ||
          "Unknown Document",
        pageNumber: source.metadata?.pageNumber,
        relevanceScore: source.score,
        excerpt: source.content,
      }));

      // Send completion event
      res.write(
        `data: ${JSON.stringify({
          complete: true,
          message: ragResponse.answer,
          sources: mappedSources,
          metadata: {
            ...ragResponse.metadata,
            uniqueDocuments: ragResponse.metadata.uniqueDocuments,
            documentsUsed: ragResponse.metadata.documentsUsed,
          },
        })}\n\n`
      );
    } catch (ragError) {
      console.error("LangGraph RAG error:", ragError);
      res.write(
        `data: ${JSON.stringify({
          error: "Failed to process message with RAG system",
        })}\n\n`
      );
    }

    res.end();
  } catch (error) {
    console.error("Error streaming chat message:", error);
    res.write(
      `data: ${JSON.stringify({ error: "Failed to process message" })}\n\n`
    );
    res.end();
  }
});

// Get document summary using LangGraph analysis (user-specific)
router.get("/summary/:documentId", async (req, res) => {
  try {
    const { documentId } = req.params;
    const userId = getUserIdFromRequest(req);

    // Verify user owns this document by checking Pinecone
    const userDocs = await pineconeService.getUserDocuments(userId);
    const document = userDocs.find((d) => d.id === documentId);

    if (!document) {
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    // Use LangGraph analysis with user filter
    const analysis = await langGraphService.analyzeDocuments(
      [documentId],
      "summary",
      { userId: userId }
    );

    res.json({
      content: analysis.analysis,
      metadata: {
        documentId,
        userId,
        analysisType: analysis.analysisType,
        documentCount: analysis.documentCount,
        provider: "LangGraph + Pinecone",
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error getting document summary:", error);
    res.status(500).json({ error: "Failed to get document summary" });
  }
});

// Delete user document
router.delete("/documents/:documentId", async (req, res) => {
  console.log(`🚨 DELETE ROUTE HIT: /documents/:documentId`);
  console.log(`🚨 DELETE PARAMS:`, req.params);
  console.log(`🚨 DELETE FULL URL:`, req.originalUrl);
  try {
    const { documentId } = req.params;
    const userId = getUserIdFromRequest(req);

    console.log(
      `🗑️ Delete request for document ${documentId} by user ${userId}`
    );

    // Verify user owns this document by checking Pinecone
    const userDocs = await pineconeService.getUserDocuments(userId);
    const document = userDocs.find((d) => d.id === documentId);

    if (!document) {
      console.log(
        `❌ Document ${documentId} not found or access denied for user ${userId}`
      );
      return res
        .status(404)
        .json({ error: "Document not found or access denied" });
    }

    console.log(`✅ Document ${documentId} found, proceeding with deletion`);
    console.log(`📄 Document details:`, {
      name: document.name,
      size: document.size,
      uploadedAt: document.uploadedAt,
    });

    // Remove from Pinecone (this will delete all chunks)
    await pineconeService.deleteDocuments([documentId]);

    console.log(
      `✅ Successfully deleted document ${documentId} for user ${userId}`
    );
    res.json({
      message: "Document deleted successfully",
      documentId: documentId,
      documentName: document.name,
    });
  } catch (error) {
    console.error(`❌ Error deleting document:`, error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";
    res.status(500).json({
      error: "Failed to delete document",
      details: errorMessage,
    });
  }
});

// Bulk delete user documents
router.delete("/documents", async (req, res) => {
  try {
    const { documentIds } = req.body;
    const userId = getUserIdFromRequest(req);

    if (
      !documentIds ||
      !Array.isArray(documentIds) ||
      documentIds.length === 0
    ) {
      return res.status(400).json({ error: "documentIds array is required" });
    }

    console.log(
      `🗑️ Bulk delete request for ${documentIds.length} documents by user ${userId}`
    );
    console.log(`📋 Document IDs:`, documentIds);

    // Verify user owns all these documents
    const userDocs = await pineconeService.getUserDocuments(userId);
    const userDocIds = userDocs.map((d) => d.id);

    const invalidDocIds = documentIds.filter((id) => !userDocIds.includes(id));
    if (invalidDocIds.length > 0) {
      console.log(`❌ Invalid document IDs found:`, invalidDocIds);
      return res.status(404).json({
        error: "Some documents not found or access denied",
        invalidDocuments: invalidDocIds,
      });
    }

    console.log(
      `✅ All ${documentIds.length} documents verified, proceeding with bulk deletion`
    );

    // Remove from Pinecone (this will delete all chunks for all documents)
    await pineconeService.deleteDocuments(documentIds);

    const deletedDocuments = userDocs.filter((d) => documentIds.includes(d.id));

    console.log(
      `✅ Successfully deleted ${documentIds.length} documents for user ${userId}`
    );
    res.json({
      message: `Successfully deleted ${documentIds.length} documents`,
      deletedCount: documentIds.length,
      deletedDocuments: deletedDocuments.map((d) => ({
        id: d.id,
        name: d.name,
      })),
    });
  } catch (error) {
    console.error(`❌ Error bulk deleting documents:`, error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";
    res.status(500).json({
      error: "Failed to delete documents",
      details: errorMessage,
    });
  }
});

// Health check endpoint
router.get("/health", async (req, res) => {
  try {
    const pineconeHealthy = await pineconeService.healthCheck();
    const langGraphHealthy = await langGraphService.healthCheck();

    const status = {
      status: "healthy",
      services: {
        pinecone: pineconeHealthy ? "healthy" : "unhealthy",
        langGraph: langGraphHealthy ? "healthy" : "unhealthy",
      },
      timestamp: new Date().toISOString(),
    };

    const overallHealthy = pineconeHealthy && langGraphHealthy;
    res.status(overallHealthy ? 200 : 503).json(status);
  } catch (error) {
    console.error("Health check error:", error);
    res.status(503).json({
      status: "unhealthy",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Debug endpoint to check Pinecone index stats
router.get("/debug/pinecone", async (req, res) => {
  try {
    const stats = await pineconeService.getIndexStats();
    const isEnabled = pineconeService.enabled;

    console.log(`🔧 Pinecone Debug Info:`, {
      enabled: isEnabled,
      stats: stats,
    });

    res.json({
      enabled: isEnabled,
      stats: stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Pinecone debug error:", error);
    res.status(500).json({
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Debug endpoint to test document fetching for any user
router.get("/debug/documents/:userId?", async (req, res) => {
  try {
    const userId = req.params.userId || getUserIdFromRequest(req);

    console.log(`🔧 Debug: Fetching documents for user: ${userId}`);

    const documents = await pineconeService.getUserDocuments(userId);

    res.json({
      userId: userId,
      documentsCount: documents.length,
      documents: documents,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Debug documents error:", error);
    res.status(500).json({
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Simple endpoint to see ALL data in Pinecone (for debugging)
router.get("/debug/all-vectors", async (req, res) => {
  try {
    console.log(`🔧 Debug: Fetching ALL vectors from Pinecone...`);

    // This will use the enhanced debugging in getUserDocuments with a dummy user
    // The method will try without filter and show all userIds
    const result = await pineconeService.getUserDocuments("debug-show-all");

    res.json({
      message: "Check server logs for detailed Pinecone data",
      result: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Debug all vectors error:", error);
    res.status(500).json({
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Debug endpoint to inspect chunks for a specific document
router.get("/debug/document-chunks/:documentId", async (req, res) => {
  try {
    const { documentId } = req.params;
    const userId = getUserIdFromRequest(req);

    console.log(`🔧 Debug: Inspecting chunks for document: ${documentId}`);

    // Search by multiple possible metadata fields
    const searchFields = [
      { llamaCloudId: documentId },
      { originalLlamaCloudId: documentId },
      { id: documentId },
      { documentId: documentId },
    ];

    const results: any = {};
    let totalChunks = 0;

    for (const filter of searchFields) {
      try {
        console.log(`🔍 Searching with filter:`, filter);
        const searchResults = await pineconeService.searchSimilar("", {
          topK: 1000,
          minScore: 0.0,
          filter: filter,
        });

        results[JSON.stringify(filter)] = {
          count: searchResults.length,
          chunks: searchResults.map((result) => ({
            id: result.id,
            score: result.score,
            metadata: result.metadata,
            textPreview: result.text.substring(0, 100) + "...",
          })),
        };

        totalChunks += searchResults.length;
        console.log(
          `🔍 Found ${searchResults.length} chunks with filter:`,
          filter
        );
      } catch (searchError) {
        console.error(
          `❌ Error searching with filter ${JSON.stringify(filter)}:`,
          searchError
        );
        results[JSON.stringify(filter)] = {
          error:
            searchError instanceof Error
              ? searchError.message
              : "Unknown error",
        };
      }
    }

    res.json({
      documentId: documentId,
      userId: userId,
      totalChunksFound: totalChunks,
      searchResults: results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Debug document chunks error:", error);
    res.status(500).json({
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Direct test endpoint to delete a specific vector ID
router.delete("/debug/delete-vector/:vectorId", async (req, res) => {
  try {
    const { vectorId } = req.params;
    const userId = getUserIdFromRequest(req);

    console.log(`🔧 Debug: Direct deletion of vector: ${vectorId}`);

    if (!pineconeService.enabled) {
      return res.status(500).json({ error: "Pinecone not available" });
    }

    // Get the raw Pinecone client
    const client = (pineconeService as any).client;
    const indexName = (pineconeService as any).indexName;
    const namespace = "default";

    console.log(
      `🗑️ Using direct Pinecone client to delete vector: ${vectorId}`
    );
    console.log(`🗑️ Index: ${indexName}, Namespace: ${namespace}`);

    const index = client.index(indexName);
    const ns = index.namespace(namespace);

    // Try to delete the specific vector using namespace deleteOne
    console.log(`🗑️ Calling ns.deleteOne() with vector: ${vectorId}`);
    const deleteResponse = await ns.deleteOne(vectorId);

    console.log(`🗑️ Direct delete response:`, deleteResponse);

    // Wait for consistency
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Verify deletion by searching for it
    console.log(`🔍 Verifying deletion by searching for the vector...`);
    const searchResults = await pineconeService.searchSimilar("", {
      topK: 1000,
      minScore: 0.0,
      filter: { id: vectorId },
    });

    const stillExists = searchResults.some((result) => result.id === vectorId);

    res.json({
      vectorId: vectorId,
      deleteResponse: deleteResponse,
      stillExists: stillExists,
      searchResults: searchResults.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("❌ Debug direct delete error:", error);
    res.status(500).json({
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
