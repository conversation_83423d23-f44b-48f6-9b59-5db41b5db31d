// API configuration
export const API_URL = import.meta.env.VITE_API_URL || "http://localhost:3000"; // Base URL without /api

// Make sure API_KEY is exported
export const API_KEY = import.meta.env.VITE_API_KEY || ""; // This is missing or renamed

// File upload configuration
export const ALLOWED_FILE_TYPES = [".pdf", ".docx", ".txt", ".md"];
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Document status types
export const DOCUMENT_STATUS = {
  SUCCESS: "success",
  PROCESSING: "processing",
  ERROR: "error",
};

// Chat configuration
export const DEFAULT_SYSTEM_MESSAGE =
  "I am an AI assistant that helps you with your documents.";
export const MIN_RELEVANCE_SCORE = 0.7; // Minimum score to consider a search result relevant

// API endpoints - endpoints already include /api prefix which matches app.ts router config
export const ENDPOINTS = {
  CHAT: "/api/chat",
  DOCUMENTS: "/api/documents",
  PROPOSALS: "/api/proposals",
  PROPOSALS_STREAM: "/api/proposals/stream", // Add this explicitly
  HEALTH: "/api/health", // Fix this from /chat/health
  REPROCESS: "/api/documents/{id}/reprocess", // Fix this from /chat/documents
};
