import React, { useState, useEffect } from "react";
import { SectionDefinition, UseScopingReturn } from "../../../hooks/useScoping";

interface SectionDefinitionFormProps {
  onNext: () => void;
  onBack: () => void;
  state: UseScopingReturn["state"];
  addSection: (section: SectionDefinition) => void;
  removeSection: (sectionId: string) => void;
  updateSection: (
    sectionId: string,
    updates: Partial<SectionDefinition>
  ) => void;
}

// Use hardcoded default sections instead of fetching from database
const DEFAULT_SECTIONS: SectionDefinition[] = [
  {
    id: "section-1",
    title: "Executive Summary",
    description:
      "A brief overview of the project, its objectives, and expected outcomes.",
  },
  {
    id: "section-2",
    title: "Project Background",
    description:
      "Overview of the client, industry context, and reasons for initiating the project.",
  },
  {
    id: "section-3",
    title: "Scope of Work",
    description:
      "Detailed description of what is included and excluded from the project scope.",
  },
  {
    id: "section-4",
    title: "Requirements & Deliverables",
    description:
      "Specific requirements, features, and deliverables that will be produced.",
  },
  {
    id: "section-5",
    title: "Timeline & Milestones",
    description: "Project schedule, key dates, phases, and milestones.",
  },
  {
    id: "section-6",
    title: "Budget & Resources",
    description:
      "Cost breakdown, resource allocation, and financial considerations.",
  },
  {
    id: "section-7",
    title: "Technical Requirements",
    description:
      "Technical specifications, platforms, and architectural considerations.",
  },
  {
    id: "section-8",
    title: "Assumptions & Constraints",
    description:
      "Key assumptions made and constraints that may affect the project.",
  },
  {
    id: "section-9",
    title: "Risks & Mitigations",
    description: "Potential risks, challenges, and mitigation strategies.",
  },
  {
    id: "section-10",
    title: "Success Criteria",
    description: "Measurable criteria that define project success.",
  },
];

const SectionDefinitionForm: React.FC<SectionDefinitionFormProps> = ({
  onNext,
  onBack,
  state,
  addSection,
  removeSection,
  updateSection,
}) => {
  const [defaultSections, setDefaultSections] = useState<SectionDefinition[]>(
    []
  );
  const [selectedDefaultSections, setSelectedDefaultSections] = useState<
    string[]
  >([]);
  const [customSections, setCustomSections] = useState<SectionDefinition[]>([]);
  const [loading, setLoading] = useState(true);

  // Load default sections from hardcoded constant
  useEffect(() => {
    setLoading(true);
    try {
      setDefaultSections(DEFAULT_SECTIONS);
    } catch (error) {
      console.error("Error loading section definitions:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize from existing state if available
  useEffect(() => {
    if (state.sections.length > 0 && defaultSections.length > 0) {
      // Identify default vs custom sections
      const defaultIds = defaultSections.map((section) => section.id);

      const defaultSelected = state.sections
        .filter((section: SectionDefinition) => defaultIds.includes(section.id))
        .map((section: SectionDefinition) => section.id);

      const custom = state.sections.filter(
        (section: SectionDefinition) => !defaultIds.includes(section.id)
      );

      setSelectedDefaultSections(defaultSelected);
      setCustomSections(custom);
    }
  }, [state.sections, defaultSections]);

  const handleDefaultSectionToggle = (sectionId: string) => {
    const isSelected = selectedDefaultSections.includes(sectionId);

    if (isSelected) {
      // Remove section
      setSelectedDefaultSections((prev) =>
        prev.filter((id) => id !== sectionId)
      );
    } else {
      // Add section
      setSelectedDefaultSections((prev) => [...prev, sectionId]);
    }
  };

  const handleAddCustomSection = () => {
    const newSection: SectionDefinition = {
      id: `custom-${Date.now()}`,
      title: "",
      description: "",
    };

    setCustomSections((prev) => [...prev, newSection]);
  };

  const handleRemoveCustomSection = (sectionId: string) => {
    setCustomSections((prev) =>
      prev.filter((section) => section.id !== sectionId)
    );
  };

  const handleCustomSectionChange = (
    sectionId: string,
    field: keyof SectionDefinition,
    value: string
  ) => {
    setCustomSections((prev) =>
      prev.map((section) =>
        section.id === sectionId ? { ...section, [field]: value } : section
      )
    );
  };

  const handleMoveSection = (index: number, direction: "up" | "down") => {
    if (
      (direction === "up" && index === 0) ||
      (direction === "down" && index === customSections.length - 1)
    ) {
      return;
    }

    const newIndex = direction === "up" ? index - 1 : index + 1;
    const newSections = [...customSections];
    [newSections[index], newSections[newIndex]] = [
      newSections[newIndex],
      newSections[index],
    ];

    setCustomSections(newSections);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear existing sections
    state.sections.forEach((section: SectionDefinition) => {
      removeSection(section.id);
    });

    // Add default sections
    defaultSections
      .filter((section) => selectedDefaultSections.includes(section.id))
      .forEach((section) => {
        addSection(section);
      });

    // Add custom sections (filter out empty ones)
    customSections
      .filter(
        (section) =>
          section.title.trim() !== "" && section.description.trim() !== ""
      )
      .forEach((section) => {
        addSection(section);
      });

    onNext();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">Document Sections</h2>
      <p className="text-gray-600 mb-6">
        Define the sections to include in your scoping document
      </p>

      <form onSubmit={handleSubmit}>
        <div className="space-y-8">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Predefined Sections
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Select common document sections to include in your scope
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {defaultSections.map((section) => (
                <div
                  key={section.id}
                  className={`border rounded-lg p-4 cursor-pointer hover:bg-gray-50 ${
                    selectedDefaultSections.includes(section.id)
                      ? "border-indigo-500 bg-indigo-50"
                      : "border-gray-300"
                  }`}
                  onClick={() => handleDefaultSectionToggle(section.id)}
                >
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      className="h-4 w-4 mt-1 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={selectedDefaultSections.includes(section.id)}
                      onChange={() => {}}
                    />
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-gray-900">
                        {section.title}
                      </h4>
                      <p className="mt-1 text-xs text-gray-500">
                        {section.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-medium text-gray-900">
                Custom Sections
              </h3>
              <button
                type="button"
                onClick={handleAddCustomSection}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <svg
                  className="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Section
              </button>
            </div>

            {customSections.length === 0 ? (
              <p className="text-sm text-gray-500 italic py-3">
                No custom sections added. Click "Add Section" to create your
                own.
              </p>
            ) : (
              <div className="space-y-4">
                {customSections.map((section, index) => (
                  <div
                    key={section.id}
                    className="border border-gray-200 rounded-lg p-4 bg-white"
                  >
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-sm font-medium text-gray-900">
                        Custom Section {index + 1}
                      </h4>
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          onClick={() => handleMoveSection(index, "up")}
                          disabled={index === 0}
                          className={`p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                            index === 0 ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                        >
                          <svg
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 15l7-7 7 7"
                            />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => handleMoveSection(index, "down")}
                          disabled={index === customSections.length - 1}
                          className={`p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                            index === customSections.length - 1
                              ? "opacity-50 cursor-not-allowed"
                              : ""
                          }`}
                        >
                          <svg
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRemoveCustomSection(section.id)}
                          className="p-1 rounded-full text-red-400 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <svg
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label
                          htmlFor={`section-title-${section.id}`}
                          className="block text-sm font-medium text-gray-700"
                        >
                          Section Title
                        </label>
                        <input
                          type="text"
                          id={`section-title-${section.id}`}
                          value={section.title}
                          onChange={(e) =>
                            handleCustomSectionChange(
                              section.id,
                              "title",
                              e.target.value
                            )
                          }
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          placeholder="e.g. Implementation Strategy"
                          required
                        />
                      </div>

                      <div>
                        <label
                          htmlFor={`section-description-${section.id}`}
                          className="block text-sm font-medium text-gray-700"
                        >
                          Section Description
                        </label>
                        <textarea
                          id={`section-description-${section.id}`}
                          value={section.description}
                          onChange={(e) =>
                            handleCustomSectionChange(
                              section.id,
                              "description",
                              e.target.value
                            )
                          }
                          rows={2}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                          placeholder="Brief description of what this section should cover"
                          required
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-between pt-4">
            <button
              type="button"
              onClick={onBack}
              className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Back
            </button>
            <button
              type="submit"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Next
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SectionDefinitionForm;
