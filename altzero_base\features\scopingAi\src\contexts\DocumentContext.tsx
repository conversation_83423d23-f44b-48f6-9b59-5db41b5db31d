import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
  ReactNode,
} from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useToast } from "../../../../base/hooks/use-toast";
import {
  GeneratedDocumentData,
  DocumentTheme,
  Section,
  AIPromptData,
  Block,
} from "../types/documentTypes";
import { themePresets } from "../utils/themeUtils";
import {
  convertContentToBlocks,
  convertBlocksToContent,
  splitIntoPages,
} from "../utils/documentUtils";
import {
  fetchDocument,
  saveDocument,
  regenerateSectionWithAI,
} from "../services/documentService";

interface DocumentContextProps {
  documentData: GeneratedDocumentData;
  editableSections: Section[];
  documentTheme: DocumentTheme;
  isLoading: boolean;
  isSaving: boolean;
  isEdited: boolean;
  overview: string;
  documentDbId: string | null;
  clientId: string | null;
  promptTemplateId: string | null;
  scopeTemplateId: string | null;
  filePath: string | null;
  aiPromptsSummary: AIPromptData;
  selectedSectionId: string | null;
  regeneratingSection: string | null;
  pages: any[];
  currentPage: number;

  // Methods
  setDocumentData: React.Dispatch<React.SetStateAction<GeneratedDocumentData>>;
  setEditableSections: React.Dispatch<React.SetStateAction<Section[]>>;
  setDocumentTheme: React.Dispatch<React.SetStateAction<DocumentTheme>>;
  handleUpdateSectionTitle: (sectionId: string, newTitle: string) => void;
  handleUpdateBlock: (
    sectionId: string,
    blockId: string,
    updates: Partial<Block>
  ) => void;
  handleAddBlock: (sectionId: string, type: Block["type"]) => void;
  handleDeleteBlock: (sectionId: string, blockId: string) => void;
  handleRegenerateWithAI: (
    sectionId: string,
    enhancementType: string
  ) => Promise<void>;
  setCurrentPage: (page: number) => void;
  setSelectedSectionId: (id: string | null) => void;
  saveDocumentToSupabase: () => Promise<void>;
  applyThemePreset: (
    presetName: "professional" | "modern" | "creative" | "formal"
  ) => void;
  saveDocumentTheme: () => void;
  updateThemeProperty: (property: keyof DocumentTheme, value: string) => void;
  updateHeaderProperty: (property: string, value: any) => void;
  updateFooterProperty: (property: string, value: any) => void;
  handleLogoUpload: () => void;
}

export const DocumentContext = createContext<DocumentContextProps | null>(null);

export const useDocumentContext = () => {
  const context = useContext(DocumentContext);
  if (!context) {
    throw new Error(
      "useDocumentContext must be used within a DocumentProvider"
    );
  }
  return context;
};

interface DocumentProviderProps {
  children: ReactNode;
}

export const DocumentProvider: React.FC<DocumentProviderProps> = ({
  children,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const documentId = searchParams?.get("id");
  const titleFromURL = searchParams?.get("title");
  const preserveOriginal = searchParams?.get("preserveOriginal") === "true";

  // State variables
  const [documentData, setDocumentData] = useState<GeneratedDocumentData>({
    title: "",
    client: "",
    type: "",
    sections: [],
  });
  const [editableSections, setEditableSections] = useState<Section[]>([]);
  const [documentTheme, setDocumentTheme] = useState<DocumentTheme>(
    themePresets.professional
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isEdited, setIsEdited] = useState(false);
  const [overview, setOverview] = useState("");
  const [documentDbId, setDocumentDbId] = useState<string | null>(null);
  const [clientId, setClientId] = useState<string | null>(null);
  const [promptTemplateId, setPromptTemplateId] = useState<string | null>(null);
  const [scopeTemplateId, setScopeTemplateId] = useState<string | null>(null);
  const [filePath, setFilePath] = useState<string | null>(null);
  const [aiPromptsSummary, setAiPromptsSummary] = useState<AIPromptData>({
    styleGuidance: "",
    contentFocus: "",
    additionalInstructions: "",
  });
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(
    null
  );
  const [regeneratingSection, setRegeneratingSection] = useState<string | null>(
    null
  );
  const [pages, setPages] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(0);

  // Show a toast notification when content is preserved from original
  useEffect(() => {
    if (preserveOriginal && !isLoading) {
      toast({
        title: "Content preserved from reference document",
        description:
          "This document contains the original content from your reference document. You can edit it or use AI to enhance specific sections.",
        variant: "default",
      });
    }
  }, [preserveOriginal, isLoading, toast]);

  // Update pages when editableSections change (for live preview)
  useEffect(() => {
    if (editableSections.length > 0) {
      const newPages = splitIntoPages(editableSections);
      setPages(newPages);
    }
  }, [editableSections]);

  // Load document data when component mounts
  useEffect(() => {
    const loadDocumentData = async () => {
      try {
        setIsLoading(true);

        // Always reset currentPage to 0 when loading new data
        setCurrentPage(0);

        if (documentId) {
          // Try to load from session storage first
          let documentDataFromSession = null;
          try {
            const sessionData = sessionStorage.getItem(
              `document_${documentId}`
            );
            if (sessionData) {
              documentDataFromSession = JSON.parse(sessionData);
            }
          } catch (error) {
            console.error("Error reading from session storage:", error);
          }

          if (documentDataFromSession) {
            // Set data from session storage
            console.log(
              "Loading document from session storage:",
              documentDataFromSession
            );

            setDocumentData(
              documentDataFromSession.documentData || {
                title: titleFromURL || "",
                client: "",
                type: "",
                sections: [],
              }
            );

            setEditableSections(documentDataFromSession.sections || []);

            // Initialize pages from session data
            if (
              documentDataFromSession.sections &&
              documentDataFromSession.sections.length > 0
            ) {
              console.log(
                "Loading sections from session:",
                documentDataFromSession.sections
              );
              const newPages = splitIntoPages(documentDataFromSession.sections);
              setPages(newPages);
              console.log("Created pages from session:", newPages);
            }

            if (documentDataFromSession.theme) {
              setDocumentTheme(documentDataFromSession.theme);
            }

            // Set additional metadata from session
            if (documentDataFromSession.overview) {
              setOverview(documentDataFromSession.overview);
            }

            if (documentDataFromSession.documentDbId) {
              setDocumentDbId(documentDataFromSession.documentDbId);
            }

            if (documentDataFromSession.clientId) {
              setClientId(documentDataFromSession.clientId);
            }

            if (documentDataFromSession.promptTemplateId) {
              setPromptTemplateId(documentDataFromSession.promptTemplateId);
            }

            if (documentDataFromSession.scopeTemplateId) {
              setScopeTemplateId(documentDataFromSession.scopeTemplateId);
            }

            if (documentDataFromSession.filePath) {
              setFilePath(documentDataFromSession.filePath);
            }

            if (documentDataFromSession.aiPromptsSummary) {
              setAiPromptsSummary(documentDataFromSession.aiPromptsSummary);
            }
          } else {
            // Fallback: Fetch from database
            console.log("Loading document from database, ID:", documentId);
            console.log("Document ID type:", typeof documentId);

            try {
              const result = await fetchDocument(documentId);
              console.log("fetchDocument result:", result);

              if (result?.document) {
                console.log("Fetched document from database:", result.document);

                // Set document data
                setDocumentData(result.document);
                setDocumentDbId(result.document.id?.toString() || documentId);

                // Create editable sections from document sections
                const sectionsWithBlocks = result.document.sections.map(
                  (section: any) => ({
                    ...section,
                    blocks: convertContentToBlocks(section.content || ""),
                  })
                );

                console.log(
                  "Loading sections from database:",
                  result.document.sections
                );
                console.log("Sections with blocks:", sectionsWithBlocks);

                setEditableSections(sectionsWithBlocks);

                // Initialize pages after setting sections
                const newPages = splitIntoPages(sectionsWithBlocks);
                setPages(newPages);
                console.log("Created pages from database:", newPages);

                // Set theme if available
                if (result.document.theme) {
                  setDocumentTheme(result.document.theme);
                }

                // Set client info if available
                if (result.document.client) {
                  // For now, we don't have a way to get client ID from the service
                  // but we have the client name
                  setDocumentData((prev) => ({
                    ...prev,
                    client: result.document.client,
                  }));
                }

                // toast({
                //   title: "Document Loaded",
                //   description: "Document loaded successfully from database",
                // });
              } else {
                throw new Error("Document not found");
              }
            } catch (dbError) {
              console.error("Error loading document from database:", dbError);
              console.error("Document ID that failed:", documentId);
              console.error("Error details:", JSON.stringify(dbError, null, 2));

              // Always create fallback content immediately when database fails
              const fallbackSections = [
                {
                  id: "section-1",
                  title: "Error Loading Document",
                  content:
                    "There was an error loading this document. You can edit this content and save it.",
                  blocks: convertContentToBlocks(
                    "There was an error loading this document. You can edit this content and save it."
                  ),
                  status: "completed" as const,
                  order: 1,
                },
              ];

              setEditableSections(fallbackSections);
              const fallbackPages = splitIntoPages(fallbackSections);
              setPages(fallbackPages);

              console.log("Set fallback sections:", fallbackSections);
              console.log("Set fallback pages:", fallbackPages);

              toast({
                title: "Error Loading Document",
                description: `Could not load document from database. Document ID: ${documentId}. Error: ${
                  dbError instanceof Error ? dbError.message : "Unknown error"
                }`,
                variant: "destructive",
              });

              // Create a basic document structure as fallback
              setDocumentData({
                title: titleFromURL || `Document ${documentId}`,
                client: "",
                type: "Document",
                sections: fallbackSections,
              });
            }
          }
        } else {
          // For new documents or if no document ID, set a dummy section for testing
          console.log("No document ID, creating new document");

          const sampleContent =
            "This is a sample section. You can edit this content or regenerate it with AI.";
          const sampleBlocks = convertContentToBlocks(sampleContent);

          setEditableSections([
            {
              id: "section-1",
              title: "Introduction",
              content: sampleContent,
              blocks: sampleBlocks,
              status: "completed",
              order: 1,
            },
          ]);

          // Initialize the pages with the sample section
          const newPages = splitIntoPages([
            {
              id: "section-1",
              title: "Introduction",
              content: sampleContent,
              blocks: sampleBlocks,
              status: "completed",
              order: 1,
            },
          ]);
          setPages(newPages);

          if (titleFromURL) {
            setDocumentData((prev) => ({ ...prev, title: titleFromURL }));
          }
        }
      } catch (error) {
        console.error("Error loading document:", error);
        toast({
          title: "Error Loading Document",
          description:
            "There was a problem loading the document. Please try again.",
          variant: "destructive",
        });

        // Ensure we have fallback content even on critical errors
        const fallbackSections = [
          {
            id: "section-1",
            title: "Error Loading Document",
            content:
              "There was an error loading this document. You can edit this content and save it.",
            blocks: convertContentToBlocks(
              "There was an error loading this document. You can edit this content and save it."
            ),
            status: "completed" as const,
            order: 1,
          },
        ];

        setEditableSections(fallbackSections);
        const fallbackPages = splitIntoPages(fallbackSections);
        setPages(fallbackPages);
      } finally {
        // Always set loading to false, even if there's an error
        setIsLoading(false);

        // Final safety check - ensure pages is never empty
        setTimeout(() => {
          // Use a callback to get the current state
          setPages((currentPages) => {
            if (currentPages.length === 0) {
              console.warn(
                "Pages array is empty after loading, creating emergency fallback"
              );
              const emergencySection = {
                id: "emergency-section",
                title: "Document Content",
                content:
                  "Content is being loaded. Please refresh the page if this persists.",
                blocks: convertContentToBlocks(
                  "Content is being loaded. Please refresh the page if this persists."
                ),
                status: "completed" as const,
                order: 1,
              };
              setEditableSections([emergencySection]);
              return splitIntoPages([emergencySection]);
            }
            return currentPages;
          });
        }, 100);
      }
    };

    loadDocumentData();
  }, [documentId, titleFromURL, toast]);

  // Sync editableSections with documentData and pages for preview updates
  useEffect(() => {
    if (editableSections.length > 0) {
      // Update documentData.sections to match editableSections
      setDocumentData((prev) => ({
        ...prev,
        sections: editableSections,
      }));

      // Update pages when sections change
      const newPages = splitIntoPages(editableSections);
      setPages(newPages);
    }
  }, [editableSections]);

  // Section and Block management functions
  const handleUpdateSectionTitle = useCallback(
    (sectionId: string, newTitle: string) => {
      setEditableSections((prev) =>
        prev.map((section) =>
          section.id === sectionId ? { ...section, title: newTitle } : section
        )
      );
      setIsEdited(true);
    },
    []
  );

  const handleUpdateBlock = useCallback(
    (sectionId: string, blockId: string, updates: Partial<Block>) => {
      setEditableSections((prev) =>
        prev.map((section) => {
          if (section.id === sectionId) {
            const updatedBlocks = section.blocks.map((block) =>
              block.id === blockId ? { ...block, ...updates } : block
            );
            // Regenerate content from blocks
            const updatedContent = convertBlocksToContent(updatedBlocks);
            return {
              ...section,
              blocks: updatedBlocks,
              content: updatedContent,
            };
          }
          return section;
        })
      );
      setIsEdited(true);
    },
    []
  );

  const handleAddBlock = useCallback(
    (sectionId: string, type: Block["type"]) => {
      setEditableSections((prev) =>
        prev.map((section) => {
          if (section.id === sectionId) {
            const newBlock: Block = {
              id: `block-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 9)}`,
              type,
              content: "",
              ...(type === "header" && { level: 2 }),
              ...(type === "list" && { items: [""] }),
            };

            const updatedBlocks = [...section.blocks, newBlock];
            // Regenerate content from blocks
            const updatedContent = convertBlocksToContent(updatedBlocks);

            return {
              ...section,
              blocks: updatedBlocks,
              content: updatedContent,
            };
          }
          return section;
        })
      );
      setIsEdited(true);
    },
    []
  );

  const handleDeleteBlock = useCallback(
    (sectionId: string, blockId: string) => {
      setEditableSections((prev) =>
        prev.map((section) => {
          if (section.id === sectionId) {
            const updatedBlocks = section.blocks.filter(
              (block) => block.id !== blockId
            );
            // Regenerate content from blocks
            const updatedContent = convertBlocksToContent(updatedBlocks);

            return {
              ...section,
              blocks: updatedBlocks,
              content: updatedContent,
            };
          }
          return section;
        })
      );
      setIsEdited(true);
    },
    []
  );

  // Function to handle AI regeneration
  const handleRegenerateWithAI = async (
    sectionId: string,
    enhancementType: string
  ) => {
    try {
      // Find the section to regenerate
      const sectionToRegenerate = editableSections.find(
        (section) => section.id === sectionId
      );

      if (!sectionToRegenerate) {
        toast({
          title: "Error",
          description: "Section not found",
          variant: "destructive",
        });
        return;
      }

      // Set loading state
      setRegeneratingSection(sectionId);

      const response = await regenerateSectionWithAI(
        sectionToRegenerate.content,
        sectionToRegenerate.title,
        enhancementType,
        documentData.title
      );

      if (response.success) {
        // Convert the regenerated content to blocks
        const regeneratedBlocks = convertContentToBlocks(
          response.regeneratedContent
        );

        // Process and fix any image blocks - ensure they render properly
        const processedBlocks = regeneratedBlocks.map((block) => {
          if (block.type === "image") {
            // Extract caption and image URL properly
            const caption = block.caption || "";
            // If it's already a data URL or valid URL, keep it
            if (
              block.content.startsWith("data:image") ||
              block.content.startsWith("http")
            ) {
              return block;
            }
            // For image markdown references, try to preserve the original image if possible
            else {
              // Look for existing image blocks to preserve
              const existingImageBlock = sectionToRegenerate.blocks.find(
                (b) => b.type === "image"
              );
              if (existingImageBlock && existingImageBlock.content) {
                return {
                  ...block,
                  content: existingImageBlock.content,
                  caption: block.caption || existingImageBlock.caption,
                };
              }
              return block;
            }
          }
          return block;
        });

        // Update the section with regenerated content
        setEditableSections((prev) =>
          prev.map((section) =>
            section.id === sectionId
              ? {
                  ...section,
                  content: response.regeneratedContent,
                  blocks: processedBlocks,
                  status: "completed",
                }
              : section
          )
        );

        // Also mark document as edited
        setIsEdited(true);

        // Show success toast
        toast({
          title: "Content regenerated",
          description: `The ${sectionToRegenerate.title} section has been enhanced.`,
        });
      } else {
        throw new Error("Failed to regenerate content");
      }
    } catch (error) {
      console.error("Error regenerating content:", error);
      toast({
        title: "Regeneration failed",
        description:
          "There was an error enhancing the content. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Clear loading state
      setRegeneratingSection(null);
    }
  };

  // Save document function
  const saveDocumentToSupabase = async () => {
    try {
      // Check if title is provided
      if (!documentData.title.trim()) {
        toast({
          title: "Title Required",
          description: "Please enter a document title before saving",
          variant: "destructive",
        });
        return;
      }

      setIsSaving(true);

      // Ensure current theme is synchronized to document data before saving
      const updatedDocumentData = {
        ...documentData,
        theme: { ...documentTheme },
      };

      const result = await saveDocument(
        updatedDocumentData,
        editableSections,
        documentTheme,
        documentDbId,
        clientId,
        promptTemplateId,
        scopeTemplateId,
        filePath
      );

      // Store the file path and document ID for future updates
      if (result.documentId) {
        setDocumentDbId(result.documentId);
      }
      if (result.filePath) {
        setFilePath(result.filePath);
      }

      // Reset edited state after successful save
      setIsEdited(false);

      // Clear session storage data for this document
      if (documentId) {
        try {
          sessionStorage.removeItem(`document_${documentId}`);
          console.log(`Cleared session storage for document: ${documentId}`);
        } catch (error) {
          console.error("Error clearing session storage:", error);
        }
      }

      // Clear local storage
      try {
        localStorage.removeItem("generatedContent");
        localStorage.removeItem("aiPrompts");
        console.log("Cleared local storage for generated content");
      } catch (error) {
        console.error("Error clearing local storage:", error);
      }

      toast({
        title: documentDbId ? "Document Updated" : "Document Saved",
        description: documentDbId
          ? "Your document has been updated successfully"
          : "Your document has been saved successfully",
      });

      // Redirect to proposals page with delay to allow toast to be seen
      setTimeout(() => {
        navigate("/scopingai/documents/library");
      }, 1500);
    } catch (error) {
      console.error("Error saving document:", error);
      toast({
        title: "Save Failed",
        description: "There was an error saving your document",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Apply theme preset
  const applyThemePreset = (
    presetName: "professional" | "modern" | "creative" | "formal"
  ) => {
    setDocumentTheme(themePresets[presetName]);

    // Show toast notification
    toast({
      title: "Theme Applied",
      description: `Applied the ${presetName} theme to your document.`,
    });
  };

  // Save document theme
  const saveDocumentTheme = useCallback(() => {
    // Show loading indicator
    setIsLoading(true);

    try {
      console.log("Saving document theme...");
      console.log("Logo present:", !!documentTheme.logo);
      console.log(
        "Logo length:",
        documentTheme.logo ? documentTheme.logo.length : 0
      );
      console.log("Show cover page:", documentTheme.showCoverPage !== false);

      // Update document data with theme only on explicit save
      // This creates a complete, fresh copy of the theme to avoid reference issues
      setDocumentData((prev) => ({
        ...prev,
        theme: JSON.parse(JSON.stringify(documentTheme)),
      }));

      // Mark as edited
      setIsEdited(true);

      // Save to local storage or session storage depending on documentId
      if (documentId) {
        const existingData = JSON.parse(
          sessionStorage.getItem(`document_${documentId}`) || "{}"
        );

        // Ensure we're storing the complete theme with all properties
        sessionStorage.setItem(
          `document_${documentId}`,
          JSON.stringify({
            ...existingData,
            theme: documentTheme,
            // Explicitly add properties to ensure they're saved
            hasLogo: !!documentTheme.logo,
            logoData: documentTheme.logo || null,
            showCoverPage: documentTheme.showCoverPage !== false,
          })
        );
      } else {
        const existingData = JSON.parse(
          localStorage.getItem("generatedContent") || "{}"
        );

        // Ensure we're storing the complete theme with all properties
        localStorage.setItem(
          "generatedContent",
          JSON.stringify({
            ...existingData,
            theme: documentTheme,
            // Explicitly add properties to ensure they're saved
            hasLogo: !!documentTheme.logo,
            logoData: documentTheme.logo || null,
            showCoverPage: documentTheme.showCoverPage !== false,
          })
        );
      }

      toast({
        title: "Theme Saved",
        description: "Document theme has been updated.",
      });
    } catch (e) {
      console.error("Error saving theme:", e);
      toast({
        title: "Save Failed",
        description: "There was an error saving the theme",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [documentId, documentTheme, toast]);

  // Theme property update functions
  const updateThemeProperty = useCallback(
    (property: keyof DocumentTheme, value: string) => {
      setDocumentTheme((prev) => ({
        ...prev,
        [property]: value,
      }));
      setIsEdited(true);
    },
    []
  );

  const updateHeaderProperty = useCallback((property: string, value: any) => {
    setDocumentTheme((prev) => ({
      ...prev,
      header: {
        ...prev.header,
        [property]: value,
      },
    }));
    setIsEdited(true);
  }, []);

  const updateFooterProperty = useCallback((property: string, value: any) => {
    setDocumentTheme((prev) => ({
      ...prev,
      footer: {
        ...prev.footer,
        [property]: value,
      },
    }));
    setIsEdited(true);
  }, []);

  const handleLogoUpload = useCallback(() => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      if (target.files && target.files.length > 0) {
        const file = target.files[0];
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target && typeof event.target.result === "string") {
            setDocumentTheme((prev) => ({
              ...prev,
              logo: event.target!.result as string,
            }));
            setIsEdited(true);
            toast({
              title: "Logo uploaded",
              description: "Your logo has been added to the document theme.",
            });
          }
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  }, [toast]);

  const value = {
    documentData,
    editableSections,
    documentTheme,
    isLoading,
    isSaving,
    isEdited,
    overview,
    documentDbId,
    clientId,
    promptTemplateId,
    scopeTemplateId,
    filePath,
    aiPromptsSummary,
    selectedSectionId,
    regeneratingSection,
    pages,
    currentPage,

    // Methods
    setDocumentData,
    setEditableSections,
    setDocumentTheme,
    handleUpdateSectionTitle,
    handleUpdateBlock,
    handleAddBlock,
    handleDeleteBlock,
    handleRegenerateWithAI,
    setCurrentPage,
    setSelectedSectionId,
    saveDocumentToSupabase,
    applyThemePreset,
    saveDocumentTheme,
    updateThemeProperty,
    updateHeaderProperty,
    updateFooterProperty,
    handleLogoUpload,
  };

  return (
    <DocumentContext.Provider value={value}>
      {children}
    </DocumentContext.Provider>
  );
};
