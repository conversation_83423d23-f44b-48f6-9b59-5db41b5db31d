/**
 * Formats a date string to a human-readable format
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return "";

  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

/**
 * Formats a price in cents to dollars with 2 decimal places
 */
export const formatPrice = (cents: number): string => {
  if (!cents && cents !== 0) return "";

  const dollars = cents / 100;
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(dollars);
};

/**
 * Truncates text to a specified length
 */
export const truncateText = (text: string, maxLength: number = 100): string => {
  if (!text) return "";

  if (text.length <= maxLength) return text;

  return `${text.substring(0, maxLength)}...`;
};

/**
 * Converts string to title case (capitalize first letter of each word)
 */
export const toTitleCase = (text: string): string => {
  if (!text) return "";

  return text
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
