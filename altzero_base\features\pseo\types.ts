import { PSEO_CONSTANTS } from './utilities/pseo/constants';

// Base entity interfaces
export interface PSEOClient {
  id: string;
  user_id: string;
  name: string;
  email?: string;
  company?: string;
  created_at: string;
  updated_at: string;
}

export interface PSEOWebsite {
  id: string;
  client_id: string;
  url: string;
  domain: string;
  name: string;
  status: typeof PSEO_CONSTANTS.WEBSITE_STATUS[keyof typeof PSEO_CONSTANTS.WEBSITE_STATUS];
  created_at: string;
  updated_at: string;
}

export interface PSEOAudit {
  id: string;
  website_id: string;
  status: typeof PSEO_CONSTANTS.AUDIT_STATUS[keyof typeof PSEO_CONSTANTS.AUDIT_STATUS];
  scraped_content?: string;
  scrape_metadata: Record<string, unknown>;
  technical_audit_raw?: string;
  content_audit_raw?: string;
  technical_analysis: Record<string, unknown>;
  content_analysis: Record<string, unknown>;
  combined_report?: string;
  report_html?: string;
  ai_model_used: string;
  processing_time_seconds?: number;
  error_message?: string;
  html_analysis_result?: string;
  seo_score?: number;
  seo_grade?: string;
  seo_scoring_result?: string;
  created_at: string;
  completed_at?: string;
  updated_at?: string;
  html_analysis?: any;
  html_issues?: any[];
  total_issues?: number;
  issues_by_type?: Record<string, number>;
  performance_metrics?: any;
  overall_score?: number;
  grade?: string;
  seo_metrics?: any;
  recommendations?: any[];
  provider_breakdown?: any;
}

export interface PSEOAuditStep {
  id: string;
  audit_id: string;
  step_name: string;
  step_type?: string;
  status: typeof PSEO_CONSTANTS.STEP_STATUS[keyof typeof PSEO_CONSTANTS.STEP_STATUS];
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  step_data: Record<string, unknown>;
  result?: string;
  metadata?: string;
}

// Data transfer objects for API operations
export interface CreateClientRequest {
  name: string;
  email?: string;
  company?: string;
}

export interface CreateWebsiteRequest {
  client_id: string;
  url: string;
  name: string;
}

export interface StartAuditRequest {
  website_id: string;
  ai_model?: string;
}

// Scraped data interface
export interface ScrapedData {
  url: string;
  statusCode: number;
  html: string;
  headers: Record<string, string>;
  loadTime: number;
  timestamp: string;
}

// Audit progress tracking
export interface AuditProgress {
  auditId: string;
  currentStep: string;
  completedSteps: string[];
  totalSteps: number;
  isComplete: boolean;
  hasError: boolean;
  errorMessage?: string;
  progressPercentage: number;
}

// Dashboard view interfaces
export interface ClientDashboardData extends PSEOClient {
  website_count: number;
  total_audits: number;
  completed_audits: number;
  last_audit_date?: string;
}

export interface WebsiteDashboardData extends PSEOWebsite {
  client_name: string;
  audit_count: number;
  completed_audits: number;
  failed_audits: number;
  last_audit_date?: string;
  last_completed_audit?: string;
}

// AI Analysis results
export interface TechnicalAnalysisResult {
  criticalIssues: string[];
  quickWins: string[];
  opportunities: string[];
  rawAnalysis: string;
}

export interface ContentAnalysisResult {
  analysis: {
    contentQuality: string;
    keywordAnalysis: string;
    readabilityAnalysis: string;
  };
  recommendations: string[];
  rawAnalysis: string;
}

// Report generation
export interface AuditReport {
  id: string;
  website_url: string;
  website_name: string;
  client_name: string;
  audit_date: string;
  technical_analysis: TechnicalAnalysisResult;
  content_analysis: ContentAnalysisResult;
  combined_markdown: string;
  combined_html: string;
  processing_time: number;
  ai_model_used: string;
}

// Error handling
export interface PSEOError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// API Response wrappers
export interface PSEOApiResponse<T> {
  success: boolean;
  data?: T;
  error?: PSEOError;
}

export interface PSEOListResponse<T> extends PSEOApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form validation
export interface ClientFormData {
  name: string;
  email: string;
  company: string;
}

export interface WebsiteFormData {
  url: string;
  name: string;
}

export interface FormErrors {
  [key: string]: string;
}

// Component props interfaces
export interface ClientCardProps {
  client: ClientDashboardData;
  onEdit: (client: PSEOClient) => void;
  onDelete: (clientId: string) => void;
  onViewWebsites: (clientId: string) => void;
}

export interface WebsiteCardProps {
  website: WebsiteDashboardData;
  onEdit: (website: PSEOWebsite) => void;
  onDelete: (websiteId: string) => void;
  onStartAudit: (websiteId: string) => void;
  onViewAudits: (websiteId: string) => void;
}

export interface AuditProgressProps {
  auditId: string;
  onComplete: (auditId: string) => void;
  onError: (error: PSEOError) => void;
}

export interface ReportViewerProps {
  audit: PSEOAudit;
  website: PSEOWebsite;
  client: PSEOClient;
}

// Context state interfaces
export interface PSEOContextState {
  clients: PSEOClient[];
  selectedClient: PSEOClient | null;
  websites: PSEOWebsite[];
  selectedWebsite: PSEOWebsite | null;
  audits: PSEOAudit[];
  currentAudit: PSEOAudit | null;
  auditProgress: AuditProgress | null;
  isLoading: boolean;
  error: PSEOError | null;
}

// Action types for context reducer
export type PSEOAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: PSEOError | null }
  | { type: 'SET_CLIENTS'; payload: PSEOClient[] }
  | { type: 'SET_SELECTED_CLIENT'; payload: PSEOClient | null }
  | { type: 'SET_WEBSITES'; payload: PSEOWebsite[] }
  | { type: 'SET_SELECTED_WEBSITE'; payload: PSEOWebsite | null }
  | { type: 'SET_AUDITS'; payload: PSEOAudit[] }
  | { type: 'SET_CURRENT_AUDIT'; payload: PSEOAudit | null }
  | { type: 'SET_AUDIT_PROGRESS'; payload: AuditProgress | null }
  | { type: 'ADD_CLIENT'; payload: PSEOClient }
  | { type: 'UPDATE_CLIENT'; payload: PSEOClient }
  | { type: 'REMOVE_CLIENT'; payload: string }
  | { type: 'ADD_WEBSITE'; payload: PSEOWebsite }
  | { type: 'UPDATE_WEBSITE'; payload: PSEOWebsite }
  | { type: 'REMOVE_WEBSITE'; payload: string }
  | { type: 'ADD_AUDIT'; payload: PSEOAudit }
  | { type: 'UPDATE_AUDIT'; payload: PSEOAudit }
  | { type: 'REMOVE_AUDIT'; payload: string };

// Utility types
export type PSEOEntityType = 'client' | 'website' | 'audit';

export type SortDirection = 'asc' | 'desc';

export interface SortConfig {
  field: string;
  direction: SortDirection;
}

export interface FilterConfig {
  status?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  searchTerm?: string;
}

// Export type aliases for convenience
export type AuditStatus = typeof PSEO_CONSTANTS.AUDIT_STATUS[keyof typeof PSEO_CONSTANTS.AUDIT_STATUS];
export type StepStatus = typeof PSEO_CONSTANTS.STEP_STATUS[keyof typeof PSEO_CONSTANTS.STEP_STATUS];
export type WebsiteStatus = typeof PSEO_CONSTANTS.WEBSITE_STATUS[keyof typeof PSEO_CONSTANTS.WEBSITE_STATUS];
export type AuditStepName = typeof PSEO_CONSTANTS.AUDIT_STEPS[keyof typeof PSEO_CONSTANTS.AUDIT_STEPS];

// AI Analysis Service Types
export interface PSEOTechnicalAnalysis {
  criticalIssues: Array<{
    issue: string;
    impact: string;
    recommendation: string;
    priority: 'high' | 'medium' | 'low';
  }>;
  quickWins: Array<{
    opportunity: string;
    implementation: string;
    expectedImpact: string;
  }>;
  opportunities: Array<{
    area: string;
    description: string;
    effort: string;
    impact: string;
  }>;
  htmlIssues?: any[];
  htmlScore?: number;
  issuesByType?: Record<string, number>;
}

export interface PSEOContentAnalysis {
  analysis: {
    contentQuality: string;
    keywordOptimization: string;
    structureAssessment: string;
    userExperience: string;
  };
  recommendations: Array<{
    category: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
    implementation: string;
  }>;
  seoMetrics?: any;
  overallScore?: number;
  grade?: string;
}

// API Request/Response Types
export interface PSEOAnalysisRequest {
  scrapedContent: string;
  url: string;
  userId?: string;
  auditId?: string;
}

export interface PSEOAnalysisResponse {
  technical: PSEOTechnicalAnalysis;
  content: PSEOContentAnalysis;
  processingTime: number;
  tokensUsed: number;
  url: string;
  timestamp: string;
  enhancedAnalysis?: {
    htmlAnalysis: any;
    seoScoring: any;
    processingTime: number;
  };
}

export interface PSEOReportGenerationRequest {
  technicalAnalysis: PSEOTechnicalAnalysis;
  contentAnalysis: PSEOContentAnalysis;
  url: string;
  domain: string;
  userId?: string;
}

export interface PSEOReportGenerationResponse {
  markdownReport: string;
  url: string;
  domain: string;
  timestamp: string;
}

export interface PSEOBatchAnalysisRequest {
  urls: string[];
  scrapedContents: string[];
  userId?: string;
}

export interface PSEOBatchAnalysisResponse {
  success: boolean;
  results: Array<{
    url: string;
    status: 'fulfilled' | 'rejected';
    analysis?: PSEOAnalysisResponse;
    error?: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
  metadata: {
    timestamp: string;
  };
}

export interface PSEOServiceHealthResponse {
  status: 'ok' | 'error';
  services: {
    openai: 'healthy' | 'unhealthy';
  };
  timestamp: string;
}

export interface PSEOOpenAIStatusResponse {
  enabled: boolean;
  availableModels: string[];
  defaultModel: string;
} 

// =====================================================
// AGENTIC pSEO TYPES - NEW INTERFACES
// =====================================================

// Website page discovery
export interface PSEOWebsitePage {
  id: string;
  website_id: string;
  url: string;
  title?: string;
  meta_description?: string;
  content_hash?: string;
  page_type: 'page' | 'blog' | 'product' | 'category' | 'homepage';
  status: 'discovered' | 'crawled' | 'analyzed' | 'error';
  discovered_via: 'sitemap' | 'crawl' | 'manual';
  last_crawled?: string;
  crawl_error?: string;
  word_count?: number;
  response_code?: number;
  load_time_ms?: number;
  created_at: string;
  updated_at: string;
}

// Keyword research data
export interface PSEOKeyword {
  id: string;
  website_id: string;
  keyword: string;
  search_volume?: number;
  keyword_difficulty?: number;
  cpc?: number;
  competition?: 'low' | 'medium' | 'high';
  ranking_position?: number;
  ranking_url?: string;
  intent?: 'informational' | 'navigational' | 'commercial' | 'transactional';
  data_source: 'google_planner' | 'ubersuggest' | 'dataforseo' | 'manual';
  created_at: string;
  updated_at: string;
}

// Backlink analysis data
export interface PSEOBacklink {
  id: string;
  website_id: string;
  source_domain: string;
  source_url: string;
  target_url: string;
  anchor_text?: string;
  link_type: 'dofollow' | 'nofollow' | 'unknown';
  domain_authority?: number;
  page_authority?: number;
  spam_score?: number;
  first_seen?: string;
  last_seen?: string;
  status: 'active' | 'lost' | 'unknown';
  data_source: 'moz' | 'majestic' | 'ahrefs' | 'manual';
  created_at: string;
}

// Content opportunities
export interface PSEOContentOpportunity {
  id: string;
  website_id: string;
  target_keyword: string;
  content_type: 'blog' | 'landing' | 'product' | 'guide' | 'faq';
  title: string;
  content_brief?: string;
  target_word_count?: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  difficulty_score?: number;
  estimated_traffic?: number;
  status: 'identified' | 'in_progress' | 'completed' | 'abandoned';
  assigned_to?: string;
  due_date?: string;
  generated_content?: string;
  ai_model_used?: string;
  created_at: string;
  updated_at: string;
}

// Agent job tracking
export interface PSEOAgentJob {
  id: string;
  website_id: string;
  job_type: 'page_discovery' | 'keyword_research' | 'backlink_analysis' | 'content_generation' | 'full_site_audit';
  agent_name: string;
  parent_job_id?: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  started_at?: string;
  completed_at?: string;
  processing_time_seconds?: number;
  result_data: Record<string, unknown>;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  created_at: string;
  updated_at: string;
}

// Website analysis summary
export interface PSEOWebsiteAnalysis {
  id: string;
  website_id: string;
  total_pages: number;
  crawled_pages: number;
  total_keywords: number;
  ranking_keywords: number;
  total_backlinks: number;
  referring_domains: number;
  domain_authority?: number;
  organic_traffic_estimate?: number;
  content_gaps_identified: number;
  technical_issues_count: number;
  last_full_analysis?: string;
  analysis_status: 'pending' | 'in_progress' | 'completed' | 'error';
  created_at: string;
  updated_at: string;
}

// =====================================================
// AGENTIC DASHBOARD VIEW TYPES
// =====================================================

// Website discovery progress
export interface PSEOWebsiteDiscoveryProgress {
  website_id: string;
  website_name: string;
  domain: string;
  total_discovered_pages: number;
  crawled_pages: number;
  analyzed_pages: number;
  error_pages: number;
  last_crawl_date?: string;
  avg_load_time?: number;
}

// Keyword opportunities view
export interface PSEOKeywordOpportunity {
  website_id: string;
  website_name: string;
  keyword: string;
  search_volume?: number;
  keyword_difficulty?: number;
  ranking_position?: number;
  ranking_tier: 'not_ranking' | 'top_3' | 'page_1' | 'page_2' | 'beyond_page_2';
  intent?: string;
  data_source: string;
}

// Content gap analysis
export interface PSEOContentGap {
  website_id: string;
  website_name: string;
  target_keyword: string;
  content_type: string;
  priority: string;
  difficulty_score?: number;
  estimated_traffic?: number;
  status: string;
  title: string;
  opportunity_type: 'content_exists' | 'quick_win' | 'high_impact' | 'standard_opportunity';
}

// Agent job summary
export interface PSEOAgentJobSummary {
  website_id: string;
  website_name: string;
  job_type?: string;
  total_jobs: number;
  completed_jobs: number;
  running_jobs: number;
  failed_jobs: number;
  avg_processing_time?: number;
  last_completed?: string;
}

// =====================================================
// AGENTIC API REQUEST/RESPONSE TYPES
// =====================================================

// Full site analysis request
export interface StartFullSiteAnalysisRequest {
  website_id: string;
  analysis_types: ('page_discovery' | 'keyword_research' | 'backlink_analysis' | 'content_generation')[];
  options?: {
    max_pages_to_crawl?: number;
    keyword_limit?: number;
    include_competitors?: boolean;
    generate_content?: boolean;
  };
}

// Page discovery request
export interface PageDiscoveryRequest {
  website_id: string;
  discovery_methods: ('sitemap' | 'crawl')[];
  max_pages?: number;
  crawl_depth?: number;
}

// Keyword research request
export interface KeywordResearchRequest {
  website_id: string;
  seed_keywords?: string[];
  competitor_domains?: string[];
  max_keywords?: number;
  data_sources: ('google_planner' | 'ubersuggest' | 'dataforseo')[];
}

// Backlink analysis request
export interface BacklinkAnalysisRequest {
  website_id: string;
  data_sources: ('moz' | 'majestic' | 'ahrefs')[];
  include_competitor_analysis?: boolean;
}

// Content generation request
export interface ContentGenerationRequest {
  website_id: string;
  target_keywords: string[];
  content_types: ('blog' | 'landing' | 'product' | 'guide' | 'faq')[];
  ai_model?: string;
}

// Agent job progress
export interface AgentJobProgress {
  job_id: string;
  job_type: string;
  status: string;
  progress_percentage: number;
  current_step?: string;
  total_steps?: number;
  completed_steps?: number;
  estimated_completion?: string;
  error_message?: string;
}

// =====================================================
// AGENTIC COMPONENT PROPS
// =====================================================

export interface FullSiteAnalysisProps {
  website: PSEOWebsite;
  client: PSEOClient;
  onAnalysisStart: (request: StartFullSiteAnalysisRequest) => void;
  onAnalysisComplete: (results: PSEOWebsiteAnalysis) => void;
}

export interface KeywordResearchProps {
  website: PSEOWebsite;
  keywords: PSEOKeyword[];
  opportunities: PSEOKeywordOpportunity[];
  onKeywordResearch: (request: KeywordResearchRequest) => void;
}

export interface BacklinkProfileProps {
  website: PSEOWebsite;
  backlinks: PSEOBacklink[];
  onBacklinkAnalysis: (request: BacklinkAnalysisRequest) => void;
}

export interface ContentOpportunitiesProps {
  website: PSEOWebsite;
  opportunities: PSEOContentOpportunity[];
  gaps: PSEOContentGap[];
  onContentGeneration: (request: ContentGenerationRequest) => void;
}

export interface AgentJobMonitorProps {
  jobs: PSEOAgentJob[];
  progress: AgentJobProgress[];
  onJobCancel: (jobId: string) => void;
  onJobRetry: (jobId: string) => void;
}

// =====================================================
// EXTENDED CONTEXT STATE FOR AGENTIC FEATURES
// =====================================================

export interface PSEOAgenticContextState extends PSEOContextState {
  // Website pages
  websitePages: PSEOWebsitePage[];
  discoveryProgress: PSEOWebsiteDiscoveryProgress | null;
  
  // Keywords
  keywords: PSEOKeyword[];
  keywordOpportunities: PSEOKeywordOpportunity[];
  
  // Backlinks
  backlinks: PSEOBacklink[];
  
  // Content opportunities
  contentOpportunities: PSEOContentOpportunity[];
  contentGaps: PSEOContentGap[];
  
  // Agent jobs
  agentJobs: PSEOAgentJob[];
  jobProgress: AgentJobProgress[];
  
  // Website analysis
  websiteAnalysis: PSEOWebsiteAnalysis | null;
  
  // UI state
  activeAgenticTab: 'discovery' | 'keywords' | 'backlinks' | 'content' | 'overview';
  isAgenticAnalysisRunning: boolean;
}

// Extended action types for agentic features
export type PSEOAgenticAction = PSEOAction
  | { type: 'SET_WEBSITE_PAGES'; payload: PSEOWebsitePage[] }
  | { type: 'SET_DISCOVERY_PROGRESS'; payload: PSEOWebsiteDiscoveryProgress | null }
  | { type: 'SET_KEYWORDS'; payload: PSEOKeyword[] }
  | { type: 'SET_KEYWORD_OPPORTUNITIES'; payload: PSEOKeywordOpportunity[] }
  | { type: 'SET_BACKLINKS'; payload: PSEOBacklink[] }
  | { type: 'SET_CONTENT_OPPORTUNITIES'; payload: PSEOContentOpportunity[] }
  | { type: 'SET_CONTENT_GAPS'; payload: PSEOContentGap[] }
  | { type: 'SET_AGENT_JOBS'; payload: PSEOAgentJob[] }
  | { type: 'SET_JOB_PROGRESS'; payload: AgentJobProgress[] }
  | { type: 'SET_WEBSITE_ANALYSIS'; payload: PSEOWebsiteAnalysis | null }
  | { type: 'SET_ACTIVE_AGENTIC_TAB'; payload: 'discovery' | 'keywords' | 'backlinks' | 'content' | 'overview' }
  | { type: 'SET_AGENTIC_ANALYSIS_RUNNING'; payload: boolean }
  | { type: 'ADD_AGENT_JOB'; payload: PSEOAgentJob }
  | { type: 'UPDATE_AGENT_JOB'; payload: PSEOAgentJob }
  | { type: 'UPDATE_JOB_PROGRESS'; payload: AgentJobProgress };

// =====================================================
// AGENTIC ANALYSIS RESULTS
// =====================================================

export interface AgenticAnalysisResults {
  website_id: string;
  analysis_type: 'full_site' | 'pages_only' | 'keywords_only' | 'backlinks_only' | 'content_only';
  started_at: string;
  completed_at?: string;
  status: 'running' | 'completed' | 'failed';
  
  page_discovery?: {
    total_pages_found: number;
    pages_crawled: number;
    pages_with_errors: number;
    discovery_method: string[];
  };
  
  keyword_research?: {
    total_keywords_found: number;
    ranking_keywords: number;
    keyword_opportunities: number;
    data_sources_used: string[];
  };
  
  backlink_analysis?: {
    total_backlinks: number;
    referring_domains: number;
    domain_authority: number;
    spam_score: number;
  };
  
  content_analysis?: {
    content_gaps_identified: number;
    content_opportunities: number;
    suggested_topics: string[];
  };
  
  summary: {
    overall_score: number;
    priority_actions: string[];
    estimated_effort_hours: number;
    potential_traffic_increase: number;
  };
} 