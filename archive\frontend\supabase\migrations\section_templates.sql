-- Create section templates table for Supa<PERSON>
CREATE TABLE section_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE, -- Whether template is publicly available to all users
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
CREATE TABLE sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  sections JSONB,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE
);
-- Create the junction table for section templates and sections
CREATE TABLE section_template_sections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  section_template_id UUID REFERENCES section_templates(id) ON DELETE CASCADE NOT NULL,
  section_id UUID REFERENCES sections(id) ON DELETE CASCADE NOT NULL,
  order_position INTEGER NOT NULL DEFAULT 0, -- Position of section in the template
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(section_template_id, section_id) -- Prevent duplicate entries
);

-- Create index for faster queries by user
CREATE INDEX section_templates_user_id_idx ON section_templates(user_id);
CREATE INDEX section_template_sections_template_idx ON section_template_sections(section_template_id);
CREATE INDEX section_template_sections_section_idx ON section_template_sections(section_id);

-- Enable Row Level Security
ALTER TABLE section_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE section_template_sections ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select their own templates or public ones
CREATE POLICY "Users can view their own templates or public ones" ON section_templates
  FOR SELECT USING (
    auth.uid() = user_id OR 
    is_public = TRUE
  );

-- Create policy to allow users to insert their own templates
CREATE POLICY "Users can insert their own templates" ON section_templates
  FOR INSERT WITH CHECK (
    auth.uid() = user_id
  );

-- Create policy to allow users to update their own templates
CREATE POLICY "Users can update their own templates" ON section_templates
  FOR UPDATE USING (
    auth.uid() = user_id
  );

-- Create policy to allow users to delete their own templates
CREATE POLICY "Users can delete their own templates" ON section_templates
  FOR DELETE USING (
    auth.uid() = user_id
  );

-- Create policies for the junction table
CREATE POLICY "Users can view their template sections" ON section_template_sections
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM section_templates
      WHERE id = section_template_id AND (auth.uid() = user_id OR is_public = TRUE)
    )
  );

CREATE POLICY "Users can insert their template sections" ON section_template_sections
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM section_templates
      WHERE id = section_template_id AND auth.uid() = user_id
    )
  );

CREATE POLICY "Users can update their template sections" ON section_template_sections
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM section_templates
      WHERE id = section_template_id AND auth.uid() = user_id
    )
  );

CREATE POLICY "Users can delete their template sections" ON section_template_sections
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM section_templates
      WHERE id = section_template_id AND auth.uid() = user_id
    )
  );

-- Add comment to describe the structure of the sections JSONB field
COMMENT ON COLUMN section_templates.sections IS E'JSON array of section objects with format: 
[
  {
    "id": "string",
    "title": "string",
    "description": "string",
    "content": "string",
    "order": number
  }
]'; 