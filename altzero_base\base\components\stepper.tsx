import React from "react"
import { Check } from "lucide-react"

interface StepperProps {
  currentStep: number
  steps: {
    label: string
    description?: string
  }[]
}

export function Stepper({ currentStep, steps }: StepperProps) {
  return (
    <div className="w-full">
      <ol className="grid grid-cols-1 md:grid-cols-5 lg:grid-cols-5 gap-2">
        {steps.map((step, index) => {
          const stepNumber = index + 1
          const isActive = currentStep === stepNumber
          const isCompleted = currentStep > stepNumber

          return (
            <li key={step.label} className="relative">
              <div className="flex flex-col md:flex-col items-center md:items-center text-center md:text-center">
                {/* Step circle */}
                <div
                  className={`flex h-10 w-10 shrink-0 items-center justify-center rounded-full border-2 z-10 transition-all duration-200 ${
                    isActive
                      ? "border-primary bg-primary text-primary-foreground shadow-md shadow-primary/20"
                      : isCompleted
                        ? "border-primary bg-primary text-primary-foreground"
                        : "border-muted-foreground/30 text-muted-foreground"
                  }`}
                >
                  {isCompleted ? <Check className="h-5 w-5" /> : <span>{stepNumber}</span>}
                </div>
                
                {/* Line connecting steps */}
                {index < steps.length - 1 && (
                  <div className="absolute top-5 left-10 w-full h-[2px] hidden md:block">
                    <div 
                      className={`h-full ${
                        isCompleted ? "bg-primary" : "bg-muted-foreground/30"
                      }`}
                    />
                  </div>
                )}
                
                {/* Step label and description */}
                <div className="mt-3 w-full px-1">
                  <p
                    className={`text-sm font-medium ${
                      isActive ? "text-primary font-semibold" : 
                      isCompleted ? "text-foreground" : "text-muted-foreground"
                    }`}
                  >
                    {step.label}
                  </p>
                  {step.description && (
                    <p className={`text-xs mt-0.5 ${
                      isActive ? "text-foreground" : "text-muted-foreground"
                    }`}>
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
            </li>
          )
        })}
      </ol>
    </div>
  )
}
