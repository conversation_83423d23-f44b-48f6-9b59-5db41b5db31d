// =====================================================
// BASE EXTERNAL SERVICE - BACKEND PROVIDER INTERFACE
// =====================================================

export interface SEOMetrics {
  overall: number;
  technical: number;
  content: number;
  performance: number;
  accessibility: number;
  seo: number;
  details?: any;
}

export interface SEOIssue {
  category: string;
  severity: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  recommendation: string;
  impact: 'high' | 'medium' | 'low';
}

export interface ExternalServiceConfig {
  apiKey?: string;
  rateLimit?: number;
  timeout?: number;
  retries?: number;
  enabled: boolean;
}

export interface ExternalServiceResult {
  provider: string;
  metrics: SEOMetrics;
  issues: SEOIssue[];
  rawData?: any;
  timestamp: string;
  success: boolean;
  error?: string;
}

export abstract class BaseExternalService {
  protected config: ExternalServiceConfig;
  protected name: string;
  protected tier: 'free' | 'premium';

  constructor(name: string, tier: 'free' | 'premium', config: ExternalServiceConfig) {
    this.name = name;
    this.tier = tier;
    this.config = config;
  }

  /**
   * Get the service name
   */
  getName(): string {
    return this.name;
  }

  /**
   * Get the service tier (free/premium)
   */
  getTier(): 'free' | 'premium' {
    return this.tier;
  }

  /**
   * Check if service is enabled and configured
   */
  isEnabled(): boolean {
    return this.config.enabled && this.isConfigured();
  }

  /**
   * Check if service is properly configured
   */
  abstract isConfigured(): boolean;

  /**
   * Test service connectivity
   */
  abstract testConnection(): Promise<boolean>;

  /**
   * Get SEO analysis for a URL
   */
  abstract analyze(url: string): Promise<ExternalServiceResult>;

  /**
   * Get service specific rate limits
   */
  getRateLimit(): number {
    return this.config.rateLimit || 60; // Default 60 requests per minute
  }

  /**
   * Get service cost tier info
   */
  getCostInfo(): {
    tier: 'free' | 'premium';
    monthlyLimit?: number;
    costPerRequest?: number;
  } {
    return {
      tier: this.tier,
      monthlyLimit: this.tier === 'free' ? this.getFreeTierLimit() : undefined,
      costPerRequest: this.tier === 'premium' ? this.getPremiumCost() : 0
    };
  }

  /**
   * Get free tier monthly limit
   */
  protected abstract getFreeTierLimit(): number;

  /**
   * Get premium cost per request
   */
  protected abstract getPremiumCost(): number;

  /**
   * Handle rate limiting
   */
  protected async handleRateLimit(): Promise<void> {
    // Basic rate limiting implementation
    const delay = (60 / this.getRateLimit()) * 1000;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Handle API errors with retries
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.retries || 3
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }

  /**
   * Create error result
   */
  protected createErrorResult(error: string): ExternalServiceResult {
    return {
      provider: this.name,
      metrics: {
        overall: 0,
        technical: 0,
        content: 0,
        performance: 0,
        accessibility: 0,
        seo: 0
      },
      issues: [{
        category: 'Service Error',
        severity: 'critical',
        title: `${this.name} Analysis Failed`,
        description: error,
        recommendation: `Check ${this.name} configuration and API status`,
        impact: 'high'
      }],
      timestamp: new Date().toISOString(),
      success: false,
      error
    };
  }
} 