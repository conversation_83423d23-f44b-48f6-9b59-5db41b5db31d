import React, { Suspense } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>Left, Loader2, FileText, Info, X } from "lucide-react";
import { Button } from "../../../../base/components/ui/button";
import { Stepper } from "../components/stepper";
import {
  DocumentFormProvider,
  useDocumentForm,
} from "../contexts/DocumentFormContext";
import Layout from "../components/layout";

// Import our step components
import ClientInfoStep from "../components/ClientInfoStep";
import TemplateSelectionStep from "../components/TemplateSelectionStep";
import RequirementsStep from "../components/RequirementsStep";
import DocumentOverviewStep from "../components/DocumentOverviewStep";

// Main document content component that manages the steps
function DocumentCreator() {
  const {
    currentStep,
    isLoading,
    referenceDocument,
    isGenerating,
    generationStatus,
    generationProgress,
    eventSourceRef,
    handleBack,
    handleNext,
    handleRemoveReference,
    setIsGenerating,
  } = useDocumentForm();

  // Function to render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <ClientInfoStep onNext={handleNext} onBack={handleBack} />;
      case 2:
        return (
          <TemplateSelectionStep onNext={handleNext} onBack={handleBack} />
        );
      case 3:
        return <RequirementsStep onNext={handleNext} onBack={handleBack} />;
      case 4:
        return <DocumentOverviewStep onNext={handleNext} onBack={handleBack} />;
      default:
        return null;
    }
  };

  return (
    <Layout>
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link
            to="/"
            className="flex items-center text-muted-foreground hover:text-foreground mb-4"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Dashboard
          </Link>
          <h1 className="text-3xl font-bold">Create New Scoping Document</h1>
          <p className="text-muted-foreground mt-1">
            Follow the steps to generate your AI-powered scoping document
          </p>
        </div>

        {referenceDocument && (
          <div className="mb-6">
            <div className="bg-primary/10 rounded-lg p-4 flex items-start justify-between">
              <div>
                <div className="flex items-center">
                  <FileText size={18} className="text-primary mr-2" />
                  <h3 className="font-medium">Using Reference Document</h3>
                </div>
                <p className="text-sm mt-1">
                  <span className="font-medium">{referenceDocument.title}</span>{" "}
                  - The AI will use this document's structure and format as a
                  reference.
                </p>
                <p className="text-xs text-primary mt-2 flex items-center">
                  <Info size={12} className="mr-1" />
                  Using "Custom" template based on this reference document
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemoveReference}
                className="text-muted-foreground"
              >
                <X size={16} />
                <span className="sr-only">Remove reference</span>
              </Button>
            </div>
          </div>
        )}

        {isLoading && (
          <div className="mb-6">
            <div className="bg-muted rounded-lg p-6 flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-3" />
              <p className="text-center font-medium">
                Loading reference document...
              </p>
              <p className="text-center text-sm text-muted-foreground mt-1">
                This may take a moment while we fetch the document content.
              </p>
            </div>
          </div>
        )}

        <Stepper
          currentStep={currentStep}
          steps={[
            { label: "Client Info", description: "Project details" },
            { label: "Template", description: "Document structure" },
            {
              label: "Requirements & Resources",
              description: "Project specs & files",
            },
            { label: "Document Overview", description: "Review & Generate" },
          ]}
        />

        <div className="mt-8">{renderStep()}</div>

        {isGenerating && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 max-w-lg w-full">
              <div className="flex items-center mb-4">
                <h3 className="text-xl font-semibold">Generating Document</h3>
              </div>

              <div className="mb-4">
                <p className="mb-2">{generationStatus}</p>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  This may take a few minutes. We're using AI to create a
                  comprehensive document based on your input.
                </p>
              </div>

              <div className="text-center">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // Close the EventSource if it exists
                    if (eventSourceRef.current) {
                      eventSourceRef.current.close();
                    }
                    setIsGenerating(false);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
      </main>
    </Layout>
  );
}

// Default export wrapped in Suspense and provider
export default function NewDocument() {
  return (
    <Suspense
      fallback={
        <div className="flex flex-col items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">
            Loading document creator...
          </p>
        </div>
      }
    >
      <DocumentFormProvider>
        <DocumentCreator />
      </DocumentFormProvider>
    </Suspense>
  );
}
