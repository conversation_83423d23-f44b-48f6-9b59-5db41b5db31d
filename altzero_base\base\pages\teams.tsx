import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "../components/ui/button";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "../components/ui/tabs";
import { CreateTeamDialog } from "../components/team/CreateTeamDialog";
import { TeamCard } from "../components/team/TeamCard";
import {
  getUserGroups,
  getUserPendingGroupInvitations,
} from "../services/teamService";
import { Group, GroupInvitation } from "../types/team";
import { MemberRole } from "../types/organization";
import { toast } from "../hooks/use-toast";
import { InvitationsList } from "../components/invitation/InvitationsList";

export default function TeamsPage() {
  const [teams, setTeams] = useState<Group[]>([]);
  const [memberRoles, setMemberRoles] = useState<Record<string, MemberRole>>(
    {}
  );
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("teams");

  useEffect(() => {
    fetchTeamsAndInvitations();
  }, []);

  const fetchTeamsAndInvitations = async () => {
    setIsLoading(true);
    try {
      // Fetch teams
      const { groups, membership } = await getUserGroups();
      setTeams(groups);

      // Map roles to teams for easy lookup
      const roleMap: Record<string, MemberRole> = {};
      membership.forEach((member) => {
        roleMap[member.group_id] = member.role as MemberRole;
      });
      setMemberRoles(roleMap);

      // Fetch pending invitations
      const invites = await getUserPendingGroupInvitations();
      setInvitations(invites);
    } catch (error) {
      console.error("Error fetching teams and invitations:", error);
      toast({
        title: "Error",
        description: "Failed to load teams. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTeamCreated = () => {
    fetchTeamsAndInvitations();
  };

  const handleInvitationAccepted = () => {
    fetchTeamsAndInvitations();
  };

  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Teams</h1>
        <CreateTeamDialog onSuccess={handleTeamCreated} />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="teams">My Teams</TabsTrigger>
          <TabsTrigger value="invitations">
            Invitations
            {invitations.length > 0 && (
              <span className="ml-2 px-2 py-0.5 text-xs bg-primary text-primary-foreground rounded-full">
                {invitations.length}
              </span>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="teams">
          {isLoading ? (
            <div className="flex justify-center p-10">
              <div className="animate-spin w-8 h-8 border-2 border-primary rounded-full border-t-transparent"></div>
            </div>
          ) : teams.length === 0 ? (
            <div className="text-center p-10 bg-muted/40 rounded-lg">
              <h3 className="text-lg font-medium mb-2">No teams yet</h3>
              <p className="text-muted-foreground mb-4">
                Create a team to collaborate with specific members
              </p>
              <CreateTeamDialog
                onSuccess={handleTeamCreated}
                trigger={<Button>Create Your First Team</Button>}
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {teams.map((team) => (
                <TeamCard
                  key={team.id}
                  team={team}
                  role={memberRoles[team.id] || "member"}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="invitations">
          <InvitationsList
            groupInvitations={invitations}
            onAcceptGroup={handleInvitationAccepted}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
