import React, { useMemo, useCallback } from "react";
import { Button } from "@base/components/ui/button";
import {
  GeneratedDocumentData,
  PageData,
  DocumentTheme,
} from "../types/documentTypes";
import {
  renderBlocksToHTML,
  convertContentToBlocks,
} from "../utils/documentUtils";
import { themePresets } from "../utils/themeUtils";

interface PagedDocumentPreviewProps {
  document: GeneratedDocumentData;
  pages: PageData[];
  currentPage: number;
  setCurrentPage: (page: number) => void;
  isEditMode?: boolean;
  editedContent?: { [sectionId: string]: string };
  onEditContent?: (sectionId: string, content: string) => void;
}

export const PagedDocumentPreview: React.FC<PagedDocumentPreviewProps> = ({
  document,
  pages,
  currentPage,
  setCurrentPage,
  isEditMode = false,
  editedContent = {},
  onEditContent = (_sectionId: string, _content: string) => {},
}) => {
  // Use useMemo for theme and showTitlePage to prevent recalculations on every render
  const theme = useMemo(
    () => document.theme || themePresets.professional,
    [document.theme]
  );
  const showTitlePage = useMemo(
    () => theme?.showCoverPage !== false,
    [theme?.showCoverPage]
  );

  // Use useMemo for derived values to prevent recalculations
  const totalPageCount = useMemo(
    () => (showTitlePage ? pages.length + 1 : pages.length),
    [showTitlePage, pages.length]
  );

  const currentPageNumber = currentPage + 1;

  // Extract contentPageIndex calculation to avoid recalculations
  const contentPageIndex = useMemo(
    () => (showTitlePage ? currentPage - 1 : currentPage),
    [showTitlePage, currentPage]
  );

  // More efficient validation check
  const hasValidContentForCurrentPage = useMemo(
    () =>
      showTitlePage
        ? currentPage > 0 && contentPageIndex < pages.length
        : contentPageIndex >= 0 && contentPageIndex < pages.length,
    [showTitlePage, currentPage, contentPageIndex, pages.length]
  );

  // Fixed navigation functions
  const goToNextPage = useCallback(() => {
    // Calculate the maximum page index
    const maxPageIndex = showTitlePage ? pages.length : pages.length - 1;

    if (currentPage < maxPageIndex) {
      setCurrentPage(currentPage + 1);
    }
  }, [currentPage, pages.length, setCurrentPage, showTitlePage]);

  const goToPrevPage = useCallback(() => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage, setCurrentPage]);

  if (pages.length === 0) {
    return (
      <div className="text-center py-10 text-gray-500">
        No content available to preview
      </div>
    );
  }

  // Render title page - no need for useMemo as it's only rendered when needed
  const renderTitlePage = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center px-10">
        {theme?.logo ? (
          <div className="mb-10 max-w-[300px]">
            <img
              src={theme.logo}
              alt="Company Logo"
              className="max-h-32 w-auto object-contain mx-auto"
              onError={(e) => {
                console.error("Logo failed to load in title page");
                const imgElement = e.target as HTMLImageElement;
                imgElement.src =
                  "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWltYWdlIj48cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIvPjxjaXJjbGUgY3g9IjguNSIgY3k9IjguNSIgcj0iMS41Ii8+PHBvbHlsaW5lIHBvaW50cz0iMjEgMTUgMTYgMTAgNSAyMSIvPjwvc3ZnPg==";
              }}
            />
          </div>
        ) : (
          <div className="mb-10">
            <div className="h-32"></div>
          </div>
        )}

        <h1
          className="text-4xl font-bold mb-6"
          style={{ color: theme?.headingColor || "#000000" }}
        >
          {document.title || "Document Title"}
        </h1>

        {document.client && (
          <p
            className="text-xl mb-6"
            style={{ color: theme?.textColor || "#000000" }}
          >
            Prepared for: {document.client}
          </p>
        )}

        <p
          className="text-sm mt-10"
          style={{ color: theme?.textColor || "#000000" }}
        >
          {new Date().toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </p>
      </div>
    );
  };

  // Render header based on settings
  const renderHeader = () => {
    if (!theme?.header?.enabled) return null;

    let headerContent = "";

    switch (theme.header.type) {
      case "simple":
        headerContent = document.title || "Document Title";
        break;
      case "logo":
        headerContent = document.title || "Document Title";
        break;
      case "custom":
        headerContent = theme.header.text || "";
        break;
    }

    return (
      <div
        className="py-2 px-4 border-b text-sm flex justify-between items-center"
        style={{
          color: theme?.textColor || "#000000",
          borderColor: theme?.accentColor || "#cccccc",
        }}
      >
        <div className="flex items-center gap-2">
          {/* Show logo in all header types if available */}
          {theme?.logo && (
            <img
              src={theme.logo}
              alt="Logo"
              className="h-6 object-contain"
              onError={(e) => {
                console.warn("Error loading logo image:", e);
                e.currentTarget.style.display = "none";
              }}
            />
          )}
          <div>{headerContent}</div>
        </div>
        {theme?.header?.includePageNumbers && (
          <div>
            Page {currentPageNumber} of {totalPageCount}
          </div>
        )}
      </div>
    );
  };

  // Render footer based on settings
  const renderFooter = () => {
    if (!theme?.footer?.enabled) return null;

    let footerContent = "";

    switch (theme.footer.type) {
      case "simple":
        footerContent = "ScopingAI, Inc.";
        break;
      case "contact":
        footerContent =
          "ScopingAI, Inc. | <EMAIL> | (555) 123-4567";
        break;
      case "custom":
        footerContent = theme.footer.text || "";
        break;
    }

    return (
      <div
        className="py-2 px-4 border-t text-sm flex justify-between items-center mt-auto"
        style={{
          color: theme?.textColor || "#000000",
          borderColor: theme?.accentColor || "#cccccc",
        }}
      >
        <div className="flex items-center gap-2">
          {theme?.logo && (
            <img
              src={theme.logo}
              alt="Logo"
              className="h-5 object-contain"
              onError={(e) => {
                console.warn("Error loading logo image:", e);
                e.currentTarget.style.display = "none";
              }}
            />
          )}
          <div>{footerContent}</div>
        </div>
        {theme?.footer?.includePageNumbers && (
          <div>
            Page {currentPageNumber} of {totalPageCount}
          </div>
        )}
      </div>
    );
  };

  // Optimized renderPageContent function
  const renderPageContent = () => {
    // If we're showing title page and we're on page 0, return the title page
    if (showTitlePage && currentPage === 0) {
      return renderTitlePage();
    }

    // Otherwise, show regular content page
    if (hasValidContentForCurrentPage) {
      const pageContent = pages[contentPageIndex];
      if (
        !pageContent ||
        !pageContent.content ||
        pageContent.content.length === 0
      ) {
        return (
          <div className="p-8 flex-grow flex items-center justify-center">
            <p className="text-gray-500">No content available for this page</p>
          </div>
        );
      }

      const section = pageContent.content[0];

      if (!section) {
        return (
          <div className="p-8 flex-grow flex items-center justify-center">
            <p className="text-gray-500">No section content available</p>
          </div>
        );
      }

      // Get either edited content or original content
      const sectionContent = editedContent[section.id] || section.content;

      // Ensure section has blocks, if not create them from content
      if (!section.blocks || !Array.isArray(section.blocks)) {
        section.blocks = convertContentToBlocks(section.content || "");
      }

      return (
        <div className="p-8 flex-grow">
          {!showTitlePage && currentPage === 0 && (
            <>
              <div
                className="text-3xl font-bold mb-6"
                style={{ color: theme?.headingColor || "#000000" }}
              >
                {document.title || "Document Title"}
              </div>
              {document.client && (
                <p
                  style={{ color: theme?.textColor || "#000000" }}
                  className="mb-6"
                >
                  Client: {document.client}
                </p>
              )}
            </>
          )}

          {section && (
            <div className="mb-8">
              <h2
                className="text-2xl font-semibold mb-4 pb-2 border-b"
                style={{
                  color: theme?.subheadingColor || "#000000",
                  borderColor: theme?.accentColor || "#cccccc",
                }}
              >
                {section.title}
              </h2>

              {isEditMode ? (
                <textarea
                  className="w-full min-h-[400px] p-4 border rounded text-md font-light"
                  value={sectionContent}
                  onChange={(e) => onEditContent(section.id, e.target.value)}
                  style={{ color: theme?.textColor || "#000000" }}
                />
              ) : (
                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: renderBlocksToHTML(section.blocks),
                  }}
                />
              )}
            </div>
          )}
        </div>
      );
    }

    // Fallback if page is out of range
    return (
      <div className="p-8 flex-grow flex items-center justify-center">
        <p className="text-gray-500">No content available for this page</p>
      </div>
    );
  };

  // Main component return
  return (
    <div className="flex">
      {/* Left sidebar with page thumbnails */}
      <div className="w-[180px] pr-4 border-r overflow-y-auto max-h-[800px]">
        <div className="flex flex-col gap-3 p-2">
          {/* Add title page thumbnail if logo exists */}
          {showTitlePage && (
            <div
              onClick={() => setCurrentPage(0)}
              className={`cursor-pointer transition-all ${
                currentPage === 0
                  ? "border-2 border-blue-500"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="bg-white aspect-[1/1.4] p-2 flex flex-col text-xs shadow-sm">
                <div className="text-center font-semibold mb-1 truncate">
                  Cover Page
                </div>
                <div className="overflow-hidden flex flex-col items-center justify-center">
                  {/* Mini logo preview */}
                  <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center mb-1">
                    {theme?.logo ? (
                      <img
                        src={theme.logo}
                        alt="Logo"
                        className="max-w-[80%] max-h-[80%] object-contain"
                        onError={(e) => {
                          console.warn("Error loading logo thumbnail");
                          e.currentTarget.style.display = "none";
                          e.currentTarget.parentElement
                            ?.querySelector(".text-gray-400")
                            ?.classList.remove("hidden");
                        }}
                      />
                    ) : (
                      <div className="text-[6px] text-gray-400">Title</div>
                    )}
                    <div className="text-[6px] text-gray-400 hidden">Title</div>
                  </div>
                  <div className="h-2 bg-gray-200 rounded w-2/3 mx-auto mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2 mb-1"></div>
                </div>
              </div>
              <div className="text-center text-xs py-1 bg-gray-50">Title</div>
            </div>
          )}

          {/* Regular page thumbnails */}
          {pages.map((page, index) => (
            <div
              key={page.id}
              onClick={() => setCurrentPage(showTitlePage ? index + 1 : index)}
              className={`cursor-pointer transition-all ${
                currentPage === (showTitlePage ? index + 1 : index)
                  ? "border-2 border-blue-500"
                  : "border border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="bg-white aspect-[1/1.4] p-2 flex flex-col text-xs shadow-sm">
                <div className="text-center font-semibold mb-1 truncate">
                  {page.content[0]?.title || `Page ${index + 1}`}
                </div>
                <div className="overflow-hidden text-[7px] text-gray-500 text-center">
                  {/* Simple representation of content */}
                  <div className="h-2 bg-gray-200 rounded w-2/3 mx-auto mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-full mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-5/6 mb-1"></div>
                </div>
              </div>
              <div className="text-center text-xs py-1 bg-gray-50">
                {showTitlePage ? index + 1 : index}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Document content */}
        <div className="flex-1 bg-gray-100 p-4 overflow-y-auto">
          <div
            className="mx-auto bg-white shadow-md rounded-md max-w-[794px] min-h-[1100px] flex flex-col"
            style={{
              aspectRatio: "1/1.4",
              backgroundColor: theme?.backgroundColor || "#ffffff",
            }}
          >
            {/* Document Header - Don't show on title page */}
            {!(showTitlePage && currentPage === 0) && renderHeader()}

            {/* Document Content - Use our new renderPageContent function */}
            {renderPageContent()}

            {/* Document Footer - Don't show on title page */}
            {!(showTitlePage && currentPage === 0) && renderFooter()}
          </div>
        </div>

        {/* Page navigation controls */}
        <div className="flex justify-between items-center p-2 border-t">
          <div className="text-sm text-gray-600">
            Page {currentPageNumber} of {totalPageCount}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPrevPage}
              disabled={currentPage === 0}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={
                currentPage >= (showTitlePage ? pages.length : pages.length - 1)
              }
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
