/**
 * Application-wide constants
 */

// Theme settings
export const STORAGE_THEME_KEY = 'dashboard-theme';
export const DEFAULT_THEME = 'system';

// Authentication settings
export const AUTH_TOKEN_KEY = 'auth-token';
export const AUTH_REFRESH_KEY = 'auth-refresh-token';
export const AUTH_ROUTES = {
  LOGIN: '/login',
  SIGNUP: '/signup',
  FORGOT_PASSWORD: '/forgot-password',
  UPDATE_PASSWORD: '/update-password',
  PROFILE: '/profile',
  HOME: '/home'
};

// API settings
export const API_TIMEOUT = 30000; // 30 seconds

// UI settings
export const TOAST_DURATION = 5000; // 5 seconds
export const ANIMATION_DURATION = 300; // 300ms

// Pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const PAGE_SIZE_OPTIONS = [5, 10, 20, 50, 100]; 