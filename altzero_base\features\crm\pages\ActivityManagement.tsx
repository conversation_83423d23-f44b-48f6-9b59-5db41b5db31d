import React, { useState, useEffect } from "react";
import {
  Plus,
  Activity,
  Phone,
  Mail,
  Calendar,
  FileText,
  RefreshCw,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  Filter,
  Search,
  Clock,
  User,
  Building,
  MessageSquare,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { crmService } from "../services/crmService";
import {
  Activity as ActivityType,
  ActivityFilters,
  PaginatedResponse,
  ACTIVITY_TYPES,
} from "../types";
import ActivityForm from "../components/ActivityForm";
import Modal from "../components/Modal";
import CRMLayout from "../components/CRMLayout";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../../base/components/ui/card";
import { Button } from "../../../base/components/ui/button";
import { Badge } from "../../../base/components/ui/badge";
import { Input } from "../../../base/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "../../../base/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../base/components/ui/select";
import { Avatar, AvatarFallback } from "../../../base/components/ui/avatar";

const ActivityManagement: React.FC = () => {
  const [activities, setActivities] = useState<ActivityType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedType, setSelectedType] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedActivities, setSelectedActivities] = useState<string[]>([]);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<ActivityType | null>(
    null
  );
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Optimized for large datasets
  const pageSize = 50;

  useEffect(() => {
    loadActivities();
  }, [currentPage, selectedType]);

  // Filter activities based on search term
  const filteredActivities = activities.filter(
    (activity) =>
      searchTerm === "" ||
      activity.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.crm_contacts?.full_name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      activity.crm_opportunities?.title
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase())
  );

  const loadActivities = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: ActivityFilters = {
        page: currentPage,
        limit: pageSize,
        type: selectedType && selectedType !== "all" ? selectedType : undefined,
      };

      const response: PaginatedResponse<ActivityType> =
        await crmService.getActivities(filters);
      setActivities(response.data);
      setTotal(response.total);
    } catch (err) {
      console.error("Error loading activities:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load activities"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCreateActivity = async (activityData: any) => {
    try {
      await crmService.createActivity(activityData);
      setShowCreateModal(false);
      loadActivities();
    } catch (err) {
      console.error("Error creating activity:", err);
      throw err;
    }
  };

  const handleEditActivity = async (activityData: any) => {
    if (!selectedActivity?.id) return;

    try {
      await crmService.updateActivity(selectedActivity.id, activityData);
      setShowEditModal(false);
      setSelectedActivity(null);
      loadActivities();
    } catch (err) {
      console.error("Error updating activity:", err);
      throw err;
    }
  };

  const handleDeleteActivity = async () => {
    if (!selectedActivity?.id) return;

    try {
      await crmService.deleteActivity(selectedActivity.id);
      setShowDeleteModal(false);
      setSelectedActivity(null);
      loadActivities();
    } catch (err) {
      console.error("Error deleting activity:", err);
      setError(
        err instanceof Error ? err.message : "Failed to delete activity"
      );
    }
  };

  const openEditModal = (activity: ActivityType) => {
    setSelectedActivity(activity);
    setShowEditModal(true);
  };

  const openDeleteModal = (activity: ActivityType) => {
    setSelectedActivity(activity);
    setShowDeleteModal(true);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "call":
        return <Phone className="w-4 h-4" />;
      case "email":
        return <Mail className="w-4 h-4" />;
      case "meeting":
        return <Calendar className="w-4 h-4" />;
      case "note":
        return <FileText className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case "call":
        return "bg-blue-100 text-blue-800";
      case "email":
        return "bg-green-100 text-green-800";
      case "meeting":
        return "bg-purple-100 text-purple-800";
      case "note":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const totalPages = Math.ceil(total / pageSize);

  return (
    <CRMLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Activity Management
              </h1>
              <p className="text-muted-foreground mt-1">
                Track all customer interactions and communications
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={loadActivities}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <MoreHorizontal className="h-4 w-4" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem className="gap-2">
                    <Upload className="h-4 w-4" />
                    Import Activities
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Download className="h-4 w-4" />
                    Export Activities
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="gap-2">
                    <Filter className="h-4 w-4" />
                    Advanced Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Log Activity
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search activities..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="sm:w-48">
                  <Select
                    value={selectedType}
                    onValueChange={(value) => {
                      setSelectedType(value);
                      setCurrentPage(1);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {ACTIVITY_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {ACTIVITY_TYPES.map((type) => {
              const count = filteredActivities.filter(
                (a) => a.type === type.value
              ).length;
              return (
                <Card key={type.value}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {type.label}s
                    </CardTitle>
                    <div className="h-4 w-4 text-muted-foreground">
                      {getActivityIcon(type.value)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{count}</div>
                    <p className="text-xs text-muted-foreground">
                      {count === 1 ? "activity" : "activities"} logged
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Error Display */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                    <AlertCircle className="w-6 h-6 text-destructive" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-destructive">
                      Error Loading Activities
                    </h3>
                    <p className="text-muted-foreground">{error}</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={loadActivities}
                    className="gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Retry
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Activities List */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Recent Activities</span>
                <Badge variant="secondary">
                  {searchTerm || (selectedType && selectedType !== "all")
                    ? `${filteredActivities.length} filtered`
                    : `${total} total`}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {loading ? (
                <div className="p-6">
                  <div className="animate-pulse space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex space-x-4">
                        <div className="h-10 w-10 bg-muted rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-muted rounded w-3/4"></div>
                          <div className="h-3 bg-muted rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : filteredActivities.length === 0 ? (
                <div className="p-12 text-center">
                  <Activity className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No activities found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm
                      ? "No activities match your search criteria."
                      : "Start logging your customer interactions."}
                  </p>
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Log First Activity
                  </Button>
                </div>
              ) : (
                <div className="divide-y divide-border">
                  {filteredActivities.map((activity) => (
                    <div
                      key={activity.id}
                      className="p-6 hover:bg-muted/50 transition-colors group"
                    >
                      <div className="flex items-start space-x-4">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-primary/10 text-primary">
                            {getActivityIcon(activity.type)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="capitalize">
                                {activity.type}
                              </Badge>
                              {activity.crm_contacts && (
                                <span className="text-sm text-muted-foreground">
                                  with {activity.crm_contacts.full_name}
                                </span>
                              )}
                              {activity.crm_opportunities && (
                                <span className="text-sm text-muted-foreground">
                                  for {activity.crm_opportunities.title}
                                </span>
                              )}
                            </div>
                            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => openEditModal(activity)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => openEditModal(activity)}
                                    className="gap-2"
                                  >
                                    <Eye className="h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => openEditModal(activity)}
                                    className="gap-2"
                                  >
                                    <Edit className="h-4 w-4" />
                                    Edit Activity
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => openDeleteModal(activity)}
                                    className="gap-2 text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    Delete Activity
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                          <div className="mt-1 flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {activity.created_at &&
                                new Date(activity.created_at).toLocaleString()}
                            </div>
                            {activity.scheduled_at && (
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                Scheduled:{" "}
                                {new Date(
                                  activity.scheduled_at
                                ).toLocaleString()}
                              </div>
                            )}
                          </div>
                          {activity.content && (
                            <p className="mt-2 text-sm text-foreground">
                              {activity.content}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="px-4 py-3 border-t bg-muted/20">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                  <div className="text-sm text-muted-foreground">
                    Showing {(currentPage - 1) * pageSize + 1} to{" "}
                    {Math.min(currentPage * pageSize, total)} of{" "}
                    {total.toLocaleString()} activities
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="px-2"
                    >
                      First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Badge variant="secondary" className="px-3 py-1">
                      {currentPage} of {totalPages}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="px-2"
                    >
                      Last
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Create Activity Modal */}
          <Modal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            title="Log New Activity"
          >
            <ActivityForm
              onSubmit={handleCreateActivity}
              onCancel={() => setShowCreateModal(false)}
            />
          </Modal>

          {/* Edit Activity Modal */}
          <Modal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            title="Edit Activity"
          >
            {selectedActivity && (
              <ActivityForm
                activity={selectedActivity}
                onSubmit={handleEditActivity}
                onCancel={() => setShowEditModal(false)}
              />
            )}
          </Modal>

          {/* Delete Confirmation Modal */}
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Activity"
          >
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                  <Trash2 className="w-6 h-6 text-destructive" />
                </div>
                <div>
                  <h3 className="font-semibold">Delete Activity</h3>
                  <p className="text-muted-foreground">
                    Are you sure you want to delete this activity? This action
                    cannot be undone.
                  </p>
                </div>
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteActivity}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Activity
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </CRMLayout>
  );
};

export default ActivityManagement;
