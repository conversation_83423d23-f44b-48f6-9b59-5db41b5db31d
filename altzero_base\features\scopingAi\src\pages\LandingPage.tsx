import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import {
  ArrowRight,
  Check,
  FileText,
  Sparkles,
  Users,
  BarChart,
  FileOutput,
  Layers,
  Repeat,
  Star,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { But<PERSON> } from "@base/components/ui/button";
import { Badge } from "@base/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@base/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@base/components/ui/tabs";
import { WaitlistModal } from "../components/ui/waitlist-modal";
import { ThemeToggle } from "@base/components/theme-toggle";

export default function LandingPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "yearly">(
    "monthly"
  );
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [isWaitlistModalOpen, setIsWaitlistModalOpen] = useState(false);

  useEffect(() => {
    // Simulate any initial data loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const features = [
    {
      icon: <Sparkles className="h-10 w-10 text-primary" />,
      title: "AI-Powered Generation",
      description:
        "Create comprehensive scoping documents in minutes with advanced AI assistance",
    },
    {
      icon: <Layers className="h-10 w-10 text-primary" />,
      title: "Customizable Templates",
      description:
        "Choose from a variety of industry-specific templates or create your own",
    },
    {
      icon: <FileOutput className="h-10 w-10 text-primary" />,
      title: "Export Options",
      description:
        "Export your documents in multiple formats including PDF, DOCX, and HTML",
    },
    {
      icon: <Repeat className="h-10 w-10 text-primary" />,
      title: "Reference Documents",
      description:
        "Use existing documents as references to maintain consistency across projects",
    },
    {
      icon: <Users className="h-10 w-10 text-primary" />,
      title: "Collaboration",
      description:
        "Work together with your team in real-time with collaborative editing",
    },
    {
      icon: <BarChart className="h-10 w-10 text-primary" />,
      title: "Analytics",
      description:
        "Track document usage, completion rates, and team productivity",
    },
  ];

  const pricingTiers = {
    monthly: [
      {
        name: "Starter",
        price: "$19",
        description: "Perfect for freelancers and individuals",
        features: [
          "5 AI-generated documents per month",
          "Basic templates",
          "PDF & DOCX export",
          "Email support",
          "7-day revision history",
        ],
        limitations: [
          "No team collaboration",
          "Limited template customization",
          "No reference documents",
        ],
        cta: "Start Free Trial",
        popular: false,
      },
      {
        name: "Professional",
        price: "$49",
        description: "Ideal for small teams and growing businesses",
        features: [
          "25 AI-generated documents per month",
          "All templates included",
          "All export formats",
          "Team collaboration (up to 5 users)",
          "Reference document library",
          "Custom AI prompts",
          "30-day revision history",
          "Priority support",
        ],
        limitations: [],
        cta: "Start Free Trial",
        popular: true,
      },
      {
        name: "Enterprise",
        price: "$99",
        description: "For organizations with advanced needs",
        features: [
          "Unlimited AI-generated documents",
          "Custom template creation",
          "All export formats",
          "Unlimited team collaboration",
          "Advanced document analytics",
          "API access",
          "Unlimited revision history",
          "Dedicated account manager",
          "SSO & advanced security",
        ],
        limitations: [],
        cta: "Contact Sales",
        popular: false,
      },
    ],
    yearly: [
      {
        name: "Starter",
        price: "$190",
        period: "$15.83/mo, billed annually",
        description: "Perfect for freelancers and individuals",
        features: [
          "5 AI-generated documents per month",
          "Basic templates",
          "PDF & DOCX export",
          "Email support",
          "7-day revision history",
        ],
        limitations: [
          "No team collaboration",
          "Limited template customization",
          "No reference documents",
        ],
        cta: "Start Free Trial",
        popular: false,
      },
      {
        name: "Professional",
        price: "$490",
        period: "$40.83/mo, billed annually",
        description: "Ideal for small teams and growing businesses",
        features: [
          "25 AI-generated documents per month",
          "All templates included",
          "All export formats",
          "Team collaboration (up to 5 users)",
          "Reference document library",
          "Custom AI prompts",
          "30-day revision history",
          "Priority support",
        ],
        limitations: [],
        cta: "Start Free Trial",
        popular: true,
      },
      {
        name: "Enterprise",
        price: "$990",
        period: "$82.50/mo, billed annually",
        description: "For organizations with advanced needs",
        features: [
          "Unlimited AI-generated documents",
          "Custom template creation",
          "All export formats",
          "Unlimited team collaboration",
          "Advanced document analytics",
          "API access",
          "Unlimited revision history",
          "Dedicated account manager",
          "SSO & advanced security",
        ],
        limitations: [],
        cta: "Contact Sales",
        popular: false,
      },
    ],
  };

  const testimonials = [
    {
      quote:
        "This tool has completely transformed our project scoping process. What used to take days now takes hours, and the quality is consistently high.",
      author: "Sarah Johnson",
      title: "Project Manager, TechCorp",
      avatar: "/placeholder.svg?height=60&width=60",
    },
    {
      quote:
        "The AI-powered suggestions are incredibly accurate. It's like having an experienced project manager guiding you through the scoping process.",
      author: "Michael Chen",
      title: "Freelance Developer",
      avatar: "/placeholder.svg?height=60&width=60",
    },
    {
      quote:
        "We've standardized all our project scopes using this tool. The consistency across teams has improved client communication and reduced scope creep by 40%.",
      author: "Emily Rodriguez",
      title: "Director of Operations, DesignHub",
      avatar: "/placeholder.svg?height=60&width=60",
    },
  ];

  const faqs = [
    {
      question: "How does the AI generate scoping documents?",
      answer:
        "Our AI analyzes your inputs about the project, client, requirements, and resources. It then uses this information along with best practices from thousands of successful project scopes to generate a comprehensive, well-structured document. You can further guide the AI with specific prompts and reference documents.",
    },
    {
      question: "Can I customize the templates?",
      answer:
        "Yes! All plans allow some level of template customization. The Starter plan includes basic template selection, while Professional and Enterprise plans offer advanced customization options, including creating entirely custom templates and sections.",
    },
    {
      question: "How accurate are the AI-generated documents?",
      answer:
        "The AI generates high-quality initial drafts based on your inputs. While the documents are comprehensive, we recommend reviewing and adjusting them to ensure they perfectly match your specific project needs. The more information you provide, the more accurate the output will be.",
    },
    {
      question: "What's included in the free trial?",
      answer:
        "Our 14-day free trial gives you access to all features of the Professional plan, including 5 AI-generated documents, all templates, export options, and collaboration features. No credit card is required to start your trial.",
    },
    {
      question: "Can I upgrade or downgrade my plan later?",
      answer:
        "You can upgrade your plan at any time, and the new features will be immediately available. If you need to downgrade, the change will take effect at the start of your next billing cycle.",
    },
    {
      question:
        "Do you offer discounts for nonprofits or educational institutions?",
      answer:
        "Yes, we offer special pricing for nonprofits, educational institutions, and startups. Please contact our sales team for more information about our discount programs.",
    },
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Top navigation with theme toggle */}
      <div className="fixed top-0 left-0 right-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between px-4">
          <div className="flex items-center space-x-2">
            <svg
              className="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              />
            </svg>
            <span className="font-bold text-lg">ScopingAI</span>
          </div>
          <div className="flex items-center gap-4">
            <ThemeToggle />
            <Button
              variant="outline"
              onClick={() => setIsWaitlistModalOpen(true)}
            >
              Join Waitlist
            </Button>
          </div>
        </div>
      </div>

      <main className="flex-1">
        {/* Hero Section */}
        <section className="pt-28 md:pt-20 pb-10 md:pt-40 md:pb-10">
          <div className="container mx-auto px-4 text-center">
            <Badge className="mb-4 px-3 py-1 text-xs md:text-sm">
              AI-Powered Document Generation
            </Badge>
            <h1 className="text-2xl md:text-6xl font-bold mb-2 md:mb-6 md:!leading-[72px]">
              Create Perfect Scoping Documents{" "}
              <br className="hidden md:block" /> in{" "}
              <span className="text-primary">Minutes</span>, Not Days
            </h1>
            <p className="text-sm md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Our AI-powered platform helps you create comprehensive,
              professional scoping documents for any project. Save time, reduce
              scope creep, and impress your clients.
            </p>
            <div className="mb-12">
              {/* <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12"> */}
              {/* <Link to="/signup">
                <Button size="lg" className="gap-2">
                  Start Free Trial <ArrowRight size={16} />
                </Button>
              </Link> */}
              <Button
                // variant="outline"
                size="lg"
                onClick={() => setIsWaitlistModalOpen(true)}
              >
                Join Waitlist
              </Button>
              {/* <Link to="/dashboard">
                <Button variant="secondary" size="lg" className="gap-2">
                  Go to App
                </Button>
              </Link> */}
            </div>
            <div className="relative mx-auto max-w-5xl rounded-xl border shadow-lg overflow-hidden">
              <img
                src="/scopingai.png"
                alt="ScopingAI Dashboard"
                className="w-full h-auto"
              />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="md:py-20 py-10 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4">Features</Badge>
              <h2 className="text-2xl md:text-4xl font-bold mb-4">
                Everything You Need for Perfect Scoping Documents
              </h2>
              <p className="text-sm md:text-xl text-muted-foreground max-w-3xl mx-auto">
                Our platform combines AI technology with industry best practices
                to help you create comprehensive scoping documents for any
                project.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <Card
                  key={index}
                  className="border bg-card hover:shadow-md transition-shadow"
                >
                  <CardHeader>
                    <div className="mb-4">{feature.icon}</div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8">
              <Badge className="mb-4">How It Works</Badge>
              <h2 className="text-2xl md:text-4xl font-bold mb-4">
                Create Scoping Documents <br className="md:hidden" /> in 4
                Simple Steps
              </h2>
              <p className="text-sm md:text-xl text-muted-foreground max-w-3xl mx-auto">
                Our guided process makes it easy to create comprehensive scoping
                documents, even if you've never written one before.
              </p>
            </div>

            {/* <div className="flex flex-col md:flex-row items-center"> */}
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <span className="text-primary font-bold text-xl">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Enter Client Info
                </h3>
                <p className="text-muted-foreground">
                  Start by entering basic information about your client and the
                  project.
                </p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <span className="text-primary font-bold text-xl">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Define Requirements
                </h3>
                <p className="text-muted-foreground">
                  Specify project requirements, deliverables, and constraints in
                  our guided form.
                </p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <span className="text-primary font-bold text-xl">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Guide the AI</h3>
                <p className="text-muted-foreground">
                  Provide specific instructions to the AI to customize your
                  document's tone and focus.
                </p>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <span className="text-primary font-bold text-xl">4</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Review & Export</h3>
                <p className="text-muted-foreground">
                  Review the generated document, make any necessary edits, and
                  export in your preferred format.
                </p>
              </div>
            </div>

            <div className="flex-1 relative mx-auto flex justify-center items-center">
              <img
                src="/scopingai_4steps.png"
                alt="ScopingAI Document Creation Flow"
                className="h-auto w-auto mt-8 md:mt-0"
              />
            </div>
            {/* </div> */}
          </div>
        </section>

        {/* Pricing Section */}
        {/* <section id="pricing" className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4">Pricing</Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Simple, Transparent Pricing</h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Choose the plan that's right for you. All plans include a 14-day free trial.
              </p>

              <div className="flex items-center justify-center mt-8 mb-8">
                <div className="bg-muted rounded-full p-1 inline-flex">
                  <button
                    className={`px-4 py-2 rounded-full text-sm font-medium ${
                      billingPeriod === "monthly" ? "bg-background shadow-sm text-foreground" : "text-muted-foreground"
                    }`}
                    onClick={() => setBillingPeriod("monthly")}
                  >
                    Monthly
                  </button>
                  <button
                    className={`px-4 py-2 rounded-full text-sm font-medium ${
                      billingPeriod === "yearly" ? "bg-background shadow-sm text-foreground" : "text-muted-foreground"
                    }`}
                    onClick={() => setBillingPeriod("yearly")}
                  >
                    Yearly <span className="text-xs text-primary font-normal">Save 20%</span>
                  </button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {pricingTiers[billingPeriod].map((tier, index) => (
                <Card
                  key={index}
                  className={`border ${
                    tier.popular ? "border-primary shadow-lg relative" : ""
                  } hover:shadow-md transition-shadow`}
                >
                  {tier.popular && (
                    <div className="absolute top-0 right-0 transform translate-x-2 -translate-y-2">
                      <Badge className="bg-primary text-primary-foreground">Most Popular</Badge>
                    </div>
                  )}
                  <CardHeader>
                    <CardTitle className="text-2xl">{tier.name}</CardTitle>
                    <CardDescription>{tier.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <span className="text-4xl font-bold">{tier.price}</span>
                      <span className="text-muted-foreground ml-2">
                        {billingPeriod === "monthly" ? "/month" : "/year"}
                      </span>
                      {"period" in tier && tier.period && (
                        <p className="text-sm text-muted-foreground mt-1">{tier.period}</p>
                      )}
                    </div>

                    <div className="space-y-4">
                      <p className="font-medium">What's included:</p>
                      <ul className="space-y-2">
                        {tier.features.map((feature, i) => (
                          <li key={i} className="flex items-start">
                            <Check className="h-5 w-5 text-primary shrink-0 mr-2" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      {tier.limitations.length > 0 && (
                        <>
                          <p className="font-medium mt-4">Limitations:</p>
                          <ul className="space-y-2">
                            {tier.limitations.map((limitation, i) => (
                              <li key={i} className="flex items-start">
                                <span className="h-5 w-5 text-muted-foreground shrink-0 mr-2 flex items-center justify-center">
                                  —
                                </span>
                                <span className="text-sm text-muted-foreground">{limitation}</span>
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      className={`w-full ${tier.popular ? "" : "variant-outline"}`}
                      variant={tier.popular ? "default" : "outline"}
                    >
                      {tier.cta}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>

            <div className="mt-12 text-center">
              <p className="text-muted-foreground">
                Need a custom plan for your enterprise?{" "}
                <a to="#contact" className="text-primary font-medium hover:underline">
                  Contact our sales team
                </a>
              </p>
            </div>
          </div>
        </section> */}

        {/* Document Types Section */}
        <section className="md:py-20 py-10">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4">Document Types</Badge>
              <h2 className="text-2xl md:text-4xl font-bold mb-4">
                From Simple to Complex, We've Got You Covered
              </h2>
              <p className="text-sm md:text-xl text-muted-foreground max-w-3xl mx-auto">
                Whether you need a basic scope or a comprehensive project plan,
                our AI can generate the perfect document.
              </p>
            </div>

            <Tabs defaultValue="simple" className="max-w-4xl mx-auto">
              <TabsList className="grid w-full grid-cols-3 mb-8">
                <TabsTrigger value="simple">Simple Scope</TabsTrigger>
                <TabsTrigger value="standard">Standard Scope</TabsTrigger>
                <TabsTrigger value="complex">Complex Scope</TabsTrigger>
              </TabsList>
              <TabsContent value="simple" className="border rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">
                      Simple Scoping Document
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Perfect for small projects with clear deliverables and
                      timelines. Includes the essential sections to define scope
                      without overwhelming detail.
                    </p>
                    <ul className="space-y-2">
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Project Overview</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Key Deliverables</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Timeline</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Budget Summary</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Approval Section</span>
                      </li>
                    </ul>
                    {/* <div className="mt-6">
                      <Button variant="outline" className="gap-2">
                        <FileText size={16} />
                        View Sample
                      </Button>
                    </div> */}
                  </div>
                  <div className="rounded-md overflow-hidden flex justify-center items-center">
                    <img
                      src="/sample_document.jpg"
                      alt="Simple Scoping Document Example"
                      className="w-auto h-[400px]"
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="standard" className="border rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">
                      Standard Scoping Document
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Our most popular option, suitable for most projects.
                      Includes comprehensive sections to define scope,
                      requirements, and expectations.
                    </p>
                    <ul className="space-y-2">
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Project Overview & Objectives</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Detailed Deliverables</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Technical Requirements</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Timeline with Milestones</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Budget Breakdown</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Team & Resources</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Assumptions & Constraints</span>
                      </li>
                    </ul>
                    {/* <div className="mt-6">
                      <Button variant="outline" className="gap-2">
                        <FileText size={16} />
                        View Sample
                      </Button>
                    </div> */}
                  </div>
                  <div className="rounded-md overflow-hidden">
                    <img
                      src="/sample_document.jpg"
                      alt="Simple Scoping Document Example"
                      className="w-auto h-[400px]"
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="complex" className="border rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">
                      Complex Scoping Document
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      For large-scale or enterprise projects requiring detailed
                      planning and documentation. Includes advanced sections for
                      comprehensive project definition.
                    </p>
                    <ul className="space-y-2">
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Executive Summary</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Comprehensive Project Overview</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Stakeholder Analysis</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Detailed Requirements Specification</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Technical Architecture</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Risk Assessment & Mitigation</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Quality Assurance Plan</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Detailed Timeline & Dependencies</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Comprehensive Budget Analysis</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="h-5 w-5 text-primary mr-2" />
                        <span>Change Management Process</span>
                      </li>
                    </ul>
                    {/* <div className="mt-6">
                      <Button variant="outline" className="gap-2">
                        <FileText size={16} />
                        View Sample
                      </Button>
                    </div> */}
                  </div>
                  <div className="rounded-md overflow-hidden">
                    <img
                      src="/sample_document.jpg"
                      alt="Simple Scoping Document Example"
                      className="w-auto h-[400px]"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Testimonials Section */}
        {/* <section id="testimonials" className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4">Testimonials</Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Trusted by Teams of All Sizes</h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                See what our customers have to say about how ScopingAI has transformed their project scoping process.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {testimonials.map((testimonial, index) => (
                <Card key={index} className="border hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="mb-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="inline-block h-5 w-5 text-yellow-400 fill-yellow-400" />
                      ))}
                    </div>
                    <p className="text-muted-foreground mb-6 italic">"{testimonial.quote}"</p>
                    <div className="flex items-center">
                      <img
                        src={testimonial.avatar || "/placeholder.svg"}
                        alt={testimonial.author}
                        className="h-12 w-12 rounded-full mr-4"
                      />
                      <div>
                        <p className="font-medium">{testimonial.author}</p>
                        <p className="text-sm text-muted-foreground">{testimonial.title}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section> */}

        {/* FAQ Section */}
        {/* <section id="faq" className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4">FAQ</Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Find answers to common questions about our platform and pricing.
              </p>
            </div>

            <div className="max-w-3xl mx-auto">
              {faqs.map((faq, index) => (
                <div key={index} className="border-b last:border-b-0 py-6">
                  <button
                    className="flex justify-between items-center w-full text-left"
                    onClick={() => toggleFaq(index)}
                  >
                    <h3 className="text-lg font-medium">{faq.question}</h3>
                    {openFaq === index ? (
                      <ChevronUp className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-muted-foreground" />
                    )}
                  </button>
                  {openFaq === index && <p className="mt-4 text-muted-foreground">{faq.answer}</p>}
                </div>
              ))}
            </div>
          </div>
        </section> */}

        {/* CTA Section */}
        <section className="md:py-20 py-10 bg-black text-white dark:bg-muted/30 dark:text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl md:text-4xl font-bold mb-10">
              Ready to Transform Your Scoping Process?
            </h2>
            {/* <p className="text-xl opacity-90 mb-8 max-w-3xl mx-auto">
              Join thousands of teams who are creating better scoping documents in less time.
            </p> */}
            <div className="">
              {/*<div className="flex flex-col sm:flex-row gap-4 justify-center"> <Link to="/signup">
                <Button size="lg" variant="secondary" className="gap-2">
                  Start Your Free Trial <ArrowRight size={16} />
                </Button>
              </Link> */}
              <Button
                // variant="outline"
                // className="text-black"
                size="lg"
                onClick={() => setIsWaitlistModalOpen(true)}
              >
                Join Waitlist
              </Button>
              {/* <a to="#pricing">
                <Button
                  size="lg"
                  variant="secondary"
                  className="gap-2"
                >
                  View Pricing
                </Button>
              </a> */}
            </div>
            {/* <p className="mt-6 text-sm opacity-80">No credit card required. 14-day free trial.</p> */}
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t py-4 bg-muted/30">
        <div className="container mx-auto px-4">
          {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <FileText className="h-6 w-6 text-primary" />
                <span className="font-bold text-xl">ScopingAI</span>
              </div>
              <p className="text-muted-foreground mb-4">
                AI-powered scoping documents that save time and reduce scope creep.
              </p>
              <div className="flex gap-4">
                <a to="#" className="text-muted-foreground hover:text-foreground">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                  </svg>
                </a>
                <a to="#" className="text-muted-foreground hover:text-foreground">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                </a>
                <a to="#" className="text-muted-foreground hover:text-foreground">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                  </svg>
                </a>
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-4">Product</h3>
              <ul className="space-y-2">
                <li>
                  <a to="#features" className="text-muted-foreground hover:text-foreground">
                    Features
                  </a>
                </li>
                <li>
                  <a to="#pricing" className="text-muted-foreground hover:text-foreground">
                    Pricing
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Templates
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Integrations
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    API
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-4">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Documentation
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Blog
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Guides
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Support Center
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Webinars
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-4">Company</h3>
              <ul className="space-y-2">
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    About Us
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Careers
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Contact
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a to="#" className="text-muted-foreground hover:text-foreground">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>
          </div> */}
          <div className="text-center text-muted-foreground flex flex-col md:flex-row items-center justify-between">
            <p>
              &copy; {new Date().getFullYear()} Altzero. All rights reserved.
            </p>
            <div className="flex items-center justify-center space-x-4 text-sm">
              <Link to="/privacy-policy" className="hover:underline">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link to="/terms-of-service" className="hover:underline">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </footer>

      {/* Waitlist Modal */}
      <WaitlistModal
        isOpen={isWaitlistModalOpen}
        onClose={() => setIsWaitlistModalOpen(false)}
      />
    </div>
  );
}
