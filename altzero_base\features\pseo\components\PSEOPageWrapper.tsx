import React, { ReactNode } from 'react';
import PSEONavigation from './PSEOSidePanel';

interface PSEOPageWrapperProps {
  children: ReactNode;
  stats?: {
    totalClients: number;
    totalWebsites: number;
    totalAudits: number;
    totalAnalysisJobs: number;
    loading: boolean;
  };
  fullSiteAnalysisResults?: any[];
  contentOpportunities?: any[];
  keywordResearch?: any[];
}

const PSEOPageWrapper: React.FC<PSEOPageWrapperProps> = ({
  children,
  stats = {
    totalClients: 0,
    totalWebsites: 0,
    totalAudits: 0,
    totalAnalysisJobs: 0,
    loading: false
  },
  fullSiteAnalysisResults = [],
  contentOpportunities = [],
  keywordResearch = []
}) => {
  return (
    <div className="flex min-h-screen bg-background">
      {/* Fixed Side Navigation */}
      <div className="w-64 flex-shrink-0">
        <PSEONavigation
          stats={stats}
          fullSiteAnalysisResults={fullSiteAnalysisResults}
          contentOpportunities={contentOpportunities}
          keywordResearch={keywordResearch}
        />
      </div>
      
      {/* Main Content Area */}
      <div className="flex-1 overflow-y-auto">
        {children}
      </div>
    </div>
  );
};

export default PSEOPageWrapper;
