erDiagram
    crm_contacts {
        UUID id PK
        UUID organisation_id FK
        TEXT full_name
        TEXT email
        TEXT phone
        TEXT phone2
        TEXT mobile
        JSONB address
        TEXT job_title
        UUID company_id FK
        UUID owner_id FK
        TEXT[] tags
        JSONB custom_fields
    }
    
    crm_companies {
        UUID id PK
        UUID organisation_id FK
        TEXT name
        TEXT website
        TEXT email
        TEXT phone
        TEXT fax
        JSONB address
        INTEGER employees
        TEXT revenues
        TEXT tax_number
        UUID region_id FK
        UUID owner_id FK
        JSONB custom_fields
    }
    
    crm_events {
        UUID id PK
        UUID organisation_id FK
        TEXT title
        TIMESTAMPTZ start_time
        TIMESTAMPTZ end_time
        TEXT location
        JSONB geo
        TEXT note
        UUID contact_id FK
        UUID company_id FK
        UUID owner_id FK
        JSONB custom_fields
    }
    
    crm_groups {
        UUID id PK
        UUID organisation_id FK
        TEXT name
        UUID owner_id FK
        TEXT color
        BOOLEAN is_favorite
    }
    
    crm_contact_groups {
        UUID id PK
        UUID organisation_id FK
        UUID contact_id FK
        UUID group_id FK
    }
    
    crm_regions {
        UUID id PK
        UUID organisation_id FK
        TEXT name
        BOOLEAN is_favorite
    }
    
    crm_pipelines {
        UUID id PK
        UUID organisation_id FK
        TEXT name
    }
    
    crm_pipeline_stages {
        UUID id PK
        UUID organisation_id FK
        UUID pipeline_id FK
        TEXT name
        INTEGER position
    }
    
    crm_opportunities {
        UUID id PK
        UUID organisation_id FK
        UUID contact_id FK
        TEXT title
        NUMERIC value
        TEXT currency
        TEXT stage
        DATE close_date
        UUID assigned_to FK
    }
    
    crm_activities {
        UUID id PK
        UUID organisation_id FK
        UUID contact_id FK
        UUID opportunity_id FK
        TEXT type
        TEXT content
        TIMESTAMPTZ scheduled_at
        UUID created_by FK
    }
    
    crm_jobs {
        UUID id PK
        UUID organisation_id FK
        TEXT title
        TEXT description
        TEXT location
        TEXT employment_type
        TEXT salary_range
        TIMESTAMPTZ posted_at
        TIMESTAMPTZ closing_date
    }
    
    crm_job_applications {
        UUID id PK
        UUID organisation_id FK
        UUID job_id FK
        UUID contact_id FK
        TEXT status
        TEXT resume_url
        TEXT cover_letter
    }
    
    crm_organisation_users {
        UUID id PK
        UUID user_id FK
        UUID organisation_id FK
        TEXT role
    }
    
    organisations {
        UUID id PK
        TEXT name
    }
    
    auth_users {
        UUID id PK
    }

    crm_contacts ||--|| crm_companies : "company_id"
    crm_contacts ||--|| organisations : "organisation_id"
    crm_companies ||--|| organisations : "organisation_id"
    crm_events ||--|| crm_contacts : "contact_id"
    crm_events ||--|| crm_companies : "company_id"
    crm_events ||--|| organisations : "organisation_id"
    crm_groups ||--|| organisations : "organisation_id"
    crm_contact_groups ||--|| crm_contacts : "contact_id"
    crm_contact_groups ||--|| crm_groups : "group_id"
    crm_contact_groups ||--|| organisations : "organisation_id"
    crm_regions ||--|| organisations : "organisation_id"
    crm_pipelines ||--|| organisations : "organisation_id"
    crm_pipeline_stages ||--|| crm_pipelines : "pipeline_id"
    crm_pipeline_stages ||--|| organisations : "organisation_id"
    crm_opportunities ||--|| crm_contacts : "contact_id"
    crm_opportunities ||--|| organisations : "organisation_id"
    crm_activities ||--|| crm_contacts : "contact_id"
    crm_activities ||--|| crm_opportunities : "opportunity_id"
    crm_activities ||--|| organisations : "organisation_id"
    crm_jobs ||--|| organisations : "organisation_id"
    crm_job_applications ||--|| crm_jobs : "job_id"
    crm_job_applications ||--|| crm_contacts : "contact_id"
    crm_job_applications ||--|| organisations : "organisation_id"
    crm_organisation_users ||--|| organisations : "organisation_id"
    crm_organisation_users ||--|| auth_users : "user_id"
