// =====================================================
// SCOPINGAI WORKFLOW STATE TYPES - LANGGRAPH
// =====================================================

export interface ScopingAiWorkflowState {
  // Core workflow metadata
  workflow_id: string;
  user_id: string;
  status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
  current_step: string;
  progress: number;
  started_at: string;
  completed_at?: string;
  last_updated: string;
  processing_time: number;

  // Input data
  client: {
    id?: string;
    name: string;
    industry: string;
    size?: string;
    location?: string;
    contact_person?: string;
    email?: string;
    phone?: string;
    website?: string;
    description?: string;
  };
  project: {
    title: string;
    description: string;
    scope?: string;
    timeline?: string;
    budget_range?: string;
    requirements?: string[];
    deliverables?: string[];
  };
  template: {
    id?: string;
    name: string;
    sections: string[];
    style?: string;
  };
  requirements: Record<string, any>;
  selected_knowledge_documents: string[];
  document_requirements: Record<string, string>;
  ai_prompts?: {
    style_guidance?: string;
    content_focus?: string;
    additional_instructions?: string;
  };

  // Generated data
  client_analysis?: {
    industry_insights: any[];
    market_position: string;
    key_challenges: string[];
    opportunities: string[];
    competitive_landscape: any[];
    recommendations: string[];
  };
  market_research?: {
    industry_trends: any[];
    market_size: any;
    growth_projections: any[];
    key_players: any[];
    regulatory_factors: string[];
  };
  knowledge_base_content?: {
    retrieved_documents: any[];
    relevant_content: string;
    search_results: any[];
    content_summary: string;
  };
  research_analysis?: {
    content: string;
    key_findings: string[];
    recommendations: string[];
    data_sources: string[];
  };
  executive_summary?: {
    content: string;
    key_points: string[];
    value_proposition: string;
  };
  proposal_sections?: Array<{
    title: string;
    content: string;
    word_count: number;
    quality_score: number;
  }>;
  final_proposal?: {
    id: string;
    title: string;
    sections: any[];
    metadata: any;
    quality_metrics: any;
  };

  // Workflow tracking
  errors: WorkflowError[];
  warnings: string[];
  api_calls_made: ApiCall[];
  total_cost: number;
  data_sources_used: string[];
  node_data: Record<string, any>;
  config: WorkflowConfig;
}

export interface WorkflowError {
  node_name: string;
  error_message: string;
  error_code: string;
  timestamp: string;
  recoverable: boolean;
  retry_count?: number;
}

export interface ApiCall {
  provider: string;
  endpoint: string;
  calls_made: number;
  success_rate: number;
  average_response_time: number;
  cost_estimate: number;
  timestamp: string;
}

export interface WorkflowConfig {
  timeout_seconds: number;
  retry_attempts: number;
  enable_caching: boolean;
  quality_threshold: number;
  max_sections: number;
  enable_market_research: boolean;
  enable_competitive_analysis: boolean;
  openai_api_key?: string;
  pinecone_api_key?: string;
  serp_api_key?: string;
}

export interface WorkflowContext {
  state: ScopingAiWorkflowState;
  tools: WorkflowTools;
  logger: WorkflowLogger;
  config: WorkflowConfig;
}

export interface WorkflowTools {
  ai: {
    generateText: (prompt: string, options?: any) => Promise<string>;
    generateStructuredData: (prompt: string, schema: any) => Promise<any>;
    analyzeContent: (content: string, criteria: string[]) => Promise<any>;
  };
  knowledgeBase: {
    searchByRequirements: (requirements: Record<string, string>, userId: string, documentIds?: string[]) => Promise<any>;
    getUserDocuments: (userId: string) => Promise<any[]>;
    formatDocumentsForAI: (documents: any[]) => string;
  };
  database: {
    query: (sql: string, params?: any[]) => Promise<any>;
    insert: (table: string, data: any) => Promise<any>;
    update: (table: string, data: any, where: any) => Promise<any>;
  };
  crm: {
    getClientData: (clientId: string) => Promise<any>;
    getIndustryData: (industry: string) => Promise<any>;
    getCompetitorData: (industry: string, location?: string) => Promise<any[]>;
  };
  market: {
    getIndustryTrends: (industry: string) => Promise<any[]>;
    getMarketData: (industry: string, location?: string) => Promise<any>;
    getCompetitiveAnalysis: (industry: string, competitors: string[]) => Promise<any>;
  };
  http: {
    get: (url: string, options?: any) => Promise<any>;
    post: (url: string, data: any, options?: any) => Promise<any>;
  };
}

export interface WorkflowLogger {
  info: (message: string, data?: any) => void;
  warn: (message: string, data?: any) => void;
  error: (message: string, error?: any) => void;
  debug: (message: string, data?: any) => void;
}

// Input types for workflow execution
export interface ProposalGenerationInput {
  user_id: string;
  client: ScopingAiWorkflowState['client'];
  project: ScopingAiWorkflowState['project'];
  template: ScopingAiWorkflowState['template'];
  requirements: Record<string, any>;
  selected_knowledge_documents: string[];
  document_requirements: Record<string, string>;
  ai_prompts?: ScopingAiWorkflowState['ai_prompts'];
  config?: Partial<WorkflowConfig>;
}

// Quality metrics
export interface QualityMetrics {
  overall_score: number;
  content_relevance: number;
  technical_accuracy: number;
  business_value: number;
  readability: number;
  completeness: number;
  consistency: number;
  improvement_suggestions: string[];
}

// Content analysis results
export interface ContentAnalysis {
  word_count: number;
  readability_score: number;
  technical_depth: number;
  business_focus: number;
  key_topics: string[];
  missing_elements: string[];
  strengths: string[];
  weaknesses: string[];
}
