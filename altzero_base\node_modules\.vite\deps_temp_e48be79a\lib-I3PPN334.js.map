{"version": 3, "sources": ["../../@supabase/realtime-js/node_modules/@supabase/node-fetch/lib/index.mjs"], "sourcesContent": ["import Stream from 'stream';\nimport http from 'http';\nimport Url from 'url';\nimport whatwgUrl from 'whatwg-url';\nimport https from 'https';\nimport zlib from 'zlib';\n\n// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js\n\n// fix for \"Readable\" isn't a named export issue\nconst Readable = Stream.Readable;\n\nconst BUFFER = Symbol('buffer');\nconst TYPE = Symbol('type');\n\nclass Blob {\n\tconstructor() {\n\t\tthis[TYPE] = '';\n\n\t\tconst blobParts = arguments[0];\n\t\tconst options = arguments[1];\n\n\t\tconst buffers = [];\n\t\tlet size = 0;\n\n\t\tif (blobParts) {\n\t\t\tconst a = blobParts;\n\t\t\tconst length = Number(a.length);\n\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\tconst element = a[i];\n\t\t\t\tlet buffer;\n\t\t\t\tif (element instanceof Buffer) {\n\t\t\t\t\tbuffer = element;\n\t\t\t\t} else if (ArrayBuffer.isView(element)) {\n\t\t\t\t\tbuffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);\n\t\t\t\t} else if (element instanceof ArrayBuffer) {\n\t\t\t\t\tbuffer = Buffer.from(element);\n\t\t\t\t} else if (element instanceof Blob) {\n\t\t\t\t\tbuffer = element[BUFFER];\n\t\t\t\t} else {\n\t\t\t\t\tbuffer = Buffer.from(typeof element === 'string' ? element : String(element));\n\t\t\t\t}\n\t\t\t\tsize += buffer.length;\n\t\t\t\tbuffers.push(buffer);\n\t\t\t}\n\t\t}\n\n\t\tthis[BUFFER] = Buffer.concat(buffers);\n\n\t\tlet type = options && options.type !== undefined && String(options.type).toLowerCase();\n\t\tif (type && !/[^\\u0020-\\u007E]/.test(type)) {\n\t\t\tthis[TYPE] = type;\n\t\t}\n\t}\n\tget size() {\n\t\treturn this[BUFFER].length;\n\t}\n\tget type() {\n\t\treturn this[TYPE];\n\t}\n\ttext() {\n\t\treturn Promise.resolve(this[BUFFER].toString());\n\t}\n\tarrayBuffer() {\n\t\tconst buf = this[BUFFER];\n\t\tconst ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n\t\treturn Promise.resolve(ab);\n\t}\n\tstream() {\n\t\tconst readable = new Readable();\n\t\treadable._read = function () {};\n\t\treadable.push(this[BUFFER]);\n\t\treadable.push(null);\n\t\treturn readable;\n\t}\n\ttoString() {\n\t\treturn '[object Blob]';\n\t}\n\tslice() {\n\t\tconst size = this.size;\n\n\t\tconst start = arguments[0];\n\t\tconst end = arguments[1];\n\t\tlet relativeStart, relativeEnd;\n\t\tif (start === undefined) {\n\t\t\trelativeStart = 0;\n\t\t} else if (start < 0) {\n\t\t\trelativeStart = Math.max(size + start, 0);\n\t\t} else {\n\t\t\trelativeStart = Math.min(start, size);\n\t\t}\n\t\tif (end === undefined) {\n\t\t\trelativeEnd = size;\n\t\t} else if (end < 0) {\n\t\t\trelativeEnd = Math.max(size + end, 0);\n\t\t} else {\n\t\t\trelativeEnd = Math.min(end, size);\n\t\t}\n\t\tconst span = Math.max(relativeEnd - relativeStart, 0);\n\n\t\tconst buffer = this[BUFFER];\n\t\tconst slicedBuffer = buffer.slice(relativeStart, relativeStart + span);\n\t\tconst blob = new Blob([], { type: arguments[2] });\n\t\tblob[BUFFER] = slicedBuffer;\n\t\treturn blob;\n\t}\n}\n\nObject.defineProperties(Blob.prototype, {\n\tsize: { enumerable: true },\n\ttype: { enumerable: true },\n\tslice: { enumerable: true }\n});\n\nObject.defineProperty(Blob.prototype, Symbol.toStringTag, {\n\tvalue: 'Blob',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\n/**\n * fetch-error.js\n *\n * FetchError interface for operational errors\n */\n\n/**\n * Create FetchError instance\n *\n * @param   String      message      Error message for human\n * @param   String      type         Error type for machine\n * @param   String      systemError  For Node.js system error\n * @return  FetchError\n */\nfunction FetchError(message, type, systemError) {\n  Error.call(this, message);\n\n  this.message = message;\n  this.type = type;\n\n  // when err.type is `system`, err.code contains system error code\n  if (systemError) {\n    this.code = this.errno = systemError.code;\n  }\n\n  // hide custom error implementation details from end-users\n  Error.captureStackTrace(this, this.constructor);\n}\n\nFetchError.prototype = Object.create(Error.prototype);\nFetchError.prototype.constructor = FetchError;\nFetchError.prototype.name = 'FetchError';\n\nlet convert;\n\nconst INTERNALS = Symbol('Body internals');\n\n// fix an issue where \"PassThrough\" isn't a named export for node <10\nconst PassThrough = Stream.PassThrough;\n\n/**\n * Body mixin\n *\n * Ref: https://fetch.spec.whatwg.org/#body\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nfunction Body(body) {\n\tvar _this = this;\n\n\tvar _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n\t    _ref$size = _ref.size;\n\n\tlet size = _ref$size === undefined ? 0 : _ref$size;\n\tvar _ref$timeout = _ref.timeout;\n\tlet timeout = _ref$timeout === undefined ? 0 : _ref$timeout;\n\n\tif (body == null) {\n\t\t// body is undefined or null\n\t\tbody = null;\n\t} else if (isURLSearchParams(body)) {\n\t\t// body is a URLSearchParams\n\t\tbody = Buffer.from(body.toString());\n\t} else if (isBlob(body)) ; else if (Buffer.isBuffer(body)) ; else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {\n\t\t// body is ArrayBuffer\n\t\tbody = Buffer.from(body);\n\t} else if (ArrayBuffer.isView(body)) {\n\t\t// body is ArrayBufferView\n\t\tbody = Buffer.from(body.buffer, body.byteOffset, body.byteLength);\n\t} else if (body instanceof Stream) ; else {\n\t\t// none of the above\n\t\t// coerce to string then buffer\n\t\tbody = Buffer.from(String(body));\n\t}\n\tthis[INTERNALS] = {\n\t\tbody,\n\t\tdisturbed: false,\n\t\terror: null\n\t};\n\tthis.size = size;\n\tthis.timeout = timeout;\n\n\tif (body instanceof Stream) {\n\t\tbody.on('error', function (err) {\n\t\t\tconst error = err.name === 'AbortError' ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, 'system', err);\n\t\t\t_this[INTERNALS].error = error;\n\t\t});\n\t}\n}\n\nBody.prototype = {\n\tget body() {\n\t\treturn this[INTERNALS].body;\n\t},\n\n\tget bodyUsed() {\n\t\treturn this[INTERNALS].disturbed;\n\t},\n\n\t/**\n  * Decode response as ArrayBuffer\n  *\n  * @return  Promise\n  */\n\tarrayBuffer() {\n\t\treturn consumeBody.call(this).then(function (buf) {\n\t\t\treturn buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n\t\t});\n\t},\n\n\t/**\n  * Return raw response as Blob\n  *\n  * @return Promise\n  */\n\tblob() {\n\t\tlet ct = this.headers && this.headers.get('content-type') || '';\n\t\treturn consumeBody.call(this).then(function (buf) {\n\t\t\treturn Object.assign(\n\t\t\t// Prevent copying\n\t\t\tnew Blob([], {\n\t\t\t\ttype: ct.toLowerCase()\n\t\t\t}), {\n\t\t\t\t[BUFFER]: buf\n\t\t\t});\n\t\t});\n\t},\n\n\t/**\n  * Decode response as json\n  *\n  * @return  Promise\n  */\n\tjson() {\n\t\tvar _this2 = this;\n\n\t\treturn consumeBody.call(this).then(function (buffer) {\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(buffer.toString());\n\t\t\t} catch (err) {\n\t\t\t\treturn Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, 'invalid-json'));\n\t\t\t}\n\t\t});\n\t},\n\n\t/**\n  * Decode response as text\n  *\n  * @return  Promise\n  */\n\ttext() {\n\t\treturn consumeBody.call(this).then(function (buffer) {\n\t\t\treturn buffer.toString();\n\t\t});\n\t},\n\n\t/**\n  * Decode response as buffer (non-spec api)\n  *\n  * @return  Promise\n  */\n\tbuffer() {\n\t\treturn consumeBody.call(this);\n\t},\n\n\t/**\n  * Decode response as text, while automatically detecting the encoding and\n  * trying to decode to UTF-8 (non-spec api)\n  *\n  * @return  Promise\n  */\n\ttextConverted() {\n\t\tvar _this3 = this;\n\n\t\treturn consumeBody.call(this).then(function (buffer) {\n\t\t\treturn convertBody(buffer, _this3.headers);\n\t\t});\n\t}\n};\n\n// In browsers, all properties are enumerable.\nObject.defineProperties(Body.prototype, {\n\tbody: { enumerable: true },\n\tbodyUsed: { enumerable: true },\n\tarrayBuffer: { enumerable: true },\n\tblob: { enumerable: true },\n\tjson: { enumerable: true },\n\ttext: { enumerable: true }\n});\n\nBody.mixIn = function (proto) {\n\tfor (const name of Object.getOwnPropertyNames(Body.prototype)) {\n\t\t// istanbul ignore else: future proof\n\t\tif (!(name in proto)) {\n\t\t\tconst desc = Object.getOwnPropertyDescriptor(Body.prototype, name);\n\t\t\tObject.defineProperty(proto, name, desc);\n\t\t}\n\t}\n};\n\n/**\n * Consume and convert an entire Body to a Buffer.\n *\n * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body\n *\n * @return  Promise\n */\nfunction consumeBody() {\n\tvar _this4 = this;\n\n\tif (this[INTERNALS].disturbed) {\n\t\treturn Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));\n\t}\n\n\tthis[INTERNALS].disturbed = true;\n\n\tif (this[INTERNALS].error) {\n\t\treturn Body.Promise.reject(this[INTERNALS].error);\n\t}\n\n\tlet body = this.body;\n\n\t// body is null\n\tif (body === null) {\n\t\treturn Body.Promise.resolve(Buffer.alloc(0));\n\t}\n\n\t// body is blob\n\tif (isBlob(body)) {\n\t\tbody = body.stream();\n\t}\n\n\t// body is buffer\n\tif (Buffer.isBuffer(body)) {\n\t\treturn Body.Promise.resolve(body);\n\t}\n\n\t// istanbul ignore if: should never happen\n\tif (!(body instanceof Stream)) {\n\t\treturn Body.Promise.resolve(Buffer.alloc(0));\n\t}\n\n\t// body is stream\n\t// get ready to actually consume the body\n\tlet accum = [];\n\tlet accumBytes = 0;\n\tlet abort = false;\n\n\treturn new Body.Promise(function (resolve, reject) {\n\t\tlet resTimeout;\n\n\t\t// allow timeout on slow response body\n\t\tif (_this4.timeout) {\n\t\t\tresTimeout = setTimeout(function () {\n\t\t\t\tabort = true;\n\t\t\t\treject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, 'body-timeout'));\n\t\t\t}, _this4.timeout);\n\t\t}\n\n\t\t// handle stream errors\n\t\tbody.on('error', function (err) {\n\t\t\tif (err.name === 'AbortError') {\n\t\t\t\t// if the request was aborted, reject with this Error\n\t\t\t\tabort = true;\n\t\t\t\treject(err);\n\t\t\t} else {\n\t\t\t\t// other errors, such as incorrect content-encoding\n\t\t\t\treject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, 'system', err));\n\t\t\t}\n\t\t});\n\n\t\tbody.on('data', function (chunk) {\n\t\t\tif (abort || chunk === null) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (_this4.size && accumBytes + chunk.length > _this4.size) {\n\t\t\t\tabort = true;\n\t\t\t\treject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, 'max-size'));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\taccumBytes += chunk.length;\n\t\t\taccum.push(chunk);\n\t\t});\n\n\t\tbody.on('end', function () {\n\t\t\tif (abort) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tclearTimeout(resTimeout);\n\n\t\t\ttry {\n\t\t\t\tresolve(Buffer.concat(accum, accumBytes));\n\t\t\t} catch (err) {\n\t\t\t\t// handle streams that have accumulated too much data (issue #414)\n\t\t\t\treject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, 'system', err));\n\t\t\t}\n\t\t});\n\t});\n}\n\n/**\n * Detect buffer encoding and convert to target encoding\n * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding\n *\n * @param   Buffer  buffer    Incoming buffer\n * @param   String  encoding  Target encoding\n * @return  String\n */\nfunction convertBody(buffer, headers) {\n\t{\n\t\tthrow new Error('The package `encoding` must be installed to use the textConverted() function');\n\t}\n\n\tconst ct = headers.get('content-type');\n\tlet charset = 'utf-8';\n\tlet res, str;\n\n\t// header\n\tif (ct) {\n\t\tres = /charset=([^;]*)/i.exec(ct);\n\t}\n\n\t// no charset in content type, peek at response body for at most 1024 bytes\n\tstr = buffer.slice(0, 1024).toString();\n\n\t// html5\n\tif (!res && str) {\n\t\tres = /<meta.+?charset=(['\"])(.+?)\\1/i.exec(str);\n\t}\n\n\t// html4\n\tif (!res && str) {\n\t\tres = /<meta[\\s]+?http-equiv=(['\"])content-type\\1[\\s]+?content=(['\"])(.+?)\\2/i.exec(str);\n\t\tif (!res) {\n\t\t\tres = /<meta[\\s]+?content=(['\"])(.+?)\\1[\\s]+?http-equiv=(['\"])content-type\\3/i.exec(str);\n\t\t\tif (res) {\n\t\t\t\tres.pop(); // drop last quote\n\t\t\t}\n\t\t}\n\n\t\tif (res) {\n\t\t\tres = /charset=(.*)/i.exec(res.pop());\n\t\t}\n\t}\n\n\t// xml\n\tif (!res && str) {\n\t\tres = /<\\?xml.+?encoding=(['\"])(.+?)\\1/i.exec(str);\n\t}\n\n\t// found charset\n\tif (res) {\n\t\tcharset = res.pop();\n\n\t\t// prevent decode issues when sites use incorrect encoding\n\t\t// ref: https://hsivonen.fi/encoding-menu/\n\t\tif (charset === 'gb2312' || charset === 'gbk') {\n\t\t\tcharset = 'gb18030';\n\t\t}\n\t}\n\n\t// turn raw buffers into a single utf-8 buffer\n\treturn convert(buffer, 'UTF-8', charset).toString();\n}\n\n/**\n * Detect a URLSearchParams object\n * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143\n *\n * @param   Object  obj     Object to detect by type or brand\n * @return  String\n */\nfunction isURLSearchParams(obj) {\n\t// Duck-typing as a necessary condition.\n\tif (typeof obj !== 'object' || typeof obj.append !== 'function' || typeof obj.delete !== 'function' || typeof obj.get !== 'function' || typeof obj.getAll !== 'function' || typeof obj.has !== 'function' || typeof obj.set !== 'function') {\n\t\treturn false;\n\t}\n\n\t// Brand-checking and more duck-typing as optional condition.\n\treturn obj.constructor.name === 'URLSearchParams' || Object.prototype.toString.call(obj) === '[object URLSearchParams]' || typeof obj.sort === 'function';\n}\n\n/**\n * Check if `obj` is a W3C `Blob` object (which `File` inherits from)\n * @param  {*} obj\n * @return {boolean}\n */\nfunction isBlob(obj) {\n\treturn typeof obj === 'object' && typeof obj.arrayBuffer === 'function' && typeof obj.type === 'string' && typeof obj.stream === 'function' && typeof obj.constructor === 'function' && typeof obj.constructor.name === 'string' && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);\n}\n\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed  instance  Response or Request instance\n * @return  Mixed\n */\nfunction clone(instance) {\n\tlet p1, p2;\n\tlet body = instance.body;\n\n\t// don't allow cloning a used body\n\tif (instance.bodyUsed) {\n\t\tthrow new Error('cannot clone body after it is used');\n\t}\n\n\t// check that body is a stream and not form-data object\n\t// note: we can't clone the form-data object without having it as a dependency\n\tif (body instanceof Stream && typeof body.getBoundary !== 'function') {\n\t\t// tee instance body\n\t\tp1 = new PassThrough();\n\t\tp2 = new PassThrough();\n\t\tbody.pipe(p1);\n\t\tbody.pipe(p2);\n\t\t// set instance body to teed body and return the other teed body\n\t\tinstance[INTERNALS].body = p1;\n\t\tbody = p2;\n\t}\n\n\treturn body;\n}\n\n/**\n * Performs the operation \"extract a `Content-Type` value from |object|\" as\n * specified in the specification:\n * https://fetch.spec.whatwg.org/#concept-bodyinit-extract\n *\n * This function assumes that instance.body is present.\n *\n * @param   Mixed  instance  Any options.body input\n */\nfunction extractContentType(body) {\n\tif (body === null) {\n\t\t// body is null\n\t\treturn null;\n\t} else if (typeof body === 'string') {\n\t\t// body is string\n\t\treturn 'text/plain;charset=UTF-8';\n\t} else if (isURLSearchParams(body)) {\n\t\t// body is a URLSearchParams\n\t\treturn 'application/x-www-form-urlencoded;charset=UTF-8';\n\t} else if (isBlob(body)) {\n\t\t// body is blob\n\t\treturn body.type || null;\n\t} else if (Buffer.isBuffer(body)) {\n\t\t// body is buffer\n\t\treturn null;\n\t} else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {\n\t\t// body is ArrayBuffer\n\t\treturn null;\n\t} else if (ArrayBuffer.isView(body)) {\n\t\t// body is ArrayBufferView\n\t\treturn null;\n\t} else if (typeof body.getBoundary === 'function') {\n\t\t// detect form data input from form-data module\n\t\treturn `multipart/form-data;boundary=${body.getBoundary()}`;\n\t} else if (body instanceof Stream) {\n\t\t// body is stream\n\t\t// can't really do much about this\n\t\treturn null;\n\t} else {\n\t\t// Body constructor defaults other things to string\n\t\treturn 'text/plain;charset=UTF-8';\n\t}\n}\n\n/**\n * The Fetch Standard treats this as if \"total bytes\" is a property on the body.\n * For us, we have to explicitly get it with a function.\n *\n * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes\n *\n * @param   Body    instance   Instance of Body\n * @return  Number?            Number of bytes, or null if not possible\n */\nfunction getTotalBytes(instance) {\n\tconst body = instance.body;\n\n\n\tif (body === null) {\n\t\t// body is null\n\t\treturn 0;\n\t} else if (isBlob(body)) {\n\t\treturn body.size;\n\t} else if (Buffer.isBuffer(body)) {\n\t\t// body is buffer\n\t\treturn body.length;\n\t} else if (body && typeof body.getLengthSync === 'function') {\n\t\t// detect form data input from form-data module\n\t\tif (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x\n\t\tbody.hasKnownLength && body.hasKnownLength()) {\n\t\t\t// 2.x\n\t\t\treturn body.getLengthSync();\n\t\t}\n\t\treturn null;\n\t} else {\n\t\t// body is stream\n\t\treturn null;\n\t}\n}\n\n/**\n * Write a Body to a Node.js WritableStream (e.g. http.Request) object.\n *\n * @param   Body    instance   Instance of Body\n * @return  Void\n */\nfunction writeToStream(dest, instance) {\n\tconst body = instance.body;\n\n\n\tif (body === null) {\n\t\t// body is null\n\t\tdest.end();\n\t} else if (isBlob(body)) {\n\t\tbody.stream().pipe(dest);\n\t} else if (Buffer.isBuffer(body)) {\n\t\t// body is buffer\n\t\tdest.write(body);\n\t\tdest.end();\n\t} else {\n\t\t// body is stream\n\t\tbody.pipe(dest);\n\t}\n}\n\n// expose Promise\nBody.Promise = global.Promise;\n\n/**\n * headers.js\n *\n * Headers class offers convenient helpers\n */\n\nconst invalidTokenRegex = /[^\\^_`a-zA-Z\\-0-9!#$%&'*+.|~]/;\nconst invalidHeaderCharRegex = /[^\\t\\x20-\\x7e\\x80-\\xff]/;\n\nfunction validateName(name) {\n\tname = `${name}`;\n\tif (invalidTokenRegex.test(name) || name === '') {\n\t\tthrow new TypeError(`${name} is not a legal HTTP header name`);\n\t}\n}\n\nfunction validateValue(value) {\n\tvalue = `${value}`;\n\tif (invalidHeaderCharRegex.test(value)) {\n\t\tthrow new TypeError(`${value} is not a legal HTTP header value`);\n\t}\n}\n\n/**\n * Find the key in the map object given a header name.\n *\n * Returns undefined if not found.\n *\n * @param   String  name  Header name\n * @return  String|Undefined\n */\nfunction find(map, name) {\n\tname = name.toLowerCase();\n\tfor (const key in map) {\n\t\tif (key.toLowerCase() === name) {\n\t\t\treturn key;\n\t\t}\n\t}\n\treturn undefined;\n}\n\nconst MAP = Symbol('map');\nclass Headers {\n\t/**\n  * Headers class\n  *\n  * @param   Object  headers  Response headers\n  * @return  Void\n  */\n\tconstructor() {\n\t\tlet init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;\n\n\t\tthis[MAP] = Object.create(null);\n\n\t\tif (init instanceof Headers) {\n\t\t\tconst rawHeaders = init.raw();\n\t\t\tconst headerNames = Object.keys(rawHeaders);\n\n\t\t\tfor (const headerName of headerNames) {\n\t\t\t\tfor (const value of rawHeaders[headerName]) {\n\t\t\t\t\tthis.append(headerName, value);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t// We don't worry about converting prop to ByteString here as append()\n\t\t// will handle it.\n\t\tif (init == null) ; else if (typeof init === 'object') {\n\t\t\tconst method = init[Symbol.iterator];\n\t\t\tif (method != null) {\n\t\t\t\tif (typeof method !== 'function') {\n\t\t\t\t\tthrow new TypeError('Header pairs must be iterable');\n\t\t\t\t}\n\n\t\t\t\t// sequence<sequence<ByteString>>\n\t\t\t\t// Note: per spec we have to first exhaust the lists then process them\n\t\t\t\tconst pairs = [];\n\t\t\t\tfor (const pair of init) {\n\t\t\t\t\tif (typeof pair !== 'object' || typeof pair[Symbol.iterator] !== 'function') {\n\t\t\t\t\t\tthrow new TypeError('Each header pair must be iterable');\n\t\t\t\t\t}\n\t\t\t\t\tpairs.push(Array.from(pair));\n\t\t\t\t}\n\n\t\t\t\tfor (const pair of pairs) {\n\t\t\t\t\tif (pair.length !== 2) {\n\t\t\t\t\t\tthrow new TypeError('Each header pair must be a name/value tuple');\n\t\t\t\t\t}\n\t\t\t\t\tthis.append(pair[0], pair[1]);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// record<ByteString, ByteString>\n\t\t\t\tfor (const key of Object.keys(init)) {\n\t\t\t\t\tconst value = init[key];\n\t\t\t\t\tthis.append(key, value);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tthrow new TypeError('Provided initializer must be an object');\n\t\t}\n\t}\n\n\t/**\n  * Return combined header value given name\n  *\n  * @param   String  name  Header name\n  * @return  Mixed\n  */\n\tget(name) {\n\t\tname = `${name}`;\n\t\tvalidateName(name);\n\t\tconst key = find(this[MAP], name);\n\t\tif (key === undefined) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn this[MAP][key].join(', ');\n\t}\n\n\t/**\n  * Iterate over all headers\n  *\n  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)\n  * @param   Boolean   thisArg   `this` context for callback function\n  * @return  Void\n  */\n\tforEach(callback) {\n\t\tlet thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n\n\t\tlet pairs = getHeaders(this);\n\t\tlet i = 0;\n\t\twhile (i < pairs.length) {\n\t\t\tvar _pairs$i = pairs[i];\n\t\t\tconst name = _pairs$i[0],\n\t\t\t      value = _pairs$i[1];\n\n\t\t\tcallback.call(thisArg, value, name, this);\n\t\t\tpairs = getHeaders(this);\n\t\t\ti++;\n\t\t}\n\t}\n\n\t/**\n  * Overwrite header values given name\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */\n\tset(name, value) {\n\t\tname = `${name}`;\n\t\tvalue = `${value}`;\n\t\tvalidateName(name);\n\t\tvalidateValue(value);\n\t\tconst key = find(this[MAP], name);\n\t\tthis[MAP][key !== undefined ? key : name] = [value];\n\t}\n\n\t/**\n  * Append a value onto existing header\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */\n\tappend(name, value) {\n\t\tname = `${name}`;\n\t\tvalue = `${value}`;\n\t\tvalidateName(name);\n\t\tvalidateValue(value);\n\t\tconst key = find(this[MAP], name);\n\t\tif (key !== undefined) {\n\t\t\tthis[MAP][key].push(value);\n\t\t} else {\n\t\t\tthis[MAP][name] = [value];\n\t\t}\n\t}\n\n\t/**\n  * Check for header name existence\n  *\n  * @param   String   name  Header name\n  * @return  Boolean\n  */\n\thas(name) {\n\t\tname = `${name}`;\n\t\tvalidateName(name);\n\t\treturn find(this[MAP], name) !== undefined;\n\t}\n\n\t/**\n  * Delete all header values given name\n  *\n  * @param   String  name  Header name\n  * @return  Void\n  */\n\tdelete(name) {\n\t\tname = `${name}`;\n\t\tvalidateName(name);\n\t\tconst key = find(this[MAP], name);\n\t\tif (key !== undefined) {\n\t\t\tdelete this[MAP][key];\n\t\t}\n\t}\n\n\t/**\n  * Return raw headers (non-spec api)\n  *\n  * @return  Object\n  */\n\traw() {\n\t\treturn this[MAP];\n\t}\n\n\t/**\n  * Get an iterator on keys.\n  *\n  * @return  Iterator\n  */\n\tkeys() {\n\t\treturn createHeadersIterator(this, 'key');\n\t}\n\n\t/**\n  * Get an iterator on values.\n  *\n  * @return  Iterator\n  */\n\tvalues() {\n\t\treturn createHeadersIterator(this, 'value');\n\t}\n\n\t/**\n  * Get an iterator on entries.\n  *\n  * This is the default iterator of the Headers object.\n  *\n  * @return  Iterator\n  */\n\t[Symbol.iterator]() {\n\t\treturn createHeadersIterator(this, 'key+value');\n\t}\n}\nHeaders.prototype.entries = Headers.prototype[Symbol.iterator];\n\nObject.defineProperty(Headers.prototype, Symbol.toStringTag, {\n\tvalue: 'Headers',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nObject.defineProperties(Headers.prototype, {\n\tget: { enumerable: true },\n\tforEach: { enumerable: true },\n\tset: { enumerable: true },\n\tappend: { enumerable: true },\n\thas: { enumerable: true },\n\tdelete: { enumerable: true },\n\tkeys: { enumerable: true },\n\tvalues: { enumerable: true },\n\tentries: { enumerable: true }\n});\n\nfunction getHeaders(headers) {\n\tlet kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key+value';\n\n\tconst keys = Object.keys(headers[MAP]).sort();\n\treturn keys.map(kind === 'key' ? function (k) {\n\t\treturn k.toLowerCase();\n\t} : kind === 'value' ? function (k) {\n\t\treturn headers[MAP][k].join(', ');\n\t} : function (k) {\n\t\treturn [k.toLowerCase(), headers[MAP][k].join(', ')];\n\t});\n}\n\nconst INTERNAL = Symbol('internal');\n\nfunction createHeadersIterator(target, kind) {\n\tconst iterator = Object.create(HeadersIteratorPrototype);\n\titerator[INTERNAL] = {\n\t\ttarget,\n\t\tkind,\n\t\tindex: 0\n\t};\n\treturn iterator;\n}\n\nconst HeadersIteratorPrototype = Object.setPrototypeOf({\n\tnext() {\n\t\t// istanbul ignore if\n\t\tif (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {\n\t\t\tthrow new TypeError('Value of `this` is not a HeadersIterator');\n\t\t}\n\n\t\tvar _INTERNAL = this[INTERNAL];\n\t\tconst target = _INTERNAL.target,\n\t\t      kind = _INTERNAL.kind,\n\t\t      index = _INTERNAL.index;\n\n\t\tconst values = getHeaders(target, kind);\n\t\tconst len = values.length;\n\t\tif (index >= len) {\n\t\t\treturn {\n\t\t\t\tvalue: undefined,\n\t\t\t\tdone: true\n\t\t\t};\n\t\t}\n\n\t\tthis[INTERNAL].index = index + 1;\n\n\t\treturn {\n\t\t\tvalue: values[index],\n\t\t\tdone: false\n\t\t};\n\t}\n}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));\n\nObject.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {\n\tvalue: 'HeadersIterator',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\n/**\n * Export the Headers object in a form that Node.js can consume.\n *\n * @param   Headers  headers\n * @return  Object\n */\nfunction exportNodeCompatibleHeaders(headers) {\n\tconst obj = Object.assign({ __proto__: null }, headers[MAP]);\n\n\t// http.request() only supports string as Host header. This hack makes\n\t// specifying custom Host header possible.\n\tconst hostHeaderKey = find(headers[MAP], 'Host');\n\tif (hostHeaderKey !== undefined) {\n\t\tobj[hostHeaderKey] = obj[hostHeaderKey][0];\n\t}\n\n\treturn obj;\n}\n\n/**\n * Create a Headers object from an object of headers, ignoring those that do\n * not conform to HTTP grammar productions.\n *\n * @param   Object  obj  Object of headers\n * @return  Headers\n */\nfunction createHeadersLenient(obj) {\n\tconst headers = new Headers();\n\tfor (const name of Object.keys(obj)) {\n\t\tif (invalidTokenRegex.test(name)) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (Array.isArray(obj[name])) {\n\t\t\tfor (const val of obj[name]) {\n\t\t\t\tif (invalidHeaderCharRegex.test(val)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (headers[MAP][name] === undefined) {\n\t\t\t\t\theaders[MAP][name] = [val];\n\t\t\t\t} else {\n\t\t\t\t\theaders[MAP][name].push(val);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (!invalidHeaderCharRegex.test(obj[name])) {\n\t\t\theaders[MAP][name] = [obj[name]];\n\t\t}\n\t}\n\treturn headers;\n}\n\nconst INTERNALS$1 = Symbol('Response internals');\n\n// fix an issue where \"STATUS_CODES\" aren't a named export for node <10\nconst STATUS_CODES = http.STATUS_CODES;\n\n/**\n * Response class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nclass Response {\n\tconstructor() {\n\t\tlet body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n\t\tlet opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n\t\tBody.call(this, body, opts);\n\n\t\tconst status = opts.status || 200;\n\t\tconst headers = new Headers(opts.headers);\n\n\t\tif (body != null && !headers.has('Content-Type')) {\n\t\t\tconst contentType = extractContentType(body);\n\t\t\tif (contentType) {\n\t\t\t\theaders.append('Content-Type', contentType);\n\t\t\t}\n\t\t}\n\n\t\tthis[INTERNALS$1] = {\n\t\t\turl: opts.url,\n\t\t\tstatus,\n\t\t\tstatusText: opts.statusText || STATUS_CODES[status],\n\t\t\theaders,\n\t\t\tcounter: opts.counter\n\t\t};\n\t}\n\n\tget url() {\n\t\treturn this[INTERNALS$1].url || '';\n\t}\n\n\tget status() {\n\t\treturn this[INTERNALS$1].status;\n\t}\n\n\t/**\n  * Convenience property representing if the request ended normally\n  */\n\tget ok() {\n\t\treturn this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;\n\t}\n\n\tget redirected() {\n\t\treturn this[INTERNALS$1].counter > 0;\n\t}\n\n\tget statusText() {\n\t\treturn this[INTERNALS$1].statusText;\n\t}\n\n\tget headers() {\n\t\treturn this[INTERNALS$1].headers;\n\t}\n\n\t/**\n  * Clone this response\n  *\n  * @return  Response\n  */\n\tclone() {\n\t\treturn new Response(clone(this), {\n\t\t\turl: this.url,\n\t\t\tstatus: this.status,\n\t\t\tstatusText: this.statusText,\n\t\t\theaders: this.headers,\n\t\t\tok: this.ok,\n\t\t\tredirected: this.redirected\n\t\t});\n\t}\n}\n\nBody.mixIn(Response.prototype);\n\nObject.defineProperties(Response.prototype, {\n\turl: { enumerable: true },\n\tstatus: { enumerable: true },\n\tok: { enumerable: true },\n\tredirected: { enumerable: true },\n\tstatusText: { enumerable: true },\n\theaders: { enumerable: true },\n\tclone: { enumerable: true }\n});\n\nObject.defineProperty(Response.prototype, Symbol.toStringTag, {\n\tvalue: 'Response',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nconst INTERNALS$2 = Symbol('Request internals');\nconst URL = Url.URL || whatwgUrl.URL;\n\n// fix an issue where \"format\", \"parse\" aren't a named export for node <10\nconst parse_url = Url.parse;\nconst format_url = Url.format;\n\n/**\n * Wrapper around `new URL` to handle arbitrary URLs\n *\n * @param  {string} urlStr\n * @return {void}\n */\nfunction parseURL(urlStr) {\n\t/*\n \tCheck whether the URL is absolute or not\n \t\tScheme: https://tools.ietf.org/html/rfc3986#section-3.1\n \tAbsolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\n */\n\tif (/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.exec(urlStr)) {\n\t\turlStr = new URL(urlStr).toString();\n\t}\n\n\t// Fallback to old implementation for arbitrary URLs\n\treturn parse_url(urlStr);\n}\n\nconst streamDestructionSupported = 'destroy' in Stream.Readable.prototype;\n\n/**\n * Check if a value is an instance of Request.\n *\n * @param   Mixed   input\n * @return  Boolean\n */\nfunction isRequest(input) {\n\treturn typeof input === 'object' && typeof input[INTERNALS$2] === 'object';\n}\n\nfunction isAbortSignal(signal) {\n\tconst proto = signal && typeof signal === 'object' && Object.getPrototypeOf(signal);\n\treturn !!(proto && proto.constructor.name === 'AbortSignal');\n}\n\n/**\n * Request class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */\nclass Request {\n\tconstructor(input) {\n\t\tlet init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n\t\tlet parsedURL;\n\n\t\t// normalize input\n\t\tif (!isRequest(input)) {\n\t\t\tif (input && input.href) {\n\t\t\t\t// in order to support Node.js' Url objects; though WHATWG's URL objects\n\t\t\t\t// will fall into this branch also (since their `toString()` will return\n\t\t\t\t// `href` property anyway)\n\t\t\t\tparsedURL = parseURL(input.href);\n\t\t\t} else {\n\t\t\t\t// coerce input to a string before attempting to parse\n\t\t\t\tparsedURL = parseURL(`${input}`);\n\t\t\t}\n\t\t\tinput = {};\n\t\t} else {\n\t\t\tparsedURL = parseURL(input.url);\n\t\t}\n\n\t\tlet method = init.method || input.method || 'GET';\n\t\tmethod = method.toUpperCase();\n\n\t\tif ((init.body != null || isRequest(input) && input.body !== null) && (method === 'GET' || method === 'HEAD')) {\n\t\t\tthrow new TypeError('Request with GET/HEAD method cannot have body');\n\t\t}\n\n\t\tlet inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;\n\n\t\tBody.call(this, inputBody, {\n\t\t\ttimeout: init.timeout || input.timeout || 0,\n\t\t\tsize: init.size || input.size || 0\n\t\t});\n\n\t\tconst headers = new Headers(init.headers || input.headers || {});\n\n\t\tif (inputBody != null && !headers.has('Content-Type')) {\n\t\t\tconst contentType = extractContentType(inputBody);\n\t\t\tif (contentType) {\n\t\t\t\theaders.append('Content-Type', contentType);\n\t\t\t}\n\t\t}\n\n\t\tlet signal = isRequest(input) ? input.signal : null;\n\t\tif ('signal' in init) signal = init.signal;\n\n\t\tif (signal != null && !isAbortSignal(signal)) {\n\t\t\tthrow new TypeError('Expected signal to be an instanceof AbortSignal');\n\t\t}\n\n\t\tthis[INTERNALS$2] = {\n\t\t\tmethod,\n\t\t\tredirect: init.redirect || input.redirect || 'follow',\n\t\t\theaders,\n\t\t\tparsedURL,\n\t\t\tsignal\n\t\t};\n\n\t\t// node-fetch-only options\n\t\tthis.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;\n\t\tthis.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;\n\t\tthis.counter = init.counter || input.counter || 0;\n\t\tthis.agent = init.agent || input.agent;\n\t}\n\n\tget method() {\n\t\treturn this[INTERNALS$2].method;\n\t}\n\n\tget url() {\n\t\treturn format_url(this[INTERNALS$2].parsedURL);\n\t}\n\n\tget headers() {\n\t\treturn this[INTERNALS$2].headers;\n\t}\n\n\tget redirect() {\n\t\treturn this[INTERNALS$2].redirect;\n\t}\n\n\tget signal() {\n\t\treturn this[INTERNALS$2].signal;\n\t}\n\n\t/**\n  * Clone this request\n  *\n  * @return  Request\n  */\n\tclone() {\n\t\treturn new Request(this);\n\t}\n}\n\nBody.mixIn(Request.prototype);\n\nObject.defineProperty(Request.prototype, Symbol.toStringTag, {\n\tvalue: 'Request',\n\twritable: false,\n\tenumerable: false,\n\tconfigurable: true\n});\n\nObject.defineProperties(Request.prototype, {\n\tmethod: { enumerable: true },\n\turl: { enumerable: true },\n\theaders: { enumerable: true },\n\tredirect: { enumerable: true },\n\tclone: { enumerable: true },\n\tsignal: { enumerable: true }\n});\n\n/**\n * Convert a Request to Node.js http request options.\n *\n * @param   Request  A Request instance\n * @return  Object   The options object to be passed to http.request\n */\nfunction getNodeRequestOptions(request) {\n\tconst parsedURL = request[INTERNALS$2].parsedURL;\n\tconst headers = new Headers(request[INTERNALS$2].headers);\n\n\t// fetch step 1.3\n\tif (!headers.has('Accept')) {\n\t\theaders.set('Accept', '*/*');\n\t}\n\n\t// Basic fetch\n\tif (!parsedURL.protocol || !parsedURL.hostname) {\n\t\tthrow new TypeError('Only absolute URLs are supported');\n\t}\n\n\tif (!/^https?:$/.test(parsedURL.protocol)) {\n\t\tthrow new TypeError('Only HTTP(S) protocols are supported');\n\t}\n\n\tif (request.signal && request.body instanceof Stream.Readable && !streamDestructionSupported) {\n\t\tthrow new Error('Cancellation of streamed requests with AbortSignal is not supported in node < 8');\n\t}\n\n\t// HTTP-network-or-cache fetch steps 2.4-2.7\n\tlet contentLengthValue = null;\n\tif (request.body == null && /^(POST|PUT)$/i.test(request.method)) {\n\t\tcontentLengthValue = '0';\n\t}\n\tif (request.body != null) {\n\t\tconst totalBytes = getTotalBytes(request);\n\t\tif (typeof totalBytes === 'number') {\n\t\t\tcontentLengthValue = String(totalBytes);\n\t\t}\n\t}\n\tif (contentLengthValue) {\n\t\theaders.set('Content-Length', contentLengthValue);\n\t}\n\n\t// HTTP-network-or-cache fetch step 2.11\n\tif (!headers.has('User-Agent')) {\n\t\theaders.set('User-Agent', 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)');\n\t}\n\n\t// HTTP-network-or-cache fetch step 2.15\n\tif (request.compress && !headers.has('Accept-Encoding')) {\n\t\theaders.set('Accept-Encoding', 'gzip,deflate');\n\t}\n\n\tlet agent = request.agent;\n\tif (typeof agent === 'function') {\n\t\tagent = agent(parsedURL);\n\t}\n\n\tif (!headers.has('Connection') && !agent) {\n\t\theaders.set('Connection', 'close');\n\t}\n\n\t// HTTP-network fetch step 4.2\n\t// chunked encoding is handled by Node.js\n\n\treturn Object.assign({}, parsedURL, {\n\t\tmethod: request.method,\n\t\theaders: exportNodeCompatibleHeaders(headers),\n\t\tagent\n\t});\n}\n\n/**\n * abort-error.js\n *\n * AbortError interface for cancelled requests\n */\n\n/**\n * Create AbortError instance\n *\n * @param   String      message      Error message for human\n * @return  AbortError\n */\nfunction AbortError(message) {\n  Error.call(this, message);\n\n  this.type = 'aborted';\n  this.message = message;\n\n  // hide custom error implementation details from end-users\n  Error.captureStackTrace(this, this.constructor);\n}\n\nAbortError.prototype = Object.create(Error.prototype);\nAbortError.prototype.constructor = AbortError;\nAbortError.prototype.name = 'AbortError';\n\nconst URL$1 = Url.URL || whatwgUrl.URL;\n\n// fix an issue where \"PassThrough\", \"resolve\" aren't a named export for node <10\nconst PassThrough$1 = Stream.PassThrough;\n\nconst isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {\n\tconst orig = new URL$1(original).hostname;\n\tconst dest = new URL$1(destination).hostname;\n\n\treturn orig === dest || orig[orig.length - dest.length - 1] === '.' && orig.endsWith(dest);\n};\n\n/**\n * isSameProtocol reports whether the two provided URLs use the same protocol.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */\nconst isSameProtocol = function isSameProtocol(destination, original) {\n\tconst orig = new URL$1(original).protocol;\n\tconst dest = new URL$1(destination).protocol;\n\n\treturn orig === dest;\n};\n\n/**\n * Fetch function\n *\n * @param   Mixed    url   Absolute url or Request instance\n * @param   Object   opts  Fetch options\n * @return  Promise\n */\nfunction fetch(url, opts) {\n\n\t// allow custom promise\n\tif (!fetch.Promise) {\n\t\tthrow new Error('native promise missing, set fetch.Promise to your favorite alternative');\n\t}\n\n\tBody.Promise = fetch.Promise;\n\n\t// wrap http.request into fetch\n\treturn new fetch.Promise(function (resolve, reject) {\n\t\t// build request object\n\t\tconst request = new Request(url, opts);\n\t\tconst options = getNodeRequestOptions(request);\n\n\t\tconst send = (options.protocol === 'https:' ? https : http).request;\n\t\tconst signal = request.signal;\n\n\t\tlet response = null;\n\n\t\tconst abort = function abort() {\n\t\t\tlet error = new AbortError('The user aborted a request.');\n\t\t\treject(error);\n\t\t\tif (request.body && request.body instanceof Stream.Readable) {\n\t\t\t\tdestroyStream(request.body, error);\n\t\t\t}\n\t\t\tif (!response || !response.body) return;\n\t\t\tresponse.body.emit('error', error);\n\t\t};\n\n\t\tif (signal && signal.aborted) {\n\t\t\tabort();\n\t\t\treturn;\n\t\t}\n\n\t\tconst abortAndFinalize = function abortAndFinalize() {\n\t\t\tabort();\n\t\t\tfinalize();\n\t\t};\n\n\t\t// send request\n\t\tconst req = send(options);\n\t\tlet reqTimeout;\n\n\t\tif (signal) {\n\t\t\tsignal.addEventListener('abort', abortAndFinalize);\n\t\t}\n\n\t\tfunction finalize() {\n\t\t\treq.abort();\n\t\t\tif (signal) signal.removeEventListener('abort', abortAndFinalize);\n\t\t\tclearTimeout(reqTimeout);\n\t\t}\n\n\t\tif (request.timeout) {\n\t\t\treq.once('socket', function (socket) {\n\t\t\t\treqTimeout = setTimeout(function () {\n\t\t\t\t\treject(new FetchError(`network timeout at: ${request.url}`, 'request-timeout'));\n\t\t\t\t\tfinalize();\n\t\t\t\t}, request.timeout);\n\t\t\t});\n\t\t}\n\n\t\treq.on('error', function (err) {\n\t\t\treject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, 'system', err));\n\n\t\t\tif (response && response.body) {\n\t\t\t\tdestroyStream(response.body, err);\n\t\t\t}\n\n\t\t\tfinalize();\n\t\t});\n\n\t\tfixResponseChunkedTransferBadEnding(req, function (err) {\n\t\t\tif (signal && signal.aborted) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (response && response.body) {\n\t\t\t\tdestroyStream(response.body, err);\n\t\t\t}\n\t\t});\n\n\t\t/* c8 ignore next 18 */\n\t\tif (parseInt(process.version.substring(1)) < 14) {\n\t\t\t// Before Node.js 14, pipeline() does not fully support async iterators and does not always\n\t\t\t// properly handle when the socket close/end events are out of order.\n\t\t\treq.on('socket', function (s) {\n\t\t\t\ts.addListener('close', function (hadError) {\n\t\t\t\t\t// if a data listener is still present we didn't end cleanly\n\t\t\t\t\tconst hasDataListener = s.listenerCount('data') > 0;\n\n\t\t\t\t\t// if end happened before close but the socket didn't emit an error, do it now\n\t\t\t\t\tif (response && hasDataListener && !hadError && !(signal && signal.aborted)) {\n\t\t\t\t\t\tconst err = new Error('Premature close');\n\t\t\t\t\t\terr.code = 'ERR_STREAM_PREMATURE_CLOSE';\n\t\t\t\t\t\tresponse.body.emit('error', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\n\t\treq.on('response', function (res) {\n\t\t\tclearTimeout(reqTimeout);\n\n\t\t\tconst headers = createHeadersLenient(res.headers);\n\n\t\t\t// HTTP fetch step 5\n\t\t\tif (fetch.isRedirect(res.statusCode)) {\n\t\t\t\t// HTTP fetch step 5.2\n\t\t\t\tconst location = headers.get('Location');\n\n\t\t\t\t// HTTP fetch step 5.3\n\t\t\t\tlet locationURL = null;\n\t\t\t\ttry {\n\t\t\t\t\tlocationURL = location === null ? null : new URL$1(location, request.url).toString();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// error here can only be invalid URL in Location: header\n\t\t\t\t\t// do not throw when options.redirect == manual\n\t\t\t\t\t// let the user extract the errorneous redirect URL\n\t\t\t\t\tif (request.redirect !== 'manual') {\n\t\t\t\t\t\treject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, 'invalid-redirect'));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// HTTP fetch step 5.5\n\t\t\t\tswitch (request.redirect) {\n\t\t\t\t\tcase 'error':\n\t\t\t\t\t\treject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, 'no-redirect'));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\tcase 'manual':\n\t\t\t\t\t\t// node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.\n\t\t\t\t\t\tif (locationURL !== null) {\n\t\t\t\t\t\t\t// handle corrupted header\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\theaders.set('Location', locationURL);\n\t\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\t\t// istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request\n\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'follow':\n\t\t\t\t\t\t// HTTP-redirect fetch step 2\n\t\t\t\t\t\tif (locationURL === null) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 5\n\t\t\t\t\t\tif (request.counter >= request.follow) {\n\t\t\t\t\t\t\treject(new FetchError(`maximum redirect reached at: ${request.url}`, 'max-redirect'));\n\t\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 6 (counter increment)\n\t\t\t\t\t\t// Create a new Request object.\n\t\t\t\t\t\tconst requestOpts = {\n\t\t\t\t\t\t\theaders: new Headers(request.headers),\n\t\t\t\t\t\t\tfollow: request.follow,\n\t\t\t\t\t\t\tcounter: request.counter + 1,\n\t\t\t\t\t\t\tagent: request.agent,\n\t\t\t\t\t\t\tcompress: request.compress,\n\t\t\t\t\t\t\tmethod: request.method,\n\t\t\t\t\t\t\tbody: request.body,\n\t\t\t\t\t\t\tsignal: request.signal,\n\t\t\t\t\t\t\ttimeout: request.timeout,\n\t\t\t\t\t\t\tsize: request.size\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\tif (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {\n\t\t\t\t\t\t\tfor (const name of ['authorization', 'www-authenticate', 'cookie', 'cookie2']) {\n\t\t\t\t\t\t\t\trequestOpts.headers.delete(name);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 9\n\t\t\t\t\t\tif (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {\n\t\t\t\t\t\t\treject(new FetchError('Cannot follow redirect with body being a readable stream', 'unsupported-redirect'));\n\t\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 11\n\t\t\t\t\t\tif (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === 'POST') {\n\t\t\t\t\t\t\trequestOpts.method = 'GET';\n\t\t\t\t\t\t\trequestOpts.body = undefined;\n\t\t\t\t\t\t\trequestOpts.headers.delete('content-length');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 15\n\t\t\t\t\t\tresolve(fetch(new Request(locationURL, requestOpts)));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// prepare response\n\t\t\tres.once('end', function () {\n\t\t\t\tif (signal) signal.removeEventListener('abort', abortAndFinalize);\n\t\t\t});\n\t\t\tlet body = res.pipe(new PassThrough$1());\n\n\t\t\tconst response_options = {\n\t\t\t\turl: request.url,\n\t\t\t\tstatus: res.statusCode,\n\t\t\t\tstatusText: res.statusMessage,\n\t\t\t\theaders: headers,\n\t\t\t\tsize: request.size,\n\t\t\t\ttimeout: request.timeout,\n\t\t\t\tcounter: request.counter\n\t\t\t};\n\n\t\t\t// HTTP-network fetch step ********\n\t\t\tconst codings = headers.get('Content-Encoding');\n\n\t\t\t// HTTP-network fetch step ********: handle content codings\n\n\t\t\t// in following scenarios we ignore compression support\n\t\t\t// 1. compression support is disabled\n\t\t\t// 2. HEAD request\n\t\t\t// 3. no Content-Encoding header\n\t\t\t// 4. no content response (204)\n\t\t\t// 5. content not modified response (304)\n\t\t\tif (!request.compress || request.method === 'HEAD' || codings === null || res.statusCode === 204 || res.statusCode === 304) {\n\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// For Node v6+\n\t\t\t// Be less strict when decoding compressed responses, since sometimes\n\t\t\t// servers send slightly invalid responses that are still accepted\n\t\t\t// by common browsers.\n\t\t\t// Always using Z_SYNC_FLUSH is what cURL does.\n\t\t\tconst zlibOptions = {\n\t\t\t\tflush: zlib.Z_SYNC_FLUSH,\n\t\t\t\tfinishFlush: zlib.Z_SYNC_FLUSH\n\t\t\t};\n\n\t\t\t// for gzip\n\t\t\tif (codings == 'gzip' || codings == 'x-gzip') {\n\t\t\t\tbody = body.pipe(zlib.createGunzip(zlibOptions));\n\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// for deflate\n\t\t\tif (codings == 'deflate' || codings == 'x-deflate') {\n\t\t\t\t// handle the infamous raw deflate response from old servers\n\t\t\t\t// a hack for old IIS and Apache servers\n\t\t\t\tconst raw = res.pipe(new PassThrough$1());\n\t\t\t\traw.once('data', function (chunk) {\n\t\t\t\t\t// see http://stackoverflow.com/questions/37519828\n\t\t\t\t\tif ((chunk[0] & 0x0F) === 0x08) {\n\t\t\t\t\t\tbody = body.pipe(zlib.createInflate());\n\t\t\t\t\t} else {\n\t\t\t\t\t\tbody = body.pipe(zlib.createInflateRaw());\n\t\t\t\t\t}\n\t\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\t\tresolve(response);\n\t\t\t\t});\n\t\t\t\traw.on('end', function () {\n\t\t\t\t\t// some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.\n\t\t\t\t\tif (!response) {\n\t\t\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\t\t\tresolve(response);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// for br\n\t\t\tif (codings == 'br' && typeof zlib.createBrotliDecompress === 'function') {\n\t\t\t\tbody = body.pipe(zlib.createBrotliDecompress());\n\t\t\t\tresponse = new Response(body, response_options);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// otherwise, use response as-is\n\t\t\tresponse = new Response(body, response_options);\n\t\t\tresolve(response);\n\t\t});\n\n\t\twriteToStream(req, request);\n\t});\n}\nfunction fixResponseChunkedTransferBadEnding(request, errorCallback) {\n\tlet socket;\n\n\trequest.on('socket', function (s) {\n\t\tsocket = s;\n\t});\n\n\trequest.on('response', function (response) {\n\t\tconst headers = response.headers;\n\n\t\tif (headers['transfer-encoding'] === 'chunked' && !headers['content-length']) {\n\t\t\tresponse.once('close', function (hadError) {\n\t\t\t\t// tests for socket presence, as in some situations the\n\t\t\t\t// the 'socket' event is not triggered for the request\n\t\t\t\t// (happens in deno), avoids `TypeError`\n\t\t\t\t// if a data listener is still present we didn't end cleanly\n\t\t\t\tconst hasDataListener = socket && socket.listenerCount('data') > 0;\n\n\t\t\t\tif (hasDataListener && !hadError) {\n\t\t\t\t\tconst err = new Error('Premature close');\n\t\t\t\t\terr.code = 'ERR_STREAM_PREMATURE_CLOSE';\n\t\t\t\t\terrorCallback(err);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t});\n}\n\nfunction destroyStream(stream, err) {\n\tif (stream.destroy) {\n\t\tstream.destroy(err);\n\t} else {\n\t\t// node < 8\n\t\tstream.emit('error', err);\n\t\tstream.end();\n\t}\n}\n\n/**\n * Redirect code matching\n *\n * @param   Number   code  Status code\n * @return  Boolean\n */\nfetch.isRedirect = function (code) {\n\treturn code === 301 || code === 302 || code === 303 || code === 307 || code === 308;\n};\n\n// expose Promise\nfetch.Promise = global.Promise;\n\nexport default fetch;\nexport { Headers, Request, Response, FetchError };\n"], "mappings": ";;;;;;;;;;;;;AAAA,oBAAmB;AACnB,kBAAiB;AACjB,iBAAgB;AAChB,wBAAsB;AACtB,mBAAkB;AAClB,kBAAiB;AAKjB,IAAM,WAAW,cAAAA,QAAO;AAExB,IAAM,SAAS,OAAO,QAAQ;AAC9B,IAAM,OAAO,OAAO,MAAM;AAE1B,IAAM,OAAN,MAAM,MAAK;AAAA,EACV,cAAc;AACb,SAAK,IAAI,IAAI;AAEb,UAAM,YAAY,UAAU,CAAC;AAC7B,UAAM,UAAU,UAAU,CAAC;AAE3B,UAAM,UAAU,CAAC;AACjB,QAAI,OAAO;AAEX,QAAI,WAAW;AACd,YAAM,IAAI;AACV,YAAM,SAAS,OAAO,EAAE,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,cAAM,UAAU,EAAE,CAAC;AACnB,YAAI;AACJ,YAAI,mBAAmB,QAAQ;AAC9B,mBAAS;AAAA,QACV,WAAW,YAAY,OAAO,OAAO,GAAG;AACvC,mBAAS,OAAO,KAAK,QAAQ,QAAQ,QAAQ,YAAY,QAAQ,UAAU;AAAA,QAC5E,WAAW,mBAAmB,aAAa;AAC1C,mBAAS,OAAO,KAAK,OAAO;AAAA,QAC7B,WAAW,mBAAmB,OAAM;AACnC,mBAAS,QAAQ,MAAM;AAAA,QACxB,OAAO;AACN,mBAAS,OAAO,KAAK,OAAO,YAAY,WAAW,UAAU,OAAO,OAAO,CAAC;AAAA,QAC7E;AACA,gBAAQ,OAAO;AACf,gBAAQ,KAAK,MAAM;AAAA,MACpB;AAAA,IACD;AAEA,SAAK,MAAM,IAAI,OAAO,OAAO,OAAO;AAEpC,QAAI,OAAO,WAAW,QAAQ,SAAS,UAAa,OAAO,QAAQ,IAAI,EAAE,YAAY;AACrF,QAAI,QAAQ,CAAC,mBAAmB,KAAK,IAAI,GAAG;AAC3C,WAAK,IAAI,IAAI;AAAA,IACd;AAAA,EACD;AAAA,EACA,IAAI,OAAO;AACV,WAAO,KAAK,MAAM,EAAE;AAAA,EACrB;AAAA,EACA,IAAI,OAAO;AACV,WAAO,KAAK,IAAI;AAAA,EACjB;AAAA,EACA,OAAO;AACN,WAAO,QAAQ,QAAQ,KAAK,MAAM,EAAE,SAAS,CAAC;AAAA,EAC/C;AAAA,EACA,cAAc;AACb,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,KAAK,IAAI,OAAO,MAAM,IAAI,YAAY,IAAI,aAAa,IAAI,UAAU;AAC3E,WAAO,QAAQ,QAAQ,EAAE;AAAA,EAC1B;AAAA,EACA,SAAS;AACR,UAAM,WAAW,IAAI,SAAS;AAC9B,aAAS,QAAQ,WAAY;AAAA,IAAC;AAC9B,aAAS,KAAK,KAAK,MAAM,CAAC;AAC1B,aAAS,KAAK,IAAI;AAClB,WAAO;AAAA,EACR;AAAA,EACA,WAAW;AACV,WAAO;AAAA,EACR;AAAA,EACA,QAAQ;AACP,UAAM,OAAO,KAAK;AAElB,UAAM,QAAQ,UAAU,CAAC;AACzB,UAAM,MAAM,UAAU,CAAC;AACvB,QAAI,eAAe;AACnB,QAAI,UAAU,QAAW;AACxB,sBAAgB;AAAA,IACjB,WAAW,QAAQ,GAAG;AACrB,sBAAgB,KAAK,IAAI,OAAO,OAAO,CAAC;AAAA,IACzC,OAAO;AACN,sBAAgB,KAAK,IAAI,OAAO,IAAI;AAAA,IACrC;AACA,QAAI,QAAQ,QAAW;AACtB,oBAAc;AAAA,IACf,WAAW,MAAM,GAAG;AACnB,oBAAc,KAAK,IAAI,OAAO,KAAK,CAAC;AAAA,IACrC,OAAO;AACN,oBAAc,KAAK,IAAI,KAAK,IAAI;AAAA,IACjC;AACA,UAAM,OAAO,KAAK,IAAI,cAAc,eAAe,CAAC;AAEpD,UAAM,SAAS,KAAK,MAAM;AAC1B,UAAM,eAAe,OAAO,MAAM,eAAe,gBAAgB,IAAI;AACrE,UAAM,OAAO,IAAI,MAAK,CAAC,GAAG,EAAE,MAAM,UAAU,CAAC,EAAE,CAAC;AAChD,SAAK,MAAM,IAAI;AACf,WAAO;AAAA,EACR;AACD;AAEA,OAAO,iBAAiB,KAAK,WAAW;AAAA,EACvC,MAAM,EAAE,YAAY,KAAK;AAAA,EACzB,MAAM,EAAE,YAAY,KAAK;AAAA,EACzB,OAAO,EAAE,YAAY,KAAK;AAC3B,CAAC;AAED,OAAO,eAAe,KAAK,WAAW,OAAO,aAAa;AAAA,EACzD,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AACf,CAAC;AAgBD,SAAS,WAAW,SAAS,MAAM,aAAa;AAC9C,QAAM,KAAK,MAAM,OAAO;AAExB,OAAK,UAAU;AACf,OAAK,OAAO;AAGZ,MAAI,aAAa;AACf,SAAK,OAAO,KAAK,QAAQ,YAAY;AAAA,EACvC;AAGA,QAAM,kBAAkB,MAAM,KAAK,WAAW;AAChD;AAEA,WAAW,YAAY,OAAO,OAAO,MAAM,SAAS;AACpD,WAAW,UAAU,cAAc;AACnC,WAAW,UAAU,OAAO;AAE5B,IAAI;AAEJ,IAAM,YAAY,OAAO,gBAAgB;AAGzC,IAAM,cAAc,cAAAA,QAAO;AAW3B,SAAS,KAAK,MAAM;AACnB,MAAI,QAAQ;AAEZ,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,YAAY,KAAK;AAErB,MAAI,OAAO,cAAc,SAAY,IAAI;AACzC,MAAI,eAAe,KAAK;AACxB,MAAI,UAAU,iBAAiB,SAAY,IAAI;AAE/C,MAAI,QAAQ,MAAM;AAEjB,WAAO;AAAA,EACR,WAAW,kBAAkB,IAAI,GAAG;AAEnC,WAAO,OAAO,KAAK,KAAK,SAAS,CAAC;AAAA,EACnC,WAAW,OAAO,IAAI;AAAG;AAAA,WAAW,OAAO,SAAS,IAAI;AAAG;AAAA,WAAW,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,wBAAwB;AAEtI,WAAO,OAAO,KAAK,IAAI;AAAA,EACxB,WAAW,YAAY,OAAO,IAAI,GAAG;AAEpC,WAAO,OAAO,KAAK,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AAAA,EACjE,WAAW,gBAAgB,cAAAA;AAAQ;AAAA,OAAO;AAGzC,WAAO,OAAO,KAAK,OAAO,IAAI,CAAC;AAAA,EAChC;AACA,OAAK,SAAS,IAAI;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,EACR;AACA,OAAK,OAAO;AACZ,OAAK,UAAU;AAEf,MAAI,gBAAgB,cAAAA,SAAQ;AAC3B,SAAK,GAAG,SAAS,SAAU,KAAK;AAC/B,YAAM,QAAQ,IAAI,SAAS,eAAe,MAAM,IAAI,WAAW,+CAA+C,MAAM,GAAG,KAAK,IAAI,OAAO,IAAI,UAAU,GAAG;AACxJ,YAAM,SAAS,EAAE,QAAQ;AAAA,IAC1B,CAAC;AAAA,EACF;AACD;AAEA,KAAK,YAAY;AAAA,EAChB,IAAI,OAAO;AACV,WAAO,KAAK,SAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,WAAW;AACd,WAAO,KAAK,SAAS,EAAE;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACb,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,SAAU,KAAK;AACjD,aAAO,IAAI,OAAO,MAAM,IAAI,YAAY,IAAI,aAAa,IAAI,UAAU;AAAA,IACxE,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACN,QAAI,KAAK,KAAK,WAAW,KAAK,QAAQ,IAAI,cAAc,KAAK;AAC7D,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,SAAU,KAAK;AACjD,aAAO,OAAO;AAAA;AAAA,QAEd,IAAI,KAAK,CAAC,GAAG;AAAA,UACZ,MAAM,GAAG,YAAY;AAAA,QACtB,CAAC;AAAA,QAAG;AAAA,UACH,CAAC,MAAM,GAAG;AAAA,QACX;AAAA,MAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACN,QAAI,SAAS;AAEb,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,SAAU,QAAQ;AACpD,UAAI;AACH,eAAO,KAAK,MAAM,OAAO,SAAS,CAAC;AAAA,MACpC,SAAS,KAAK;AACb,eAAO,KAAK,QAAQ,OAAO,IAAI,WAAW,iCAAiC,OAAO,GAAG,YAAY,IAAI,OAAO,IAAI,cAAc,CAAC;AAAA,MAChI;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACN,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,SAAU,QAAQ;AACpD,aAAO,OAAO,SAAS;AAAA,IACxB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACR,WAAO,YAAY,KAAK,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB;AACf,QAAI,SAAS;AAEb,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,SAAU,QAAQ;AACpD,aAAO,YAAY,QAAQ,OAAO,OAAO;AAAA,IAC1C,CAAC;AAAA,EACF;AACD;AAGA,OAAO,iBAAiB,KAAK,WAAW;AAAA,EACvC,MAAM,EAAE,YAAY,KAAK;AAAA,EACzB,UAAU,EAAE,YAAY,KAAK;AAAA,EAC7B,aAAa,EAAE,YAAY,KAAK;AAAA,EAChC,MAAM,EAAE,YAAY,KAAK;AAAA,EACzB,MAAM,EAAE,YAAY,KAAK;AAAA,EACzB,MAAM,EAAE,YAAY,KAAK;AAC1B,CAAC;AAED,KAAK,QAAQ,SAAU,OAAO;AAC7B,aAAW,QAAQ,OAAO,oBAAoB,KAAK,SAAS,GAAG;AAE9D,QAAI,EAAE,QAAQ,QAAQ;AACrB,YAAM,OAAO,OAAO,yBAAyB,KAAK,WAAW,IAAI;AACjE,aAAO,eAAe,OAAO,MAAM,IAAI;AAAA,IACxC;AAAA,EACD;AACD;AASA,SAAS,cAAc;AACtB,MAAI,SAAS;AAEb,MAAI,KAAK,SAAS,EAAE,WAAW;AAC9B,WAAO,KAAK,QAAQ,OAAO,IAAI,UAAU,0BAA0B,KAAK,GAAG,EAAE,CAAC;AAAA,EAC/E;AAEA,OAAK,SAAS,EAAE,YAAY;AAE5B,MAAI,KAAK,SAAS,EAAE,OAAO;AAC1B,WAAO,KAAK,QAAQ,OAAO,KAAK,SAAS,EAAE,KAAK;AAAA,EACjD;AAEA,MAAI,OAAO,KAAK;AAGhB,MAAI,SAAS,MAAM;AAClB,WAAO,KAAK,QAAQ,QAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,EAC5C;AAGA,MAAI,OAAO,IAAI,GAAG;AACjB,WAAO,KAAK,OAAO;AAAA,EACpB;AAGA,MAAI,OAAO,SAAS,IAAI,GAAG;AAC1B,WAAO,KAAK,QAAQ,QAAQ,IAAI;AAAA,EACjC;AAGA,MAAI,EAAE,gBAAgB,cAAAA,UAAS;AAC9B,WAAO,KAAK,QAAQ,QAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,EAC5C;AAIA,MAAI,QAAQ,CAAC;AACb,MAAI,aAAa;AACjB,MAAI,QAAQ;AAEZ,SAAO,IAAI,KAAK,QAAQ,SAAU,SAAS,QAAQ;AAClD,QAAI;AAGJ,QAAI,OAAO,SAAS;AACnB,mBAAa,WAAW,WAAY;AACnC,gBAAQ;AACR,eAAO,IAAI,WAAW,0CAA0C,OAAO,GAAG,UAAU,OAAO,OAAO,OAAO,cAAc,CAAC;AAAA,MACzH,GAAG,OAAO,OAAO;AAAA,IAClB;AAGA,SAAK,GAAG,SAAS,SAAU,KAAK;AAC/B,UAAI,IAAI,SAAS,cAAc;AAE9B,gBAAQ;AACR,eAAO,GAAG;AAAA,MACX,OAAO;AAEN,eAAO,IAAI,WAAW,+CAA+C,OAAO,GAAG,KAAK,IAAI,OAAO,IAAI,UAAU,GAAG,CAAC;AAAA,MAClH;AAAA,IACD,CAAC;AAED,SAAK,GAAG,QAAQ,SAAU,OAAO;AAChC,UAAI,SAAS,UAAU,MAAM;AAC5B;AAAA,MACD;AAEA,UAAI,OAAO,QAAQ,aAAa,MAAM,SAAS,OAAO,MAAM;AAC3D,gBAAQ;AACR,eAAO,IAAI,WAAW,mBAAmB,OAAO,GAAG,gBAAgB,OAAO,IAAI,IAAI,UAAU,CAAC;AAC7F;AAAA,MACD;AAEA,oBAAc,MAAM;AACpB,YAAM,KAAK,KAAK;AAAA,IACjB,CAAC;AAED,SAAK,GAAG,OAAO,WAAY;AAC1B,UAAI,OAAO;AACV;AAAA,MACD;AAEA,mBAAa,UAAU;AAEvB,UAAI;AACH,gBAAQ,OAAO,OAAO,OAAO,UAAU,CAAC;AAAA,MACzC,SAAS,KAAK;AAEb,eAAO,IAAI,WAAW,kDAAkD,OAAO,GAAG,KAAK,IAAI,OAAO,IAAI,UAAU,GAAG,CAAC;AAAA,MACrH;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACF;AAUA,SAAS,YAAY,QAAQ,SAAS;AACrC;AACC,UAAM,IAAI,MAAM,8EAA8E;AAAA,EAC/F;AAEA,QAAM,KAAK,QAAQ,IAAI,cAAc;AACrC,MAAI,UAAU;AACd,MAAI,KAAK;AAGT,MAAI,IAAI;AACP,UAAM,mBAAmB,KAAK,EAAE;AAAA,EACjC;AAGA,QAAM,OAAO,MAAM,GAAG,IAAI,EAAE,SAAS;AAGrC,MAAI,CAAC,OAAO,KAAK;AAChB,UAAM,iCAAiC,KAAK,GAAG;AAAA,EAChD;AAGA,MAAI,CAAC,OAAO,KAAK;AAChB,UAAM,yEAAyE,KAAK,GAAG;AACvF,QAAI,CAAC,KAAK;AACT,YAAM,yEAAyE,KAAK,GAAG;AACvF,UAAI,KAAK;AACR,YAAI,IAAI;AAAA,MACT;AAAA,IACD;AAEA,QAAI,KAAK;AACR,YAAM,gBAAgB,KAAK,IAAI,IAAI,CAAC;AAAA,IACrC;AAAA,EACD;AAGA,MAAI,CAAC,OAAO,KAAK;AAChB,UAAM,mCAAmC,KAAK,GAAG;AAAA,EAClD;AAGA,MAAI,KAAK;AACR,cAAU,IAAI,IAAI;AAIlB,QAAI,YAAY,YAAY,YAAY,OAAO;AAC9C,gBAAU;AAAA,IACX;AAAA,EACD;AAGA,SAAO,QAAQ,QAAQ,SAAS,OAAO,EAAE,SAAS;AACnD;AASA,SAAS,kBAAkB,KAAK;AAE/B,MAAI,OAAO,QAAQ,YAAY,OAAO,IAAI,WAAW,cAAc,OAAO,IAAI,WAAW,cAAc,OAAO,IAAI,QAAQ,cAAc,OAAO,IAAI,WAAW,cAAc,OAAO,IAAI,QAAQ,cAAc,OAAO,IAAI,QAAQ,YAAY;AAC3O,WAAO;AAAA,EACR;AAGA,SAAO,IAAI,YAAY,SAAS,qBAAqB,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,8BAA8B,OAAO,IAAI,SAAS;AAChJ;AAOA,SAAS,OAAO,KAAK;AACpB,SAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,SAAS,YAAY,OAAO,IAAI,WAAW,cAAc,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,YAAY,SAAS,YAAY,gBAAgB,KAAK,IAAI,YAAY,IAAI,KAAK,gBAAgB,KAAK,IAAI,OAAO,WAAW,CAAC;AAC/T;AAQA,SAAS,MAAM,UAAU;AACxB,MAAI,IAAI;AACR,MAAI,OAAO,SAAS;AAGpB,MAAI,SAAS,UAAU;AACtB,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AAIA,MAAI,gBAAgB,cAAAA,WAAU,OAAO,KAAK,gBAAgB,YAAY;AAErE,SAAK,IAAI,YAAY;AACrB,SAAK,IAAI,YAAY;AACrB,SAAK,KAAK,EAAE;AACZ,SAAK,KAAK,EAAE;AAEZ,aAAS,SAAS,EAAE,OAAO;AAC3B,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAWA,SAAS,mBAAmB,MAAM;AACjC,MAAI,SAAS,MAAM;AAElB,WAAO;AAAA,EACR,WAAW,OAAO,SAAS,UAAU;AAEpC,WAAO;AAAA,EACR,WAAW,kBAAkB,IAAI,GAAG;AAEnC,WAAO;AAAA,EACR,WAAW,OAAO,IAAI,GAAG;AAExB,WAAO,KAAK,QAAQ;AAAA,EACrB,WAAW,OAAO,SAAS,IAAI,GAAG;AAEjC,WAAO;AAAA,EACR,WAAW,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,wBAAwB;AAE3E,WAAO;AAAA,EACR,WAAW,YAAY,OAAO,IAAI,GAAG;AAEpC,WAAO;AAAA,EACR,WAAW,OAAO,KAAK,gBAAgB,YAAY;AAElD,WAAO,gCAAgC,KAAK,YAAY,CAAC;AAAA,EAC1D,WAAW,gBAAgB,cAAAA,SAAQ;AAGlC,WAAO;AAAA,EACR,OAAO;AAEN,WAAO;AAAA,EACR;AACD;AAWA,SAAS,cAAc,UAAU;AAChC,QAAM,OAAO,SAAS;AAGtB,MAAI,SAAS,MAAM;AAElB,WAAO;AAAA,EACR,WAAW,OAAO,IAAI,GAAG;AACxB,WAAO,KAAK;AAAA,EACb,WAAW,OAAO,SAAS,IAAI,GAAG;AAEjC,WAAO,KAAK;AAAA,EACb,WAAW,QAAQ,OAAO,KAAK,kBAAkB,YAAY;AAE5D,QAAI,KAAK,qBAAqB,KAAK,kBAAkB,UAAU;AAAA,IAC/D,KAAK,kBAAkB,KAAK,eAAe,GAAG;AAE7C,aAAO,KAAK,cAAc;AAAA,IAC3B;AACA,WAAO;AAAA,EACR,OAAO;AAEN,WAAO;AAAA,EACR;AACD;AAQA,SAAS,cAAc,MAAM,UAAU;AACtC,QAAM,OAAO,SAAS;AAGtB,MAAI,SAAS,MAAM;AAElB,SAAK,IAAI;AAAA,EACV,WAAW,OAAO,IAAI,GAAG;AACxB,SAAK,OAAO,EAAE,KAAK,IAAI;AAAA,EACxB,WAAW,OAAO,SAAS,IAAI,GAAG;AAEjC,SAAK,MAAM,IAAI;AACf,SAAK,IAAI;AAAA,EACV,OAAO;AAEN,SAAK,KAAK,IAAI;AAAA,EACf;AACD;AAGA,KAAK,UAAU,OAAO;AAQtB,IAAM,oBAAoB;AAC1B,IAAM,yBAAyB;AAE/B,SAAS,aAAa,MAAM;AAC3B,SAAO,GAAG,IAAI;AACd,MAAI,kBAAkB,KAAK,IAAI,KAAK,SAAS,IAAI;AAChD,UAAM,IAAI,UAAU,GAAG,IAAI,kCAAkC;AAAA,EAC9D;AACD;AAEA,SAAS,cAAc,OAAO;AAC7B,UAAQ,GAAG,KAAK;AAChB,MAAI,uBAAuB,KAAK,KAAK,GAAG;AACvC,UAAM,IAAI,UAAU,GAAG,KAAK,mCAAmC;AAAA,EAChE;AACD;AAUA,SAAS,KAAK,KAAK,MAAM;AACxB,SAAO,KAAK,YAAY;AACxB,aAAW,OAAO,KAAK;AACtB,QAAI,IAAI,YAAY,MAAM,MAAM;AAC/B,aAAO;AAAA,IACR;AAAA,EACD;AACA,SAAO;AACR;AAEA,IAAM,MAAM,OAAO,KAAK;AACxB,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOb,cAAc;AACb,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE/E,SAAK,GAAG,IAAI,uBAAO,OAAO,IAAI;AAE9B,QAAI,gBAAgB,UAAS;AAC5B,YAAM,aAAa,KAAK,IAAI;AAC5B,YAAM,cAAc,OAAO,KAAK,UAAU;AAE1C,iBAAW,cAAc,aAAa;AACrC,mBAAW,SAAS,WAAW,UAAU,GAAG;AAC3C,eAAK,OAAO,YAAY,KAAK;AAAA,QAC9B;AAAA,MACD;AAEA;AAAA,IACD;AAIA,QAAI,QAAQ;AAAM;AAAA,aAAW,OAAO,SAAS,UAAU;AACtD,YAAM,SAAS,KAAK,OAAO,QAAQ;AACnC,UAAI,UAAU,MAAM;AACnB,YAAI,OAAO,WAAW,YAAY;AACjC,gBAAM,IAAI,UAAU,+BAA+B;AAAA,QACpD;AAIA,cAAM,QAAQ,CAAC;AACf,mBAAW,QAAQ,MAAM;AACxB,cAAI,OAAO,SAAS,YAAY,OAAO,KAAK,OAAO,QAAQ,MAAM,YAAY;AAC5E,kBAAM,IAAI,UAAU,mCAAmC;AAAA,UACxD;AACA,gBAAM,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA,QAC5B;AAEA,mBAAW,QAAQ,OAAO;AACzB,cAAI,KAAK,WAAW,GAAG;AACtB,kBAAM,IAAI,UAAU,6CAA6C;AAAA,UAClE;AACA,eAAK,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,QAC7B;AAAA,MACD,OAAO;AAEN,mBAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACpC,gBAAM,QAAQ,KAAK,GAAG;AACtB,eAAK,OAAO,KAAK,KAAK;AAAA,QACvB;AAAA,MACD;AAAA,IACD,OAAO;AACN,YAAM,IAAI,UAAU,wCAAwC;AAAA,IAC7D;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACT,WAAO,GAAG,IAAI;AACd,iBAAa,IAAI;AACjB,UAAM,MAAM,KAAK,KAAK,GAAG,GAAG,IAAI;AAChC,QAAI,QAAQ,QAAW;AACtB,aAAO;AAAA,IACR;AAEA,WAAO,KAAK,GAAG,EAAE,GAAG,EAAE,KAAK,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU;AACjB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAElF,QAAI,QAAQ,WAAW,IAAI;AAC3B,QAAI,IAAI;AACR,WAAO,IAAI,MAAM,QAAQ;AACxB,UAAI,WAAW,MAAM,CAAC;AACtB,YAAM,OAAO,SAAS,CAAC,GACjB,QAAQ,SAAS,CAAC;AAExB,eAAS,KAAK,SAAS,OAAO,MAAM,IAAI;AACxC,cAAQ,WAAW,IAAI;AACvB;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,MAAM,OAAO;AAChB,WAAO,GAAG,IAAI;AACd,YAAQ,GAAG,KAAK;AAChB,iBAAa,IAAI;AACjB,kBAAc,KAAK;AACnB,UAAM,MAAM,KAAK,KAAK,GAAG,GAAG,IAAI;AAChC,SAAK,GAAG,EAAE,QAAQ,SAAY,MAAM,IAAI,IAAI,CAAC,KAAK;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MAAM,OAAO;AACnB,WAAO,GAAG,IAAI;AACd,YAAQ,GAAG,KAAK;AAChB,iBAAa,IAAI;AACjB,kBAAc,KAAK;AACnB,UAAM,MAAM,KAAK,KAAK,GAAG,GAAG,IAAI;AAChC,QAAI,QAAQ,QAAW;AACtB,WAAK,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACN,WAAK,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK;AAAA,IACzB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM;AACT,WAAO,GAAG,IAAI;AACd,iBAAa,IAAI;AACjB,WAAO,KAAK,KAAK,GAAG,GAAG,IAAI,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM;AACZ,WAAO,GAAG,IAAI;AACd,iBAAa,IAAI;AACjB,UAAM,MAAM,KAAK,KAAK,GAAG,GAAG,IAAI;AAChC,QAAI,QAAQ,QAAW;AACtB,aAAO,KAAK,GAAG,EAAE,GAAG;AAAA,IACrB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM;AACL,WAAO,KAAK,GAAG;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO;AACN,WAAO,sBAAsB,MAAM,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACR,WAAO,sBAAsB,MAAM,OAAO;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,CAAC,OAAO,QAAQ,IAAI;AACnB,WAAO,sBAAsB,MAAM,WAAW;AAAA,EAC/C;AACD;AACA,QAAQ,UAAU,UAAU,QAAQ,UAAU,OAAO,QAAQ;AAE7D,OAAO,eAAe,QAAQ,WAAW,OAAO,aAAa;AAAA,EAC5D,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AACf,CAAC;AAED,OAAO,iBAAiB,QAAQ,WAAW;AAAA,EAC1C,KAAK,EAAE,YAAY,KAAK;AAAA,EACxB,SAAS,EAAE,YAAY,KAAK;AAAA,EAC5B,KAAK,EAAE,YAAY,KAAK;AAAA,EACxB,QAAQ,EAAE,YAAY,KAAK;AAAA,EAC3B,KAAK,EAAE,YAAY,KAAK;AAAA,EACxB,QAAQ,EAAE,YAAY,KAAK;AAAA,EAC3B,MAAM,EAAE,YAAY,KAAK;AAAA,EACzB,QAAQ,EAAE,YAAY,KAAK;AAAA,EAC3B,SAAS,EAAE,YAAY,KAAK;AAC7B,CAAC;AAED,SAAS,WAAW,SAAS;AAC5B,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE/E,QAAM,OAAO,OAAO,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK;AAC5C,SAAO,KAAK,IAAI,SAAS,QAAQ,SAAU,GAAG;AAC7C,WAAO,EAAE,YAAY;AAAA,EACtB,IAAI,SAAS,UAAU,SAAU,GAAG;AACnC,WAAO,QAAQ,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI;AAAA,EACjC,IAAI,SAAU,GAAG;AAChB,WAAO,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,EACpD,CAAC;AACF;AAEA,IAAM,WAAW,OAAO,UAAU;AAElC,SAAS,sBAAsB,QAAQ,MAAM;AAC5C,QAAM,WAAW,OAAO,OAAO,wBAAwB;AACvD,WAAS,QAAQ,IAAI;AAAA,IACpB;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACR;AACA,SAAO;AACR;AAEA,IAAM,2BAA2B,OAAO,eAAe;AAAA,EACtD,OAAO;AAEN,QAAI,CAAC,QAAQ,OAAO,eAAe,IAAI,MAAM,0BAA0B;AACtE,YAAM,IAAI,UAAU,0CAA0C;AAAA,IAC/D;AAEA,QAAI,YAAY,KAAK,QAAQ;AAC7B,UAAM,SAAS,UAAU,QACnB,OAAO,UAAU,MACjB,QAAQ,UAAU;AAExB,UAAM,SAAS,WAAW,QAAQ,IAAI;AACtC,UAAM,MAAM,OAAO;AACnB,QAAI,SAAS,KAAK;AACjB,aAAO;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,MACP;AAAA,IACD;AAEA,SAAK,QAAQ,EAAE,QAAQ,QAAQ;AAE/B,WAAO;AAAA,MACN,OAAO,OAAO,KAAK;AAAA,MACnB,MAAM;AAAA,IACP;AAAA,EACD;AACD,GAAG,OAAO,eAAe,OAAO,eAAe,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,CAAC,CAAC;AAEtE,OAAO,eAAe,0BAA0B,OAAO,aAAa;AAAA,EACnE,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AACf,CAAC;AAQD,SAAS,4BAA4B,SAAS;AAC7C,QAAM,MAAM,OAAO,OAAO,EAAE,WAAW,KAAK,GAAG,QAAQ,GAAG,CAAC;AAI3D,QAAM,gBAAgB,KAAK,QAAQ,GAAG,GAAG,MAAM;AAC/C,MAAI,kBAAkB,QAAW;AAChC,QAAI,aAAa,IAAI,IAAI,aAAa,EAAE,CAAC;AAAA,EAC1C;AAEA,SAAO;AACR;AASA,SAAS,qBAAqB,KAAK;AAClC,QAAM,UAAU,IAAI,QAAQ;AAC5B,aAAW,QAAQ,OAAO,KAAK,GAAG,GAAG;AACpC,QAAI,kBAAkB,KAAK,IAAI,GAAG;AACjC;AAAA,IACD;AACA,QAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,GAAG;AAC7B,iBAAW,OAAO,IAAI,IAAI,GAAG;AAC5B,YAAI,uBAAuB,KAAK,GAAG,GAAG;AACrC;AAAA,QACD;AACA,YAAI,QAAQ,GAAG,EAAE,IAAI,MAAM,QAAW;AACrC,kBAAQ,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG;AAAA,QAC1B,OAAO;AACN,kBAAQ,GAAG,EAAE,IAAI,EAAE,KAAK,GAAG;AAAA,QAC5B;AAAA,MACD;AAAA,IACD,WAAW,CAAC,uBAAuB,KAAK,IAAI,IAAI,CAAC,GAAG;AACnD,cAAQ,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAChC;AAAA,EACD;AACA,SAAO;AACR;AAEA,IAAM,cAAc,OAAO,oBAAoB;AAG/C,IAAM,eAAe,YAAAC,QAAK;AAS1B,IAAM,WAAN,MAAM,UAAS;AAAA,EACd,cAAc;AACb,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC/E,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEhF,SAAK,KAAK,MAAM,MAAM,IAAI;AAE1B,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;AAExC,QAAI,QAAQ,QAAQ,CAAC,QAAQ,IAAI,cAAc,GAAG;AACjD,YAAM,cAAc,mBAAmB,IAAI;AAC3C,UAAI,aAAa;AAChB,gBAAQ,OAAO,gBAAgB,WAAW;AAAA,MAC3C;AAAA,IACD;AAEA,SAAK,WAAW,IAAI;AAAA,MACnB,KAAK,KAAK;AAAA,MACV;AAAA,MACA,YAAY,KAAK,cAAc,aAAa,MAAM;AAAA,MAClD;AAAA,MACA,SAAS,KAAK;AAAA,IACf;AAAA,EACD;AAAA,EAEA,IAAI,MAAM;AACT,WAAO,KAAK,WAAW,EAAE,OAAO;AAAA,EACjC;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK;AACR,WAAO,KAAK,WAAW,EAAE,UAAU,OAAO,KAAK,WAAW,EAAE,SAAS;AAAA,EACtE;AAAA,EAEA,IAAI,aAAa;AAChB,WAAO,KAAK,WAAW,EAAE,UAAU;AAAA,EACpC;AAAA,EAEA,IAAI,aAAa;AAChB,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA,EAEA,IAAI,UAAU;AACb,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACP,WAAO,IAAI,UAAS,MAAM,IAAI,GAAG;AAAA,MAChC,KAAK,KAAK;AAAA,MACV,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,SAAS,KAAK;AAAA,MACd,IAAI,KAAK;AAAA,MACT,YAAY,KAAK;AAAA,IAClB,CAAC;AAAA,EACF;AACD;AAEA,KAAK,MAAM,SAAS,SAAS;AAE7B,OAAO,iBAAiB,SAAS,WAAW;AAAA,EAC3C,KAAK,EAAE,YAAY,KAAK;AAAA,EACxB,QAAQ,EAAE,YAAY,KAAK;AAAA,EAC3B,IAAI,EAAE,YAAY,KAAK;AAAA,EACvB,YAAY,EAAE,YAAY,KAAK;AAAA,EAC/B,YAAY,EAAE,YAAY,KAAK;AAAA,EAC/B,SAAS,EAAE,YAAY,KAAK;AAAA,EAC5B,OAAO,EAAE,YAAY,KAAK;AAC3B,CAAC;AAED,OAAO,eAAe,SAAS,WAAW,OAAO,aAAa;AAAA,EAC7D,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AACf,CAAC;AAED,IAAM,cAAc,OAAO,mBAAmB;AAC9C,IAAM,MAAM,WAAAC,QAAI,OAAO,kBAAAC,QAAU;AAGjC,IAAM,YAAY,WAAAD,QAAI;AACtB,IAAM,aAAa,WAAAA,QAAI;AAQvB,SAAS,SAAS,QAAQ;AAMzB,MAAI,4BAA4B,KAAK,MAAM,GAAG;AAC7C,aAAS,IAAI,IAAI,MAAM,EAAE,SAAS;AAAA,EACnC;AAGA,SAAO,UAAU,MAAM;AACxB;AAEA,IAAM,6BAA6B,aAAa,cAAAF,QAAO,SAAS;AAQhE,SAAS,UAAU,OAAO;AACzB,SAAO,OAAO,UAAU,YAAY,OAAO,MAAM,WAAW,MAAM;AACnE;AAEA,SAAS,cAAc,QAAQ;AAC9B,QAAM,QAAQ,UAAU,OAAO,WAAW,YAAY,OAAO,eAAe,MAAM;AAClF,SAAO,CAAC,EAAE,SAAS,MAAM,YAAY,SAAS;AAC/C;AASA,IAAM,UAAN,MAAM,SAAQ;AAAA,EACb,YAAY,OAAO;AAClB,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEhF,QAAI;AAGJ,QAAI,CAAC,UAAU,KAAK,GAAG;AACtB,UAAI,SAAS,MAAM,MAAM;AAIxB,oBAAY,SAAS,MAAM,IAAI;AAAA,MAChC,OAAO;AAEN,oBAAY,SAAS,GAAG,KAAK,EAAE;AAAA,MAChC;AACA,cAAQ,CAAC;AAAA,IACV,OAAO;AACN,kBAAY,SAAS,MAAM,GAAG;AAAA,IAC/B;AAEA,QAAI,SAAS,KAAK,UAAU,MAAM,UAAU;AAC5C,aAAS,OAAO,YAAY;AAE5B,SAAK,KAAK,QAAQ,QAAQ,UAAU,KAAK,KAAK,MAAM,SAAS,UAAU,WAAW,SAAS,WAAW,SAAS;AAC9G,YAAM,IAAI,UAAU,+CAA+C;AAAA,IACpE;AAEA,QAAI,YAAY,KAAK,QAAQ,OAAO,KAAK,OAAO,UAAU,KAAK,KAAK,MAAM,SAAS,OAAO,MAAM,KAAK,IAAI;AAEzG,SAAK,KAAK,MAAM,WAAW;AAAA,MAC1B,SAAS,KAAK,WAAW,MAAM,WAAW;AAAA,MAC1C,MAAM,KAAK,QAAQ,MAAM,QAAQ;AAAA,IAClC,CAAC;AAED,UAAM,UAAU,IAAI,QAAQ,KAAK,WAAW,MAAM,WAAW,CAAC,CAAC;AAE/D,QAAI,aAAa,QAAQ,CAAC,QAAQ,IAAI,cAAc,GAAG;AACtD,YAAM,cAAc,mBAAmB,SAAS;AAChD,UAAI,aAAa;AAChB,gBAAQ,OAAO,gBAAgB,WAAW;AAAA,MAC3C;AAAA,IACD;AAEA,QAAI,SAAS,UAAU,KAAK,IAAI,MAAM,SAAS;AAC/C,QAAI,YAAY;AAAM,eAAS,KAAK;AAEpC,QAAI,UAAU,QAAQ,CAAC,cAAc,MAAM,GAAG;AAC7C,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACtE;AAEA,SAAK,WAAW,IAAI;AAAA,MACnB;AAAA,MACA,UAAU,KAAK,YAAY,MAAM,YAAY;AAAA,MAC7C;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAGA,SAAK,SAAS,KAAK,WAAW,SAAY,KAAK,SAAS,MAAM,WAAW,SAAY,MAAM,SAAS;AACpG,SAAK,WAAW,KAAK,aAAa,SAAY,KAAK,WAAW,MAAM,aAAa,SAAY,MAAM,WAAW;AAC9G,SAAK,UAAU,KAAK,WAAW,MAAM,WAAW;AAChD,SAAK,QAAQ,KAAK,SAAS,MAAM;AAAA,EAClC;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA,EAEA,IAAI,MAAM;AACT,WAAO,WAAW,KAAK,WAAW,EAAE,SAAS;AAAA,EAC9C;AAAA,EAEA,IAAI,UAAU;AACb,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA,EAEA,IAAI,WAAW;AACd,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO,KAAK,WAAW,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACP,WAAO,IAAI,SAAQ,IAAI;AAAA,EACxB;AACD;AAEA,KAAK,MAAM,QAAQ,SAAS;AAE5B,OAAO,eAAe,QAAQ,WAAW,OAAO,aAAa;AAAA,EAC5D,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AACf,CAAC;AAED,OAAO,iBAAiB,QAAQ,WAAW;AAAA,EAC1C,QAAQ,EAAE,YAAY,KAAK;AAAA,EAC3B,KAAK,EAAE,YAAY,KAAK;AAAA,EACxB,SAAS,EAAE,YAAY,KAAK;AAAA,EAC5B,UAAU,EAAE,YAAY,KAAK;AAAA,EAC7B,OAAO,EAAE,YAAY,KAAK;AAAA,EAC1B,QAAQ,EAAE,YAAY,KAAK;AAC5B,CAAC;AAQD,SAAS,sBAAsB,SAAS;AACvC,QAAM,YAAY,QAAQ,WAAW,EAAE;AACvC,QAAM,UAAU,IAAI,QAAQ,QAAQ,WAAW,EAAE,OAAO;AAGxD,MAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC3B,YAAQ,IAAI,UAAU,KAAK;AAAA,EAC5B;AAGA,MAAI,CAAC,UAAU,YAAY,CAAC,UAAU,UAAU;AAC/C,UAAM,IAAI,UAAU,kCAAkC;AAAA,EACvD;AAEA,MAAI,CAAC,YAAY,KAAK,UAAU,QAAQ,GAAG;AAC1C,UAAM,IAAI,UAAU,sCAAsC;AAAA,EAC3D;AAEA,MAAI,QAAQ,UAAU,QAAQ,gBAAgB,cAAAA,QAAO,YAAY,CAAC,4BAA4B;AAC7F,UAAM,IAAI,MAAM,iFAAiF;AAAA,EAClG;AAGA,MAAI,qBAAqB;AACzB,MAAI,QAAQ,QAAQ,QAAQ,gBAAgB,KAAK,QAAQ,MAAM,GAAG;AACjE,yBAAqB;AAAA,EACtB;AACA,MAAI,QAAQ,QAAQ,MAAM;AACzB,UAAM,aAAa,cAAc,OAAO;AACxC,QAAI,OAAO,eAAe,UAAU;AACnC,2BAAqB,OAAO,UAAU;AAAA,IACvC;AAAA,EACD;AACA,MAAI,oBAAoB;AACvB,YAAQ,IAAI,kBAAkB,kBAAkB;AAAA,EACjD;AAGA,MAAI,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC/B,YAAQ,IAAI,cAAc,wDAAwD;AAAA,EACnF;AAGA,MAAI,QAAQ,YAAY,CAAC,QAAQ,IAAI,iBAAiB,GAAG;AACxD,YAAQ,IAAI,mBAAmB,cAAc;AAAA,EAC9C;AAEA,MAAI,QAAQ,QAAQ;AACpB,MAAI,OAAO,UAAU,YAAY;AAChC,YAAQ,MAAM,SAAS;AAAA,EACxB;AAEA,MAAI,CAAC,QAAQ,IAAI,YAAY,KAAK,CAAC,OAAO;AACzC,YAAQ,IAAI,cAAc,OAAO;AAAA,EAClC;AAKA,SAAO,OAAO,OAAO,CAAC,GAAG,WAAW;AAAA,IACnC,QAAQ,QAAQ;AAAA,IAChB,SAAS,4BAA4B,OAAO;AAAA,IAC5C;AAAA,EACD,CAAC;AACF;AAcA,SAAS,WAAW,SAAS;AAC3B,QAAM,KAAK,MAAM,OAAO;AAExB,OAAK,OAAO;AACZ,OAAK,UAAU;AAGf,QAAM,kBAAkB,MAAM,KAAK,WAAW;AAChD;AAEA,WAAW,YAAY,OAAO,OAAO,MAAM,SAAS;AACpD,WAAW,UAAU,cAAc;AACnC,WAAW,UAAU,OAAO;AAE5B,IAAM,QAAQ,WAAAE,QAAI,OAAO,kBAAAC,QAAU;AAGnC,IAAM,gBAAgB,cAAAH,QAAO;AAE7B,IAAM,sBAAsB,SAASI,qBAAoB,aAAa,UAAU;AAC/E,QAAM,OAAO,IAAI,MAAM,QAAQ,EAAE;AACjC,QAAM,OAAO,IAAI,MAAM,WAAW,EAAE;AAEpC,SAAO,SAAS,QAAQ,KAAK,KAAK,SAAS,KAAK,SAAS,CAAC,MAAM,OAAO,KAAK,SAAS,IAAI;AAC1F;AASA,IAAM,iBAAiB,SAASC,gBAAe,aAAa,UAAU;AACrE,QAAM,OAAO,IAAI,MAAM,QAAQ,EAAE;AACjC,QAAM,OAAO,IAAI,MAAM,WAAW,EAAE;AAEpC,SAAO,SAAS;AACjB;AASA,SAAS,MAAM,KAAK,MAAM;AAGzB,MAAI,CAAC,MAAM,SAAS;AACnB,UAAM,IAAI,MAAM,wEAAwE;AAAA,EACzF;AAEA,OAAK,UAAU,MAAM;AAGrB,SAAO,IAAI,MAAM,QAAQ,SAAU,SAAS,QAAQ;AAEnD,UAAM,UAAU,IAAI,QAAQ,KAAK,IAAI;AACrC,UAAM,UAAU,sBAAsB,OAAO;AAE7C,UAAM,QAAQ,QAAQ,aAAa,WAAW,aAAAC,UAAQ,YAAAL,SAAM;AAC5D,UAAM,SAAS,QAAQ;AAEvB,QAAI,WAAW;AAEf,UAAM,QAAQ,SAASM,SAAQ;AAC9B,UAAI,QAAQ,IAAI,WAAW,6BAA6B;AACxD,aAAO,KAAK;AACZ,UAAI,QAAQ,QAAQ,QAAQ,gBAAgB,cAAAP,QAAO,UAAU;AAC5D,sBAAc,QAAQ,MAAM,KAAK;AAAA,MAClC;AACA,UAAI,CAAC,YAAY,CAAC,SAAS;AAAM;AACjC,eAAS,KAAK,KAAK,SAAS,KAAK;AAAA,IAClC;AAEA,QAAI,UAAU,OAAO,SAAS;AAC7B,YAAM;AACN;AAAA,IACD;AAEA,UAAM,mBAAmB,SAASQ,oBAAmB;AACpD,YAAM;AACN,eAAS;AAAA,IACV;AAGA,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI;AAEJ,QAAI,QAAQ;AACX,aAAO,iBAAiB,SAAS,gBAAgB;AAAA,IAClD;AAEA,aAAS,WAAW;AACnB,UAAI,MAAM;AACV,UAAI;AAAQ,eAAO,oBAAoB,SAAS,gBAAgB;AAChE,mBAAa,UAAU;AAAA,IACxB;AAEA,QAAI,QAAQ,SAAS;AACpB,UAAI,KAAK,UAAU,SAAU,QAAQ;AACpC,qBAAa,WAAW,WAAY;AACnC,iBAAO,IAAI,WAAW,uBAAuB,QAAQ,GAAG,IAAI,iBAAiB,CAAC;AAC9E,mBAAS;AAAA,QACV,GAAG,QAAQ,OAAO;AAAA,MACnB,CAAC;AAAA,IACF;AAEA,QAAI,GAAG,SAAS,SAAU,KAAK;AAC9B,aAAO,IAAI,WAAW,cAAc,QAAQ,GAAG,oBAAoB,IAAI,OAAO,IAAI,UAAU,GAAG,CAAC;AAEhG,UAAI,YAAY,SAAS,MAAM;AAC9B,sBAAc,SAAS,MAAM,GAAG;AAAA,MACjC;AAEA,eAAS;AAAA,IACV,CAAC;AAED,wCAAoC,KAAK,SAAU,KAAK;AACvD,UAAI,UAAU,OAAO,SAAS;AAC7B;AAAA,MACD;AAEA,UAAI,YAAY,SAAS,MAAM;AAC9B,sBAAc,SAAS,MAAM,GAAG;AAAA,MACjC;AAAA,IACD,CAAC;AAGD,QAAI,SAAS,QAAQ,QAAQ,UAAU,CAAC,CAAC,IAAI,IAAI;AAGhD,UAAI,GAAG,UAAU,SAAU,GAAG;AAC7B,UAAE,YAAY,SAAS,SAAU,UAAU;AAE1C,gBAAM,kBAAkB,EAAE,cAAc,MAAM,IAAI;AAGlD,cAAI,YAAY,mBAAmB,CAAC,YAAY,EAAE,UAAU,OAAO,UAAU;AAC5E,kBAAM,MAAM,IAAI,MAAM,iBAAiB;AACvC,gBAAI,OAAO;AACX,qBAAS,KAAK,KAAK,SAAS,GAAG;AAAA,UAChC;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,QAAI,GAAG,YAAY,SAAU,KAAK;AACjC,mBAAa,UAAU;AAEvB,YAAM,UAAU,qBAAqB,IAAI,OAAO;AAGhD,UAAI,MAAM,WAAW,IAAI,UAAU,GAAG;AAErC,cAAM,WAAW,QAAQ,IAAI,UAAU;AAGvC,YAAI,cAAc;AAClB,YAAI;AACH,wBAAc,aAAa,OAAO,OAAO,IAAI,MAAM,UAAU,QAAQ,GAAG,EAAE,SAAS;AAAA,QACpF,SAAS,KAAK;AAIb,cAAI,QAAQ,aAAa,UAAU;AAClC,mBAAO,IAAI,WAAW,wDAAwD,QAAQ,IAAI,kBAAkB,CAAC;AAC7G,qBAAS;AACT;AAAA,UACD;AAAA,QACD;AAGA,gBAAQ,QAAQ,UAAU;AAAA,UACzB,KAAK;AACJ,mBAAO,IAAI,WAAW,0EAA0E,QAAQ,GAAG,IAAI,aAAa,CAAC;AAC7H,qBAAS;AACT;AAAA,UACD,KAAK;AAEJ,gBAAI,gBAAgB,MAAM;AAEzB,kBAAI;AACH,wBAAQ,IAAI,YAAY,WAAW;AAAA,cACpC,SAAS,KAAK;AAEb,uBAAO,GAAG;AAAA,cACX;AAAA,YACD;AACA;AAAA,UACD,KAAK;AAEJ,gBAAI,gBAAgB,MAAM;AACzB;AAAA,YACD;AAGA,gBAAI,QAAQ,WAAW,QAAQ,QAAQ;AACtC,qBAAO,IAAI,WAAW,gCAAgC,QAAQ,GAAG,IAAI,cAAc,CAAC;AACpF,uBAAS;AACT;AAAA,YACD;AAIA,kBAAM,cAAc;AAAA,cACnB,SAAS,IAAI,QAAQ,QAAQ,OAAO;AAAA,cACpC,QAAQ,QAAQ;AAAA,cAChB,SAAS,QAAQ,UAAU;AAAA,cAC3B,OAAO,QAAQ;AAAA,cACf,UAAU,QAAQ;AAAA,cAClB,QAAQ,QAAQ;AAAA,cAChB,MAAM,QAAQ;AAAA,cACd,QAAQ,QAAQ;AAAA,cAChB,SAAS,QAAQ;AAAA,cACjB,MAAM,QAAQ;AAAA,YACf;AAEA,gBAAI,CAAC,oBAAoB,QAAQ,KAAK,WAAW,KAAK,CAAC,eAAe,QAAQ,KAAK,WAAW,GAAG;AAChG,yBAAW,QAAQ,CAAC,iBAAiB,oBAAoB,UAAU,SAAS,GAAG;AAC9E,4BAAY,QAAQ,OAAO,IAAI;AAAA,cAChC;AAAA,YACD;AAGA,gBAAI,IAAI,eAAe,OAAO,QAAQ,QAAQ,cAAc,OAAO,MAAM,MAAM;AAC9E,qBAAO,IAAI,WAAW,4DAA4D,sBAAsB,CAAC;AACzG,uBAAS;AACT;AAAA,YACD;AAGA,gBAAI,IAAI,eAAe,QAAQ,IAAI,eAAe,OAAO,IAAI,eAAe,QAAQ,QAAQ,WAAW,QAAQ;AAC9G,0BAAY,SAAS;AACrB,0BAAY,OAAO;AACnB,0BAAY,QAAQ,OAAO,gBAAgB;AAAA,YAC5C;AAGA,oBAAQ,MAAM,IAAI,QAAQ,aAAa,WAAW,CAAC,CAAC;AACpD,qBAAS;AACT;AAAA,QACF;AAAA,MACD;AAGA,UAAI,KAAK,OAAO,WAAY;AAC3B,YAAI;AAAQ,iBAAO,oBAAoB,SAAS,gBAAgB;AAAA,MACjE,CAAC;AACD,UAAI,OAAO,IAAI,KAAK,IAAI,cAAc,CAAC;AAEvC,YAAM,mBAAmB;AAAA,QACxB,KAAK,QAAQ;AAAA,QACb,QAAQ,IAAI;AAAA,QACZ,YAAY,IAAI;AAAA,QAChB;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ;AAAA,MAClB;AAGA,YAAM,UAAU,QAAQ,IAAI,kBAAkB;AAU9C,UAAI,CAAC,QAAQ,YAAY,QAAQ,WAAW,UAAU,YAAY,QAAQ,IAAI,eAAe,OAAO,IAAI,eAAe,KAAK;AAC3H,mBAAW,IAAI,SAAS,MAAM,gBAAgB;AAC9C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAOA,YAAM,cAAc;AAAA,QACnB,OAAO,YAAAC,QAAK;AAAA,QACZ,aAAa,YAAAA,QAAK;AAAA,MACnB;AAGA,UAAI,WAAW,UAAU,WAAW,UAAU;AAC7C,eAAO,KAAK,KAAK,YAAAA,QAAK,aAAa,WAAW,CAAC;AAC/C,mBAAW,IAAI,SAAS,MAAM,gBAAgB;AAC9C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAGA,UAAI,WAAW,aAAa,WAAW,aAAa;AAGnD,cAAM,MAAM,IAAI,KAAK,IAAI,cAAc,CAAC;AACxC,YAAI,KAAK,QAAQ,SAAU,OAAO;AAEjC,eAAK,MAAM,CAAC,IAAI,QAAU,GAAM;AAC/B,mBAAO,KAAK,KAAK,YAAAA,QAAK,cAAc,CAAC;AAAA,UACtC,OAAO;AACN,mBAAO,KAAK,KAAK,YAAAA,QAAK,iBAAiB,CAAC;AAAA,UACzC;AACA,qBAAW,IAAI,SAAS,MAAM,gBAAgB;AAC9C,kBAAQ,QAAQ;AAAA,QACjB,CAAC;AACD,YAAI,GAAG,OAAO,WAAY;AAEzB,cAAI,CAAC,UAAU;AACd,uBAAW,IAAI,SAAS,MAAM,gBAAgB;AAC9C,oBAAQ,QAAQ;AAAA,UACjB;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAGA,UAAI,WAAW,QAAQ,OAAO,YAAAA,QAAK,2BAA2B,YAAY;AACzE,eAAO,KAAK,KAAK,YAAAA,QAAK,uBAAuB,CAAC;AAC9C,mBAAW,IAAI,SAAS,MAAM,gBAAgB;AAC9C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAGA,iBAAW,IAAI,SAAS,MAAM,gBAAgB;AAC9C,cAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,kBAAc,KAAK,OAAO;AAAA,EAC3B,CAAC;AACF;AACA,SAAS,oCAAoC,SAAS,eAAe;AACpE,MAAI;AAEJ,UAAQ,GAAG,UAAU,SAAU,GAAG;AACjC,aAAS;AAAA,EACV,CAAC;AAED,UAAQ,GAAG,YAAY,SAAU,UAAU;AAC1C,UAAM,UAAU,SAAS;AAEzB,QAAI,QAAQ,mBAAmB,MAAM,aAAa,CAAC,QAAQ,gBAAgB,GAAG;AAC7E,eAAS,KAAK,SAAS,SAAU,UAAU;AAK1C,cAAM,kBAAkB,UAAU,OAAO,cAAc,MAAM,IAAI;AAEjE,YAAI,mBAAmB,CAAC,UAAU;AACjC,gBAAM,MAAM,IAAI,MAAM,iBAAiB;AACvC,cAAI,OAAO;AACX,wBAAc,GAAG;AAAA,QAClB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD,CAAC;AACF;AAEA,SAAS,cAAc,QAAQ,KAAK;AACnC,MAAI,OAAO,SAAS;AACnB,WAAO,QAAQ,GAAG;AAAA,EACnB,OAAO;AAEN,WAAO,KAAK,SAAS,GAAG;AACxB,WAAO,IAAI;AAAA,EACZ;AACD;AAQA,MAAM,aAAa,SAAU,MAAM;AAClC,SAAO,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS;AACjF;AAGA,MAAM,UAAU,OAAO;AAEvB,IAAO,cAAQ;", "names": ["Stream", "http", "Url", "whatwgUrl", "isDomainOrSubdomain", "isSameProtocol", "https", "abort", "abortAndFinalize", "zlib"]}