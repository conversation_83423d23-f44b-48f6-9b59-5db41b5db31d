# Environment Setup Guide

This document explains how to configure environment variables for the ScopingAI frontend application.

## Environment Variables

The application uses V<PERSON>'s environment variable system. Create a `.env` file in the root directory of the `frontendnew` folder with the following variables:

### Required Variables

```bash
# API Configuration
VITE_API_URL=http://localhost:3000
```

### Optional Variables

```bash
# Application Configuration
VITE_APP_NAME=ScopingAI
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_DEBUG_MODE=true
VITE_ENABLE_ANALYTICS=false
```

## Environment Files

### Development (.env)

```bash
VITE_API_URL=http://localhost:3000
VITE_DEBUG_MODE=true
```

### Production (.env.production)

```bash
VITE_API_URL=https://api.scopingai.com
VITE_DEBUG_MODE=false
VITE_ENABLE_ANALYTICS=true
```

### Local Development (.env.local)

```bash
# Override any variables for local development
VITE_API_URL=http://localhost:8080
```

## Configuration Usage

The application uses a centralized configuration system located in `src/config/environment.ts`. This file:

1. Reads environment variables using `import.meta.env`
2. Provides default fallback values
3. Exports typed configuration objects
4. Centralizes all API endpoints

### Example Usage

```typescript
import { config, apiEndpoints } from "../config/environment";

// Use API URL
const apiUrl = config.apiUrl;

// Use specific endpoint
const documentsEndpoint = apiEndpoints.documents;
```

## API Endpoints

The following API endpoints are configured:

### Document Generation

- `POST /api/proposals/stream` - Stream document generation
- `POST /api/regenerate-section` - Regenerate document sections

### Document Management

- `GET /api/documents` - List documents
- `GET /api/documents/:id` - Get specific document
- `POST /api/documents/save` - Save document
- `DELETE /api/documents/:id` - Delete document

### Export

- `GET /api/documents/:id/export/pdf` - Export as PDF
- `GET /api/documents/:id/export/docx` - Export as Word document

### Client Management

- `GET /api/clients` - List clients
- `POST /api/clients` - Create client

### Templates

- `GET /api/prompt-templates` - List prompt templates
- `POST /api/prompt-templates` - Create prompt template
- `GET /api/reference-documents` - List reference documents

## Backend Requirements

Ensure your backend API supports the following:

1. **CORS Configuration**: Allow requests from your frontend domain
2. **EventSource Support**: For streaming document generation
3. **File Upload**: For document and image uploads
4. **Authentication**: If using protected endpoints

## Troubleshooting

### Common Issues

1. **API Connection Failed**

   - Check if `VITE_API_URL` is correctly set
   - Verify backend is running on the specified port
   - Check CORS configuration

2. **Environment Variables Not Loading**

   - Ensure variables start with `VITE_`
   - Restart the development server after changes
   - Check file is named `.env` (not `.env.txt`)

3. **Build Issues**
   - Verify all required environment variables are set
   - Check for typos in variable names
   - Ensure production environment file exists

### Debug Mode

Enable debug mode to see additional logging:

```bash
VITE_DEBUG_MODE=true
```

This will enable:

- API request/response logging
- EventSource connection details
- Error stack traces
- Performance metrics

## Security Notes

1. **Never commit `.env` files** to version control
2. **Use `.env.example`** to document required variables
3. **Prefix all variables** with `VITE_` for Vite to include them
4. **Avoid sensitive data** in environment variables that go to the browser
5. **Use different API keys** for development and production
