
# Single Page SEO Audit Design Document

## Overview
The Single Page SEO Audit functionality provides detailed technical SEO analysis for a specific URL. This document outlines the components, data flow, and database schema involved in this feature.

## Components Involved

### Frontend Components
- **AuditRunner.tsx**: Main page for initiating a single page audit
- **AuditHistory.tsx**: Page for viewing past audit results
- **AuditResults.tsx**: Detailed view of a completed audit

### Backend Services
1. **auditOrchestrationService.ts**
   - Coordinates the entire audit workflow
   - Manages audit state and progress
   - Delegates to specialized services

2. **seoIntegrationService.ts**
   - Performs comprehensive SEO analysis
   - Provides exact HTML issue locations
   - Generates SEO scores with provider integration

3. **htmlAnalysisService.ts**
   - Analyzes HTML structure for SEO issues
   - Identifies exact line numbers and selectors for issues
   - Provides context for each issue

4. **seoScoringService.ts**
   - Calculates SEO scores based on various metrics
   - Integrates with external providers
   - Generates letter grades and recommendations

5. **scraperService.ts**
   - Fetches website content
   - Extracts metadata and SEO elements
   - Provides content for analysis

6. **contentOptimizationService.ts**
   - Optimizes HTML content for analysis
   - Reduces content size while preserving structure
   - Improves analysis performance

7. **aiAnalysisService.ts**
   - Generates reports from analysis results
   - Converts markdown to HTML
   - Provides AI-powered recommendations

### Database Tables
1. **pseo_audits**
   ```sql
   CREATE TABLE pseo_audits (
     id UUID PRIMARY KEY,
     website_id UUID REFERENCES pseo_websites(id),
     status VARCHAR(50) NOT NULL,
     scraped_content TEXT,
     scrape_metadata JSONB,
     technical_audit_raw TEXT,
     content_audit_raw TEXT,
     technical_analysis JSONB,
     content_analysis JSONB,
     combined_report TEXT,
     report_html TEXT,
     ai_model_used VARCHAR(100),
     processing_time_seconds INTEGER,
     error_message TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     completed_at TIMESTAMP,
     html_analysis_result TEXT,
     seo_score INTEGER,
     seo_grade VARCHAR(2),
     seo_scoring_result TEXT,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   )
   ```

2. **pseo_audit_steps**
   ```sql
   CREATE TABLE pseo_audit_steps (
     id UUID PRIMARY KEY,
     audit_id UUID REFERENCES pseo_audits(id),
     step_name VARCHAR(100) NOT NULL,
     status VARCHAR(50) NOT NULL,
     step_data JSONB,
     step_type VARCHAR(50),
     result TEXT,
     metadata TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     completed_at TIMESTAMP
   )
   ```

## Data Flow

1. **Audit Initiation**
   - User enters URL in AuditRunner.tsx
   - Frontend calls workflowService.startAudit()
   - workflowService delegates to auditOrchestrationService

2. **Audit Execution**
   - auditOrchestrationService creates audit record
   - scraperService fetches website content
   - contentOptimizationService optimizes content
   - seoIntegrationService performs comprehensive analysis
   - htmlAnalysisService identifies exact issue locations
   - seoScoringService calculates scores and grades
   - aiAnalysisService generates report

3. **Results Storage**
   - Audit results stored in pseo_audits table
   - Individual steps stored in pseo_audit_steps table
   - HTML analysis stored in html_analysis_result column
   - SEO scores stored in seo_score and seo_grade columns
   - Full scoring breakdown stored in seo_scoring_result column

4. **Audit History**
   - AuditHistory.tsx fetches audits via databaseService.getAuditsByUserId()
   - Displays list of audits with status, date, and duration
   - User can view detailed results or delete audits

## Key Features

1. **Exact Issue Locations**
   - Line and column numbers for each issue
   - CSS selectors and XPath for precise targeting
   - Context showing before/after code

2. **Comprehensive Scoring**
   - Overall score (0-100)
   - Letter grade (A+ to F)
   - Breakdown by category (technical, content, performance)
   - Provider-specific metrics

3. **Actionable Recommendations**
   - Prioritized action items (immediate, short-term, long-term)
   - Estimated fix time
   - Impact assessment for each issue

4. **Detailed Reports**
   - Technical analysis with exact locations
   - Content analysis with readability metrics
   - Performance analysis with speed metrics
   - Combined summary with overall health assessment

## Removal Plan

To remove Single Page SEO Audit and Audit History from the menu:

1. **Frontend Changes**
   - Remove menu items from navigation component
   - Keep underlying components for future use
   - Update routes to prevent direct access

2. **Backend Considerations**
   - Keep all services and database tables intact
   - No schema changes required
   - Services can be reused for full site analysis

3. **Database Impact**
   - No data migration needed
   - Existing audit records remain accessible via API
   - Tables continue to be used by other features

