{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "outDir": "../dist/server", "rootDir": "./", "baseUrl": "./", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "allowJs": true, "lib": ["ES2020"], "paths": {"*": ["*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": false, "compilerOptions": {"module": "CommonJS"}}}